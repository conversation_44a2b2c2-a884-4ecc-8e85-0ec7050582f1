FROM golang:1.23 as builder
ARG TARGETOS
ARG TARGETARCH

WORKDIR /workspace
COPY . .
RUN CGO_ENABLED=0 GOOS=${TARGETOS:-linux} GOARCH=${TARGETARCH:-amd64} go build -ldflags="-s -w"  -mod=vendor -a -o backend-server backend/cmd/backend-server/main.go


FROM alpine:3.18
WORKDIR /
COPY --from=builder  /workspace/backend-server backend-server
COPY error-num-language-pkg error-num-language-pkg
COPY node-reset-config.yaml node-reset-config.yaml
COPY node-up-down-config.yaml node-up-down-config.yaml
COPY cluster-create-config.yaml cluster-create-config.yaml
COPY cluster-upgrade-config.yaml cluster-upgrade-config.yaml
