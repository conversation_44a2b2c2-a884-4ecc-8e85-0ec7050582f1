package app

import (
	"context"
	"flag"
	"log"

	clientconfig "sigs.k8s.io/controller-runtime/pkg/client/config"

	apisixapisv2 "github.com/apache/apisix-ingress-controller/pkg/kube/apisix/apis/config/v2"
	"github.com/spf13/cobra"
	veleroV1 "github.com/vmware-tanzu/velero/pkg/apis/velero/v1"
	baselinev1 "harmonycloud.cn/baseline-checker/api/v1"
	heimdallrV1 "harmonycloud.cn/heimdallr/pkg/apis/heimdallr/v1alpha1"
	ingressclassv1 "harmonycloud.cn/ingress-expose-helper/pkg/apis/expose.helper/v1"
	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	cloudservicev1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/v1alpha1"
	"harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/olympus-cluster/pkg/watcher"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/controllers"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/database"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/initialize"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/router"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/task"
	clusterconfig "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/cluster/config"
	nodeupdownconfig "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/node/config"
	noderesetconfig "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/nodereset/config"
	upgradeclusterconfig "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/upgraderender/config"
	languagecontext "harmonycloud.cn/unifiedportal/translate-sdk-golang/context"
	database_aop "harmonycloud.cn/unifiedportal/translate-sdk-golang/database-aop"
	languageerrors "harmonycloud.cn/unifiedportal/translate-sdk-golang/errors"
	apiextensionsv1 "k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1"
	"k8s.io/klog/v2"
	"k8s.io/klog/v2/klogr"
	ctrl "sigs.k8s.io/controller-runtime"
	metricsserver "sigs.k8s.io/controller-runtime/pkg/metrics/server"
)

func Command() *cobra.Command {
	cmd := cobra.Command{
		Use:  "backend-server",
		Long: "HarmonyCloud Cloud Service Backend",
		Run: func(cmd *cobra.Command, args []string) {
			run()
		},
	}
	applyFlag(&cmd)

	return &cmd
}

func applyFlag(cmd *cobra.Command) {
	flags := config.Flags
	for _, f := range flags {
		cmd.Flags().StringVar(&f.Value, f.Name, f.Value, f.Description)
	}
	klog.InitFlags(flag.CommandLine)
	cmd.Flags().AddGoFlagSet(flag.CommandLine)
	clientconfig.RegisterFlags(flag.CommandLine)
}

func run() {
	logger.Init()
	defer logger.Sync()

	// 初始化数据库链接
	if err := database.Init(); err != nil {
		panic(err)
	}

	// 初始化redis链接
	if err := database.InitRDS(); err != nil {
		panic(err)
	}

	// 初始化日志
	ctrl.SetLogger(klogr.New())
	// 初始化scheme
	InitScheme()
	// 初始化hub 集群 k8s client
	if err := client.Init(); err != nil {
		panic(err)
	}
	// 初始化Reconcile
	StartReconcileCluster()
	StartReconcileClusterAddons()
	// 初始化国际化 language-context
	if err := languagecontext.SetLanguageHeaderKey(config.TranslateLanguageCookieKey.Value); err != nil {
		panic(err)
	}
	if err := languagecontext.SetDefaultLanguageCode(config.TranslateDefaultLanguageCode.Value); err != nil {
		panic(err)
	}
	// 错误处理code国际化映射文件
	if err := languageerrors.SetTranslateFilePath(config.TranslateErrorCodeFileDir.Value); err != nil {
		panic(err)
	}
	if err := languageerrors.Hook(); err != nil {
		panic(err)
	}

	// 初始化国际化数据库客户端
	database_aop.SetDatabaseClient(database.CaasDB)

	// + option 设置国际化缓存器为redis
	// database_aop.SetDefaultCache(database_aop.NewRedisCache(database.RDS))

	// init some tasks
	go func() {
		// start init job
		if err := initialize.Start(); err != nil {
			// if init failed need panic
			panic(err)
		}
	}()

	// 添加定时任务
	task.AddTask()

	// 初始化集群创建配置文件
	if err := clusterconfig.ShouldReadConfig(); err != nil {
		panic(err)
	}
	if err := upgradeclusterconfig.ReadConfig(); err != nil {
		panic(err)
	}
	if err := nodeupdownconfig.ShouldReadConfig(); err != nil {
		panic(err)
	}
	if err := noderesetconfig.ShouldReadConfig(); err != nil {
		panic(err)
	}
	router := router.InitGinRouter()
	if err := router.Run(config.Port.Value); err != nil {
		panic(err)
	}
}

// InitScheme
// 初始化Scheme
func InitScheme() {
	//client.AddScheme(stellarisv1alpha1.AddToScheme)
	client.AddScheme(cloudservicev1alpha1.AddToScheme)
	client.AddScheme(veleroV1.AddToScheme)
	client.AddScheme(installerv1alpha1.AddToScheme)
	client.AddScheme(heimdallrV1.AddToScheme)
	client.AddScheme(apisixapisv2.AddToScheme)
	client.AddScheme(baselinev1.AddToScheme)
	client.AddScheme(apiextensionsv1.AddToScheme)
	client.AddScheme(ingressclassv1.AddToScheme)
}

// StartReconcileCluster
// 开始 Reconcile Cluster
func StartReconcileCluster() {
	mgr, err := ctrl.NewManager(ctrl.GetConfigOrDie(), ctrl.Options{
		Scheme:         client.Scheme,
		LeaderElection: false,
		Metrics: metricsserver.Options{
			BindAddress: "0",
		},
		HealthProbeBindAddress: "0",
	})
	if err != nil {
		panic(err)
	}
	watcher.NewStellarisClusterReconciler(mgr).SetupWithManager(mgr)
	// add portal controller
	if err := controllers.SetupControllers(mgr.GetClient(), mgr); err != nil {
		panic(err)
	}
	go func() {
		if err := mgr.Start(context.Background()); err != nil {
			log.Fatalf("start watch  hub cluster error ,error is '%s'", err.Error())
		}
	}()
}

// StartReconcileClusterAddons
// 开始 Reconcile ClusterAddons
func StartReconcileClusterAddons() {
	mgr, err := ctrl.NewManager(ctrl.GetConfigOrDie(), ctrl.Options{
		Scheme:         client.Scheme,
		LeaderElection: false,
		Metrics: metricsserver.Options{
			BindAddress: "0",
		},
		HealthProbeBindAddress: "0",
	})
	if err != nil {
		panic(err)
	}
	watcher.NewClusterAddonsReconciler(mgr).SetupWithManager(mgr)
	go func() {
		if err := mgr.Start(context.Background()); err != nil {
			log.Fatalf("start watch  hub cluster error ,error is '%s'", err.Error())
		}
	}()
}
