package config

import (
	"context"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/config"
)

type Handler interface {
	// ListConfig
	// 根据 group_name 获取配置组的配置项目列表
	ListConfig(ctx context.Context, groupName string) (config.ListResponse, error)

	// GetConfigByType
	// 根据 group_name\typeName 获取配置组的某一配置项目集
	GetConfigByType(ctx context.Context, groupName string, typeName string) (*config.Response, error)

	// ListSugarConfig
	// 获取带语法糖格式的配置
	ListSugarConfig(ctx context.Context, groupName string) (config.ListSugarResponse, error)

	// GetSugarConfigByType
	// 获取带语法糖格式的配置
	GetSugarConfigByType(ctx context.Context, groupName string, typeName string) (*config.SugarResponse, error)
}
