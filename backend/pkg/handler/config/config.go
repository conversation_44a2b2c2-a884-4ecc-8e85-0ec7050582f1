package config

import (
	"context"
	"time"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/async"
)

func NewHandler() Handler {
	return &handler{}

}

type handler struct {
}

// ListConfig
// 根据 group_name 获取配置组的配置项目列表
func (h *handler) ListConfig(ctx context.Context, groupName string) (config.ListResponse, error) {
	gHandler, err := mgr.GetGroup(groupName)
	if err != nil {
		return nil, err
	}
	tHandlers := gHandler.handlers()
	jobAsync := async.NewTimeoutJobGroupAsync[config.Response](5 * time.Second)
	for _, tHandler := range tHandlers {
		jobAsync.AddJob(tHandler.GetConfig)
	}
	return jobAsync.Result(ctx)
}

// GetConfigByType
// 根据 group_name\typeName 获取配置组的某一配置项目集
func (h *handler) GetConfigByType(ctx context.Context, groupName string, typeName string) (*config.Response, error) {
	gHandler, err := mgr.GetGroup(groupName)
	if err != nil {
		return nil, err
	}
	tHandler, err := gHandler.handler(typeName)
	if err != nil {
		return nil, err
	}
	res, err := tHandler.GetConfig(ctx)
	if err != nil {
		return nil, err
	}
	return &res, nil
}

// ListSugarConfig
// 获取带语法糖格式的配置
func (h *handler) ListSugarConfig(ctx context.Context, groupName string) (config.ListSugarResponse, error) {
	gHandler, err := mgr.GetGroup(groupName)
	if err != nil {
		return nil, err
	}
	tHandlers := gHandler.handlers()
	jobAsync := async.NewTimeoutJobGroupAsync[config.SugarResponse](5 * time.Second)
	for _, tHandler := range tHandlers {
		jobAsync.AddJob(tHandler.GetSugar)
	}
	return jobAsync.Result(ctx)
}

// GetSugarConfigByType
// 获取带语法糖格式的配置
func (h *handler) GetSugarConfigByType(ctx context.Context, groupName string, typeName string) (*config.SugarResponse, error) {
	gHandler, err := mgr.GetGroup(groupName)
	if err != nil {
		return nil, err
	}
	tHandler, err := gHandler.handler(typeName)
	if err != nil {
		return nil, err
	}
	res, err := tHandler.GetSugar(ctx)
	if err != nil {
		return nil, err
	}
	return &res, nil
}
