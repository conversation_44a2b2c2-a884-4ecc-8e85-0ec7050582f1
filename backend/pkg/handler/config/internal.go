package config

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"strings"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/addon"

	"harmonycloud.cn/unifiedportal/cloudservice-operator/pkg/handler/installer/client"
	hcclient "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	sysconfig "harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	clusterconfig "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/cluster/config"
	v1 "k8s.io/api/core/v1"
	ctrlclient "sigs.k8s.io/controller-runtime/pkg/client"
)

const (
	GroupCreateCluster  = "create-cluster"
	GroupSisyphusConfig = "sisyphus-config"
	GroupSystemInfo     = "system-info"
	GroupClusterInfo    = "cluster-info"
)

var (
	supportKubernetesVersion      = newSupportKubernetesVersionTypeHandler()
	supportCRIVersion             = newSupportCRIVersionTypeHandler()
	hubClusterIngressAddress      = newHubClusterIngressAddressTypeHandlerTypeHandler()
	hubClusterStellariesComponent = newHubClusterStellariesComponentTypeHandler()
	defaultCalicoCidr             = newDefaultCalicoCidrTypeHandler()
	sisyphusSupport               = newSisyphusSupportHandler()
	defaultRegistry               = newDefaultRegistryHandler()
	clusterChronyInfo             = newClusterChronyInfoHandler()
)

var mgr GroupManager = &groupManager{
	groupMap: map[string]GroupHandler{
		GroupCreateCluster:  newGroupHandler(GroupCreateCluster, supportKubernetesVersion, supportCRIVersion, hubClusterIngressAddress, hubClusterStellariesComponent, defaultCalicoCidr),
		GroupSisyphusConfig: newGroupHandler(GroupSisyphusConfig, sisyphusSupport),
		GroupSystemInfo:     newGroupHandler(GroupSystemInfo, defaultRegistry),
		GroupClusterInfo:    newGroupHandler(GroupClusterInfo, clusterChronyInfo),
	},
}

type GroupManager interface {
	GetGroup(name string) (GroupHandler, error)
}

type groupManager struct {
	groupMap map[string]GroupHandler
}

func (mgr *groupManager) GetGroup(name string) (GroupHandler, error) {
	group, exist := mgr.groupMap[name]
	if !exist {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, fmt.Sprintf("config group %s is not exist", name))
	}
	return group, nil
}

func newGroupHandler(name string, typeHandlers ...TypeHandler) GroupHandler {
	typeHandlerMap := make(map[string]TypeHandler, 16)
	if len(typeHandlers) != 0 {
		for _, typeHandler := range typeHandlers {
			typeHandler := typeHandler
			typeHandlerMap[typeHandler.GetTypeName()] = typeHandler
		}
	}
	return &groupHandler{
		name:           name,
		typeHandlerMap: typeHandlerMap,
	}
}

type GroupHandler interface {
	getGroupName() string
	handlers() []TypeHandler
	handler(name string) (TypeHandler, error)
}

type groupHandler struct {
	name           string
	typeHandlerMap map[string]TypeHandler
}

func (group *groupHandler) getGroupName() string {
	return group.name
}
func (group *groupHandler) handlers() []TypeHandler {
	var groups = make([]TypeHandler, 0, len(group.typeHandlerMap))
	for _, handler := range group.typeHandlerMap {
		handler := handler
		groups = append(groups, handler)
	}
	return groups
}
func (group *groupHandler) handler(name string) (TypeHandler, error) {
	handler, exist := group.typeHandlerMap[name]
	if !exist {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, fmt.Sprintf("config group %s,type %s is not exist", group.getGroupName(), name))
	}
	return handler, nil
}

type TypeHandler interface {
	GetTypeName() string
	GetConfig(ctx context.Context) (config.Response, error)
	GetSugar(ctx context.Context) (config.SugarResponse, error)
}

// newSupportKubernetesVersionTypeHandler
// 获取支持的k8s 版本
func newSupportKubernetesVersionTypeHandler() TypeHandler {
	return &supportKubernetesVersionTypeHandler{
		key: config.SupportKubernetesVersion,
	}
}

type supportKubernetesVersionTypeHandler struct {
	key config.Key
}

func (th *supportKubernetesVersionTypeHandler) GetTypeName() string {
	return th.key.ToString()
}

func (th *supportKubernetesVersionTypeHandler) GetConfig(ctx context.Context) (config.Response, error) {
	solutionInfos := clusterconfig.CreateClusterConfig.SolutionInfos
	var values = make([]config.ValueResponse, 0, len(solutionInfos))
	for _, solutionInfo := range solutionInfos {
		solutionInfo := solutionInfo
		values = append(values, config.ValueResponse{
			Code:  solutionInfo.KubernetesVersion,
			Value: solutionInfo.KubernetesVersion,
		})
	}
	return config.Response{
		Key:    th.key,
		Values: values,
	}, nil
}

func (th *supportKubernetesVersionTypeHandler) GetSugar(ctx context.Context) (config.SugarResponse, error) {
	var sugarResponse = config.SugarResponse{
		Key: th.key,
	}
	configs, err := th.GetConfig(ctx)
	if err != nil {
		return sugarResponse, nil
	}
	convertFunc := func(values []config.ValueResponse) interface{} {
		var sugars []config.SupportKubernetesVersionResponse
		for _, value := range values {
			value := value
			sugars = append(sugars, config.SupportKubernetesVersionResponse{
				Version: value.Value,
			})
		}
		return sugars
	}
	sugarResponse.Sugar = convertFunc(configs.Values)
	return sugarResponse, nil
}

// newSupportCRIVersionTypeHandler
// 获取支持的CRI版本列表
func newSupportCRIVersionTypeHandler() TypeHandler {
	return &supportCRIVersionTypeHandler{
		key: config.SupportKubernetesCRIs,
	}
}

type supportCRIVersionTypeHandler struct {
	key config.Key
}

func (th *supportCRIVersionTypeHandler) GetTypeName() string {
	return th.key.ToString()
}

func (th *supportCRIVersionTypeHandler) GetConfig(ctx context.Context) (config.Response, error) {
	solutionInfos := clusterconfig.CreateClusterConfig.SolutionInfos
	var values = make([]config.ValueResponse, 0, len(solutionInfos))
	for _, solutionInfo := range solutionInfos {
		solutionInfo := solutionInfo
		values = append(values, config.ValueResponse{
			Code:  solutionInfo.CRIVersion,
			Value: solutionInfo.CRIVersion,
		})
	}
	return config.Response{
		Key:    th.key,
		Values: values,
	}, nil
}

func (th *supportCRIVersionTypeHandler) GetSugar(ctx context.Context) (config.SugarResponse, error) {
	var sugarResponse = config.SugarResponse{
		Key: th.key,
	}
	configs, err := th.GetConfig(ctx)
	if err != nil {
		return sugarResponse, nil
	}
	convertFunc := func(values []config.ValueResponse) interface{} {
		var sugars []config.SupportCRIVersionResponse
		for _, value := range values {
			value := value
			sugars = append(sugars, config.SupportCRIVersionResponse{
				Version: value.Value,
			})
		}
		return sugars
	}
	sugarResponse.Sugar = convertFunc(configs.Values)
	return sugarResponse, nil
}

// newHubClusterIngressAddressTypeHandlerTypeHandler
// 获取管理平台ingress访问地址
func newHubClusterIngressAddressTypeHandlerTypeHandler() TypeHandler {
	return &hubClusterIngressAddressTypeHandler{
		key: config.HubIngressAddress,
	}
}

type hubClusterIngressAddressTypeHandler struct {
	key config.Key
}

func (th *hubClusterIngressAddressTypeHandler) GetTypeName() string {
	return th.key.ToString()
}

func (th *hubClusterIngressAddressTypeHandler) GetConfig(ctx context.Context) (config.Response, error) {
	// todo 管理平台ingress 访问地址先随便写一个
	// 届时由实施写一个ConfigMap 到时候去这里读
	return config.Response{
		Key: th.key,
		Values: []config.ValueResponse{
			{
				Code:  "hub_ingress_address",
				Value: sysconfig.TopClusterIngressAddress.Value,
			},
		},
	}, nil
}

func (th *hubClusterIngressAddressTypeHandler) GetSugar(ctx context.Context) (config.SugarResponse, error) {
	var sugarResponse = config.SugarResponse{
		Key: th.key,
	}
	configs, err := th.GetConfig(ctx)
	if err != nil {
		return sugarResponse, nil
	}
	convertFunc := func(values []config.ValueResponse) interface{} {
		var sugars config.HubClusterIngressResponse
		for _, value := range values {
			switch value.Code {
			case "hub_ingress_address":
				sugars.Address = value.Value
			}
		}
		return &sugars
	}
	sugarResponse.Sugar = convertFunc(configs.Values)
	return sugarResponse, nil
}

// newSupportHubClusterStellariesComponentTypeHandler
// 获取平台 stellaries歇息
func newHubClusterStellariesComponentTypeHandler() TypeHandler {
	return &hubClusterStellariesComponentTypeHandler{
		key: config.HubStellariesComponent,
	}
}

type hubClusterStellariesComponentTypeHandler struct {
	key config.Key
}

func (th *hubClusterStellariesComponentTypeHandler) GetTypeName() string {
	return th.key.ToString()
}

func (th *hubClusterStellariesComponentTypeHandler) GetConfig(ctx context.Context) (config.Response, error) {
	var dre config.DisasterRecoveryEnabled = false
	if strings.EqualFold("true", strings.TrimSpace(sysconfig.DisasterRecoveryEnabled.Value)) {
		dre = true
	}
	response := config.Response{
		Key: th.key,
		Values: []config.ValueResponse{
			{
				Code:  "model",
				Value: dre.ParseModel().ToString(),
			},
		},
	}
	switch dre {
	case true:
		response.Values = append(response.Values, config.ValueResponse{
			Code:  "standby_address",
			Value: sysconfig.StandbyStellariesAddress.Value,
		})
		response.Values = append(response.Values, config.ValueResponse{
			Code:  "standby_port",
			Value: sysconfig.StandbyStellariesPort.Value,
		})
		fallthrough
	case false:
		response.Values = append(response.Values, config.ValueResponse{
			Code:  "address",
			Value: sysconfig.StellariesAddress.Value,
		})
		response.Values = append(response.Values, config.ValueResponse{
			Code:  "port",
			Value: sysconfig.StellariesPort.Value,
		})
	}
	return response, nil

}

func (th *hubClusterStellariesComponentTypeHandler) GetSugar(ctx context.Context) (config.SugarResponse, error) {
	var sugarResponse = config.SugarResponse{
		Key: th.key,
	}
	configs, err := th.GetConfig(ctx)
	if err != nil {
		return sugarResponse, nil
	}
	convertFunc := func(values []config.ValueResponse) (interface{}, error) {
		result := &config.HubStellariesComponentResponse{}
		for _, value := range values {
			value := value
			switch value.Code {
			case "model":
				result.Model = config.StellariesComponentModel(value.Value)
			case "address":
				result.Address = value.Value
			case "port":
				port, err := strconv.Atoi(value.Value)
				if err != nil {
					return nil, err
				}
				result.Port = port
			case "standby_address":
				result.StandbyAddress = &value.Value
			case "standby_port":
				port, err := strconv.Atoi(value.Value)
				if err != nil {
					return nil, err
				}
				result.StandbyPort = &port
			}
		}
		return result, nil
	}
	sugar, err := convertFunc(configs.Values)
	if err != nil {
		return sugarResponse, err
	}
	sugarResponse.Sugar = sugar
	return sugarResponse, nil
}

func newDefaultCalicoCidrTypeHandler() TypeHandler {
	return &defaultCalicoCidrTypeHandler{
		key: config.DefaultCalicoCidr,
	}
}

type defaultCalicoCidrTypeHandler struct {
	key config.Key
}

func (th *defaultCalicoCidrTypeHandler) GetTypeName() string {
	return th.key.ToString()
}
func (th *defaultCalicoCidrTypeHandler) GetConfig(ctx context.Context) (config.Response, error) {
	return config.Response{
		Key: th.key,
		Values: []config.ValueResponse{
			{
				Code:  "ipv4",
				Value: "**********/16",
			},
			{
				Code:  "ipv6",
				Value: "2001:db8:42:0::/112",
			},
		},
	}, nil

}
func (th *defaultCalicoCidrTypeHandler) GetSugar(ctx context.Context) (config.SugarResponse, error) {
	var sugarResponse = config.SugarResponse{
		Key: th.key,
	}
	configs, err := th.GetConfig(ctx)
	if err != nil {
		return sugarResponse, nil
	}
	convertFunc := func(values []config.ValueResponse) interface{} {
		result := &config.DefaultCalicoCidrResponse{}
		for _, value := range values {
			value := value
			switch value.Code {
			case "ipv4":
				result.IPv4 = value.Value
			case "ipv6":
				result.IPv6 = value.Value
			}
		}
		return result
	}
	sugarResponse.Sugar = convertFunc(configs.Values)
	return sugarResponse, nil
}

func newSisyphusSupportHandler() TypeHandler {
	return &sisyphusSupportHandler{
		key:                   config.SisyphusSupport,
		sisyphusClient:        client.NewSisyphus(),
		sisyphusConfigHandler: addon.NewSisyphusConfigHandler(),
	}
}

type sisyphusSupportHandler struct {
	key            config.Key
	sisyphusClient client.Sisyphus
	// 读取下层集群的西西弗斯地址
	sisyphusConfigHandler addon.SisyphusConfigHandler
}

func (th *sisyphusSupportHandler) GetTypeName() string {
	return th.key.ToString()
}
func (th *sisyphusSupportHandler) GetConfig(ctx context.Context) (config.Response, error) {
	return config.Response{}, fmt.Errorf("un support now")
}
func (th *sisyphusSupportHandler) GetSugar(ctx context.Context) (config.SugarResponse, error) {
	var sugarResponse = config.SugarResponse{
		Key: th.key,
	}
	clusterNameStr := ctx.Value("clusterName")
	var clusterName string
	if clusterNameStr != nil {
		clusterName = clusterNameStr.(string)
	}
	sisyphusConfig, err := th.sisyphusConfigHandler.GetSisyphusURL(ctx, clusterName)
	if err != nil {
		return sugarResponse, err
	}

	supportResponse, err := th.sisyphusClient.Supports(ctx, client.WithURL(sisyphusConfig.Address), client.WithAuth(sisyphusConfig.Address, sisyphusConfig.Username, sisyphusConfig.Password))
	if err != nil {
		return sugarResponse, err
	}
	archInfoMap := map[string]*config.NodeSupportResponse{}
	for _, support := range supportResponse.Data {
		os, arch, enablePtr := support.Os, support.Arch, support.Enabled
		var enable bool
		if enablePtr != nil && *enablePtr {
			enable = true
		}
		archAlias, err := utils.ParseArchAlias(arch)
		if err != nil {
			return sugarResponse, err
		}
		archInfo, exist := archInfoMap[arch]
		if !exist {
			archInfo = new(config.NodeSupportResponse)
		}
		archInfo.Arch = arch
		archInfo.Alias = archAlias
		archInfo.OSList = append(archInfo.OSList, config.NodeSupportOS{
			OS:     os,
			Enable: enable,
		})
		archInfoMap[arch] = archInfo
	}
	var archList []string
	for arch, value := range archInfoMap {
		arch := arch
		archList = append(archList, arch)
		value.SetAndSort()
	}
	sort.Strings(archList)
	var values []*config.NodeSupportResponse
	for _, archName := range archList {
		archInfo := archInfoMap[archName]
		values = append(values, archInfo)
	}
	sugarResponse.Sugar = values
	return sugarResponse, nil

}

func newDefaultRegistryHandler() TypeHandler {
	return &defaultRegistryHandler{
		key: config.DefaultRegistry,
	}
}

type defaultRegistryHandler struct {
	key config.Key
}

func (th *defaultRegistryHandler) GetTypeName() string {
	return th.key.ToString()
}
func (th *defaultRegistryHandler) GetConfig(ctx context.Context) (config.Response, error) {
	return config.Response{
		Key: th.key,
		Values: []config.ValueResponse{
			{
				Code:  "protocol",
				Value: sysconfig.DefaultRegistryProtocol.Value,
			},
			{
				Code:  "host",
				Value: sysconfig.DefaultRegistryHost.Value,
			},
			{
				Code:  "port",
				Value: sysconfig.DefaultRegistryPort.Value,
			},
		},
	}, nil
}
func (th *defaultRegistryHandler) GetSugar(ctx context.Context) (config.SugarResponse, error) {
	var sugar config.SugarResponse
	configResponse, err := th.GetConfig(ctx)
	if err != nil {
		return sugar, err
	}
	sugar.Key = configResponse.Key
	var registry config.Registry
	resderFunc := func(r *config.Registry, value config.ValueResponse) error {
		switch value.Code {
		case "protocol":
			r.Protocol = value.Value
		case "host":
			r.Host = value.Value
		case "port":
			p, err := strconv.Atoi(value.Value)
			if err != nil {
				return err
			}
			r.Port = p
		}
		return nil
	}
	for _, item := range configResponse.Values {
		if err := resderFunc(&registry, item); err != nil {
			return sugar, err
		}
	}
	sugar.Sugar = registry
	return sugar, nil

}

func newClusterChronyInfoHandler() TypeHandler {
	return &clusterChronyInfoHandler{
		key: config.ChronyInfo,
	}
}

type clusterChronyInfoHandler struct {
	key config.Key
}

func (th *clusterChronyInfoHandler) GetTypeName() string {
	return th.key.ToString()
}
func (th *clusterChronyInfoHandler) GetConfig(ctx context.Context) (config.Response, error) {
	var clusterName string
	clusterNameIntf := ctx.Value("clusterName")
	switch clusterNameIntf.(type) {
	case []string:
		arr := clusterNameIntf.([]string)
		if len(arr) != 1 {
			return config.Response{}, errors.NewFromCodeWithMessage(errors.Var.ParamError, "clusterName")
		}
		clusterName = arr[0]
	case string:
		clusterName = clusterNameIntf.(string)
	default:
		return config.Response{}, errors.NewFromCodeWithMessage(errors.Var.ParamError, "clusterName")
	}

	cluster, err := hcclient.OnlineClusterAssert(hcclient.GetCluster(clusterName))
	if err != nil {
		return config.Response{}, errors.NewFromCodeWithMessage(errors.Var.ClusterStatusNotOnline, err.Error())
	}
	var nodeList v1.NodeList
	if err := cluster.GetClient().GetCtrlClient().List(ctx, &nodeList, ctrlclient.HasLabels{constants.ClusterChronyServerLabelKey}); err != nil {
		return config.Response{}, err
	}
	var timeServer string
	if len(nodeList.Items) != 0 {
		node := nodeList.Items[0]
		for _, address := range node.Status.Addresses {
			switch address.Type {
			case v1.NodeInternalIP:
				timeServer = address.Address
			}
		}
	}

	return config.Response{
		Key: th.key,
		Values: []config.ValueResponse{
			{
				Code:  "timeServer",
				Value: timeServer,
			},
		},
	}, nil
}
func (th *clusterChronyInfoHandler) GetSugar(ctx context.Context) (config.SugarResponse, error) {
	var sugar config.SugarResponse
	configResponse, err := th.GetConfig(ctx)
	if err != nil {
		return sugar, err
	}
	var timeServer string
	for _, item := range configResponse.Values {
		switch item.Code {
		case "timeServer":
			timeServer = item.Value

		}
	}
	sugar.Key = configResponse.Key
	sugar.Sugar = config.ChronyInfoStruct{
		TimeServer: timeServer,
	}
	return sugar, nil
}
