package internalopenapi

import (
	"context"
	"database/sql"
	"time"

	"gorm.io/gorm"
	stellarisv1alpha1 "harmonycloud.cn/stellaris/pkg/apis/stellaris/v1alpha1"
	permissionv1 "harmonycloud.cn/unifiedportal/api-definition/permission/v1"
	translationv1 "harmonycloud.cn/unifiedportal/api-definition/translation/v1"
	"harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/database"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/internalopenapi/internal"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/lock"
	runtimeclient "sigs.k8s.io/controller-runtime/pkg/client"
)

type internalOpenApiHandler struct {
}

func (handler *internalOpenApiHandler) HandlePermission(ctx context.Context, steps permissionv1.MenuSteps) error {
	// 合法性校验
	if err := steps.Validator(); err != nil {
		return err
	}
	distributeLock := lock.NewRDSDistributeLock(database.RDS)
	lockHandler, err := distributeLock.TryLockAndWait(ctx, "import-menu-lock", 5*time.Second)
	if err != nil {
		return err
	}
	defer lockHandler.UnLock(ctx)

	// apply for permission
	err = database.AmpDB.Transaction(func(tx *gorm.DB) error {
		pool, err := internal.NewInMemoryPool(tx)
		if err != nil {
			return err
		}
		for _, step := range steps {
			op, err := internal.GetOperator(step.Operator, pool)
			if err != nil {
				return err
			}
			if err := op.Handle(step.Configs); err != nil {
				return err
			}
		}
		return pool.Submit()
	}, &sql.TxOptions{
		ReadOnly:  false,
		Isolation: sql.LevelReadUncommitted, // 采用读未提交的事物隔离级别
	})

	if err != nil {
		logger.GetSugared().Errorf("[HandlePermission],Transaction for permission error,err is %v", err)
		return err
	}
	return nil
}

func (handler *internalOpenApiHandler) HandleTranslation(ctx context.Context, steps translationv1.TranslateConfigSteps) error {
	// 合法性校验
	if err := steps.Validator(); err != nil {
		return err
	}
	// 获取分布式锁
	distributeLock := lock.NewRDSDistributeLock(database.RDS)
	lockHandler, err := distributeLock.TryLockAndWait(ctx, "import-translate-lock", 5*time.Second)
	if err != nil {
		return err
	}
	defer lockHandler.UnLock(ctx)

	// 提交数据
	err = database.CaasDB.Transaction(func(tx *gorm.DB) error {
		pool, err := internal.NewInMemoryTranslationPool(tx)
		if err != nil {
			return err
		}
		for _, step := range steps {
			op, err := internal.GetTranslationOperator(step.Operator, pool)
			if err != nil {
				return err
			}
			if err := op.Handle(step.Configs); err != nil {
				return err
			}
		}
		return pool.Submit()
	}, &sql.TxOptions{
		ReadOnly:  false,
		Isolation: sql.LevelReadUncommitted, // 采用读未提交的事物隔离级别
	})
	if err != nil {
		logger.GetSugared().Errorf("[HandleTranslation],Transaction for translate error,err is %v", err)
		return err
	}
	return nil

}
func (handler *internalOpenApiHandler) DescribeCluster(ctx context.Context, clusterName string) (*models.ClusterModel, error) {

	cluster := stellarisv1alpha1.Cluster{}
	if err := client.GetLocalCluster().GetClient().GetCtrlClient().Get(ctx, runtimeclient.ObjectKey{Name: clusterName}, &cluster); err != nil {
		return nil, err
	}

	return utils.Convert2Cluster(cluster), nil
}

func (handler *internalOpenApiHandler) ListCluster(ctx context.Context) (models.ListResponse, error) {

	clusters := stellarisv1alpha1.ClusterList{}
	if err := client.GetLocalCluster().GetClient().GetCtrlClient().List(ctx, &clusters); err != nil {
		return nil, err
	}
	var result models.ListResponse
	for _, cluster := range clusters.Items {
		result = append(result, *utils.Convert2Cluster(cluster))
	}
	return result, nil
}
