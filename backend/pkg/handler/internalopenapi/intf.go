package internalopenapi

import (
	"context"

	permissionv1 "harmonycloud.cn/unifiedportal/api-definition/permission/v1"
	translationv1 "harmonycloud.cn/unifiedportal/api-definition/translation/v1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
)

type HandlerIntf interface {
	HandlePermission(ctx context.Context, steps permissionv1.MenuSteps) error
	//
	DescribeCluster(ctx context.Context, cluster string) (*models.ClusterModel, error)

	ListCluster(ctx context.Context) (models.ListResponse, error)

	HandleTranslation(ctx context.Context, steps translationv1.TranslateConfigSteps) error
}

func NewInternalOpenApiHandler() HandlerIntf {
	return &internalOpenApiHandler{}
}
