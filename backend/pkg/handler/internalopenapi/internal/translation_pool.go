package internal

import (
	"database/sql"
	"strings"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao"
)

const (
	regexResourceConfigRefOfGroupNameAndRegex               = "group_name_regex"
	regexResourceTranslateConfigRefOfRegexIdAndLanguageCode = "regex_id_language_code"
)

func regexResourceConfigIDFunc(regexResourceConfig dao.RegexResourceConfig) string {
	return decimal.NewFromInt(regexResourceConfig.ID).String()
}

func regexResourceTranslateConfigIDFunc(regexResourceTranslateConfig dao.RegexResourceTranslateConfig) string {
	return decimal.NewFromInt(regexResourceTranslateConfig.ID).String()
}

type willApplyRegexResourceConfig struct {
	isSave              bool
	regexResourceConfig dao.RegexResourceConfig
}

type willApplyRegexResourceTranslateConfig struct {
	isSave                       bool
	regexResourceTranslateConfig dao.RegexResourceTranslateConfig
}

type TranslationPool interface {
	GetResourceTranslateConfig(groupName, uniqueValue, languageCode, property string) (*dao.ResourceTranslateConfig, error)
	GetResourceTranslateConfigByGroupNameAndUniqueValueAndProperty(groupName, uniqueValue, property string) ([]dao.ResourceTranslateConfig, error)
	SaveResourceTranslateConfig(config dao.ResourceTranslateConfig) error
	DeleteResourceTranslateConfig(config dao.ResourceTranslateConfig) error

	GetMaxRegexResourceConfigId() int64
	MaxRegexResourceConfigIdIncrement()

	ListAllRegexResourceConfig() ([]dao.RegexResourceConfig, error)
	GetRegexResourceConfigByGroupNameAndRegex(groupName, regex string) (*dao.RegexResourceConfig, error)
	SaveRegexResourceConfig(config dao.RegexResourceConfig) error
	DeleteRegexResourceConfig(config dao.RegexResourceConfig) error

	ListAllRegexResourceTranslateConfig() ([]dao.RegexResourceTranslateConfig, error)
	GetRegexResourceTranslateConfigByRegexId(regexId int64) ([]dao.RegexResourceTranslateConfig, error)
	GetRegexResourceTranslateConfigByRegexIdAndLanguageCode(regexId int64, languageCode string) (*dao.RegexResourceTranslateConfig, error)
	SaveRegexResourceTranslateConfig(config dao.RegexResourceTranslateConfig) error
	DeleteRegexResourceTranslateConfig(config dao.RegexResourceTranslateConfig) error

	Submit() error
}

func NewInMemoryTranslationPool(caasDB *gorm.DB) (TranslationPool, error) {
	// 查找所有 ResourceTranslateConfig
	var daoResourceTranslateConfigs []dao.ResourceTranslateConfig
	if err := caasDB.Find(&daoResourceTranslateConfigs).Error; err != nil {
		return nil, err
	}
	var daoRefResourceTranslateConfigs ObjectsRef[dao.ResourceTranslateConfig] = NewInMemoryObjectsRef(resourceTranslateConfigIDFunc)
	for _, resourceTranslateConfig := range daoResourceTranslateConfigs {
		resourceTranslateConfig := resourceTranslateConfig
		ResourceTranslateConfigRefHandler(daoRefResourceTranslateConfigs, resourceTranslateConfig)
	}

	// 查找所有 RegexResourceConfig
	var daoRegexResourceConfig []dao.RegexResourceConfig
	if err := caasDB.Find(&daoRegexResourceConfig).Error; err != nil {
		return nil, err
	}
	var daoRefRegexResourceConfigs ObjectsRef[dao.RegexResourceConfig] = NewInMemoryObjectsRef(regexResourceConfigIDFunc)
	for _, regexResourceConfig := range daoRegexResourceConfig {
		regexResourceConfig := regexResourceConfig
		RegexResourceConfigRefHandler(daoRefRegexResourceConfigs, regexResourceConfig)
	}

	// 查找所有 RegexResourceTranslateConfig
	var daoRegexResourceTranslateConfig []dao.RegexResourceTranslateConfig
	if err := caasDB.Find(&daoRegexResourceTranslateConfig).Error; err != nil {
		return nil, err
	}
	var daoRefRegexResourceTranslateConfigs ObjectsRef[dao.RegexResourceTranslateConfig] = NewInMemoryObjectsRef(regexResourceTranslateConfigIDFunc)
	for _, regexResourceTranslateConfig := range daoRegexResourceTranslateConfig {
		regexResourceTranslateConfig := regexResourceTranslateConfig
		RegexResourceTranslateConfigRefHandler(daoRefRegexResourceTranslateConfigs, regexResourceTranslateConfig)
	}

	// 读取MaxRegexResourceID
	var maxRegexResourceConfigID sql.NullInt64
	if err := caasDB.Model(dao.RegexResourceConfig{}).Select("max(id)").Take(&maxRegexResourceConfigID).Error; err != nil {
		return nil, err
	}

	return &inMemoryTranslationPool{
		caasDB:                              caasDB,
		maxRegexResourceConfigID:            maxRegexResourceConfigID.Int64,
		daoRefResourceTranslateConfigs:      daoRefResourceTranslateConfigs,
		daoRefRegexResourceConfigs:          daoRefRegexResourceConfigs,
		daoRefRegexResourceTranslateConfigs: daoRefRegexResourceTranslateConfigs,
	}, nil
}

type inMemoryTranslationPool struct {
	caasDB                                *gorm.DB
	maxRegexResourceConfigID              int64
	daoRefResourceTranslateConfigs        ObjectsRef[dao.ResourceTranslateConfig]
	daoRefRegexResourceConfigs            ObjectsRef[dao.RegexResourceConfig]
	daoRefRegexResourceTranslateConfigs   ObjectsRef[dao.RegexResourceTranslateConfig]
	willApplyResourceTranslateConfig      []willApplyResourceTranslateConfig
	willApplyRegexResourceConfig          []willApplyRegexResourceConfig
	willApplyRegexResourceTranslateConfig []willApplyRegexResourceTranslateConfig
}

func (pool *inMemoryTranslationPool) GetResourceTranslateConfig(groupName, uniqueValue, languageCode, property string) (*dao.ResourceTranslateConfig, error) {
	var items []dao.ResourceTranslateConfig
	items = pool.daoRefResourceTranslateConfigs.FindByRef(resourceTranslateConfigRefOfGroupNameAndUniqueValueAndLanguageCodeAndProperty, groupName, uniqueValue, languageCode, property)
	if len(items) == 0 {
		return nil, nil
	}
	return &items[0], nil
}
func (pool *inMemoryTranslationPool) GetResourceTranslateConfigByGroupNameAndUniqueValueAndProperty(groupName, uniqueValue, property string) ([]dao.ResourceTranslateConfig, error) {
	var items []dao.ResourceTranslateConfig
	items = pool.daoRefResourceTranslateConfigs.FindByRef(resourceTranslateConfigRefOfGroupNameAndUniqueValueAndLanguageCodeAndProperty, groupName, uniqueValue)
	var result []dao.ResourceTranslateConfig
	for _, item := range items {
		item := item
		if strings.EqualFold(item.Property, property) {
			result = append(result, item)
		}
	}
	return result, nil

}
func (pool *inMemoryTranslationPool) SaveResourceTranslateConfig(config dao.ResourceTranslateConfig) error {
	pool.willApplyResourceTranslateConfig = append(pool.willApplyResourceTranslateConfig, willApplyResourceTranslateConfig{
		isSave:                  true,
		ResourceTranslateConfig: config,
	})
	id := resourceTranslateConfigIDFunc(config)
	_, exist := pool.daoRefResourceTranslateConfigs.FindById(id)
	if !exist {
		// if not exist put then
		ResourceTranslateConfigRefHandler(pool.daoRefResourceTranslateConfigs, config)
	} else {
		// if exist delete and put then
		pool.daoRefResourceTranslateConfigs.Remove(config)
		ResourceTranslateConfigRefHandler(pool.daoRefResourceTranslateConfigs, config)
	}
	return nil
}
func (pool *inMemoryTranslationPool) DeleteResourceTranslateConfig(config dao.ResourceTranslateConfig) error {
	pool.willApplyResourceTranslateConfig = append(pool.willApplyResourceTranslateConfig, willApplyResourceTranslateConfig{
		isSave:                  false,
		ResourceTranslateConfig: config,
	})
	id := resourceTranslateConfigIDFunc(config)
	_, exist := pool.daoRefResourceTranslateConfigs.FindById(id)
	if exist {
		pool.daoRefResourceTranslateConfigs.Remove(config)
	}
	return nil
}

func (pool *inMemoryTranslationPool) GetMaxRegexResourceConfigId() int64 {
	return pool.maxRegexResourceConfigID
}
func (pool *inMemoryTranslationPool) MaxRegexResourceConfigIdIncrement() {
	pool.maxRegexResourceConfigID++
}

func (pool *inMemoryTranslationPool) ListAllRegexResourceConfig() ([]dao.RegexResourceConfig, error) {
	var result []dao.RegexResourceConfig
	result = pool.daoRefRegexResourceConfigs.ListUnSorted()
	return result, nil
}

func (pool *inMemoryTranslationPool) GetRegexResourceConfigByGroupNameAndRegex(groupName, regex string) (*dao.RegexResourceConfig, error) {
	var items []dao.RegexResourceConfig
	items = pool.daoRefRegexResourceConfigs.FindByRef(regexResourceConfigRefOfGroupNameAndRegex, groupName, regex)
	if len(items) == 0 {
		return nil, nil
	}
	return &items[0], nil
}
func (pool *inMemoryTranslationPool) SaveRegexResourceConfig(config dao.RegexResourceConfig) error {
	pool.willApplyRegexResourceConfig = append(pool.willApplyRegexResourceConfig, willApplyRegexResourceConfig{
		isSave:              true,
		regexResourceConfig: config,
	})
	id := regexResourceConfigIDFunc(config)
	_, exist := pool.daoRefRegexResourceConfigs.FindById(id)
	if !exist {
		// if not exist put then
		RegexResourceConfigRefHandler(pool.daoRefRegexResourceConfigs, config)
	} else {
		// if exist delete and put then
		pool.daoRefRegexResourceConfigs.Remove(config)
		RegexResourceConfigRefHandler(pool.daoRefRegexResourceConfigs, config)
	}
	return nil
}
func (pool *inMemoryTranslationPool) DeleteRegexResourceConfig(config dao.RegexResourceConfig) error {
	pool.willApplyRegexResourceConfig = append(pool.willApplyRegexResourceConfig, willApplyRegexResourceConfig{
		isSave:              false,
		regexResourceConfig: config,
	})
	id := regexResourceConfigIDFunc(config)
	_, exist := pool.daoRefRegexResourceConfigs.FindById(id)
	if exist {
		pool.daoRefRegexResourceConfigs.Remove(config)
	}
	return nil
}

func (pool *inMemoryTranslationPool) ListAllRegexResourceTranslateConfig() ([]dao.RegexResourceTranslateConfig, error) {
	var result []dao.RegexResourceTranslateConfig
	result = pool.daoRefRegexResourceTranslateConfigs.ListUnSorted()
	return result, nil
}

func (pool *inMemoryTranslationPool) GetRegexResourceTranslateConfigByRegexId(regexId int64) ([]dao.RegexResourceTranslateConfig, error) {
	var items []dao.RegexResourceTranslateConfig
	items = pool.daoRefRegexResourceTranslateConfigs.FindByRef(regexResourceTranslateConfigRefOfRegexIdAndLanguageCode, regexId)
	return items, nil
}
func (pool *inMemoryTranslationPool) GetRegexResourceTranslateConfigByRegexIdAndLanguageCode(regexId int64, languageCode string) (*dao.RegexResourceTranslateConfig, error) {
	var items []dao.RegexResourceTranslateConfig
	items = pool.daoRefRegexResourceTranslateConfigs.FindByRef(regexResourceTranslateConfigRefOfRegexIdAndLanguageCode, regexId, languageCode)
	if len(items) == 0 {
		return nil, nil
	}
	return &items[0], nil
}
func (pool *inMemoryTranslationPool) SaveRegexResourceTranslateConfig(config dao.RegexResourceTranslateConfig) error {
	pool.willApplyRegexResourceTranslateConfig = append(pool.willApplyRegexResourceTranslateConfig, willApplyRegexResourceTranslateConfig{
		isSave:                       true,
		regexResourceTranslateConfig: config,
	})
	id := regexResourceTranslateConfigIDFunc(config)
	_, exist := pool.daoRefRegexResourceTranslateConfigs.FindById(id)
	if !exist {
		// if not exist put then
		RegexResourceTranslateConfigRefHandler(pool.daoRefRegexResourceTranslateConfigs, config)
	} else {
		// if exist delete and put then
		pool.daoRefRegexResourceTranslateConfigs.Remove(config)
		RegexResourceTranslateConfigRefHandler(pool.daoRefRegexResourceTranslateConfigs, config)
	}
	return nil
}
func (pool *inMemoryTranslationPool) DeleteRegexResourceTranslateConfig(config dao.RegexResourceTranslateConfig) error {
	pool.willApplyRegexResourceTranslateConfig = append(pool.willApplyRegexResourceTranslateConfig, willApplyRegexResourceTranslateConfig{
		isSave:                       false,
		regexResourceTranslateConfig: config,
	})
	id := regexResourceTranslateConfigIDFunc(config)
	_, exist := pool.daoRefRegexResourceTranslateConfigs.FindById(id)
	if exist {
		pool.daoRefRegexResourceTranslateConfigs.Remove(config)
	}
	return nil
}

func (pool *inMemoryTranslationPool) Submit() error {
	// apply resource translate config
	if len(pool.willApplyResourceTranslateConfig) != 0 {
		handleFunc := func(isApply bool, cache []dao.ResourceTranslateConfig, tx *gorm.DB) error {
			if len(cache) == 0 {
				return nil
			}
			var err error
			if isApply {
				err = tx.Save(&cache).Error
			} else {
				err = tx.Delete(&cache).Error
			}
			return err
		}

		isApply := pool.willApplyResourceTranslateConfig[0].isSave
		var cache []dao.ResourceTranslateConfig
		for _, willRtc := range pool.willApplyResourceTranslateConfig {
			willRtc := willRtc
			if willRtc.isSave == isApply {
				cache = append(cache, willRtc.ResourceTranslateConfig)
			} else {
				if err := handleFunc(isApply, cache, pool.caasDB); err != nil {
					return err
				}
				isApply = !isApply
				cache = make([]dao.ResourceTranslateConfig, 0)
				cache = append(cache, willRtc.ResourceTranslateConfig)
			}
		}
		if err := handleFunc(isApply, cache, pool.caasDB); err != nil {
			return err
		}
	}

	// apply regex resource config
	if len(pool.willApplyRegexResourceConfig) != 0 {
		handleFunc := func(isApply bool, cache []dao.RegexResourceConfig, tx *gorm.DB) error {
			if len(cache) == 0 {
				return nil
			}
			var err error
			if isApply {
				err = tx.Save(&cache).Error
			} else {
				err = tx.Delete(&cache).Error
			}
			return err
		}

		isApply := pool.willApplyRegexResourceConfig[0].isSave
		var cache []dao.RegexResourceConfig
		for _, willRrc := range pool.willApplyRegexResourceConfig {
			willRrc := willRrc
			if willRrc.isSave == isApply {
				cache = append(cache, willRrc.regexResourceConfig)
			} else {
				if err := handleFunc(isApply, cache, pool.caasDB); err != nil {
					return err
				}
				isApply = !isApply
				cache = make([]dao.RegexResourceConfig, 0)
				cache = append(cache, willRrc.regexResourceConfig)
			}
		}
		if err := handleFunc(isApply, cache, pool.caasDB); err != nil {
			return err
		}
	}

	// apply regex resource translate config
	if len(pool.willApplyRegexResourceTranslateConfig) != 0 {
		handleFunc := func(isApply bool, cache []dao.RegexResourceTranslateConfig, tx *gorm.DB) error {
			if len(cache) == 0 {
				return nil
			}
			var err error
			if isApply {
				err = tx.Save(&cache).Error
			} else {
				err = tx.Delete(&cache).Error
			}
			return err
		}

		isApply := pool.willApplyRegexResourceTranslateConfig[0].isSave
		var cache []dao.RegexResourceTranslateConfig
		for _, willRrtc := range pool.willApplyRegexResourceTranslateConfig {
			willRrtc := willRrtc
			if willRrtc.isSave == isApply {
				cache = append(cache, willRrtc.regexResourceTranslateConfig)
			} else {
				if err := handleFunc(isApply, cache, pool.caasDB); err != nil {
					return err
				}
				isApply = !isApply
				cache = make([]dao.RegexResourceTranslateConfig, 0)
				cache = append(cache, willRrtc.regexResourceTranslateConfig)
			}
		}
		if err := handleFunc(isApply, cache, pool.caasDB); err != nil {
			return err
		}
	}
	return nil
}

func RegexResourceConfigRefHandler(ref ObjectsRef[dao.RegexResourceConfig], item dao.RegexResourceConfig) {
	ref.Ref(regexResourceConfigRefOfGroupNameAndRegex, item, item.GroupName, item.Regex)
}

func RegexResourceTranslateConfigRefHandler(ref ObjectsRef[dao.RegexResourceTranslateConfig], item dao.RegexResourceTranslateConfig) {
	ref.Ref(regexResourceTranslateConfigRefOfRegexIdAndLanguageCode, item, item.RegexID, item.LanguageCode)
}
