package internal

import (
	"fmt"
	"reflect"
	"testing"

	permissionv1 "harmonycloud.cn/unifiedportal/api-definition/permission/v1"
)

func Test_configsConvert2RoleIds(t *testing.T) {
	params := []struct {
		input   permissionv1.MenuConfigs
		wantErr bool
		output  []int
	}{

		{
			input: permissionv1.MenuConfigs{
				{
					Auths: []permissionv1.Role{
						permissionv1.PlatformNormal,
						permissionv1.PlatformNormal,
					},
				},
				{
					Auths: []permissionv1.Role{
						permissionv1.WorkspaceNormal,
						permissionv1.PlatformNormal,
						permissionv1.OrganManager,
					},
				},
			},
			wantErr: false,
			output:  []int{2, 3, 6},
		},
		{
			input: permissionv1.MenuConfigs{
				{
					Auths: []permissionv1.Role{
						permissionv1.WorkspaceNormal,
					},
				},
				{
					Auths: []permissionv1.Role{
						permissionv1.PlatformNormal,
						permissionv1.OrganManager,
						permissionv1.PlatformNormal,
						permissionv1.PlatformNormal,
					},
					Children: permissionv1.MenuConfigs{
						{
							Auths: []permissionv1.Role{
								permissionv1.WorkspaceNormal,
								permissionv1.PlatformOperatorManager,
							},
						},
					},
				},
			},
			wantErr: false,
			output:  []int{2, 3, 6, 9},
		},
		{
			input: permissionv1.MenuConfigs{
				{
					Auths: []permissionv1.Role{
						permissionv1.PlatformNormal,
						permissionv1.PlatformNormal,
					},
					Children: permissionv1.MenuConfigs{
						{
							Auths: []permissionv1.Role{
								permissionv1.PlatformOperatorManager,
							},
							Children: permissionv1.MenuConfigs{
								{
									Auths: []permissionv1.Role{
										permissionv1.PlatformManager,
									},
								},
							},
						},
					},
				},
				{
					Auths: []permissionv1.Role{
						permissionv1.WorkspaceNormal,
						permissionv1.PlatformNormal,
						permissionv1.OrganManager,
						permissionv1.PlatformNormal,
						permissionv1.PlatformNormal,
					},
					Children: permissionv1.MenuConfigs{
						{
							Auths: []permissionv1.Role{
								permissionv1.PlatformOperatorManager,
							},
							Children: permissionv1.MenuConfigs{
								{
									Auths: []permissionv1.Role{
										permissionv1.PlatformManager,
									},
								},
							},
						},
					},
				},
			},
			wantErr: false,
			output:  []int{2, 3, 6, 9, 10},
		},
		{
			input: permissionv1.MenuConfigs{
				{
					Auths: []permissionv1.Role{
						permissionv1.WorkspaceNormal,
						permissionv1.OrganManager,
						permissionv1.PlatformNormal,
						permissionv1.PlatformNormal,
					},
				},
				{
					Auths: []permissionv1.Role{
						"",
					},
				},
			},
			wantErr: true,
			output:  nil,
		},
	}
	for index, param := range params {
		name := fmt.Sprintf("Test_configConvert2RoleIds%d", index)
		t.Run(name, func(t *testing.T) {
			result, err := configsConvert2RoleIds(param.input)
			if (err != nil) != param.wantErr {
				t.Errorf("input is %+v,want err %v,err is %v", param.input, param.wantErr, err)
				return
			}
			if !reflect.DeepEqual(result, param.output) {
				t.Errorf("result is not eqault expect,result is %+v,expect is %+v", result, param.output)
			}

		})
	}
}

func Test_configConvert2RoleIds(t *testing.T) {
	params := []struct {
		input   permissionv1.MenuConfig
		wantErr bool
		output  []int
	}{

		{
			input: permissionv1.MenuConfig{
				Auths: []permissionv1.Role{
					permissionv1.WorkspaceNormal,
					permissionv1.PlatformNormal,
					permissionv1.OrganManager,
					permissionv1.PlatformNormal,
					permissionv1.PlatformNormal,
				},
			},
			wantErr: false,
			output:  []int{2, 3, 6},
		},
		{
			input: permissionv1.MenuConfig{
				Auths: []permissionv1.Role{
					permissionv1.WorkspaceNormal,
					permissionv1.PlatformNormal,
					permissionv1.OrganManager,
					permissionv1.PlatformNormal,
					permissionv1.PlatformNormal,
				},
				Children: permissionv1.MenuConfigs{
					{
						Auths: []permissionv1.Role{
							permissionv1.PlatformOperatorManager,
						},
					},
				},
			},
			wantErr: false,
			output:  []int{2, 3, 6, 9},
		},
		{
			input: permissionv1.MenuConfig{
				Auths: []permissionv1.Role{
					permissionv1.WorkspaceNormal,
					permissionv1.PlatformNormal,
					permissionv1.OrganManager,
					permissionv1.PlatformNormal,
					permissionv1.PlatformNormal,
				},
				Children: permissionv1.MenuConfigs{
					{
						Auths: []permissionv1.Role{
							permissionv1.PlatformOperatorManager,
						},
						Children: permissionv1.MenuConfigs{
							{
								Auths: []permissionv1.Role{
									permissionv1.PlatformManager,
								},
							},
						},
					},
				},
			},
			wantErr: false,
			output:  []int{2, 3, 6, 9, 10},
		},
		{
			input: permissionv1.MenuConfig{
				Auths: []permissionv1.Role{
					permissionv1.WorkspaceNormal,
					"",
					permissionv1.OrganManager,
					permissionv1.PlatformNormal,
					permissionv1.PlatformNormal,
				},
			},
			wantErr: true,
			output:  nil,
		},
	}
	for index, param := range params {
		name := fmt.Sprintf("Test_configConvert2RoleIds%d", index)
		t.Run(name, func(t *testing.T) {
			result, err := configConvert2RoleIds(param.input)
			if (err != nil) != param.wantErr {
				t.Errorf("input is %+v,want err %v,err is %v", param.input, param.wantErr, err)
				return
			}
			if !reflect.DeepEqual(result, param.output) {
				t.Errorf("result is not eqault expect,result is %+v,expect is %+v", result, param.output)
			}

		})
	}
}

func Test_configConvert2RoleIdsUnIncludeChild(t *testing.T) {
	params := []struct {
		input   permissionv1.MenuConfig
		wantErr bool
		output  []int
	}{

		{
			input: permissionv1.MenuConfig{
				Auths: []permissionv1.Role{
					permissionv1.WorkspaceNormal,
					permissionv1.PlatformNormal,
					permissionv1.OrganManager,
					permissionv1.PlatformNormal,
					permissionv1.PlatformNormal,
				},
			},
			wantErr: false,
			output:  []int{2, 3, 6},
		},
		{
			input: permissionv1.MenuConfig{
				Auths: []permissionv1.Role{
					permissionv1.WorkspaceNormal,
					permissionv1.PlatformNormal,
					permissionv1.OrganManager,
					permissionv1.PlatformNormal,
					permissionv1.PlatformNormal,
				},
				Children: permissionv1.MenuConfigs{
					{
						Auths: []permissionv1.Role{
							permissionv1.PlatformOperatorManager,
						},
					},
				},
			},
			wantErr: false,
			output:  []int{2, 3, 6},
		},
		{
			input: permissionv1.MenuConfig{
				Auths: []permissionv1.Role{
					permissionv1.WorkspaceNormal,
					"",
					permissionv1.OrganManager,
					permissionv1.PlatformNormal,
					permissionv1.PlatformNormal,
				},
			},
			wantErr: true,
			output:  nil,
		},
	}
	for index, param := range params {
		name := fmt.Sprintf("Test_configConvert2RoleIdsUnIncludeChild_%d", index)
		t.Run(name, func(t *testing.T) {
			result, err := configConvert2RoleIdsUnIncludeChild(param.input)
			if (err != nil) != param.wantErr {
				t.Errorf("input is %+v,want err %v,err is %v", param.input, param.wantErr, err)
				return
			}
			if !reflect.DeepEqual(result, param.output) {
				t.Errorf("result is not eqault expect,result is %+v,expect is %+v", result, param.output)
			}

		})
	}
}
