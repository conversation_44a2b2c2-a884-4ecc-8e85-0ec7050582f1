package internal

import (
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"testing"

	permissionv1 "harmonycloud.cn/unifiedportal/api-definition/permission/v1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao"
	"k8s.io/apimachinery/pkg/util/sets"
)

var lastID int64

func Test_ObjectsRef(t *testing.T) {
	params := []struct {
		permissionv1jsonStr string
	}{
		{
			permissionv1jsonStr: "[{\"operator\":\"add\",\"configs\":[{\"cloudServiceName\":\"unified_platform\",\"level\":\"platform\",\"name\":\"工作空间\",\"codeSuffix\":\"sys_work_area\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/workspace\",\"sortCode\":0,\"auths\":[\"platform_normal\",\"platform_resource_mgr\",\"platform_dev_mgr\",\"platform_operator_mgr\",\"platform_mgr\"],\"annotations\":{\"ceiling\":true}}]},{\"operator\":\"add\",\"configs\":[{\"cloudServiceName\":\"unified_platform\",\"level\":\"platform\",\"name\":\"平台运营\",\"codeSuffix\":\"sys_operate_platform\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/organization/list\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"企业管理\",\"codeSuffix\":\"dev_organization\",\"type\":\"menu\",\"icon\":\"market\",\"method\":\"get\",\"url\":\"/organization/list\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"组织管理\",\"codeSuffix\":\"sys_organization_manage\",\"type\":\"menu\",\"icon\":\"organizational-management-menu\",\"method\":\"get\",\"url\":\"/organization/list\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/organizations/page\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/organizations\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑\",\"codeSuffix\":\"edit\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/organizations/{id}\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/organizations/{id}\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"配额管理\",\"codeSuffix\":\"quota_manage\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"]}],\"annotations\":{\"titleDescription\":\"不同组织创建、配额管理等\"}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"用户管理\",\"codeSuffix\":\"sys_system_users\",\"type\":\"menu\",\"icon\":\"user-menu\",\"method\":\"get\",\"url\":\"/system/user\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/system/user\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/users\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑\",\"codeSuffix\":\"edit\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/users/{userId}\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/users/{userId}\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"密码重置\",\"codeSuffix\":\"resetPassword\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/users/updatePassword\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"关联角色\",\"codeSuffix\":\"bindRole\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/user-roles/users/{userId}/roles/{rolesId}\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"]}]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"系统角色\",\"codeSuffix\":\"sys_system_roles\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/system/role\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/system/role\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/roles\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑\",\"codeSuffix\":\"edit\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/roles/{roleId}\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/roles/{roleId}\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"分配角色权限\",\"codeSuffix\":\"distribute_privilege\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/roles/{roleId}\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"]}]}],\"annotations\":{\"ceiling\":true}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"系统运营\",\"codeSuffix\":\"sys_operate\",\"type\":\"menu\",\"icon\":\"market\",\"method\":\"get\",\"url\":\"/config/noticeConfig\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"消息通道\",\"codeSuffix\":\"sys_alarm_notification\",\"type\":\"menu\",\"icon\":\"Alarm-notification-menu\",\"method\":\"get\",\"url\":\"/config/noticeConfig\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"邮箱查看\",\"codeSuffix\":\"emailquery\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/config/alertemail/query\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"webhook查看\",\"codeSuffix\":\"webhookquery\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/config/webhook\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"邮箱测试\",\"codeSuffix\":\"emailtest\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/config/alertemail/testconnect\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"webhook测试\",\"codeSuffix\":\"webhooktest\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/config/webhook/testconnect\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"邮箱保存\",\"codeSuffix\":\"emailsave\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/config/alertemail\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"webhook保存\",\"codeSuffix\":\"webhooksave\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/config/webhook\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"]}]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"操作审计\",\"codeSuffix\":\"system_audit\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/systemaudit/audit\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"apps/replicasets\",\"batch/jobs\",\"batch/cronjobs\",\"pods\",\"nodes\"]}},{\"cloudServiceName\":\"unified_platform\",\"level\":\"platform\",\"name\":\"主题配置\",\"codeSuffix\":\"skyview_custom_settings\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/individuation\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"],\"invisible\":true,\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/system/loginPageSettings\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"],\"invisible\":true},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"修改\",\"codeSuffix\":\"edit\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/system/loginPageSettings\",\"sortCode\":0,\"auths\":[\"platform_operator_mgr\"],\"invisible\":true}],\"annotations\":{\"ceiling\":true}}],\"annotations\":{\"ceiling\":true}}],\"annotations\":{\"ceiling\":true}}]},{\"operator\":\"add\",\"configs\":[{\"cloudServiceName\":\"unified_platform\",\"level\":\"platform\",\"name\":\"平台管理\",\"codeSuffix\":\"sys_platform_mgr\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/quotacenter/space/overview\",\"sortCode\":0,\"auths\":[\"platform_dev_mgr\",\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"unified_platform\",\"level\":\"platform\",\"name\":\"系统总览\",\"codeSuffix\":\"sys_overview\",\"type\":\"menu\",\"icon\":\"system-resources-overview-menu\",\"method\":\"get\",\"url\":\"/quotacenter/space/overview\",\"sortCode\":0,\"auths\":[\"platform_dev_mgr\",\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"\",\"sortCode\":0,\"auths\":[\"platform_dev_mgr\",\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"admissionregistration.k8s.io/mutatingwebhookconfigurations\":[\"get\",\"list\"],\"admissionregistration.k8s.io/validatingwebhookconfigurations\":[\"get\",\"list\"],\"apiextensions.k8s.io/customresourcedefinitions\":[\"get\",\"list\"],\"apiregistration.k8s.io/apiservices\":[\"get\",\"list\"],\"application.decompile.harmonycloud.cn/decompileconfigs\":[\"get\",\"list\"],\"apps/controllerrevisions\":[\"get\",\"list\"],\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"autoscaling.alibabacloud.com/cronhorizontalpodautoscalers\":[\"get\",\"list\"],\"autoscaling/horizontalpodautoscalers\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"bifrost.heimdallr.harmonycloud.cn/subnets\":[\"get\",\"list\"],\"bindings\":[\"get\",\"list\"],\"certificates.k8s.io/certificatesigningrequests\":[\"get\",\"list\"],\"cluster.core.oam.dev/clustergateways\":[\"get\",\"list\"],\"componentstatuses\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"coordination.k8s.io/leases\":[\"get\",\"list\"],\"core.oam.dev/applicationrevisions\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\"],\"core.oam.dev/componentdefinitions\":[\"get\",\"list\"],\"core.oam.dev/policydefinitions\":[\"get\",\"list\"],\"core.oam.dev/resourcetrackers\":[\"get\",\"list\"],\"core.oam.dev/scopedefinitions\":[\"get\",\"list\"],\"core.oam.dev/traitdefinitions\":[\"get\",\"list\"],\"core.oam.dev/workflows\":[\"get\",\"list\"],\"core.oam.dev/workflowstepdefinitions\":[\"get\",\"list\"],\"core.oam.dev/workloaddefinitions\":[\"get\",\"list\"],\"crd.projectcalico.org/bgpconfigurations\":[\"get\",\"list\"],\"crd.projectcalico.org/bgppeers\":[\"get\",\"list\"],\"crd.projectcalico.org/blockaffinities\":[\"get\",\"list\"],\"crd.projectcalico.org/caliconodestatuses\":[\"get\",\"list\"],\"crd.projectcalico.org/clusterinformations\":[\"get\",\"list\"],\"crd.projectcalico.org/felixconfigurations\":[\"get\",\"list\"],\"crd.projectcalico.org/globalnetworkpolicies\":[\"get\",\"list\"],\"crd.projectcalico.org/globalnetworksets\":[\"get\",\"list\"],\"crd.projectcalico.org/hostendpoints\":[\"get\",\"list\"],\"crd.projectcalico.org/ipamblocks\":[\"get\",\"list\"],\"crd.projectcalico.org/ipamconfigs\":[\"get\",\"list\"],\"crd.projectcalico.org/ipamhandles\":[\"get\",\"list\"],\"crd.projectcalico.org/ippools\":[\"get\",\"list\"],\"crd.projectcalico.org/ipreservations\":[\"get\",\"list\"],\"crd.projectcalico.org/kubecontrollersconfigurations\":[\"get\",\"list\"],\"crd.projectcalico.org/networkpolicies\":[\"get\",\"list\"],\"crd.projectcalico.org/networksets\":[\"get\",\"list\"],\"discovery.k8s.io/endpointslices\":[\"get\",\"list\"],\"endpoints\":[\"get\",\"list\"],\"es.middleware.hc.cn/esclusters\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"events.k8s.io/events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"extensions/ingresses\":[\"get\",\"list\"],\"flowcontrol.apiserver.k8s.io/flowschemas\":[\"get\",\"list\"],\"flowcontrol.apiserver.k8s.io/prioritylevelconfigurations\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\",\"list\"],\"harmonycloud.cn/podsecuritypolicytemplates\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/iplocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"k8s.cni.cncf.io/network-attachment-definitions\":[\"get\",\"list\"],\"keda.sh/clustertriggerauthentications\":[\"get\",\"list\"],\"keda.sh/scaledjobs\":[\"get\",\"list\"],\"keda.sh/scaledobjects\":[\"get\",\"list\"],\"keda.sh/triggerauthentications\":[\"get\",\"list\"],\"limitranges\":[\"get\",\"list\"],\"metrics.k8s.io/nodes\":[\"get\",\"list\"],\"metrics.k8s.io/pods\":[\"get\",\"list\"],\"monitoring.coreos.com/alertmanagers\":[\"get\",\"list\"],\"monitoring.coreos.com/podmonitors\":[\"get\",\"list\"],\"monitoring.coreos.com/prometheuses\":[\"get\",\"list\"],\"monitoring.coreos.com/prometheusrules\":[\"get\",\"list\"],\"monitoring.coreos.com/servicemonitors\":[\"get\",\"list\"],\"mysql.middleware.harmonycloud.cn/mysqlbackups\":[\"get\",\"list\"],\"mysql.middleware.harmonycloud.cn/mysqlbackupschedules\":[\"get\",\"list\"],\"mysql.middleware.harmonycloud.cn/mysqlclusters\":[\"get\",\"list\"],\"mysql.middleware.harmonycloud.cn/mysqlreplicates\":[\"get\",\"list\"],\"mystra.heimdallr.harmonycloud.cn/clusternetworkpolicies\":[\"get\",\"list\"],\"mystra.heimdallr.harmonycloud.cn/identities\":[\"get\",\"list\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingressclasses\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\"],\"networking.k8s.io/networkpolicies\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"podtemplates\":[\"get\",\"list\"],\"policy/poddisruptionbudgets\":[\"get\",\"list\"],\"rbac.authorization.k8s.io/clusterrolebindings\":[\"get\",\"list\"],\"rbac.authorization.k8s.io/clusterroles\":[\"get\",\"list\"],\"rbac.authorization.k8s.io/rolebindings\":[\"get\",\"list\"],\"rbac.authorization.k8s.io/roles\":[\"get\",\"list\"],\"redis.middleware.hc.cn/redisclusters\":[\"get\",\"list\"],\"replicationcontrollers\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"rocketmq.middleware.hc.cn/brokerclusters\":[\"get\",\"list\"],\"rollouts.kruise.io/batchreleases\":[\"get\",\"list\"],\"rollouts.kruise.io/rollouts\":[\"get\",\"list\"],\"scheduling.k8s.io/priorityclasses\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"snapshot.storage.k8s.io/volumesnapshotclasses\":[\"get\",\"list\"],\"snapshot.storage.k8s.io/volumesnapshotcontents\":[\"get\",\"list\"],\"snapshot.storage.k8s.io/volumesnapshots\":[\"get\",\"list\"],\"standard.oam.dev/rollouts\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/aggregatedresources\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusterresources\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"watch\",\"get\",\"list\",\"list\"],\"stellaris.harmonycloud.cn/clustersets\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterhorizontalpodautoscalers\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourceaggregatepolicies\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourceaggregaterules\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourceschedulepolicies\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/namespacemappings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/resourceaggregatepolicies\":[\"get\",\"list\"],\"storage.k8s.io/csidrivers\":[\"get\",\"list\"],\"storage.k8s.io/csinodes\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"],\"storage.k8s.io/volumeattachments\":[\"get\",\"list\"],\"webhook.harmonycloud.cn/isolationwebhookconfigurations\":[\"get\",\"list\"]}}}],\"annotations\":{\"ceiling\":true}},{\"cloudServiceName\":\"unified_platform\",\"level\":\"platform\",\"name\":\"云服务\",\"codeSuffix\":\"sys_cloud_service\",\"type\":\"menu\",\"icon\":\"resources-menu\",\"method\":\"get\",\"url\":\"/cloundserving/list\",\"sortCode\":0,\"auths\":[\"platform_dev_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"\",\"sortCode\":0,\"auths\":[\"platform_dev_mgr\",\"platform_mgr\"],\"annotations\":{\"ceiling\":true}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"基础服务\",\"codeSuffix\":\"cloud_service_base_service\",\"type\":\"menu\",\"icon\":\"market\",\"method\":\"get\",\"url\":\"/cloundserving/space/overview\",\"sortCode\":0,\"auths\":[\"platform_dev_mgr\",\"platform_mgr\"],\"annotations\":{\"ceiling\":true}}],\"annotations\":{\"ceiling\":true}},{\"cloudServiceName\":\"unified_platform\",\"level\":\"platform\",\"name\":\"集群管理\",\"codeSuffix\":\"sys_cluster_manage\",\"type\":\"menu\",\"icon\":\"cluster-menu\",\"method\":\"get\",\"url\":\"/cluster/set/clusterlist\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"集群总览\",\"codeSuffix\":\"sys_cluster_overview\",\"type\":\"menu\",\"icon\":\"cluster-space-overview-menu\",\"method\":\"get\",\"url\":\"/cluster/space/ClusterOverview\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"daemonsets\":[\"get\",\"list\"],\"deployments\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"replicasets\":[\"get\",\"list\"],\"replicationcontrollers\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"statefulsets\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"apps/replicasets\",\"batch/jobs\",\"batch/cronjobs\",\"pods\",\"services\",\"secrets\",\"configmaps\",\"persistentvolumeclaims\",\"persistentvolumes\",\"storage.k8s.io/storageclasses\",\"serviceaccounts\",\"namespaces\",\"nodes\",\"resourcequotas\",\"harmonycloud.cn/nodepools\",\"heimdallr.harmonycloud.cn/hdareas\",\"heimdallr.harmonycloud.cn/hdblocks\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/hdpools\",\"heimdallr.harmonycloud.cn/networkdetails\",\"heimdallr.harmonycloud.cn/networkresources\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"主机管理\",\"codeSuffix\":\"sys_node_manage\",\"type\":\"menu\",\"icon\":\"nodes-menu\",\"method\":\"get\",\"url\":\"/cluster/space/node/normal\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"普通视角\",\"codeSuffix\":\"sys_node_manage_normal\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/cluster/space/node/normal\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/nodes/{nodeName}/resources,/clusters/{clusterName}/nodes/{nodeName}/nodeInfo,/clusters/{clusterName}/nodes/{nodeName}/events\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增主机\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/clusters/{clusterName}/nodes\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"主机维护\",\"codeSuffix\":\"nodeMaintain\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"create\",\"delete\",\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"delete\",\"get\",\"patch\",\"create\",\"update\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"delete\",\"get\",\"patch\",\"create\",\"update\"],\"namespaces\":[\"create\",\"delete\",\"get\",\"patch\",\"update\"],\"nodes\":[\"create\",\"get\",\"list\",\"patch\",\"update\"],\"stellaris.harmonycloud.cn/clusters\":[\"delete\",\"watch\",\"get\",\"list\",\"patch\",\"create\",\"update\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"应用迁移\",\"codeSuffix\":\"nodeDrain\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"create\",\"delete\",\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"delete\",\"get\",\"patch\",\"create\",\"update\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"delete\",\"get\",\"patch\",\"create\",\"update\"],\"namespaces\":[\"create\",\"delete\",\"get\",\"patch\",\"update\"],\"nodes\":[\"create\",\"get\",\"list\",\"patch\",\"update\"],\"stellaris.harmonycloud.cn/clusters\":[\"delete\",\"watch\",\"get\",\"list\",\"patch\",\"create\",\"update\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"主机隔离\",\"codeSuffix\":\"nodeIsolate\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"create\",\"delete\",\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"delete\",\"get\",\"patch\",\"create\",\"update\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"delete\",\"get\",\"patch\",\"create\",\"update\"],\"namespaces\":[\"create\",\"delete\",\"get\",\"patch\",\"update\"],\"nodes\":[\"create\",\"get\",\"list\",\"patch\",\"update\"],\"stellaris.harmonycloud.cn/clusters\":[\"delete\",\"watch\",\"get\",\"list\",\"patch\",\"create\",\"update\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"主机校验\",\"codeSuffix\":\"skyview_node_validate\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/clusters/{clusterName}/nodes/{nodeName}/status\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"调度\",\"codeSuffix\":\"dispatch\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/clusters/{clusterName}/nodes/{nodeName}/cordon,/clusters/{clusterName}/nodes/{nodeName}/uncordon\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"patch\",\"update\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\",\"patch\",\"update\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"patch\",\"update\"],\"pods\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑注解\",\"codeSuffix\":\"editAnnotations\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/clusters/{clusterName}/nodes/{nodeName}/annotations\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"patch\",\"update\"],\"pods\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑标签\",\"codeSuffix\":\"editLabel\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/clusters/{clusterName}/nodes/{nodeName}/labels\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"patch\",\"update\"],\"pods\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑污点\",\"codeSuffix\":\"editTaint\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/clusters/{clusterName}/nodes/{nodeName}/taints\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"patch\",\"update\"],\"pods\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"主机下线\",\"codeSuffix\":\"nodeOffLine\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/clusters/{clusterName}/nodes/{nodeName}/drain\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]}],\"annotations\":{\"resources\":[\"nodes\",\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"events\",\"isolate.harmonycloud.cn/isolatelocks\",\"isolate.harmonycloud.cn/hleases\",\"harmonycloud.cn/nodepools\",\"pods\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"资源池视角\",\"codeSuffix\":\"sys_node_manage_resource_pool\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/cluster/space/node/resourcepool\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"nodes\",\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"events\",\"isolate.harmonycloud.cn/isolatelocks\",\"isolate.harmonycloud.cn/hleases\",\"harmonycloud.cn/nodepools\",\"pods\"]}}],\"annotations\":{\"resources\":[\"nodes\",\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"events\",\"isolate.harmonycloud.cn/isolatelocks\",\"isolate.harmonycloud.cn/hleases\",\"harmonycloud.cn/nodepools\",\"pods\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"添加集群\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/token\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\",\"create\",\"update\",\"patch\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑标签\",\"codeSuffix\":\"editLabel\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/clusters/{clusterName}/labels\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\",\"create\",\"update\",\"patch\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑描述\",\"codeSuffix\":\"editDescribe\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/clusters/{clusterName}/description\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\",\"create\",\"update\",\"patch\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/clusters/{clusterName}\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"stellaris.harmonycloud.cn/clusters\":[\"watch\",\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"命名空间\",\"codeSuffix\":\"sys_cluster_namespace_manager\",\"type\":\"menu\",\"icon\":\"namespace-menu\",\"method\":\"get\",\"url\":\"/cluster/space/namespace\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/namespaces\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/statefulsets\":[\"list\",\"get\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/clusters/{clusterName}/namespaces\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/statefulsets\":[\"list\",\"get\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\",\"patch\",\"create\",\"update\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\",\"patch\",\"create\",\"update\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑元数据\",\"codeSuffix\":\"edit\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/namespaces/{namespaces}\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/statefulsets\":[\"list\",\"get\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\",\"patch\",\"create\",\"update\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\",\"patch\",\"create\",\"update\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑描述\",\"codeSuffix\":\"editDescription\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/namespaces/{namespaces}/description\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/statefulsets\":[\"list\",\"get\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\",\"patch\",\"create\",\"update\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\",\"patch\",\"create\",\"update\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/namespaces/{namespaces}\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/statefulsets\":[\"list\",\"get\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\",\"patch\",\"create\",\"update\",\"delete\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\",\"patch\",\"create\",\"update\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"nodes\",\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"events\",\"isolate.harmonycloud.cn/isolatelocks\",\"isolate.harmonycloud.cn/hleases\",\"harmonycloud.cn/nodepools\",\"pods\",\"resourcequotas\",\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"batch/cronjobs\",\"batch/jobs\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"集群组件\",\"codeSuffix\":\"sys_cluster_component\",\"type\":\"menu\",\"icon\":\"components-menu\",\"method\":\"get\",\"url\":\"/cluster/space/clusterComp\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"组件详情\",\"codeSuffix\":\"detail\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/components/{componentName}\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"批量新增\",\"codeSuffix\":\"batch\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/clusters/{clusterName}/components/switches\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑\",\"codeSuffix\":\"edit\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/clusters/{clusterName}/components/{componentName}\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\",\"patch\",\"update\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"自动识别\",\"codeSuffix\":\"autoRecognition\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/components/scan\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\",\"patch\",\"update\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"接入\",\"codeSuffix\":\"join\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/clusters/{clusterName}/components\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\",\"patch\",\"update\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"取消接入\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/clusters/{clusterName}/components/{componentName}\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\",\"patch\",\"update\"]}}}],\"annotations\":{\"resources\":[\"nodes\",\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"events\",\"harmonycloud.cn/nodepools\",\"resourcequotas\",\"pods\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"工作负载\",\"codeSuffix\":\"sys_clusters_workloads\",\"type\":\"menu\",\"icon\":\"load-menu\",\"method\":\"get\",\"url\":\"/cluster/space/resource/deployment/list\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"无状态部署\",\"codeSuffix\":\"sys_clusters_deployment\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/cluster/space/resource/deployment/list\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/deployments,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment},/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/describe,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/events,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/metadata,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/pods,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/replicasets,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/replicasets/{replicaset}/yaml,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/yaml\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"skyview_deployment_add\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/clusters/{clusterName}/deployments\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"create\",\"update\"],\"apps/replicasets\":[\"get\",\"list\",\"create\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑副本数\",\"codeSuffix\":\"skyview_replicas_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"create\",\"update\"],\"apps/replicasets\":[\"get\",\"list\",\"create\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑元数据\",\"codeSuffix\":\"skyview_deployment_metadata_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"create\",\"update\"],\"apps/replicasets\":[\"get\",\"list\",\"create\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"版本管理\",\"codeSuffix\":\"skyview_deployment_version\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"create\",\"update\"],\"apps/replicasets\":[\"get\",\"list\",\"create\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查看版本yaml\",\"codeSuffix\":\"skyview_deployment_yaml_check\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"yaml对比\",\"codeSuffix\":\"skyview_deployment_yaml_compare\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"版本回滚\",\"codeSuffix\":\"skyview_deployment_version_rollback\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/revisions/{revision}/rollback\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑yaml\",\"codeSuffix\":\"skyview_deployment_yaml_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"update\"],\"apps/replicasets\":[\"get\",\"list\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"skyview_deployment_remove\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"delete\"],\"apps/replicasets\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"resourcequotas\",\"apps/deployments\",\"pods\",\"apps/replicasets\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"有状态部署\",\"codeSuffix\":\"sys_clusters_statefulset\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/cluster/space/resource/statefulset/list\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/namespaces/{namespace}/statefulsets/{statefulset},/clusters/{clusterName}/namespaces/{namespace}/statefulsets/{statefulset}/describe,/clusters/{clusterName}/namespaces/{namespace}/statefulsets/{statefulset}/events,/clusters/{clusterName}/namespaces/{namespace}/statefulsets/{statefulset}/metadata,/clusters/{clusterName}/namespaces/{namespace}/statefulsets/{statefulset}/pods,/clusters/{clusterName}/namespaces/{namespace}/statefulsets/{statefulset}/yaml,/clusters/{clusterName}/statefulsets\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/statefulsets\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"skyview_statefulset_add\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/clusters/{clusterName}/statefulsets\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/statefulsets\":[\"get\",\"list\",\"create\",\"update\"],\"namespaces\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑副本数\",\"codeSuffix\":\"skyview_replicas_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/statefulsets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑元数据\",\"codeSuffix\":\"skyview_statefulset_metadata_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/statefulsets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑yaml\",\"codeSuffix\":\"skyview_statefulset_yaml_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/statefulsets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"skyview_statefulset_remove\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/clusters/{clusterName}/namespaces/{namespace}/statefulsets/{statefulset}\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/statefulsets\":[\"get\",\"list\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"events\",\"resourcequotas\",\"apps/statefulsets\",\"pods\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"守护进程\",\"codeSuffix\":\"sys_clusters_daemonset\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/cluster/space/resource/daemonset/list\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/daemonsets,/clusters/{clusterName}/namespaces/{namespace}/daemonsets/{daemonset},/clusters/{clusterName}/namespaces/{namespace}/daemonsets/{daemonset}/describe,/clusters/{clusterName}/namespaces/{namespace}/daemonsets/{daemonset}/events,/clusters/{clusterName}/namespaces/{namespace}/daemonsets/{daemonset}/metadata,/clusters/{clusterName}/namespaces/{namespace}/daemonsets/{daemonset}/pods,/clusters/{clusterName}/namespaces/{namespace}/daemonsets/{daemonset}/yaml\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"skyview_daemonset_add\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/clusters/{clusterName}/daemonsets\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\",\"create\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑元数据\",\"codeSuffix\":\"skyview_daemonset_metadata_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑yaml\",\"codeSuffix\":\"skyview_daemonset_yaml_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"skyview_daemonset_remove\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/clusters/{clusterName}/namespaces/{namespace}/daemonsets/{daemonset}\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"apps/daemonsets\",\"pods\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"普通任务\",\"codeSuffix\":\"sys_clusters_job\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/cluster/space/resource/job/list\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/jobs,/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}/describe,/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}/events,/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}/info,/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}/metadata,/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}/pods,/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}/yaml\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"skyview_job_add\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/clusters/{clusterName}/jobs\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"batch/jobs\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"重新执行\",\"codeSuffix\":\"skyview_job_restart\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}/start\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"batch/jobs\":[\"get\",\"list\",\"create\",\"update\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑元数据\",\"codeSuffix\":\"skyview_job_metadata_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"batch/jobs\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑yaml\",\"codeSuffix\":\"skyview_job_yaml_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"batch/jobs\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"skyview_job_remove\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"batch/jobs\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"batch/jobs\",\"pods\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"定时任务\",\"codeSuffix\":\"sys_clusters_cronjob\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/cluster/space/resource/cronjob/list\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/cronjobs,/clusters/{clusterName}/namespaces/{namespace}/cronjob/{cronjobName}/yaml,/clusters/{clusterName}/namespaces/{namespace}/cronjobs/{cronjobName}/describe,/clusters/{clusterName}/namespaces/{namespace}/cronjobs/{cronjobName}/events,/clusters/{clusterName}/namespaces/{namespace}/cronjobs/{cronjobName}/info,/clusters/{clusterName}/namespaces/{namespace}/cronjobs/{cronjobName}/jobs,/clusters/{clusterName}/namespaces/{namespace}/cronjobs/{cronjobName}/metadata\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/clusters/{clusterName}/cronjobs\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"启停\",\"codeSuffix\":\"schedule\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/clusters/{clusterName}/namespaces/{namespace}/cronjobs/{cronjobName}\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑元数据\",\"codeSuffix\":\"editMetaData\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/clusters/{clusterName}/namespaces/{namespace}/cronjobs/{cronjobName}/metadata\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑策略\",\"codeSuffix\":\"editInfo\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/clusters/{clusterName}/namespaces/{namespace}/cronjob/{cronjobName}\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑yaml\",\"codeSuffix\":\"editYaml\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/clusters/{clusterName}/cronjob/yaml\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除执行记录\",\"codeSuffix\":\"deleteJob\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"delete\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/clusters/{clusterName}/namespaces/{namespace}/cronjob/{cronjobName}\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"batch/cronjobs\",\"pods\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"Pod容器组\",\"codeSuffix\":\"sys_clusters_pod_container_group\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/cluster/space/resource/pod\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/pods/filter,/clusters/{clusterName}/pods,/clusters/{clusterName}/namespaces/{namespace}/pods/{podName},/clusters/{clusterName}/namespaces/{namespace}/pods/{podName}/containers\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"监控\",\"codeSuffix\":\"monitor\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/cluster/{clusterName}/namespace/{namespaces}/getPodResource\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"pod日志\",\"codeSuffix\":\"containerLog\",\"type\":\"elem\",\"method\":\"patch\",\"url\":\"/clusters/{clusterName}/namespaces/{namespace}/pods/{podName}/containers/{container}/stderrlogs\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"watch\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"pod控制台\",\"codeSuffix\":\"containerTerminal\",\"type\":\"elem\",\"method\":\"patch\",\"url\":\"/podTerminal\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"事件\",\"codeSuffix\":\"event\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/cluster/{clusterName}/namespaces/{namespace}/pods/{podName}/events\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查看yaml\",\"codeSuffix\":\"yaml\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/cluster/{clusterName}/namespaces/{namespace}/pods/{podName}/yaml\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/cluster/{clusterName}/namespaces/{namespace}/pods/{podName}\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"harmonycloud.cn/nodepools\",\"events\",\"pods\",\"nodes\"]}}]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"配置挂载\",\"codeSuffix\":\"sys_cluster_config_mount\",\"type\":\"menu\",\"icon\":\"configure-mount-menu\",\"method\":\"get\",\"url\":\"/cluster/space/configMap/list\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"配置文件\",\"codeSuffix\":\"sys_cluster_configmap\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/cluster/space/configMap/list\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\",\"watch\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"skyview_configmap_add\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑挂载数据\",\"codeSuffix\":\"skyview_configmap_data_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑元数据\",\"codeSuffix\":\"skyview_configmap_meta_data_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑Yaml\",\"codeSuffix\":\"skyview_configmap_yaml_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"skyview_configmap_remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"stellaris.harmonycloud.cn/multiclusterresources\",\"stellaris.harmonycloud.cn/multiclusterresourcebindings\",\"events\",\"pods\",\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"apps/replicasets\",\"batch/jobs\",\"batch/cronjobs\",\"configmaps\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"保密字典\",\"codeSuffix\":\"sys_cluster_secret\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/cluster/space/secret/list\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"skyview_secret_add\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑加密数据\",\"codeSuffix\":\"skyview_secret_data_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑元数据\",\"codeSuffix\":\"skyview_secret_meta_data_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑Yaml\",\"codeSuffix\":\"skyview_secret_yaml_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"skyview_secret_remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"pods\",\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"apps/replicasets\",\"batch/jobs\",\"batch/cronjobs\",\"secrets\"]}}]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"存储\",\"codeSuffix\":\"sys_cluster_storage_service\",\"type\":\"menu\",\"icon\":\"storage-menu\",\"method\":\"get\",\"url\":\"/cluster/space/pvc/list\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"存储卷声明\",\"codeSuffix\":\"sys_cluster_pvc\",\"type\":\"menu\",\"icon\":\"\",\"method\":\"get\",\"url\":\"/cluster/space/pvc/list\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/pvc,/clusters/{clusterName}/pvc/{pvcName}/describe,/clusters/{clusterName}/pvc/{pvcName}/events,/clusters/{clusterName}/pvc/{pvcName}/info,/clusters/{clusterName}/pvc/{pvcName}/pods,/clusters/{clusterName}/pvc/{pvcName}/yaml,/clusters/{clusterName}/pvc/available\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"skyview_pvc_add\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\",\"create\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"扩容\",\"codeSuffix\":\"skyview_pvc_expand\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\",\"update\",\"patch\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑元数据\",\"codeSuffix\":\"skyview_pvc_meta_data_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\",\"update\",\"patch\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑Yaml\",\"codeSuffix\":\"skyview_pvc_yaml_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\",\"update\",\"patch\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"skyview_pvc_remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\",\"delete\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"pods\",\"storage.k8s.io/storageclasses\",\"persistentvolumeclaims\",\"persistentvolumes\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"存储卷\",\"codeSuffix\":\"sys_cluster_pv\",\"type\":\"menu\",\"icon\":\"\",\"method\":\"get\",\"url\":\"/cluster/space/pv/list\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/persistentvolume,/clusters/{clusterName}/persistentvolume/{pvName},/clusters/{clusterName}/persistentvolume/bound/pods,/clusters/{clusterName}/persistentvolume/custom/storage,/clusters/{clusterName}/persistentvolume/describe,/clusters/{clusterName}/persistentvolume/events,/clusters/{clusterName}/persistentvolume/storageclass/names,/clusters/{clusterName}/persistentvolume/yaml\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"skyview_pv_add\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\",\"create\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑元数据\",\"codeSuffix\":\"skyview_pv_meta_data_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑Yaml\",\"codeSuffix\":\"skyview_pv_yaml_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"skyview_pv_remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\",\"delete\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"pods\",\"storage.k8s.io/storageclasses\",\"persistentvolumeclaims\",\"persistentvolumes\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"存储服务\",\"codeSuffix\":\"sys_cluster_storage_class\",\"type\":\"menu\",\"icon\":\"\",\"method\":\"get\",\"url\":\"/cluster/space/scService/list\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/storageservices,/clusters/{clusterName}/storageservices/{storageServiceName},/clusters/{clusterName}/storageservices/{storageServiceName}/associatedresources\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"skyview_sc_service_add\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\",\"create\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"扩容\",\"codeSuffix\":\"skyview_sc_service_expand\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\",\"update\",\"patch\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"skyview_sc_service_remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"delete\"],\"configmaps\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\",\"delete\"],\"persistentvolumes\":[\"get\",\"list\",\"delete\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"delete\"],\"services\":[\"get\",\"list\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\",\"delete\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"apps/deployments\",\"pods\",\"services\",\"storage.k8s.io/storageclasses\",\"persistentvolumeclaims\",\"persistentvolumes\",\"secrets\"]}}]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"网络\",\"codeSuffix\":\"sys_cluster_network_manager\",\"type\":\"menu\",\"icon\":\"network-menu\",\"method\":\"get\",\"url\":\"/cluster/space/network/service\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"Service服务\",\"codeSuffix\":\"sys_cluster_services\",\"type\":\"menu\",\"icon\":\"\",\"method\":\"get\",\"url\":\"/cluster/space/network/service\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"endpoints\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"skyview_service_add\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"create\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑\",\"codeSuffix\":\"skyview_service_edit\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增四层对外路由\",\"codeSuffix\":\"skyview_service_add_four_layer_exponse\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增七层对外路由\",\"codeSuffix\":\"skyview_service_add_seven_layer_exponse\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑对外路由\",\"codeSuffix\":\"edit_domains\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除对外路由\",\"codeSuffix\":\"remove_domains\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑元数据\",\"codeSuffix\":\"skyview_service_metadata_edit\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑yaml\",\"codeSuffix\":\"skyview_service_yaml_edit\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"skyview_service_delete\",\"type\":\"elem\",\"method\":\"delete\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"services\",\"expose.helper.harmonycloud.cn/layer4exposes\",\"networking.k8s.io/ingresses\",\"expose.helper.harmonycloud.cn/ingressclasses\",\"configmaps\",\"endpoints\",\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"apps/replicasets\",\"pods\",\"nodes\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"Ingress路由\",\"codeSuffix\":\"sys_cluster_ingress\",\"type\":\"menu\",\"icon\":\"\",\"method\":\"get\",\"url\":\"/cluster/space/network/ingress\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\"],\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"endpoints\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\",\"watch\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\"],\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"create\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑\",\"codeSuffix\":\"edit\",\"type\":\"elem\",\"method\":\"put\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\"],\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增对外路由\",\"codeSuffix\":\"add_domains\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\"],\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑对外路由\",\"codeSuffix\":\"edit_domains\",\"type\":\"elem\",\"method\":\"put\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\"],\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑yaml\",\"codeSuffix\":\"edit_yaml\",\"type\":\"elem\",\"method\":\"put\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\"],\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除对外路由\",\"codeSuffix\":\"remove_domains\",\"type\":\"elem\",\"method\":\"delete\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"services\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑元数据\",\"codeSuffix\":\"edit_metadata\",\"type\":\"elem\",\"method\":\"put\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\",\"watch\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"delete\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"services\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"services\",\"expose.helper.harmonycloud.cn/layer4exposes\",\"networking.k8s.io/ingresses\",\"expose.helper.harmonycloud.cn/ingressclasses\",\"apisix.apache.org/apisixroutes\",\"configmaps\",\"secrets\",\"endpoints\",\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"apps/replicasets\",\"pods\",\"nodes\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"网络域\",\"codeSuffix\":\"sys_cluster_network_area\",\"type\":\"menu\",\"icon\":\"\",\"method\":\"get\",\"url\":\"/cluster/space/network/networkarea\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"heimdallr.harmonycloud.cn/networkresources\",\"heimdallr.harmonycloud.cn/networkdetails\",\"heimdallr.harmonycloud.cn/hdareas\",\"heimdallr.harmonycloud.cn/hdblocks\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/hdpools\",\"heimdallr.harmonycloud.cn/hdsvcs\",\"isolate.harmonycloud.cn/hleases\",\"mystra.heimdallr.harmonycloud.cn/podpolicies\",\"configmaps\",\"pods\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"网络IP池\",\"codeSuffix\":\"sys_cluster_network_ip_pool\",\"type\":\"menu\",\"icon\":\"\",\"method\":\"get\",\"url\":\"/cluster/space/network/ippool\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"heimdallr.harmonycloud.cn/networkresources\",\"heimdallr.harmonycloud.cn/networkdetails\",\"heimdallr.harmonycloud.cn/hdareas\",\"heimdallr.harmonycloud.cn/hdblocks\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/hdpools\",\"heimdallr.harmonycloud.cn/hdsvcs\",\"isolate.harmonycloud.cn/hleases\",\"mystra.heimdallr.harmonycloud.cn/podpolicies\",\"configmaps\",\"pods\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"负载均衡\",\"codeSuffix\":\"sys_cluster_network_loadbalance\",\"type\":\"menu\",\"icon\":\"\",\"method\":\"get\",\"url\":\"/cluster/space/network/loadbalance\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"endpoints\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\",\"create\",\"update\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑\",\"codeSuffix\":\"edit\",\"type\":\"elem\",\"method\":\"put\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"分配组织\",\"codeSuffix\":\"assgin_to_org\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增域名\",\"codeSuffix\":\"add_domains\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除域名\",\"codeSuffix\":\"remove_domains\",\"type\":\"elem\",\"method\":\"delete\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增证书\",\"codeSuffix\":\"add_secret\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除证书\",\"codeSuffix\":\"remove_secret\",\"type\":\"elem\",\"method\":\"delete\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"nginx配置\",\"codeSuffix\":\"edit_nginx\",\"type\":\"elem\",\"method\":\"put\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"services\",\"expose.helper.harmonycloud.cn/layer4exposes\",\"networking.k8s.io/ingresses\",\"expose.helper.harmonycloud.cn/ingressclasses\",\"apisix.apache.org/apisixroutes\",\"configmaps\",\"secrets\",\"endpoints\",\"pods\",\"nodes\"]}}]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"Kubernetes资源\",\"codeSuffix\":\"sys_kubernetes_resource\",\"type\":\"menu\",\"icon\":\"ip-pool-menu\",\"method\":\"get\",\"url\":\"/cluster/space/kubernetesres\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑Yaml\",\"codeSuffix\":\"skyview_cluster_resource_yaml_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"skyview_cluster_resource_remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]}]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"集群空间设置\",\"codeSuffix\":\"cluster_space_setting\",\"type\":\"menu\",\"icon\":\"space-setting-menu\",\"method\":\"get\",\"url\":\"/cluster/space/network/strategy\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"网络策略\",\"codeSuffix\":\"sys_cluster_network_podpolicy\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/cluster/space/network/strategy\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"添加策略\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除策略\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"开启/关闭策略\",\"codeSuffix\":\"edit_state\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑策略\",\"codeSuffix\":\"edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"heimdallr.harmonycloud.cn/networkresources\",\"heimdallr.harmonycloud.cn/networkdetails\",\"heimdallr.harmonycloud.cn/hdareas\",\"heimdallr.harmonycloud.cn/hdblocks\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/hdpools\",\"heimdallr.harmonycloud.cn/hdsvcs\",\"isolate.harmonycloud.cn/hleases\",\"mystra.heimdallr.harmonycloud.cn/podpolicies\",\"configmaps\",\"pods\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"集群设置\",\"codeSuffix\":\"sys_cluster_setting\",\"type\":\"menu\",\"icon\":\"\",\"method\":\"get\",\"url\":\"/cluster/space/clusterSetting\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"集群隔离锁重置\",\"codeSuffix\":\"clusterIsolateLock\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\",\"create\",\"update\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"集群删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\",\"delete\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"isolate.harmonycloud.cn/isolatelocks\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"我的集群\",\"codeSuffix\":\"sys_cluster_kubeconfig\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/cluster/space/myCluster\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查看\",\"codeSuffix\":\"cluster_kubeconfig_detail\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/cluster/space/myCluster/kubeconfig\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"stellaris.harmonycloud.cn/clusters\":[\"watch\",\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"下载\",\"codeSuffix\":\"cluster_kubeconfig_download\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"clusters/{clusterName}/kubeconfig/download\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"stellaris.harmonycloud.cn/clusters\":[\"watch\",\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\"]}}]}],\"annotations\":{\"ceiling\":true,\"resources\":[\"stellaris.harmonycloud.cn/clusters\"],\"titleDescription\":\"多集群纳管等统一管理中心\"}},{\"cloudServiceName\":\"unified_platform\",\"level\":\"platform\",\"name\":\"资源池\",\"codeSuffix\":\"sys_node_pool\",\"type\":\"menu\",\"icon\":\"resources-menu\",\"method\":\"get\",\"url\":\"/quotacenter/poolList\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/nodepools,/clusters/nodepools/filters\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/clusters/{clusterName}/nodepools\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"主机管理\",\"codeSuffix\":\"node_manage\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"delete\",\"update\",\"patch\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"添加主机\",\"codeSuffix\":\"add_node\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/clusters/{clusterName}/nodepools/{nodePoolName}/nodes\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"移除主机\",\"codeSuffix\":\"remove_node\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/clusters/{clusterName}/nodepools/{nodePoolName}/nodes/{nodeName}\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"分配组织\",\"codeSuffix\":\"assign_to_org\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"添加组织配额\",\"codeSuffix\":\"add_quota\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/clusters/{clusterName}/nodepools/{nodePoolName}/organizations/{organizationId}/quota\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑组织配额\",\"codeSuffix\":\"edit_quota\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/clusters/{clusterName}/nodepools/{nodePoolName}/organizations/{organizationId}/quota\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除组织配额\",\"codeSuffix\":\"remove_quota\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/clusters/{clusterName}/nodepools/{nodePoolName}/organizations/{organizationId}/quota\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/clusters/{clusterName}/nodepools/{nodePoolName}\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"ceiling\":true,\"resources\":[\"nodes\",\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"isolate.harmonycloud.cn/isolatelocks\",\"isolate.harmonycloud.cn/hleases\",\"harmonycloud.cn/nodepools\"]}},{\"cloudServiceName\":\"unified_platform\",\"level\":\"platform\",\"name\":\"存储服务\",\"codeSuffix\":\"sys_storage\",\"type\":\"menu\",\"icon\":\"storage-service-menu\",\"method\":\"get\",\"url\":\"/quotacenter/storageList\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\",\"create\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"扩容\",\"codeSuffix\":\"capacity_expansion\",\"type\":\"elem\",\"method\":\"put\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\",\"update\",\"patch\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"分配组织\",\"codeSuffix\":\"assign_to_org\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\",\"update\",\"patch\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"添加组织配额\",\"codeSuffix\":\"add_quota\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\",\"update\",\"patch\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑组织配额\",\"codeSuffix\":\"edit_quota\",\"type\":\"elem\",\"method\":\"put\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\",\"update\",\"patch\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除组织配额\",\"codeSuffix\":\"remove_quota\",\"type\":\"elem\",\"method\":\"delete\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\",\"update\",\"patch\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"delete\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"delete\"],\"configmaps\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\",\"delete\"],\"persistentvolumes\":[\"get\",\"list\",\"delete\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"delete\"],\"services\":[\"get\",\"list\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\",\"delete\"]}}}],\"annotations\":{\"ceiling\":true,\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"apps/deployments\",\"pods\",\"services\",\"storage.k8s.io/storageclasses\",\"persistentvolumeclaims\",\"persistentvolumes\",\"secrets\"]}},{\"cloudServiceName\":\"unified_platform\",\"level\":\"platform\",\"name\":\"网络管理\",\"codeSuffix\":\"sys_network_manage\",\"type\":\"menu\",\"icon\":\"network-management-menu\",\"method\":\"get\",\"url\":\"/networkmanage/resource\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"网络规划\",\"codeSuffix\":\"sys_network_resource_manager\",\"type\":\"menu\",\"icon\":\"network-plan-menu\",\"method\":\"get\",\"url\":\"/networkmanage/resource\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑\",\"codeSuffix\":\"edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"网络域\",\"codeSuffix\":\"sys_network_area\",\"type\":\"menu\",\"icon\":\"network-domain-menu\",\"method\":\"get\",\"url\":\"/quotacenter/networkarea\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑\",\"codeSuffix\":\"edit\",\"type\":\"elem\",\"method\":\"put\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"update\",\"patch\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"分配组织\",\"codeSuffix\":\"assgin_to_org\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"update\",\"patch\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"全局共享\",\"codeSuffix\":\"global_sharing\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"update\",\"patch\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"delete\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"delete\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"delete\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"heimdallr.harmonycloud.cn/networkresources\",\"heimdallr.harmonycloud.cn/networkdetails\",\"heimdallr.harmonycloud.cn/hdareas\",\"heimdallr.harmonycloud.cn/hdblocks\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/hdpools\",\"heimdallr.harmonycloud.cn/hdsvcs\",\"isolate.harmonycloud.cn/hleases\",\"mystra.heimdallr.harmonycloud.cn/podpolicies\",\"configmaps\",\"pods\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"网络IP池\",\"codeSuffix\":\"sys_network_area_ip_pool\",\"type\":\"menu\",\"icon\":\"ip-pool-menu\",\"method\":\"get\",\"url\":\"/quotacenter/ippool\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑\",\"codeSuffix\":\"edit\",\"type\":\"elem\",\"method\":\"put\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"update\",\"patch\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"分配组织\",\"codeSuffix\":\"assgin_to_org\",\"type\":\"elem\",\"method\":\"put\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"update\",\"patch\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"全局共享\",\"codeSuffix\":\"global_sharing\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"update\",\"patch\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"delete\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"delete\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"delete\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"heimdallr.harmonycloud.cn/networkresources\",\"heimdallr.harmonycloud.cn/networkdetails\",\"heimdallr.harmonycloud.cn/hdareas\",\"heimdallr.harmonycloud.cn/hdblocks\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/hdpools\",\"heimdallr.harmonycloud.cn/hdsvcs\",\"isolate.harmonycloud.cn/hleases\",\"mystra.heimdallr.harmonycloud.cn/podpolicies\",\"configmaps\",\"pods\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"网络策略\",\"codeSuffix\":\"sys_network_policy\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/networkmanage/space/strategy\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"添加策略\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除策略\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"delete\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"delete\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"开启/关闭策略\",\"codeSuffix\":\"edit_state\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"delete\",\"create\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑策略\",\"codeSuffix\":\"edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"delete\",\"create\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"heimdallr.harmonycloud.cn/networkresources\",\"heimdallr.harmonycloud.cn/networkdetails\",\"heimdallr.harmonycloud.cn/hdareas\",\"heimdallr.harmonycloud.cn/hdblocks\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/hdpools\",\"heimdallr.harmonycloud.cn/hdsvcs\",\"isolate.harmonycloud.cn/hleases\",\"mystra.heimdallr.harmonycloud.cn/podpolicies\",\"configmaps\",\"pods\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"网络模板\",\"codeSuffix\":\"sys_network_template\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/networkmanage/space/template\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"skyview_network_space_template_add\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑\",\"codeSuffix\":\"skyview_network_space_template_update\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"delete\",\"create\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"skyview_network_space_template_remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"delete\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"delete\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"heimdallr.harmonycloud.cn/networkresources\",\"heimdallr.harmonycloud.cn/networkdetails\",\"heimdallr.harmonycloud.cn/hdareas\",\"heimdallr.harmonycloud.cn/hdblocks\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/hdpools\",\"heimdallr.harmonycloud.cn/hdsvcs\",\"isolate.harmonycloud.cn/hleases\",\"mystra.heimdallr.harmonycloud.cn/podpolicies\",\"configmaps\",\"pods\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"网络配置\",\"codeSuffix\":\"sys_network_setting\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/networkmanage/space/setting\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"IP使用配置\",\"codeSuffix\":\"network_ip_use_config\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"heimdallr.harmonycloud.cn/networkresources\",\"heimdallr.harmonycloud.cn/networkdetails\",\"heimdallr.harmonycloud.cn/hdareas\",\"heimdallr.harmonycloud.cn/hdblocks\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/hdpools\",\"heimdallr.harmonycloud.cn/hdsvcs\",\"isolate.harmonycloud.cn/hleases\",\"mystra.heimdallr.harmonycloud.cn/podpolicies\",\"configmaps\",\"pods\"]}}],\"annotations\":{\"ceiling\":true,\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"heimdallr.harmonycloud.cn/networkresources\",\"heimdallr.harmonycloud.cn/networkdetails\",\"heimdallr.harmonycloud.cn/hdareas\"],\"titleDescription\":\"双栈及IP网络规划管理\"}},{\"cloudServiceName\":\"unified_platform\",\"level\":\"platform\",\"name\":\"制品服务\",\"codeSuffix\":\"sys_registry\",\"type\":\"menu\",\"icon\":\"registry-menu\",\"method\":\"get\",\"url\":\"/quotacenter/registry\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"移除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"delete\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增仓库\",\"codeSuffix\":\"add_repos\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"分配组织\",\"codeSuffix\":\"assign_to_org\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除仓库\",\"codeSuffix\":\"remove_repos\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增备份规则\",\"codeSuffix\":\"repo_add_rule\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑备份规则\",\"codeSuffix\":\"repo_edit_rule\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"执行备份规则\",\"codeSuffix\":\"repo_execute_rule\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除备份规则\",\"codeSuffix\":\"repo_remove_rule\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增备份服务器\",\"codeSuffix\":\"add_server\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑备份服务器\",\"codeSuffix\":\"edit_server\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除备份服务器\",\"codeSuffix\":\"remove_server\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑垃圾清理\",\"codeSuffix\":\"edit_schedule\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"立即清理垃圾\",\"codeSuffix\":\"execute_schedule\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑Harbor配置\",\"codeSuffix\":\"edit_harbor_config\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑仓库标签\",\"codeSuffix\":\"repo_label_manage\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"上传\",\"codeSuffix\":\"upload_image\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除镜像\",\"codeSuffix\":\"remove_image\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查看版本\",\"codeSuffix\":\"view_version\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"添加规则\",\"codeSuffix\":\"add_rule\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"立即执行\",\"codeSuffix\":\"execute_rule\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑定时任务\",\"codeSuffix\":\"edit_timed_task\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"模拟运行\",\"codeSuffix\":\"simulation_run\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"中止\",\"codeSuffix\":\"suspend_rule\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"日志\",\"codeSuffix\":\"log\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑规则\",\"codeSuffix\":\"edit_rule\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"启用/禁用规则\",\"codeSuffix\":\"enable_disable_rule\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除规则\",\"codeSuffix\":\"remove_rule\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"内容信任\",\"codeSuffix\":\"content_trust\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"阻止潜在漏洞镜像\",\"codeSuffix\":\"Block_potential_vulnerability_mirroring\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"自动扫描镜像\",\"codeSuffix\":\"auto_scan\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑CVE白名单\",\"codeSuffix\":\"CVE_whitelist\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"镜像复制\",\"codeSuffix\":\"copy\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"构建记录\",\"codeSuffix\":\"build_record\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"漏洞扫描\",\"codeSuffix\":\"bug_scan\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"拉取/下载\",\"codeSuffix\":\"download\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"标签管理\",\"codeSuffix\":\"label_manage\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除镜像版本\",\"codeSuffix\":\"images_remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"]}],\"annotations\":{\"ceiling\":true}},{\"cloudServiceName\":\"unified_platform\",\"level\":\"platform\",\"name\":\"负载均衡\",\"codeSuffix\":\"sys_loadbalance\",\"type\":\"menu\",\"icon\":\"loadbalance-menu\",\"method\":\"get\",\"url\":\"/quotacenter/loadbalance\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"endpoints\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\",\"create\",\"update\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑\",\"codeSuffix\":\"edit\",\"type\":\"elem\",\"method\":\"put\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"分配组织\",\"codeSuffix\":\"assign_to_org\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增域名\",\"codeSuffix\":\"add_domains\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除域名\",\"codeSuffix\":\"remove_domains\",\"type\":\"elem\",\"method\":\"delete\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增证书\",\"codeSuffix\":\"add_secret\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除证书\",\"codeSuffix\":\"remove_secret\",\"type\":\"elem\",\"method\":\"delete\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"全局共享\",\"codeSuffix\":\"global_sharing\",\"type\":\"elem\",\"method\":\"delete\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"nginx配置\",\"codeSuffix\":\"edit_nginx\",\"type\":\"elem\",\"method\":\"put\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"platform_resource_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"ceiling\":true,\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"services\",\"expose.helper.harmonycloud.cn/layer4exposes\",\"networking.k8s.io/ingresses\",\"expose.helper.harmonycloud.cn/ingressclasses\",\"apisix.apache.org/apisixroutes\",\"configmaps\",\"secrets\",\"endpoints\",\"pods\",\"nodes\"]}},{\"cloudServiceName\":\"unified_platform\",\"level\":\"platform\",\"name\":\"集群监控\",\"codeSuffix\":\"sys_monitoring_centre\",\"type\":\"menu\",\"icon\":\"Monitoring-center-menu\",\"method\":\"get\",\"url\":\"/monitoring\",\"sortCode\":0,\"auths\":[\"platform_dev_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_dev_mgr\",\"platform_mgr\"]}],\"annotations\":{\"ceiling\":true}},{\"cloudServiceName\":\"unified_platform\",\"level\":\"platform\",\"name\":\"告警中心\",\"codeSuffix\":\"sys_alarm_centre\",\"type\":\"menu\",\"icon\":\"alarm-center\",\"method\":\"get\",\"url\":\"/alarm/alarmProcessing\",\"sortCode\":0,\"auths\":[\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"告警处理\",\"codeSuffix\":\"alarm_Processing\",\"type\":\"menu\",\"icon\":\"\",\"method\":\"get\",\"url\":\"/alarm/alarmProcessing\",\"sortCode\":0,\"auths\":[\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/alarm/find\",\"sortCode\":0,\"auths\":[\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"标记\",\"codeSuffix\":\"sign\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/alarm/handle\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]}]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"应用告警\",\"codeSuffix\":\"application_alert\",\"type\":\"menu\",\"icon\":\"\",\"method\":\"get\",\"url\":\"/alarm/applicationAlert\",\"sortCode\":0,\"auths\":[\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/prometheusrules/{prometheusruleName}\",\"sortCode\":0,\"auths\":[\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"monitoring.coreos.com/prometheusrules\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/clusters/{clusterName}/prometheusrules\",\"sortCode\":0,\"auths\":[\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"monitoring.coreos.com/prometheusrules\":[\"create\",\"update\",\"patch\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑\",\"codeSuffix\":\"edit\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/clusters/{clusterName}/prometheusrules/{prometheusruleName}\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"delete\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/clusters/{clusterName}/prometheusrules\",\"sortCode\":0,\"auths\":[\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"monitoring.coreos.com/prometheusrules\":[\"delete\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"monitoring.coreos.com/prometheusrules\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"组件告警\",\"codeSuffix\":\"component_alert\",\"type\":\"menu\",\"icon\":\"\",\"method\":\"get\",\"url\":\"/alarm/componentAlert\",\"sortCode\":0,\"auths\":[\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/prometheusrules/{prometheusruleName}\",\"sortCode\":0,\"auths\":[\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"monitoring.coreos.com/prometheusrules\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/clusters/{clusterName}/prometheusrules\",\"sortCode\":0,\"auths\":[\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"monitoring.coreos.com/prometheusrules\":[\"create\",\"update\",\"patch\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑\",\"codeSuffix\":\"edit\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/clusters/{clusterName}/prometheusrules/{prometheusruleName}\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"delete\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/clusters/{clusterName}/prometheusrules\",\"sortCode\":0,\"auths\":[\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"monitoring.coreos.com/prometheusrules\":[\"delete\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"monitoring.coreos.com/prometheusrules\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"主机告警\",\"codeSuffix\":\"node_alert\",\"type\":\"menu\",\"icon\":\"\",\"method\":\"get\",\"url\":\"/alarm/nodeAlert\",\"sortCode\":0,\"auths\":[\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/prometheusrules/{prometheusruleName}\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/clusters/{clusterName}/prometheusrules\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑\",\"codeSuffix\":\"edit\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/clusters/{clusterName}/prometheusrules/{prometheusruleName}\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"delete\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/clusters/{clusterName}/prometheusrules\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]}]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"日志告警\",\"codeSuffix\":\"log_alert\",\"type\":\"menu\",\"icon\":\"\",\"method\":\"get\",\"url\":\"/alarm/logAlert\",\"sortCode\":0,\"auths\":[\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/applog/monitor/get\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/applog/monitor/save\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑\",\"codeSuffix\":\"edit\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/applog/monitor/save\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"delete\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/applog/monitor/delete\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"状态操作\",\"codeSuffix\":\"changestatus\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/applog/monitor/status\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]}]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"指标管理\",\"codeSuffix\":\"alert_rule_templates\",\"type\":\"menu\",\"icon\":\"\",\"method\":\"get\",\"url\":\"/alarm/alertruletemplates\",\"sortCode\":0,\"auths\":[\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/modules/{module}/prometheusruletypes/{typeCode}/alertruletemplates/custom/{id}\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/modules/{module}/prometheusruletypes/{typeCode}/alertruletemplates\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑\",\"codeSuffix\":\"edit\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/modules/{module}/prometheusruletypes/{typeCode}/alertruletemplates\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"delete\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/modules/{module}/prometheusruletypes/alertruletemplates\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]}]}],\"annotations\":{\"ceiling\":true,\"resources\":[\"stellaris.harmonycloud.cn/clusters\"]}},{\"cloudServiceName\":\"unified_platform\",\"level\":\"platform\",\"name\":\"日志中心\",\"codeSuffix\":\"sys_log_centre\",\"type\":\"menu\",\"icon\":\"sys_log_center\",\"method\":\"get\",\"url\":\"/log/logQuery\",\"sortCode\":0,\"auths\":[\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"日志查询\",\"codeSuffix\":\"sys_log_query\",\"type\":\"menu\",\"icon\":\"\",\"method\":\"get\",\"url\":\"/log/logQuery\",\"sortCode\":0,\"auths\":[\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/apps/{appName}/applogs/filenames\",\"sortCode\":0,\"auths\":[\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"导出\",\"codeSuffix\":\"export\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/apps/{appName}/applogs/export\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"apps/replicasets\",\"batch/jobs\",\"batch/cronjobs\",\"pods\",\"nodes\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"日志备份\",\"codeSuffix\":\"log_backup\",\"type\":\"menu\",\"icon\":\"\",\"method\":\"get\",\"url\":\"/log/logBackup\",\"sortCode\":0,\"auths\":[\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query-backuprule\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/snapshotrules\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"创建规则\",\"codeSuffix\":\"add-backuprule\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/snapshotrules\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"创建快照\",\"codeSuffix\":\"add-snapshot\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/snapshotrules/snapshots\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"操作规则\",\"codeSuffix\":\"edit-backuprule\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/snapshotrules\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"恢复备份\",\"codeSuffix\":\"restore-snapshot\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/snapshotrules/snapshots\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除快照\",\"codeSuffix\":\"delete-snapshot\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/snapshotrules/snapshots\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除备份记录\",\"codeSuffix\":\"delete-restore\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/snapshotrules/snapshots/restored/{date}\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]}]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"系统日志\",\"codeSuffix\":\"system_log\",\"type\":\"menu\",\"icon\":\"\",\"method\":\"get\",\"url\":\"/log/systemLog\",\"sortCode\":0,\"auths\":[\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/deploys/{deployName}/logs/filenames\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"导出\",\"codeSuffix\":\"export\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/deploys/{deployName}/logs/export\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]}],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"ceiling\":true}},{\"cloudServiceName\":\"unified_platform\",\"level\":\"platform\",\"name\":\"系统运维\",\"codeSuffix\":\"sys_system_dev_ops\",\"type\":\"menu\",\"icon\":\"system-maintenance-menu\",\"method\":\"get\",\"url\":\"/systemaudit/clusterPatrol\",\"sortCode\":0,\"auths\":[\"platform_dev_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"集群巡检\",\"codeSuffix\":\"sys_cluster_patrol\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/systemaudit/clusterPatrol\",\"sortCode\":0,\"auths\":[\"platform_dev_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/metricsreport/cluster/getreport\",\"sortCode\":0,\"auths\":[\"platform_dev_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"巡检\",\"codeSuffix\":\"patrol\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/metricsreport/cluster/getreport\",\"sortCode\":0,\"auths\":[\"platform_dev_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"详情\",\"codeSuffix\":\"detail\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/dashboards\",\"sortCode\":0,\"auths\":[\"platform_dev_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"导出\",\"codeSuffix\":\"export\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/metricsreport/cluster/exportreport\",\"sortCode\":0,\"auths\":[\"platform_dev_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"设置接收人\",\"codeSuffix\":\"setreceiver\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/cluster/{clusterName}/health/monitor\",\"sortCode\":0,\"auths\":[\"platform_dev_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增指标\",\"codeSuffix\":\"addrule\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/cluster/{clusterName}/health/thresholds/{metricsName}\",\"sortCode\":0,\"auths\":[\"platform_dev_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除指标\",\"codeSuffix\":\"delete\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/cluster/{clusterName}/health/thresholds\",\"sortCode\":0,\"auths\":[\"platform_dev_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑指标\",\"codeSuffix\":\"edit\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/cluster/{clusterName}/health/thresholds/{metricsName}\",\"sortCode\":0,\"auths\":[\"platform_dev_mgr\",\"platform_mgr\"]}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\"]}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"故障隔离\",\"codeSuffix\":\"faultsolation\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/systemaudit/faultsolation\",\"sortCode\":0,\"auths\":[\"platform_dev_mgr\",\"platform_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_dev_mgr\",\"platform_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"pod控制台\",\"codeSuffix\":\"containerTerminal\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_dev_mgr\",\"platform_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"platform_dev_mgr\",\"platform_mgr\"]}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"apps/replicasets\",\"batch/jobs\",\"batch/cronjobs\",\"pods\",\"nodes\"]}}],\"annotations\":{\"ceiling\":true}},{\"cloudServiceName\":\"unified_platform\",\"level\":\"platform\",\"name\":\"配置查询\",\"codeSuffix\":\"configs\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/system/configs\",\"sortCode\":0,\"auths\":[\"platform_mgr\"]}],\"annotations\":{\"ceiling\":true}}]}]",
		}, {
			permissionv1jsonStr: "[{\"operator\":\"add\",\"configs\":[{\"cloudServiceName\":\"unified_platform\",\"level\":\"organ\",\"name\":\"组织总览\",\"codeSuffix\":\"sys_organ_overview\",\"type\":\"menu\",\"icon\":\"organizational-space-overview-menu\",\"method\":\"get\",\"url\":\"/organization/space/overview\",\"sortCode\":0,\"auths\":[\"organ_mgr\",\"organ_normal\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"organ_mgr\",\"organ_normal\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"apps/replicasets\",\"batch/jobs\",\"batch/cronjobs\",\"pods\",\"services\",\"secrets\",\"configmaps\",\"persistentvolumeclaims\",\"persistentvolumes\",\"storage.k8s.io/storageclasses\",\"serviceaccounts\",\"namespaces\",\"nodes\",\"resourcequotas\",\"harmonycloud.cn/nodepools\",\"heimdallr.harmonycloud.cn/hdareas\",\"heimdallr.harmonycloud.cn/hdblocks\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/hdpools\",\"heimdallr.harmonycloud.cn/networkdetails\",\"heimdallr.harmonycloud.cn/networkresources\"]}},{\"cloudServiceName\":\"unified_platform\",\"level\":\"organ\",\"name\":\"项目列表\",\"codeSuffix\":\"sys_organ_project_list\",\"type\":\"menu\",\"icon\":\"project-list-menu\",\"method\":\"get\",\"url\":\"/organization/space/projectList\",\"sortCode\":0,\"auths\":[\"organ_mgr\",\"organ_normal\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/projects\",\"sortCode\":0,\"auths\":[\"organ_mgr\",\"organ_normal\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/organization/{organizationId}/projects\",\"sortCode\":0,\"auths\":[\"organ_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"配额管理\",\"codeSuffix\":\"quota_manage\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/organization/space/projectList\",\"sortCode\":0,\"auths\":[\"organ_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑\",\"codeSuffix\":\"edit\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/organization/{organizationId}/projects/{id}\",\"sortCode\":0,\"auths\":[\"organ_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/organization/{organizationId}/projects/{id}\",\"sortCode\":0,\"auths\":[\"organ_mgr\"]}]},{\"cloudServiceName\":\"unified_platform\",\"level\":\"organ\",\"name\":\"命名空间\",\"codeSuffix\":\"sys_organ_namespaces\",\"type\":\"menu\",\"icon\":\"namespace-menu\",\"method\":\"get\",\"url\":\"/organization/space/namespaceList\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/organizations/{organizationId}/namespaces,/organizations/{organizationId}/namespaces/filters\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/statefulsets\":[\"list\",\"get\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"delete\",\"update\",\"patch\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"unified_platform\",\"level\":\"organ\",\"name\":\"组织空间配额\",\"codeSuffix\":\"sys_organ_quota\",\"type\":\"menu\",\"icon\":\"space-quota-menu\",\"method\":\"get\",\"url\":\"/organization/space/quotacenter/poolList\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"children\":[{\"cloudServiceName\":\"unified_platform\",\"level\":\"organ\",\"name\":\"资源池\",\"codeSuffix\":\"sys_organ_node_pool\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/organization/space/quotacenter/poolList\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"分配项目\",\"codeSuffix\":\"assign_to_project\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"添加项目配额\",\"codeSuffix\":\"add_quota\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑项目配额\",\"codeSuffix\":\"edit_quota\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除项目配额\",\"codeSuffix\":\"remove_quota\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"nodes\",\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"isolate.harmonycloud.cn/isolatelocks\",\"isolate.harmonycloud.cn/hleases\",\"harmonycloud.cn/nodepools\"]}},{\"cloudServiceName\":\"unified_platform\",\"level\":\"organ\",\"name\":\"存储服务\",\"codeSuffix\":\"sys_organ_storage\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/organization/space/quotacenter/storageList\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"分配项目\",\"codeSuffix\":\"assign_to_project\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\",\"update\",\"patch\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"添加项目配额\",\"codeSuffix\":\"add_quota\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\",\"update\",\"patch\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑项目配额\",\"codeSuffix\":\"edit_quota\",\"type\":\"elem\",\"method\":\"put\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\",\"update\",\"patch\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除项目配额\",\"codeSuffix\":\"remove_quota\",\"type\":\"elem\",\"method\":\"delete\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\",\"update\",\"patch\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"delete\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"delete\"],\"configmaps\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\",\"delete\"],\"persistentvolumes\":[\"get\",\"list\",\"delete\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"delete\"],\"services\":[\"get\",\"list\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\",\"delete\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"apps/deployments\",\"pods\",\"services\",\"storage.k8s.io/storageclasses\",\"persistentvolumeclaims\",\"persistentvolumes\",\"secrets\"]}},{\"cloudServiceName\":\"unified_platform\",\"level\":\"organ\",\"name\":\"网络域\",\"codeSuffix\":\"sys_organ_network_area\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/organization/space/quotacenter/networkarea\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"分配项目\",\"codeSuffix\":\"assgin_to_project\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"update\",\"patch\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove_quota\",\"type\":\"elem\",\"method\":\"delete\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"delete\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"delete\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"stellaris.harmonycloud.cn/multiclusterresources\",\"stellaris.harmonycloud.cn/multiclusterresourcebindings\",\"events\",\"heimdallr.harmonycloud.cn/networkresources\",\"heimdallr.harmonycloud.cn/networkdetails\",\"heimdallr.harmonycloud.cn/hdareas\",\"heimdallr.harmonycloud.cn/hdblocks\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/hdpools\",\"heimdallr.harmonycloud.cn/hdsvcs\",\"isolate.harmonycloud.cn/hleases\",\"mystra.heimdallr.harmonycloud.cn/podpolicies\",\"configmaps\",\"pods\"]}},{\"cloudServiceName\":\"unified_platform\",\"level\":\"organ\",\"name\":\"网络IP池\",\"codeSuffix\":\"sys_organ_network_ip_pool\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/organization/space/quotacenter/ippool\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"分配项目\",\"codeSuffix\":\"assgin_to_project\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"update\",\"patch\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove_quota\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"delete\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"delete\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"heimdallr.harmonycloud.cn/networkresources\",\"heimdallr.harmonycloud.cn/networkdetails\",\"heimdallr.harmonycloud.cn/hdareas\",\"heimdallr.harmonycloud.cn/hdblocks\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/hdpools\",\"heimdallr.harmonycloud.cn/hdsvcs\",\"isolate.harmonycloud.cn/hleases\",\"mystra.heimdallr.harmonycloud.cn/podpolicies\",\"configmaps\",\"pods\"]}},{\"cloudServiceName\":\"unified_platform\",\"level\":\"organ\",\"name\":\"制品服务\",\"codeSuffix\":\"sys_organ_registry\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/organization/space/quotacenter/registry\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"organ_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"分配项目\",\"codeSuffix\":\"assign_to_project\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"organ_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"organ_mgr\"]}]},{\"cloudServiceName\":\"unified_platform\",\"level\":\"organ\",\"name\":\"负载均衡\",\"codeSuffix\":\"sys_organ_lb\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/organization/space/quotacenter/loadbalance\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"endpoints\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"分配项目\",\"codeSuffix\":\"assign_to_project\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"services\",\"expose.helper.harmonycloud.cn/layer4exposes\",\"networking.k8s.io/ingresses\",\"expose.helper.harmonycloud.cn/ingressclasses\",\"apisix.apache.org/apisixroutes\",\"configmaps\",\"secrets\",\"endpoints\",\"pods\",\"nodes\"]}}]},{\"cloudServiceName\":\"unified_platform\",\"level\":\"organ\",\"name\":\"组织空间设置\",\"codeSuffix\":\"sys_organ_settings\",\"type\":\"menu\",\"icon\":\"set\",\"method\":\"get\",\"url\":\"/organization/space/set/user\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"children\":[{\"cloudServiceName\":\"unified_platform\",\"level\":\"organ\",\"name\":\"组织用户\",\"codeSuffix\":\"sys_organ_users\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/organization/space/set/user\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/users/page\",\"sortCode\":0,\"auths\":[\"organ_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/user-organ-project\",\"sortCode\":0,\"auths\":[\"organ_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"关联角色\",\"codeSuffix\":\"bindRole\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/user-roles/users/{userId}/roles/{roleId}\",\"sortCode\":0,\"auths\":[\"organ_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/user-organ-project\",\"sortCode\":0,\"auths\":[\"organ_mgr\"]}]},{\"cloudServiceName\":\"unified_platform\",\"level\":\"organ\",\"name\":\"组织角色\",\"codeSuffix\":\"sys_organ_roles\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/organization/space/set/role\",\"sortCode\":0,\"auths\":[\"organ_mgr\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/roles/page\",\"sortCode\":0,\"auths\":[\"organ_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/roles\",\"sortCode\":0,\"auths\":[\"organ_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑\",\"codeSuffix\":\"edit\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/roles/{roleId}/simple\",\"sortCode\":0,\"auths\":[\"organ_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"分配角色权限\",\"codeSuffix\":\"distribute_privilege\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/roles/{roleId}\",\"sortCode\":0,\"auths\":[\"organ_mgr\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/roles/{roleId}\",\"sortCode\":0,\"auths\":[\"organ_mgr\"]}]}]}]}]",
		}, {
			permissionv1jsonStr: "[{\"operator\":\"add\",\"configs\":[{\"cloudServiceName\":\"unified_platform\",\"level\":\"workspace\",\"name\":\"项目中心\",\"codeSuffix\":\"sys_project_mgr\",\"type\":\"menu\",\"icon\":\"market\",\"method\":\"get\",\"url\":\"/project/space/namespaceList\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"unified_platform\",\"level\":\"workspace\",\"name\":\"命名空间\",\"codeSuffix\":\"sys_project_namespaces\",\"type\":\"menu\",\"icon\":\"namespace-menu\",\"method\":\"get\",\"url\":\"/project/space/namespaceList\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/organizations/{organizationId}/projects/{projectId}/namespaces,/organizations/{organizationId}/projects/{projectId}/clusters/{clusterName}/namespaces/{namespace},/organizations/{organizationId}/projects/{projectId}/namespaces/filters\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/statefulsets\":[\"list\",\"get\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/organizations/{organizationId}/projects/{projectId}/clusters/{clusterName}/namespaces\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/statefulsets\":[\"list\",\"get\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\",\"patch\",\"create\",\"update\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\",\"patch\",\"create\",\"update\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"配额管理\",\"codeSuffix\":\"quota_manage\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/clusters/{clusterName}/namespaces/{namespace}\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/statefulsets\":[\"list\",\"get\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\",\"patch\",\"create\",\"update\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\",\"patch\",\"create\",\"update\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑基础信息\",\"codeSuffix\":\"editDescription\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/namespaces/{namespaces}/description\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/statefulsets\":[\"list\",\"get\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\",\"patch\",\"create\",\"update\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\",\"patch\",\"create\",\"update\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑元数据\",\"codeSuffix\":\"edit\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/namespaces/{namespaces}\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/statefulsets\":[\"list\",\"get\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\",\"patch\",\"create\",\"update\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\",\"patch\",\"create\",\"update\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/clusters/{clusterName}/namespaces/{namespaces}\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/statefulsets\":[\"list\",\"get\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\",\"patch\",\"create\",\"update\",\"delete\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\",\"patch\",\"create\",\"update\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"nodes\",\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"events\",\"isolate.harmonycloud.cn/isolatelocks\",\"isolate.harmonycloud.cn/hleases\",\"harmonycloud.cn/nodepools\",\"pods\",\"resourcequotas\",\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"batch/cronjobs\",\"batch/jobs\"]}},{\"cloudServiceName\":\"unified_platform\",\"level\":\"workspace\",\"name\":\"项目空间配额\",\"codeSuffix\":\"sys_project_quota\",\"type\":\"menu\",\"icon\":\"space-quota-menu\",\"method\":\"get\",\"url\":\"/project/space/quotacenter/poolList\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"unified_platform\",\"level\":\"workspace\",\"name\":\"资源池\",\"codeSuffix\":\"sys_project_node_pool\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/quotacenter/poolList\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"分配命名空间\",\"codeSuffix\":\"assign_to_namespace\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑命名空间配额\",\"codeSuffix\":\"edit_quota\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除命名空间配额\",\"codeSuffix\":\"remove_quota\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"nodes\",\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"isolate.harmonycloud.cn/isolatelocks\",\"isolate.harmonycloud.cn/hleases\",\"harmonycloud.cn/nodepools\"]}},{\"cloudServiceName\":\"unified_platform\",\"level\":\"workspace\",\"name\":\"存储服务\",\"codeSuffix\":\"sys_project_storage\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/quotacenter/storageList\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"分配命名空间\",\"codeSuffix\":\"assign_to_namespace\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\",\"update\",\"patch\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"添加命名空间配额\",\"codeSuffix\":\"add_quota\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\",\"update\",\"patch\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑命名空间配额\",\"codeSuffix\":\"edit_quota\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\",\"update\",\"patch\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除命名空间配额\",\"codeSuffix\":\"remove_quota\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\",\"update\",\"patch\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"delete\"],\"configmaps\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\",\"delete\"],\"persistentvolumes\":[\"get\",\"list\",\"delete\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"delete\"],\"services\":[\"get\",\"list\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\",\"delete\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"apps/deployments\",\"pods\",\"services\",\"storage.k8s.io/storageclasses\",\"persistentvolumeclaims\",\"persistentvolumes\",\"secrets\"]}},{\"cloudServiceName\":\"unified_platform\",\"level\":\"workspace\",\"name\":\"网络域\",\"codeSuffix\":\"sys_project_network_area\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/quotacenter/networkarea\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"分配命名空间\",\"codeSuffix\":\"assgin_to_namespace\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"update\",\"patch\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove_quota\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"delete\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"delete\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"stellaris.harmonycloud.cn/multiclusterresources\",\"stellaris.harmonycloud.cn/multiclusterresourcebindings\",\"events\",\"heimdallr.harmonycloud.cn/networkresources\",\"heimdallr.harmonycloud.cn/networkdetails\",\"heimdallr.harmonycloud.cn/hdareas\",\"heimdallr.harmonycloud.cn/hdblocks\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/hdpools\",\"heimdallr.harmonycloud.cn/hdsvcs\",\"isolate.harmonycloud.cn/hleases\",\"mystra.heimdallr.harmonycloud.cn/podpolicies\",\"configmaps\",\"pods\"]}},{\"cloudServiceName\":\"unified_platform\",\"level\":\"workspace\",\"name\":\"网络IP池\",\"codeSuffix\":\"sys_project_network_ip_pool\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/quotacenter/ippool\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"分配命名空间\",\"codeSuffix\":\"assgin_to_namespace\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"update\",\"patch\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove_quota\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"delete\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"delete\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"heimdallr.harmonycloud.cn/networkresources\",\"heimdallr.harmonycloud.cn/networkdetails\",\"heimdallr.harmonycloud.cn/hdareas\",\"heimdallr.harmonycloud.cn/hdblocks\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/hdpools\",\"heimdallr.harmonycloud.cn/hdsvcs\",\"isolate.harmonycloud.cn/hleases\",\"mystra.heimdallr.harmonycloud.cn/podpolicies\",\"configmaps\",\"pods\"]}},{\"cloudServiceName\":\"unified_platform\",\"level\":\"workspace\",\"name\":\"制品服务\",\"codeSuffix\":\"sys_project_registry\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/quotacenter/registry\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]}]},{\"cloudServiceName\":\"unified_platform\",\"level\":\"workspace\",\"name\":\"负载均衡\",\"codeSuffix\":\"sys_project_lb\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/quotacenter/loadbalance\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"endpoints\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"services\",\"expose.helper.harmonycloud.cn/layer4exposes\",\"networking.k8s.io/ingresses\",\"expose.helper.harmonycloud.cn/ingressclasses\",\"apisix.apache.org/apisixroutes\",\"configmaps\",\"secrets\",\"endpoints\",\"pods\",\"nodes\"]}}]},{\"cloudServiceName\":\"unified_platform\",\"level\":\"workspace\",\"name\":\"项目空间设置\",\"codeSuffix\":\"sys_project_settings\",\"type\":\"menu\",\"icon\":\"space-setting-menu\",\"method\":\"get\",\"url\":\"/project/space/set/user\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"unified_platform\",\"level\":\"workspace\",\"name\":\"项目用户\",\"codeSuffix\":\"sys_project_users\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/set/user\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/users/page\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/user-organ-project\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"关联角色\",\"codeSuffix\":\"bindRole\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/user-roles/users/{userId}/roles/{roleId}\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/user-organ-project\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]}]},{\"cloudServiceName\":\"unified_platform\",\"level\":\"workspace\",\"name\":\"项目角色\",\"codeSuffix\":\"sys_project_roles\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/set/role\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/roles/page\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/roles\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑\",\"codeSuffix\":\"edit\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/roles/{roleId}/simple\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"分配角色权限\",\"codeSuffix\":\"distribute_privilege\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/roles/{roleId}\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/roles/{roleId}\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]}]},{\"cloudServiceName\":\"unified_platform\",\"level\":\"workspace\",\"name\":\"项目gpu远程调用\",\"codeSuffix\":\"sys_project_remote\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/set/allowRemote\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]}]}],\"annotations\":{\"ceiling\":true,\"titleDescription\":\"项目成员、角色及配额等基础管理\"}}]}]",
		}, {
			permissionv1jsonStr: "[{\"operator\":\"add\",\"configs\":[{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"应用市场\",\"codeSuffix\":\"sys_application_market\",\"type\":\"menu\",\"icon\":\"market-menu\",\"method\":\"get\",\"url\":\"/applicationMarket\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"ceiling\":true,\"titleDescription\":\"Helm模板及应用模板中心\"}}]},{\"operator\":\"add\",\"configs\":[{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"容器服务\",\"codeSuffix\":\"sys_container_services\",\"type\":\"menu\",\"icon\":\"market\",\"method\":\"get\",\"url\":\"/project/space/overview\",\"sortCode\":0,\"auths\":[\"workspace_manager\",\"workspace_normal\"],\"children\":[{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"总览\",\"codeSuffix\":\"sys_container_service_overview\",\"type\":\"menu\",\"icon\":\"project-space-overview-menu\",\"method\":\"get\",\"url\":\"/project/space/overview\",\"sortCode\":0,\"auths\":[\"workspace_manager\",\"workspace_normal\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\",\"workspace_normal\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"apps/replicasets\",\"batch/jobs\",\"batch/cronjobs\",\"pods\",\"services\",\"secrets\",\"configmaps\",\"persistentvolumeclaims\",\"persistentvolumes\",\"storage.k8s.io/storageclasses\",\"serviceaccounts\",\"namespaces\",\"nodes\",\"resourcequotas\",\"harmonycloud.cn/nodepools\",\"heimdallr.harmonycloud.cn/hdareas\",\"heimdallr.harmonycloud.cn/hdblocks\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/hdpools\",\"heimdallr.harmonycloud.cn/networkdetails\",\"heimdallr.harmonycloud.cn/networkresources\"]}},{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"应用管理\",\"codeSuffix\":\"sys_project_applications\",\"type\":\"menu\",\"icon\":\"app-management-menu\",\"method\":\"get\",\"url\":\"/project/space/application/single\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"单集群应用\",\"codeSuffix\":\"sys_project_applicaiton_single\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/application/single\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"skyview_application_add\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"保存草稿\",\"codeSuffix\":\"skyview_application_template_add\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"重新加载\",\"codeSuffix\":\"skyview_application_reload\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"重新启动\",\"codeSuffix\":\"skyview_application_restart\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"启动组件\",\"codeSuffix\":\"skyview_application_component_start\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"批量启动\",\"codeSuffix\":\"skyview_application_batch_start\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"停止组件\",\"codeSuffix\":\"skyview_application_component_stop\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"批量停止\",\"codeSuffix\":\"skyview_application_batch_stop\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"组件实例数变更\",\"codeSuffix\":\"skyview_application_component_edit_replicas\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"版本编辑更新\",\"codeSuffix\":\"skyview_application_version_update\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"执行批次\",\"codeSuffix\":\"skyview_application_execute\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"完成升级\",\"codeSuffix\":\"skyview_application_update_finish\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"接管流量\",\"codeSuffix\":\"skyview_application_version_flow\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"回滚升级\",\"codeSuffix\":\"skyview_application_update_rollback\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"版本回滚\",\"codeSuffix\":\"skyview_application_version_rollback\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"保存拓扑图\",\"codeSuffix\":\"skyview_application_edit_canvas\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"hpa新增\",\"codeSuffix\":\"skyview_application_hpa_add\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"hpa编辑\",\"codeSuffix\":\"skyview_application_hpa_edit\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"hpa删除\",\"codeSuffix\":\"skyview_application_hpa_remove\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"集群内部暴露\",\"codeSuffix\":\"skyview_application_expose_internalService\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"集群外部暴露\",\"codeSuffix\":\"skyview_application_expose_externalServices\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"skyview_application_remove\",\"type\":\"elem\",\"method\":\"delete\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"草稿继续编辑\",\"codeSuffix\":\"skyview_application_draft_edit\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"草稿删除\",\"codeSuffix\":\"skyview_application_draft_remove\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]}],\"annotations\":{\"resources\":[\"core.oam.dev/applications\",\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"apps/replicasets\",\"batch/jobs\",\"batch/cronjobs\",\"pods\",\"services\",\"secrets\",\"configmaps\",\"persistentvolumeclaims\",\"persistentvolumes\",\"storage.k8s.io/storageclasses\",\"serviceaccounts\",\"namespaces\",\"resourcequotas\",\"nodes\",\"harmonycloud.cn/nodepools\",\"heimdallr.harmonycloud.cn/hdareas\",\"heimdallr.harmonycloud.cn/hdblocks\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/hdpools\",\"heimdallr.harmonycloud.cn/networkdetails\",\"heimdallr.harmonycloud.cn/networkresources\",\"endpoints\",\"harmonycloud.cn/oamapprevisions\"]}},{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"Helm Chart服务\",\"codeSuffix\":\"sys_project_helm_chart_service\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/application/helmChart\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"put\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"应用市场新增\",\"codeSuffix\":\"skyview_helm_chart_to_application_market_add\",\"type\":\"elem\",\"method\":\"post\",\"url\":\"/appstore/types/chart/tags\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"升级\",\"codeSuffix\":\"skyview_helm_chart_upgrade\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/clusters/{clusterName}/namespace/{namespace}/helms/{helmName}/upgrade\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"版本回滚\",\"codeSuffix\":\"skyview_helm_chart_version_rollback\",\"type\":\"elem\",\"method\":\"put\",\"url\":\"/clusters/{clusterName}/namespace/{namespace}/helms/{helmName}/rollback\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"参数对比\",\"codeSuffix\":\"skyview_helm_chart_yaml_compare\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/namespace/{namespace}/helms/{helmName}/revision/{revision}/values\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"文件下载\",\"codeSuffix\":\"skyview_helm_chart_download\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/namespace/{namespace}/helms/{helmName}/revision/{revision}/download\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"服务暴露编辑\",\"codeSuffix\":\"skyview_helm_chart_expose_edit\",\"type\":\"elem\",\"method\":\"put\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"skyview_helm_chart_delete\",\"type\":\"elem\",\"method\":\"delete\",\"url\":\"/clusters/{clusterName}/namespace/{namespace}/helms/{helmName}\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]}]},{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"多集群应用\",\"codeSuffix\":\"sys_project_applicaiton_multi\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/application/multi\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]}]},{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"工作负载\",\"codeSuffix\":\"sys_project_workload\",\"type\":\"menu\",\"icon\":\"load-menu\",\"method\":\"get\",\"url\":\"/project/space/resource/deployment/list\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"无状态部署\",\"codeSuffix\":\"sys_project_deployment\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/resource/deployment/list\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/deployments,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment},/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/describe,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/events,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/metadata,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/pods,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/replicasets,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/replicasets/{replicaset}/yaml,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/yaml\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"skyview_deployment_add\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"create\",\"update\"],\"apps/replicasets\":[\"get\",\"list\",\"create\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑副本数\",\"codeSuffix\":\"skyview_replicas_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"create\",\"update\"],\"apps/replicasets\":[\"get\",\"list\",\"create\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑元数据\",\"codeSuffix\":\"skyview_deployment_metadata_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"create\",\"update\"],\"apps/replicasets\":[\"get\",\"list\",\"create\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"版本管理\",\"codeSuffix\":\"skyview_deployment_version\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"create\",\"update\"],\"apps/replicasets\":[\"get\",\"list\",\"create\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查看版本yaml\",\"codeSuffix\":\"skyview_deployment_yaml_check\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"yaml对比\",\"codeSuffix\":\"skyview_deployment_yaml_compare\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"版本回滚\",\"codeSuffix\":\"skyview_deployment_version_rollback\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/revisions/{revision}/rollback\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"update\"],\"apps/replicasets\":[\"get\",\"list\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑yaml\",\"codeSuffix\":\"skyview_deployment_yaml_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"update\"],\"apps/replicasets\":[\"get\",\"list\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"skyview_deployment_remove\",\"type\":\"elem\",\"method\":\"get\",\"url\":\"/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"delete\"],\"apps/replicasets\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"resourcequotas\",\"apps/deployments\",\"pods\",\"apps/replicasets\"]}},{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"有状态部署\",\"codeSuffix\":\"sys_project_statefulset\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/resource/statefulset/list\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/statefulsets\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"skyview_statefulset_add\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/statefulsets\":[\"get\",\"list\",\"create\",\"update\"],\"namespaces\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑副本数\",\"codeSuffix\":\"skyview_replicas_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/statefulsets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑元数据\",\"codeSuffix\":\"skyview_statefulset_metadata_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/statefulsets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑yaml\",\"codeSuffix\":\"skyview_statefulset_yaml_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/statefulsets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"skyview_statefulset_remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/statefulsets\":[\"get\",\"list\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"events\",\"resourcequotas\",\"apps/statefulsets\",\"pods\"]}},{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"守护进程\",\"codeSuffix\":\"sys_project_daemonset\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/resource/daemonset/list\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"skyview_daemonset_add\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\",\"create\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑元数据\",\"codeSuffix\":\"skyview_daemonset_metadata_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑yaml\",\"codeSuffix\":\"skyview_daemonset_yaml_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"skyview_daemonset_remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"apps/daemonsets\",\"pods\"]}},{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"普通任务\",\"codeSuffix\":\"sys_project_job\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/resource/job/list\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"skyview_job_add\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"batch/jobs\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"重新执行\",\"codeSuffix\":\"skyview_job_restart\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"batch/jobs\":[\"get\",\"list\",\"create\",\"update\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑元数据\",\"codeSuffix\":\"skyview_job_metadata_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"batch/jobs\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑yaml\",\"codeSuffix\":\"skyview_job_yaml_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"batch/jobs\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"skyview_job_remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"batch/jobs\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}]},{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"定时任务\",\"codeSuffix\":\"sys_project_cronjob\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/resource/cronjob/list\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"启停\",\"codeSuffix\":\"schedule\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑元数据\",\"codeSuffix\":\"editMetaData\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑策略\",\"codeSuffix\":\"editInfo\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑yaml\",\"codeSuffix\":\"editYaml\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除执行记录\",\"codeSuffix\":\"deleteJob\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"delete\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"batch/cronjobs\",\"pods\"]}},{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"Pod容器组\",\"codeSuffix\":\"sys_project_pod_container_group\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/resource/pod\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"监控\",\"codeSuffix\":\"monitor\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"pod日志\",\"codeSuffix\":\"containerLog\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"pod控制台\",\"codeSuffix\":\"containerTerminal\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"事件\",\"codeSuffix\":\"event\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查看yaml\",\"codeSuffix\":\"yaml\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"harmonycloud.cn/nodepools\",\"events\",\"pods\",\"nodes\"]}}]},{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"配置挂载\",\"codeSuffix\":\"sys_project_config_mount\",\"type\":\"menu\",\"icon\":\"configure-mount-menu\",\"method\":\"get\",\"url\":\"/project/space/configMap/list\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"配置文件\",\"codeSuffix\":\"sys_project_configMap\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/configMap/list\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"skyview_configmap_add\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\",\"create\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\",\"create\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑挂载数据\",\"codeSuffix\":\"skyview_configmap_data_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑元数据\",\"codeSuffix\":\"skyview_configmap_meta_data_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑Yaml\",\"codeSuffix\":\"skyview_configmap_yaml_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"skyview_configmap_remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\",\"delete\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\",\"delete\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"stellaris.harmonycloud.cn/multiclusterresources\",\"stellaris.harmonycloud.cn/multiclusterresourcebindings\",\"namespaces\",\"resourcequotas\",\"events\",\"pods\",\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"apps/replicasets\",\"batch/jobs\",\"batch/cronjobs\",\"configmaps\"]}},{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"保密字典\",\"codeSuffix\":\"sys_project_Secret\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/secret/list\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"skyview_secret_add\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\",\"create\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\",\"create\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑加密数据\",\"codeSuffix\":\"skyview_secret_data_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑元数据\",\"codeSuffix\":\"skyview_secret_meta_data_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑Yaml\",\"codeSuffix\":\"skyview_secret_yaml_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"skyview_secret_remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\",\"delete\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\",\"delete\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"stellaris.harmonycloud.cn/multiclusterresources\",\"stellaris.harmonycloud.cn/multiclusterresourcebindings\",\"namespaces\",\"resourcequotas\",\"events\",\"pods\",\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"apps/replicasets\",\"batch/jobs\",\"batch/cronjobs\",\"secrets\"]}}]},{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"存储\",\"codeSuffix\":\"sys_storage_service\",\"type\":\"menu\",\"icon\":\"storage-menu\",\"method\":\"get\",\"url\":\"/project/space/pvc/list\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"存储卷声明\",\"codeSuffix\":\"sys_project_PVC\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/pvc/list\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"skyview_pvc_add\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\",\"create\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\",\"create\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\",\"create\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"扩容\",\"codeSuffix\":\"skyview_pvc_expand\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\",\"update\",\"patch\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑元数据\",\"codeSuffix\":\"skyview_pvc_meta_data_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\",\"update\",\"patch\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑Yaml\",\"codeSuffix\":\"skyview_pvc_yaml_edit\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\",\"update\",\"patch\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"skyview_pvc_remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\",\"delete\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\",\"delete\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\",\"delete\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"stellaris.harmonycloud.cn/multiclusterresources\",\"stellaris.harmonycloud.cn/multiclusterresourcebindings\",\"namespaces\",\"resourcequotas\",\"events\",\"pods\",\"storage.k8s.io/storageclasses\",\"persistentvolumeclaims\",\"persistentvolumes\"]}},{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"存储卷\",\"codeSuffix\":\"sys_project_PV\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/pv/list\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"pods\",\"storage.k8s.io/storageclasses\",\"persistentvolumeclaims\",\"persistentvolumes\"]}}]},{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"网络\",\"codeSuffix\":\"sys_project_network_service\",\"type\":\"menu\",\"icon\":\"network-menu\",\"method\":\"get\",\"url\":\"/project/space/network/service\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"Service服务\",\"codeSuffix\":\"sys_project_service\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/network/service\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"endpoints\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"skyview_service_add\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"create\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑\",\"codeSuffix\":\"skyview_service_edit\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增四层对外路由\",\"codeSuffix\":\"skyview_service_add_four_layer_exponse\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增七层对外路由\",\"codeSuffix\":\"skyview_service_add_seven_layer_exponse\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑对外路由\",\"codeSuffix\":\"edit_domains\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除对外路由\",\"codeSuffix\":\"remove_domains\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑元数据\",\"codeSuffix\":\"skyview_service_metadata_edit\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑yaml\",\"codeSuffix\":\"skyview_service_yaml_edit\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"skyview_service_delete\",\"type\":\"elem\",\"method\":\"delete\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"services\",\"expose.helper.harmonycloud.cn/layer4exposes\",\"networking.k8s.io/ingresses\",\"expose.helper.harmonycloud.cn/ingressclasses\",\"configmaps\",\"endpoints\",\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"apps/replicasets\",\"pods\",\"nodes\"]}},{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"Ingress路由\",\"codeSuffix\":\"sys_project_ingress\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/network/ingress\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\"],\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"endpoints\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\",\"watch\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"add\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\"],\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"create\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑\",\"codeSuffix\":\"edit\",\"type\":\"elem\",\"method\":\"put\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\"],\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增对外路由\",\"codeSuffix\":\"add_domains\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\"],\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑对外路由\",\"codeSuffix\":\"edit_domains\",\"type\":\"elem\",\"method\":\"put\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\"],\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除对外路由\",\"codeSuffix\":\"remove_domains\",\"type\":\"elem\",\"method\":\"delete\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"services\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑元数据\",\"codeSuffix\":\"edit_metadata\",\"type\":\"elem\",\"method\":\"put\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\"],\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"remove\",\"type\":\"elem\",\"method\":\"delete\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"services\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"services\",\"expose.helper.harmonycloud.cn/layer4exposes\",\"networking.k8s.io/ingresses\",\"expose.helper.harmonycloud.cn/ingressclasses\",\"apisix.apache.org/apisixroutes\",\"configmaps\",\"secrets\",\"endpoints\",\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"apps/replicasets\",\"pods\",\"nodes\"]}}]},{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"仓库及模板\",\"codeSuffix\":\"sys_repository_template\",\"type\":\"menu\",\"icon\":\"repos-tmpl-menu\",\"method\":\"get\",\"url\":\"/project/space/repository\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"镜像仓库\",\"codeSuffix\":\"sys_image_repository\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/repository\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"上传\",\"codeSuffix\":\"upload_image\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑仓库标签\",\"codeSuffix\":\"repo_label_manage\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查看版本\",\"codeSuffix\":\"view_version\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除镜像\",\"codeSuffix\":\"remove_image\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"添加规则\",\"codeSuffix\":\"add_rule\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"立即执行\",\"codeSuffix\":\"execute_rule\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"模拟运行\",\"codeSuffix\":\"simulation_run\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑规则\",\"codeSuffix\":\"edit_rule\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"启用/禁用规则\",\"codeSuffix\":\"enable_disable_rule\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除规则\",\"codeSuffix\":\"remove_rule\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑定时任务\",\"codeSuffix\":\"edit_timed_task\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"中止\",\"codeSuffix\":\"suspend_rule\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"日志\",\"codeSuffix\":\"log\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"内容信任\",\"codeSuffix\":\"content_trust\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"阻止潜在漏洞镜像\",\"codeSuffix\":\"Block_potential_vulnerability_mirroring\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"自动扫描镜像\",\"codeSuffix\":\"auto_scan\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑CVE白名单\",\"codeSuffix\":\"CVE_whitelist\",\"type\":\"elem\",\"method\":\"post\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"镜像复制\",\"codeSuffix\":\"copy\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"构建记录\",\"codeSuffix\":\"build_record\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"漏洞扫描\",\"codeSuffix\":\"bug_scan\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"拉取/下载\",\"codeSuffix\":\"download\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"标签管理\",\"codeSuffix\":\"label_manage\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除镜像版本\",\"codeSuffix\":\"images_remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"]}]},{\"cloudServiceName\":\"container_service\",\"level\":\"workspace\",\"name\":\"网络模板\",\"codeSuffix\":\"sys_project_space_network_template\",\"type\":\"menu\",\"method\":\"get\",\"url\":\"/project/space/network/template\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"children\":[{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"查询\",\"codeSuffix\":\"query\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"新增\",\"codeSuffix\":\"skyview_project_space_network_template_add\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"编辑\",\"codeSuffix\":\"skyview_project_space_network_template_update\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"delete\",\"create\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}},{\"cloudServiceName\":\"\",\"level\":\"\",\"name\":\"删除\",\"codeSuffix\":\"skyview_project_space_network_template_remove\",\"type\":\"elem\",\"method\":\"get\",\"sortCode\":0,\"auths\":[\"workspace_manager\"],\"annotations\":{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"delete\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"delete\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}}],\"annotations\":{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"heimdallr.harmonycloud.cn/networkresources\",\"heimdallr.harmonycloud.cn/networkdetails\",\"heimdallr.harmonycloud.cn/hdareas\",\"heimdallr.harmonycloud.cn/hdblocks\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/hdpools\",\"heimdallr.harmonycloud.cn/hdsvcs\",\"isolate.harmonycloud.cn/hleases\",\"mystra.heimdallr.harmonycloud.cn/podpolicies\",\"configmaps\",\"pods\"]}}]}],\"annotations\":{\"ceiling\":true,\"titleDescription\":\"以应用管理为中心的容器服务\"}}]}]",
		},
	}

	for index, param := range params {
		var id int64 = 0
		t.Run(fmt.Sprintf("Test_ObjectsRef_%d", index), func(t *testing.T) {
			var menuSteps permissionv1.MenuSteps
			var err error
			err = json.Unmarshal([]byte(param.permissionv1jsonStr), &menuSteps)
			if err != nil {
				t.Errorf("unmarshal error,e:%v", err)
			}
			err = menuSteps.Validator()
			if err != nil {
				t.Errorf("unmarshal error,e:%v", err)
			}

			var permissions []dao.Permission

			for _, menuStep := range menuSteps {
				for _, config := range menuStep.Configs {
					id++
					permissions = append(permissions, convertConfigToPermissionFunc(config, 0)...)
				}
			}

			idFunc := func(t dao.Permission) string {
				return strconv.Itoa(int(t.ID))
			}

			ref := NewInMemoryObjectsRef[dao.Permission](idFunc)
			for _, permission := range permissions {
				permission := permission
				ref.Ref("code_and_parent_id", permission, permission.Code, permission.ParentId)
				ref.Ref("code", permission, permission.Code)
				ref.Ref("parent_id", permission, permission.ParentId)
			}

			testFunc := func() {
				// check by id
				for _, permission := range permissions {
					permission := permission
					id := idFunc(permission)
					cachePermission, exist := ref.FindById(id)
					if !exist && !reflect.DeepEqual(permission, cachePermission) {
						t.Errorf("error")
					}
				}

				// check code by code_and_parent_id
				codesForCodeAndParentID := codeSets(permissions)
				for _, code := range codesForCodeAndParentID {
					codeRefPermissions := ref.FindByRef("code_and_parent_id", code)
					codePermission := sortByCodes(permissions, code)
					codeRefPermissionsIds := sortPermissionId(codeRefPermissions, idFunc)
					codePermissionIds := sortPermissionId(codePermission, idFunc)
					if !compareResult(codeRefPermissionsIds, codePermissionIds) {
						t.Errorf("error,code = %v,from permission id is %v,from ref id is %v", code, codePermissionIds, codeRefPermissionsIds)
					}
				}

				// check code and parent id by code_and_parent_id
				for _, permission := range permissions {
					permission := permission
					codeParentIdRefPermissions := ref.FindByRef("code_and_parent_id", permission.Code, permission.ParentId)
					codeParentIdRefPermissionsIds := sortPermissionId(codeParentIdRefPermissions, idFunc)
					permissionId := []string{idFunc(permission)}
					if !compareResult(codeParentIdRefPermissionsIds, permissionId) {
						t.Errorf("error,code = %v,parent id = %v,from permission id is %v,from ref id is %v", permission.Code, permission.ParentId, permissionId, codeParentIdRefPermissionsIds)
					}
				}

				// check code by code
				codesForCode := codeSets(permissions)
				for _, code := range codesForCode {
					codeRefPermissions := ref.FindByRef("code", code)
					codePermission := sortByCodes(permissions, code)
					codeRefPermissionsIds := sortPermissionId(codeRefPermissions, idFunc)
					codePermissionIds := sortPermissionId(codePermission, idFunc)
					if !compareResult(codeRefPermissionsIds, codePermissionIds) {
						t.Errorf("error,code = %v,from permission id is %v,from ref id is %v", code, codePermissionIds, codeRefPermissionsIds)
					}
				}

				// check parent_id by parent_id
				parentIdsByParentId := parentIDSets(permissions)
				for _, parentID := range parentIdsByParentId {
					codeRefPermissions := ref.FindByRef("parent_id", parentID)
					codePermission := sortByParentID(permissions, parentID)
					codeRefPermissionsIds := sortPermissionId(codeRefPermissions, idFunc)
					codePermissionIds := sortPermissionId(codePermission, idFunc)
					if !compareResult(codeRefPermissionsIds, codePermissionIds) {
						t.Errorf("error,parentId = %v,from permission id is %v,from ref id is %v", parentID, codePermissionIds, codeRefPermissionsIds)
					}
				}

			}
			testFunc()
			// 测试寻找不存在的ID
			_, exist := ref.FindById("hahah")
			if exist {
				t.Errorf("error id hahah is not exist")
			}

			// 测试寻找refType存在 ref不存在
			var testPermissions []dao.Permission
			testPermissions = ref.FindByRef("code_and_parent_id", "xuyunjin")
			if len(testPermissions) != 0 {
				t.Errorf("error type '' ref xuyunjin is not exist")
			}

			// 测试寻找refType不存在
			testPermissions = ref.FindByRef("ccc", "xuyunjin")
			if len(testPermissions) != 0 {
				t.Errorf("error type 'ccc' ref xuyunjin is not exist")
			}

			// 测试code,suffix
			existCode := permissions[0].Code
			testPermissions = ref.FindByRef("code_and_parent_id", existCode, "xuyunjin")
			if len(testPermissions) != 0 {
				t.Errorf("error type 'ccc' ref xuyunjin is not exist")
			}

			permissionLen := len(permissions)
			for i := 0; i < permissionLen; i++ {
				deletePermission := permissions[0]
				permissions = permissions[1:]
				ref.Remove(deletePermission)
				testFunc()
			}

			switch ref.(type) {
			case *inmemoryObjectRef[dao.Permission]:
				inMemoryRef := ref.(*inmemoryObjectRef[dao.Permission])
				tree := inMemoryRef.refTreeMap["code_and_parent_id"]
				if tree.ids.Len() != 0 || len(tree.leaf) != 0 {
					t.Errorf("error,all data deleted,ids is %v,leaf is  %v", tree.ids.UnsortedList(), tree.leaf)
				}
			}

		})
	}

}

func Test_RefTree(t *testing.T) {
	tree := new(RefTree)
	tree.Store("a", 1, 2, 4)
	tree.Store("b", 1, 2, 4)
	tree.Store("c", 1, 2, 5)
	tree.Store("d", 1, 2, 5)
	tree.Store("e", 1, 3, 6)
	tree.Store("f", 1, 3, 6)
	tree.Store("g", 1, 3, 7)
	tree.Store("h", 1, 3, 7)
	params := []struct {
		tree   *RefTree
		ref    []any
		result []string
	}{
		{
			tree:   tree,
			ref:    []any{},
			result: []string{"a", "b", "c", "d", "e", "f", "g", "h"},
		},
		{
			tree:   tree,
			ref:    []any{1},
			result: []string{"a", "b", "c", "d", "e", "f", "g", "h"},
		},
		{
			tree:   tree,
			ref:    []any{1, 2},
			result: []string{"a", "b", "c", "d"},
		},
		{
			tree:   tree,
			ref:    []any{1, 3},
			result: []string{"e", "f", "g", "h"},
		},
		{
			tree:   tree,
			ref:    []any{1, 2, 4},
			result: []string{"a", "b"},
		},
		{
			tree:   tree,
			ref:    []any{1, 2, 5},
			result: []string{"c", "d"},
		},
		{
			tree:   tree,
			ref:    []any{1, 3, 6},
			result: []string{"e", "f"},
		},
		{
			tree:   tree,
			ref:    []any{1, 3, 7},
			result: []string{"g", "h"},
		},
		{
			tree:   tree,
			ref:    []any{2},
			result: []string{},
		},
		{
			tree:   tree,
			ref:    []any{2, 8},
			result: []string{},
		},
	}
	for index, param := range params {
		t.Run(fmt.Sprintf("Test_RefTree_%d", index), func(t *testing.T) {
			result := param.tree.GetIds(param.ref...)
			if !compareResult(param.result, result) {
				t.Errorf("error,want %v,result %v", param.result, result)
			}

		})
	}
}

func Test_RefTreeAndRemove(t *testing.T) {
	tree := new(RefTree)
	tree.Store("a", 1, 2, 4)
	tree.Store("b", 1, 2, 4)
	tree.Store("c", 1, 2, 5)
	tree.Store("d", 1, 2, 5)
	tree.Store("e", 1, 3, 6)
	tree.Store("f", 1, 3, 6)
	tree.Store("g", 1, 3, 7)
	tree.Store("h", 1, 3, 7)
	params := []struct {
		tree     *RefTree
		removeId string
		result   []string
	}{
		{
			tree:     tree,
			removeId: "a",
			result:   []string{"b", "c", "d", "e", "f", "g", "h"},
		},
		{
			tree:     tree,
			removeId: "b",
			result:   []string{"c", "d", "e", "f", "g", "h"},
		},
		{
			tree:     tree,
			removeId: "c",
			result:   []string{"d", "e", "f", "g", "h"},
		},
	}
	for index, param := range params {
		t.Run(fmt.Sprintf("Test_RefTreeAndRemove%d", index), func(t *testing.T) {
			param.tree.Remove(param.removeId)
			result := param.tree.GetIds()
			if !compareResult(param.result, result) {
				t.Errorf("error,want %v,result %v", param.result, result)
			}

		})
	}
}

func Test_compareResult(t *testing.T) {
	params := []struct {
		result1 []string
		result2 []string
		want    bool
	}{
		{
			result1: nil,
			result2: nil,
			want:    true,
		},
		{
			result1: []string{},
			result2: nil,
			want:    true,
		},
		{
			result1: nil,
			result2: []string{},
			want:    true,
		},
		{
			result1: []string{},
			result2: []string{},
			want:    true,
		},
		{
			result1: []string{"a", "b", "c"},
			result2: []string{"c", "b", "a"},
			want:    true,
		},
		{
			result1: []string{"a", "b", "k"},
			result2: []string{"c", "b", "a"},
			want:    false,
		},
		{
			result1: []string{"a", "b", "c", "d"},
			result2: []string{"c", "b", "a"},
			want:    false,
		},
	}
	for index, param := range params {
		t.Run(fmt.Sprintf("Test_compareResult%d", index), func(t *testing.T) {
			result := compareResult(param.result1, param.result2)
			if result != param.want {
				t.Errorf("error want is %v,result is %v", param.want, result)
			}
		})
	}
}

func compareResult(result1, result2 []string) bool {
	if len(result1) != len(result2) {
		return false
	}
	if len(result1) == 0 {
		return true
	}
	resultSet := sets.New[string](result2...)
	for _, val := range result1 {
		if !resultSet.Has(val) {
			return false
		}
	}
	return true
}

func convertConfigToPermissionFunc(config permissionv1.MenuConfig, parentId int64) []dao.Permission {
	var result []dao.Permission
	lastID++
	myID := lastID
	// 处理自己
	permission := buildPermission(dao.Permission{}, myID, parentId, config)
	result = append(result, permission)
	// 处理孩子
	if len(config.Children) != 0 {
		for _, child := range config.Children {
			child := child
			result = append(result, convertConfigToPermissionFunc(child, myID)...)
		}
	}
	return result
}

func codeSets(permissions []dao.Permission) []string {
	codeSets := sets.New[string]()
	for _, permission := range permissions {
		codeSets.Insert(permission.Code)
	}
	return codeSets.UnsortedList()
}

func sortByCodes(permissions []dao.Permission, code string) []dao.Permission {
	var result []dao.Permission
	for _, permission := range permissions {
		permission := permission
		if strings.EqualFold(permission.Code, code) {
			result = append(result, permission)
		}
	}
	return result
}

func parentIDSets(permissions []dao.Permission) []int64 {
	parentIDSets := sets.New[int64]()
	for _, permission := range permissions {
		parentIDSets.Insert(permission.ParentId)
	}
	return parentIDSets.UnsortedList()
}

func sortByParentID(permissions []dao.Permission, parentID int64) []dao.Permission {
	var result []dao.Permission
	for _, permission := range permissions {
		permission := permission
		if permission.ParentId == parentID {
			result = append(result, permission)
		}
	}
	return result
}

func sortPermissionId(permissions []dao.Permission, idFunc func(permission dao.Permission) string) []string {
	var ids []string
	for _, permissin := range permissions {
		ids = append(ids, idFunc(permissin))
	}
	return ids
}
