package internal

import (
	"fmt"

	translationv1 "harmonycloud.cn/unifiedportal/api-definition/translation/v1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao"
	"k8s.io/apimachinery/pkg/util/sets"
)

type TranslationOperator interface {
	Handle(configs []translationv1.TranslateConfig) error
}

func GetTranslationOperator(operator translationv1.TranslateConfigOperator, pool TranslationPool) (TranslationOperator, error) {

	switch operator {
	case translationv1.OperatorADD:
		return &AddTranslationOperator{pool: pool}, nil
	case translationv1.OperatorDelete:
		return &DeleteTranslationOperator{pool: pool}, nil
	default:
		return nil, fmt.Errorf("unsuppotr operator of '%v'", operator)
	}
}

type AddTranslationOperator struct {
	pool TranslationPool
}

func (operator *AddTranslationOperator) Handle(configs []translationv1.TranslateConfig) error {
	for _, config := range configs {
		config := config
		if err := operator.HandleForResources(config.ResourceTranslates); err != nil {
			return err
		}
		if err := operator.HandleForRegexs(config.RegexTranslates); err != nil {
			return err
		}
	}
	return nil
}

func (operator *AddTranslationOperator) HandleForResources(configs []translationv1.ResourceTranslateConfig) error {
	if len(configs) == 0 {
		return nil
	}
	for _, config := range configs {
		config := config
		if err := operator.HandleForResource(config); err != nil {
			return err
		}
	}
	return nil
}

func (operator *AddTranslationOperator) HandleForRegexs(configs []translationv1.RegexResourceConfig) error {
	if len(configs) == 0 {
		return nil
	}
	for _, config := range configs {
		config := config
		if err := operator.HandleForRegex(config); err != nil {
			return err
		}
	}
	return nil
}

func (operator *AddTranslationOperator) HandleForResource(config translationv1.ResourceTranslateConfig) error {
	if len(config.LanguageMap) == 0 {
		return nil
	}
	for languageCode, translation := range config.LanguageMap {
		// find is exist
		cache, err := operator.pool.GetResourceTranslateConfig(config.GroupName, config.UniqueValue, languageCode, config.Property)
		if err != nil {
			return err
		}
		if cache == nil {
			cache = new(dao.ResourceTranslateConfig)
		}
		cache.GroupName = config.GroupName
		cache.UniqueValue = config.UniqueValue
		cache.LanguageCode = languageCode
		cache.Property = config.Property
		cache.Translation = translation
		if err := operator.pool.SaveResourceTranslateConfig(*cache); err != nil {
			return err
		}
	}
	return nil
}

func (operator *AddTranslationOperator) HandleForRegex(config translationv1.RegexResourceConfig) error {
	if len(config.LanguageMap) == 0 {
		return nil
	}

	regexResourceConfig, err := operator.pool.GetRegexResourceConfigByGroupNameAndRegex(config.GroupName, config.Regex)
	if err != nil {
		return err
	}
	if regexResourceConfig == nil {
		operator.pool.MaxRegexResourceConfigIdIncrement()
		id := operator.pool.GetMaxRegexResourceConfigId()
		regexResourceConfig = new(dao.RegexResourceConfig)
		regexResourceConfig.ID = id
		regexResourceConfig.GroupName = config.GroupName
		regexResourceConfig.Regex = config.Regex
		if err := operator.pool.SaveRegexResourceConfig(*regexResourceConfig); err != nil {
			return err
		}
	}
	for languageCode, translate := range config.LanguageMap {
		rrtc, err := operator.pool.GetRegexResourceTranslateConfigByRegexIdAndLanguageCode(regexResourceConfig.ID, languageCode)
		if err != nil {
			return err
		}
		if rrtc == nil {
			rrtc = new(dao.RegexResourceTranslateConfig)
			rrtc.RegexID = regexResourceConfig.ID
			rrtc.LanguageCode = languageCode
			rrtc.Translation = translate
		}
		if err := operator.pool.SaveRegexResourceTranslateConfig(*rrtc); err != nil {
			return err
		}
	}
	return nil
}

type DeleteTranslationOperator struct {
	pool TranslationPool
}

func (operator *DeleteTranslationOperator) Handle(configs []translationv1.TranslateConfig) error {
	for _, config := range configs {
		config := config
		if err := operator.HandleForResources(config.ResourceTranslates); err != nil {
			return err
		}
		if err := operator.HandleForRegexs(config.RegexTranslates); err != nil {
			return err
		}
	}
	return operator.cleanRegex()
}

func (operator *DeleteTranslationOperator) HandleForResources(configs []translationv1.ResourceTranslateConfig) error {
	if len(configs) == 0 {
		return nil
	}
	for _, config := range configs {
		config := config
		if err := operator.HandleForResource(config); err != nil {
			return err
		}
	}
	return nil
}
func (operator *DeleteTranslationOperator) HandleForRegexs(configs []translationv1.RegexResourceConfig) error {
	if len(configs) == 0 {
		return nil
	}
	for _, config := range configs {
		config := config
		if err := operator.HandleForRegex(config); err != nil {
			return err
		}
	}
	return nil
}

func (operator *DeleteTranslationOperator) HandleForResource(config translationv1.ResourceTranslateConfig) error {
	if len(config.LanguageMap) == 0 {
		// remove all
		config.LanguageMap = map[string]string{}
		configs, err := operator.pool.GetResourceTranslateConfigByGroupNameAndUniqueValueAndProperty(config.GroupName, config.UniqueValue, config.Property)
		if err != nil {
			return err
		}
		for _, c := range configs {
			config.LanguageMap[c.LanguageCode] = c.Translation
		}
	}
	for languageCode, _ := range config.LanguageMap {
		// find is exist
		cache, err := operator.pool.GetResourceTranslateConfig(config.GroupName, config.UniqueValue, languageCode, config.Property)
		if err != nil {
			return err
		}
		if cache != nil {
			if err := operator.pool.DeleteResourceTranslateConfig(*cache); err != nil {
				return err
			}
		}
	}
	return nil
}

func (operator *DeleteTranslationOperator) HandleForRegex(config translationv1.RegexResourceConfig) error {
	regexResourceConfig, err := operator.pool.GetRegexResourceConfigByGroupNameAndRegex(config.GroupName, config.Regex)
	if err != nil {
		return err
	}
	if regexResourceConfig == nil {
		return nil
	}
	if len(config.LanguageMap) == 0 {
		translates, err := operator.pool.GetRegexResourceTranslateConfigByRegexId(regexResourceConfig.ID)
		if err != nil {
			return err
		}
		config.LanguageMap = map[string]string{}
		for _, t := range translates {
			config.LanguageMap[t.LanguageCode] = t.Translation
		}
	}
	for languageCode, _ := range config.LanguageMap {
		rc, err := operator.pool.GetRegexResourceTranslateConfigByRegexIdAndLanguageCode(regexResourceConfig.ID, languageCode)
		if err != nil {
			return err
		}
		if rc != nil {
			if err := operator.pool.DeleteRegexResourceTranslateConfig(*rc); err != nil {
				return err
			}
		}
	}
	return nil
}

func (operator *DeleteTranslationOperator) cleanRegex() error {
	allRegexResourceConfigs, err := operator.pool.ListAllRegexResourceConfig()
	if err != nil {
		return err
	}
	allRegexResourceTranslateConfigs, err := operator.pool.ListAllRegexResourceTranslateConfig()
	if err != nil {
		return err
	}
	// 删除 allRegexResourceConfigs 没有 allRegexResourceTranslateConfigs的
	var regexResourceTranslateGroupByParentId map[int64]sets.Set[int64] = map[int64]sets.Set[int64]{}
	for _, rrtc := range allRegexResourceTranslateConfigs {
		children := regexResourceTranslateGroupByParentId[rrtc.RegexID]
		if children == nil {
			children = sets.New[int64]()
		}
		children.Insert(rrtc.ID)
		regexResourceTranslateGroupByParentId[rrtc.RegexID] = children
	}
	for _, regexResourceConfigs := range allRegexResourceConfigs {
		regexResourceConfigs := regexResourceConfigs
		_, exist := regexResourceTranslateGroupByParentId[regexResourceConfigs.ID]
		if !exist {
			if err := operator.pool.DeleteRegexResourceConfig(regexResourceConfigs); err != nil {
				return err
			}
		}
	}

	// 删除 allRegexResourceTranslateConfigs的 没有 allRegexResourceConfigs
	regexResourceConfigIdSet := sets.New[int64]()
	for _, regexResourceConfigs := range allRegexResourceConfigs {
		regexResourceConfigIdSet.Insert(regexResourceConfigs.ID)
	}
	for _, regexResourceTranslateConfigs := range allRegexResourceTranslateConfigs {
		regexResourceTranslateConfigs := regexResourceTranslateConfigs
		if !regexResourceConfigIdSet.Has(regexResourceTranslateConfigs.RegexID) {
			if err := operator.pool.DeleteRegexResourceTranslateConfig(regexResourceTranslateConfigs); err != nil {
				return err
			}
		}
	}
	return nil

}
