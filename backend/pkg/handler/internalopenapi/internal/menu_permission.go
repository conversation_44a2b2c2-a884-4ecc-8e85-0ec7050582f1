package internal

import (
	"encoding/json"
	"fmt"
	"sort"
	"strconv"

	permissionv1 "harmonycloud.cn/unifiedportal/api-definition/permission/v1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	"k8s.io/apimachinery/pkg/util/sets"
)

const (
	PermissionGroupName        = "sys_permission"
	PermissionAnotherGroupName = "sys_permission_another"
	PermissionProperty         = "name"
)

type PermissionOperator interface {
	Handle(configs permissionv1.MenuConfigs) error
}

func GetOperator(operator permissionv1.MenuConfigOperator, pool PermissionPool) (PermissionOperator, error) {
	switch operator {
	case permissionv1.OperatorADD:
		return &addOperator{pool: pool}, nil
	case permissionv1.OperatorDelete:
		return &deleteOperator{pool: pool}, nil
	default:
		return nil, fmt.Errorf("unsuppotr operator of '%v'", operator)
	}
}

type addOperator struct {
	pool PermissionPool
}

func (operator addOperator) Handle(configs permissionv1.MenuConfigs) error {
	if len(configs) == 0 {
		return nil
	}
	// 角色校验 角色需均在数据库中
	var roleIds []int
	var err error
	if roleIds, err = configsConvert2RoleIds(configs); err != nil {
		return err
	}
	if len(roleIds) != 0 {
		for _, roleId := range roleIds {
			exist, err := operator.pool.IsRoleExist(int64(roleId))
			if err != nil {
				return err
			} else if !exist {
				return fmt.Errorf("role is not apply, should be exist role id is '%+v'", roleId)
			}
		}
	}

	// 中序遍历处理 config
	for _, config := range configs {
		if err := operator.dealWithConfigFunc(config); err != nil {
			return err
		}
	}
	return operator.handleRolePermissions()
}

func (operator *addOperator) dealWithConfigFunc(config permissionv1.MenuConfig) error {
	// get permission parent id
	var parentId int64
	if config.ParentCode == nil {
		parentId = 0
	} else {
		parentCode := config.ParentCode
		parentPermission, err := operator.pool.GetPermissionByCode(*parentCode)
		if err != nil {
			return err
		} else if parentPermission == nil {
			var errorMsg string
			errorMsg = fmt.Sprintf("sys_permission code = '%s' is not exist", *parentCode)
			logger.GetSugared().Errorf(errorMsg)
			return fmt.Errorf(errorMsg)
		}
		parentId = parentPermission.ID
	}

	// GetPermission
	code := config.ToCode()
	var permission *dao.Permission
	var getPermissionError error

	if config.Type == permissionv1.MenuTypeElem {
		permission, getPermissionError = operator.pool.GetPermissionByCodeAndParentId(code, parentId)
	} else {
		permission, getPermissionError = operator.pool.GetPermissionByCode(code)
	}
	if getPermissionError != nil {
		// 出现异常
		return getPermissionError
	}

	// apply permission
	var willApplyPermission dao.Permission

	if permission == nil {
		// 记录不存在
		logger.GetSugared().Infof("[addOperator<handle>] permission code '%s' is not exist,it will be insert.", code)
		operator.pool.MaxPermissionIDAutoIncrement()
		willApplyPermission = buildPermission(dao.Permission{}, operator.pool.GetMaxPermissionID(), parentId, config)
	} else {
		// 记录已存在
		logger.GetSugared().Infof("[addOperator<handle>] permission code '%s' is exist,it will be update.", code)
		willApplyPermission = buildPermission(*permission, permission.ID, parentId, config)
	}
	if err := operator.pool.ApplyPermission(willApplyPermission); err != nil {
		return err
	}

	// deal with language translate
	// ---- translate of name
	if len(config.NameMultiLanguage) != 0 {
		for code, translate := range config.NameMultiLanguage {
			groupName := PermissionGroupName
			uniqueValue := strconv.Itoa(int(willApplyPermission.ID))
			languageCode := code
			property := PermissionProperty
			translate := translate
			rtc, err := operator.pool.GetResourceTranslateConfig(groupName, uniqueValue, languageCode, property)
			if err != nil {
				return err
			}
			if rtc == nil {
				rtc = new(dao.ResourceTranslateConfig)
			}
			rtc.GroupName = groupName
			rtc.UniqueValue = uniqueValue
			rtc.LanguageCode = languageCode
			rtc.Property = property
			rtc.Translation = translate
			if err := operator.pool.SaveResourceTranslateConfig(*rtc); err != nil {
				return err
			}
		}
	}
	// ---- translate of another
	if len(config.AnotherMultiLanguageDic) != 0 {
		for code, translateMap := range config.AnotherMultiLanguageDic {
			if len(translateMap) == 0 {
				continue
			}
			for property, translate := range translateMap {
				groupName := PermissionAnotherGroupName
				uniqueValue := strconv.Itoa(int(willApplyPermission.ID))
				languageCode := code
				property := property
				translate := translate
				rtc, err := operator.pool.GetResourceTranslateConfig(groupName, uniqueValue, languageCode, property)
				if err != nil {
					return err
				}
				if rtc == nil {
					rtc = new(dao.ResourceTranslateConfig)
				}
				rtc.GroupName = groupName
				rtc.UniqueValue = uniqueValue
				rtc.LanguageCode = languageCode
				rtc.Property = property
				rtc.Translation = translate
				if err := operator.pool.SaveResourceTranslateConfig(*rtc); err != nil {
					return err
				}
			}
		}
	}

	// deal with role permission
	for _, auth := range config.Auths {
		roleId, _ := auth.AsDaoValue()
		permissionId := willApplyPermission.ID
		rolePermission, err := operator.pool.GetRolePermissionByRoleIdAndPermissionIDWithOrganId(int64(roleId), permissionId, nil)
		if err != nil {
			return err
		}

		if rolePermission == nil {
			// 记录不存在
			logger.GetSugared().Infof("[addOperator<handle>] get role permission, role id = '%d',permission_id = '%d' is not exist,it will be insert.", roleId, permissionId)
			operator.pool.MaxRolePermissionIDAutoIncrement()
			rolePermission = new(dao.RolePermission)
			rolePermission.ID = operator.pool.GetMaxRolePermissionID()
			rolePermission.RoleID = int64(roleId)
			rolePermission.PermissionID = permissionId
			// for insert
			if err := operator.pool.CreateRolePermission(*rolePermission); err != nil {
				return err
			}
		} else {
			// 记录已存在
			logger.GetSugared().Infof("[addOperator<handle>] get role permission, role id = '%d',permission_id = '%d' is not exist,it will be ignored.", roleId, permissionId)
		}

	}

	// 处理child
	if len(config.Children) != 0 {
		for _, child := range config.Children {
			if err := operator.dealWithConfigFunc(child); err != nil {
				return err
			}
		}
	}
	return nil
}

// handleRolePermissions
// 处理角色权限 补充下级具备权限但上级不具备权限的情况
func (operator *addOperator) handleRolePermissions() error {
	// 获取所有角色、权限、角色权限点
	var permissions dao.PermissionList
	var err error
	if permissions, err = operator.pool.ListAllPermissions(); err != nil {
		return err
	}

	leftPermissions := permissions.LeftPermissions()
	for _, left := range leftPermissions {
		left := left
		if err := operator.handlePermissionForRolePermission(left); err != nil {
			return err
		}
	}
	return nil

}

func (operator *addOperator) handlePermissionForRolePermission(permission dao.Permission) error {
	// 获取父亲 如果父亲不存在则跳过
	permissionParentId := permission.ParentId
	parentPermission, err := operator.pool.getPermissionByPermissionId(permissionParentId)
	if err != nil {
		return err
	}
	if parentPermission == nil {
		return nil
	}

	// 获取当前权限的 RolePermission 列表
	permissionId := permission.ID
	rolePermissions, err := operator.pool.GetRolePermissionsByPermissionId(permissionId)
	if err != nil {
		return err
	}
	if len(rolePermissions) != 0 {
		for _, rolePermission := range rolePermissions {
			rolePermission := rolePermission
			parentRolePermission, err := operator.pool.GetRolePermissionByRoleIdAndPermissionIDWithOrganId(rolePermission.RoleID, permissionParentId, rolePermission.OrganID)
			if err != nil {
				return err
			}

			if parentRolePermission == nil {
				operator.pool.MaxRolePermissionIDAutoIncrement()
				addRolePermission := new(dao.RolePermission)
				addRolePermission.ID = operator.pool.GetMaxRolePermissionID()
				addRolePermission.RoleID = rolePermission.RoleID
				addRolePermission.PermissionID = permissionParentId
				addRolePermission.OrganID = rolePermission.OrganID
				// for insert
				if err := operator.pool.CreateRolePermission(*addRolePermission); err != nil {
					return err
				}
			}

		}
	}

	return operator.handlePermissionForRolePermission(*parentPermission)
}

type deleteOperator struct {
	pool PermissionPool
}

func (operator deleteOperator) Handle(configs permissionv1.MenuConfigs) error {
	if len(configs) == 0 {
		return nil
	}
	for i := len(configs) - 1; i >= 0; i-- {
		config := configs[i]
		if err := operator.dealWithConfigFunc(config); err != nil {
			return err
		}

	}

	return nil
}
func (operator *deleteOperator) dealWithConfigFunc(config permissionv1.MenuConfig) error {
	// 	先处理子权限
	if len(config.Children) != 0 {
		for i := len(config.Children) - 1; i >= 0; i-- {
			child := config.Children[i]
			if err := operator.dealWithConfigFunc(child); err != nil {
				return err
			}

		}
	}
	// 查找权限ID
	var permission *dao.Permission
	var getPermissionErr error
	code := config.ToCode()

	if config.Type == permissionv1.MenuTypeElem {
		// 先查找父亲
		parentCode := *config.ParentCode
		parentPermission, err := operator.pool.GetPermissionByCode(parentCode)
		if err != nil {
			return err
		} else if parentPermission == nil {
			// return fmt.Errorf("parent is nil,parent code is %v", parentPermission)
			// menuType = Elem 时 找不到父亲可视为不存在
			return nil
		}
		parentId := parentPermission.ParentId
		permission, getPermissionErr = operator.pool.GetPermissionByCodeAndParentId(code, parentId)

	} else {
		permission, getPermissionErr = operator.pool.GetPermissionByCode(code)
	}
	if getPermissionErr != nil {
		return getPermissionErr
	} else if permission == nil {
		return nil
	}
	permissionId := permission.ID

	// 处理翻译
	resourceTranslateConfigs := []dao.ResourceTranslateConfig{}
	uniqueValue := strconv.Itoa(int(permissionId))
	// ---- 处理翻译 by 名称
	if len(config.NameMultiLanguage) != 0 {
		// 有config.NameMultiLanguage 标识只移除名称翻译
		for language, _ := range config.NameMultiLanguage {
			rtcs, err := operator.pool.GetResourceTranslateConfigByGroupNameAndUniqueValueAndLanguageCode(PermissionGroupName, uniqueValue, language)
			if err != nil {
				return err
			}
			resourceTranslateConfigs = append(resourceTranslateConfigs, rtcs...)
		}
	}
	// ----处理翻译 by another
	if len(config.AnotherMultiLanguageDic) != 0 {
		for language, propertiMap := range config.AnotherMultiLanguageDic {
			if len(propertiMap) == 0 {
				continue
			}
			for property, _ := range propertiMap {
				rtc, err := operator.pool.GetResourceTranslateConfig(PermissionAnotherGroupName, uniqueValue, language, property)
				if err != nil {
					return err
				}
				if rtc != nil {
					resourceTranslateConfigs = append(resourceTranslateConfigs, *rtc)
				}
			}
		}
	}

	// delete resourceTranslateConfigs
	if err := operator.pool.DeleteResourceTranslateConfig(resourceTranslateConfigs); err != nil {
		return err
	}

	// 处理角色
	roleIds, err := configConvert2RoleIdsUnIncludeChild(config)
	if err != nil {
		return err
	}
	var toDeleteRolePermissions []dao.RolePermission
	if len(roleIds) != 0 {
		rolePermissions, err := operator.pool.GetRolePermissionsByPermissionIdAndRoleIdsWithOrganId(permissionId, nil, utils.Ints2Int64s(roleIds...)...)
		if err != nil {
			return err
		}
		toDeleteRolePermissions = append(toDeleteRolePermissions, rolePermissions...)
	}
	if err := operator.pool.DeleteRolePermissions(toDeleteRolePermissions...); err != nil {
		return err
	}

	// 处理需要删除菜单的场景
	if len(roleIds) == 0 && len(config.NameMultiLanguage) == 0 && len(config.AnotherMultiLanguageDic) == 0 {
		// 先处理子菜单
		if err := operator.dealForPermissionChild(permissionId, nil); err != nil {
			return err
		}
		// 处理翻译
		// 翻译 by name
		resourceTranslateConfigs, err = operator.pool.GetResourceTranslateConfigByGroupNameAndUniqueValue(PermissionGroupName, uniqueValue)
		if err != nil {
			return err
		}
		if err := operator.pool.DeleteResourceTranslateConfig(resourceTranslateConfigs); err != nil {
			return err
		}
		// 翻译 by another
		resourceTranslateConfigs, err = operator.pool.GetResourceTranslateConfigByGroupNameAndUniqueValue(PermissionAnotherGroupName, uniqueValue)
		if err != nil {
			return err
		}
		if err := operator.pool.DeleteResourceTranslateConfig(resourceTranslateConfigs); err != nil {
			return err
		}

		// 移除本身的权限
		toDeleteRolePermissions, err = operator.pool.GetRolePermissionsByPermissionIdWithOrganId(permissionId, nil)
		if err != nil {
			return err
		}
		if err := operator.pool.DeleteRolePermissions(toDeleteRolePermissions...); err != nil {
			return err
		}

		// 处理菜单 set idDelete on exist
		permission.IsDelete = true
		// for update
		if err := operator.pool.ApplyPermission(*permission); err != nil {
			return err
		}
	}

	return nil
}

func (operator *deleteOperator) dealForPermissionChild(parentId int64, dealIdStack *sets.Set[int64]) error {
	// 处理 dealIdStack 避免脏数据成环
	if dealIdStack == nil {
		dealIdStack = &sets.Set[int64]{}
	}
	// 根据parentId查找未删除的子项
	children, err := operator.pool.GetUnDeletePermissionsByParentId(parentId)
	if err != nil {
		return err
	}
	if len(children) == 0 {
		return nil
	}

	for _, child := range children {
		permissionId := child.ID
		if dealIdStack.Has(permissionId) {
			continue
		}
		dealIdStack.Insert(permissionId)
		// 先处理孩子
		if err := operator.dealForPermissionChild(permissionId, dealIdStack); err != nil {
			return err
		}
		// 先删除role permission
		rolePermissions, err := operator.pool.GetRolePermissionsByPermissionId(permissionId)
		if err != nil {
			return err
		}
		if err := operator.pool.DeleteRolePermissions(rolePermissions...); err != nil {
			return err
		}

		// 先处理翻译
		// 翻译 by name
		uniqueValue := strconv.Itoa(int(permissionId))
		var rtcs []dao.ResourceTranslateConfig
		r, err := operator.pool.GetResourceTranslateConfigByGroupNameAndUniqueValue(PermissionGroupName, uniqueValue)
		if err != nil {
			return err
		}
		rtcs = append(rtcs, r...)

		// 翻译 by another
		r, err = operator.pool.GetResourceTranslateConfigByGroupNameAndUniqueValue(PermissionAnotherGroupName, uniqueValue)
		if err != nil {
			return err
		}
		rtcs = append(rtcs, r...)

		if err := operator.pool.DeleteResourceTranslateConfig(rtcs); err != nil {
			return err
		}

		// 再删除 permission
		child.IsDelete = true
		if err := operator.pool.ApplyPermission(child); err != nil {
			return err
		}
	}
	return nil
}

// configsConvert2RoleIds
// 获取configs中的所有角色 包含递归子角色
func configsConvert2RoleIds(configs permissionv1.MenuConfigs) ([]int, error) {
	roleIdSets := sets.Set[int]{}
	for _, config := range configs {
		roleIds, err := configConvert2RoleIds(config)
		if err != nil {
			return nil, err
		}
		roleIdSets.Insert(roleIds...)
	}
	var list []int
	list = roleIdSets.UnsortedList()
	sort.Ints(list)
	return list, nil
}

// configConvert2RoleIds
// 获取config中的所有角色 包含递归子角色
func configConvert2RoleIds(config permissionv1.MenuConfig) ([]int, error) {
	roleIdSets := sets.Set[int]{}
	// add config role ids
	roleIds, err := configConvert2RoleIdsUnIncludeChild(config)
	if err != nil {
		return nil, err
	}
	roleIdSets.Insert(roleIds...)

	// add child role ids
	if len(config.Children) != 0 {
		for _, child := range config.Children {
			roleIds, err = configConvert2RoleIds(child)
			if err != nil {
				return nil, err
			}
			roleIdSets.Insert(roleIds...)
		}
	}
	var list []int
	list = roleIdSets.UnsortedList()
	sort.Ints(list)
	return list, nil
}

// configConvert2RoleIdsUnIncludeChild
// 获取config中的所有角色 不包含递归子角色
func configConvert2RoleIdsUnIncludeChild(config permissionv1.MenuConfig) ([]int, error) {
	roleSet := sets.Set[permissionv1.Role]{}
	// before result handle
	roleSet.Insert(config.Auths...)
	// to result
	var list []permissionv1.Role
	list = roleSet.UnsortedList()
	result := make([]int, 0, len(list))
	for _, item := range list {
		roleId, err := item.AsDaoValue()
		if err != nil {
			return nil, err
		}
		result = append(result, roleId)
	}
	sort.Ints(result)
	return result, nil
}

func buildPermission(oldPermission dao.Permission, id, parentId int64, config permissionv1.MenuConfig) dao.Permission {
	oldPermission.ID = id
	oldPermission.ParentId = parentId
	oldPermission.Name = config.Name
	oldPermission.Code = config.ToCode()
	permissionType, _ := config.Type.AsDaoValue()
	oldPermission.PermissionType = permissionType
	kind, _ := config.Level.AsDaoValue()
	oldPermission.Kind = kind
	oldPermission.AppId = utils.Int64Prt(210482)
	oldPermission.Icon = config.Icon
	method, _ := config.Method.AsDaoValue()
	oldPermission.Method = method
	oldPermission.Url = config.URL
	v := !config.Invisible
	oldPermission.Visible = &v
	oldPermission.SortId = config.SortCode
	oldPermission.IsDelete = false
	if config.Annotations != nil {
		annotationBytes, _ := json.Marshal(config.Annotations)
		annotationStr := string(annotationBytes)
		oldPermission.Annotations = &annotationStr
	} else {
		oldPermission.Annotations = nil
	}
	return oldPermission
}
