package internal

import (
	"k8s.io/apimachinery/pkg/util/sets"
)

type ObjectsRef[T comparable] interface {
	// ListUnSorted
	// 获取Cache中的全部Object
	ListUnSorted() []T

	// ID
	// 获取Object的唯一Key
	ID(T) string

	// FindById
	// 通过ID查找元素
	FindById(ID string) (T, bool)

	// Ref
	// 创建索引
	Ref(refType string, t T, ref ...any)

	// Remove
	// 移除元素
	Remove(t T)

	// FindByRef
	// 通过索引查找元素
	FindByRef(refType string, ref ...any) []T
}

func NewInMemoryObjectsRef[T comparable](hashFunc func(t T) string) ObjectsRef[T] {
	return &inmemoryObjectRef[T]{
		objectMap:  make(map[string]T),
		refTreeMap: make(map[string]*RefTree),
		hashFunc:   hashFunc,
	}
}

type inmemoryObjectRef[T comparable] struct {
	// objectMap
	// object 的 主键索引树
	objectMap  map[string]T
	refTreeMap map[string]*RefTree
	hashFunc   func(T) string
}

// ListUnSorted
// 获取Cache中的全部Object
func (objRef *inmemoryObjectRef[T]) ListUnSorted() []T {
	var result = make([]T, 0)
	for _, value := range objRef.objectMap {
		result = append(result, value)
	}
	return result
}

// ID
// 获取Object的唯一Key
func (objRef *inmemoryObjectRef[T]) ID(t T) string {
	return objRef.hashFunc(t)
}

func (objRef *inmemoryObjectRef[T]) FindById(ID string) (T, bool) {
	t, exist := objRef.objectMap[ID]
	return t, exist
}

// Ref
// 创建索引
func (objRef *inmemoryObjectRef[T]) Ref(refType string, t T, ref ...any) {
	// 若元素不存在 存储元素
	id := objRef.ID(t)
	if _, exist := objRef.FindById(id); !exist {
		objRef.objectMap[id] = t
	}
	// 创建索引
	tree, exist := objRef.refTreeMap[refType]
	if !exist {
		tree = new(RefTree)
		objRef.refTreeMap[refType] = tree
	}
	tree.Store(id, ref...)
}

// Remove
// 移除元素
func (objRef *inmemoryObjectRef[T]) Remove(t T) {
	// 若元素不存在 则字节返回
	id := objRef.ID(t)
	if _, exist := objRef.FindById(id); !exist {
		return
	}
	// 若元素存在 则在主键索引中移除 并移除子索引
	delete(objRef.objectMap, id)
	if len(objRef.refTreeMap) != 0 {
		for _, tree := range objRef.refTreeMap {
			tree.Remove(id)
		}
	}
}

// FindByRef
// 通过索引查找元素
func (objRef *inmemoryObjectRef[T]) FindByRef(refType string, ref ...any) []T {
	tree, exist := objRef.refTreeMap[refType]
	if !exist {
		return nil
	}
	ids := tree.GetIds(ref...)
	if ids == nil {
		return nil
	}
	var result []T
	for _, id := range ids {
		obj, _ := objRef.FindById(id)
		result = append(result, obj)
	}
	return result
}

type RefTree struct {
	key  any
	leaf map[any]*RefTree
	ids  sets.Set[string]
}

func (tree *RefTree) Store(id string, ref ...any) {
	tree.shouldInit()
	// 存储ID
	if len(ref) == 0 {
		tree.ids.Insert(id)
		return
	}

	// 向叶子中保存
	currentRef := ref[0]
	nextRefs := ref[1:]
	// find current tree from child
	leafTree, exist := tree.leaf[currentRef]
	if !exist {
		leafTree = new(RefTree)
		leafTree.key = currentRef
		tree.leaf[currentRef] = leafTree
	}
	leafTree.Store(id, nextRefs...)
}

func (tree *RefTree) Remove(id string) {
	tree.shouldInit()
	removeIdFromTree(id, tree)
}

func (tree *RefTree) GetIds(ref ...any) []string {
	tree.shouldInit()
	if len(ref) == 0 {
		var result []string
		var current []string = tree.ids.UnsortedList()
		if len(current) != 0 {
			result = append(result, current...)
		}
		if len(tree.leaf) != 0 {
			for _, child := range tree.leaf {
				if childResult := child.GetIds(); len(childResult) != 0 {
					result = append(result, childResult...)
				}
			}
		}
		return result
	}

	currentRef := ref[0]
	nextRefs := ref[1:]
	// 查找叶子
	child, exist := tree.leaf[currentRef]
	if !exist {
		return nil
	}
	return child.GetIds(nextRefs...)
}

func (tree *RefTree) shouldInit() {
	// 初始化left
	if tree.leaf == nil {
		tree.leaf = make(map[any]*RefTree)
	}
	// 初始化ids
	if tree.ids == nil {
		tree.ids = sets.New[string]()
	}
}

func removeIdFromTree(id string, tree *RefTree) bool {
	if tree.ids != nil && tree.ids.Has(id) {
		// remove from values
		tree.ids.Delete(id)
	}
	// deal with child
	if tree.leaf != nil {
		var removeTreeKeys []any
		for key, child := range tree.leaf {
			key := key
			child := child
			if shouldRemove := removeIdFromTree(id, child); shouldRemove {
				removeTreeKeys = append(removeTreeKeys, key)
			}
		}
		if len(removeTreeKeys) != 0 {
			for _, key := range removeTreeKeys {
				key := key
				delete(tree.leaf, key)
			}
		}
	}

	// 告诉上层移除自己
	if tree.ids.Len() == 0 && len(tree.leaf) == 0 {
		return true
	}
	return false

}
