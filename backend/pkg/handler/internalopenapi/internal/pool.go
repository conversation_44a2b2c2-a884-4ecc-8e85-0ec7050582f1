package internal

import (
	"database/sql"
	"fmt"

	"github.com/shopspring/decimal"
	"gorm.io/gorm"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/database"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao"
	"k8s.io/apimachinery/pkg/util/sets"
)

const (
	permissionRefTypeOfCodeAndParentID                                            = "code_and_parent_id"
	permissionRefTypeOfParentID                                                   = "parent_id"
	rolePermissionRefOfPermissionIDAndRoleIDAndOrganID                            = "permission_id_and_role_id_organ_id"
	resourceTranslateConfigRefOfGroupNameAndUniqueValueAndLanguageCodeAndProperty = "group_name_and_unique_value_and_language_code_and_property"
)

func permissionIDFunc(permission dao.Permission) string {
	return decimal.NewFromInt(permission.ID).String()
}

func rolePermissionIDFunc(rolePermission dao.RolePermission) string {
	return decimal.NewFromInt(rolePermission.ID).String()
}

func resourceTranslateConfigIDFunc(resourceTranslateConfig dao.ResourceTranslateConfig) string {
	return decimal.NewFromInt(resourceTranslateConfig.ID).String()
}

type PermissionPool interface {
	// IsRoleExist
	// 判断角色是否存在
	IsRoleExist(roleID int64) (bool, error)

	// ListAllPermissions
	// 获取所有的权限
	ListAllPermissions() ([]dao.Permission, error)

	// getPermissionByPermissionId
	// 根据PermissionID获取Permission
	getPermissionByPermissionId(id int64) (*dao.Permission, error)

	// GetPermissionByCode
	// 通过Code获取Permission
	GetPermissionByCode(code string) (*dao.Permission, error)

	// GetPermissionByCodeAndParentId
	// 根据code和parentID获取Permission
	GetPermissionByCodeAndParentId(code string, parentId int64) (*dao.Permission, error)

	// GetMaxPermissionID
	// 获取PermissionID的最大值
	GetMaxPermissionID() int64

	// MaxPermissionIDAutoIncrement
	// PermissionID最大值自增
	MaxPermissionIDAutoIncrement()

	// GetMaxRolePermissionID
	// 获取RolePermissionID的最大值
	GetMaxRolePermissionID() int64
	// MaxRolePermissionIDAutoIncrement
	// RolePermissionID最大值自增
	MaxRolePermissionIDAutoIncrement()
	// ApplyPermission
	// 应用 permission
	ApplyPermission(permission dao.Permission) error

	// GetRolePermissionByRoleIdAndPermissionIDWithOrganId
	// 通过RoleID 和 PermissionID获取RolePermission
	GetRolePermissionByRoleIdAndPermissionIDWithOrganId(roleId, permissionId int64, organId *int64) (*dao.RolePermission, error)

	// GetRolePermissionsByPermissionId
	// 通 PermissionID获取RolePermissions
	GetRolePermissionsByPermissionId(permissionId int64) ([]dao.RolePermission, error)

	// GetRolePermissionsByPermissionIdWithOrganId
	// 通 PermissionID获取RolePermissions
	GetRolePermissionsByPermissionIdWithOrganId(permissionId int64, organId *int64) ([]dao.RolePermission, error)

	// GetRolePermissionsByPermissionIdAndRoleIdsWithOrganId
	// 通过permissionId 和 limitRoleId(in 逻辑) 获取RolePermissions
	GetRolePermissionsByPermissionIdAndRoleIdsWithOrganId(permissionId int64, organId *int64, limitRoleId ...int64) ([]dao.RolePermission, error)

	// CreateRolePermission
	// 创建RolePermission
	CreateRolePermission(rolePermission dao.RolePermission) error

	// DeleteRolePermissions
	// 删除RolePermissions
	DeleteRolePermissions(permissions ...dao.RolePermission) error

	// GetUnDeletePermissionsByParentId
	// 通过ParentID获取未软删除的Permission
	GetUnDeletePermissionsByParentId(parenId int64) ([]dao.Permission, error)

	// Submit
	// 提交应用
	Submit() error

	GetResourceTranslateConfig(groupName, uniqueValue, languageCode, property string) (*dao.ResourceTranslateConfig, error)
	GetResourceTranslateConfigByGroupNameAndUniqueValue(groupName, uniqueValue string) ([]dao.ResourceTranslateConfig, error)
	GetResourceTranslateConfigByGroupNameAndUniqueValueAndLanguageCode(groupName, uniqueValue, languageCode string) ([]dao.ResourceTranslateConfig, error)

	SaveResourceTranslateConfig(config dao.ResourceTranslateConfig) error
	DeleteResourceTranslateConfig(configs []dao.ResourceTranslateConfig) error
}

func NewInMemoryPool(tx *gorm.DB) (PermissionPool, error) {
	// 查找数据库中的所有角色
	var daoRoles []dao.Role
	if err := tx.Model(dao.Role{}).Where("is_deleted = 0").Find(&daoRoles).Error; err != nil {
		return nil, err
	}
	var roleSet = sets.New[int64]()
	for _, role := range daoRoles {
		roleSet.Insert(role.ID)
	}

	// 查询数据库中permission 的最大ID 和 rolePermission 的最大ID
	var maxPermissionId sql.NullInt64
	var maxRolePermissionId sql.NullInt64

	// Get max permission id
	if err := database.AmpDB.Model(dao.Permission{}).Select("max(id)").Take(&maxPermissionId).Error; err != nil {
		return nil, err
	}

	// Get max permission id
	if err := database.AmpDB.Model(dao.RolePermission{}).Select("max(id)").Take(&maxRolePermissionId).Error; err != nil {
		return nil, err
	}

	// 查找数据库中的所有Permission
	var daoPermissions []dao.Permission
	if err := database.AmpDB.Find(&daoPermissions).Error; err != nil {
		return nil, err
	}
	var daoRefPermissions ObjectsRef[dao.Permission] = NewInMemoryObjectsRef(permissionIDFunc)
	for _, permission := range daoPermissions {
		permission := permission
		PermissionRefHandler(daoRefPermissions, permission)
	}

	// 查找数据库中的所有PermissionRole
	var daoRolePermissions []dao.RolePermission
	if err := database.AmpDB.Find(&daoRolePermissions).Error; err != nil {
		return nil, err
	}
	var daoRefRolePermissions ObjectsRef[dao.RolePermission] = NewInMemoryObjectsRef(rolePermissionIDFunc)
	for _, rolePermission := range daoRolePermissions {
		rolePermission := rolePermission
		RolePermissionRefHandler(daoRefRolePermissions, rolePermission)
	}
	caasSessionTx := database.CaasDB
	caasSessionTx = caasSessionTx.Begin(&sql.TxOptions{
		ReadOnly:  false,
		Isolation: sql.LevelReadUncommitted, // 采用读未提交的事物隔离级别
	})

	// 查找数据库中所有的resourceTranslateConfig
	var daoResourceTranslateConfigs []dao.ResourceTranslateConfig
	if err := caasSessionTx.Find(&daoResourceTranslateConfigs).Error; err != nil {
		return nil, err
	}
	var daoRefResourceTranslateConfigs ObjectsRef[dao.ResourceTranslateConfig] = NewInMemoryObjectsRef(resourceTranslateConfigIDFunc)
	for _, resourceTranslateConfig := range daoResourceTranslateConfigs {
		resourceTranslateConfig := resourceTranslateConfig
		ResourceTranslateConfigRefHandler(daoRefResourceTranslateConfigs, resourceTranslateConfig)
	}

	return &inMemoryPermissionPool{
		tx:                            tx,
		caasSessionTx:                 caasSessionTx,
		roleSet:                       roleSet,
		maxPermissionId:               maxPermissionId.Int64,
		maxRolePermissionId:           maxRolePermissionId.Int64,
		daoRefPermissions:             daoRefPermissions,
		daoRefRolePermissions:         daoRefRolePermissions,
		daoRefResourceTranslateConfig: daoRefResourceTranslateConfigs,
	}, nil

}

type inMemoryPermissionPool struct {
	tx                            *gorm.DB
	caasSessionTx                 *gorm.DB
	roleSet                       sets.Set[int64]
	maxPermissionId               int64
	maxRolePermissionId           int64
	daoRefPermissions             ObjectsRef[dao.Permission]
	daoRefRolePermissions         ObjectsRef[dao.RolePermission]
	daoRefResourceTranslateConfig ObjectsRef[dao.ResourceTranslateConfig]

	willApplyPermissions             []dao.Permission
	willApplyRolePermissions         []willApplyRolePermission
	willApplyResourceTranslateConfig []willApplyResourceTranslateConfig
}

type willApplyRolePermission struct {
	isCreate bool
	dao.RolePermission
}

type willApplyResourceTranslateConfig struct {
	isSave bool
	dao.ResourceTranslateConfig
}

// IsRoleExist
// 判断角色是否存在
func (pool *inMemoryPermissionPool) IsRoleExist(roleID int64) (bool, error) {
	has := pool.roleSet.Has(roleID)
	return has, nil
}

// ListAllPermissions
// 获取所有的权限
func (pool *inMemoryPermissionPool) ListAllPermissions() ([]dao.Permission, error) {
	var result []dao.Permission
	result = pool.daoRefPermissions.ListUnSorted()
	return result, nil
}

// getPermissionByPermissionId
// 根据PermissionID获取Permission
func (pool *inMemoryPermissionPool) getPermissionByPermissionId(id int64) (*dao.Permission, error) {
	permission := dao.Permission{}
	permission.ID = id
	permissionID := permissionIDFunc(permission)
	result, exist := pool.daoRefPermissions.FindById(permissionID)
	if !exist {
		return nil, nil
	}
	return &result, nil
}

// GetPermissionByCode
// 通过Code获取Permission
func (pool *inMemoryPermissionPool) GetPermissionByCode(code string) (*dao.Permission, error) {

	var refPermissions []dao.Permission = pool.daoRefPermissions.FindByRef(permissionRefTypeOfCodeAndParentID, code)
	refPermissionsNumbers := len(refPermissions)
	if refPermissionsNumbers == 0 {
		return nil, nil
	} else if refPermissionsNumbers == 1 {
		return &refPermissions[0], nil
	} else {
		return nil, fmt.Errorf("code of %v number > 1", code)
	}
}

// GetPermissionByCodeAndParentId
// 根据code和parentID获取Permission
func (pool *inMemoryPermissionPool) GetPermissionByCodeAndParentId(code string, parentId int64) (*dao.Permission, error) {

	var refPermissions []dao.Permission = pool.daoRefPermissions.FindByRef(permissionRefTypeOfCodeAndParentID, code, parentId)
	refPermissionsNumbers := len(refPermissions)
	if refPermissionsNumbers == 0 {
		return nil, nil
	} else if refPermissionsNumbers == 1 {
		return &refPermissions[0], nil
	} else {
		return nil, fmt.Errorf("code of %v and parent_id of %v number > 1", code, parentId)
	}
}

// GetMaxPermissionID
// 获取PermissionID的最大值
func (pool *inMemoryPermissionPool) GetMaxPermissionID() int64 {
	return pool.maxPermissionId
}

// MaxPermissionIDAutoIncrement
// PermissionID最大值自增
func (pool *inMemoryPermissionPool) MaxPermissionIDAutoIncrement() {
	pool.maxPermissionId = pool.maxPermissionId + 1
}

// GetMaxRolePermissionID
// 获取RolePermissionID的最大值
func (pool *inMemoryPermissionPool) GetMaxRolePermissionID() int64 {
	return pool.maxRolePermissionId
}

// MaxRolePermissionIDAutoIncrement
// RolePermissionID最大值自增
func (pool *inMemoryPermissionPool) MaxRolePermissionIDAutoIncrement() {
	pool.maxRolePermissionId = pool.maxRolePermissionId + 1
}

// ApplyPermission
// 应用 permission
func (pool *inMemoryPermissionPool) ApplyPermission(permission dao.Permission) error {
	pool.willApplyPermissions = append(pool.willApplyPermissions, permission)
	id := permissionIDFunc(permission)
	_, exist := pool.daoRefPermissions.FindById(id)
	if !exist {
		// if not exist put then
		PermissionRefHandler(pool.daoRefPermissions, permission)
	} else {
		// if exist delete and put then
		pool.daoRefPermissions.Remove(permission)
		PermissionRefHandler(pool.daoRefPermissions, permission)
	}
	return nil
}

// GetRolePermissionByRoleIdAndPermissionIDWithOrganId
// 通过RoleID 和 PermissionID获取RolePermission
func (pool *inMemoryPermissionPool) GetRolePermissionByRoleIdAndPermissionIDWithOrganId(roleId, permissionId int64, organId *int64) (*dao.RolePermission, error) {
	var refRolePermissions []dao.RolePermission = pool.daoRefRolePermissions.FindByRef(rolePermissionRefOfPermissionIDAndRoleIDAndOrganID, permissionId, roleId, DefaultInt64IndexValue(organId))
	refRolePermissionsNumbers := len(refRolePermissions)
	if refRolePermissionsNumbers == 0 {
		return nil, nil
	} else if refRolePermissionsNumbers == 1 {
		return &refRolePermissions[0], nil
	} else {
		return nil, fmt.Errorf("permission_id of %v and role_id of %v number > 1", permissionId, roleId)
	}
}

// GetRolePermissionsByPermissionId
// 通 PermissionID获取RolePermissions
func (pool *inMemoryPermissionPool) GetRolePermissionsByPermissionId(permissionId int64) ([]dao.RolePermission, error) {
	var rolePermissions []dao.RolePermission
	rolePermissions = pool.daoRefRolePermissions.FindByRef(rolePermissionRefOfPermissionIDAndRoleIDAndOrganID, permissionId)

	if rolePermissions == nil {
		rolePermissions = make([]dao.RolePermission, 0)
	}
	return rolePermissions, nil
}

// GetRolePermissionsByPermissionIdWithOrganId
// 通 PermissionID获取RolePermissions
func (pool *inMemoryPermissionPool) GetRolePermissionsByPermissionIdWithOrganId(permissionId int64, organId *int64) ([]dao.RolePermission, error) {
	rolePermissions, err := pool.GetRolePermissionsByPermissionId(permissionId)
	if err != nil {
		return nil, err
	}
	var result = make([]dao.RolePermission, 0, len(rolePermissions))
	for _, r := range result {
		r := r
		if Int64ValueEquals(r.OrganID, organId) {
			result = append(result, r)
		}
	}
	return result, nil
}

// GetRolePermissionsByPermissionIdAndRoleIdsWithOrganId
// 通过permissionId 和 limitRoleId(in 逻辑) 获取RolePermissions
func (pool *inMemoryPermissionPool) GetRolePermissionsByPermissionIdAndRoleIdsWithOrganId(permissionId int64, organId *int64, limitRoleId ...int64) ([]dao.RolePermission, error) {
	var limitRoleIdSet sets.Set[int64]
	limitRoleIdSet = sets.New(limitRoleId...)

	rolePermissionsByPermissionID, err := pool.GetRolePermissionsByPermissionIdWithOrganId(permissionId, organId)
	if err != nil {
		return nil, err
	}
	var rolePermissions = make([]dao.RolePermission, 0, len(rolePermissionsByPermissionID))
	for _, rolePermission := range rolePermissionsByPermissionID {
		rolePermission := rolePermission
		if limitRoleIdSet.Has(rolePermission.RoleID) {
			rolePermissions = append(rolePermissions, rolePermission)
		}
	}

	return rolePermissions, nil
}

// CreateRolePermission
// 创建RolePermission
func (pool *inMemoryPermissionPool) CreateRolePermission(rolePermission dao.RolePermission) error {
	RolePermissionRefHandler(pool.daoRefRolePermissions, rolePermission)
	pool.willApplyRolePermissions = append(pool.willApplyRolePermissions, willApplyRolePermission{
		isCreate:       true,
		RolePermission: rolePermission,
	})
	return nil
}

// DeleteRolePermissions
// 删除RolePermissions
func (pool *inMemoryPermissionPool) DeleteRolePermissions(rolePermissions ...dao.RolePermission) error {
	if len(rolePermissions) == 0 {
		return nil
	}
	for _, rolePermission := range rolePermissions {
		rolePermission := rolePermission
		pool.willApplyRolePermissions = append(pool.willApplyRolePermissions, willApplyRolePermission{
			isCreate:       false,
			RolePermission: rolePermission,
		})
		pool.daoRefRolePermissions.Remove(rolePermission)
	}
	return nil
}

// GetUnDeletePermissionsByParentId
// 通过ParentID获取未软删除的Permission
func (pool *inMemoryPermissionPool) GetUnDeletePermissionsByParentId(parenId int64) ([]dao.Permission, error) {
	var parentIdPermissions []dao.Permission
	parentIdPermissions = pool.daoRefPermissions.FindByRef(permissionRefTypeOfParentID, parenId)
	var rolePermission []dao.Permission
	for _, parentIdPermission := range parentIdPermissions {
		parentIdPermission := parentIdPermission
		if !parentIdPermission.IsDelete {
			rolePermission = append(rolePermission, parentIdPermission)
		}
	}

	if rolePermission == nil {
		rolePermission = make([]dao.Permission, 0)
	}
	return rolePermission, nil
}

// Submit
// 提交应用
func (pool *inMemoryPermissionPool) Submit() error {
	// apply permissions
	if len(pool.willApplyPermissions) != 0 {
		if err := pool.tx.Save(&pool.willApplyPermissions).Error; err != nil {
			return err
		}
	}

	// apply permission role
	if len(pool.willApplyRolePermissions) != 0 {
		handleFunc := func(isCreate bool, cache []dao.RolePermission, tx *gorm.DB) error {
			if len(cache) == 0 {
				return nil
			}
			var err error
			if isCreate {
				err = pool.tx.Create(&cache).Error
			} else {
				err = pool.tx.Delete(&cache).Error
			}
			return err
		}

		isCreate := pool.willApplyRolePermissions[0].isCreate
		var cache []dao.RolePermission
		for _, rolePermission := range pool.willApplyRolePermissions {
			rolePermission := rolePermission
			if rolePermission.isCreate == isCreate {
				cache = append(cache, rolePermission.RolePermission)
			} else {
				if err := handleFunc(isCreate, cache, pool.tx); err != nil {
					return err
				}
				isCreate = !isCreate
				cache = make([]dao.RolePermission, 0)
				cache = append(cache, rolePermission.RolePermission)
			}
		}
		if err := handleFunc(isCreate, cache, pool.tx); err != nil {
			return err
		}
	}

	// apply language
	if len(pool.willApplyResourceTranslateConfig) != 0 {
		handleFunc := func(isApply bool, cache []dao.ResourceTranslateConfig, tx *gorm.DB) error {
			if len(cache) == 0 {
				return nil
			}
			var err error
			if isApply {
				err = tx.Save(&cache).Error
			} else {
				err = tx.Delete(&cache).Error
			}
			return err
		}

		isApply := pool.willApplyResourceTranslateConfig[0].isSave
		var cache []dao.ResourceTranslateConfig
		for _, willRtc := range pool.willApplyResourceTranslateConfig {
			willRtc := willRtc
			if willRtc.isSave == isApply {
				cache = append(cache, willRtc.ResourceTranslateConfig)
			} else {
				if err := handleFunc(isApply, cache, pool.caasSessionTx); err != nil {
					return err
				}
				isApply = !isApply
				cache = make([]dao.ResourceTranslateConfig, 0)
				cache = append(cache, willRtc.ResourceTranslateConfig)
			}
		}
		if err := handleFunc(isApply, cache, pool.caasSessionTx); err != nil {
			return err
		}
	}

	return pool.caasSessionTx.Commit().Error
}

func (pool *inMemoryPermissionPool) GetResourceTranslateConfig(groupName, uniqueValue, languageCode, property string) (*dao.ResourceTranslateConfig, error) {
	var items []dao.ResourceTranslateConfig
	items = pool.daoRefResourceTranslateConfig.FindByRef(resourceTranslateConfigRefOfGroupNameAndUniqueValueAndLanguageCodeAndProperty, groupName, uniqueValue, languageCode, property)
	if len(items) == 0 {
		return nil, nil
	}
	return &items[0], nil
}

func (pool *inMemoryPermissionPool) GetResourceTranslateConfigByGroupNameAndUniqueValue(groupName, uniqueValue string) ([]dao.ResourceTranslateConfig, error) {
	var items []dao.ResourceTranslateConfig
	items = pool.daoRefResourceTranslateConfig.FindByRef(resourceTranslateConfigRefOfGroupNameAndUniqueValueAndLanguageCodeAndProperty, groupName, uniqueValue)
	if len(items) == 0 {
		return nil, nil
	}
	return items, nil
}

func (pool *inMemoryPermissionPool) GetResourceTranslateConfigByGroupNameAndUniqueValueAndLanguageCode(groupName, uniqueValue, languageCode string) ([]dao.ResourceTranslateConfig, error) {
	var items []dao.ResourceTranslateConfig
	items = pool.daoRefResourceTranslateConfig.FindByRef(resourceTranslateConfigRefOfGroupNameAndUniqueValueAndLanguageCodeAndProperty, groupName, uniqueValue, languageCode)
	if len(items) == 0 {
		return nil, nil
	}
	return items, nil
}

func (pool *inMemoryPermissionPool) SaveResourceTranslateConfig(config dao.ResourceTranslateConfig) error {
	pool.willApplyResourceTranslateConfig = append(pool.willApplyResourceTranslateConfig, willApplyResourceTranslateConfig{
		isSave:                  true,
		ResourceTranslateConfig: config,
	})
	id := resourceTranslateConfigIDFunc(config)
	_, exist := pool.daoRefResourceTranslateConfig.FindById(id)
	if !exist {
		// if not exist put then
		ResourceTranslateConfigRefHandler(pool.daoRefResourceTranslateConfig, config)
	} else {
		// if exist delete and put then
		pool.daoRefResourceTranslateConfig.Remove(config)
		ResourceTranslateConfigRefHandler(pool.daoRefResourceTranslateConfig, config)
	}
	return nil
}

func (pool *inMemoryPermissionPool) DeleteResourceTranslateConfig(configs []dao.ResourceTranslateConfig) error {
	for _, config := range configs {
		config := config
		pool.willApplyResourceTranslateConfig = append(pool.willApplyResourceTranslateConfig, willApplyResourceTranslateConfig{
			isSave:                  false,
			ResourceTranslateConfig: config,
		})
		id := resourceTranslateConfigIDFunc(config)
		_, exist := pool.daoRefResourceTranslateConfig.FindById(id)
		if exist {
			pool.daoRefResourceTranslateConfig.Remove(config)
		}
	}
	return nil
}

func PermissionRefHandler(daoRefPermissions ObjectsRef[dao.Permission], permission dao.Permission) {
	daoRefPermissions.Ref(permissionRefTypeOfCodeAndParentID, permission, permission.Code, permission.ParentId)
	daoRefPermissions.Ref(permissionRefTypeOfParentID, permission, permission.ParentId)
}

func RolePermissionRefHandler(daoRefRolePermissions ObjectsRef[dao.RolePermission], rolePermission dao.RolePermission) {
	daoRefRolePermissions.Ref(rolePermissionRefOfPermissionIDAndRoleIDAndOrganID, rolePermission, rolePermission.PermissionID, rolePermission.RoleID, DefaultInt64IndexValue(rolePermission.OrganID))
}

func ResourceTranslateConfigRefHandler(daoRefResourceTranslateConfig ObjectsRef[dao.ResourceTranslateConfig], resourceTranslateConfig dao.ResourceTranslateConfig) {
	daoRefResourceTranslateConfig.Ref(resourceTranslateConfigRefOfGroupNameAndUniqueValueAndLanguageCodeAndProperty, resourceTranslateConfig, resourceTranslateConfig.GroupName, resourceTranslateConfig.UniqueValue, resourceTranslateConfig.LanguageCode, resourceTranslateConfig.Property)
}

func DefaultInt64IndexValue(i *int64) int64 {
	if i == nil {
		return -1
	}
	return *i
}

func Int64ValueEquals(i *int64, i1 *int64) bool {
	if i == nil && i1 == nil {
		return true
	}
	if i != nil && i1 != nil {
		return *i == *i1
	}
	return false
}
