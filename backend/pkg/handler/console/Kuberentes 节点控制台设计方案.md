# Kuberentes 节点控制台设计方案

## 一、背景

在Kubernetes集群中，用户往往需要以交互方式登录节点（node）进行调试和维护，但由于集群节点通常不开放SSH登录，需通过容器方式间接访问。常见方案包括：

*   **方案一：特权Pod + 挂载主机文件系统 +** `**chroot**`。在目标节点上创建一个具有 `**privileged: true**` 权限的Pod，将主机根目录挂载到容器（如挂载到`**/host**`），然后在容器内执行 `**chroot /host /bin/bash**` 获取节点环境的shell。该方案可以获得节点根权限（`**root**`），例如使用如下命令即可进入节点环境：
    

```plain
kubectl exec -it debug-node -- chroot /host /bin/bash
```

(此处假设 `**debug-node**` 是已启动且挂载了节点根目录的特权Pod)。优点是简单直接、支持所有K8s版本；缺点是安全风险高（需要特权容器和HostPath），容易绕过原生安全机制。

*   **方案二：使用** `**nsenter**` **进入节点命名空间**。借助 `**hostPID=true**` 等配置，在容器内使用 `**nsenter**` 命令进入宿主节点的各个命名空间（mount、uts、ipc、net、pid等）。例如，创建一个特权Pod并在启动时运行：
    

```yaml
spec:
    nodeName: 目标节点名
    hostPID: true
    hostNetwork: true
    containers:
        - name: nsenter-shell
          image: alpine
          command:
              - nsenter
              - "--target"
              - "1"
              - "--mount"
              - "--uts"
              - "--ipc"
              - "--net"
              - "--pid"
              - "--"
              - /bin/sh
          securityContext:
              privileged: true
          stdin: true
          tty: true
    tolerations:
        - operator: Exists
```

该Pod启动后会直接进入宿主机的PID为1的进程命名空间，从而获得与节点系统相同的环境。使用`**nsentr**`方法的优势是可以在容器中执行宿主机上的工具和特权操作（如完整的网络抓包、系统管理命令）；缺点是同样需要特权容器，且需要在镜像中预装`**nsenter**`工具。

*   **方案三：**`**kubectl debug**`**命令（本质为特权Pod+挂载+PID共享的封装）**。从Kubernetes 1.18+版本开始，`**kubectl debug node/<node>**`命令可自动创建一个调试Pod，以交互方式登录指定节点。此调试Pod会运行在目标节点上，挂载该节点的根文件系统到`**/host**`，并与节点共享Host PID、Network、IPC命名空间[kubernetes.io](https://kubernetes.io/docs/reference/kubectl/generated/kubectl_debug/#:~:text=,image%3Dbusybox)。比如：
    

```plain
kubectl debug node/mynode -it --image=ubuntu
```

该命令创建的Pod会自动命名（如`**node-debugger-mynode-xxx**`），并打开交互shell `**root@mynode:/#**`[kubernetes.io](https://kubernetes.io/docs/tasks/debug/debug-cluster/kubectl-node-debug/#:~:text=Debugging%20a%20Node%20using%20,debug%20node)。需要注意的是，该调试Pod**以root身份运行但默认并非真正的privileged**容器，执行`**chroot /host**`等操作可能会失败 [github.com](https://github.com/kubernetes/website/issues/35170#:~:text=%3E%20,Node%20using%20kubectl%20debug%2C%20run)。优点是使用Kubernetes原生工具、流程简便安全控制更好；缺点是功能略受限制（如受限于宿主机镜像命名空间）。

```yaml
apiVersion: v1
kind: Pod
metadata:
  creationTimestamp: "2025-06-04T02:07:39Z"
  name: node-debugger-cluster-187-node0003.*************-9bhk6
  namespace: caas-system
  resourceVersion: "*********"
  uid: cff088e4-0ba3-41b9-9262-70f7c7cdcaa2
spec:
  containers:
  - image: k8s-deploy/busybox:1.27.kube
    imagePullPolicy: IfNotPresent
    name: debugger
    resources: {}
    stdin: true
    terminationMessagePath: /dev/termination-log
    terminationMessagePolicy: File
    tty: true
    volumeMounts:
    - mountPath: /host
      name: host-root
    - mountPath: /var/run/secrets/kubernetes.io/serviceaccount
      name: kube-api-access-hsqqq
      readOnly: true
  dnsPolicy: ClusterFirst
  enableServiceLinks: true
  hostIPC: true
  hostNetwork: true
  hostPID: true
  nodeName: cluster-187-node0003.*************
  nodeSelector:
    node-role.kubernetes.io/system: ""
    skyview/nodepool: system
  preemptionPolicy: PreemptLowerPriority
  priority: 0
  restartPolicy: Never
  schedulerName: default-scheduler
  securityContext: {}
  serviceAccount: default
  serviceAccountName: default
  terminationGracePeriodSeconds: 30
  tolerations:
  - operator: Exists
  - effect: NoSchedule
    key: skyview/nodepool-taint
    operator: Equal
    value: system
  volumes:
  - hostPath:
      path: /
      type: ""
    name: host-root
  - name: kube-api-access-hsqqq
    projected:
      defaultMode: 420
      sources:
      - serviceAccountToken:
          expirationSeconds: 3607
          path: token
      - configMap:
          items:
          - key: ca.crt
            path: ca.crt
          name: kube-root-ca.crt
      - downwardAPI:
          items:
          - fieldRef:
              apiVersion: v1
              fieldPath: metadata.namespace
            path: namespace
status:
  conditions:
  - lastProbeTime: null
    lastTransitionTime: "2025-06-04T02:07:39Z"
    reason: PodCompleted
    status: "True"
    type: Initialized
  - lastProbeTime: null
    lastTransitionTime: "2025-06-04T02:13:05Z"
    reason: PodCompleted
    status: "False"
    type: Ready
  - lastProbeTime: null
    lastTransitionTime: "2025-06-04T02:13:05Z"
    reason: PodCompleted
    status: "False"
    type: ContainersReady
  - lastProbeTime: null
    lastTransitionTime: "2025-06-04T02:07:39Z"
    status: "True"
    type: PodScheduled
  containerStatuses:
  - containerID: containerd://18ba83881cd1c9c2d840ee6b60083764ec19779f9673311ea9a908cb22d673a3
    image: docker.io/k8s-deploy/busybox:1.27.kube
    imageID: docker.io/k8s-deploy/busybox@sha256:81dfa66327157e19bd870b87a207d66c6efbf79c3018b1caa27c640f3550e50c
    lastState: {}
    name: debugger
    ready: false
    restartCount: 0
    started: false
    state:
      terminated:
        containerID: containerd://18ba83881cd1c9c2d840ee6b60083764ec19779f9673311ea9a908cb22d673a3
        exitCode: 0
        finishedAt: "2025-06-04T02:13:05Z"
        reason: Completed
        startedAt: "2025-06-04T02:07:42Z"
  hostIP: *************
  phase: Succeeded
  podIP: *************
  podIPs:
  - ip: *************
  qosClass: BestEffort
  startTime: "2025-06-04T02:07:39Z"
```

综上，为满足安全与易用性的平衡，通常推荐使用`**kubectl debug**`的逻辑方式+ 挂载根目录的方案，它结合了挂载主机文件系统的便利和Kubernetes原生命令的管理优势。后续设计将基于此方案构建节点控制台访问机制。

*   **方案四：基于传统方式提供ssh登录** 在某些极端场景或更贴近传统运维方式下，仍有用户希望使用原生 SSH 登录节点环境。为在 Kubernetes 中兼容该需求，可通过部署一个包含 SSH 客户端的 Pod，并借助平台维护的 SSH 密钥或中间网关，实现从 Pod 内部 SSH 到目标节点。
    

## 二、需求概述

*   **需求描述**：平台需要提供一种统一、可控的方式，让用户能够以交互方式登录Kubernetes节点进行运维或调试工作。用户点击节点后，能够在浏览器或终端获得类似SSH的shell访问。
    
*   **背景**：直接SSH登录节点可能不被允许或无法管理；采用容器调试可充分利用Kubernetes特性，简化用户操作。
    
*   **目标**：设计一个**基于**`**kubectl debug**`**的调试会话机制**，结合平台权限体系和自动回收，安全高效地实现节点访问。
    
*   **解决的问题**：解决当前因手动方式复杂、权限难管控和兼容性差等问题，提供标准化的接入流程和自动化运维支持。
    

## 三、用例描述

典型使用流程如下：

1.  **发起请求**：租户用户在平台控制台或命令行中选择目标节点（如`**node01**`）并发起“打开节点控制台”请求。
    
2.  **创建会话**：平台API接收请求，在后台生成一个**NodeConsoleSession**（调试会话）的pod，指定节点名称、使用镜像、超时时间等信息。
    
3.  **调度Pod**：监听到对应的创建pod请求后，在对应节点上创建一个调试Pod（使用HostPID/HostNetwork并挂载`**/host**`根目录），使其进入`**Pending**`状态。
    
4.  **启动Shell**：当Pod就绪并运行后，平台自动执行类似`**kubectl attach**`操作将用户终端与Pod的交互shell相连，用户获得节点的root环境（节点根文件系统挂载在`**/host**`）
    
5.  **交互调试**：用户在Shell中执行需要的诊断或维护命令。此时所有操作都在Pod内执行，但作用于节点环境（例如查看`**/host/var/log**`日志、运行网络工具等。
    
6.  **结束会话**：用户退出Shell或会话超时后，控制器检测到会话结束事件，自动删除调试Pod并更新NodeConsoleSession状态为“已结束”，完成资源回收。
    

**用例流程示意：** 用户→调试API→创建调试Pod→用户Attach Shell→用户操作节点→用户退出→删除Pod。

```plantuml
@startuml
title 调试流程示意图

actor 用户
participant "API Server" as API
participant "调试 Pod" as Pod
participant "Node Shell" as Shell

== 发起调试 ==
用户 -> API : 调用调试 API

== 创建调试环境 ==
API -> Pod : 创建调试 Pod

== 用户连接 ==
用户 -> Pod : Attach Shell
Pod -> Shell : 打开终端会话

== 用户操作 ==
用户 -> Shell : 执行节点调试操作

== 退出与清理 ==
用户 -> Shell : 退出终端
Shell -> Pod : 终端关闭
CRD -> Pod : 删除调试 Pod

@enduml

```

## 四、详细设计

### 1. 设计方案

*   **方案一：特权Pod + 挂载 + chroot**
    

*   _安全性_：风险最高。需要设置`**privileged: true**`并挂载整个宿主文件系统[taozhi.medium.com](https://taozhi.medium.com/the-amazing-chroot-making-simply-ssh-to-each-nodes-in-kubernetes-a3448a665c95#:~:text=We%20mount%20the%20node%20entire,we%20get%20the%20root%20privilege)。该Pod一旦被攻破，可完全控制节点。
    
*   _兼容性_：兼容所有K8s版本，无需额外功能支持。
    
*   _易用性_：手动操作量大。首先需要提前部署Pod并保持运行，然后通过`**kubectl exec**` + `**chroot**`进入节点环境[taozhi.medium.com](https://taozhi.medium.com/the-amazing-chroot-making-simply-ssh-to-each-nodes-in-kubernetes-a3448a665c95#:~:text=%60kubectl%20exec%20,chroot%20%2Fhost%20%2Fbin%2Fbash)。每次访问节点都需先找到对应Pod。
    
*   _平台扩展_：可通过DaemonSet在每个节点预部署此类Pod，方便快速访问。但维护和清理也比较麻烦。
    

*   **方案二：nsenter**
    

*   _安全性_：同样需要`**privileged=true**`、`**hostPID=true**`等配置，风险与方案一相近。
    
*   _兼容性_：基本兼容所有集群。需要调试镜像内包含`**nsenter**`工具。
    
*   _易用性_：通过脚本或命令行直接进入节点，无需后续`**exec**`或`**chroot**`步骤。使用`**nsenter**`可进入节点PID1的多个命名空间，见上述示例[qingwave.github.io](https://qingwave.github.io/k8s-debug-nsenter/#:~:text=%E5%8E%9F%E7%90%86%E6%98%AF%E9%80%9A%E8%BF%87%E5%85%B1%E4%BA%AB%20pid%20%E6%96%B9%E5%BC%8F%20,%E7%AD%89%20namespace%EF%BC%8C%E4%BB%8E%E8%80%8C%E5%8F%AF%E4%BB%A5%E8%8E%B7%E5%8F%96%E7%B1%BB%E4%BC%BC%E5%AE%BF%E4%B8%BB%E6%9C%BA%E7%9A%84%20shell%E3%80%82)。但需要记住`**nsenter**`命令参数。
    
*   _平台扩展_：同样可部署为DaemonSet（每节点一个Pod预载`**nsenter**`），随时登入使用[qingwave.github.io](https://qingwave.github.io/k8s-debug-nsenter/#:~:text=%E5%8E%9F%E7%90%86%E6%98%AF%E9%80%9A%E8%BF%87%E5%85%B1%E4%BA%AB%20pid%20%E6%96%B9%E5%BC%8F%20,%E7%AD%89%20namespace%EF%BC%8C%E4%BB%8E%E8%80%8C%E5%8F%AF%E4%BB%A5%E8%8E%B7%E5%8F%96%E7%B1%BB%E4%BC%BC%E5%AE%BF%E4%B8%BB%E6%9C%BA%E7%9A%84%20shell%E3%80%82)。
    

*   **方案三：kubectl debug + 挂载根目录**
    

*   _安全性_：较安全。调试Pod以root身份运行，但默认未设置`**privileged**`，只能共享主机的PID/网络/IPC命名空间。无法直接执行`**chroot /host**`等敏感操作。相较方案一/二，不容易被用于持久化攻击。
    
*   _兼容性_：要求Kubernetes v1.18+，而需要支持1.21-1.27版本的集群。使用`**kubectl debug**`命令需要对应版本的kubectl和集群支持该功能，借助kubectl 依赖能简单实现。
    
*   _易用性_：集成到kubectl中，用户界面友好，只需一条命令即可交互式登录节点。无需手动管理复杂的Pod配置。
    

*   **方案四：基于传统方式提供ssh登录** 
    

*   安全性: 基于ssh方案，较为安全。
    
*   兼容性: 兼容所有的kubernetes版本
    

**方案评估：** 方案一和二虽然功能强大，但需要开启特权容器并挂载宿主文件系统，方案二比方案一提供更高的权限，且安全风险极高。而`**kubectl debug**`方案由Kubernetes官方支持，默认环境下限制较多但足够常规调试使用；同时简化了用户操作，因此最终选择“**参考kubectl debug结合Pod挂载根目录**”的组合方案。

相关代码期望直接复用。

[https://github.com/kubernetes/kubectl/blob/f125772/pkg/cmd/debug/debug.go#L567](https://github.com/kubernetes/kubectl/blob/f125772/pkg/cmd/debug/debug.go#L567)

**资源回收策略：** 为避免调试Pod长期占用资源，应设计自动回收机制：可以在控制器中监测Session活跃状态或超时，在Pod空闲（无交互或进程结束）时自动删除Pod。  
可选方案包括：

*   _控制器自动回收_：实现一个专门的控制器周期性扫描所有调试会话，如检测Pod是否终止或长时间无连接，自动删除对应Pod并更新会话状态。
    
*   _脚本触发式_：使用定时任务或脚本，通过Kubernetes API删除超过TTL的调试Pod。
    
*   _进程活跃度判断_：监测调试Pod的主进程，如无交互事件或进程退出后立即触发Pod删除。  
    最终方案可采用CRD+Controller模式：定义调试会话资源，控制器在会话完成后（如进程结束或用户退出）自动清理Pod，并可对超时会话执行强制回收。
    

### 2. 详细设计

#### debug控制台

*   **实现思路概述**：用户通过平台接口创建在对应的节点上启动一个一个调试Pod（采用与`**kubectl debug**`类似的配置：HostPID/HostNetwork共享、挂载主机根目录，并运行用户指定的镜像及命令）。
    
*   **组件**：
    

*   **API服务**：提供 `**/apis/v1**` 前缀的RESTful接口，处理用户请求，创建/删除`**node-console-pod**`资源。
    
*   **Kubernetes集群**：实际运行调试Pod的环境，需要允许HostPath挂载和HostPID共享。
    
*   **用户终端/前端**：通过CLI或Web终端与调试Pod交互。
    

**部署结构：** API服务可集成于平台中，平台作为Kubernetes集群中的一个组件部署（通常以Deployment形式运行），具有权限管理和Pod资源的ClusterRole绑定

*   **数据结构（pod定义示例）**：
    

该配置通过 `kubectl debug`创建的pod示例获取。

```yaml
apiVersion: v1
kind: Pod
metadata:
  generateName: node-debugger-cluster-187-node0003.*************
spec:
  containers:
  - image: k8s-deploy/busybox:1.27.kube
    imagePullPolicy: IfNotPresent
    name: debugger
    resources: {}
    stdin: true
    tty: true
    # 安全上下文如无必要没必要开
    # securityContext:
    #   privileged: true 
    volumeMounts:
    - mountPath: /host
      name: host-root
  # 主机IPC
  hostIPC: true
  # 主机网络
  hostNetwork: true
  # 主机pid
  hostPID: true
  nodeName: cluster-187-node0003.*************
  nodeSelector:
    kubernetes.io/hostname: cluster-187-node0003.*************
  restartPolicy: Never
  tolerations:
  - operator: Exists
  volumes:
  - hostPath:
      path: /
      type: ""
    name: host-root
```

#### ssh 控制台

*   **实现思路概述**：用户通过平台接口创建在对应的节点上启动一个执行传递ssh命令的pod 到对应的节点，同时提供免密密钥挂载的形式
    
*   **实现方式**
    

1.  用户通过平台发起调试请求。
    
2.  平台创建一个包含 SSH 客户端的调试 Pod。
    
3.  该 Pod 挂载平台维护的密钥（或从平台 API 下发一次性 token/secret）。
    
4.  Pod 启动后自动或手动 SSH 登录目标节点（通过内网地址连接）。
    
5.  用户通过 attach/websocket 与该 SSH shell 交互。
    

**示例podYAML**

```yaml
apiVersion: v1
kind: Pod
metadata:
  generateName: node-ssh-session-
spec:
  containers:
    - name: ssh-client
      image: alpine:3.18
      command: ["/bin/sh", "-c"]
      args:
        - |
          apk add --no-cache openssh && \
          ssh -o StrictHostKeyChecking=no root@*************
      stdin: true
      tty: true
      volumeMounts:
        - name: ssh-key
          mountPath: /root/.ssh
  restartPolicy: Never
  volumes:
    - name: ssh-key
      secret:
        secretName: ssh-private-key
```

### 3. 接口设计

放在`olympus-portal`中

所有对外接口统一使用`**/apis/v1**`前缀，遵循RESTful风格并返回JSON。示例如下：

*   **POST /apis/v1/clusters/{cluster}/nodeconsoles** – 创建节点控制台会话
    

*   Path 参数 `**cluster**` 集群名称
    
*   **请求参数**（JSON body）:
    

```json
{
  // 节点名称
  "nodeName": "node01",
  // 模式debug 和传统ssh模式
  "mode": "debug|ssh",
  // sh bash
  "shell": "sh",
  // 是否开启特权模式
  "privileged": fasle,
}
```

*   **返回示例**:
    

```json
{
  "podName": "debugger",
  "nodeName": "node01",
  "status": "Pending"
}
```

*   **说明**: 根据请求参数生成`**NodeConsoleSession**`资源，返回唯一的`**sessionId**`和初始状态。
    

*   **GET /apis/v1/clusters/{cluster}/nodeconsoles/{podName}** – 查询会话状态
    

*   **请求参数**: Path 参数 `**cluster**` 集群名称 `**podName**`（会话ID）。
    
*   **返回示例**:
    

```json
{
  "nodeName": "node01",
  "status": "Running",
  "podName": "node-console-xyz",
  "startTime": "2025-06-03T19:31:28Z"
}
```

*   **说明**: 返回指定会话的当前状态、关联的Pod名称及启动时间等信息。
    

*   **DELETE /apis/v1/clusters/{cluster}/nodeconsoles/{podName}**  – 结束会话
    

*   **请求参数**: Path 参数`**cluster**` 集群名称  `**podName**`。
    
*   **返回示例**:
    

```json
{
  "podName": "xxx-pod",
  "status": "Terminated"
}
```

*   **说明**: 平台接收到此请求后标记会话结束，并删除对应调试Pod，实现资源回收。
    

*   **GET /apis/v1/clusters/{cluster}/nodeconsoles/{podName}/session/ws**
    

websocket

*   **请求参数**: Path 参数`**cluster**` 集群名称  `**podName**`。
    
*   **请求参数(JSON body)**
    

```json
{
  // 输入
  "type": "stdin",
  // 返回的文本
  "data": "ls -al\n"
}
```

*   **返回示例**:
    

```json
{
  // 输出
  "type": "stdout",
  // 返回的文本
  "data": "total 12\ndrwxr-xr-x ..."
}

```

*   **说明**: 平台通过此接口通过前端和控制台进行交互。
    

## 五、权限设计

### 审计

*   **审计控制**：集成平台审计日志，将API调用和会话创建/删除操作记录到审计系统，确保可追溯每一次调试访问。
    

TODO

### 安全

由于Kubernetes v1.21起已废弃 **PodSecurityPolicy（PSP）**，并在v1.25中移除[kubernetes.io](https://kubernetes.io/docs/concepts/security/pod-security-policy/#:~:text=PodSecurityPolicy%20was%20deprecated%20in%20Kubernetes,25)，新的 **Pod Security Admission（PSA）** 机制从v1.25开始才可用。目前平台需兼容1.21至1.27多个版本，为避免引入不同版本的安全策略不一致，暂不依赖PSP/PSA。相应地，平台采取自有策略来确保安全性：

*   **命名空间隔离**：将调试会话资源和调试Pod限定在独立的调试命名空间（或项目命名空间）中，只允许经过授权的租户/用户在其命名空间创建调试会话。
    
*   **调试镜像白名单**：仅允许使用经过审核的调试镜像（例如官方提供的带工具集的基础镜像），防止用户使用未知或恶意镜像启动容器。
    
*   **最小权限原则**：调试Pod默认不设为privileged，并仅共享必要的命名空间。通过设定安全上下文（`**securityContext**`）限制Pod行为。
    
*   **自动销毁机制**：及时销毁不再使用的调试Pod，避免闲置Pod被滥用。例如可检测会话结束后自动删除，或对长期未活动会话进行回收。
    
*   **审计和监控**：记录调试会话的创建、操作和销毁日志，便于审计和问题追踪。
    

综上，通过命名空间、角色控制、镜像管控和自动销毁等措施，在不使用PSP/PSA的情况下构建安全控制机制，满足多版本兼容性需求。

## 六、外部依赖

*   **容器运行时**：需使用支持`**hostPID**`、`**hostNetwork**`和`**HostPath**`卷的容器运行时（如containerd、CRI-O或旧版Docker）。节点必须允许启动特权容器（`**securityContext.privileged: true**`）和挂载主机文件系统以支持调试场景。
    
*   **镜像仓库**：平台需准备包含调试工具的基础镜像仓库（如预装`**bash**`、`**nsenter**`、网络调试工具的Ubuntu/Alpine镜像），并保证各节点可拉取这些镜像。
    
*   **kubectl和Kubernetes版本**：`**kubectl debug**`命令需要kubectl客户端版本≥1.18，集群Kubernetes版本最低为1.18（本设计支持1.21–1.27）。在使用`**kubectl debug node**`时，调试容器将运行在宿主节点的命名空间内，并将节点文件系统挂载到容器的`**/host**`[kubernetes.io](https://kubernetes.io/docs/reference/kubectl/generated/kubectl_debug/#:~:text=,image%3Dbusybox)。
    
*   **其他组件**：如果集群中启用了Pod安全策略(PSP)或PodSecurityAdmission（在1.25+），需要在调试Pod所属命名空间放宽相应策略（例如允许`**privileged: true**`、`**hostPath**`卷）。此外，容器日志和监控组件（如Prometheus Node Exporter）也可以作为辅助工具查看节点状态，但不是必需条件。
    

**关键配置示例**：

*   调试Pod示例（由控制器生成）：
    

```yaml
apiVersion: v1
kind: Pod
metadata:
  name: node-console-xyz
  namespace: tenant-a
spec:
  nodeName: node01
  hostPID: true
  hostNetwork: true
  hostIPC: true
  containers:
    - name: debugger
      image: ubuntu:20.04
      securityContext:
        privileged: false
      stdin: true
      tty: true
      command: ["/bin/bash", "-c", "--"]
      args: ["sleep 3600"]
      volumeMounts:
        - mountPath: /host
          name: host-root
  volumes:
    - name: host-root
      hostPath:
        path: /
        type: ""
  tolerations:
    - operator: Exists
```

_说明：_ 上述Pod将节点`**node01**`的根目录挂载到容器`**/host**`，进程共享主机PID/IPC/网络，实现登录环境与主机基本相同。

**参考资料：** Kubernetes官方文档和社区经验总结

1.  [https://kubernetes.io/zh-cn/docs/reference/kubectl/generated/kubectl\_debug/](https://kubernetes.io/zh-cn/docs/reference/kubectl/generated/kubectl_debug/)
    
2.  [https://github.com/kubernetes/website/issues/35170](https://github.com/kubernetes/website/issues/35170)
    
3.  [https://qingwave.github.io/k8s-debug-nsenter/#:~:text=%E5%8E%9F%E7%90%86%E6%98%AF%E9%80%9A%E8%BF%87%E5%85%B1%E4%BA%AB%20pid%20%E6%96%B9%E5%BC%8F%20,%E7%AD%89%20namespace%EF%BC%8C%E4%BB%8E%E8%80%8C%E5%8F%AF%E4%BB%A5%E8%8E%B7%E5%8F%96%E7%B1%BB%E4%BC%BC%E5%AE%BF%E4%B8%BB%E6%9C%BA%E7%9A%84%20shell%E3%80%82](https://qingwave.github.io/k8s-debug-nsenter/#:~:text=%E5%8E%9F%E7%90%86%E6%98%AF%E9%80%9A%E8%BF%87%E5%85%B1%E4%BA%AB%20pid%20%E6%96%B9%E5%BC%8F%20,%E7%AD%89%20namespace%EF%BC%8C%E4%BB%8E%E8%80%8C%E5%8F%AF%E4%BB%A5%E8%8E%B7%E5%8F%96%E7%B1%BB%E4%BC%BC%E5%AE%BF%E4%B8%BB%E6%9C%BA%E7%9A%84%20shell%E3%80%82)
    
4.  [https://taozhi.medium.com/the-amazing-chroot-making-simply-ssh-to-each-nodes-in-kubernetes-a3448a665c95#:~:text=We%20mount%20the%20node%20entire,we%20get%20the%20root%20privilege](https://taozhi.medium.com/the-amazing-chroot-making-simply-ssh-to-each-nodes-in-kubernetes-a3448a665c95#:~:text=We%20mount%20the%20node%20entire,we%20get%20the%20root%20privilege)
    
5.  [https://kubernetes.io/docs/concepts/security/pod-security-policy/#:~:text=PodSecurityPolicy%20was%20deprecated%20in%20Kubernetes,25](https://kubernetes.io/docs/concepts/security/pod-security-policy/#:~:text=PodSecurityPolicy%20was%20deprecated%20in%20Kubernetes,25)
    
6.  [https://kubernetes.io/docs/concepts/security/pod-security-admission/](https://kubernetes.io/docs/concepts/security/pod-security-admission/)
    

**引用**

[The amazing chroot making simply ssh to each nodes in kubernetes | by zhi tao | Mediumhttps://taozhi.medium.com/the-amazing-chroot-making-simply-ssh-to-each-nodes-in-kubernetes-a3448a665c95](https://taozhi.medium.com/the-amazing-chroot-making-simply-ssh-to-each-nodes-in-kubernetes-a3448a665c95#:~:text=We%20mount%20the%20node%20entire,we%20get%20the%20root%20privilege)[The amazing chroot making simply ssh to each nodes in kubernetes | by zhi tao | Mediumhttps://taozhi.medium.com/the-amazing-chroot-making-simply-ssh-to-each-nodes-in-kubernetes-a3448a665c95](https://taozhi.medium.com/the-amazing-chroot-making-simply-ssh-to-each-nodes-in-kubernetes-a3448a665c95#:~:text=%60kubectl%20exec%20,chroot%20%2Fhost%20%2Fbin%2Fbash)[Kubernetes调试利器Nsenterhttps://qingwave.github.io/k8s-debug-nsenter/](https://qingwave.github.io/k8s-debug-nsenter/#:~:text=%E5%8E%9F%E7%90%86%E6%98%AF%E9%80%9A%E8%BF%87%E5%85%B1%E4%BA%AB%20pid%20%E6%96%B9%E5%BC%8F%20,%E7%AD%89%20namespace%EF%BC%8C%E4%BB%8E%E8%80%8C%E5%8F%AF%E4%BB%A5%E8%8E%B7%E5%8F%96%E7%B1%BB%E4%BC%BC%E5%AE%BF%E4%B8%BB%E6%9C%BA%E7%9A%84%20shell%E3%80%82)[kubectl debug | Kuberneteshttps://kubernetes.io/docs/reference/kubectl/generated/kubectl\_debug/](https://kubernetes.io/docs/reference/kubectl/generated/kubectl_debug/#:~:text=,image%3Dbusybox)[Debugging Kubernetes Nodes With Kubectl | Kuberneteshttps://kubernetes.io/docs/tasks/debug/debug-cluster/kubectl-node-debug/](https://kubernetes.io/docs/tasks/debug/debug-cluster/kubectl-node-debug/#:~:text=Debugging%20a%20Node%20using%20,debug%20node)[Clarify documented privileges of kubectl debug node · Issue #35170 · kubernetes/website · GitHubhttps://github.com/kubernetes/website/issues/35170](https://github.com/kubernetes/website/issues/35170#:~:text=%3E%20,Node%20using%20kubectl%20debug%2C%20run)[Pod Security Policies | Kuberneteshttps://kubernetes.io/docs/concepts/security/pod-security-policy/](https://kubernetes.io/docs/concepts/security/pod-security-policy/#:~:text=PodSecurityPolicy%20was%20deprecated%20in%20Kubernetes,25)