# 节点控制台模块

## 概述

节点控制台模块提供了在Kubernetes集群中访问节点shell的功能，支持两种模式：
- **Debug模式**：使用特权容器直接访问节点文件系统
- **SSH模式**：通过SSH客户端连接到节点

## 主要改进

### 1. 移除kubectl命令依赖

**之前的问题**：
- 使用`exec.Command("kubectl", args...)`调用外部kubectl命令
- 依赖系统安装kubectl
- 无法准确解析命令输出和错误
- Pod名称生成不准确

**现在的解决方案**：
- 直接使用Kubernetes客户端库创建Pod
- 使用`k8s.io/client-go/kubernetes`包
- 准确的错误处理和状态管理
- 动态生成唯一的Pod名称

### 2. 支持多种访问模式

#### Debug模式
```yaml
# 创建的Pod配置
apiVersion: v1
kind: Pod
metadata:
  name: node-debugger-{nodeName}-{random}
  namespace: default
spec:
  nodeName: {nodeName}
  hostPID: true
  hostNetwork: true
  hostIPC: true
  containers:
  - name: debugger
    image: busybox:latest
    command: ["/bin/sh"]
    stdin: true
    tty: true
    securityContext:
      privileged: {privileged}
    volumeMounts:
    - name: host-root
      mountPath: /host
  volumes:
  - name: host-root
    hostPath:
      path: /
  restartPolicy: Never
```

#### SSH模式
```yaml
# 创建的Pod配置
apiVersion: v1
kind: Pod
metadata:
  name: node-ssh-session-{nodeName}-{random}
  namespace: default
spec:
  nodeName: {nodeName}
  containers:
  - name: ssh-client
    image: alpine:3.18
    command: ["/bin/sh", "-c"]
    args:
    - "apk add --no-cache openssh && ssh -o StrictHostKeyChecking=no root@{nodeName}"
    stdin: true
    tty: true
    volumeMounts:
    - name: ssh-key
      mountPath: /root/.ssh
  volumes:
  - name: ssh-key
    secret:
      secretName: {sshKeySecret}
  restartPolicy: Never
```

## API接口

### 创建会话

```http
POST /apis/v1/clusters/{cluster}/console/sessions
```

**请求体**：
```json
{
  "nodeName": "node-1",
  "mode": "debug",  // "debug" 或 "ssh"
  "shell": "/bin/sh",  // debug模式
  "privileged": false,  // debug模式
  "sshKeySecret": "ssh-private-key"  // ssh模式
}
```

**响应**：
```json
{
  "success": true,
  "data": {
    "podName": "node-debugger-node-1-abc12",
    "nodeName": "node-1",
    "status": "Pending",
    "mode": "debug"
  }
}
```

### 查询会话状态

```http
GET /apis/v1/clusters/{cluster}/console/sessions/{podName}
```

### 删除会话

```http
DELETE /apis/v1/clusters/{cluster}/console/sessions/{podName}
```

### WebSocket连接

```http
GET /apis/v1/clusters/{cluster}/console/sessions/{podName}/websocket
```

## 使用示例

### Debug模式
```bash
curl -X POST http://localhost:8080/apis/v1/clusters/my-cluster/console/sessions \
  -H "Content-Type: application/json" \
  -d '{
    "nodeName": "worker-node-1",
    "mode": "debug",
    "shell": "/bin/bash",
    "privileged": false
  }'
```

### SSH模式
```bash
curl -X POST http://localhost:8080/apis/v1/clusters/my-cluster/console/sessions \
  -H "Content-Type: application/json" \
  -d '{
    "nodeName": "worker-node-1",
    "mode": "ssh",
    "sshKeySecret": "my-ssh-keys"
  }'
```

## 安全考虑

1. **权限控制**：确保只有授权用户能够创建调试会话
2. **命名空间隔离**：将调试Pod限制在特定命名空间中
3. **镜像白名单**：只允许使用经过审核的调试镜像
4. **自动清理**：及时删除不再使用的调试Pod
5. **审计日志**：记录所有调试会话的创建和删除操作

## 待改进项

1. **真正的Pod Exec**：需要添加`k8s.io/client-go/tools/remotecommand`依赖来实现真正的pod exec功能
2. **会话超时**：实现自动会话超时和清理机制
3. **资源限制**：为调试Pod添加资源限制
4. **多租户支持**：实现基于命名空间的租户隔离
5. **SSH密钥管理**：改进SSH密钥的安全管理

## 依赖要求

- Kubernetes 1.21+
- k8s.io/client-go v0.32.2
- k8s.io/api v0.32.2
- k8s.io/apimachinery v0.32.2

## 注意事项

1. Debug模式需要节点支持HostPath挂载和特权容器
2. SSH模式需要预先配置SSH密钥Secret
3. 确保集群有足够的资源运行调试Pod
4. 建议在生产环境中限制调试功能的使用 