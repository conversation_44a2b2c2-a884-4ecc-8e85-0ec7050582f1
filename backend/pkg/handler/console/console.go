package console

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"

	"go.uber.org/zap"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"

	"github.com/samber/lo"

	"github.com/gorilla/websocket"

	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/console"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	corev1 "k8s.io/api/core/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/tools/remotecommand"
)

type WsMessage struct {
	Type string `json:"type"`
	Data string `json:"data"`
}

type ConsoleHandler struct {
	service ConsoleService
	log     *zap.Logger
}

func NewConsoleHandler(service ConsoleService) *ConsoleHandler {
	return &ConsoleHandler{
		service: service,
		log:     logger.GetLogger().Named("console-handler"),
	}
}
func (h *ConsoleHandler) CreateConsoleSession(c *gin.Context) {
	cluster := c.Param("cluster")

	var req models.ConsoleSessionRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		h.log.Error("invalid request parameters", zap.Error(err))
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, "invalid request parameters"))
		return
	}

	var response *models.ConsoleSessionResponse
	var err error

	switch req.Mode {
	case "debug":
		response, err = h.service.CreateSession(cluster, req.NodeName, req.Shell, req.Privileged)
	case "ssh":
		// SSH模式需要SSH密钥secret名称
		sshKeySecret := req.SSHKeySecret
		sshUser := req.SSHUser
		sshPort := req.SSHPort
		response, err = h.service.CreateSSHSession(cluster, req.NodeName, sshKeySecret, sshUser, sshPort)
	default:
		h.log.Error("unsupported mode", zap.String("mode", req.Mode))
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, "unsupported mode: "+req.Mode))
		return
	}

	if err != nil {
		h.log.Error("create session failed", zap.Error(err))
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}

	h.log.Info("create session succeed", zap.Any("response", response))
	utils.Succeed(c, response)
}

func (h *ConsoleHandler) GetConsoleSession(c *gin.Context) {
	cluster := c.Param("cluster")
	podName := c.Param("podName")
	h.log.Info("get console session", zap.String("cluster", cluster), zap.String("podName", podName))

	status, err := h.service.GetSessionStatus(cluster, podName)
	if err != nil {
		h.log.Error("get session status failed", zap.Error(err))
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}

	utils.Succeed(c, status)
}

func (h *ConsoleHandler) DeleteConsoleSession(c *gin.Context) {
	cluster := c.Param("cluster")
	podName := c.Param("podName")
	h.log.Info("delete console session", zap.String("cluster", cluster), zap.String("podName", podName))

	err := h.service.DeleteSession(cluster, podName)
	if err != nil {
		h.log.Error("delete session failed", zap.Error(err))
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}

	utils.Succeed(c, gin.H{
		"podName": podName,
		"status":  "deleted",
	})
}

func (h *ConsoleHandler) HandleWebSocket(c *gin.Context) {
	cluster := c.Param("cluster")
	podName := c.Param("podName")
	// 通过 service 层获取 k8s client（多集群自动适配）
	h.log.Info("websocket request", zap.String("cluster", cluster), zap.String("podName", podName))

	var k8sClientInterface interface{} = h.service
	var k8sClient kubernetes.Interface
	var err error
	if svc, ok := k8sClientInterface.(*consoleService); ok {
		k8sClient, err = svc.getK8sClient(cluster)
	} else {
		h.log.Error("service type assertion failed", zap.String("cluster", cluster))
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.K8sInternalError, "service type assertion failed"))
		return
	}
	if err != nil {
		h.log.Error("failed to get k8s client", zap.String("cluster", cluster), zap.Error(err))
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}

	// 检查pod是否存在
	pod, err := k8sClient.CoreV1().Pods("default").Get(context.TODO(), podName, v1.GetOptions{})
	if err != nil {
		h.log.Error("pod not found", zap.Error(err))
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.K8sNotFound, "Pod not found: "+err.Error()))
		return
	}
	h.log.Info("pod status", zap.String("podName", podName), zap.String("status", string(pod.Status.Phase)))
	if pod.Status.Phase != corev1.PodRunning {
		h.log.Warn("pod not running", zap.String("podName", podName), zap.String("status", string(pod.Status.Phase)))
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.K8sError, "Pod not running: "+string(pod.Status.Phase)))
		return
	}

	// 构造 exec 请求
	clusterObj, err := client.GetCluster(cluster)
	if err != nil {
		h.log.Error("failed to get cluster object", zap.Error(err))
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	restConfig := clusterObj.GetClient().GetConfig()

	req := k8sClient.CoreV1().RESTClient().Post().
		Resource("pods").
		Name(podName).
		Namespace("default").
		SubResource("attach").
		VersionedParams(&corev1.PodAttachOptions{
			Stdin:  true,
			Stdout: true,
			Stderr: true,
			TTY:    true},
			scheme.ParameterCodec)

	// 创建 SPDY executor
	executor, err := remotecommand.NewSPDYExecutor(restConfig, "POST", req.URL())
	if err != nil {
		h.log.Error("failed to create executor", zap.Error(err))
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}

	// 升级 http 连接为 websocket
	var upgrader = websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool { return true },
	}
	ws, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		h.log.Error("failed to upgrade to websocket", zap.Error(err))
		return
	}
	// 设置websocket连接参数
	wsTerminal := &terminalStream{
		stdin:  make(chan []byte),
		stdout: make(chan []byte),
	}
	ctx, cancel := context.WithCancel(c)
	defer cancel()
	// 将 websocket 连接包装成流
	//wsStream := &websocketStream{conn: ws}

	// 启动 goroutine 处理 websocket 输入和输出
	go h.recycle(ctx, ws, wsTerminal, func() error {
		h.log.Info("cleaning up pod", zap.String("podName", podName))
		return h.service.DeleteSession(cluster, podName)
	})
	go h.handleWebSocketInput(ctx, ws, wsTerminal, cancel)
	go h.handleWebSocketOutput(ctx, ws, wsTerminal, cancel)
	// 执行流式传输
	err = executor.StreamWithContext(ctx, remotecommand.StreamOptions{
		Stdin:  wsTerminal,
		Stdout: wsTerminal,
		Stderr: wsTerminal, // 将标准错误也重定向到 websocket
		Tty:    true,
	})
	if err != nil {
		h.log.Error("stream error", zap.Error(err))
	}
}

// HandleAttachWebSocket 处理kubectl attach方式的WebSocket连接
func (h *ConsoleHandler) HandleSSHWebSocket(c *gin.Context) {
	cluster := c.Param("cluster")
	podName := c.Param("podName")
	h.log.Info("attach websocket request", zap.String("cluster", cluster), zap.String("podName", podName))

	var k8sClientInterface interface{} = h.service
	var k8sClient kubernetes.Interface
	var err error
	if svc, ok := k8sClientInterface.(*consoleService); ok {
		k8sClient, err = svc.getK8sClient(cluster)
	} else {
		h.log.Error("service type assertion failed", zap.String("cluster", cluster))
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.UnKnow, "service type assertion failed"))
		return
	}
	if err != nil {
		h.log.Error("failed to get k8s client", zap.String("cluster", cluster), zap.Error(err))
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}

	// 检查pod是否存在
	pod, err := k8sClient.CoreV1().Pods("default").Get(context.TODO(), podName, v1.GetOptions{})
	if err != nil {
		h.log.Error("pod not found", zap.Error(err))
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.K8sNotFound, "Pod not found: "+err.Error()))
		return
	}
	h.log.Info("pod status", zap.String("podName", podName), zap.String("status", string(pod.Status.Phase)))
	if pod.Status.Phase != corev1.PodRunning {
		h.log.Warn("pod not running", zap.String("podName", podName), zap.String("status", string(pod.Status.Phase)))
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.K8sError, "Pod not running: "+string(pod.Status.Phase)))
		return
	}

	// 构造 attach 请求
	clusterObj, err := client.GetCluster(cluster)
	if err != nil {
		h.log.Error("failed to get cluster object", zap.Error(err))
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	restConfig := clusterObj.GetClient().GetConfig()
	// 	fmt.Sprintf("ssh -o StrictHostKeyChecking=no %s@%s -p %s", sshUser, nodeIP, sshPort)

	// 简化：直接使用固定的容器名称 "ssh-client"
	// 因为 SSH 模式下容器名称是固定的
	containerName := "ssh-client"
	sshUser := lo.If(pod.Annotations["ssh-user"] != "", pod.Annotations["ssh-user"]).Else("root")
	sshPort := lo.If(pod.Annotations["ssh-port"] != "", pod.Annotations["ssh-port"]).Else("22")
	nodeIP := lo.If(pod.Annotations["node-ip"] != "", pod.Annotations["node-ip"]).Else("")
	if nodeIP == "" {
		nodeIP = pod.Status.HostIP
	}

	req := k8sClient.CoreV1().RESTClient().Post().
		Resource("pods").
		Name(podName).
		Namespace("default").
		SubResource("exec").
		VersionedParams(&corev1.PodExecOptions{
			Command:   []string{"/bin/sh", "-c", fmt.Sprintf("ssh -o StrictHostKeyChecking=no %s@%s -p %s", sshUser, nodeIP, sshPort)},
			Stdin:     true,
			Stdout:    true,
			Stderr:    true,
			TTY:       true,
			Container: containerName, // 固定使用 ssh-client 容器
		}, scheme.ParameterCodec)
	// 创建 SPDY executor
	executor, err := remotecommand.NewSPDYExecutor(restConfig, "POST", req.URL())
	if err != nil {
		h.log.Error("failed to create executor", zap.Error(err))
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}

	// 升级 http 连接为 websocket
	var upgrader = websocket.Upgrader{
		CheckOrigin: func(r *http.Request) bool { return true },
	}
	ws, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		h.log.Error("failed to upgrade to websocket", zap.Error(err))
		return
	}

	// 设置websocket连接参数
	// 添加defer函数来处理websocket断开时的pod清理
	//ctx, cancel := context.WithCancel(c)
	//defer cancel()

	// 创建双向数据流
	wsTerminal := &terminalStream{
		stdin:  make(chan []byte, 1024),
		stdout: make(chan []byte, 1024),
	}

	// 启动 goroutine 处理 websocket 输入和输出
	go h.recycle(c, ws, wsTerminal, func() error {
		h.log.Info("cleaning up pod", zap.String("podName", podName))
		return h.service.DeleteSession(cluster, podName)
	})
	go h.handleWebSocketInput(c, ws, wsTerminal, func() {})
	go h.handleWebSocketOutput(c, ws, wsTerminal, func() {})

	// 执行流式传输
	err = executor.StreamWithContext(c, remotecommand.StreamOptions{
		Stdin:  wsTerminal,
		Stdout: wsTerminal,
		Stderr: wsTerminal,
		Tty:    true,
	})
	if err != nil {
		h.log.Error("stream error", zap.Error(err))
		// 发送错误消息到前端
		errorMsg := WsMessage{Type: "error", Data: err.Error()}
		if data, err := json.Marshal(errorMsg); err == nil {
			ws.WriteMessage(websocket.TextMessage, data)
		}
	}
	h.log.Info("attach websocket connection closed")
}

type terminalStream struct {
	stdin  chan []byte
	stdout chan []byte
}

func (t *terminalStream) Read(p []byte) (int, error) {
	data, ok := <-t.stdin
	if !ok {
		return 0, io.EOF
	}
	copy(p, data)
	return len(data), nil
}

func (t *terminalStream) Write(p []byte) (int, error) {
	t.stdout <- p
	return len(p), nil
}

func (t *terminalStream) Close() error {
	close(t.stdin)
	close(t.stdout)
	return nil
}

func (h *ConsoleHandler) recycle(ctx context.Context, ws *websocket.Conn, wsTerminal *terminalStream,
	deletePodFn func() error) {
	for {
		select {
		case <-ctx.Done():
			h.log.Info("context done, cleaning up pod")
			if err := deletePodFn(); err != nil {
				h.log.Error("failed to delete pod", zap.Error(err))
			} else {
				h.log.Info("successfully deleted pod")
			}
			ws.Close()
			wsTerminal.Close()
			return
		}
	}
}

func (h *ConsoleHandler) handleWebSocketInput(ctx context.Context, ws *websocket.Conn, wsTerminal *terminalStream, cancel context.CancelFunc) {
	defer func() {
		h.log.Info("websocket input handler exiting")
		cancel() // 取消context，通知其他goroutine
	}()

	for {
		select {
		case <-ctx.Done():
			return
		default:
			messageType, p, err := ws.ReadMessage()
			if err != nil {
				h.log.Error("failed to read websocket message", zap.Error(err))
				return
			}
			if messageType == websocket.TextMessage {
				var wsMsg WsMessage
				if err := json.Unmarshal(p, &wsMsg); err != nil {
					h.log.Error("failed to unmarshal websocket message", zap.Error(err))
					return
				}
				h.log.Info("received websocket message", zap.Any("msg", wsMsg))
				switch wsMsg.Type {
				case "stdin":
					wsTerminal.stdin <- []byte(wsMsg.Data)
				}
			}
		}
	}
}
func (h *ConsoleHandler) handleWebSocketOutput(ctx context.Context, ws *websocket.Conn, wsTerminal *terminalStream, cancel context.CancelFunc) {
	defer func() {
		h.log.Info("websocket output handler exiting")
		cancel()
	}()

	for {
		select {
		case <-ctx.Done():
			return
		case data, ok := <-wsTerminal.stdout:
			if !ok {
				h.log.Info("websocket output channel closed")
				return
			}
			h.log.Info("sending websocket stdout", zap.String("data", string(data)))
			var wsMsg WsMessage
			wsMsg.Type = "stdout"
			wsMsg.Data = string(data)
			wsMsgData, err := json.Marshal(wsMsg)
			if err != nil {
				h.log.Error("failed to marshal websocket message", zap.Error(err))
				return
			}
			if err := ws.WriteMessage(websocket.TextMessage, wsMsgData); err != nil {
				h.log.Error("failed to write websocket message", zap.Error(err))
				return
			}
		}
	}
}
