package console

import (
	"context"
	"fmt"
	"time"

	"go.uber.org/zap"
	stellarisv1alhpha1 "harmonycloud.cn/stellaris/pkg/apis/stellaris/v1alpha1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"

	"harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/console"
	corev1 "k8s.io/api/core/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	ctrlclient "sigs.k8s.io/controller-runtime/pkg/client"
)

type ConsoleService interface {
	CreateSession(cluster, nodeName, shell string, privileged bool) (*models.ConsoleSessionResponse, error)
	GetSessionStatus(cluster, podName string) (*models.SessionStatusResponse, error)
	DeleteSession(cluster, podName string) error
	CreateSSHSession(cluster, nodeName, sshKeySecret, sshUser, sshPort string) (*models.ConsoleSessionResponse, error)
}

type consoleService struct {
	log *zap.Logger
}

func NewConsoleService(client *kubernetes.Clientset) ConsoleService {
	return &consoleService{
		log: logger.GetLogger().Named("console-service"),
	}
}

func (s *consoleService) getK8sClient(clusterName string) (kubernetes.Interface, error) {
	cluster, err := client.GetCluster(clusterName)
	if err != nil {
		s.log.Error("failed to get cluster", zap.String("cluster", clusterName), zap.Error(err))
		return nil, fmt.Errorf("failed to get cluster %s: %w", clusterName, err)
	}
	return cluster.GetClient().GetKubeClient(), nil
}

func (s *consoleService) getNodeConsoleImage(clusterName string) string {
	clusterObj, err := client.GetCluster(clusterName)
	if err != nil {
		s.log.Warn("failed to get cluster, using default image", zap.String("cluster", clusterName), zap.Error(err))
		return config.NodeConsoleImage.Value
	}
	var cluster stellarisv1alhpha1.Cluster
	if err := clusterObj.GetClient().GetCtrlClient().Get(context.TODO(), ctrlclient.ObjectKey{Name: clusterName}, &cluster); err != nil {
		s.log.Warn("failed to get cluster object, using default image", zap.String("cluster", clusterName), zap.Error(err))
		return config.NodeConsoleImage.Value
	}
	if cluster.Annotations != nil {
		if image, exists := cluster.Annotations["unified-platform.harmonycloud.cn/node-console-image"]; exists && image != "" {
			s.log.Info("using cluster-specific node console image", zap.String("image", image))
			return image
		}
	}
	s.log.Info("using default node console image", zap.String("image", config.NodeConsoleImage.Value))
	return config.NodeConsoleImage.Value
}

func (s *consoleService) getNodeSSHConsoleImage(clusterName string) string {
	clusterObj, err := client.GetCluster(clusterName)
	if err != nil {
		s.log.Warn("failed to get cluster, using default image", zap.String("cluster", clusterName), zap.Error(err))
		return config.NodeSSHConsoleImage.Value
	}
	var cluster stellarisv1alhpha1.Cluster
	if err := clusterObj.GetClient().GetCtrlClient().Get(context.TODO(), ctrlclient.ObjectKey{Name: clusterName}, &cluster); err != nil {
		s.log.Warn("failed to get cluster object, using default image", zap.String("cluster", clusterName), zap.Error(err))
		return config.NodeSSHConsoleImage.Value
	}
	if cluster.Annotations != nil {
		if image, exists := cluster.Annotations["unified-platform.harmonycloud.cn/node-ssh-console-image"]; exists && image != "" {
			s.log.Info("using cluster-specific node ssh console image", zap.String("image", image))
			return image
		}
	}
	s.log.Info("using default node ssh console image", zap.String("image", config.NodeSSHConsoleImage.Value))
	return config.NodeSSHConsoleImage.Value
}

func (s *consoleService) CreateSession(cluster, nodeName, shell string, privileged bool) (*models.ConsoleSessionResponse, error) {
	k8sClient, err := s.getK8sClient(cluster)
	if err != nil {
		s.log.Error("failed to get k8s client", zap.String("cluster", cluster), zap.Error(err))
		return nil, fmt.Errorf("failed to get k8s client: %w", err)
	}

	if shell == "" {
		shell = "/bin/sh"
	}
	consoleImage := s.getNodeConsoleImage(cluster)
	s.log.Info("creating debug pod", zap.String("image", consoleImage), zap.String("node", nodeName))

	pod := &corev1.Pod{
		ObjectMeta: v1.ObjectMeta{
			GenerateName: "node-debugger-" + nodeName + "-",
			Namespace:    "default",
			Labels: map[string]string{
				"app":  "node-debugger",
				"node": nodeName,
			},
		},
		Spec: corev1.PodSpec{
			NodeName:    nodeName,
			HostPID:     true,
			HostNetwork: true,
			HostIPC:     true,
			Containers: []corev1.Container{
				{
					Name:    "debugger",
					Image:   consoleImage,
					Command: []string{shell},
					Stdin:   true,
					TTY:     true,
					SecurityContext: &corev1.SecurityContext{
						Privileged: &privileged,
					},
					VolumeMounts: []corev1.VolumeMount{
						{
							Name:      "host-root",
							MountPath: "/host",
						},
					},
				},
			},
			Volumes: []corev1.Volume{
				{
					Name: "host-root",
					VolumeSource: corev1.VolumeSource{
						HostPath: &corev1.HostPathVolumeSource{
							Path: "/",
						},
					},
				},
			},
			RestartPolicy: corev1.RestartPolicyNever,
			Tolerations: []corev1.Toleration{
				{
					Operator: corev1.TolerationOpExists,
				},
			},
		},
	}

	createdPod, err := k8sClient.CoreV1().Pods("default").Create(context.TODO(), pod, v1.CreateOptions{})
	if err != nil {
		s.log.Error("failed to create debug pod", zap.String("node", nodeName), zap.Error(err))
		return nil, fmt.Errorf("failed to create debug pod: %w", err)
	}

	s.log.Info("created debug pod", zap.String("pod", createdPod.Name), zap.String("node", nodeName), zap.String("image", consoleImage))
	return &models.ConsoleSessionResponse{
		PodName:  createdPod.Name,
		NodeName: nodeName,
		Status:   string(createdPod.Status.Phase),
		Mode:     "debug",
	}, nil
}

func (s *consoleService) CreateSSHSession(cluster, nodeName, sshKeySecret, sshUser, sshPort string) (*models.ConsoleSessionResponse, error) {
	k8sClient, err := s.getK8sClient(cluster)
	if err != nil {
		s.log.Error("failed to get k8s client", zap.String("cluster", cluster), zap.Error(err))
		return nil, fmt.Errorf("failed to get k8s client: %w", err)
	}

	if sshUser == "" {
		sshUser = "root"
	}
	if sshPort == "" {
		sshPort = "22"
	}

	consoleImage := s.getNodeSSHConsoleImage(cluster)
	s.log.Info("creating SSH pod", zap.String("image", consoleImage), zap.String("node", nodeName))

	node, err := k8sClient.CoreV1().Nodes().Get(context.TODO(), nodeName, v1.GetOptions{})
	if err != nil {
		s.log.Error("failed to get node", zap.String("node", nodeName), zap.Error(err))
		return nil, fmt.Errorf("failed to get node %s: %w", nodeName, err)
	}
	var nodeIP string
	for _, addr := range node.Status.Addresses {
		if addr.Type == corev1.NodeInternalIP {
			nodeIP = addr.Address
			break
		}
	}
	if nodeIP == "" {
		s.log.Error("node has no InternalIP", zap.String("node", nodeName))
		return nil, fmt.Errorf("node %s has no InternalIP", nodeName)
	}

	// 修改：直接执行 SSH 命令，这样用户 attach 后就直接进入 SSH 连接
	var container corev1.Container
	var volumes []corev1.Volume
	if sshKeySecret != "" {
		// 免密方式 - 直接执行 SSH 命令
		container = corev1.Container{
			Name:    "ssh-client",
			Image:   consoleImage,
			Command: []string{"/bin/sh", "-c"},
			Args: []string{
				"sleep 86400", // 保持容器运行，防止 Pod 被删除
			},
			Stdin: true,
			TTY:   true,
			VolumeMounts: []corev1.VolumeMount{
				{
					Name:      "ssh-key",
					MountPath: "/root/.ssh",
				},
			},
		}
		volumes = []corev1.Volume{
			{
				Name: "ssh-key",
				VolumeSource: corev1.VolumeSource{
					Secret: &corev1.SecretVolumeSource{
						SecretName: sshKeySecret,
					},
				},
			},
		}
	} else {
		// 密码方式 - 直接执行 SSH 命令
		container = corev1.Container{
			Name:    "ssh-client",
			Image:   consoleImage,
			Command: []string{"/bin/sh", "-c"},
			//Args:    []string{fmt.Sprintf("ssh -o StrictHostKeyChecking=no %s@%s -p %s", sshUser, nodeIP, sshPort)},
			Args: []string{
				"sleep 86400", // 保持容器运行，防止 Pod 被删除
			},
			Stdin: true,
			TTY:   true,
		}
		volumes = nil
	}

	pod := &corev1.Pod{
		ObjectMeta: v1.ObjectMeta{
			GenerateName: "node-ssh-session-" + nodeName + "-",
			Namespace:    "default",
			Labels: map[string]string{
				"app":  "node-ssh",
				"node": nodeName,
			},
			Annotations: map[string]string{
				"ssh-user": sshUser,
				"ssh-port": sshPort,
				"node-ip":  nodeIP,
			},
		},
		Spec: corev1.PodSpec{
			NodeName:      nodeName,
			Containers:    []corev1.Container{container},
			Volumes:       volumes,
			RestartPolicy: corev1.RestartPolicyNever,
		},
	}

	createdPod, err := k8sClient.CoreV1().Pods("default").Create(context.TODO(), pod, v1.CreateOptions{})
	if err != nil {
		s.log.Error("failed to create SSH pod", zap.String("node", nodeName), zap.Error(err))
		return nil, fmt.Errorf("failed to create SSH pod: %w", err)
	}

	s.log.Info("created SSH pod", zap.String("pod", createdPod.Name), zap.String("node", nodeName), zap.String("image", consoleImage))
	return &models.ConsoleSessionResponse{
		PodName:  createdPod.Name,
		NodeName: nodeName,
		Status:   string(createdPod.Status.Phase),
		Mode:     "ssh",
	}, nil
}

func (s *consoleService) GetSessionStatus(cluster, podName string) (*models.SessionStatusResponse, error) {
	k8sClient, err := s.getK8sClient(cluster)
	if err != nil {
		s.log.Error("failed to get k8s client", zap.String("cluster", cluster), zap.Error(err))
		return nil, err
	}
	namespace := "default"
	pod, err := k8sClient.CoreV1().Pods(namespace).Get(context.TODO(), podName, v1.GetOptions{})
	if err != nil {
		s.log.Error("failed to get pod", zap.String("podName", podName), zap.Error(err))
		return nil, err
	}
	s.log.Info("get session status", zap.String("podName", pod.Name), zap.String("status", string(pod.Status.Phase)))
	return &models.SessionStatusResponse{
		PodName:   pod.Name,
		Status:    string(pod.Status.Phase),
		NodeName:  pod.Spec.NodeName,
		StartTime: pod.CreationTimestamp.Format(time.RFC3339),
	}, nil
}

func (s *consoleService) DeleteSession(cluster, podName string) error {
	k8sClient, err := s.getK8sClient(cluster)
	if err != nil {
		s.log.Error("failed to get k8s client", zap.String("cluster", cluster), zap.Error(err))
		return err
	}
	namespace := "default"
	s.log.Info("delete session", zap.String("podName", podName))
	return k8sClient.CoreV1().Pods(namespace).Delete(context.TODO(), podName, v1.DeleteOptions{})
}
