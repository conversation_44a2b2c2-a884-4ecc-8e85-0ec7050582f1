package workload

import (
	"context"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	workloadmodels "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/workload"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/rbac"
	appsv1 "k8s.io/api/apps/v1"
	"k8s.io/apimachinery/pkg/runtime"
	ctrlclient "sigs.k8s.io/controller-runtime/pkg/client"
)

var _ DeploymentHandler = (*internalDeploymentHandler)(nil)

// NewDeploymentHandler ...
func NewDeploymentHandler() DeploymentHandler {
	return &internalDeploymentHandler{
		CommonHandler: common.NewCommonHandler(),
	}
}

type internalDeploymentHandler struct {
	CommonHandler common.CommonIntf
}

func (i *internalDeploymentHandler) FromUnstructured(object ctrlclient.Object) (*appsv1.Deployment, error) {
	toUnstructured, err := runtime.DefaultUnstructuredConverter.ToUnstructured(object)
	if err != nil {
		return nil, err
	}
	deploy := &appsv1.Deployment{}
	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(toUnstructured, deploy); err != nil {
		return nil, err
	}
	return deploy, nil
}

func (i *internalDeploymentHandler) Get(ctx context.Context, request *workloadmodels.GetDeploymentRequest) (response *workloadmodels.GetDeploymentResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbGet); err != nil {
		return nil, err
	}
	obj, err := i.CommonHandler.Get(ctx, request)
	if err != nil {
		return nil, err
	}
	deploy, err := i.FromUnstructured(obj)
	if err != nil {
		return nil, err
	}
	return &workloadmodels.GetDeploymentResponse{Deployment: *deploy}, nil
}

func (i *internalDeploymentHandler) List(ctx context.Context, request *workloadmodels.ListDeploymentRequest) (response *workloadmodels.ListDeploymentResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbList); err != nil {
		return nil, err
	}
	list, err := i.CommonHandler.List(ctx, request)
	if err != nil {
		return nil, err
	}
	filter, err := request.Filter.FilterResult(list.Items)
	if err != nil {
		return nil, err
	}
	var deployments []appsv1.Deployment
	for _, item := range filter.Items {
		deploy, err := i.FromUnstructured(item.DeepCopy())
		if err != nil {
			return nil, err
		}
		deployments = append(deployments, *deploy)
	}
	response = new(workloadmodels.ListDeploymentResponse)
	response.PageableResponse = models.PageableResponse[appsv1.Deployment]{
		Items:      deployments,
		TotalCount: filter.TotalCount,
	}
	return response, nil
}

func (i *internalDeploymentHandler) Create(ctx context.Context, request *workloadmodels.CreateDeploymentRequest) (response *workloadmodels.CreateDeploymentResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbCreate); err != nil {
		return nil, err
	}
	obj, err := i.CommonHandler.Create(ctx, request)
	if err != nil {
		return nil, err
	}
	deploy, err := i.FromUnstructured(obj)
	if err != nil {
		return nil, err
	}
	response = new(workloadmodels.CreateDeploymentResponse)
	response.Deployment = *deploy
	return response, nil
}

func (i *internalDeploymentHandler) Update(ctx context.Context, request *workloadmodels.UpdateDeploymentRequest) (response *workloadmodels.UpdateDeploymentResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbUpdate); err != nil {
		return nil, err
	}
	obj, err := i.CommonHandler.Update(ctx, request)
	if err != nil {
		return nil, err
	}
	deploy, err := i.FromUnstructured(obj)
	if err != nil {
		return nil, err
	}
	response = new(workloadmodels.UpdateDeploymentResponse)
	response.Deployment = *deploy
	return response, nil
}

func (i *internalDeploymentHandler) Patch(ctx context.Context, request *workloadmodels.PatchDeploymentRequest) (response *workloadmodels.PatchDeploymentResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbPatch); err != nil {
		return nil, err
	}
	obj, err := i.CommonHandler.Patch(ctx, request)
	if err != nil {
		return nil, err
	}
	deploy, err := i.FromUnstructured(obj)
	if err != nil {
		return nil, err
	}
	response = new(workloadmodels.PatchDeploymentResponse)
	response.Deployment = *deploy
	return response, nil
}

func (i *internalDeploymentHandler) Delete(ctx context.Context, request *workloadmodels.DeleteDeploymentRequest) (response *workloadmodels.DeleteDeploymentResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbDelete); err != nil {
		return nil, err
	}
	err = i.CommonHandler.Delete(ctx, request)
	if err != nil {
		return nil, err
	}
	response = new(workloadmodels.DeleteDeploymentResponse)
	return response, nil
}

func (i *internalDeploymentHandler) DeleteAll(ctx context.Context, request *workloadmodels.DeleteAllDeploymentRequest) (response *workloadmodels.DeleteAllDeploymentResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbDeleteCollection); err != nil {
		return nil, err
	}
	err = i.CommonHandler.DeleteAll(ctx, request)
	if err != nil {
		return nil, err
	}
	response = new(workloadmodels.DeleteAllDeploymentResponse)
	return response, nil
}
