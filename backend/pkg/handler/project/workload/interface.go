package workload

import (
	"context"

	workloadmodels "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/workload"
)

type DeploymentHandler interface {
	Get(ctx context.Context, request *workloadmodels.GetDeploymentRequest) (response *workloadmodels.GetDeploymentResponse, err error)
	List(ctx context.Context, request *workloadmodels.ListDeploymentRequest) (response *workloadmodels.ListDeploymentResponse, err error)
	Create(ctx context.Context, request *workloadmodels.CreateDeploymentRequest) (response *workloadmodels.CreateDeploymentResponse, err error)
	Update(ctx context.Context, request *workloadmodels.UpdateDeploymentRequest) (response *workloadmodels.UpdateDeploymentResponse, err error)
	Patch(ctx context.Context, request *workloadmodels.PatchDeploymentRequest) (response *workloadmodels.PatchDeploymentResponse, err error)
	Delete(ctx context.Context, request *workloadmodels.DeleteDeploymentRequest) (response *workloadmodels.DeleteDeploymentResponse, err error)
	DeleteAll(ctx context.Context, request *workloadmodels.DeleteAllDeploymentRequest) (response *workloadmodels.DeleteAllDeploymentResponse, err error)
}

type StatefulSetHandler interface {
	Get(ctx context.Context, request *workloadmodels.GetStatefulSetRequest) (response *workloadmodels.GetStatefulSetResponse, err error)
	List(ctx context.Context, request *workloadmodels.ListStatefulSetRequest) (response *workloadmodels.ListStatefulSetResponse, err error)
	Create(ctx context.Context, request *workloadmodels.CreateStatefulSetRequest) (response *workloadmodels.CreateStatefulSetResponse, err error)
	Update(ctx context.Context, request *workloadmodels.UpdateStatefulSetRequest) (response *workloadmodels.UpdateStatefulSetResponse, err error)
	Patch(ctx context.Context, request *workloadmodels.PatchStatefulSetRequest) (response *workloadmodels.PatchStatefulSetResponse, err error)
	Delete(ctx context.Context, request *workloadmodels.DeleteStatefulSetRequest) (response *workloadmodels.DeleteStatefulSetResponse, err error)
	DeleteAll(ctx context.Context, request *workloadmodels.DeleteAllStatefulSetRequest) (response *workloadmodels.DeleteAllStatefulSetResponse, err error)
}

type Handler struct {
	DeploymentHandler  DeploymentHandler
	StatefulSetHandler StatefulSetHandler
}

func NewWorkoadHanlder() *Handler {
	return &Handler{
		DeploymentHandler:  NewDeploymentHandler(),
		StatefulSetHandler: NewStatefulSetHandler(),
	}
}
