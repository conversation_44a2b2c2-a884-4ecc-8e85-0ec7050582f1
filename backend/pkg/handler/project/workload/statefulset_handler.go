package workload

import (
	"context"
	"fmt"

	clientmgr "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	workloadmodels "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/workload"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/rbac"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/apimachinery/pkg/util/json"
	ctrlclient "sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
)

var _ StatefulSetHandler = (*internalStatefulSetHandler)(nil)

// NewStatefulSetHandler ...
func NewStatefulSetHandler() StatefulSetHandler {
	return &internalStatefulSetHandler{
		CommonHandler: common.NewCommonHandler(),
	}
}

type internalStatefulSetHandler struct {
	CommonHandler common.CommonIntf
}

func (i *internalStatefulSetHandler) FromUnstructured(object ctrlclient.Object) (*appsv1.StatefulSet, error) {
	toUnstructured, err := runtime.DefaultUnstructuredConverter.ToUnstructured(object)
	if err != nil {
		return nil, err
	}
	sts := &appsv1.StatefulSet{}
	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(toUnstructured, sts); err != nil {
		return nil, err
	}
	return sts, nil
}

func (i *internalStatefulSetHandler) Get(ctx context.Context, request *workloadmodels.GetStatefulSetRequest) (response *workloadmodels.GetStatefulSetResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbGet); err != nil {
		return nil, err
	}
	object, err := i.CommonHandler.Get(ctx, request)
	if err != nil {
		return nil, err
	}
	sts, err := i.FromUnstructured(object)
	if err != nil {
		return nil, err
	}
	response = &workloadmodels.GetStatefulSetResponse{StatefulSet: *sts}
	return response, nil
}

func (i *internalStatefulSetHandler) List(ctx context.Context, request *workloadmodels.ListStatefulSetRequest) (response *workloadmodels.ListStatefulSetResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbList); err != nil {
		return nil, err
	}
	objects, err := i.CommonHandler.List(ctx, request)
	if err != nil {
		return nil, err
	}
	filterResult, err := request.Filter.FilterResult(objects.Items)
	if err != nil {
		return nil, err
	}
	var stses []appsv1.StatefulSet
	for _, item := range filterResult.Items {
		statefulSet, err := i.FromUnstructured(item.DeepCopy())
		if err != nil {
			return nil, err
		}
		stses = append(stses, *statefulSet)
	}
	response = &workloadmodels.ListStatefulSetResponse{}
	response.Items = stses
	response.TotalCount = filterResult.TotalCount
	return response, nil
}

func (i *internalStatefulSetHandler) Create(ctx context.Context, request *workloadmodels.CreateStatefulSetRequest) (response *workloadmodels.CreateStatefulSetResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbCreate); err != nil {
		return nil, err
	}
	// 1.create service
	raw := request.GetRaw()
	var statefulset appsv1.StatefulSet
	err = json.Unmarshal(raw, &statefulset)
	if err != nil {
		return nil, err
	}
	cluster, err := clientmgr.GetCluster(request.GetCluster())
	if err != nil {
		return nil, err
	}
	cli := cluster.GetClient()
	//if err != nil {
	//	return nil, err
	//}
	// 1.1 get svc name
	svcName := statefulset.Spec.ServiceName
	if svcName == "" {
		svcName = fmt.Sprintf("%s-headless-svc", statefulset.GetName())
		continers := statefulset.Spec.Template.Spec.Containers
		var servicePorts []corev1.ServicePort
		for _, container := range continers {
			ports := container.Ports
			for _, port := range ports {
				servicePorts = append(servicePorts, corev1.ServicePort{
					Name:       port.Name,
					Protocol:   port.Protocol,
					Port:       port.ContainerPort,
					TargetPort: intstr.FromInt(int(port.ContainerPort)),
				})
			}
		}
		// 1.2 create service
		service := corev1.Service{}
		service.SetName(svcName)
		service.SetNamespace(request.GetNamespace())
		service.SetLabels(statefulset.GetLabels())
		service.Spec.ClusterIP = corev1.ClusterIPNone
		service.Spec.Selector = statefulset.Spec.Selector.MatchLabels
		service.Spec.Ports = servicePorts
		//svcRequest.Raw, err = json.Marshal(service)
		//svcRequest.Group = ""
		//svcRequest.ResourceType = "services"

		deepCopy := service.DeepCopy()
		_, err = controllerutil.CreateOrUpdate(ctx, cli.GetCtrlClient(), deepCopy, func() error {
			service.DeepCopyInto(deepCopy)
			return nil
		})
		if err != nil {
			logger.GetSugared().Errorf("create service %s failed, err: %s", svcName, err.Error())
			return nil, errors.HandleK8sError(err)
		}
		logger.GetSugared().Infof("create service %s success", svcName)
	}
	// 2.create statefulset
	statefulset.Spec.ServiceName = svcName
	request.Raw, err = json.Marshal(statefulset)
	if err != nil {
		return nil, err
	}
	object, err := i.CommonHandler.Create(ctx, request)
	if err != nil {
		logger.GetSugared().Errorf("create statefulset failed, err: %s", err.Error())
		svcErr := cli.GetCtrlClient().Delete(ctx, &corev1.Service{ObjectMeta: metav1.ObjectMeta{
			Name:      statefulset.Spec.ServiceName,
			Namespace: request.GetNamespace(),
		}})
		if svcErr != nil {
			logger.GetSugared().Errorf("delete service %s failed, err: %s", svcName, err.Error())
		}
		return nil, err

	}
	sts, err := i.FromUnstructured(object)
	if err != nil {
		return nil, err
	}
	response = &workloadmodels.CreateStatefulSetResponse{StatefulSet: *sts}
	return response, nil
}

func (i *internalStatefulSetHandler) Update(ctx context.Context, request *workloadmodels.UpdateStatefulSetRequest) (response *workloadmodels.UpdateStatefulSetResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbUpdate); err != nil {
		return nil, err
	}
	object, err := i.CommonHandler.Update(ctx, request)
	if err != nil {
		return nil, err
	}
	sts, err := i.FromUnstructured(object)
	if err != nil {
		return nil, err
	}
	response = &workloadmodels.UpdateStatefulSetResponse{StatefulSet: *sts}
	return response, nil
}

func (i *internalStatefulSetHandler) Patch(ctx context.Context, request *workloadmodels.PatchStatefulSetRequest) (response *workloadmodels.PatchStatefulSetResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbPatch); err != nil {
		return nil, err
	}
	object, err := i.CommonHandler.Patch(ctx, request)
	if err != nil {
		return nil, err
	}
	sts, err := i.FromUnstructured(object)
	if err != nil {
		return nil, err
	}
	response = &workloadmodels.PatchStatefulSetResponse{StatefulSet: *sts}
	return response, nil
}

func (i *internalStatefulSetHandler) Delete(ctx context.Context, request *workloadmodels.DeleteStatefulSetRequest) (response *workloadmodels.DeleteStatefulSetResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbDelete); err != nil {
		return nil, err
	}
	err = i.CommonHandler.Delete(ctx, request)
	if err != nil {
		return nil, err
	}
	return &workloadmodels.DeleteStatefulSetResponse{}, nil
}

func (i *internalStatefulSetHandler) DeleteAll(ctx context.Context, request *workloadmodels.DeleteAllStatefulSetRequest) (response *workloadmodels.DeleteAllStatefulSetResponse, err error) {
	if err := authWorkloadPermission(ctx, request, rbac.VerbDeleteCollection); err != nil {
		return nil, err
	}
	err = i.CommonHandler.DeleteAll(ctx, request)
	if err != nil {
		return nil, err
	}
	return &workloadmodels.DeleteAllStatefulSetResponse{}, nil
}
