package workload

import (
	"context"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/rbac"
)

func authWorkloadPermission(ctx context.Context, p common.ReaderAndWriterParam, verb string) error {
	authRequest := &rbac.AuthRequest{
		SelfSubjectAccessReview: &rbac.SelfSubjectAccessReviewRequest{
			Cluster:   p.GetCluster(),
			Namespace: p.GetNamespace(),
			Group:     p.GetGroup(),
			Version:   p.GetVersion(),
			Resource:  p.GetResourceType(),
			Verb:      verb,
		}}
	if err := rbac.AuthK8sRBAC(ctx, authRequest); err != nil {
		return err
	}
	return nil
}
