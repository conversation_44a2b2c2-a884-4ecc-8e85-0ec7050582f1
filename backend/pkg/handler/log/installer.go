package log

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/gorilla/websocket"
	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	hcclient "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	addonmodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/addon"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

func NewInstallerLog() InstallerLog {
	return installerLog{}
}

type installerLog struct {
}

func (handler installerLog) SisyphusLogForInstaller(ctx context.Context, objectKey client.ObjectKey, config addonmodel.SisyphusAddressConfig) (<-chan []byte, context.Context, context.CancelFunc, error) {
	installer, err := handler.getInstallerFunc(ctx, objectKey)
	if err != nil {
		// 查询installer 失败 返回错误
		return nil, nil, nil, err
	}
	logChain := make(chan []byte, 1)
	logContext, logCancelFunc := context.WithCancel(context.Background())

	// 当ctx done 时，logCancelFunc Func 随之运行
	go func(logCancelFunc func()) {
		select {
		case <-ctx.Done():
			logCancelFunc()
		}
	}(logCancelFunc)

	go func(logChain chan<- []byte, logContext context.Context, logCancelFunc context.CancelFunc) {
		defer logCancelFunc()
		for index, task := range installer.Spec.Tasks {
			// 如果一下出现错误，取消函数延迟关闭
			after1SecondsCancelFuncClose := func() {
				time.Sleep(1 * time.Second)
				logCancelFunc()
			}
			if task.Kind == nil {
				return
			}
			select {
			case <-logContext.Done():
				return
			default:
				switch *task.Kind {
				case installerv1alpha1.InstallerTaskKindSisyphusNodeInitial:
					handler.handleForNodeInitial(objectKey, index, logChain, logContext, after1SecondsCancelFuncClose)
				case installerv1alpha1.InstallerTaskKindSisyphusSolutionApply:
					handler.handleForSolutionApply(objectKey, index, logChain, logContext, after1SecondsCancelFuncClose)
				case installerv1alpha1.InstallerTaskKindSisyphusSolutionExec:
					handler.handleForSolutionExec(objectKey, index, logChain, logContext, after1SecondsCancelFuncClose, config)
				case installerv1alpha1.InstallerTaskSisyphusKindAddCluster:
					handler.handleForAddCluster(objectKey, index, logChain, logContext, after1SecondsCancelFuncClose, config)
				}
			}

		}
	}(logChain, logContext, logCancelFunc)
	return logChain, logContext, logCancelFunc, nil
}

func (handler installerLog) handleForNodeInitial(objectKey client.ObjectKey, taskIndex int, logChain chan<- []byte, logContext context.Context, logCancelFunc context.CancelFunc) {
nodeInitial:
	// 先查询 installer 如果查询installer 失败 则可以直接返回
	installer, err := handler.getInstallerFunc(logContext, objectKey)
	if err != nil {
		logChain <- []byte(fmt.Sprintf("find installer failed,object key is %s/%s,error is %s", objectKey.Namespace, objectKey.Name, err.Error()))
		logCancelFunc()
	}
	// 获取spec
	var taskSpec = installer.Spec.Tasks[taskIndex]

	// 生成nodeList 以方便日志输出
	nodes := make([]string, 0, len(taskSpec.SisyphusNodeInitial.Param))
	for _, param := range taskSpec.SisyphusNodeInitial.Param {
		nodes = append(nodes, fmt.Sprintf("%s:%s", param.IP, param.Port))
	}

	// 根据status 获取状态
	var statusTaskPhase installerv1alpha1.StatusPhase
	var failedMessage string
	// 获取当前步骤的status =》 若status task == 0 则表示与初始化中
	if len(installer.Status.Tasks) == 0 {
		statusTaskPhase = installerv1alpha1.StatusPhasePending
	} else {
		statusTaskPhase = installer.Status.Tasks[taskIndex].Phase
		failedMessage = installer.Status.Tasks[taskIndex].Message
	}

	switch statusTaskPhase {
	case installerv1alpha1.StatusPhaseSuccess:
		logChain <- []byte(fmt.Sprintf("success add node [%s]", strings.Join(nodes, ",")))
		return
	case installerv1alpha1.StatusPhaseFailed:
		logChain <- []byte(fmt.Sprintf("failed add nodes,message is '%s'", failedMessage))
		logCancelFunc()
		return
	case installerv1alpha1.StatusPhasePending:
		fallthrough
	case installerv1alpha1.StatusPhaseRunning:
		fallthrough
	default:
		logChain <- []byte(fmt.Sprintf("node initial doing,node list is  [%s],please wait......", strings.Join(nodes, ",")))
		time.Sleep(3 * time.Second)
		goto nodeInitial
	}
}

func (handler installerLog) handleForSolutionApply(objectKey client.ObjectKey, taskIndex int, logChain chan<- []byte, logContext context.Context, logCancelFunc context.CancelFunc) {
solutionApply:
	// 先查询 installer 如果查询installer 失败 则可以直接返回
	installer, err := handler.getInstallerFunc(logContext, objectKey)
	if err != nil {
		logChain <- []byte(fmt.Sprintf("find installer failed,object key is %s/%s,error is %s", objectKey.Namespace, objectKey.Name, err.Error()))
		logCancelFunc()
	}
	// 获取spec
	var taskSpec = installer.Spec.Tasks[taskIndex]

	// 获取solution 的名称 与 参数 以方便日志输出
	solutionName := taskSpec.SisyphusSolutionApply.SolutionName
	params := taskSpec.SisyphusSolutionApply.Param

	// 根据status 获取状态
	var statusTaskPhase installerv1alpha1.StatusPhase
	var failedMessage string
	// 获取当前步骤的status =》 若status task == 0 则表示与初始化中
	if len(installer.Status.Tasks) == 0 {
		statusTaskPhase = installerv1alpha1.StatusPhasePending
	} else {
		statusTaskPhase = installer.Status.Tasks[taskIndex].Phase
		failedMessage = installer.Status.Tasks[taskIndex].Message
	}

	switch statusTaskPhase {
	case installerv1alpha1.StatusPhaseSuccess:
		logChain <- []byte(fmt.Sprintf("success apply sisyphus solution,solution name is %s,param is\n %s \n", solutionName, params))
		return
	case installerv1alpha1.StatusPhaseFailed:
		logChain <- []byte(fmt.Sprintf("failed apply sisyphus solution,solution name is %s,message is %s,param is\n %s \n", solutionName, failedMessage, params))
		logCancelFunc()
		return
	case installerv1alpha1.StatusPhasePending:
		fallthrough
	case installerv1alpha1.StatusPhaseRunning:
		fallthrough
	default:
		logChain <- []byte(fmt.Sprintf("doing for apply sisyphus solution,solution name is %s,param is\n %s \n please wait ......", solutionName, params))
		time.Sleep(3 * time.Second)
		goto solutionApply
	}
}

func (handler installerLog) handleForSolutionExec(objectKey client.ObjectKey, taskIndex int, logChain chan<- []byte, logContext context.Context, logCancelFunc context.CancelFunc, config addonmodel.SisyphusAddressConfig) {
execInitial:
	// 先查询 installer 如果查询installer 失败 则可以直接返回
	installer, err := handler.getInstallerFunc(logContext, objectKey)
	if err != nil {
		logChain <- []byte(fmt.Sprintf("find installer failed,object key is %s/%s,error is %s", objectKey.Namespace, objectKey.Name, err.Error()))
		logCancelFunc()
	}
	// 获取spec
	var taskSpec = installer.Spec.Tasks[taskIndex]

	// 获取solutionName 和 待执行步骤 以方便日志输出
	solutionName := taskSpec.SisyphusSolutionExec.SolutionName
	execSteps := taskSpec.SisyphusSolutionExec.Param

	// 根据status 获取状态
	var statusTaskPhase installerv1alpha1.StatusPhase
	// 获取当前步骤的status =》 若status task == 0 则表示与初始化中
	if len(installer.Status.Tasks) == 0 {
		statusTaskPhase = installerv1alpha1.StatusPhasePending
	} else {
		statusTaskPhase = installer.Status.Tasks[taskIndex].Phase
	}

	switch statusTaskPhase {
	case installerv1alpha1.StatusPhaseSuccess:
		fallthrough
	case installerv1alpha1.StatusPhaseFailed:
		fallthrough
	case installerv1alpha1.StatusPhaseRunning:
		goto buildExecLog
	case installerv1alpha1.StatusPhasePending:
		fallthrough
	default:
		logChain <- []byte(fmt.Sprintf("sisyphus solution exec preparing,solutoin name is %s, running step is [%s],please wait......", solutionName, strings.Join(execSteps, ",")))
		time.Sleep(3 * time.Second)
		goto execInitial
	}
buildExecLog:
	handler.handleExecLog(solutionName, objectKey, taskIndex, logChain, logContext, logCancelFunc, config)

}

func (handler installerLog) handleForAddCluster(objectKey client.ObjectKey, taskIndex int, logChain chan<- []byte, logContext context.Context, logCancelFunc context.CancelFunc, config addonmodel.SisyphusAddressConfig) {
execInitial:
	// 先查询 installer 如果查询installer 失败 则可以直接返回
	installer, err := handler.getInstallerFunc(logContext, objectKey)
	if err != nil {
		logChain <- []byte(fmt.Sprintf("find installer failed,object key is %s/%s,error is %s", objectKey.Namespace, objectKey.Name, err.Error()))
		logCancelFunc()
	}
	// 获取spec
	var taskSpec = installer.Spec.Tasks[taskIndex]

	// 获取solutionName 和 待执行步骤 以方便日志输出
	solutionName := taskSpec.SisyphusAddCluster.SolutionName
	execStep := taskSpec.SisyphusAddCluster.StepName

	// 根据status 获取状态
	var statusTaskPhase installerv1alpha1.StatusPhase
	// 获取当前步骤的status =》 若status task == 0 则表示与初始化中
	if len(installer.Status.Tasks) == 0 {
		statusTaskPhase = installerv1alpha1.StatusPhasePending
	} else {
		statusTaskPhase = installer.Status.Tasks[taskIndex].Phase
	}

	switch statusTaskPhase {
	case installerv1alpha1.StatusPhaseSuccess:
		fallthrough
	case installerv1alpha1.StatusPhaseFailed:
		fallthrough
	case installerv1alpha1.StatusPhaseRunning:
		goto buildExecLog
	case installerv1alpha1.StatusPhasePending:
		fallthrough
	default:
		logChain <- []byte(fmt.Sprintf("sisyphus solution exec preparing,solutoin name is %s, running step is [%s],please wait......", solutionName, strings.Join([]string{execStep}, ",")))
		time.Sleep(3 * time.Second)
		goto execInitial
	}
buildExecLog:
	handler.handleExecLog(solutionName, objectKey, taskIndex, logChain, logContext, logCancelFunc, config)

}

func (handler installerLog) handleExecLog(solutionName string, objectKey client.ObjectKey, taskIndex int, logChain chan<- []byte, logContext context.Context, logCancelFunc context.CancelFunc, config addonmodel.SisyphusAddressConfig) {
	// 构建websocket 请求
	url := handler.buildSisyphusTaskLogUrl(solutionName, config)
	initWebsocketConnFunc := func(ctx context.Context, url string) (*websocket.Conn, error) {
		conn, _, err := websocket.DefaultDialer.DialContext(ctx, url, nil)
		return conn, err
	}
	// 确保任务运行结束时 websocket被关闭
	websocketContext, websocketCancelFunc := context.WithCancel(context.Background())
	defer websocketCancelFunc()
	conn, err := initWebsocketConnFunc(websocketContext, url)
	if err != nil {
		logChain <- []byte(fmt.Sprintf("connect sisyphus websocket failed,url is %s,err is %s", url, err.Error()))
		logCancelFunc()
		return
	}
	// 开启异步任务 转发Websocket请求
	go func(conn *websocket.Conn) {
		for {
			_, bytes, err := conn.ReadMessage()
			if err != nil {
				logger.GetLogger().Sugar().Infof("web socket closed,solution name is %s", solutionName)
				break
			}
			logChain <- bytes
		}
	}(conn)

	// 当installer 成功时候 跳到下一个installer
	// 当installer 失败时候 结束
	var onBreakInstallerTaskStatus = handler.onBreakInstallerTaskStatusFunc(logContext, objectKey, taskIndex)
	for {
		select {
		case <-logContext.Done():
			// websocket已被关闭
			return
		case status := <-onBreakInstallerTaskStatus:
			// 如果状态为成功 停止本次，进入下一次
			// 如果状态为失败 停止任务运行
			// 如果状态为pending 停止任务运行
			if status != installerv1alpha1.StatusPhaseSuccess {
				logCancelFunc()
			}
			return
		}
	}
}

func (handler installerLog) onBreakInstallerTaskStatusFunc(ctx context.Context, objectKey client.ObjectKey, taskIndex int) <-chan installerv1alpha1.StatusPhase {
	onBreakInstallerTaskStatus := make(chan installerv1alpha1.StatusPhase)
	go func(chan<- installerv1alpha1.StatusPhase) {
		// 每5秒重新获取一次installer 状态
		ticker := time.NewTicker(5 * time.Second)
		defer ticker.Stop()
	selectRange:
		select {
		case <-ctx.Done():
			// web socket 已关闭
			return
		case <-ticker.C:
			// 间隔5秒 获取 installer 状态
			installer, err := handler.getInstallerFunc(ctx, objectKey)
			if err != nil {
				logger.GetLogger().Sugar().Errorf("get installer error,object:%v,err is %s", objectKey, err.Error())
				onBreakInstallerTaskStatus <- installerv1alpha1.StatusPhasePending
				return
			}

			if len(installer.Status.Tasks) == 0 {
				onBreakInstallerTaskStatus <- installerv1alpha1.StatusPhasePending
				return
			}

			phase := installer.Status.Tasks[taskIndex].Phase
			switch phase {
			case installerv1alpha1.StatusPhaseSuccess:
				fallthrough
			case installerv1alpha1.StatusPhaseFailed:
				fallthrough
			case installerv1alpha1.StatusPhasePending:
				onBreakInstallerTaskStatus <- phase
				return
			case installerv1alpha1.StatusPhaseRunning:
				goto selectRange
			default:
				onBreakInstallerTaskStatus <- installerv1alpha1.StatusPhasePending
				return
			}
		}
	}(onBreakInstallerTaskStatus)
	return onBreakInstallerTaskStatus
}

func (handler installerLog) getInstallerFunc(ctx context.Context, objectKey client.ObjectKey) (installerv1alpha1.Installer, error) {
	// 先查询 installer 如果查询installer 失败 则可以直接返回
	var installer installerv1alpha1.Installer
	if err := hcclient.GetLocalCluster().GetClient().GetCtrlClient().Get(ctx, objectKey, &installer); err != nil {
		return installerv1alpha1.Installer{}, err
	}
	if installer.Spec.ReApply {
		installer.Status = installerv1alpha1.InstallerStatus{}
	}
	return installer, nil
}

func (handler installerLog) buildSisyphusTaskLogUrl(solutionName string, config addonmodel.SisyphusAddressConfig) string {
	logAddressPath := fmt.Sprintf("/deploys/%s/logs", solutionName)
	target := config.Address
	if strings.HasPrefix(target, "http://") {
		target = strings.TrimLeft(target, "http://")
	} else if strings.HasPrefix(target, "HTTP://") {
		target = strings.TrimLeft(target, "HTTP://")
	} else if strings.HasPrefix(target, "https://") {
		target = strings.TrimLeft(target, "https://")
	} else if strings.HasPrefix(target, "HTTPS://") {
		target = strings.TrimLeft(target, "HTTPS://")
	}
	url := fmt.Sprintf("ws://%s%s", target, logAddressPath)
	return url
}
