package backupserver

import (
	"context"
	"errors"
	"fmt"
	"strconv"

	madmin "github.com/minio/madmin-go/v2"
	minio "github.com/minio/minio-go/v7"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"harmonycloud.cn/unifiedportal/midware-go/midwares/auth"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/database"
	appmanagement "harmonycloud.cn/unifiedportal/portal/backend/pkg/feign/app-management"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/backupserver"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/formatter"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/mr"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/snowflake"
)

var _ OrganInterface = (*organ)(nil)

func NewOrgan() OrganInterface {
	return &organ{
		caasDB:     database.CaasDB,
		snowflake:  snowflake.NewSnowflakeIntf(),
		log:        logger.GetLogger(),
		apmService: appmanagement.NewService(),
	}
}

type organ struct {
	caasDB     *gorm.DB
	snowflake  snowflake.SnowflakeIntf
	log        *zap.Logger
	apmService *appmanagement.Service
}

func (o organ) GetStorageServerListByOrganId(c context.Context, organId string) ([]*backupserver.OrganStorageServer, error) {
	// 1. 获取组织下的存储服务器和桶映射关系
	backupServerOrganizationBuckets := []caas.BackupServerOrganizationBucket{}
	if err := o.caasDB.Model(&caas.BackupServerOrganizationBucket{}).
		Where("organ_id = ?", organId).
		Find(&backupServerOrganizationBuckets).Error; err != nil {
		o.log.Error("failed to get buckets", zap.Error(err))
		return nil, fmt.Errorf("failed to get buckets: %v", err)
	}

	// 构建存储服务器和桶的映射
	storageServerMap := make(map[int64][]caas.BackupServerOrganizationBucket)
	for _, bucket := range backupServerOrganizationBuckets {
		storageServerMap[bucket.StorageServerId] = append(storageServerMap[bucket.StorageServerId], bucket)
	}

	// 2. 获取项目映射
	projectMap, err := o.GetProjectMap(c, organId)
	if err != nil {
		o.log.Error("failed to get project map", zap.Error(err))
	}

	// 3. 使用 mr.MapReduce 并行处理每个存储服务器
	storageServers, err := mr.MapReduce(
		func(source chan<- int64) {
			// 将存储服务器 ID 投递到 MapReduce 的源
			for serverID := range storageServerMap {
				source <- serverID
			}
		},
		func(serverID int64, writer mr.Writer[*backupserver.OrganStorageServer], cancel func(error)) {
			// 处理每个存储服务器
			bucketList := storageServerMap[serverID]

			// 获取存储服务器信息
			var storageServer caas.BackupServer
			if err := o.caasDB.Model(&caas.BackupServer{}).
				Where("id = ?", serverID).
				First(&storageServer).Error; err != nil {
				o.log.Error("failed to get storage server", zap.Error(err))
				return
			}

			// 检查存储服务器健康状态
			status := checkMinioServerStatus(storageServer)
			if status.Reason != Health {
				// 服务器不健康，直接写入基础信息
				writer.Write(&backupserver.OrganStorageServer{
					StorageServerId: strconv.FormatInt(serverID, 10),
					NickName:        storageServer.Nickname,
					Type:            storageServer.Type,
					Status:          status,
					Objects:         0,
					Size:            0,
					BucketList:      nil,
					CreatedAt:       backupserver.CustomTime{Time: storageServer.CreateTime},
				})
				return
			}

			// 获取 MinIO 客户端
			client, err := o.getMinioAdminClient(c, strconv.FormatInt(serverID, 10))
			if err != nil {
				o.log.Error("failed to get minio admin client", zap.Error(err))
				return
			}

			// 获取账户信息
			accountInfo, err := client.AccountInfo(c, madmin.AccountOpts{})
			if err != nil {
				o.log.Error("failed to get account info", zap.Error(err))
				return
			}

			// 构建桶信息映射
			bucketInfoMap := make(map[string]madmin.BucketAccessInfo)
			for _, bucketInfo := range accountInfo.Buckets {
				bucketInfoMap[bucketInfo.Name] = bucketInfo
			}

			// 构建当前存储服务器的桶信息
			var objects, size uint64
			var organBuckets []backupserver.OrganBucket
			for _, bucket := range bucketList {
				bucketInfo, exists := bucketInfoMap[bucket.Bucket]
				if !exists {
					o.log.Warn("bucket info not found in account info", zap.String("bucket", bucket.Bucket))
					continue
				}

				// 获取项目名称
				project := caas.BackupServerBucketProject{}
				projectName := ""
				if err := o.caasDB.Model(&caas.BackupServerBucketProject{}).
					Where("storage_server_id = ? and bucket = ?", serverID, bucket.Bucket).
					First(&project).Error; err == nil {
					if projectEntry, ok := projectMap[strconv.FormatInt(project.ProjectId, 10)]; ok {
						projectName = projectEntry.Name
					}
				} else {
					o.log.Warn("project not found for bucket", zap.String("bucket", bucket.Bucket))
				}

				// 累积对象数量和大小
				objects += bucketInfo.Objects
				size += bucketInfo.Size
				projectId := strconv.FormatInt(project.ProjectId, 10)
				if projectId == "0" {
					projectId = ""
				}

				organBuckets = append(organBuckets, backupserver.OrganBucket{
					Name:        bucket.Bucket,
					ProjectId:   projectId,
					ProjectName: projectName,
					Objects:     bucketInfo.Objects,
					Quota: backupserver.BucketQuota{
						Quota: formatter.FormattedGiBSize(bucketInfo.Details.Quota.Quota),
						Type:  backupserver.QuotaType(bucketInfo.Details.Quota.Type),
					},
					Size:       formatter.FormattedSize(bucketInfo.Size),
					CreateTime: backupserver.CustomTime{Time: bucket.CreateTime},
				})
			}

			// 写入存储服务器信息
			writer.Write(&backupserver.OrganStorageServer{
				StorageServerId: strconv.FormatInt(serverID, 10),
				NickName:        storageServer.Nickname,
				Type:            storageServer.Type,
				Status:          status,
				Objects:         objects,
				Size:            formatter.FormattedSize(size),
				BucketList:      organBuckets,
				CreatedAt:       backupserver.CustomTime{Time: storageServer.CreateTime},
			})
		},
		func(pipe <-chan *backupserver.OrganStorageServer, writer mr.Writer[[]*backupserver.OrganStorageServer], cancel func(error)) {
			// 收集所有结果
			var result []*backupserver.OrganStorageServer
			for server := range pipe {
				result = append(result, server)
			}
			writer.Write(result)
		},
	)
	if err != nil {
		o.log.Error("failed to process storage servers", zap.Error(err))
		return nil, err
	}
	return storageServers, nil
}

func (o organ) UnassignProjectToBucket(_ context.Context, organId string, req backupserver.UnassignProjectToBucketReq) error {
	backupServerUsage := make([]caas.ResourceUsage, 0)
	if err := o.caasDB.Model(&caas.BackupServerBucketProject{}).
		Where("resource_type = ?", "backupServer").
		Where("resource_id = ?", req.StorageId).
		Where("project_id = ?", req.ProjectId).
		Where("is_delete = ?", false).Find(&backupServerUsage).Error; err != nil {
		o.log.Error("failed to unassign project from bucket", zap.Error(err))
		return fmt.Errorf("failed to unassign project from bucket: %v", err)
	}
	if len(backupServerUsage) != 0 {
		return fmt.Errorf("backupServer is already used")
	}
	// 移除项目桶分配
	if err := o.caasDB.Model(&caas.BackupServerBucketProject{}).
		Where("organ_id = ? AND bucket = ? AND project_id = ? AND storage_server_id = ?", organId, req.BucketName, req.ProjectId, req.StorageId).
		Delete(&caas.BackupServerBucketProject{}).Error; err != nil {
		o.log.Error("failed to unassign project from bucket", zap.Error(err))
		return fmt.Errorf("failed to unassign project from bucket: %v", err)
	}
	return nil
}

func (o organ) AssignProjectToBucket(_ context.Context, organId string, req backupserver.AssignProjectToBucketReq) error {
	// 1.1是否存在
	var bucket caas.BackupServerOrganizationBucket
	if err := o.caasDB.Model(&caas.BackupServerOrganizationBucket{}).
		Where("storage_server_id = ? and bucket = ? and organ_id = ?", req.StorageId, req.BucketName, organId).
		First(&bucket).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			o.log.Error("bucket not found", zap.Error(err))
			return fmt.Errorf("桶不存在")
		} else {
			o.log.Error("failed to get bucket", zap.Error(err))
			return fmt.Errorf("failed to get bucket: %v", err)
		}
	}
	// 1.2查看桶是否分配
	var bucketCount int64
	err := o.caasDB.Model(&caas.BackupServerBucketProject{}).
		Where("storage_server_id = ? and bucket = ?", req.StorageId, req.BucketName).
		Count(&bucketCount).Error
	if err != nil {
		o.log.Error("failed to check bucket assignment", zap.Error(err))
		return fmt.Errorf("failed to check bucket assignment: %v", err)
	}
	if bucketCount > 0 {
		return fmt.Errorf("存储桶已经分配给项目")
	}

	// 2.分配桶给项目
	storageIdInt, err := strconv.ParseInt(req.StorageId, 10, 64)
	if err != nil {
		o.log.Error("failed to parse storage id", zap.Error(err))
		return fmt.Errorf("invalid storage id: %v", err)
	}
	organIdInt, err := strconv.ParseInt(organId, 10, 64)
	if err != nil {
		o.log.Error("failed to parse organ id", zap.Error(err))
		return fmt.Errorf("invalid organ id: %v", err)
	}
	projectIdInt, err := strconv.ParseInt(req.ProjectId, 10, 64)
	if err != nil {
		o.log.Error("failed to parse project id", zap.Error(err))
		return fmt.Errorf("invalid project id: %v", err)
	}
	assignment := &caas.BackupServerBucketProject{
		Id:              o.snowflake.GenerateID(),
		StorageServerId: storageIdInt,
		Bucket:          req.BucketName,
		OrganId:         organIdInt,
		ProjectId:       projectIdInt,
	}
	err = o.caasDB.Create(assignment).Error
	if err != nil {
		o.log.Error("failed to assign bucket to project", zap.Error(err))
		return fmt.Errorf("failed to assign bucket to project: %v", err)
	}
	return nil
}

func (o organ) GetProjectList(c context.Context, id string) ([]*backupserver.Project, error) {
	jwt := auth.GetToken(c)
	//去掉Bearer
	if len(jwt) > 7 && jwt[:7] == "Bearer " {
		jwt = jwt[7:]
	}
	if jwt == "" {
		o.log.Error("JWT is missing in the request")
		return nil, fmt.Errorf("authorization token is required")
	}
	projects, err := o.apmService.GetProjects(jwt, id)
	if err != nil {
		o.log.Error("GetProject error", zap.Error(err))
		return nil, err
	}
	return projects, nil

}

func (o organ) GetProjectMap(c context.Context, organId string) (map[string]backupserver.Project, error) {
	jwt := auth.GetToken(c)
	//去掉Bearer
	if len(jwt) > 7 && jwt[:7] == "Bearer " {
		jwt = jwt[7:]
	}
	if jwt == "" {
		o.log.Error("JWT is missing in the request")
		return nil, fmt.Errorf("authorization token is required")
	}
	projectMap := make(map[string]backupserver.Project)
	var err error
	projectMap, err = o.apmService.GetProjectMapByOrganId(jwt, organId)
	if err != nil {
		return nil, err
	}
	return projectMap, nil
}

// getMinioClient returns a minio client configured with the given endpoint, access key, secret key, and secure flag.
func (o organ) getMinioClient(_ context.Context, storageServersId string) (*minio.Client, error) {
	server := caas.BackupServer{}
	if err := o.caasDB.First(&server, storageServersId).Error; err != nil {
		o.log.Error("not find backupSever", zap.Error(err))
		return nil, err
	}
	endpoint := server.RevertS3StorageServersConfig().Url.EndPoint()
	username := server.RevertS3StorageServersConfig().Username
	password := server.RevertS3StorageServersConfig().Password
	secure := server.RevertS3StorageServersConfig().Url.Protocol == "https"
	return getMinioClient(endpoint, username, password, secure)
}

// getMinioAdminClient returns a minio admin client configured with the given endpoint, access key, secret key, and secure flag.
func (o organ) getMinioAdminClient(_ context.Context, storageServersId string) (*madmin.AdminClient, error) {
	server := caas.BackupServer{}
	if err := o.caasDB.First(&server, storageServersId).Error; err != nil {
		o.log.Error("not find backupSever", zap.Error(err))
		return nil, err
	}
	endpoint := server.RevertS3StorageServersConfig().Url.EndPoint()
	username := server.RevertS3StorageServersConfig().Username
	password := server.RevertS3StorageServersConfig().Password
	secure := server.RevertS3StorageServersConfig().Url.Protocol == "https"
	return getMinioAdminClient(endpoint, username, password, secure)
}
