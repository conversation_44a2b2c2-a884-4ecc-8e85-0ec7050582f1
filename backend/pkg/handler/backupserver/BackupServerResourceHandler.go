package backupserver

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"sync"

	madmin "github.com/minio/madmin-go/v2"
	minio "github.com/minio/minio-go/v7"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"harmonycloud.cn/unifiedportal/midware-go/midwares/auth"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/database"
	syserr "harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	appmanagement "harmonycloud.cn/unifiedportal/portal/backend/pkg/feign/app-management"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/backupserver"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/formatter"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/mr"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/snowflake"
)

var _ ResourceInterface = (*resource)(nil)

func NewResource() ResourceInterface {
	return &resource{
		caasDB:     database.CaasDB,
		appService: appmanagement.NewService(),
		snowflake:  snowflake.NewSnowflakeIntf(),
		log:        logger.GetLogger(),
	}
}

type resource struct {
	caasDB     *gorm.DB
	appService *appmanagement.Service
	snowflake  snowflake.SnowflakeIntf
	log        *zap.Logger
}

func (r resource) UpdateStorageServer(_ context.Context, storageServersId string, req backupserver.StorageServersReq) error {
	endPoint := req.S3StorageServersConfig.Url.EndPoint()
	secure := req.S3StorageServersConfig.Url.Protocol == "https"
	username := req.S3StorageServersConfig.Username
	password := req.S3StorageServersConfig.Password
	_, err := getMinioClient(endPoint, username, password, secure)
	if err != nil {
		r.log.Error("failed to connect to minio", zap.Error(err))
		return fmt.Errorf("%w", err)
	}
	var config string
	data, jsonError := json.Marshal(req.S3StorageServersConfig)
	if jsonError != nil {
		r.log.Error("failed to marshal S3StorageServersConfig", zap.Error(jsonError))
		return fmt.Errorf("failed to marshal S3StorageServersConfig: %w", jsonError)
	}
	config = string(data)
	err = r.caasDB.Model(&caas.BackupServer{}).Where("id = ?", storageServersId).Update("config", config).Error
	if err != nil {
		r.log.Error("failed to update backup server", zap.Error(err))
		return err
	}
	return nil
}

func (r resource) CheckBucket(c context.Context, storageServersId string, req backupserver.CheckBucketReq) error {
	client, err := r.getMinioClient(c, storageServersId)
	if err != nil {
		r.log.Error("failed to get minio client", zap.Error(err))
		return err
	}
	ok, err := client.BucketExists(c, req.Name)
	if err != nil {
		r.log.Error("failed to check bucket", zap.Error(err))
		return err
	}
	if !ok {
		return nil
	}
	return errors.New("bucket already exists")
}

func (r resource) GetOrganList(c context.Context) (map[string]any, error) {
	return r.appService.GetOrganMap(auth.GetToken(c))
}

func (r resource) DeleteBucket(c context.Context, storageServersId string, req backupserver.DeleteBucketReq) error {
	//// 检查是否有组织正在使用此存储服务器
	var orgBucketsCount int64
	if err := r.caasDB.Model(&caas.BackupServerOrganizationBucket{}).
		Where("storage_server_id = ? and bucket = ?", storageServersId, req.Name).Count(&orgBucketsCount).Error; err != nil {
		r.log.Error("failed to check organization bucket usage", zap.String("storageServerId", storageServersId), zap.Error(err))
		return err
	}
	if orgBucketsCount > 0 {
		r.log.Warn("storage server is in use by one or more tenants", zap.String("storageServerId", storageServersId))
		return errors.New(syserr.Var.BackupServerIsUsedByOrgan.Message)
	}

	// 检查是否有项目正在使用此存储服务器
	var projBucketsCount int64
	if err := r.caasDB.Model(&caas.BackupServerBucketProject{}).
		Where("storage_server_id = ? and bucket = ?", storageServersId, req.Name).Count(&projBucketsCount).Error; err != nil {
		r.log.Error("failed to check project bucket usage", zap.String("storageServerId", storageServersId), zap.Error(err))
		return err
	}
	if projBucketsCount > 0 {
		r.log.Warn("storage server is in use by one or more projects", zap.String("storageServerId", storageServersId))
		return errors.New(syserr.Var.BackupServerIsUsedByProject.Message)
	}
	// 删除 MinIO 中的桶
	client, err := r.getMinioClient(c, storageServersId)
	if err != nil {
		r.log.Error("failed to get MinIO client", zap.Error(err))
		return err
	}
	objectsCh := client.ListObjects(c, req.Name, minio.ListObjectsOptions{Recursive: true})
	var wg sync.WaitGroup
	var mu sync.Mutex
	var errs []error

	for object := range objectsCh {
		wg.Add(1)
		go func(obj minio.ObjectInfo) {
			defer wg.Done()
			if err := client.RemoveObject(c, req.Name, obj.Key, minio.RemoveObjectOptions{}); err != nil {
				r.log.Error("failed to delete object", zap.String("bucketName", req.Name), zap.String("objectName", obj.Key), zap.Error(err))
				mu.Lock()
				errs = append(errs, err)
				mu.Unlock()
			}
		}(object)
	}
	wg.Wait()

	if len(errs) > 0 {
		return fmt.Errorf("failed to delete objects: %w", syserr.Join(errs...))
	}

	if err := client.RemoveBucket(c, req.Name); err != nil {
		r.log.Error("failed to delete bucket", zap.String("bucketName", req.Name), zap.Error(err))
		return fmt.Errorf("failed to delete bucket: %w", err)
	}
	if err := r.caasDB.Delete(&caas.StorageServer{}, "backup_server_id = ? and bucket = ?", storageServersId, req.Name).Error; err != nil {
		return err
	}
	return nil
}

func (r resource) UnassignBucketToOrgan(c context.Context, storageServersId string, req backupserver.UnassignOrganToBucketReq) error {
	client, err := r.getMinioClient(c, storageServersId)
	if err != nil {
		r.log.Error("failed to get minio client", zap.String("storageServersId", storageServersId), zap.Error(err))
		return err
	}
	_, err = client.GetBucketLocation(c, req.BucketName)
	if err != nil {
		r.log.Error("failed to get bucket ", zap.String("bucketName", req.BucketName), zap.Error(err))
		return err
	}
	var projectCount int64
	err = r.caasDB.Model(&caas.BackupServerBucketProject{}).
		Where("storage_server_id = ? AND bucket = ? AND organ_id = ?", storageServersId, req.BucketName, req.OrganId).
		Count(&projectCount).Error
	if err != nil {
		r.log.Error("failed to count project", zap.String("bucketName", req.BucketName), zap.String("organId", req.OrganId), zap.String("storageServersId", storageServersId), zap.Error(err))
		return err
	}
	if projectCount > 0 {
		r.log.Error("bucket is used by project", zap.String("bucketName", req.BucketName), zap.String("organId", req.OrganId), zap.String("storageServersId", storageServersId))
		return errors.New(syserr.Var.BucketIsUsedByProject.Message)
	}
	if err := r.caasDB.Model(&caas.BackupServerOrganizationBucket{}).
		Where("bucket = ? and organ_id = ?", req.BucketName, req.OrganId).
		Delete(&caas.BackupServerOrganizationBucket{}).Error; err != nil {
		r.log.Error("failed to delete organization bucket", zap.String("bucketName", req.BucketName), zap.String("organId", req.OrganId), zap.String("storageServersId", storageServersId), zap.Error(err))
		return err
	}
	return nil
}

func (r resource) AssignBucketToOrgan(c context.Context, storageServersId string, req backupserver.AssignOrganToBucketReq) error {
	client, err := r.getMinioClient(c, storageServersId)
	if err != nil {
		r.log.Error("failed to get minio client", zap.String("storageServersId", storageServersId), zap.Error(err))
		return err
	}
	_, err = client.GetBucketLocation(c, req.BucketName)
	if err != nil {
		r.log.Error("failed to get bucket ", zap.String("bucketName", req.BucketName), zap.Error(err))
		return err
	}
	storageServersIdInt, err := strconv.Atoi(storageServersId)
	if err != nil {
		r.log.Error("failed to convert storageServersId to int", zap.String("storageServersId", storageServersId), zap.Error(err))
		return err
	}
	organIdInt, err := strconv.Atoi(req.OrganId)
	if err != nil {
		r.log.Error("failed to convert organId to int", zap.String("organId", req.OrganId), zap.Error(err))
		return err
	}
	organizationBucket := &caas.BackupServerOrganizationBucket{
		Id:              r.snowflake.GenerateID(),
		OrganId:         int64(organIdInt),
		StorageServerId: int64(storageServersIdInt),
		Bucket:          req.BucketName,
	}
	if err := r.caasDB.Create(organizationBucket).Error; err != nil {
		r.log.Error("failed to create organization bucket", zap.String("bucketName", req.BucketName), zap.String("organId", req.OrganId), zap.String("storageServersId", storageServersId), zap.Error(err))
		return err
	}
	return nil

}

func (r resource) UpdateBucket(c context.Context, storageServersId string, req backupserver.UpdateBucketReq) error {
	client, err := r.getMinioAdminClient(c, storageServersId)
	if err != nil {
		r.log.Error("failed to get minio client", zap.String("storageServersId", storageServersId), zap.Error(err))
		return err
	}
	if req.Quota {
		bucketSize, err := r.getBucketSize(c, req.Name, storageServersId)
		if err != nil {
			r.log.Error("failed to get bucket size", zap.String("bucketName", req.Name), zap.String("storageServersId", storageServersId), zap.Error(err))
			return fmt.Errorf("failed to get bucket size: %w", err)
		}
		if uint64(bucketSize) > req.QuotaSize {
			return fmt.Errorf("current limit storage must be greater than  %v", formatter.FormattedSize(bucketSize))
		}
		if err := client.SetBucketQuota(c, req.Name, &madmin.BucketQuota{
			Quota: req.QuotaSize,
			Type:  madmin.HardQuota,
		}); err != nil {
			return err
		}
		return nil
	}
	err = client.SetBucketQuota(c, req.Name, &madmin.BucketQuota{Type: ""})
	if err != nil {
		return err
	}
	return nil
}

func (r resource) CreateBuckets(c context.Context, storageServersId string, req backupserver.CreateBucketsReq) error {
	// 获取 MinIO 客户端
	minioClient, err := r.getMinioClient(c, storageServersId)
	if err != nil {
		r.log.Error("failed to get MinIO client", zap.String("storageServerId", storageServersId), zap.Error(err))
		return err
	}
	exists, errBucketExists := minioClient.BucketExists(c, req.Name)
	if errBucketExists == nil && exists {
		r.log.Warn("bucket already exists", zap.String("bucketName", req.Name), zap.String("storageServerId", storageServersId))
		return errors.New(syserr.Var.BucketNameRepeat.Message)
	}
	// 创建桶
	if err := minioClient.MakeBucket(c, req.Name, minio.MakeBucketOptions{Region: CNRegion}); err != nil {
		r.log.Error("failed to create bucket", zap.String("bucketName", req.Name), zap.String("storageServerId", storageServersId), zap.Error(err))
		return err
	}
	r.log.Info("bucket created successfully", zap.String("bucketName", req.Name), zap.String("storageServerId", storageServersId))

	// 设置桶配额
	if req.Quota {
		adminClient, err := r.getMinioAdminClient(c, storageServersId)
		if err != nil {
			r.log.Error("failed to get MinIO admin client for setting quota", zap.String("storageServerId", storageServersId), zap.Error(err))
			return err
		}
		if err := adminClient.SetBucketQuota(c, req.Name, &madmin.BucketQuota{Quota: req.QuotaSize, Type: madmin.HardQuota}); err != nil {
			r.log.Error("failed to set bucket quota", zap.String("bucketName", req.Name), zap.Uint64("quotaSize", req.QuotaSize), zap.String("storageServerId", storageServersId), zap.Error(err))
			return err
		}
		r.log.Info("bucket quota set successfully", zap.String("bucketName", req.Name), zap.Uint64("quotaSize", req.QuotaSize), zap.String("storageServerId", storageServersId))
	}

	r.log.Info("CreateBuckets completed successfully", zap.String("bucketName", req.Name), zap.String("storageServerId", storageServersId))
	return nil
}

func (r resource) DeleteStorageServer(c context.Context, storageServersId string) (bool, error) {
	// 如果服务器连接不上直接可以删除
	var storageServer caas.BackupServer
	if err := r.caasDB.Where("id = ?", storageServersId).First(&storageServer).Error; err != nil {
		r.log.Error("failed to get storage server", zap.String("storageServerId", storageServersId), zap.Error(err))
		return false, err
	}
	if !isMinioServerHealthy(storageServer) {
		err := r.withTransaction(func(tx *gorm.DB) error {
			if err := tx.Delete(&caas.BackupServer{}, "id = ?", storageServersId).Error; err != nil {
				r.log.Error("failed to delete storage server", zap.String("storageServerId", storageServersId), zap.Error(err))
				return err
			}
			if err := tx.Delete(&caas.BackupServerOrganizationBucket{}, "storage_server_id = ?", storageServersId).Error; err != nil {
				r.log.Error("failed to delete organization bucket", zap.String("storageServerId", storageServersId), zap.Error(err))
				return err
			}
			if err := tx.Delete(&caas.BackupServerBucketProject{}, "storage_server_id = ?", storageServersId).Error; err != nil {
				r.log.Error("failed to delete project bucket", zap.String("storageServerId", storageServersId), zap.Error(err))
				return err
			}
			if err := r.caasDB.Delete(&caas.StorageServer{}, "backup_server_id = ? ", storageServersId).Error; err != nil {
				r.log.Error("failed to delete storage server", zap.String("storageServerId", storageServersId), zap.Error(err))
				return err
			}
			return nil
		})
		if err != nil {
			return false, fmt.Errorf("failed to delete storage server: %w", err)
		}
		return true, nil
	}
	var orgBucketsCount int64
	if err := r.caasDB.Model(&caas.BackupServerOrganizationBucket{}).
		Where("storage_server_id = ?", storageServersId).Count(&orgBucketsCount).Error; err != nil {
		r.log.Error("failed to check organization bucket usage", zap.String("storageServerId", storageServersId), zap.Error(err))
		return false, err
	}
	if orgBucketsCount > 0 {
		r.log.Warn("storage server is in use by one or more tenants", zap.String("storageServerId", storageServersId))
		return false, errors.New(syserr.Var.BackupServerIsUsedByOrgan.Message)
	}

	// 检查是否有项目正在使用此存储服务器
	var projBucketsCount int64
	if err := r.caasDB.Model(&caas.BackupServerBucketProject{}).
		Where("storage_server_id = ?", storageServersId).Count(&projBucketsCount).Error; err != nil {
		r.log.Error("failed to check project bucket usage", zap.String("storageServerId", storageServersId), zap.Error(err))
		return false, err
	}
	if projBucketsCount > 0 {
		r.log.Warn("storage server is in use by one or more projects", zap.String("storageServerId", storageServersId))
		return false, errors.New(syserr.Var.BackupServerIsUsedByProject.Message)
	}

	err := r.withTransaction(func(tx *gorm.DB) error {
		err := tx.Delete(&caas.BackupServer{}, "id = ?", storageServersId).Error
		if err != nil {
			return err
		}
		err = tx.Delete(&caas.StorageServer{}, "backup_server_id = ?", storageServersId).Error
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return false, err
	}

	return true, nil
}

func (r resource) CreateStorageServer(c context.Context, req backupserver.StorageServersReq) error {
	// 1. 校验
	var count int64
	if dbError := r.caasDB.
		Model(caas.BackupServer{}).
		Where("nickname = ?", req.NickName).
		Count(&count).Error; dbError != nil {
		return dbError
	}
	if count > 0 {
		r.log.Error("nickname already exists")
		return fmt.Errorf("nickname already exists")
	}
	// 3. 检查数据库中是否存在重复的 IP 和 Port
	var duplicateCount int64
	urlConfig := req.S3StorageServersConfig.Url

	if dbError := r.caasDB.Model(caas.BackupServer{}).
		Where("JSON_UNQUOTE(JSON_EXTRACT(config, '$.url.ip')) = ?", urlConfig.Ip).
		Where("JSON_UNQUOTE(JSON_EXTRACT(config, '$.url.port')) = ?", urlConfig.Port).
		Count(&duplicateCount).Error; dbError != nil {
		return dbError
	}

	if duplicateCount > 0 {
		r.log.Error("backup server with the same IP and Port already exists",
			zap.String("ip", urlConfig.Ip),
			zap.Int("port", urlConfig.Port))
		return fmt.Errorf("backup server with the same IP (%s) and Port (%d) already exists", urlConfig.Ip, urlConfig.Port)
	}

	// 2. 测试minio连接
	endPoint := req.S3StorageServersConfig.Url.EndPoint()
	secure := req.S3StorageServersConfig.Url.Protocol == "https"
	username := req.S3StorageServersConfig.Username
	password := req.S3StorageServersConfig.Password
	_, err := getMinioClient(endPoint, username, password, secure)
	if err != nil {
		r.log.Error("failed to connect to minio", zap.Error(err))
		return err
	}
	// 3. 创建备份服务器
	var config string
	data, jsonError := json.Marshal(req.S3StorageServersConfig)
	if jsonError != nil {
		return fmt.Errorf("failed to marshal S3StorageServersConfig: %w", jsonError)
	}
	config = string(data)
	if dbError := r.caasDB.Create(&caas.BackupServer{
		Id:       r.snowflake.GenerateID(),
		Nickname: req.NickName,
		Type:     req.StorageServerType,
		Config:   config,
	}).Error; dbError != nil {
		return dbError
	}
	return nil
}

func (r resource) GetStorageServerList(c context.Context) ([]*backupserver.ResourceStorageServer, error) {
	var dbStorageServers []caas.BackupServer
	err := r.caasDB.Find(&dbStorageServers).Error
	if err != nil {
		return nil, err
	}
	// 使用 MapReduce 来并行处理 dbStorageServers
	// 这里因为可能存在多个服务器超时，导致接口返回
	storageServers, err := mr.MapReduce(
		func(source chan<- caas.BackupServer) {
			for _, server := range dbStorageServers {
				source <- server
			}
		},
		func(server caas.BackupServer, writer mr.Writer[*backupserver.ResourceStorageServer], cancel func(error)) {
			// 处理单个 BackupServer 的逻辑
			status := checkMinioServerStatus(server)
			writer.Write(&backupserver.ResourceStorageServer{
				StorageServerId: strconv.FormatInt(server.Id, 10),
				NickName:        server.Nickname,
				Type:            server.Type,
				Url:             server.RevertS3StorageServersConfig().Url.SpellUrl(),
				UserName:        server.RevertS3StorageServersConfig().Username,
				Password:        server.RevertS3StorageServersConfig().Password,
				Status:          status,
				CreatedAt:       server.CreateTime,
				UpdatedAt:       server.UpdateTime,
			})
		},
		func(pipe <-chan *backupserver.ResourceStorageServer, writer mr.Writer[[]*backupserver.ResourceStorageServer], cancel func(error)) {
			// 汇总所有处理结果
			var results []*backupserver.ResourceStorageServer
			for server := range pipe {
				results = append(results, server)
			}
			writer.Write(results)
		},
	)

	if err != nil {
		return nil, err
	}
	return storageServers, nil
}

func (r resource) GetBucketList(c context.Context, storageServersId string) (*backupserver.ResourceBucketList, error) {
	var serverInfo caas.BackupServer
	err := r.caasDB.Model(&caas.BackupServer{}).Where("id = ?", storageServersId).First(&serverInfo).Error
	if err != nil {
		r.log.Error("failed to get server info", zap.Error(err))
		return nil, fmt.Errorf("failed to get server info")
	}
	status := checkMinioServerStatus(serverInfo)
	if status.Reason != Health {
		r.log.Error("server is not healthy", zap.Error(err))
		return &backupserver.ResourceBucketList{
			TotalObjects: 0,
			TotalSize:    0,
			Buckets:      nil,
			ServerInfo: backupserver.StorageServerInfo{
				NickName:     serverInfo.Nickname,
				Type:         serverInfo.Type,
				Url:          serverInfo.RevertS3StorageServersConfig().Url.SpellUrl(),
				UserName:     serverInfo.RevertS3StorageServersConfig().Username,
				Password:     serverInfo.RevertS3StorageServersConfig().Password,
				ServerStatus: status,
			},
			Total:        0,
			CurrentTotal: 0,
		}, nil
	}

	client, err := r.getMinioAdminClient(c, storageServersId)
	if err != nil {
		r.log.Error("failed to get minio client", zap.Error(err))
		return nil, err
	}
	accountInfo, err := client.AccountInfo(c, madmin.AccountOpts{})
	if err != nil {
		r.log.Error("failed to get account info", zap.Error(err))
		return nil, err
	}
	// 查询数据库，获取租户与存储桶的关联信息
	var orgBuckets []caas.BackupServerOrganizationBucket
	err = r.caasDB.Where("storage_server_id = ?", storageServersId).Find(&orgBuckets).Error
	if err != nil {
		r.log.Error("failed to query organization buckets", zap.Error(err))
		return nil, err
	}

	// 将查询结果转换为 map，方便查找
	organMap, err := r.appService.GetOrganIdMap(auth.GetToken(c))
	if err != nil {
		r.log.Error("failed to get organ name", zap.Error(err))
	}

	orgBucketMap := make(map[string]backupserver.Organ)
	for _, orgBucket := range orgBuckets {
		organId := strconv.FormatInt(orgBucket.OrganId, 10)
		orgBucketMap[orgBucket.Bucket] = backupserver.Organ{
			OrganId:   organId,
			OrganName: organMap[organId],
		}
	}

	buckets := make([]backupserver.ResourceBucket, len(accountInfo.Buckets))
	var totalObjects uint64
	var totalSize uint64
	for i, bucket := range accountInfo.Buckets {
		totalObjects += bucket.Objects
		totalSize += bucket.Size
		quota, err := client.GetBucketQuota(c, bucket.Name)
		if err != nil {
			r.log.Error("failed to get bucket quota", zap.Error(err))
		}
		organValue := orgBucketMap[bucket.Name]
		buckets[i] = backupserver.ResourceBucket{
			Bucket: backupserver.Bucket{
				Name:    bucket.Name,
				Objects: bucket.Objects,
				Size:    formatter.FormattedSize(bucket.Size),
				Read:    bucket.Access.Read,
				Write:   bucket.Access.Write,
				Quota: backupserver.BucketQuota{
					Quota: formatter.FormattedGiBSize(quota.Quota),
					Type:  backupserver.QuotaType(quota.Type),
				},
			},
			OrganId:    organValue.OrganId,
			OrganName:  organValue.OrganName,
			CreateTime: bucket.Created,
		}
	}

	return &backupserver.ResourceBucketList{
		ServerInfo: backupserver.StorageServerInfo{
			NickName:     serverInfo.Nickname,
			Type:         serverInfo.Type,
			Url:          serverInfo.RevertS3StorageServersConfig().Url.SpellUrl(),
			UserName:     serverInfo.RevertS3StorageServersConfig().Username,
			Password:     serverInfo.RevertS3StorageServersConfig().Password,
			ServerStatus: status,
		},
		TotalObjects: totalObjects,
		TotalSize:    formatter.FormattedSize(totalSize),
		Buckets:      buckets,
		Total:        len(buckets),
	}, nil
}

func (r resource) GetBucketQuota(c context.Context, backupServersId string, bucketName string) (*backupserver.Bucket, error) {
	client, err := r.getMinioAdminClient(c, backupServersId)
	if err != nil {
		return nil, err
	}

	quota, quotaErr := client.GetBucketQuota(c, bucketName)
	if quotaErr != nil {
		return nil, quotaErr
	}

	bucketSize, err := r.getBucketSize(c, bucketName, backupServersId)
	return &backupserver.Bucket{
		Name: bucketName,
		Size: formatter.FormattedSize(bucketSize),
		Quota: backupserver.BucketQuota{
			Quota: formatter.FormattedGiBSize(quota.Quota),
			Type:  backupserver.QuotaType(quota.Type),
		},
	}, nil
}

func (r resource) GetOrganInfo(c context.Context, organId string) (*backupserver.Organ, error) {
	jwt := auth.GetToken(c)
	if jwt == "" {
		r.log.Error("JWT is missing in the request")
		return nil, fmt.Errorf("authorization token is required")
	}
	organInfo, err := r.appService.GetOrganInfo(jwt, organId)
	if err != nil {
		r.log.Error("Failed to get organ info", zap.Error(err))
		return nil, err
	}
	return organInfo, nil
}

// getMinioClient returns a minio client configured with the given endpoint, access key, secret key, and secure flag.
func (r resource) getMinioClient(_ context.Context, storageServersId string) (*minio.Client, error) {
	server := caas.BackupServer{}
	if err := r.caasDB.First(&server, storageServersId).Error; err != nil {
		r.log.Error("not find backupSever", zap.Error(err))
		return nil, err
	}
	endpoint := server.RevertS3StorageServersConfig().Url.EndPoint()
	username := server.RevertS3StorageServersConfig().Username
	password := server.RevertS3StorageServersConfig().Password
	secure := server.RevertS3StorageServersConfig().Url.Protocol == "https"
	return getMinioClient(endpoint, username, password, secure)
}

// getMinioAdminClient returns a minio admin client configured with the given endpoint, access key, secret key, and secure flag.
func (r resource) getMinioAdminClient(_ context.Context, storageServersId string) (*madmin.AdminClient, error) {
	server := caas.BackupServer{}
	if err := r.caasDB.First(&server, storageServersId).Error; err != nil {
		r.log.Error("not find backupSever", zap.Error(err))
		return nil, err
	}
	endpoint := server.RevertS3StorageServersConfig().Url.EndPoint()
	username := server.RevertS3StorageServersConfig().Username
	password := server.RevertS3StorageServersConfig().Password
	secure := server.RevertS3StorageServersConfig().Url.Protocol == "https"
	return getMinioAdminClient(endpoint, username, password, secure)
}

func (r resource) getBucketSize(c context.Context, bucketName string, storageServersId string) (int64, error) {
	client, err := r.getMinioClient(c, storageServersId)
	if err != nil {
		return 0, fmt.Errorf("failed to get minio client: %w", err)
	}
	objectCh := client.ListObjects(c, bucketName, minio.ListObjectsOptions{
		Recursive: true,
	})
	var size int64
	for object := range objectCh {
		if object.Err != nil {
			return 0, fmt.Errorf("failed to list objects: %w", object.Err)
		}
		size += object.Size
	}
	return size, nil
}

func (r resource) withTransaction(fn func(tx *gorm.DB) error) error {
	tx := r.caasDB.Begin()
	if err := fn(tx); err != nil {
		tx.Rollback()
		return err
	}
	return tx.Commit().Error
}
