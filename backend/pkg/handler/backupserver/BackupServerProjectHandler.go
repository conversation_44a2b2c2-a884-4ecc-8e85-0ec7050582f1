package backupserver

import (
	"context"
	"encoding/base64"
	"sort"
	"strconv"

	madmin "github.com/minio/madmin-go/v2"
	minio "github.com/minio/minio-go/v7"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"gorm.io/gorm"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/database"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/feign/olympuscore"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/backupserver"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/formatter"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/mr"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/snowflake"
)

var _ ProjectInterface = (*Project)(nil)

func NewProject() ProjectInterface {
	return &Project{
		caasDB:             database.CaasDB,
		snowflake:          snowflake.NewSnowflakeIntf(),
		log:                logger.GetLogger(),
		olympuscoreService: olympuscore.NewService(),
	}
}

type Project struct {
	caasDB             *gorm.DB
	snowflake          snowflake.SnowflakeIntf
	log                *zap.Logger
	olympuscoreService *olympuscore.Service
}

func (p Project) GetStorageServerInfoByProjectId(_ context.Context, projectId string) ([]*backupserver.ProjectStorageServerInfo, error) {
	// 1. 获取项目下的存储服务器与桶的关系
	var backupServerBucketProjects []caas.BackupServerBucketProject
	if err := p.caasDB.Where("project_id = ?", projectId).Find(&backupServerBucketProjects).Error; err != nil {
		p.log.Error("get backupServerBucketProject error", zap.Error(err))
		return nil, err
	}

	// 2. 提取唯一的存储服务器ID
	storageServerIds := lo.Uniq(lo.Map(backupServerBucketProjects, func(item caas.BackupServerBucketProject, _ int) int64 {
		return item.StorageServerId
	}))

	// 3. 如果没有找到任何存储服务器ID，则返回空结果
	if len(storageServerIds) == 0 {
		return []*backupserver.ProjectStorageServerInfo{}, nil
	}

	// 4. 一次性查询所有服务器信息
	var servers []caas.BackupServer
	if err := p.caasDB.Where("id IN ?", storageServerIds).Find(&servers).Error; err != nil {
		p.log.Error("failed to get backup servers", zap.Error(err))
		return nil, err
	}

	// 5. 创建服务器ID到服务器对象的映射
	serverMap := make(map[int64]caas.BackupServer)
	for _, server := range servers {
		serverMap[server.Id] = server
	}

	// 6. 使用mr.MapReduce并行处理服务器健康检查和信息构建
	storageServerInfos, err := mr.MapReduce(
		func(source chan<- int64) {
			for _, id := range storageServerIds {
				source <- id
			}
		},
		func(serverId int64, writer mr.Writer[*backupserver.ProjectStorageServerInfo], cancel func(error)) {
			server, exists := serverMap[serverId]
			if !exists {
				p.log.Warn("server not found in map", zap.Int64("server_id", serverId))
				return
			}

			// 检查服务器状态
			status := checkMinioServerStatus(server)

			// 获取服务器配置
			serverConfig := server.RevertS3StorageServersConfig()
			// password base64 解密
			password, err := base64.StdEncoding.DecodeString(serverConfig.Password)
			if err != nil {
				p.log.Error("failed to decode password", zap.Error(err))
				return
			}
			// 构建并写入结果
			writer.Write(&backupserver.ProjectStorageServerInfo{
				StorageServerId: strconv.FormatInt(serverId, 10),
				StorageServerInfo: backupserver.StorageServerInfo{
					NickName:     server.Nickname,
					Type:         server.Type,
					Url:          serverConfig.Url.SpellUrl(),
					UserName:     serverConfig.Username,
					Password:     string(password),
					ServerStatus: status,
				},
			})
		},
		func(pipe <-chan *backupserver.ProjectStorageServerInfo, writer mr.Writer[[]*backupserver.ProjectStorageServerInfo], cancel func(error)) {
			// 收集所有结果
			var results []*backupserver.ProjectStorageServerInfo
			for info := range pipe {
				results = append(results, info)
			}
			writer.Write(results)
		},
	)

	if err != nil {
		p.log.Error("failed to process storage servers", zap.Error(err))
		return nil, err
	}

	// 7. 排序
	sort.Slice(storageServerInfos, func(i, j int) bool {
		return storageServerInfos[i].StorageServerId < storageServerInfos[j].StorageServerId
	})
	return storageServerInfos, nil
}

func (p Project) GetStorageServerListByProjectId(c context.Context, projectId string) ([]*backupserver.ProjectStorageServer, error) {
	// 1. 获取项目下的存储服务器与桶的关系
	var backupServerBucketProjects []caas.BackupServerBucketProject
	if err := p.caasDB.Where("project_id = ?", projectId).Find(&backupServerBucketProjects).Error; err != nil {
		p.log.Error("get backupServerBucketProject error", zap.Error(err))
		return nil, err
	}

	// 构建存储服务器和桶的映射
	storageServerMap := make(map[int64][]caas.BackupServerBucketProject)
	for _, bucket := range backupServerBucketProjects {
		storageServerMap[bucket.StorageServerId] = append(storageServerMap[bucket.StorageServerId], bucket)
	}

	// 2. 使用 mr.MapReduce 并行处理每个存储服务器
	storageServers, err := mr.MapReduce(
		func(source chan<- int64) {
			for storageServerId := range storageServerMap {
				source <- storageServerId
			}
		},
		func(storageServerId int64, writer mr.Writer[*backupserver.ProjectStorageServer], cancel func(error)) {
			// 获取存储服务器信息
			var storageServer caas.BackupServer
			if err := p.caasDB.Model(&caas.BackupServer{}).Where("id = ?", storageServerId).First(&storageServer).Error; err != nil {
				p.log.Error("failed to get storage server", zap.Error(err))
				return
			}
			// 检查存储服务器的健康状态
			status := checkMinioServerStatus(storageServer)
			if status.Reason != Health {
				// 如果服务器不健康，直接返回基础信息
				writer.Write(&backupserver.ProjectStorageServer{
					StorageServerId: strconv.FormatInt(storageServer.Id, 10),
					NickName:        storageServer.Nickname,
					Type:            storageServer.Type,
					Status:          status,
					Objects:         0,
					Size:            0,
					BucketList:      nil,
					CreatedAt:       backupserver.CustomTime{Time: storageServer.CreateTime},
				})
				return
			}
			// 获取 MinIO 客户端
			client, err := p.getMinioAdminClient(c, strconv.FormatInt(storageServerId, 10))
			if err != nil {
				p.log.Error("get minio client error", zap.Error(err))
				return
			}
			// 获取账户信息
			accountInfo, err := client.AccountInfo(c, madmin.AccountOpts{})
			if err != nil {
				p.log.Error("get account info error", zap.Error(err))
				return
			}
			// 构建桶信息的映射
			bucketInfoMap := make(map[string]madmin.BucketAccessInfo)
			for _, bucketInfo := range accountInfo.Buckets {
				bucketInfoMap[bucketInfo.Name] = bucketInfo
			}

			// 构建当前存储服务器的桶信息列表
			var objects, size uint64
			var projectBuckets []backupserver.ProjectBucket
			for _, bucketProject := range storageServerMap[storageServerId] {
				bucketInfo, exists := bucketInfoMap[bucketProject.Bucket]
				if !exists {
					p.log.Warn("bucket info not found in account info", zap.String("bucket", bucketProject.Bucket))
					continue
				}

				// 累积对象数量和大小
				objects += bucketInfo.Objects
				size += bucketInfo.Size

				// 添加到桶列表
				projectBuckets = append(projectBuckets, backupserver.ProjectBucket{
					Name:    bucketProject.Bucket,
					Objects: bucketInfo.Objects,
					Size:    formatter.FormattedSize(bucketInfo.Size),
					Quota: backupserver.BucketQuota{
						Quota: formatter.FormattedGiBSize(bucketInfo.Details.Quota.Quota),
						Type:  backupserver.QuotaType(bucketInfo.Details.Quota.Type),
					},
					CreateTime: backupserver.CustomTime{Time: bucketProject.CreateTime},
				})
			}

			// 构建存储服务器信息
			writer.Write(&backupserver.ProjectStorageServer{
				StorageServerId: strconv.FormatInt(storageServer.Id, 10),
				NickName:        storageServer.Nickname,
				Type:            storageServer.Type,
				Objects:         objects,
				Size:            formatter.FormattedSize(size),
				Status:          status,
				BucketList:      projectBuckets,
				CreatedAt:       backupserver.CustomTime{Time: storageServer.CreateTime},
			})
		},
		func(pipe <-chan *backupserver.ProjectStorageServer, writer mr.Writer[[]*backupserver.ProjectStorageServer], cancel func(error)) {
			// 汇总所有处理结果
			var results []*backupserver.ProjectStorageServer
			for server := range pipe {
				results = append(results, server)
			}
			writer.Write(results)
		},
	)

	if err != nil {
		return nil, err
	}

	return storageServers, nil
}

// getMinioClient returns a minio client configured with the given endpoint, access key, secret key, and secure flag.
func (p Project) getMinioClient(_ context.Context, storageServersId string) (*minio.Client, error) {
	server := caas.BackupServer{}
	if err := p.caasDB.First(&server, storageServersId).Error; err != nil {
		p.log.Error("not find backupSever", zap.Error(err))
		return nil, err
	}
	endpoint := server.RevertS3StorageServersConfig().Url.EndPoint()
	username := server.RevertS3StorageServersConfig().Username
	password := server.RevertS3StorageServersConfig().Password
	secure := server.RevertS3StorageServersConfig().Url.Protocol == "https"
	return getMinioClient(endpoint, username, password, secure)
}

// getMinioAdminClient returns a minio admin client configured with the given endpoint, access key, secret key, and secure flag.
func (p Project) getMinioAdminClient(_ context.Context, storageServersId string) (*madmin.AdminClient, error) {
	server := caas.BackupServer{}
	if err := p.caasDB.First(&server, storageServersId).Error; err != nil {
		p.log.Error("not find backupSever", zap.Error(err))
		return nil, err
	}
	endpoint := server.RevertS3StorageServersConfig().Url.EndPoint()
	username := server.RevertS3StorageServersConfig().Username
	password := server.RevertS3StorageServersConfig().Password
	secure := server.RevertS3StorageServersConfig().Url.Protocol == "https"
	return getMinioAdminClient(endpoint, username, password, secure)
}
