package backupserver

import (
	"context"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/backupserver"
)

type ResourceInterface interface {
	CreateStorageServer(c context.Context, req backupserver.StorageServersReq) error
	DeleteStorageServer(c context.Context, storageServersId string) (bool, error)
	GetStorageServerList(c context.Context) ([]*backupserver.ResourceStorageServer, error)
	GetBucketList(c context.Context, storageServersId string) (*backupserver.ResourceBucketList, error)
	CreateBuckets(c context.Context, storageServersId string, req backupserver.CreateBucketsReq) error
	UpdateBucket(c context.Context, storageServersId string, req backupserver.UpdateBucketReq) error
	AssignBucketToOrgan(c context.Context, storageServersId string, req backupserver.AssignOrganToBucketReq) error
	Unassign<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(c context.Context, storageServersId string, req backupserver.UnassignOrganToBucketReq) error
	DeleteBucket(c context.Context, storageServersId string, req backupserver.DeleteBucketReq) error
	GetOrganList(c context.Context) (map[string]any, error)
	CheckBucket(c context.Context, storageServersId string, req backupserver.CheckBucketReq) error
	UpdateStorageServer(c context.Context, storageServersId string, req backupserver.StorageServersReq) error
	GetBucketQuota(c context.Context, backupServersId string, bucketName string) (*backupserver.Bucket, error)
}

type OrganInterface interface {
	AssignProjectToBucket(c context.Context, organId string, req backupserver.AssignProjectToBucketReq) error
	UnassignProjectToBucket(c context.Context, organId string, req backupserver.UnassignProjectToBucketReq) error
	GetStorageServerListByOrganId(c context.Context, organId string) ([]*backupserver.OrganStorageServer, error)
	GetProjectList(c context.Context, id string) ([]*backupserver.Project, error)
}

type ProjectInterface interface {
	GetStorageServerListByProjectId(c context.Context, projectId string) ([]*backupserver.ProjectStorageServer, error)
	GetStorageServerInfoByProjectId(c context.Context, projectId string) ([]*backupserver.ProjectStorageServerInfo, error)
}
