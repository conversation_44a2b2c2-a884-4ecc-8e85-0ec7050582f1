package backupserver

import (
	"context"
	"encoding/base64"
	"fmt"
	"strings"
	"time"

	madmin "github.com/minio/madmin-go/v2"
	minio "github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/backupserver"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
)

const (
	Health                   = "Health"                   // 健康状态
	URLFailed                = "URLFailed"                // URL访问失败
	ConnectionFailed         = "ConnectionFailed"         // 连接失败
	UsernameOrPassWordFailed = "UsernameOrPassWordFailed" // 用户名或密码错误
	Unknown                  = "Unknown"                  // 未知错误
	ProtocolFailed           = "ProtocolFailed"           // 协议错误
)

const (
	CNRegion = "cn-north-1"
)

// getMinioClient
// 获取Minio客户端
func getMinioClient(endpoint, username, password string, secure bool) (*minio.Client, error) {
	// 解码密码
	decodedSecretKey, _ := base64.StdEncoding.DecodeString(password)
	// 创建 MinIO 客户端
	client, err := minio.New(endpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(username, string(decodedSecretKey), ""),
		Secure: secure,
	})
	if err != nil {
		return nil, fmt.Errorf("MinIO client creation error: %v", err)
	}
	// 默认超时
	timeout := 2 * time.Second
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	_, err = client.ListBuckets(ctx)
	if err != nil {
		return nil, fmt.Errorf("error listing buckets: %v", err)
	}

	return client, nil
}

// getMinioAdminClient
// 获取Minio管理员客户端
func getMinioAdminClient(endpoint, username, password string, secure bool) (*madmin.AdminClient, error) {
	decodedSecretKey, _ := base64.StdEncoding.DecodeString(password)
	client, err := madmin.New(endpoint, username, string(decodedSecretKey), secure)
	if err != nil {
		return nil, err
	}
	// 默认超时
	timeout := 2 * time.Second
	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	_, err = client.AccountInfo(ctx, madmin.AccountOpts{})
	if err != nil {
		return nil, fmt.Errorf("MinIO admin client creation error: %v", err)
	}
	return client, nil
}

func checkMinioServerStatus(server caas.BackupServer) backupserver.Status {
	status := backupserver.Status{
		IsHealthy: false,
		Reason:    "",
	}
	endpoint := server.RevertS3StorageServersConfig().Url.EndPoint()
	username := server.RevertS3StorageServersConfig().Username
	password := server.RevertS3StorageServersConfig().Password
	secure := server.RevertS3StorageServersConfig().Url.Protocol == "https"
	_, err := getMinioClient(endpoint, username, password, secure)
	if err != nil {
		switch {
		case strings.Contains(err.Error(), "connection refused"),
			strings.Contains(err.Error(), "S3 API Requests must be made to API port"):
			//status.Reason = URLFailed
			status.Reason = ConnectionFailed
		case strings.Contains(err.Error(), "gave HTTP response"),
			strings.Contains(err.Error(), "gave HTTPs response"):
			//status.Reason = ProtocolFailed
			status.Reason = ConnectionFailed
		case strings.Contains(err.Error(), "failed to connect to minio"):
			status.Reason = ConnectionFailed
		case strings.Contains(err.Error(), "Check your key"):
			status.Reason = UsernameOrPassWordFailed
		default:
			status.Reason = ConnectionFailed
		}
		return status
	}
	status.IsHealthy = true
	status.Reason = Health
	return status

}

func isMinioServerHealthy(server caas.BackupServer) bool {
	return checkMinioServerStatus(server).IsHealthy
}
