package monitoring

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"strings"

	"harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/cloudservice"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/monitoring"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	corev1 "k8s.io/api/core/v1"
	label "k8s.io/apimachinery/pkg/labels"
	runtimeclient "sigs.k8s.io/controller-runtime/pkg/client"
)

var defaultVariables = map[string]string{
	"NodeName":         "var-instance",
	"Namespace":        "var-namespace",
	"NamespaceList":    "var-namespaceList",
	"WorkloadKind":     "var-type",
	"WorkloadName":     "var-workloadName",
	"PodName":          "var-pod",
	"CloudServiceName": "var-cloudservice",
	"ComponentName":    "var-component",
	"PVCName":          "var-volume",
	"WorkloadList":     "var-workloadList",
	"Datasource":       "var-datasource",
}

type dashboardHandler struct {
	cloudServiceHandler cloudservice.HandlerIntf
}

func (d *dashboardHandler) ListDashboards(ctx context.Context, key monitoring.ObjectKey) ([]monitoring.Dashboard, error) {
	labels := map[string]string{
		monitoring.LabelKeyGrafana: monitoring.LabelValueGrafana,
		utils.LabelKeyScopeLevel:   string(key.Level),
	}
	switch key.Level {
	case monitoring.LevelCluster:
		return d.listDashboardsByLabels(ctx, key, labels)
	case monitoring.LevelWorkload:
		return d.listDashboardsByLabels(ctx, key, labels)
	case monitoring.LevelPod:
		return d.listDashboardsByLabels(ctx, key, labels)
	case monitoring.LevelCloudService:
		if key.CloudServiceName != "" {
			labels[utils.LabelKeyApplication] = key.CloudServiceName
		}
		return d.listDashboardsByLabels(ctx, key, labels)
	case monitoring.LevelComponent:
		return d.listComponentDashboards(ctx, key)
	}

	return nil, nil
}

func (d *dashboardHandler) listComponentDashboards(ctx context.Context, key monitoring.ObjectKey) ([]monitoring.Dashboard, error) {
	cloudComponentWorkloads, err := d.cloudServiceHandler.CloudComponentWorkloads(ctx, models.CloudComponentWorkloadReadParam{
		CloudServiceName:   key.CloudServiceName,
		CloudComponentName: key.CloudComponentName,
		Cluster:            key.Cluster,
	})
	if err != nil {
		logger.GetLogger().Error("")
	}

	var dashboards []monitoring.Dashboard

	//获取内置的dashboard
	if len(cloudComponentWorkloads) > 0 {
		labels := map[string]string{
			monitoring.LabelKeyGrafana: monitoring.LabelValueGrafana,
			utils.LabelKeyScopeLevel:   string(monitoring.LevelWorkload),
		}
		key.Namespace = cloudComponentWorkloads[0].Namespace
		key.WorkloadKind = strings.ToLower(string(cloudComponentWorkloads[0].WorkloadType))
		key.WorkloadName = cloudComponentWorkloads[0].Name
		key.Datasource = key.Cluster
		//拼接datasource workloadList
		var workloadList string
		namespaceSet := make(map[string]bool)
		namespaceList := ""
		for _, workload := range cloudComponentWorkloads {
			if workload.Name == "" {
				continue
			}
			workloadList = fmt.Sprintf("%s|%s", workloadList, workload.Name)
			if !namespaceSet[workload.Namespace] {
				namespaceList = fmt.Sprintf("%s|%s", namespaceList, workload.Namespace)
				namespaceSet[workload.Namespace] = true
			}
		}

		key.WorkloadList = workloadList[1:]
		key.NamespaceList = namespaceList[1:]

		dashboards, err = d.listDashboardsByLabels(ctx, key, labels)

		if err != nil {
			return nil, err
		}

	}

	//获取组件的dashboard
	labels := map[string]string{
		monitoring.LabelKeyGrafana: monitoring.LabelValueGrafana,
		utils.LabelKeyScopeLevel:   string(monitoring.LevelComponent),
	}
	if key.CloudServiceName != "" {
		labels[utils.LabelKeyApplication] = key.CloudServiceName
	}
	if key.CloudComponentName != "" {
		labels[utils.CloudComponentNameLabelKey] = key.CloudComponentName
	}
	scopeDashboards, err := d.listDashboardsByLabels(ctx, key, labels)
	if err == nil {
		dashboards = append(dashboards, scopeDashboards...)
	} else {
		logger.GetLogger().Error("")
	}

	return dashboards, nil
}

func (d *dashboardHandler) listDashboardsByLabels(ctx context.Context, key monitoring.ObjectKey, labels map[string]string) ([]monitoring.Dashboard, error) {
	var dashboards []monitoring.Dashboard
	cms := corev1.ConfigMapList{}
	if err := client.GetLocalCluster().GetClient().GetCtrlClient().List(ctx, &cms, &runtimeclient.ListOptions{
		LabelSelector: label.SelectorFromSet(labels),
	}); err != nil {
		return nil, err
	}
	for _, cm := range cms.Items {
		params := GetDashboardParams(cm, key)
		for k, v := range cm.Data {
			var model monitoring.DashboardModel
			err := json.Unmarshal([]byte(v), &model)
			if err != nil {
				return nil, err
			}
			if model.UID == "" {
				logger.GetLogger().Warn(fmt.Sprintf("cm %s/%s uid is empty", cm.Name, k))
				continue
			}
			url := fmt.Sprintf(monitoring.DashboardURL, model.UID, params)
			dashboard := monitoring.Dashboard{
				UID:             model.UID,
				Title:           model.Title,
				ConfigName:      cm.Name,
				ConfigNamespace: cm.Namespace,
				URL:             url,
				Level:           monitoring.ScopeLevel(cm.Labels[utils.LabelKeyScopeLevel]),
			}
			dashboards = append(dashboards, dashboard)
		}
	}
	return dashboards, nil
}

func GetDashboardParams(cm corev1.ConfigMap, key monitoring.ObjectKey) string {
	variables := defaultVariables

	var params string
	keyType := reflect.TypeOf(key)
	keyValue := reflect.ValueOf(key)
	for i := 0; i < keyValue.NumField(); i++ {
		if keyValue.Field(i).CanInterface() {
			fieldName := variables[keyType.Field(i).Name]
			fieldValue := keyValue.Field(i).Interface()
			if fieldName != "" && fmt.Sprint(fieldValue) != "" {
				if params == "" {
					params = fmt.Sprintf("?%s=%s", fieldName, fieldValue)
				} else {
					params = fmt.Sprintf("%s&%s=%s", params, fieldName, fieldValue)
				}
			}
		}
	}
	return params
}

func deduplication(dashboards []monitoring.Dashboard) []monitoring.Dashboard {
	res := make([]monitoring.Dashboard, 0)
	set := make(map[string]bool)
	for _, dashboard := range dashboards {
		if _, exists := set[dashboard.UID]; !exists {
			res = append(res, dashboard)
			set[dashboard.UID] = true
		}
	}
	return res
}
