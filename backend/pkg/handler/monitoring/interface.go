package monitoring

import (
	"context"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/cloudservice"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/monitoring"
)

func NewDashboardHandler() DashboardIntf {
	return &dashboardHandler{
		cloudServiceHandler: cloudservice.NewCloudServiceHandler(),
	}
}

type DashboardIntf interface {
	ListDashboards(ctx context.Context, key monitoring.ObjectKey) ([]monitoring.Dashboard, error)
}
