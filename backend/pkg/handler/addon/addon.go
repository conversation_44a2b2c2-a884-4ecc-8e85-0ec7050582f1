package addon

import (
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"reflect"
	"sort"
	"strconv"
	"strings"
	"time"

	database_aop "harmonycloud.cn/unifiedportal/translate-sdk-golang/database-aop"

	"k8s.io/utils/pointer"

	"go.uber.org/zap"
	stellariscommon "harmonycloud.cn/stellaris/pkg/apis/stellaris/common"
	stellarisv1alhpha1 "harmonycloud.cn/stellaris/pkg/apis/stellaris/v1alpha1"
	hcclient "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/database"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	addonmodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/addon"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/lock"
	apiextensionsv1 "k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

const (
	Kind                     = "ClusterAddon"
	APIVersion               = "stellaris.harmonycloud.cn/v1alpha1"
	conditionUnhealthySuffix = "Unhealthy"
)

func GetAddonNamespace(clusterName string) string {
	return fmt.Sprintf("stellaris-workspace-%s", clusterName)
}

func getCreatAddonLockKey(clusterName string, componentName string) string {
	return fmt.Sprintf("olympus-portal::cluster::%s::addon::%s", clusterName, componentName)
}

func NewHandler() Handler {
	return &translateAddonHandler{
		h: &addonHandler{
			distributedLock: lock.NewRDSDistributeLock(database.RDS),
			logger:          logger.GetLogger(),
		},
	}
}

type translateAddonHandler struct {
	h Handler
}

func (handler *translateAddonHandler) SwitchComponent(ctx context.Context, clusterName string, request addonmodel.ComponentDto) error {
	return handler.h.SwitchComponent(ctx, clusterName, request)
}
func (handler *translateAddonHandler) CancelComponent(ctx context.Context, clusterName, componentName string) error {
	return handler.h.CancelComponent(ctx, clusterName, componentName)
}
func (handler *translateAddonHandler) UpdateComponent(ctx context.Context, clusterName, componentName string, request addonmodel.ComponentDto) error {
	return handler.h.UpdateComponent(ctx, clusterName, componentName, request)
}
func (handler *translateAddonHandler) GetComponent(ctx context.Context, clusterName, componentName string, baseFlag bool) (*addonmodel.ComponentDto, error) {
	return handler.h.GetComponent(ctx, clusterName, componentName, baseFlag)
}
func (handler *translateAddonHandler) ListComponent(ctx context.Context, clusterName string) (addonmodel.ListResponse, error) {
	resp, err := handler.h.ListComponent(ctx, clusterName)
	database_aop.DoTranslate(ctx, resp, err)
	return resp, err
}
func (handler *translateAddonHandler) ScanComponent(ctx context.Context, clusterName string) (addonmodel.ListComponentScanResponse, error) {
	resp, err := handler.h.ScanComponent(ctx, clusterName)
	database_aop.DoTranslate(ctx, resp, err)
	return resp, err
}
func (handler *translateAddonHandler) BatchInstall(ctx context.Context, clusterName string, componentList addonmodel.ListComponentRequest) error {
	return handler.h.BatchInstall(ctx, clusterName, componentList)
}
func (handler *translateAddonHandler) GetClusterNetworks(ctx context.Context, clusterName string) ([]string, error) {
	return handler.h.GetClusterNetworks(ctx, clusterName)
}

type addonHandler struct {
	distributedLock lock.DistributeLock
	logger          *zap.Logger
}

func (handler *addonHandler) SwitchComponent(ctx context.Context, clusterName string, request addonmodel.ComponentDto) error {
	lockKey := getCreatAddonLockKey(clusterName, request.Name)
	h, err := handler.distributedLock.Lock(ctx, lockKey)
	if err != nil {
		return err
	}
	defer func() {
		_ = h.UnLock(ctx)
	}()

	// 1.获取集群 cluster
	cluster := hcclient.GetHubCluster()

	// 2.判断插件插件存在
	_, err = getAddon(ctx, clusterName, request.Name)
	if err == nil {
		// 插件已存在暂时忽略
		return nil
	}
	if errors.IsNotFound(err) {
		// 不存在的话，则创建(创建代码可以抽离出来)
		addon := stellarisv1alhpha1.ClusterAddon{
			TypeMeta: metav1.TypeMeta{
				Kind:       Kind,
				APIVersion: APIVersion,
			},
			ObjectMeta: metav1.ObjectMeta{
				Name:      request.Name,
				Namespace: GetAddonNamespace(clusterName),
			},
		}
		//  type ,name
		addon.Spec.Type = stellarisv1alhpha1.ClusterAddonType(request.Type)
		err = convertComponentDtoAsAddon(&addon, request)
		if err != nil {
			return err
		}
		err = cluster.GetClient().GetCtrlClient().Create(ctx, &addon)
		if err != nil {
			return err
		}
		return nil
	}
	return err
}

func (handler *addonHandler) CancelComponent(ctx context.Context, clusterName, componentName string) error {
	// 获取 cluster  client
	cluster := hcclient.GetHubCluster()
	deleteAddon, err := getAddon(ctx, clusterName, componentName)
	if err != nil {
		return err
	}
	if err = cluster.GetClient().GetCtrlClient().Delete(ctx, &deleteAddon); err != nil {
		return err
	}

	return nil
}

//

func (handler *addonHandler) UpdateComponent(ctx context.Context, clusterName, componentName string, request addonmodel.ComponentDto) error {
	lockKey := getCreatAddonLockKey(clusterName, componentName)

	h, err := handler.distributedLock.Lock(ctx, lockKey)
	if err != nil {
		return err
	}
	defer func(h lock.DistributeLockHandler, ctx context.Context) {
		err := h.UnLock(ctx)
		if err != nil {
			// 解锁失败记录日志
			handler.logger.Error("unlock failed,lock key =[%s]", zap.String("lockKey", lockKey), zap.Error(err))
		}
	}(h, ctx)

	cluster := hcclient.GetHubCluster()
	addon, err := getAddon(ctx, clusterName, componentName)
	if err != nil {
		return err
	}
	addonCopy := addon.DeepCopy()
	if !request.BaseFlag {
		err = convertComponentDtoAsAddon(&addon, request)
		if err != nil {
			return err
		}
	} else {
		err = convertBaseComponentAsAddon(&addon, request)
		if err != nil {
			return err
		}
	}
	err = cluster.GetClient().GetCtrlClient().Patch(ctx, &addon, client.MergeFrom(addonCopy))
	if err != nil {
		return err
	}
	return nil
}

func (handler *addonHandler) GetComponent(ctx context.Context, clusterName, componentName string, baseFlag bool) (*addonmodel.ComponentDto, error) {
	addon, err := getAddon(ctx, clusterName, componentName)
	if err != nil {
		return nil, err
	}
	// 2.封装返回参数
	var componentDto = &addonmodel.ComponentDto{}
	componentDto.BaseFlag = baseFlag

	// 非基础组件
	if !baseFlag {
		// config
		err = convertAddonAsResponse(componentDto, addon)
		if err != nil {
			return nil, err
		}
		// health and status
		if addon.Status.HealthCheck != nil {
			convertAddonStatusAsResponse(componentDto, addon)
			if addon.Status.HealthCheck != nil && len(addon.Status.HealthCheck.Conditions) > 0 {
				for _, condition := range addon.Status.HealthCheck.Conditions {
					// crd 是unhealthy 健康是false 不健康是true 返回给前端的数据取反 正常为true 不正常为false
					if condition.Status == constants.ADDON_UNHEALTH {
						componentDto.Status = constants.ERROR
						break
					}
					componentDto.Status = constants.RUNNING
				}
			} else {
				componentDto.Status = constants.ON_SWITCH
			}
		}
	} else {
		componentDto.Name = componentName
		// 封装基础组件 addon ==> componentDto
		err = convertToBaseComponentDTO(componentDto, addon)
		if err != nil {
			return nil, err
		}
		// 健康检查
		if addon.Status.HealthCheck != nil {
			convertAddonStatusAsResponse(componentDto, addon)
			healthCondition := addon.Status.HealthCheck.Conditions
			componentDto.HealthcheckConditionList = make([]addonmodel.HealthcheckCondition, len(healthCondition))
			componentDto.Status = constants.RUNNING
			for i, condition := range healthCondition {
				componentDto.HealthcheckConditionList[i] = convert_StellarisCondition_To_HealthcheckCondition(condition)
				// 存在 健康检查出现问题，error
				if condition.Status == constants.ADDON_UNHEALTH {
					componentDto.Status = constants.ERROR
				}
			}
		} else {
			componentDto.Status = constants.ON_SWITCH
		}
	}
	return componentDto, nil
}

func convert_StellarisCondition_To_HealthcheckCondition(condition stellariscommon.Condition) addonmodel.HealthcheckCondition {
	healthcheckCondition := addonmodel.HealthcheckCondition{
		Timestamp: condition.Timestamp.UTC().Format(time.RFC3339),
		Message:   condition.Message,
		Reason:    condition.Reason,
		Type:      condition.Type,
	}
	// crd 是unhealthy 健康是false 不健康是true
	status := strings.EqualFold(string(metav1.ConditionTrue), condition.Status)
	if strings.HasSuffix(condition.Type, conditionUnhealthySuffix) {
		healthcheckCondition.Status = pointer.Bool(!status)
	} else {
		healthcheckCondition.Status = pointer.Bool(status)
	}
	return healthcheckCondition
}

func (handler *addonHandler) ListComponent(ctx context.Context, clusterName string) (addonmodel.ListResponse, error) {
	// 遍历addons
	list := make([]addonmodel.ComponentListDTO, 0)
	// 	通用插件
	for _, addonEnum := range constants.AddonEnums {
		addon, err := getAddon(ctx, clusterName, addonEnum.EnName)
		// 没找到就是未接入的插件
		if errors.IsNotFound(err) {
			componentDto := addonmodel.ComponentListDTO{
				Name:        addonEnum.EnName,
				EnName:      addonEnum.EnName,
				ChName:      addonEnum.CnName,
				Description: addonEnum.Description,
				Status:      constants.UN_SWITCH,
				BaseFlag:    false,
			}
			list = append(list, componentDto)
			continue
		}
		// 	接入的插件
		componentDto := addonmodel.ComponentListDTO{
			Name:        addon.Name,
			EnName:      addonEnum.EnName,
			ChName:      addonEnum.CnName,
			Description: addonEnum.Description,
			Status:      constants.ON_SWITCH,
			BaseFlag:    false,
		}
		if addon.Status.HealthCheck == nil {
			// 正在启用中，暂时还没有健康检查状态
			componentDto.Status = constants.ON_SWITCH
		} else {
			// 健康检查
			componentDto.Status = constants.RUNNING
			for _, healthCondition := range addon.Status.HealthCheck.Conditions {
				if healthCondition.Status == constants.ADDON_UNHEALTH {
					componentDto.Status = constants.ERROR
					break
				}
			}
		}

		list = append(list, componentDto)
	}

	// 基础插件
	for _, baseAddonEnum := range constants.BaseAddonEnums {
		addon, err := getAddon(ctx, clusterName, baseAddonEnum.Name)
		// 没找到的就是未接入的组件
		if errors.IsNotFound(err) {
			componentDto := addonmodel.ComponentListDTO{
				Name:        baseAddonEnum.Name,
				EnName:      baseAddonEnum.PlatformEnName,
				ChName:      baseAddonEnum.PlatformChName,
				Description: baseAddonEnum.Description,
				Status:      constants.UN_SWITCH,
				BaseFlag:    true,
			}
			list = append(list, componentDto)
			continue
		}
		// crd 是unhealthy 健康是false 不健康是true 返回给前端的数据取反 正常为true 不正常为false
		componentDto := addonmodel.ComponentListDTO{
			Name:        baseAddonEnum.Name,
			EnName:      baseAddonEnum.PlatformEnName,
			ChName:      baseAddonEnum.PlatformChName,
			Description: baseAddonEnum.Description,
			Status:      constants.ON_SWITCH,
			BaseFlag:    true,
		}
		if addon.Status.HealthCheck == nil {
			// 正在启用中，暂时还没有健康检查状态
			componentDto.Status = constants.ON_SWITCH
		} else {
			// 健康检查
			componentDto.Status = constants.RUNNING
			for _, healthCondition := range addon.Status.HealthCheck.Conditions {
				if healthCondition.Status == constants.ADDON_UNHEALTH {
					componentDto.Status = constants.ERROR
					break
				}
			}
		}
		list = append(list, componentDto)
	}

	// 转换resp
	var listResponse = list
	return listResponse, nil
}

func (handler *addonHandler) ScanComponent(ctx context.Context, clusterName string) (addonmodel.ListComponentScanResponse, error) {
	var listComponentScanResponse = addonmodel.ListComponentScanResponse{}
	listComponent, err := handler.ListComponent(ctx, clusterName)
	if err != nil {
		return listComponentScanResponse, err
	}
	listComponentScanResponse = make([]addonmodel.ComponentScanResponse, 0)
	//
	for _, addon := range listComponent {
		componentScan := addonmodel.ComponentScanResponse{
			Active:      addon.Status != constants.UN_SWITCH,
			Name:        addon.Name,
			NickName:    addonmodel.GetNickName(addon.ChName, addon.EnName),
			Description: addon.Description,
		}
		listComponentScanResponse = append(listComponentScanResponse, componentScan)
	}
	return listComponentScanResponse, nil
}

func (handler *addonHandler) BatchInstall(ctx context.Context, clusterName string, componentList addonmodel.ListComponentRequest) error {
	for _, request := range componentList {
		// 转换参数
		componentDto := addonmodel.ConverComponentRequest2ComponentDto(request)
		err := handler.SwitchComponent(ctx, clusterName, componentDto)
		if err != nil {
			return err
		}
	}
	return nil
}

func (handler *addonHandler) GetClusterNetworks(ctx context.Context, clusterName string) ([]string, error) {
	clusterAddon, err := getAddon(ctx, clusterName, "heimdallr")
	if err != nil {
		return nil, err
	}
	if clusterAddon.Status.Configurations == nil || clusterAddon.Status.Configurations.ConfigurationSchemaData == nil {
		return []string{}, nil
	}
	schemeData := clusterAddon.Status.Configurations.ConfigurationSchemaData
	test, err := schemeData.MarshalJSON()
	if err != nil {
		return nil, err
	}
	obj := struct {
		Heimdallr struct {
			HeimdallrCueRender []struct {
				Name string `json:"name"`
			} `json:"heimdallrCueRender"`
		} `json:"heimdallr"`
	}{}

	if err := json.Unmarshal(test, &obj); err != nil {
		return nil, err
	}
	var result = make([]string, 0, len(obj.Heimdallr.HeimdallrCueRender))
	for _, render := range obj.Heimdallr.HeimdallrCueRender {
		nw := render.Name
		result = append(result, nw)
	}

	for i := range result {
		result[i] = titleFunc(result[i])
	}
	return result, nil
}

func titleFunc(str string) string {
	if len(str) == 0 {
		return str
	}
	var runes []rune = []rune(str)
	if int(runes[0]) >= int('a') && int(runes[0]) <= int('z') {
		runes[0] = rune(int(runes[0]) - 32)
	}
	return string(runes)
}

func getAddon(ctx context.Context, clusterName, componentName string) (stellarisv1alhpha1.ClusterAddon, error) {
	var addon stellarisv1alhpha1.ClusterAddon

	localCluster := hcclient.GetLocalCluster()
	// get addon
	if err := localCluster.GetClient().GetCtrlClient().
		Get(ctx, client.ObjectKey{Namespace: GetAddonNamespace(clusterName), Name: componentName}, &addon); err != nil {
		return addon, err
	}
	return addon, nil
}

func convertAddonAsResponse(response *addonmodel.ComponentDto, addon stellarisv1alhpha1.ClusterAddon) error {
	// set name
	response.Type = string(addon.Spec.Type)
	response.Name = addon.Name
	addonEnum, ok := constants.AddonEnumsMap[addon.Name]
	if ok {
		response.EnName = addonEnum.EnName
		response.ChName = addonEnum.CnName
		response.Description = addonEnum.Description
	}
	// 配置数据

	// selector 赋值
	if addon.Spec.Endpoints != nil && addon.Spec.Endpoints.Selector != nil {
		response.Selectors = make([]addonmodel.EndpointSelector, len(addon.Spec.Endpoints.Selector))
		for i, selector := range addon.Spec.Endpoints.Selector {
			//err := copier.Copy(&response.Selectors[i], &selector)
			response.Selectors[i] = addonmodel.EndpointSelector{
				Namespace: selector.Namespace,
				Labels:    selector.Labels,
				Include:   selector.Include,
				Type:      selector.Type,
			}
		}
	}

	// Statics 赋值
	if addon.Spec.Endpoints != nil && addon.Spec.Endpoints.Static != nil {
		response.Statics = make([]addonmodel.StaticInfo, len(addon.Spec.Endpoints.Static))
		for i, static := range addon.Spec.Endpoints.Static {
			response.Statics[i] = addonmodel.StaticInfo{
				EndPoint: static.Endpoint,
				Type:     static.Type,
			}
		}
	}

	// schemas
	if addon.Spec.Configurations != nil && addon.Spec.Configurations.Schema != nil {
		response.Schema = addon.Spec.Configurations.Schema
	} else {
		return nil
	}

	if !ok {
		return nil
	}
	// 配置enum
	switch addonEnum {
	case constants.ELK:
		elkConfig := addonmodel.ElkConfig{}
		for _, v := range addon.Spec.Configurations.Schema[0].Data {
			if v.Value == nil {
				continue
			}
			switch v.Key {
			case "esPassword":
				json.Unmarshal(v.Value.Raw, &elkConfig.Password)
			case "esName":
				json.Unmarshal(v.Value.Raw, &elkConfig.Name)
			case "port":
				port, err := byteToInt(v.Value.Raw)
				if err == nil {
					elkConfig.Port = port
				}
			case "protocol":
				json.Unmarshal(v.Value.Raw, &elkConfig.Protocol)
			case "ip":
				json.Unmarshal(v.Value.Raw, &elkConfig.Ip)
			}
		}
		response.ElkConfig = elkConfig
	case constants.MONITORING:
		monitorConfig := addonmodel.MonitoringConfig{}
		for _, v := range addon.Spec.Configurations.Schema[0].Data {
			if v.Value == nil {
				continue
			}
			switch v.Key {
			case "port":
				port, err := byteToInt(v.Value.Raw)
				if err == nil {
					monitorConfig.Port = port
				}
			case "protocol":
				json.Unmarshal(v.Value.Raw, &monitorConfig.Protocol)
			case "ip":
				json.Unmarshal(v.Value.Raw, &monitorConfig.Ip)
			case "retention":
				json.Unmarshal(v.Value.Raw, &monitorConfig.Retention)
			case "alertPort":
				alertPort, err := byteToInt(v.Value.Raw)
				if err == nil {
					monitorConfig.AlertPort = alertPort
				}
			case "alertProtocol":
				json.Unmarshal(v.Value.Raw, &monitorConfig.AlertProtocol)
			case "alertIP":
				json.Unmarshal(v.Value.Raw, &monitorConfig.AlertIp)
			}
		}
		response.Monitoring = monitorConfig
	case constants.CORE_DNS:
		dnsConfig := addonmodel.CoreDnsConfig{}
		for _, v := range addon.Spec.Configurations.Schema[0].Data {
			if v.Value == nil {
				continue
			}
			switch v.Key {
			case "autopath":
				json.Unmarshal(v.Value.Raw, &dnsConfig.Autopath)
			case "cacheTime":
				json.Unmarshal(v.Value.Raw, &dnsConfig.CacheTime)
			case "enableErrorLogging":
				json.Unmarshal(v.Value.Raw, &dnsConfig.EnableErrorLogging)
			case "hosts":
				json.Unmarshal(v.Value.Raw, &dnsConfig.Hosts)
			case "forward":
				json.Unmarshal(v.Value.Raw, &dnsConfig.Forward)
			}
		}
		response.CoreDnsConfig = dnsConfig
	case constants.RESOURCE_AGGREGATE:
		resource := addonmodel.ResourceAggregateConfig{}
		for _, v := range addon.Spec.Configurations.Schema[0].Data {
			if v.Value == nil {
				continue
			}
			switch v.Key {
			case "port":
				port, err := byteToInt(v.Value.Raw)
				if err == nil {
					resource.Port = port
				}
			case "protocol":
				json.Unmarshal(v.Value.Raw, &resource.Protocol)
			case "ip":
				json.Unmarshal(v.Value.Raw, &resource.Ip)
			}
		}
		response.Resource = resource
	case constants.APP_DECOMPILE:
		appDecompile := addonmodel.AppDecompileConfig{}
		for _, v := range addon.Spec.Configurations.Schema[0].Data {
			if v.Value == nil {
				continue
			}
			switch v.Key {
			case "port":
				port, err := byteToInt(v.Value.Raw)
				if err == nil {
					appDecompile.Port = port
				}
				//json.Unmarshal(v.Value.Raw, &appDecompile.Port)
			case "protocol":
				json.Unmarshal(v.Value.Raw, &appDecompile.Protocol)
			case "ip":
				json.Unmarshal(v.Value.Raw, &appDecompile.Ip)
			}
		}
		response.AppDecompile = appDecompile
	case constants.SISYPHUS:
		sisyphusConfig := addonmodel.SisyphusConfig{}
		for _, v := range addon.Spec.Configurations.Schema[0].Data {
			if v.Value == nil {
				continue
			}
			switch v.Key {
			case "ip":
				json.Unmarshal(v.Value.Raw, &sisyphusConfig.Ip)
			case "port":
				port, err := byteToInt(v.Value.Raw)
				if err == nil {
					sisyphusConfig.Port = port
				}
				//json.Unmarshal(v.Value.Raw, &sisyphusConfig.Port)
			case "protocol":
				json.Unmarshal(v.Value.Raw, &sisyphusConfig.Protocol)
			case "sisyphusName":
				json.Unmarshal(v.Value.Raw, &sisyphusConfig.SisyphusName)
			case "sisyphusPassword":
				json.Unmarshal(v.Value.Raw, &sisyphusConfig.SisyphusPassword)
			}
		}
		response.SisyphusConfig = sisyphusConfig
	case constants.BASELINE_CHECKER:
		baselineCheckerConfig := addonmodel.BaselineCheckerConfig{}
		for _, v := range addon.Spec.Configurations.Schema[0].Data {
			if v.Value == nil {
				continue
			}
			switch v.Key {
			case "ip":
				_ = json.Unmarshal(v.Value.Raw, &baselineCheckerConfig.Ip)
			case "port":
				port, err := byteToInt(v.Value.Raw)
				if err == nil {
					baselineCheckerConfig.Port = port
				}
			case "protocol":
				_ = json.Unmarshal(v.Value.Raw, &baselineCheckerConfig.Protocol)
			}
		}
		response.BaselineCheckerConfig = baselineCheckerConfig
	default:
		klog.Errorf("unsupported addon type: %s", addonEnum)
	}

	return nil
}

func byteToInt(byteData []byte) (int, error) {
	// 将 []byte 转换为字符串
	strData := string(byteData)

	// 去除字符串两端可能的双引号或空格
	strData = strings.TrimSpace(strData)
	strData = strings.Trim(strData, "\"")

	// 使用 strconv.Atoi 将字符串转换为 int
	intValue, err := strconv.Atoi(strData)
	if err != nil {
		return 0, err
	}

	return intValue, nil
}

func convertToBaseComponentDTO(response *addonmodel.ComponentDto, addon stellarisv1alhpha1.ClusterAddon) error {
	// 基础组件
	baseAddonEnum, ok := constants.BaseAddonEnumsMap[addon.Name]
	if !ok {
		// 不存在这个基础组件
		return fmt.Errorf("unsupported addon type: %s", addon.Name)
	}
	if addon.Spec.Endpoints != nil && addon.Spec.Endpoints.Selector != nil {
		response.Selectors = make([]addonmodel.EndpointSelector, len(addon.Spec.Endpoints.Selector))
		for i, selector := range addon.Spec.Endpoints.Selector {
			response.Selectors[i] = addonmodel.EndpointSelector{
				Namespace: selector.Namespace,
				Labels:    selector.Labels,
				Include:   selector.Include,
				Type:      selector.Type,
			}
		}
	}
	if addon.Spec.Endpoints != nil && addon.Spec.Endpoints.Static != nil {
		response.Statics = make([]addonmodel.StaticInfo, len(addon.Spec.Endpoints.Static))
		for i, static := range addon.Spec.Endpoints.Static {
			response.Statics[i] = addonmodel.StaticInfo{
				EndPoint: static.Endpoint,
				Type:     static.Type,
			}
		}
		//err := copier.Copy(&response.Statics, &addon.Spec.Endpoints.Static)
	}
	response.EnName = baseAddonEnum.PlatformEnName
	response.ChName = baseAddonEnum.PlatformChName
	response.Description = baseAddonEnum.Description
	response.Type = string(addon.Spec.Type)
	return nil
}

func convertAddonStatusAsResponse(response *addonmodel.ComponentDto, addon stellarisv1alhpha1.ClusterAddon) {
	// 给 插件的endpoints 赋值
	if addon.Status.HealthCheck != nil && addon.Status.HealthCheck.Endpoints != nil {
		response.PodInfoList = make([]addonmodel.ComponentPodInfo, len(addon.Status.HealthCheck.Endpoints))
		checkTime := addon.Status.HealthCheck.CheckTime.UTC().Format(time.RFC3339)
		for i, endpoint := range addon.Status.HealthCheck.Endpoints {
			// 赋值
			response.PodInfoList[i] = addonmodel.ComponentPodInfo{
				Status:    string(endpoint.Status),
				Namespace: endpoint.PodNamespace,
				Timestamp: checkTime, // 检查时间，有问题
				Type:      string(endpoint.Type),
				Endpoint:  endpoint.Endpoint,
				Name:      endpoint.PodName,
			}
		}
	}
	if addon.Status.HealthCheck != nil {
		response.LastMonitorTime = addon.Status.HealthCheck.CheckTime.UTC().Format(time.RFC3339)
	}
	if addon.Status.HealthCheck != nil && addon.Status.HealthCheck.Conditions != nil {
		response.HealthcheckConditionList = make([]addonmodel.HealthcheckCondition, len(addon.Status.HealthCheck.Conditions))
		for i, healthConditionConfig := range addon.Status.HealthCheck.Conditions {
			// 这个status里面有问题 ==> 手动赋值吧
			//copier.Copy(&response.HealthcheckConditionList[i], &healthConditionConfig)
			response.HealthcheckConditionList[i] = convert_StellarisCondition_To_HealthcheckCondition(healthConditionConfig)
			// crd 是unhealthy 健康是false 不健康是true 返回给前端的数据取反 正常为true 不正常为false
			//response.HealthcheckConditionList[i].Status = !status
			//// 时间
			//response.HealthcheckConditionList[i].Timestamp = healthConditionConfig.Timestamp.String()
		}
	}
	return
}

func convertComponentDtoAsAddon(addon *stellarisv1alhpha1.ClusterAddon, componentDto addonmodel.ComponentDto) error {
	// configuration 根据组件类型 进行转换
	// 1.configuration schema
	addon.Spec.Configurations = &stellarisv1alhpha1.AddonConfiguration{Schema: componentDto.Schema}
	err := converAddonsSchemas(addon, componentDto)
	if err != nil {
		return err
	}
	// 2. endpoints static && selector
	addon.Spec.Endpoints = &stellarisv1alhpha1.AddonEndpoints{}
	if componentDto.Statics != nil {
		addon.Spec.Endpoints.Static = make([]stellarisv1alhpha1.EndpointStatic, len(componentDto.Statics))
		for i, static := range componentDto.Statics {
			addon.Spec.Endpoints.Static[i] = stellarisv1alhpha1.EndpointStatic{
				Endpoint: static.EndPoint,
				Type:     static.Type,
			}

		}
	}
	if componentDto.Selectors != nil {
		addon.Spec.Endpoints.Selector = make([]stellarisv1alhpha1.EndpointSelector, len(componentDto.Selectors))
		for i, selector := range componentDto.Selectors {
			//err := copier.Copy(&addon.Spec.Endpoints.Selector[i], &selector)
			addon.Spec.Endpoints.Selector[i] = stellarisv1alhpha1.EndpointSelector{
				Namespace: selector.Namespace,
				Labels:    selector.Labels,
				Include:   selector.Include,
				Type:      selector.Type,
			}
		}
	}
	return nil
}

func convertBaseComponentAsAddon(addon *stellarisv1alhpha1.ClusterAddon, componentDto addonmodel.ComponentDto) error {
	// configuration 根据组件类型 进行转换
	// 1.configuration schema
	addon.Spec.Configurations = &stellarisv1alhpha1.AddonConfiguration{Schema: componentDto.Schema}
	//err := convertBaseComponentAsAddon(addon, componentDto)
	//if err != nil {
	//	return err
	//}
	// 2. endpoints static && selector
	addon.Spec.Endpoints = &stellarisv1alhpha1.AddonEndpoints{}
	if componentDto.Statics != nil {
		addon.Spec.Endpoints.Static = make([]stellarisv1alhpha1.EndpointStatic, len(componentDto.Statics))
		for i, static := range componentDto.Statics {
			//err := copier.Copy(&addon.Spec.Endpoints.Static[i], &static)
			addon.Spec.Endpoints.Static[i] = stellarisv1alhpha1.EndpointStatic{
				Endpoint: static.EndPoint,
				Type:     static.Type,
			}

		}
	}
	if componentDto.Selectors != nil {
		addon.Spec.Endpoints.Selector = make([]stellarisv1alhpha1.EndpointSelector, len(componentDto.Selectors))
		for i, selector := range componentDto.Selectors {
			//err := copier.Copy(&addon.Spec.Endpoints.Selector[i], &selector)
			addon.Spec.Endpoints.Selector[i] = stellarisv1alhpha1.EndpointSelector{
				Namespace: selector.Namespace,
				Labels:    selector.Labels,
				Include:   selector.Include,
				Type:      selector.Type,
			}
		}
	}
	return nil
}

func converAddonsSchemas(addons *stellarisv1alhpha1.ClusterAddon, componentDto addonmodel.ComponentDto) error {
	// 给config赋值到schemas到Data上

	addonEnum, ok := constants.AddonEnumsMap[addons.Name]
	if !ok {
		// 不存在
		return fmt.Errorf("addon name %s not found", addons.Name)
	}

	if addons.Spec.Configurations.Schema == nil {
		schemas := make([]stellarisv1alhpha1.AddonConfigurationSchema, 0)
		schemas = append(schemas, stellarisv1alhpha1.AddonConfigurationSchema{
			Name: addons.Name,
		})
		addons.Spec.Configurations.Schema = schemas
	}

	addonConfigurationData := addons.Spec.Configurations.Schema[0].Data
	switch addonEnum {
	case constants.ELK:
		// check elk backup enable
		if enableBackup, err := checkELKEnableBackup(componentDto.ElkConfig); err != nil {
			return fmt.Errorf("check ELK enable backup failed, err: %w", err)
		} else {
			componentDto.ElkConfig.EnableBackup = enableBackup
		}
		schemaData, err := mergeConfigWithSchemaData(componentDto.ElkConfig, addonConfigurationData)
		if err != nil {
			return err
		}
		addons.Spec.Configurations.Schema[0].Data = schemaData
	case constants.SISYPHUS:
		schemaData, err := mergeConfigWithSchemaData(componentDto.SisyphusConfig, addonConfigurationData)
		if err != nil {
			return err
		}
		addons.Spec.Configurations.Schema[0].Data = schemaData
	case constants.MONITORING:
		schemaData, err := mergeConfigWithSchemaData(componentDto.Monitoring, addonConfigurationData)
		if err != nil {
			return err
		}
		addons.Spec.Configurations.Schema[0].Data = schemaData
	case constants.CORE_DNS:
		schemaData, err := mergeConfigWithSchemaData(componentDto.CoreDnsConfig, addonConfigurationData)
		if err != nil {
			return err
		}
		addons.Spec.Configurations.Schema[0].Data = schemaData
	case constants.RESOURCE_AGGREGATE:
		schemaData, err := mergeConfigWithSchemaData(componentDto.Resource, addonConfigurationData)
		if err != nil {
			return err
		}
		addons.Spec.Configurations.Schema[0].Data = schemaData
	case constants.APP_DECOMPILE:
		schemaData, err := mergeConfigWithSchemaData(componentDto.AppDecompile, addonConfigurationData)
		if err != nil {
			return err
		}
		addons.Spec.Configurations.Schema[0].Data = schemaData
	case constants.BASELINE_CHECKER:
		schemaData, err := mergeConfigWithSchemaData(componentDto.BaselineCheckerConfig, addonConfigurationData)
		if err != nil {
			return err
		}
		addons.Spec.Configurations.Schema[0].Data = schemaData
	}
	return nil
}

// checkELKEnableBackup ...
func checkELKEnableBackup(cfg addonmodel.ElkConfig) (bool, error) {
	req, err := http.NewRequest(http.MethodGet, fmt.Sprintf("%s://%s:%d/_snapshot", cfg.Protocol, cfg.Ip, cfg.Port), nil)
	if err != nil {
		return false, err
	}
	req.SetBasicAuth(cfg.Name, cfg.Password)
	transport := http.DefaultTransport.(*http.Transport)
	transport.TLSClientConfig = new(tls.Config)
	transport.TLSClientConfig.InsecureSkipVerify = true
	httpClient := http.Client{
		Transport:     transport,
		CheckRedirect: http.DefaultClient.CheckRedirect,
		Jar:           http.DefaultClient.Jar,
		Timeout:       60 * time.Second,
	}
	response, err := httpClient.Do(req)
	if err != nil {
		logger.GetSugared().Warnf("check ELK enable backup failed, err: %v ", err)
		return false, nil
	}
	defer response.Body.Close()
	body, err := io.ReadAll(response.Body)
	if err != nil {
		return false, err
	}
	if response.StatusCode != http.StatusOK {
		logger.GetSugared().Warnf("check ELK enable backup failed, code: %d, body: %s", response.StatusCode, string(body))
		return false, nil
	}
	logger.GetSugared().Infow("success get snapshot result", "result", body)
	obj := map[string]interface{}{}
	err = json.Unmarshal(body, &obj)
	if err != nil {
		return false, err
	}
	// 判断obj 不为空则有对应的备份存储
	return len(obj) > 0, nil
}

// mergeConfigWithSchemaData merge
// only merge platform managed config do not edit the remain schema data config
func mergeConfigWithSchemaData[Config addonmodel.ElkConfig | addonmodel.SisyphusConfig | addonmodel.MonitoringConfig |
	addonmodel.CoreDnsConfig | addonmodel.ResourceAggregateConfig | addonmodel.AppDecompileConfig | addonmodel.BaselineCheckerConfig](
	cfg Config, schemaDataList []stellarisv1alhpha1.AddonConfigurationData) ([]stellarisv1alhpha1.AddonConfigurationData, error) {

	// control component config managed fields
	addonDataMap := map[string]stellarisv1alhpha1.AddonConfigurationData{}
	for _, addonData := range schemaDataList {
		addonDataMap[addonData.Key] = *addonData.DeepCopy()
	}
	t := reflect.TypeOf(cfg)
	v := reflect.ValueOf(cfg)
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		jsonField := strings.Split(field.Tag.Get("json"), ",")[0]
		if jsonField == "" {
			jsonField = field.Name
		}
		if jsonField == "-" {
			continue
		}
		marshal, err := json.Marshal(v.Field(i).Interface())
		if err != nil {
			return nil, fmt.Errorf("failed to marshal json field %s, err: %v", jsonField, err)
		}
		cfgData := stellarisv1alhpha1.AddonConfigurationData{Key: jsonField, Value: &apiextensionsv1.JSON{Raw: marshal}}
		if v, ok := addonDataMap[cfgData.Key]; ok {
			v.Value = cfgData.Value
			addonDataMap[cfgData.Key] = v
		} else {
			addonDataMap[cfgData.Key] = cfgData
		}
	}
	var list []stellarisv1alhpha1.AddonConfigurationData
	for _, addonData := range addonDataMap {
		list = append(list, addonData)
	}
	// sort list
	sort.SliceStable(list, func(i, j int) bool {
		if list[i].Key == list[j].Key {
			return fmt.Sprint(list[i].Value) < fmt.Sprint(list[j].Value)
		}
		return list[i].Key < list[j].Key
	})
	schemaDataList = list
	return schemaDataList, nil
}

func structToSchemasDataList(entries []struct {
	Key string
	Val any
}) ([]stellarisv1alhpha1.AddonConfigurationData, error) {
	var dataList []stellarisv1alhpha1.AddonConfigurationData
	for _, entry := range entries {
		jsonVal, err := json.Marshal(entry.Val)
		if err != nil {
			return nil, fmt.Errorf("failed to marshal value for key '%s': %w", entry.Key, err)
		}

		var jsonValObj apiextensionsv1.JSON
		if err := json.Unmarshal(jsonVal, &jsonValObj); err != nil {
			return nil, fmt.Errorf("failed to unmarshal JSON value for key '%s': %w", entry.Key, err)
		}

		dataList = append(dataList, stellarisv1alhpha1.AddonConfigurationData{
			Key:   entry.Key,
			Value: &jsonValObj,
		})
	}

	return dataList, nil
}

func elkConfigToAddonConfigurationDataList(cfg addonmodel.ElkConfig) ([]stellarisv1alhpha1.AddonConfigurationData, error) {
	// 将ElkConfig的每个字段转换为AddonConfigurationData
	// 注意：这里简单地将每个字段视为键值对，其中值被序列化为JSON字符串
	// 实际应用中可能需要更复杂的逻辑来处理ValuePath和CueRenderRule
	return structToSchemasDataList([]struct {
		Key string
		Val any
	}{
		{"protocol", cfg.Protocol},
		{"port", cfg.Port},
		{"ip", cfg.Ip},
		{"name", cfg.Name},
		{"password", cfg.Password},
	})
}

func sisyphusConfigToAddonConfigurationDataList(cfg addonmodel.SisyphusConfig) ([]stellarisv1alhpha1.AddonConfigurationData, error) {
	// 将ElkConfig的每个字段转换为AddonConfigurationData
	// 注意：这里简单地将每个字段视为键值对，其中值被序列化为JSON字符串
	// 实际应用中可能需要更复杂的逻辑来处理ValuePath和CueRenderRule
	return structToSchemasDataList([]struct {
		Key string
		Val any
	}{
		{"ip", cfg.Ip},
		{"port", cfg.Port},
		{"protocol", cfg.Protocol},
		{"sisyphusName", cfg.SisyphusName},
		{"sisyphusPassword", cfg.SisyphusPassword},
	})
}

func monitoringConfigToAddonConfigurationDataList(cfg addonmodel.MonitoringConfig) ([]stellarisv1alhpha1.AddonConfigurationData, error) {
	// 将ElkConfig的每个字段转换为AddonConfigurationData
	// 注意：这里简单地将每个字段视为键值对，其中值被序列化为JSON字符串
	// 实际应用中可能需要更复杂的逻辑来处理ValuePath和CueRenderRule
	return structToSchemasDataList([]struct {
		Key string
		Val any
	}{
		{"ip", cfg.Ip},
		{"port", cfg.Port},
		{"protocol", cfg.Protocol},
		{"alertIP", cfg.AlertIp},
		{"alertPort", cfg.AlertPort},
		{"alertProtocol", cfg.AlertProtocol},
		{"retention", cfg.Retention},
	})
}

func coreDnsConfigToAddonConfigurationDataList(cfg addonmodel.CoreDnsConfig) ([]stellarisv1alhpha1.AddonConfigurationData, error) {

	// 将ElkConfig的每个字段转换为AddonConfigurationData
	// 注意：这里简单地将每个字段视为键值对，其中值被序列化为JSON字符串
	// 实际应用中可能需要更复杂的逻辑来处理ValuePath和CueRenderRule
	return structToSchemasDataList([]struct {
		Key string
		Val any
	}{
		{"enableErrorLogging", cfg.EnableErrorLogging},
		{"autopath", cfg.Autopath},
		{"cacheTime", cfg.CacheTime},
		{"hosts", cfg.Hosts},
		{"forward", cfg.Forward},
	})
}

func resourceAggregateConfigToAddonConfigurationDataList(cfg addonmodel.ResourceAggregateConfig) ([]stellarisv1alhpha1.AddonConfigurationData, error) {
	// 将ElkConfig的每个字段转换为AddonConfigurationData
	// 注意：这里简单地将每个字段视为键值对，其中值被序列化为JSON字符串
	// 实际应用中可能需要更复杂的逻辑来处理ValuePath和CueRenderRule

	return structToSchemasDataList([]struct {
		Key string
		Val any
	}{
		{"ip", cfg.Ip},
		{"port", cfg.Port},
		{"protocol", cfg.Protocol},
	})
}

func appDecompileConfigToAddonConfigurationDataList(cfg addonmodel.AppDecompileConfig) ([]stellarisv1alhpha1.AddonConfigurationData, error) {
	// 将ElkConfig的每个字段转换为AddonConfigurationData
	// 注意：这里简单地将每个字段视为键值对，其中值被序列化为JSON字符串
	// 实际应用中可能需要更复杂的逻辑来处理ValuePath和CueRenderRule
	return structToSchemasDataList([]struct {
		Key string
		Val any
	}{
		{"ip", cfg.Ip},
		{"port", cfg.Port},
		{"protocol", cfg.Protocol},
	})
}
