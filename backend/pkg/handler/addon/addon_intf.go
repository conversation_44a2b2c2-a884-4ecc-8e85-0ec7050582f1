package addon

import (
	"context"

	addonmodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/addon"
)

// Handler
// 组件相关API方法集
type Handler interface {
	SwitchComponent(ctx context.Context, clusterName string, request addonmodel.ComponentDto) error
	CancelComponent(ctx context.Context, clusterName, componentName string) error
	UpdateComponent(ctx context.Context, clusterName, componentName string, request addonmodel.ComponentDto) error
	GetComponent(ctx context.Context, clusterName, componentName string, baseFlag bool) (*addonmodel.ComponentDto, error)
	ListComponent(ctx context.Context, clusterName string) (addonmodel.ListResponse, error)
	ScanComponent(ctx context.Context, clusterName string) (addonmodel.ListComponentScanResponse, error)
	BatchInstall(ctx context.Context, clusterName string, componentList addonmodel.ListComponentRequest) error
	GetClusterNetworks(ctx context.Context, clusterName string) ([]string, error)
}

type SisyphusConfigHandler interface {
	GetSisyphusURL(ctx context.Context, clusterName string) (addonmodel.SisyphusAddressConfig, error)
}
