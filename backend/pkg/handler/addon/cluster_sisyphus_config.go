package addon

import (
	"context"
	"fmt"
	"strings"

	"harmonycloud.cn/stellaris/pkg/apis/stellaris/v1alpha1"
	hcclient "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	addonmodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/addon"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

func NewSisyphusConfigHandler() SisyphusConfigHandler {
	return sisyphusConfigHandler{}
}

type sisyphusConfigHandler struct {
}

func (sisyphusConfigHandler) GetSisyphusURL(ctx context.Context, clusterName string) (addonmodel.SisyphusAddressConfig, error) {
	var res addonmodel.SisyphusAddressConfig
	if "true" == config.ForceUseTopSisyphus.Value || clusterName == "" {
		res.Address = config.SisyphusAddress.Value
		res.Username = config.SisyphusUsername.Value
		res.Password = config.SisyphusPassword.Value
		return res, nil
	}

	_, err := hcclient.OnlineClusterAssert(hcclient.GetCluster(clusterName))
	if err != nil {
		return res, err
	}

	var addon v1alpha1.ClusterAddon
	if err := hcclient.GetLocalCluster().GetClient().GetCtrlClient().Get(ctx, client.ObjectKey{Namespace: GetAddonNamespace(clusterName), Name: constants.SISYPHUS.EnName}, &addon); err != nil {
		return res, err
	}
	if addon.Spec.Configurations == nil || len(addon.Spec.Configurations.Schema) == 0 {
		return res, errors.NewFromCode(errors.Var.SisyphusConfigList)
	}
	scheme := addon.Spec.Configurations.Schema[0]
	var protocol, ip, port, username, password string
	for _, value := range scheme.Data {
		bytes, err := value.Value.MarshalJSON()
		if err != nil {
			return res, nil
		}
		bytesVal := string(bytes)
		bytesVal = strings.ReplaceAll(bytesVal, "\"", "")
		switch value.Key {
		case "protocol":
			protocol = bytesVal
		case "ip":
			ip = bytesVal
		case "port":
			port = bytesVal
		case "sisyphusName":
			username = bytesVal
			if username == "" {
				username = config.SisyphusUsername.Value
			}
		case "sisyphusPassword":
			password = bytesVal
			if password == "" {
				password = config.SisyphusPassword.Value
			}

		}
	}
	res.Address = fmt.Sprintf("%s://%s:%s", protocol, ip, port)
	res.Username = username
	res.Password = password
	return res, nil

}
