package addon

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"testing"

	stellarisv1alhpha1 "harmonycloud.cn/stellaris/pkg/apis/stellaris/v1alpha1"
	hcclient "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	addonmodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/addon"
	apiextensionsv1 "k8s.io/apiextensions-apiserver/pkg/apis/apiextensions/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

func TestGetAddon(t *testing.T) {
	err := hcclient.Init()
	//time.Sleep(5 * time.Second)
	if err != nil {
		t.Error(err)
		return
	}
	clusterName := "cluster-57-new"
	namespace := "stellaris-workspace-cluster-57-new"
	addonName := "elk"
	addon, err := getAddon(context.TODO(), clusterName, addonName)
	if err != nil {
		t.<PERSON>rror(err)
		return
	}
	if addon.Namespace == namespace && addon.Name == addonName {
		t.Logf("get addon success")
		return
	} else {
		t.Errorf("get addon failed")
		return
	}
}

func Test_GetComponent(t *testing.T) {
	hcclient.Init()
	handler, err := shouldCreateHandlerInit()
	if err != nil {
		t.Error(err)
		return
	}
	clusterName := "cluster-57-new"
	addonName := "elk"
	component, err := handler.GetComponent(context.TODO(), clusterName, addonName, true)
	if err != nil {
		t.Error(err)
		return
	}
	if component.Name == addonName {
		t.Logf("get component success")
		return
	}
}

func Test_ListComponent(t *testing.T) {
	hcclient.Init()
	handler, err := shouldCreateHandlerInit()
	if err != nil {
		t.Error(err)
		return
	}
	//clusterName := "cluster-57-new"
	clusterName := "stellaris-system"
	listComponent, err := handler.ListComponent(context.TODO(), clusterName)
	RUNNING_COUNTER, UN_SWITCH_COUNTER, ON_SWITCH_COUNTER, ERROR_COUNTER := 0, 0, 0, 0
	for _, addon := range listComponent {
		if addon.Status == constants.RUNNING {
			println("RUNNING:==" + addon.Name)
			RUNNING_COUNTER++
		}
		if addon.Status == constants.ON_SWITCH {
			println("ON_SWITCH:==" + addon.Name)
			ON_SWITCH_COUNTER++
		}
		if addon.Status == constants.UN_SWITCH {
			println("UN_SWITCH:==" + addon.Name)
			UN_SWITCH_COUNTER++
		}
		if addon.Status == constants.ERROR {
			println("ERROR:==" + addon.Name)
			ERROR_COUNTER++
		}
	}
	fmt.Println("RUNNING_COUNTER==", RUNNING_COUNTER)
	fmt.Println("UN_SWITCH_COUNTER==", UN_SWITCH_COUNTER)
	fmt.Println("ON_SWITCH_COUNTER==", ON_SWITCH_COUNTER)
	fmt.Println("ERROR_COUNTER==", ERROR_COUNTER)
}

func Test_SwitchComponent(t *testing.T) {
	hcclient.Init()
	handler, err := shouldCreateHandlerInit()
	if err != nil {
		t.Error(err)
		return
	}
	clusterName := "cluster-57-new"

	err = handler.SwitchComponent(context.TODO(), clusterName, addonmodel.ComponentDto{
		Name: "acl",
	})

	if err != nil {
		t.Error(err)
		return
	}
}

func Test_CancelComponent(t *testing.T) {
	hcclient.Init()
	handler, err := shouldCreateHandlerInit()
	if err != nil {
		t.Error(err)
		return
	}
	err = handler.CancelComponent(context.TODO(), "cluster-57-new", "acl")

	if err != nil {
		t.Error(err)
		return
	}
}

func TestAddonHandler_ScanComponent(t *testing.T) {
	hcclient.Init()
	handler, err := shouldCreateHandlerInit()
	if err != nil {
		t.Error(err)
		return
	}
	//
	context.Background()
	clusterName := "cluster-57-new"
	listComponentScan, err := handler.ScanComponent(context.TODO(), clusterName)
	if err != nil {
		return
	}
	for i, scan := range listComponentScan {
		fmt.Println(i, scan.Name)
		fmt.Println(i, scan.Active)
	}

}

func shouldCreateHandlerInit() (Handler, error) {
	return &addonHandler{
		distributedLock: nil,
	}, nil
}

func TestConver(t *testing.T) {
	hcclient.Init()
	//ccList := &stellarisv1alhpha1.ClusterAddonList{}
	namespace := "stellaris-workspace-cluster-57-new"
	name := "elk"
	// 查询集群下的所有组件
	addon := &stellarisv1alhpha1.ClusterAddon{}
	err := hcclient.GetLocalCluster().
		GetClient().
		GetCtrlClient().Get(context.TODO(), client.ObjectKey{Namespace: namespace, Name: name}, addon)
	if err != nil {
		panic(err)
	}
	err = converAddonsSchemas(addon, addonmodel.ComponentDto{
		ElkConfig: addonmodel.ElkConfig{
			Ip:       "***********",
			Name:     "admin",
			Password: "admin",
			Port:     8080,
			Protocol: "http",
		},
	})
	if err != nil {
		panic(err)
	}

	if err != nil {
		panic(err)
	}
}

func Test_mergeConfigWithSchemaData(t *testing.T) {
	type args struct {
		cfg            any
		schemaDataList []stellarisv1alhpha1.AddonConfigurationData
	}
	type testCase struct {
		name    string
		args    args
		want    []stellarisv1alhpha1.AddonConfigurationData
		wantErr bool
	}

	tests := []testCase{
		{
			name: "ElkConfig merge with existing data",
			args: args{
				cfg: addonmodel.ElkConfig{
					Protocol: "http",
					Port:     9200,
					Ip:       "***********",
					Name:     "elastic",
					Password: "password",
				},
				schemaDataList: []stellarisv1alhpha1.AddonConfigurationData{
					{Key: "testValue", Value: &apiextensionsv1.JSON{Raw: []byte(`true`)}},
					{Key: "protocol", Value: &apiextensionsv1.JSON{Raw: []byte(`"https"`)}},
				},
			},
			want: []stellarisv1alhpha1.AddonConfigurationData{
				{Key: "testValue", Value: &apiextensionsv1.JSON{Raw: []byte(`true`)}},
				{Key: "protocol", Value: &apiextensionsv1.JSON{Raw: []byte(`"http"`)}},
				{Key: "port", Value: &apiextensionsv1.JSON{Raw: []byte(`9200`)}},
				{Key: "ip", Value: &apiextensionsv1.JSON{Raw: []byte(`"***********"`)}},
				{Key: "name", Value: &apiextensionsv1.JSON{Raw: []byte(`"elastic"`)}},
				{Key: "password", Value: &apiextensionsv1.JSON{Raw: []byte(`"password"`)}},
				{Key: "enableBackup", Value: &apiextensionsv1.JSON{Raw: []byte(`false`)}},
			},
			wantErr: false,
		},
		{
			name: "SisyphusConfig  fields",
			args: args{
				cfg: addonmodel.SisyphusConfig{
					Ip:       "127.0.0.1",
					Port:     8080,
					Protocol: "tcp",
				},
				schemaDataList: []stellarisv1alhpha1.AddonConfigurationData{},
			},
			want: []stellarisv1alhpha1.AddonConfigurationData{
				{Key: "ip", Value: &apiextensionsv1.JSON{Raw: []byte(`"127.0.0.1"`)}},
				{Key: "port", Value: &apiextensionsv1.JSON{Raw: []byte(`8080`)}},
				{Key: "protocol", Value: &apiextensionsv1.JSON{Raw: []byte(`"tcp"`)}},
				{Key: "sisyphusName", Value: &apiextensionsv1.JSON{Raw: []byte(`""`)}},
				{Key: "sisyphusPassword", Value: &apiextensionsv1.JSON{Raw: []byte(`""`)}},
			},
			wantErr: false,
		},
		{
			name: "MonitoringConfig with alert settings",
			args: args{
				cfg: addonmodel.MonitoringConfig{
					Protocol:      "http",
					Port:          9090,
					Ip:            "********",
					AlertPort:     9093,
					AlertIp:       "********",
					AlertProtocol: "http",
				},
				schemaDataList: []stellarisv1alhpha1.AddonConfigurationData{},
			},
			want: []stellarisv1alhpha1.AddonConfigurationData{
				{Key: "protocol", Value: &apiextensionsv1.JSON{Raw: []byte(`"http"`)}},
				{Key: "port", Value: &apiextensionsv1.JSON{Raw: []byte(`9090`)}},
				{Key: "ip", Value: &apiextensionsv1.JSON{Raw: []byte(`"********"`)}},
				{Key: "retention", Value: &apiextensionsv1.JSON{Raw: []byte(`""`)}},
				{Key: "alertPort", Value: &apiextensionsv1.JSON{Raw: []byte(`9093`)}},
				{Key: "alertIP", Value: &apiextensionsv1.JSON{Raw: []byte(`"********"`)}},
				{Key: "alertProtocol", Value: &apiextensionsv1.JSON{Raw: []byte(`"http"`)}},
			},
			wantErr: false,
		},
		{
			name: "CoreDnsConfig with multiple domains",
			args: args{
				cfg: addonmodel.CoreDnsConfig{
					EnableErrorLogging: true,
					Autopath:           false,
					CacheTime:          300,
					Hosts: []addonmodel.DnsDomain{
						{Domain: "example.com", Resolution: []string{"***********"}},
					},
					Forward: []addonmodel.DnsDomain{
						{Domain: "forward.com", Resolution: []string{"*******"}},
					},
				},
				schemaDataList: []stellarisv1alhpha1.AddonConfigurationData{},
			},
			want: []stellarisv1alhpha1.AddonConfigurationData{
				{Key: "enableErrorLogging", Value: &apiextensionsv1.JSON{Raw: []byte(`true`)}},
				{Key: "autopath", Value: &apiextensionsv1.JSON{Raw: []byte(`false`)}},
				{Key: "cacheTime", Value: &apiextensionsv1.JSON{Raw: []byte(`300`)}},
				{Key: "hosts", Value: &apiextensionsv1.JSON{Raw: []byte(`[{"domain":"example.com","resolution":["***********"]}]`)}},
				{Key: "forward", Value: &apiextensionsv1.JSON{Raw: []byte(`[{"domain":"forward.com","resolution":["*******"]}]`)}},
			},
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var got []stellarisv1alhpha1.AddonConfigurationData
			var err error
			switch cfg := tt.args.cfg.(type) {
			case addonmodel.ElkConfig:
				got, err = mergeConfigWithSchemaData(cfg, tt.args.schemaDataList)
			case addonmodel.SisyphusConfig:
				got, err = mergeConfigWithSchemaData(cfg, tt.args.schemaDataList)
			case addonmodel.MonitoringConfig:
				got, err = mergeConfigWithSchemaData(cfg, tt.args.schemaDataList)
			case addonmodel.CoreDnsConfig:
				got, err = mergeConfigWithSchemaData(cfg, tt.args.schemaDataList)
			case addonmodel.ResourceAggregateConfig:
				got, err = mergeConfigWithSchemaData(cfg, tt.args.schemaDataList)
			case addonmodel.AppDecompileConfig:
				got, err = mergeConfigWithSchemaData(cfg, tt.args.schemaDataList)
			}
			if (err != nil) != tt.wantErr {
				t.Errorf("mergeConfigWithSchemaData() error = %v, wantErr %v", err, tt.wantErr)
			}
			gotJSON, _ := json.Marshal(got)
			wantJSON, _ := json.Marshal(tt.want)
			if !reflect.DeepEqual(gotJSON, wantJSON) {
				t.Errorf("mergeConfigWithSchemaData() \ngot = %s \nwant = %s\n", gotJSON, wantJSON)
			}
		})
	}
}
