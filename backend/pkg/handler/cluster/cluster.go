package cluster

import (
	"context"
	"sort"
	"sync"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	clusterrender "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/cluster/render"
	database_aop "harmonycloud.cn/unifiedportal/translate-sdk-golang/database-aop"
	"k8s.io/apimachinery/pkg/util/sets"
)

type translateClusterHandler struct {
	handler Hand<PERSON>
}

func (trans *translateClusterHandler) SwitchClusterList(ctx context.Context) (*models.SwitchObjectResp, error) {
	return trans.handler.SwitchClusterList(ctx)
}

func (handler *clustersHandler) SwitchClusterList(ctx context.Context) (*models.SwitchObjectResp, error) {
	clusterResp, err := handler.ListClusters(ctx, MustListClusterStatusOption(string(clustermodel.StatusTypeOnlineStatus)), MustListClusterApiServerStatusOption(string(clustermodel.ApiServerStatusTypeSuccess)))
	if err != nil {
		return nil, err
	}
	var switchObjects []models.SwitchObject
	for _, cluster := range clusterResp {
		switchObjects = append(switchObjects, models.SwitchObject{
			ResourceInstanceId:   cluster.Name,
			Name:                 cluster.Name,
			ParentPermissionCode: "unified_platform_sys_cluster_manage_detail",
		})
	}
	switchObjectResp := &models.SwitchObjectResp{
		Code:         "cluster",
		Name:         "集群",
		ResourceList: switchObjects,
	}
	return switchObjectResp, nil
}

func (trans *translateClusterHandler) ListClusters(ctx context.Context, options ...ListClusterOption) (clustermodel.ListResponse, error) {
	return trans.handler.ListClusters(ctx, options...)
}
func (trans *translateClusterHandler) ClusterExist(ctx context.Context, clusterName string) (*clustermodel.ExistResponse, error) {
	res, err := trans.handler.ClusterExist(ctx, clusterName)
	database_aop.DoTranslate(ctx, res, err)
	return res, err
}
func (trans *translateClusterHandler) WithClusterName(ctx context.Context, clusterName string) (*clustermodel.Response, error) {
	return trans.handler.WithClusterName(ctx, clusterName)
}

func NewHandler() Handler {
	return &translateClusterHandler{
		handler: &clustersHandler{
			api:       newApiHandler(),
			createApi: newCreateApiHandler(clusterrender.NewIntf()),
		},
	}
}

type clustersHandler struct {
	api       api
	createApi createApi
}

func (handler *clustersHandler) ListClusters(ctx context.Context, options ...ListClusterOption) (clustermodel.ListResponse, error) {
	wg := &sync.WaitGroup{}
	wg.Add(2)

	listClusterFunc := func(ctx context.Context,
		getResultFunc func(ctx context.Context) (clustermodel.ListResponse, error),
		wg *sync.WaitGroup,
		resultCluster *clustermodel.ListResponse,
		resultErr *error) {
		defer wg.Done()
		clusters, err := getResultFunc(ctx)
		if err != nil {
			*resultErr = err
		} else {
			*resultCluster = clusters
		}
	}
	var controlledClusters, creatingClusters = new(clustermodel.ListResponse), new(clustermodel.ListResponse)
	var controlledClustersErr, creatingClustersErr = new(error), new(error)
	go listClusterFunc(ctx, handler.api.listCluster, wg, controlledClusters, controlledClustersErr)
	go listClusterFunc(ctx, handler.createApi.listCluster, wg, creatingClusters, creatingClustersErr)
	wg.Wait()
	if *controlledClustersErr != nil {
		return nil, *controlledClustersErr
	}
	if *creatingClustersErr != nil {
		return nil, *creatingClustersErr
	}
	var result clustermodel.ListResponse
	var creatingClusterNameSet = sets.New[string]()
	if *creatingClusters != nil {
		result = append(result, *creatingClusters...)
		for _, cc := range *creatingClusters {
			cc := cc
			creatingClusterNameSet.Insert(cc.Name)
		}
	}

	if *controlledClusters != nil {
		for _, cc := range *controlledClusters {
			cc := cc
			// 如果集群已收管 则使用stc进行返回
			if !creatingClusterNameSet.Has(cc.Name) {
				result = append(result, cc)
			}
		}
	}

	if len(options) != 0 {
		for _, op := range options {
			result = op.filter(result)
		}
	}

	sort.Sort(result)
	return result, nil

}

func (handler *clustersHandler) ClusterExist(ctx context.Context, clusterName string) (*clustermodel.ExistResponse, error) {
	var exist bool
	var message string
	// 判断创建中的集群是否存在
	inetallerExist, err := handler.createApi.isExist(ctx, clusterName)
	if err != nil {
		return nil, err
	}
	if inetallerExist {
		exist = true
		message = clustermodel.ExistResponseMessageForInstallerExist(clusterName)
	}

	// 判断已纳管集群是否存在
	stcExist, err := handler.api.isExist(ctx, clusterName)
	if err != nil {
		return nil, err
	}
	if stcExist {
		exist = true
		message = clustermodel.ExistResponseMessageForStcExist(clusterName)
	}
	return &clustermodel.ExistResponse{
		Exist:   exist,
		Message: message,
	}, nil

}
func (handler *clustersHandler) WithClusterName(ctx context.Context, clusterName string) (*clustermodel.Response, error) {
	return handler.api.withClusterName(ctx, clusterName)
}
