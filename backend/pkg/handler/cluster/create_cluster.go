package cluster

import (
	"context"
	"fmt"

	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	installerclient "harmonycloud.cn/unifiedportal/cloudservice-operator/pkg/handler/installer/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/database"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/addon"
	confighandler "harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/log"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	clusterconfig "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/cluster/config"
	clusterrender "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/cluster/render"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/installerutil"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/lock"
	database_aop "harmonycloud.cn/unifiedportal/translate-sdk-golang/database-aop"
	"k8s.io/apimachinery/pkg/util/sets"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

func getCreateClusterLockKey(clusterName string) string {
	return fmt.Sprintf("olympus-portal::create-cluster::%s", clusterName)
}

func NewCreateClusterHandler() CreateClustersHandler {
	return &translateCreateClusterHandler{
		handler: &createClusterHandler{
			api:                   newApiHandler(),
			createApi:             newCreateApiHandler(clusterrender.NewIntf()),
			mgr:                   clusterrender.NewIntf(),
			sisyphusclient:        installerclient.NewSisyphus(),
			distributeLock:        lock.NewRDSDistributeLock(database.RDS),
			sisyphusConfigHandler: addon.NewSisyphusConfigHandler(),
			installerLogHandler:   log.NewInstallerLog(),
		},
	}
}

type translateCreateClusterHandler struct {
	handler CreateClustersHandler
}

// CreateCluster
// 创建集群对外透出接口
func (translateHandler *translateCreateClusterHandler) CreateCluster(ctx context.Context, request clustermodel.CreateRequest) error {
	return translateHandler.handler.CreateCluster(ctx, request)
}

// UpdateCluster
// 更新创建集群对外透出接口
func (translateHandler *translateCreateClusterHandler) UpdateCluster(ctx context.Context, clusterName string, request clustermodel.CreateRequest) error {
	return translateHandler.handler.UpdateCluster(ctx, clusterName, request)
}

// DeleteCluster
// 删除创建失败的集群
func (translateHandler *translateCreateClusterHandler) DeleteCluster(ctx context.Context, clusterName string) error {
	return translateHandler.handler.DeleteCluster(ctx, clusterName)
}

// CreateClusterStatus
// 集群创建状态获取
func (translateHandler *translateCreateClusterHandler) CreateClusterStatus(ctx context.Context, clusterName string) (*clustermodel.CreateStatusResponse, error) {
	response, err := translateHandler.handler.CreateClusterStatus(ctx, clusterName)
	database_aop.DoTranslate(ctx, response, err)
	return response, err
}

// CreateResponse
// 获取集群创建的回显表单
func (translateHandler *translateCreateClusterHandler) CreateResponse(ctx context.Context, clusterName string) (*clustermodel.CreateResponse, error) {
	return translateHandler.handler.CreateResponse(ctx, clusterName)
}

// CreateLog
// 读取创建集群过程中的日志
func (translateHandler *translateCreateClusterHandler) CreateLog(ctx context.Context, request clustermodel.CreateLogQueryRequest) (<-chan []byte, context.Context, context.CancelFunc, error) {
	return translateHandler.handler.CreateLog(ctx, request)
}

// Retry
// 集群创建失败重试
func (translateHandler *translateCreateClusterHandler) Retry(ctx context.Context, clusterName string) error {
	return translateHandler.handler.Retry(ctx, clusterName)
}

type createClusterHandler struct {
	api            api
	createApi      createApi
	mgr            clusterrender.Intf
	sisyphusclient installerclient.Sisyphus
	distributeLock lock.DistributeLock
	// 读取下层集群的西西弗斯地址
	sisyphusConfigHandler addon.SisyphusConfigHandler
	installerLogHandler   log.InstallerLog
}

// CreateCluster
// 创建集群对外透出接口
func (handler *createClusterHandler) CreateCluster(ctx context.Context, request clustermodel.CreateRequest) error {
	// 获取分布式所
	lockKey := getCreateClusterLockKey(request.ClusterName)
	if h, err := handler.distributeLock.Lock(ctx, lockKey); err != nil {
		return err
	} else {
		defer h.UnLock(ctx)
	}

	// 暂时使用基线版本来判断CRI类型
	solutionInfo, exist := clusterconfig.CreateClusterConfig.SolutionInfos.FindByKubernetesAndCRIVersion(request.KubernetesVersion, request.KubernetesCRIVersion)
	if !exist {
		return errors.NewFromCodeWithMessage(errors.Var.ParamError, fmt.Sprintf("can not find solution by kubernetesVersion:%s,kubernetesCRIVersion:%s", request.KubernetesVersion, request.KubernetesCRIVersion))
	}
	baseLine := utils.MapGetValue(solutionInfo.MergeLabels, constants.ClusterLabelsKeyBaseline, "")
	request.CRI = installerutil.GetCRITypeByBaseLineVersion(baseLine)

	clusterName := request.ClusterName
	// 校验集群必须不存在
	if err := handler.createClusterIsExistAssert(ctx, clusterName); err != nil {
		return err
	}
	if err := handler.CreateRequestValidator(ctx, request); err != nil {
		return err
	}
	installer := new(installerv1alpha1.Installer)
	if err := handler.mgr.CreateRequestRenderToInstaller(request, installer); err != nil {
		return err
	}
	return handler.createApi.createInstaller(ctx, request.ClusterName, *installer)
}

// UpdateCluster
// 更新创建集群对外透出接口
func (handler *createClusterHandler) UpdateCluster(ctx context.Context, clusterName string, request clustermodel.CreateRequest) error {
	// 获取分布式所
	lockKey := getCreateClusterLockKey(request.ClusterName)
	if h, err := handler.distributeLock.Lock(ctx, lockKey); err != nil {
		return err
	} else {
		defer h.UnLock(ctx)
	}

	// 读取老的installer
	installer, err := handler.createApi.getInstallerByClusterName(ctx, clusterName)
	if err != nil {
		return err
	}
	// 转化installer 为 statusResponse
	var statusResponse = new(clustermodel.CreateStatusResponse)
	if err := handler.mgr.InstallerToStatusResponse(*installer, statusResponse); err != nil {
		return err
	}
	// 只有在特定的失败条件下才可以更新
	if !clustermodel.FailedClusterStatusTypes.Has(statusResponse.Status) {
		return errors.NewFromCode(errors.Var.ClusterMustCreateFail)
	}

	solutionInfo, exist := clusterconfig.CreateClusterConfig.SolutionInfos.FindByKubernetesAndCRIVersion(request.KubernetesVersion, request.KubernetesCRIVersion)
	if !exist {
		return errors.NewFromCodeWithMessage(errors.Var.ParamError, fmt.Sprintf("can not find solution by kubernetesVersion:%s,kubernetesCRIVersion:%s", request.KubernetesVersion, request.KubernetesCRIVersion))
	}
	baseLine := utils.MapGetValue(solutionInfo.MergeLabels, constants.ClusterLabelsKeyBaseline, "")
	request.CRI = installerutil.GetCRITypeByBaseLineVersion(baseLine)

	request.ClusterName = clusterName
	if err := handler.CreateRequestValidator(ctx, request); err != nil {
		return err
	}
	mergeInstaller := client.MergeFrom(installer.DeepCopy())
	if err := handler.mgr.CreateRequestRenderToInstaller(request, installer); err != nil {
		return err
	}

	return handler.createApi.updateInstaller(ctx, request.ClusterName, *installer, mergeInstaller)
}

// DeleteCluster
// 删除创建失败的集群
func (handler *createClusterHandler) DeleteCluster(ctx context.Context, clusterName string) error {
	return handler.createApi.deleteCreate(ctx, clusterName)
}

// CreateClusterStatus
// 集群创建状态获取
func (handler *createClusterHandler) CreateClusterStatus(ctx context.Context, clusterName string) (*clustermodel.CreateStatusResponse, error) {
	response, err := handler.createApi.createClusterStatus(ctx, clusterName)
	switch err.(type) {
	case errors.Error:
		e := err.(errors.Error)
		if e.ResponseCode != errors.Var.ClusterCreateTaskNotExist.ResponseCode {
			return response, err
		}
		// 判断集群是否已创建成功 若集群创建成功 则返回initial 状态
		exist, _ := handler.api.isExist(ctx, clusterName)
		if !exist {
			return response, err
		}
		return &clustermodel.CreateStatusResponse{
			Name:   clusterName,
			Status: clustermodel.StatusTypeInitializingStatus,
		}, nil
	default:
		return response, err
	}
}

// CreateResponse
// 获取集群创建的回显表单
func (handler *createClusterHandler) CreateResponse(ctx context.Context, clusterName string) (*clustermodel.CreateResponse, error) {
	return handler.createApi.createResponse(ctx, clusterName)
}

// CreateLog
// 读取创建集群过程中的日志
func (handler *createClusterHandler) CreateLog(ctx context.Context, request clustermodel.CreateLogQueryRequest) (<-chan []byte, context.Context, context.CancelFunc, error) {

	// 获取西西弗斯链接消息
	sisyphusAddressConfig, err := handler.sisyphusConfigHandler.GetSisyphusURL(ctx, "")
	if err != nil {
		return nil, nil, nil, err
	}

	installer, err := handler.createApi.getInstallerByClusterName(ctx, request.Name)
	if err != nil {
		return nil, nil, nil, err
	}

	return handler.installerLogHandler.SisyphusLogForInstaller(ctx, client.ObjectKey{Namespace: installer.Namespace, Name: installer.Name}, sisyphusAddressConfig)

}

// Retry
// 集群创建失败重试
func (handler *createClusterHandler) Retry(ctx context.Context, clusterName string) error {
	createResponse, err := handler.CreateResponse(ctx, clusterName)
	if err != nil {
		return err
	}
	request := createResponse.Convert2CreateRequest()
	request.StartFromFailed = true
	request.Reset = false
	return handler.UpdateCluster(ctx, clusterName, request)
}

// createClusterIsExistAssert
// 所创建的集群不能在创建中或已纳管
func (handler *createClusterHandler) createClusterIsExistAssert(ctx context.Context, clusterName string) error {
	// 保证集群未纳管
	if exist, err := handler.api.isExist(ctx, clusterName); err != nil {
		return err
	} else if exist {
		return errors.NewFromCodeWithMessage(errors.Var.ClusterNameRepeated, clusterName)
	}

	// 保证集群非创建中状态
	if exist, err := handler.createApi.isExist(ctx, clusterName); err != nil {
		return err
	} else if exist {
		return errors.NewFromCodeWithMessage(errors.Var.ClusterNameRepeated, clusterName)
	}

	return nil
}

func (handler *createClusterHandler) CreateRequestValidator(ctx context.Context, request clustermodel.CreateRequest) error {
	// 管理集群 stellaris组件访问地址校验
	ch := confighandler.NewHandler()
	sugar, err := ch.GetSugarConfigByType(ctx, confighandler.GroupCreateCluster, string(config.HubStellariesComponent))
	if err != nil {
		return err
	}
	var stellarisRespponse = sugar.Sugar.(*config.HubStellariesComponentResponse)
	if stellarisRespponse.Model == config.ActiveStandBy {
		if request.StellarisComponent.StandbyAddress == nil ||
			request.StellarisComponent.StandbyPort == nil {
			return errors.NewFromCode(errors.Var.StellarisStandbyValueNotNullOnActiveStandBy)
		}
	}

	// 节点模式与节点数量校验
	var expectLessNodeNum int
	var expectOnErrorResult error
	switch request.NodeConfigs.NodeConfigType {
	case clustermodel.NodeConfigTypeAllInOne:
		expectLessNodeNum = 1
		expectOnErrorResult = errors.NewFromCode(errors.Var.AllInOneNodeNumError)
	case clustermodel.NodeConfigTypeMinimizeHA:
		expectLessNodeNum = 3
		expectOnErrorResult = errors.NewFromCode(errors.Var.MinimizeHANodeNumError)
	case clustermodel.NodeConfigTypeStandardNoneHA:
		expectLessNodeNum = 4
		expectOnErrorResult = errors.NewFromCode(errors.Var.StandardNoneHANodeNumError)
	case clustermodel.NodeConfigTypeStandardHA:
		expectLessNodeNum = 7
		expectOnErrorResult = errors.NewFromCode(errors.Var.StandardHANodeNumError)
	default:
		return errors.NewFromCodeWithMessage(errors.Var.NodeConfigTypeIllegal, string(request.NodeConfigs.NodeConfigType))
	}
	if len(request.NodeConfigs.Nodes) < expectLessNodeNum {
		return expectOnErrorResult
	}
	// 至少提供一种容器网络解决方案
	if len(request.NetworkConfigs.CNIs) == 0 {
		return errors.NewFromCode(errors.Var.ChooseOneContainerNetworkSolutionAtLest)
	}

	// 高可用模式下 api-server lb 和 ingress lb 必须填写
	haNetworkType := sets.New[clustermodel.NodeConfigType](clustermodel.NodeConfigTypeMinimizeHA, clustermodel.NodeConfigTypeStandardHA)
	if haNetworkType.Has(request.NodeConfigs.NodeConfigType) {
		if request.NetworkConfigs.ApiServerVIP == nil {
			return errors.NewFromCode(errors.Var.ApiServerAddressRequiredInHAModel)
		}

		if request.NetworkConfigs.LoadBalance == nil {
			return errors.NewFromCode(errors.Var.DefaultLBAddressRequiredInHAModel)
		}
	}

	// 磁盘校验
	var etcdVerifyNode, systemDataVerifyNode, dockerVerifyNodes, kubeletVeriryNodes clustermodel.CreateNodeConfigListRequest
	switch request.NodeConfigs.NodeConfigType {
	case clustermodel.NodeConfigTypeAllInOne:
		etcdVerifyNode = request.NodeConfigs.Nodes[0:1]
		systemDataVerifyNode = request.NodeConfigs.Nodes[0:1]
		dockerVerifyNodes = request.NodeConfigs.Nodes[0:]
		kubeletVeriryNodes = request.NodeConfigs.Nodes[0:]
	case clustermodel.NodeConfigTypeStandardNoneHA:
		etcdVerifyNode = request.NodeConfigs.Nodes[0:1]
		systemDataVerifyNode = request.NodeConfigs.Nodes[1:4]
		dockerVerifyNodes = request.NodeConfigs.Nodes[0:]
		kubeletVeriryNodes = request.NodeConfigs.Nodes[0:]
	case clustermodel.NodeConfigTypeMinimizeHA:
		etcdVerifyNode = request.NodeConfigs.Nodes[0:3]
		systemDataVerifyNode = request.NodeConfigs.Nodes[0:3]
		dockerVerifyNodes = request.NodeConfigs.Nodes[0:]
		kubeletVeriryNodes = request.NodeConfigs.Nodes[0:]
	case clustermodel.NodeConfigTypeStandardHA:
		etcdVerifyNode = request.NodeConfigs.Nodes[0:3]
		systemDataVerifyNode = request.NodeConfigs.Nodes[4:7]
		dockerVerifyNodes = request.NodeConfigs.Nodes[0:]
		kubeletVeriryNodes = request.NodeConfigs.Nodes[0:]
	}
	// check etcd
	for _, node := range etcdVerifyNode {
		if node.Storage.Type != clustermodel.NodeStorageTypeAuto {
			continue
		}
		if node.Storage.DiskPath == nil || node.Storage.DiskPath.ETCD == "" {
			// result etcd error
			return errors.NewFromCodeWithMessage(errors.Var.DiskTypeAutoMasterEtcdPathNullErr, node.Ip)
		}
	}

	// check system
	for _, node := range systemDataVerifyNode {
		if node.Storage.Type != clustermodel.NodeStorageTypeAuto {
			continue
		}
		if node.Storage.DiskPath == nil || node.Storage.DiskPath.System == "" {
			// result system date error
			return errors.NewFromCodeWithMessage(errors.Var.DiskTypeAutoSystemDataPathNullErr, node.Ip)
		}
	}

	// check docker
	for _, node := range dockerVerifyNodes {
		if node.Storage.Type != clustermodel.NodeStorageTypeAuto {
			continue
		}
		if node.Storage.DiskPath == nil || node.Storage.DiskPath.Docker == "" {
			// result docker error
			return errors.NewFromCodeWithMessage(errors.Var.DiskTypeAutoDockerPathNullErr, node.Ip)
		}
	}

	// check kubelet
	for _, node := range kubeletVeriryNodes {
		if node.Storage.Type != clustermodel.NodeStorageTypeAuto {
			continue
		}
		if node.Storage.DiskPath == nil || node.Storage.DiskPath.Kubelet == "" {
			// result kubelet error
			return errors.NewFromCodeWithMessage(errors.Var.DiskTypeAutoKubeletPathNullErr, node.Ip)
		}
	}
	return nil
}
