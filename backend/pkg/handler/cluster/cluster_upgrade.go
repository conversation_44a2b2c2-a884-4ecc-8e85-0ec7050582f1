package cluster

import (
	"context"
	"encoding/json"
	"fmt"
	"mime/multipart"
	"sort"
	"strings"

	"github.com/gin-gonic/gin"
	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	installerclient "harmonycloud.cn/unifiedportal/cloudservice-operator/pkg/handler/installer/client"
	"harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	hcclient "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/database"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/addon"
	excel "harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/template"
	addonmodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/addon"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/template"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/lock"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/upgraderender"
	database_aop "harmonycloud.cn/unifiedportal/translate-sdk-golang/database-aop"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func getUpgradeClusterLockKey(clusterName string) string {
	return fmt.Sprintf("olympus-portal::upgrade-cluster::%s", clusterName)
}

func NewUpgradeClustersHandler() UpgradeClustersHandler {
	return &translateUpgradeClusterHandler{
		handler: &upgradeClustersHandler{
			api:                   newApiHandler(),
			sisyphusClient:        installerclient.NewSisyphus(),
			distributeLock:        lock.NewRDSDistributeLock(database.RDS),
			excelApi:              excel.NewHandler(),
			clusterApi:            NewHandler(),
			addonApi:              addon.NewHandler(),
			upgradeApi:            newUpgradeApiHandler(upgraderender.NewUpgradeInterface()),
			installerRender:       upgraderender.NewUpgradeInterface(),
			sisyphusConfigHandler: addon.NewSisyphusConfigHandler(),
		},
	}
}

type translateUpgradeClusterHandler struct {
	handler UpgradeClustersHandler
}

func (translateHandler *translateUpgradeClusterHandler) ListClusterUpgrade(ctx *gin.Context) (*clustermodel.ListUpgradeClusterInfo, error) {
	result, err := translateHandler.handler.ListClusterUpgrade(ctx)
	database_aop.DoTranslate(ctx, result, err)
	return result, err
}

func (translateHandler *translateUpgradeClusterHandler) ListClusterUpgradePackage(ctx *gin.Context, clusterName string) (*clustermodel.ListPackageInfo, error) {
	return translateHandler.handler.ListClusterUpgradePackage(ctx, clusterName)
}

func (translateHandler *translateUpgradeClusterHandler) GetClusterUpgradePackage(ctx *gin.Context, clusterName string, packageName string) (*clustermodel.PackageContent, error) {
	return translateHandler.handler.GetClusterUpgradePackage(ctx, clusterName, packageName)
}

func (translateHandler *translateUpgradeClusterHandler) GetClusterUpgradeLoadBalanceDefault(ctx *gin.Context, clusterName string) (*[]string, error) {
	return translateHandler.handler.GetClusterUpgradeLoadBalanceDefault(ctx, clusterName)
}

func (translateHandler *translateUpgradeClusterHandler) ListClusterUpgradeNodes(ctx *gin.Context, clusterName string, includeNative string) (*clustermodel.ListNodeInfo, error) {
	return translateHandler.handler.ListClusterUpgradeNodes(ctx, clusterName, includeNative)
}

func (translateHandler *translateUpgradeClusterHandler) DownloadClusterUpgradeNodeTemplate(ctx *gin.Context, clusterName string, includeNative string) (*template.FileDownloadInfo, error) {
	return translateHandler.handler.DownloadClusterUpgradeNodeTemplate(ctx, clusterName, includeNative)
}

func (translateHandler *translateUpgradeClusterHandler) UploadClusterUpgradeNodeTemplate(ctx *gin.Context, fhs []*multipart.FileHeader, clusterName string, includeNative string) (interface{}, error) {
	return translateHandler.handler.UploadClusterUpgradeNodeTemplate(ctx, fhs, clusterName, includeNative)
}

func (translateHandler *translateUpgradeClusterHandler) UpgradeCluster(ctx *gin.Context, clusterName string, request clustermodel.UpgradeClusterRequest) error {
	return translateHandler.handler.UpgradeCluster(ctx, clusterName, request)
}

func (translateHandler *translateUpgradeClusterHandler) GetClusterUpgradeStep(ctx *gin.Context, clusterName string) (*clustermodel.UpgradeClusterStepResponse, error) {
	return translateHandler.handler.GetClusterUpgradeStep(ctx, clusterName)
}

func (translateHandler *translateUpgradeClusterHandler) RetryClusterUpgrade(ctx *gin.Context, clusterName string) error {
	return translateHandler.handler.RetryClusterUpgrade(ctx, clusterName)
}

func (translateHandler *translateUpgradeClusterHandler) GetClusterUpgrade(ctx *gin.Context, clusterName string) (*clustermodel.UpgradeClusterRequest, error) {
	return translateHandler.handler.GetClusterUpgrade(ctx, clusterName)
}

type upgradeClustersHandler struct {
	// stc集群连接器
	api api
	// sisyphus连接器
	sisyphusClient installerclient.Sisyphus
	distributeLock lock.DistributeLock
	// excel处理器
	excelApi excel.Handler
	// portal集群连接器
	clusterApi Handler
	// 集群组件Api
	addonApi addon.Handler
	// 集群升级installerApi
	upgradeApi UpgradeApi
	// request处理Installer
	installerRender upgraderender.UpgradeInterface
	// 读取下层集群的西西弗斯地址
	sisyphusConfigHandler addon.SisyphusConfigHandler
}

func (handler *upgradeClustersHandler) ListClusterUpgrade(ctx *gin.Context) (*clustermodel.ListUpgradeClusterInfo, error) {
	listCluster, err := handler.clusterApi.ListClusters(ctx)
	if err != nil {
		return nil, err
	}

	stcList, err := handler.api.listCluster(ctx)
	if err != nil {
		return nil, err
	}
	stcMap := make(map[string]*clustermodel.Response)
	for _, stc := range stcList {
		stcMap[stc.Name] = &stc
	}
	baseLinePackageSolutionMapping, err := getBaseLinePackageSolutionMapping(ctx)
	if err != nil {
		return nil, err
	}

	response := clustermodel.ListUpgradeClusterInfo{}
	for _, cluster := range listCluster {
		baseline := cluster.Baseline
		upgradeClusterInfo := &clustermodel.UpgradeClusterInfo{
			Name:     cluster.Name,
			Baseline: baseline,
			Prole:    cluster.Prole,
			Upgrade:  false,
		}
		setMessageAndUpgrade(upgradeClusterInfo, stcMap, cluster, baseLinePackageSolutionMapping)
		// todo lanchao deadline: 2024年6月24日 查询Installer,重新设置集群升级状态,离线集群不支持升级
		response = append(response, *upgradeClusterInfo)
	}
	return &response, nil
}

func (handler *upgradeClustersHandler) ListClusterUpgradePackage(ctx *gin.Context, clusterName string) (*clustermodel.ListPackageInfo, error) {
	cluster, err := handler.api.withClusterName(ctx, clusterName)
	if err != nil {
		return nil, err
	}

	baseline := cluster.Baseline
	if baseline == "" {
		return nil, errors.NewFromCode(errors.Var.BaseLineVersionISNull)
	}

	baseLinePackageSolutionMapping, err := getOneBaseLinePackageSolution(ctx, baseline)
	if err != nil {
		return nil, err
	}

	sisyphusConfig, err := handler.sisyphusConfigHandler.GetSisyphusURL(ctx, clusterName)
	if err != nil {
		return nil, err
	}

	sisyphusRequestOptions := []installerclient.OptionFunc{installerclient.WithURL(sisyphusConfig.Address), installerclient.WithAuth(sisyphusConfig.Address, sisyphusConfig.Username, sisyphusConfig.Password)}
	solutionList, err := handler.sisyphusClient.ListSolution(ctx, sisyphusRequestOptions...)
	if err != nil {
		return nil, err
	}
	solutionMap := make(map[string]*installerclient.OrchestrationItem, len(solutionList.Data))
	for _, solution := range solutionList.Data {
		solutionMap[solution.ID] = &solution
	}
	response := clustermodel.ListPackageInfo{}
	for _, packageSolution := range baseLinePackageSolutionMapping {
		solutionSet := make(map[string]struct{}, len(packageSolution.Solution))
		for _, solution := range packageSolution.Solution {
			solutionSet[solution.ID] = struct{}{}
		}

		intersectionCount := 0
		for id := range solutionSet {
			if _, exists := solutionMap[id]; exists {
				intersectionCount++
			}
		}
		info := clustermodel.PackageInfo{
			PackageName: packageSolution.PackageName,
		}
		if packageSolution.Count == intersectionCount {
			info.Available = clustermodel.AvailableTypeTrue
		} else {
			info.Available = clustermodel.AvailableTypeFalse
			info.Message = constants.PackageMessageSisyphusLackSolution
		}
		response = append(response, info)
	}
	return &response, nil
}

func (handler *upgradeClustersHandler) GetClusterUpgradePackage(ctx *gin.Context, clusterName string, packageName string) (*clustermodel.PackageContent, error) {
	cluster, err := handler.api.withClusterName(ctx, clusterName)
	if err != nil {
		return nil, err
	}

	if cluster.Status != clustermodel.StatusTypeOnlineStatus {
		return nil, errors.NewFromCode(errors.Var.ClusterStatusNotOnline)
	}

	baseline := cluster.Baseline
	if baseline == "" {
		return nil, nil
	}

	// todo lanchao deadline: 2024年6月24日 根据集群基线和查看的包名展示组件列表
	baseLinePackageSolutionMapping, err := getOneBaseLinePackageSolution(ctx, baseline)
	if err != nil {
		return nil, err
	}
	var packageSolution = clustermodel.VersionMapping{}
	for _, packageInfo := range baseLinePackageSolutionMapping {
		if packageInfo.PackageName == packageName {
			packageSolution = packageInfo
			break
		}
	}
	packageComponentList := packageSolution.Components
	stcComponentList, err := handler.addonApi.ListComponent(ctx, clusterName)
	if err != nil {
		return nil, err
	}
	return &clustermodel.PackageContent{
		PackageName:   packageName,
		ComponentList: mergeClusterComponent(stcComponentList, packageComponentList),
	}, nil
}

func mergeClusterComponent(stcComponentList addonmodel.ListResponse, packageComponentList []clustermodel.Component) []clustermodel.ComponentInfo {
	var components []clustermodel.ComponentInfo
	componentMap := make(map[string]clustermodel.Component)
	for _, component := range packageComponentList {
		componentMap[component.ComponentName] = component
	}
	for _, component := range stcComponentList {
		info := clustermodel.ComponentInfo{
			ComponentName: component.Name,
			Status:        component.Status,
		}
		if packageComponent, exists := componentMap[component.Name]; exists {
			info.ComponentType = packageComponent.ComponentType
			info.CurrentVersion = component.Version
			info.TargetVersion = packageComponent.Version
			info.Available = packageComponent.Status
			components = append(components, info)
		}
	}
	// 排序
	statusOrder := map[constants.AddonStatusEnum]int{constants.RUNNING: 0, constants.ERROR: 1, constants.ON_SWITCH: 2, constants.UN_SWITCH: 3}

	sort.Slice(components, func(i, j int) bool {
		if components[i].ComponentType != components[j].ComponentType {
			return components[i].ComponentType == "native"
		}
		if components[i].Status != components[j].Status {
			return statusOrder[components[i].Status] < statusOrder[components[j].Status]
		}
		return components[i].ComponentName < components[j].ComponentName
	})

	return components
}

func (handler *upgradeClustersHandler) GetClusterUpgradeLoadBalanceDefault(ctx *gin.Context, clusterName string) (*[]string, error) {
	cluster, err := client.GetCluster(clusterName)
	if err != nil {
		return nil, err
	}
	daemonSet, err := cluster.GetClient().GetKubeClient().AppsV1().DaemonSets(constants.NamespaceSystem).Get(ctx, constants.DefaultNginxIngressController, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}
	ipList := make([]string, 0)
	for _, container := range daemonSet.Spec.Template.Spec.Containers {
		if container.Name == constants.DefaultNginxIngressControllerContainer {
			for _, arg := range container.Args {
				if strings.HasPrefix(arg, constants.NginxIngressControllerArgsPublishStatusAddress) {
					addresses := strings.TrimPrefix(arg, constants.NginxIngressControllerArgsPublishStatusAddress)
					ipList = append(ipList, strings.Split(addresses, ",")...)
				}
			}
		}
	}
	return &ipList, nil
}

func (handler *upgradeClustersHandler) ListClusterUpgradeNodes(ctx *gin.Context, clusterName string, includeNative string) (*clustermodel.ListNodeInfo, error) {
	cluster, err := client.GetCluster(clusterName)
	if err != nil {
		return nil, err
	}
	nodeList, err := cluster.GetClient().GetKubeClient().CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return nil, err
	}
	response := clustermodel.ListNodeInfo{}

	var workNodeList []clustermodel.NodeInfo
	var masterNodeList []clustermodel.NodeInfo

	for _, node := range nodeList.Items {
		roles := utils.FindNodeRoles(&node)
		nodeInfo := clustermodel.NodeInfo{
			NodeName: node.GetName(),
			Ip:       utils.GetNodeInternalIP(&node),
			Status:   utils.GetNodeStatus(&node),
		}
		if utils.Contains(roles, constants.NodeRoleMaster) {
			nodeInfo.NodeType = clustermodel.NodeTypeMaster
			masterNodeList = append(masterNodeList, nodeInfo)
		} else {
			nodeInfo.NodeType = clustermodel.NodeTypeWorker
			workNodeList = append(workNodeList, nodeInfo)
		}
	}
	sort.Sort(clustermodel.SortNodeIp(masterNodeList))
	sort.Sort(clustermodel.SortNodeIp(workNodeList))
	masterNodeList[0].NodeType = clustermodel.NodeTypeMaster0
	if includeNative == "true" {
		response = append(response, masterNodeList...)
		response = append(response, workNodeList...)
	}
	if includeNative == "false" {
		response = append(response, masterNodeList[0])
	}
	return &response, nil
}

func (handler *upgradeClustersHandler) DownloadClusterUpgradeNodeTemplate(ctx *gin.Context, clusterName string, includeNative string) (*template.FileDownloadInfo, error) {
	nodeList, err := handler.ListClusterUpgradeNodes(ctx, clusterName, includeNative)
	if err != nil {
		return nil, err
	}
	fileName, f, err := handler.excelApi.GetExcelTemplateFile(ctx, constants.ClusterUpgradeExcelTemplateCode)
	if err != nil {
		return nil, err
	}
	defaultSheetName := f.GetSheetName(0)

	rows, err := f.GetRows(defaultSheetName)
	if err != nil {
		return nil, err
	}

	minLineNumber := len(rows)

	for index, node := range *nodeList {
		rowNumber := minLineNumber + index + 1
		f.SetCellValue(defaultSheetName, fmt.Sprintf("A%d", rowNumber), node.NodeName)
		f.SetCellValue(defaultSheetName, fmt.Sprintf("B%d", rowNumber), node.Ip)
	}

	buffer, err := f.WriteToBuffer()
	if err != nil {
		panic(err)
	}
	return &template.FileDownloadInfo{
		FileName: fileName,
		Buffer:   buffer,
	}, nil
}

func (handler *upgradeClustersHandler) UploadClusterUpgradeNodeTemplate(ctx *gin.Context, fhs []*multipart.FileHeader, clusterName string, includeNative string) (interface{}, error) {
	nodeList, err := handler.ListClusterUpgradeNodes(ctx, clusterName, includeNative)
	if err != nil {
		return nil, err
	}
	// 将nodeList转成map
	nodeMap := make(map[string]clustermodel.NodeInfo)
	for _, node := range *nodeList {
		nodeMap[node.NodeName] = node
	}
	newCtx := context.WithValue(ctx, constants.ContextKeyClusterUpgradeNodes, nodeMap)

	response, err := handler.excelApi.ExcelTemplateUpdate(newCtx, constants.ClusterUpgradeExcelTemplateCode, fhs)
	if err != nil {
		return nil, err
	}
	return response, nil
}

func (handler *upgradeClustersHandler) UpgradeCluster(ctx *gin.Context, clusterName string, request clustermodel.UpgradeClusterRequest) error {
	lockKey := getUpgradeClusterLockKey(clusterName)
	if h, err := handler.distributeLock.Lock(ctx, lockKey); err != nil {
		return err
	} else {
		defer h.UnLock(ctx)
	}
	request.ClusterName = clusterName
	if err := handler.checkCluster(ctx, clusterName); err != nil {
		return err
	}

	if err := handler.checkUpgradeClusterRequest(ctx, request); err != nil {
		return err
	}
	// 设置节点类型
	if err := setNodeType(ctx, request); err != nil {
		return err
	}
	installer := new(installerv1alpha1.Installer)
	if err := handler.installerRender.UpgradeRequestToInstaller(request, installer); err != nil {
		return err
	}
	return handler.upgradeApi.createUpgradeInstaller(ctx, clusterName, *installer)
}

func setNodeType(ctx *gin.Context, request clustermodel.UpgradeClusterRequest) error {
	cluster, err := client.GetCluster(request.ClusterName)
	if err != nil {
		return err
	}
	nodeList, err := cluster.GetClient().GetKubeClient().CoreV1().Nodes().List(ctx, metav1.ListOptions{})
	if err != nil {
		return err
	}
	nodeListMap := make(map[string]v1.Node)
	for _, node := range nodeList.Items {
		nodeListMap[utils.GetNodeInternalIP(&node)] = node
	}
	var masterList []*clustermodel.NodeAuthInfo
	for _, upgradeNode := range request.Nodes {
		if node, exists := nodeListMap[upgradeNode.Ip]; exists {
			roles := utils.FindNodeRoles(&node)
			if utils.Contains(roles, constants.NodeRoleMaster) {
				masterList = append(masterList, upgradeNode)
				upgradeNode.NodeType = clustermodel.NodeTypeMaster
			} else {
				upgradeNode.NodeType = clustermodel.NodeTypeWorker
			}
		}
	}
	if masterList != nil {
		sort.Sort(clustermodel.SortIp(masterList))
		masterList[0].NodeType = clustermodel.NodeTypeMaster0
	}
	return nil
}

func (handler *upgradeClustersHandler) GetClusterUpgradeStep(ctx *gin.Context, clusterName string) (*clustermodel.UpgradeClusterStepResponse, error) {
	s := "{\n    \"clusterName\": \"cluster-161\",\n    \"status\": \"upgradeFailed\",\n    \"processing\": [\n        {\n            \"code\": \"data-initial\",\n            \"name\": \"信息提交\",\n            \"description\": \"\",\n            \"status\": \"error\",\n            \"steps\": [\n                {\n                    \"code\": \"olympus-node-initial\",\n                    \"name\": \"节点信息提交及联通性检查\",\n                    \"description\": \"\",\n                    \"status\": \"error\",\n                    \"errorType\": \"fail-validate-node\",\n                    \"errMsg\": \"Node connectivity check failed, failed node list is [*******]\"\n                },\n                {\n                    \"code\": \"olympus-apply-sisyphus-solution\",\n                    \"name\": \"集群信息提交\",\n                    \"description\": \"\",\n                    \"status\": \"wait\",\n                    \"errorType\": null,\n                    \"errMsg\": null\n                }\n            ]\n        },\n        {\n            \"code\": \"cluster-preflight-group\",\n            \"name\": \"集群创建预检\",\n            \"description\": \"\",\n            \"status\": \"wait\",\n            \"steps\": [\n                {\n                    \"code\": \"precheck-check-os\",\n                    \"name\": \"预检-检查CPU架构/操作系统\",\n                    \"description\": \"\",\n                    \"status\": \"wait\",\n                    \"errorType\": null,\n                    \"errMsg\": null\n                },\n                {\n                    \"code\": \"precheck-check-linux-packages\",\n                    \"name\": \"预检-检查是否存在已知的冲突软件包\",\n                    \"description\": \"\",\n                    \"status\": \"wait\",\n                    \"errorType\": null,\n                    \"errMsg\": null\n                }\n            ]\n        },\n        {\n            \"code\": \"cluster-create-group\",\n            \"name\": \"集群创建\",\n            \"description\": \"\",\n            \"status\": \"wait\",\n            \"steps\": [\n                {\n                    \"code\": \"edit-hostname\",\n                    \"name\": \"修改主机名\",\n                    \"description\": \"\",\n                    \"status\": \"wait\",\n                    \"errorType\": null,\n                    \"errMsg\": null\n                },\n                {\n                    \"code\": \"install-chrony-server\",\n                    \"name\": \"安装时间同步服务(服务端)\",\n                    \"description\": \"\",\n                    \"status\": \"wait\",\n                    \"errorType\": null,\n                    \"errMsg\": null\n                },\n                {\n                    \"code\": \"install-chrony-client\",\n                    \"name\": \"安装时间同步服务(客户端)\",\n                    \"description\": \"\",\n                    \"status\": \"wait\",\n                    \"errorType\": null,\n                    \"errMsg\": null\n                },\n                {\n                    \"code\": \"install-docker\",\n                    \"name\": \"安装docker容器运行时\",\n                    \"description\": \"\",\n                    \"status\": \"wait\",\n                    \"errorType\": null,\n                    \"errMsg\": null\n                }\n            ]\n        },\n        {\n            \"code\": \"add-cluster-group\",\n            \"name\": \"集群纳管\",\n            \"description\": \"\",\n            \"status\": \"wait\",\n            \"steps\": [\n                {\n                    \"code\": \"install-stellaris-proxy\",\n                    \"name\": \"集群纳管\",\n                    \"description\": \"\",\n                    \"status\": \"wait\",\n                    \"errorType\": null,\n                    \"errMsg\": null\n                }\n            ]\n        }\n    ]\n}\n"
	var response clustermodel.UpgradeClusterStepResponse
	if err := json.Unmarshal([]byte(s), &response); err != nil {
		return nil, err
	}
	return &response, nil
}

func (handler *upgradeClustersHandler) RetryClusterUpgrade(ctx *gin.Context, clusterName string) error {
	return nil
}

func (handler *upgradeClustersHandler) GetClusterUpgrade(ctx *gin.Context, clusterName string) (*clustermodel.UpgradeClusterRequest, error) {
	s := "{\n    \"packageName\": \"1.0.1\",\n    \"lbAddress\": \"************\",\n    \"upgradeType\": \"sequential\",\n    \"nodes\": [\n        {\n            \"nodeName\": \"************-master\",\n            \"ip\": \"************\",\n            \"port\": 22,\n            \"auth\": {\n                \"authType\": \"username_password\",\n                \"param\": {\n                    \"authorizationPassword\": null,\n                    \"password\": \"Ab1234\",\n                    \"username\": \"admin\"\n                }\n            }\n        },\n        {\n            \"nodeName\": \"************-node\",\n            \"ip\": \"************\",\n            \"port\": 22,\n            \"auth\": {\n                \"authType\": \"username_password\",\n                \"param\": {\n                    \"authorizationPassword\": null,\n                    \"password\": \"Ab1234\",\n                    \"username\": \"admin\"\n                }\n            }\n        },\n        {\n            \"nodeName\": \"************-node\",\n            \"ip\": \"************\",\n            \"port\": 22,\n            \"auth\": {\n                \"authType\": \"username_password\",\n                \"param\": {\n                    \"authorizationPassword\": null,\n                    \"password\": \"Ab1234\",\n                    \"username\": \"admin\"\n                }\n            }\n        },\n        {\n            \"nodeName\": \"************-node\",\n            \"ip\": \"************\",\n            \"port\": 22,\n            \"auth\": {\n                \"authType\": \"username_password\",\n                \"param\": {\n                    \"authorizationPassword\": null,\n                    \"password\": \"Ab1234\",\n                    \"username\": \"admin\"\n                }\n            }\n        }\n    ]\n}"
	var response clustermodel.UpgradeClusterRequest
	if err := json.Unmarshal([]byte(s), &response); err != nil {
		return nil, err
	}
	return &response, nil
}

func (handler *upgradeClustersHandler) checkCluster(ctx *gin.Context, clusterName string) error {
	if isExist, err := handler.api.isExist(ctx, clusterName); err != nil || !isExist {
		return errors.NewFromCode(errors.Var.ClusterNotExist)
	}
	return nil
}

func (handler *upgradeClustersHandler) checkUpgradeClusterRequest(ctx *gin.Context, request clustermodel.UpgradeClusterRequest) error {
	if request.PackageName == "" {
		return errors.NewFromCode(errors.Var.PackageNameEmpty)
	}
	if request.Nodes == nil || len(request.Nodes) == 0 {
		return errors.NewFromCode(errors.Var.NodeListEmpty)
	}
	if request.LbAddress == "" {
		return errors.NewFromCode(errors.Var.LbAddressEmpty)
	}
	return nil
}

func setMessageAndUpgrade(upgradeClusterInfo *clustermodel.UpgradeClusterInfo, stcMap map[string]*clustermodel.Response, cluster clustermodel.Response, baseLinePackageSolutionMapping map[string][]clustermodel.VersionMapping) {
	if stcMap[cluster.Name] != nil {
		if cluster.Baseline == "" {
			upgradeClusterInfo.Message = constants.ClusterUpgradeInfoMessageNotSupportUnknown
		}

		if clustermodel.ProleTypeHub.In(cluster.Prole) {
			upgradeClusterInfo.Message = constants.ClusterUpgradeInfoMessageNotSupportManager
		}
		if cluster.Baseline != "" && !clustermodel.ProleTypeHub.In(cluster.Prole) {
			if baseLinePackageSolutionMapping[cluster.Baseline] != nil && len(baseLinePackageSolutionMapping[cluster.Baseline]) > 0 {
				upgradeClusterInfo.Upgrade = true
				upgradeClusterInfo.UpgradeCode = clustermodel.SupportUpgrade
			}
		}
	}
}

func getOneBaseLinePackageSolution(ctx *gin.Context, baseline string) ([]clustermodel.VersionMapping, error) {
	versionConfigMap, err := hcclient.GetLocalCluster().GetClient().GetKubeClient().CoreV1().ConfigMaps(constants.NamespaceCaasSystem).Get(ctx, constants.ClusterUpgradeVersionMapping, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}
	versionConfigMapping := versionConfigMap.Data

	if versionConfigMapping == nil || len(versionConfigMapping) == 0 || versionConfigMapping[baseline] == "" {
		return nil, errors.NewFromCode(errors.Var.BaseLineVersionNotSupport)
	}
	var packageNameSolutionList []clustermodel.VersionMapping
	err = json.Unmarshal([]byte(versionConfigMapping[baseline]), &packageNameSolutionList)
	return packageNameSolutionList, err
}

func getBaseLinePackageSolutionMapping(ctx *gin.Context) (map[string][]clustermodel.VersionMapping, error) {
	versionConfigMap, err := hcclient.GetLocalCluster().GetClient().GetKubeClient().CoreV1().ConfigMaps(constants.NamespaceCaasSystem).Get(ctx, constants.ClusterUpgradeVersionMapping, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}
	versionConfigMapping := versionConfigMap.Data

	if versionConfigMapping == nil || len(versionConfigMapping) == 0 {
		return nil, errors.NewFromCode(errors.Var.BaseLineVersionNotSupport)
	}
	var baseLinePackageSolutionMapping = make(map[string][]clustermodel.VersionMapping)
	for k, v := range versionConfigMapping {
		var mapping []clustermodel.VersionMapping
		err = json.Unmarshal([]byte(v), &mapping)
		if err != nil {
			return nil, err
		}
		baseLinePackageSolutionMapping[k] = mapping
	}
	return baseLinePackageSolutionMapping, nil
}
