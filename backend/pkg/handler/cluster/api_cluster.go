package cluster

import (
	"context"
	"strings"

	stellarisv1alhpha1 "harmonycloud.cn/stellaris/pkg/apis/stellaris/v1alpha1"
	cloudserviceconstants "harmonycloud.cn/unifiedportal/cloudservice-operator/pkg/constants"
	hcclient "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	clsuterconstants "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/addon"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/meta"
	"k8s.io/apimachinery/pkg/api/errors"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

type apiHandler struct {
	addonHandle addon.Handler
}

func newApiHandler() api {
	return &apiHandler{
		addonHandle: addon.NewHandler(),
	}
}

// isExist
// 判断集群是否已纳管
func (*apiHandler) isExist(ctx context.Context, name string) (bool, error) {
	var cluster stellarisv1alhpha1.Cluster
	if err := hcclient.GetLocalCluster().GetClient().GetCtrlClient().Get(ctx, client.ObjectKey{Name: name}, &cluster); err != nil {
		if errors.IsNotFound(err) {
			return false, nil
		}
		return false, err
	}
	return true, nil
}

// listCluster
// 获取全部已纳管的集群
func (handle *apiHandler) listCluster(ctx context.Context) (clustermodel.ListResponse, error) {
	var clusterList stellarisv1alhpha1.ClusterList
	if err := hcclient.GetLocalCluster().GetClient().GetCtrlClient().List(ctx, &clusterList); err != nil {
		return nil, err
	}
	var response = make(clustermodel.ListResponse, 0, len(clusterList.Items))
	for _, cluster := range clusterList.Items {
		networks, err := handle.addonHandle.GetClusterNetworks(ctx, cluster.Name)
		if err != nil {
			if errors.IsNotFound(err) {
				networks = make([]string, 0)
			} else {
				return nil, err
			}
		}
		response = append(response, convertStcAsResponse(cluster, networks))
	}
	return response, nil
}

// withClusterName
// 根据集群名称 获取已纳管的集群
func (handle *apiHandler) withClusterName(ctx context.Context, name string) (*clustermodel.Response, error) {
	var cluster stellarisv1alhpha1.Cluster
	if err := hcclient.GetLocalCluster().GetClient().GetCtrlClient().Get(ctx, client.ObjectKey{Name: name}, &cluster); err != nil {
		return nil, err
	}
	networks, err := handle.addonHandle.GetClusterNetworks(ctx, cluster.Name)
	if err != nil {
		if errors.IsNotFound(err) {
			networks = make([]string, 0)
		} else {
			return nil, err
		}
	}
	res := convertStcAsResponse(cluster, networks)
	return &res, nil

}

func convertStcAsResponse(stc stellarisv1alhpha1.Cluster, networks []string) clustermodel.Response {
	label := meta.ToLabelStr(stc.Labels)
	description, _ := meta.GetFromMap(stc.Annotations, constants.StcDescriptionAnnotationKey)
	apiServerStatusStr, _ := meta.GetFromMap(stc.Labels, cloudserviceconstants.ApiServerStatus)
	baseline, _ := meta.GetFromMap(stc.Labels, constants.ClusterLabelsKeyBaseline)
	prole := getProle(stc)
	response := clustermodel.Response{
		Name:            stc.Name,
		Labels:          &label,
		Description:     &description,
		State:           clustermodel.StateTypeControlled,
		Status:          clustermodel.MustParseStatusTypeByStcType(stc.Status.Status),
		ApiServerStatus: clustermodel.MustParseApiServerStatus(apiServerStatusStr),
		NodeCount:       stc.Status.ClusterInfo.NodeCount,
		K8sVersion:      &stc.Status.ClusterInfo.KubeVersion,
		Structure:       getStcStructure(stc),
		NetworkType:     networks,
		CreateTime:      stc.CreationTimestamp.Time,
		Baseline:        baseline,
		Prole:           prole,
	}
	// 如果stc 在删除中 设置状态为删除中
	if stc.DeletionTimestamp != nil {
		response.Status = clustermodel.StatusTypeDeleting
	}
	return response
}

func getProle(stc stellarisv1alhpha1.Cluster) []clustermodel.ProleType {
	isHub, _ := meta.GetFromMap(stc.Labels, clsuterconstants.HubClusterLabelKey)
	if isHub == clsuterconstants.HubClusterLabelValue {
		return []clustermodel.ProleType{clustermodel.ProleTypeHub, clustermodel.ProleTypeBusiness}
	} else {
		return []clustermodel.ProleType{clustermodel.ProleTypeBusiness}
	}
}

func getStcStructure(stc stellarisv1alhpha1.Cluster) []string {
	arcs := stc.Status.ClusterInfo.Architectures
	for index, arc := range arcs {
		arcs[index] = strings.ToUpper(arc)
	}
	return arcs
}
