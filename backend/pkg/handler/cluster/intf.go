package cluster

import (
	"context"
	"mime/multipart"

	"github.com/gin-gonic/gin"
	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/template"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

// Handler
// 集群相关APi方法集
type Handler interface {
	ListClusters(ctx context.Context, options ...ListClusterOption) (clustermodel.ListResponse, error)
	ClusterExist(ctx context.Context, clusterName string) (*clustermodel.ExistResponse, error)
	WithClusterName(ctx context.Context, clusterName string) (*clustermodel.Response, error)
	SwitchClusterList(ctx context.Context) (*models.SwitchObjectResp, error)
}

// CreateClustersHandler
// 集群创建相关Api方法集
type CreateClustersHandler interface {
	// CreateCluster
	// 创建集群对外透出接口
	CreateCluster(ctx context.Context, request clustermodel.CreateRequest) error

	// UpdateCluster
	// 更新创建集群对外透出接口
	UpdateCluster(ctx context.Context, clusterName string, request clustermodel.CreateRequest) error

	// DeleteCluster
	// 删除创建失败的集群
	DeleteCluster(ctx context.Context, clusterName string) error

	// CreateClusterStatus
	// 集群创建状态获取
	CreateClusterStatus(ctx context.Context, clusterName string) (*clustermodel.CreateStatusResponse, error)

	// CreateResponse
	// 获取集群创建的回显表单
	CreateResponse(ctx context.Context, clusterName string) (*clustermodel.CreateResponse, error)

	// CreateLog
	// 读取创建集群过程中的日志
	CreateLog(ctx context.Context, request clustermodel.CreateLogQueryRequest) (<-chan []byte, context.Context, context.CancelFunc, error)

	// Retry
	// 集群创建失败重试
	Retry(ctx context.Context, clusterName string) error
}

// api
// 集群相关方法集
type api interface {
	// isExist
	// 判断集群是否已纳管
	isExist(ctx context.Context, name string) (bool, error)
	// listCluster
	// 获取全部已纳管的集群
	listCluster(ctx context.Context) (clustermodel.ListResponse, error)

	// withClusterName
	// 根据集群名称 获取已纳管的集群
	withClusterName(ctx context.Context, name string) (*clustermodel.Response, error)
}

// createApi
// 创建集群相关方法集
type createApi interface {
	// isExist
	// 判断集群是否正在创建中
	isExist(ctx context.Context, name string) (bool, error)

	// createInstaller
	// 将installer 分发到top集群
	createInstaller(ctx context.Context, name string, installer installerv1alpha1.Installer) error
	updateInstaller(ctx context.Context, name string, installer installerv1alpha1.Installer, patch client.Patch) error
	// listCluster
	// 获取全部创建中的集群
	listCluster(ctx context.Context) (clustermodel.ListResponse, error)

	// createClusterStatus
	// 集群创建状态获取
	createClusterStatus(ctx context.Context, clusterName string) (*clustermodel.CreateStatusResponse, error)

	// createResponse
	// 获取创建集群的回显表单
	createResponse(ctx context.Context, clusterName string) (*clustermodel.CreateResponse, error)

	// deleteCreate
	// 删除创建失败的集群
	deleteCreate(ctx context.Context, clusterName string) error

	// getInstallerByClusterName
	// 通过cluster name 获取installer
	getInstallerByClusterName(ctx context.Context, name string) (*installerv1alpha1.Installer, error)
}

// CreateInstallerNamespacePolicy
// 表示创建集群Installer 的 分区分发策略
type CreateInstallerNamespacePolicy interface {
	// namespace 通过集群名称得出installer 分发在哪个命名空间
	Namespace(ctx context.Context, clusterName string) (string, error)

	// shouldApplyNamespace 如果namespace 不存在 则创建Namespace
	ShouldApplyNamespace(ctx context.Context, clusterName string) error
}

type ListClusterOption interface {
	filter(list clustermodel.ListResponse) clustermodel.ListResponse
}

type UpgradeApi interface {
	// 创建集群升级cr Installer
	createUpgradeInstaller(ctx context.Context, clusterName string, installer installerv1alpha1.Installer) error
	// 更新集群升级cr Installer
	updateUpgradeInstaller(ctx context.Context, clusterName string, installer installerv1alpha1.Installer, patch client.Patch) error
	// 查询集群升级cr Installer
	getUpgradeInstaller(ctx context.Context, clusterName string) (*installerv1alpha1.Installer, error)
	// 列表集群升级cr Installer
	listUpgradeInstaller(ctx context.Context) (clustermodel.ListResponse, error)
}

type UpgradeClustersHandler interface {
	// ListClusterUpgrade 集群列表-集群升级显示
	ListClusterUpgrade(ctx *gin.Context) (*clustermodel.ListUpgradeClusterInfo, error)
	// ListClusterUpgradePackage 集群升级包列表
	ListClusterUpgradePackage(ctx *gin.Context, clusterName string) (*clustermodel.ListPackageInfo, error)
	// GetClusterUpgradePackage 集群升级包详情
	GetClusterUpgradePackage(ctx *gin.Context, clusterName string, packageName string) (*clustermodel.PackageContent, error)
	// GetClusterUpgradeLoadBalanceDefault 集群默认负载均衡
	GetClusterUpgradeLoadBalanceDefault(ctx *gin.Context, clusterName string) (*[]string, error)
	// ListClusterUpgradeNodes 集群升级节点列表
	ListClusterUpgradeNodes(ctx *gin.Context, clusterName string, includeNative string) (*clustermodel.ListNodeInfo, error)
	// DownloadClusterUpgradeNodeTemplate 下载集群模版添加节点列表
	DownloadClusterUpgradeNodeTemplate(ctx *gin.Context, clusterName string, includeNative string) (*template.FileDownloadInfo, error)
	// UploadClusterUpgradeNodeTemplate 上传集群模版添加节点列表
	UploadClusterUpgradeNodeTemplate(ctx *gin.Context, fhs []*multipart.FileHeader, clusterName string, includeNative string) (interface{}, error)
	// UpgradeCluster 创建集群升级对象
	UpgradeCluster(ctx *gin.Context, clusterName string, request clustermodel.UpgradeClusterRequest) error
	// GetClusterUpgradeStep 获取集群升级步骤
	GetClusterUpgradeStep(ctx *gin.Context, clusterName string) (*clustermodel.UpgradeClusterStepResponse, error)
	// RetryClusterUpgrade 重试集群升级
	RetryClusterUpgrade(ctx *gin.Context, clusterName string) error
	// GetClusterUpgrade 获取集群升级信息
	GetClusterUpgrade(ctx *gin.Context, clusterName string) (*clustermodel.UpgradeClusterRequest, error)
}
