package cluster

import (
	"context"

	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	hcclient "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	operatorconstant "harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	hcerrors "harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/cluster/render"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

type createApiHandler struct {
	namespacePolicy CreateInstallerNamespacePolicy
	mgr             render.Intf
}

func newCreateApiHandler(mgr render.Intf) createApi {
	return &createApiHandler{
		namespacePolicy: NewNamespacePolicy(),
		mgr:             mgr,
	}
}

// isExist
// 判断集群是否正在创建中
func (handler *createApiHandler) isExist(ctx context.Context, name string) (bool, error) {
	namespaceName, err := handler.namespacePolicy.Namespace(ctx, name)
	if err != nil {
		return false, err
	}
	var installerList installerv1alpha1.InstallerList
	if err := hcclient.GetLocalCluster().GetClient().GetCtrlClient().List(ctx, &installerList, client.InNamespace(namespaceName), client.MatchingLabels{
		operatorconstant.InstallTypeLabelKey:              operatorconstant.CreateClusterInstallerTypeLabelValue,
		operatorconstant.CreateClusterClusterNameLabelKey: name,
	}); err != nil {
		return false, err
	}
	return len(installerList.Items) > 0, nil
}

// createInstaller
// 将installer 分发到top集群
func (handler *createApiHandler) createInstaller(ctx context.Context, name string, installer installerv1alpha1.Installer) error {
	// 获取namespace 名称
	namespaceName, err := handler.namespacePolicy.Namespace(ctx, name)
	if err != nil {
		return err
	}
	// 保证Namespace就绪（Namespace已存在）
	if err := handler.namespacePolicy.ShouldApplyNamespace(ctx, name); err != nil {
		return err
	}
	// 设置installer 的分发Namespace
	installer.Namespace = namespaceName
	// 与集群上创建installer
	return hcclient.GetLocalCluster().GetClient().GetCtrlClient().Create(ctx, &installer)

}

// updateInstaller
// 将installer 分发到top集群
func (handler *createApiHandler) updateInstaller(ctx context.Context, name string, installer installerv1alpha1.Installer, patch client.Patch) error {
	// 获取namespace 名称
	namespaceName, err := handler.namespacePolicy.Namespace(ctx, name)
	if err != nil {
		return err
	}
	// 保证Namespace就绪（Namespace已存在）
	if err := handler.namespacePolicy.ShouldApplyNamespace(ctx, name); err != nil {
		return err
	}
	// 设置installer 的分发Namespace
	installer.Namespace = namespaceName
	installer.Spec.ReApply = true
	return hcclient.GetLocalCluster().GetClient().GetCtrlClient().Patch(ctx, &installer, patch)
}

// listCluster
// 获取全部创建中的集群
func (handler *createApiHandler) listCluster(ctx context.Context) (clustermodel.ListResponse, error) {
	var installerList installerv1alpha1.InstallerList
	if err := hcclient.GetLocalCluster().GetClient().GetCtrlClient().List(ctx, &installerList, client.MatchingLabels{
		operatorconstant.InstallTypeLabelKey: operatorconstant.CreateClusterInstallerTypeLabelValue,
	}, client.HasLabels{operatorconstant.CreateClusterClusterNameLabelKey}); err != nil {
		return nil, err
	}
	var result = make(clustermodel.ListResponse, 0, len(installerList.Items))
	for _, installer := range installerList.Items {
		response := new(clustermodel.Response)
		response.Prole = []clustermodel.ProleType{clustermodel.ProleTypeBusiness}
		if err := handler.mgr.InstallerToResponse(installer, response); err != nil {
			continue
		}
		result = append(result, *response)
	}
	return result, nil
}

// createClusterStatus
// 集群创建状态获取
func (handler *createApiHandler) createClusterStatus(ctx context.Context, name string) (*clustermodel.CreateStatusResponse, error) {
	var result *clustermodel.CreateStatusResponse
	installer, err := handler.getInstallerByClusterName(ctx, name)
	if err != nil {
		return result, err
	}
	result = new(clustermodel.CreateStatusResponse)
	renderErr := handler.mgr.InstallerToStatusResponse(*installer, result)
	return result, renderErr

}

// createResponse
// 获取创建集群的回显表单
func (handler *createApiHandler) createResponse(ctx context.Context, name string) (*clustermodel.CreateResponse, error) {
	var result *clustermodel.CreateResponse
	installer, err := handler.getInstallerByClusterName(ctx, name)
	if err != nil {
		return result, err
	}
	result = new(clustermodel.CreateResponse)
	renderErr := handler.mgr.InstallerRenderToCreateResponse(*installer, result)
	return result, renderErr
}

// deleteCreate
// 删除创建失败的集群
func (handler *createApiHandler) deleteCreate(ctx context.Context, name string) error {
	// 如果installer 存在 则判断状态删除 否则返回
	if oldInstaller, err := handler.getInstallerByClusterName(ctx, name); err != nil {
		switch err.(type) {
		case hcerrors.Error:
			hcerr := err.(hcerrors.Error)
			if hcerr.ErrorCode == hcerrors.Var.ClusterCreateTaskNotExist {
				return nil
			}
			return err
		default:
			return err
		}
	} else {
		statusResponse := &clustermodel.CreateStatusResponse{}
		if err := handler.mgr.InstallerToStatusResponse(*oldInstaller, statusResponse); err != nil {
			return err
		}
		if !clustermodel.FailedClusterStatusTypes.Has(statusResponse.Status) {
			return hcerrors.NewFromCode(hcerrors.Var.ClusterCouldNotDeleteByStatus)
		}

		return hcclient.GetLocalCluster().GetClient().GetCtrlClient().Delete(ctx, oldInstaller)
	}

}
func (handler *createApiHandler) getInstallerByClusterName(ctx context.Context, name string) (*installerv1alpha1.Installer, error) {
	namespaceName, err := handler.namespacePolicy.Namespace(ctx, name)
	if err != nil {
		return nil, err
	}
	var installerList installerv1alpha1.InstallerList
	if err := hcclient.GetLocalCluster().GetClient().GetCtrlClient().List(ctx, &installerList, client.InNamespace(namespaceName), client.MatchingLabels{
		operatorconstant.InstallTypeLabelKey:              operatorconstant.CreateClusterInstallerTypeLabelValue,
		operatorconstant.CreateClusterClusterNameLabelKey: name,
	}); err != nil {
		return nil, err
	}
	switch len(installerList.Items) {
	case 0:
		return nil, hcerrors.NewFromCodeWithMessage(hcerrors.Var.ClusterCreateTaskNotExist, "clusterName="+name)
	case 1:
		return &installerList.Items[0], nil
	default:
		return nil, hcerrors.NewFromCodeWithMessage(hcerrors.Var.ClusterInstallerNumberError, "clusterName="+name)
	}

}
