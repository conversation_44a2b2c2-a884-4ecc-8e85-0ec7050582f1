package cluster

import (
	"context"

	hcclient "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

// todo 先写死在caas-system 中方便调试
func NewNamespacePolicy() CreateInstallerNamespacePolicy {
	return caasSystemNamespacePolicy{}
}

type caasSystemNamespacePolicy struct {
}

func (p caasSystemNamespacePolicy) Namespace(ctx context.Context, clusterName string) (string, error) {
	return "caas-system", nil
}

// shouldApplyNamespace 如果namespace 不存在 则创建Namespace
func (p caasSystemNamespacePolicy) ShouldApplyNamespace(ctx context.Context, clusterName string) error {
	// 获取namespace 名称
	namespaceName, err := p.Namespace(ctx, clusterName)
	if err != nil {
		return err
	}
	// 判断namespace 是否存在
	var namespaceExistFlag bool
	namespace := v1.Namespace{}
	if err := hcclient.GetLocalCluster().GetClient().GetCtrlClient().Get(ctx, client.ObjectKey{Name: namespaceName}, &namespace); err != nil {
		if !errors.IsNotFound(err) {
			return err
		}
	} else {
		namespaceExistFlag = true
	}
	// namespace 不存在创建namespace
	if !namespaceExistFlag {
		namespace.Name = namespaceName
		if err := hcclient.GetLocalCluster().GetClient().GetCtrlClient().Create(ctx, &namespace); err != nil {
			return err
		}
	}
	return nil

}
