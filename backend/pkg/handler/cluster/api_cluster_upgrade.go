package cluster

import (
	"context"

	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	hcclient "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/upgraderender"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

type UpgradeApiHandler struct {
	upgradeInstaller upgraderender.UpgradeInterface
}

func newUpgradeApiHandler(upgradeInstaller upgraderender.UpgradeInterface) UpgradeApi {
	return &UpgradeApiHandler{
		upgradeInstaller: upgradeInstaller,
	}
}

func (u UpgradeApiHandler) createUpgradeInstaller(ctx context.Context, clusterName string, installer installerv1alpha1.Installer) error {
	namespaceName, err := u.namespace(ctx, clusterName)
	if err != nil {
		return err
	}
	if err := u.distributionNamespace(ctx, clusterName); err != nil {
		return err
	}
	installer.Namespace = namespaceName
	//return hcclient.GetLocalCluster().GetClient().GetCtrlClient().Create(ctx, &installer)
	return nil
}

func (u UpgradeApiHandler) updateUpgradeInstaller(ctx context.Context, clusterName string, installer installerv1alpha1.Installer, patch client.Patch) error {
	//TODO implement me
	panic("implement me")
}

func (u UpgradeApiHandler) getUpgradeInstaller(ctx context.Context, clusterName string) (*installerv1alpha1.Installer, error) {
	//TODO implement me
	panic("implement me")
}

func (u UpgradeApiHandler) listUpgradeInstaller(ctx context.Context) (clustermodel.ListResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (u UpgradeApiHandler) get(ctx context.Context) (clustermodel.ListResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (u UpgradeApiHandler) namespace(ctx context.Context, clusterName string) (string, error) {
	return constants.NamespaceDefault, nil
}

func (u UpgradeApiHandler) distributionNamespace(ctx context.Context, clusterName string) error {
	namespaceName, err := u.namespace(ctx, clusterName)
	if err != nil {
		return err
	}
	var namespaceExistFlag bool
	namespace := v1.Namespace{}
	if err := hcclient.GetLocalCluster().GetClient().GetCtrlClient().Get(ctx, client.ObjectKey{Name: namespaceName}, &namespace); err != nil {
		if !errors.IsNotFound(err) {
			return err
		}
	} else {
		namespaceExistFlag = true
	}
	if !namespaceExistFlag {
		namespace.Name = namespaceName
		if err := hcclient.GetLocalCluster().GetClient().GetCtrlClient().Create(ctx, &namespace); err != nil {
			return err
		}
	}
	return nil
}
