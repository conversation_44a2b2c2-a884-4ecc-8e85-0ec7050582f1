package cluster

import (
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

func MustListClusterStateOption(state string) ListClusterOption {
	var should bool
	var stateType clustermodel.StateType
	switch state {
	case string(clustermodel.StateTypeControlled):
		should = true
		stateType = clustermodel.StateTypeControlled
	case string(clustermodel.StateTypeUnControlled):
		should = true
		stateType = clustermodel.StateTypeUnControlled
	default:
		// nothing to do
	}
	return listClusterStateOption{
		should:    should,
		stateType: stateType,
	}
}

// listClusterStateOption
// 集群已纳管 | 未纳理 过滤器
type listClusterStateOption struct {
	should    bool
	stateType clustermodel.StateType
}

func (option listClusterStateOption) filter(list clustermodel.ListResponse) clustermodel.ListResponse {
	if !option.should {
		return list
	}
	size := len(list)
	offset := 0
	for index, _ := range list {
		if list[index-offset].State != option.stateType {
			list = utils.SliceRemoveByIndex(list, index, offset)
			offset++
		}
	}
	return list[0 : size-offset]
}

func MustListClusterStatusOption(status string) ListClusterOption {
	var should bool
	var statusType clustermodel.StatusType
	switch status {
	case string(clustermodel.StatusTypeDeleting):
		should = true
		statusType = clustermodel.StatusTypeDeleting
	case string(clustermodel.StatusTypePreflighting):
		should = true
		statusType = clustermodel.StatusTypePreflighting
	case string(clustermodel.StatusTypeInstalling):
		should = true
		statusType = clustermodel.StatusTypeInstalling
	case string(clustermodel.StatusTypeJoining):
		should = true
		statusType = clustermodel.StatusTypeJoining
	case string(clustermodel.StatusTypePreflightFailed):
		should = true
		statusType = clustermodel.StatusTypePreflightFailed
	case string(clustermodel.StatusTypeInstallFailed):
		should = true
		statusType = clustermodel.StatusTypeInstallFailed
	case string(clustermodel.StatusTypeJoinFail):
		should = true
		statusType = clustermodel.StatusTypeJoinFail
	case string(clustermodel.StatusTypeOnlineStatus):
		should = true
		statusType = clustermodel.StatusTypeOnlineStatus
	case string(clustermodel.StatusTypeOfflineStatus):
		should = true
		statusType = clustermodel.StatusTypeOfflineStatus
	case string(clustermodel.StatusTypeInitializingStatus):
		should = true
		statusType = clustermodel.StatusTypeInitializingStatus
	case string(clustermodel.StatusTypeUnKnow):
		should = true
		statusType = clustermodel.StatusTypeUnKnow
	default:
		// nothing to do
	}
	return listClusterStatusOption{
		should:     should,
		statusType: statusType,
	}
}

// listClusterStatusOption
// 集群状态选择过滤器
type listClusterStatusOption struct {
	should     bool
	statusType clustermodel.StatusType
}

func (option listClusterStatusOption) filter(list clustermodel.ListResponse) clustermodel.ListResponse {
	if !option.should {
		return list
	}
	size := len(list)
	offset := 0
	for index, _ := range list {
		if list[index-offset].Status != option.statusType {
			list = utils.SliceRemoveByIndex(list, index, offset)
			offset++
		}
	}
	return list[0 : size-offset]
}

func MustListClusterApiServerStatusOption(apiServerStatus string) ListClusterOption {
	var should bool
	var apiServerStatusType clustermodel.ApiServerStatusType
	switch apiServerStatus {
	case string(clustermodel.ApiServerStatusTypeSuccess):
		should = true
		apiServerStatusType = clustermodel.ApiServerStatusTypeSuccess
	case string(clustermodel.ApiServerStatusTypeFail):
		should = true
		apiServerStatusType = clustermodel.ApiServerStatusTypeFail
	case string(clustermodel.ApiServerStatusTypeInitialize):
		should = true
		apiServerStatusType = clustermodel.ApiServerStatusTypeInitialize
	default:
		// nothing to do
	}
	return listClusterApiServerStatusOption{
		should:              should,
		apiServerStatusType: apiServerStatusType,
	}
}

// listClusterApiServerStatusOption
// 集群状态选择过滤器
type listClusterApiServerStatusOption struct {
	should              bool
	apiServerStatusType clustermodel.ApiServerStatusType
}

func (option listClusterApiServerStatusOption) filter(list clustermodel.ListResponse) clustermodel.ListResponse {
	if !option.should {
		return list
	}
	size := len(list)
	offset := 0
	for index, _ := range list {
		if list[index-offset].ApiServerStatus != option.apiServerStatusType {
			list = utils.SliceRemoveByIndex(list, index, offset)
			offset++
		}
	}
	return list[0 : size-offset]
}
