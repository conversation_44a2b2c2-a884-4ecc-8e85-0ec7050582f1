package cluster

import (
	"fmt"
	"reflect"
	"testing"

	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
)

func Test_listClusterStateOption_filter(t *testing.T) {
	params := []struct {
		state      string
		inputList  clustermodel.ListResponse
		outputList clustermodel.ListResponse
	}{
		{
			state: "",
			inputList: clustermodel.ListResponse{
				{
					Name:  "",
					State: clustermodel.StateTypeControlled,
				},
				{
					Name:  "",
					State: clustermodel.StateTypeUnControlled,
				},
			},
			outputList: clustermodel.ListResponse{
				{
					Name:  "",
					State: clustermodel.StateTypeControlled,
				},
				{
					Name:  "",
					State: clustermodel.StateTypeUnControlled,
				},
			},
		},
		{
			state: string(clustermodel.StateTypeControlled),
			inputList: clustermodel.ListResponse{
				{
					Name:  "",
					State: clustermodel.StateTypeControlled,
				},
				{
					Name:  "",
					State: clustermodel.StateTypeUnControlled,
				},
			},
			outputList: clustermodel.ListResponse{
				{
					Name:  "",
					State: clustermodel.StateTypeControlled,
				},
			},
		},
		{
			state: string(clustermodel.StateTypeUnControlled),
			inputList: clustermodel.ListResponse{
				{
					Name:  "",
					State: clustermodel.StateTypeControlled,
				},
				{
					Name:  "",
					State: clustermodel.StateTypeUnControlled,
				},
			},
			outputList: clustermodel.ListResponse{
				{
					Name:  "",
					State: clustermodel.StateTypeUnControlled,
				},
			},
		},
	}
	for index, param := range params {
		name := fmt.Sprintf("Test_listClusterStateOption_filter_%d", index)
		t.Run(name, func(t *testing.T) {
			option := MustListClusterStateOption(param.state)
			filter := option.filter(param.inputList)
			if !reflect.DeepEqual(filter, param.outputList) {
				t.Errorf("error appear on '%s',state is '%s',input is '%#v',want output is '%#v',result is '%#v'", name, param.state, param.inputList, param.outputList, filter)
			}
		})

	}
}
