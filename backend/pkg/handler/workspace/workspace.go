package workspace

import (
	context "context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"sort"
	"strconv"
	"strings"
	"sync"
	"time"

	apisixapisv2 "github.com/apache/apisix-ingress-controller/pkg/kube/apisix/apis/config/v2"
	cloudservicev1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/v1alpha1"
	"harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/workspace/helper"
	workspace "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/workspace"
	appV1 "k8s.io/api/apps/v1"
	batchv1 "k8s.io/api/batch/v1"
	v1 "k8s.io/api/core/v1"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/klog/v2"
	ctrlclient "sigs.k8s.io/controller-runtime/pkg/client"
	runtimeclient "sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/apiutil"
)

type workspaceHandler struct {
}

var ResourceTypeListMap = map[string]workspace.WorkspaceResourceType{
	"container-service-Deployment": {
		ID:            1,
		ResourceType:  "deployments",
		ResourceName:  "container-service-Deployment",
		ResourceAlias: "无状态部署",
		Group:         "apps",
		Version:       "v1",
		EnableJump:    true,
		Url:           "/project/space/resource/deployment/list",
		IconName:      "stateless",
		IconUrl:       "",
	},
	"container-service-Statefulset": {
		ID:            2,
		ResourceType:  "statefulsets",
		ResourceName:  "container-service-Statefulset",
		ResourceAlias: "有状态部署",
		Group:         "apps",
		Version:       "v1",
		EnableJump:    true,
		Url:           "/project/space/resource/statefulset/list",
		IconName:      "stateful",
		IconUrl:       "",
	},
	"container-service-Daemonset": {
		ID:            3,
		ResourceType:  "daemonsets",
		ResourceName:  "container-service-Daemonset",
		ResourceAlias: "守护进程",
		Group:         "apps",
		Version:       "v1",
		EnableJump:    true,
		Url:           "/project/space/resource/daemonset/list",
		IconName:      "daemonset",
		IconUrl:       "",
	},
	"container-service-Job": {
		ID:            4,
		ResourceType:  "jobs",
		ResourceName:  "container-service-Job",
		ResourceAlias: "普通任务",
		Group:         "batch",
		Version:       "v1",
		EnableJump:    true,
		Url:           "/project/space/resource/job/list",
		IconName:      "noraml-job",
		IconUrl:       "",
	},
	"container-service-CronJob": {
		ID:            5,
		ResourceType:  "cronjobs",
		ResourceName:  "container-service-CronJob",
		ResourceAlias: "定时任务",
		Group:         "batch",
		Version:       "v1",
		EnableJump:    true,
		Url:           "/project/space/resource/cronjob/list",
		IconName:      "cronjob",
		IconUrl:       "",
	},
	"container-service-Pod": {
		ID:            6,
		ResourceType:  "pods",
		ResourceName:  "container-service-Pod",
		ResourceAlias: "Pod容器组",
		Group:         "",
		Version:       "v1",
		EnableJump:    true,
		Url:           "/project/space/resource/pod",
		IconName:      "Podrongqizu",
		IconUrl:       "",
	},
	"container-service-ConfigMap": {
		ID:            7,
		ResourceType:  "configmaps",
		ResourceName:  "container-service-ConfigMap",
		ResourceAlias: "配置文件",
		Group:         "",
		Version:       "v1",
		EnableJump:    true,
		Url:           "/project/space/configMap/list",
		IconName:      "config-file",
		IconUrl:       "",
	},
	"container-service-Secret": {
		ID:            8,
		ResourceType:  "secrets",
		ResourceName:  "container-service-Secret",
		ResourceAlias: "保密字典",
		Group:         "",
		Version:       "v1",
		EnableJump:    true,
		Url:           "/project/space/secret/list",
		IconName:      "key",
		IconUrl:       "",
	},
	"container-service-PersistentVolumeClaim": {
		ID:            9,
		ResourceType:  "persistentVolumeClaims",
		ResourceName:  "container-service-PersistentVolumeClaim",
		ResourceAlias: "持久卷声明",
		Group:         "",
		Version:       "v1",
		EnableJump:    true,
		Url:           "/project/space/pvc/list",
		IconName:      "pvc",
		IconUrl:       "",
	},
	"container-service-Service": {
		ID:            11,
		ResourceType:  "services",
		ResourceName:  "container-service-Service",
		ResourceAlias: "Service服务",
		Group:         "",
		Version:       "v1",
		EnableJump:    true,
		Url:           "/project/space/network/service",
		IconName:      "services-exposure-internal",
		IconUrl:       "",
	},
	"container-service-Ingress": {
		ID:            12,
		ResourceType:  "ingresses",
		ResourceName:  "container-service-Ingress",
		ResourceAlias: "Ingress路由",
		Group:         "networking.k8s.io",
		Version:       "v1",
		EnableJump:    true,
		Url:           "/project/space/network/ingress",
		IconName:      "ingressluyou",
		IconUrl:       "",
	},
}

func (w *workspaceHandler) GetCloudServicesCard(ctx context.Context) ([]string, error) {
	cloudservices := &cloudservicev1alpha1.CloudServiceList{}

	if err := client.GetLocalCluster().GetClient().GetCtrlClient().List(ctx, cloudservices, &ctrlclient.ListOptions{}); err != nil {
		return nil, err
	}

	var modelCloudServices = make([]string, 0, len(cloudservices.Items))
	for _, cloudservice := range cloudservices.Items {
		_, exist := cloudservice.Annotations[constants.CloudServiceProjectResourceListAnnotationKey]
		if exist {
			modelCloudServices = append(modelCloudServices, cloudservice.Name)
		}
	}
	sort.Slice(cloudservices.Items, func(i, j int) bool {
		return cloudservices.Items[i].CreationTimestamp.Time.Before(cloudservices.Items[j].CreationTimestamp.Time)
	})

	return modelCloudServices, nil
}

func (w *workspaceHandler) GetCloudServiceResourceList(ctx context.Context, token string, userId int64, projectId string, organId string, cloudServiceName string, resourceType string, withProjectId string) (workspace.CloudServiceResource, error) {

	cloudService := cloudservicev1alpha1.CloudService{}
	if err := client.GetLocalCluster().GetClient().GetCtrlClient().Get(ctx, ctrlclient.ObjectKey{Name: cloudServiceName}, &cloudService); err != nil {
		return workspace.CloudServiceResource{}, err
	}
	//resourceAPIBaseURL := "http://10.120.1.58/caas-core/portal/workspace/resource"
	resourceAPIBaseURL := helper.GetProjectResourceListURLFromAnnotations(cloudService.Annotations)
	if cloudServiceName == "container-service" {
		resourceGroup := workspace.ResourceGroup{}
		projectResource := workspace.ProjectResource{}
		if resourceType != "" && withProjectId == "true" {
			err := w.GetProjectResource(ctx, resourceType, token, &projectResource)
			if err != nil {
				return workspace.CloudServiceResource{}, err
			}
		} else {
			if projectId == "" && organId == "" {
				resourceInstanceBaseURL := fmt.Sprintf("%s/auth/projects", config.OlympusCoreAddress.Value)
				list, err := getProjectList(resourceInstanceBaseURL, token)
				if err != nil {
					return workspace.CloudServiceResource{}, errors.NewFromCodeWithMessage(errors.Var.CloudServiceListResourcesError, err.Error())
				}
				var resourceInstanceIdList []string
				for _, resourceInstance := range list.ResourceInstanceList {
					resourceInstanceIdList = append(resourceInstanceIdList, resourceInstance.ResourceInstanceId)
				}
				err = w.GetTotalWorkspaceResource(ctx, resourceInstanceIdList, &resourceGroup)
				if err != nil {
					return workspace.CloudServiceResource{}, err
				}
			} else {
				projectIds := []string{projectId}
				err := w.GetTotalWorkspaceResource(ctx, projectIds, &resourceGroup)
				if err != nil {
					return workspace.CloudServiceResource{}, err
				}
			}
		}
		portalWorkspaceResource := workspace.CloudServiceResource{
			TabName:             "container-service-resource",
			TabAlias:            "容器资源",
			CloudServiceName:    "container-service",
			ProjectResourceList: projectResource,
			ResourceGroups:      []workspace.ResourceGroup{resourceGroup},
		}
		return portalWorkspaceResource, nil
	} else {
		if resourceAPIBaseURL == "" {
			return workspace.CloudServiceResource{}, errors.NewFromCodeWithMessage(errors.Var.CloudServiceListResourcesError, "no project resource list url")
		}
		cloudServiceResource, err := requestResourceListApi(resourceAPIBaseURL, token, userId, projectId, organId, resourceType, withProjectId)
		if err != nil {
			return workspace.CloudServiceResource{}, errors.NewFromCodeWithMessage(errors.Var.CloudServiceListResourcesError, err.Error())
		}

		return *cloudServiceResource, nil
	}

}

func (w *workspaceHandler) GetTotalWorkspaceResource(ctx context.Context, list []string, resourceGroup *workspace.ResourceGroup) error {
	var resourceList []workspace.Resource
	for k, v := range ResourceTypeListMap {
		if k == "container-service-Ingress" {
			ingressList, _ := w.GetProjectResources(ctx, v.Group, v.Version, v.ResourceType, list)
			// resourceType -> ApisixRoute
			gvkForObject, _ := apiutil.GVKForObject(&apisixapisv2.ApisixRoute{}, client.Scheme)
			apisixList, _ := w.GetProjectResources(ctx, gvkForObject.Group, gvkForObject.Version, strings.ToLower(gvkForObject.Kind)+"s", list)
			resources, err := GetIngress(v, ingressList, apisixList)
			if err != nil {
				return err
			}
			resourceList = append(resourceList, resources...)
		} else {
			result, err := w.GetProjectResources(ctx, v.Group, v.Version, v.ResourceType, list)
			if err != nil {
				return err
			}
			resource, err := w.getResource(ctx, v, result)
			resourceList = append(resourceList, resource...)
		}
	}
	andSort := resourceRemoveEmptyAndSort(resourceList)
	resourceGroup.ResourceList = andSort
	resourceGroup.WithGroup = false
	return nil
}

func (w *workspaceHandler) GetProjectResource(ctx context.Context, resourceType string, token string, resource *workspace.ProjectResource) error {
	resourceInstanceBaseURL := fmt.Sprintf("%s/auth/projects", config.OlympusCoreAddress.Value)
	var projectResourceInfoList []workspace.ProjectResourceInfo
	list, err := getProjectList(resourceInstanceBaseURL, token)
	if err != nil {
		return errors.NewFromCodeWithMessage(errors.Var.CloudServiceListResourcesError, err.Error())
	}
	for _, resourceInstance := range list.ResourceInstanceList {
		if resourceType == "container-service-Ingress" {
			result, _ := w.GetProjectResources(ctx, ResourceTypeListMap[resourceType].Group, ResourceTypeListMap[resourceType].Version, ResourceTypeListMap[resourceType].ResourceType, []string{resourceInstance.ResourceInstanceId})
			gvkForObject, _ := apiutil.GVKForObject(&apisixapisv2.ApisixRoute{}, client.Scheme)
			apisixList, _ := w.GetProjectResources(ctx, gvkForObject.Group, gvkForObject.Version, strings.ToLower(gvkForObject.Kind)+"s", []string{resourceInstance.ResourceInstanceId})
			workspaceIngress, err := GetWorkspaceIngress(result, apisixList, &resourceInstance)
			if err != nil {
				return err
			}
			projectResourceInfoList = append(projectResourceInfoList, workspaceIngress...)
		} else {
			result, err := w.GetProjectResources(ctx, ResourceTypeListMap[resourceType].Group, ResourceTypeListMap[resourceType].Version, ResourceTypeListMap[resourceType].ResourceType, []string{resourceInstance.ResourceInstanceId})
			if err != nil {
				return err
			}
			if len(result.Items) == 0 {
				continue
			}
			workspaceResource, err := w.getWorkspaceResource(ctx, resourceType, result, &resourceInstance)
			projectResourceInfoList = append(projectResourceInfoList, workspaceResource...)
		}
	}
	//resource = &workspace.ProjectResource{
	//	ResourceType:            resourceType,
	//	EnableJump:              ResourceTypeListMap[resourceType].EnableJump,
	//	JumpUrl:                 ResourceTypeListMap[resourceType].Url,
	//	ProjectResourceInfoList: projectResourceInfoList,
	//}
	infoList := filterAndSortProjectResourceInfoList(projectResourceInfoList)
	resource.JumpUrl = ResourceTypeListMap[resourceType].Url
	resource.ResourceType = resourceType
	resource.EnableJump = ResourceTypeListMap[resourceType].EnableJump
	resource.ProjectResourceInfoList = infoList
	return nil
}

func requestResourceListApi(baseURL string, token string, userId int64, projectId string, organId string, resourceType string, withProjectId string) (*workspace.CloudServiceResource, error) {
	httpClient := http.Client{}
	u, err := url.Parse(baseURL)
	if err != nil {
		return nil, fmt.Errorf("error parsing URL %w", err)
	}
	q := u.Query()
	q.Set("userId", strconv.FormatInt(userId, 10))
	q.Set("projectId", projectId)
	q.Set("organId", organId)
	q.Set("resourceType", resourceType)
	q.Set("withProjectId", withProjectId)
	u.RawQuery = q.Encode()
	request, err := http.NewRequestWithContext(context.Background(), http.MethodGet, u.String(), nil)
	// Make the HTTP request
	request.Header.Add("Authorization", token)
	resp, err := httpClient.Do(request)
	if err != nil {
		klog.Errorf("err: %v", err)
		return nil, errors.NewFromCode(errors.Var.ConnectFailure)
	} else if resp.StatusCode == 401 {
		return nil, errors.NewFromCode(errors.Var.TokenExpire)
	}
	defer resp.Body.Close()
	rawBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %v", err)
	}
	resource := &workspace.CloudServiceResource{}
	if err := json.Unmarshal(rawBytes, resource); err != nil {
		klog.Infof("deserializing response body: %v", err)
		return nil, err
	}
	return resource, nil
}

func (w *workspaceHandler) GetProjectResources(ctx context.Context, group string, version string, resourceType string, projectIds []string) (*unstructured.UnstructuredList, error) {

	clusterNamespaceMap, err := getProjectNamespace(ctx, projectIds)
	if err != nil {
		return nil, err
	}

	resourceChan := make(chan *unstructured.UnstructuredList, 10)
	wg := &sync.WaitGroup{}
	for k, v := range clusterNamespaceMap {
		wg.Add(len(v))
		for _, namespace := range v {
			go func(cluster string, namespace string) {
				defer wg.Done()
				p := &common.ReadParam{
					Cluster:      cluster,
					Group:        group,
					Version:      version,
					Namespace:    namespace,
					ResourceType: resourceType,
				}
				unstructuredList, err := common.NewCommonHandler().ListWithoutPage(ctx, p)
				if err != nil {
					klog.Infof("get %v error,namespace:%v,error:%v", resourceType, namespace, err.Error())
					return
				}
				resourceChan <- unstructuredList
			}(k, namespace)
		}
	}
	go func() {
		// 等待所有 goroutine 完成后关闭通道
		wg.Wait()
		close(resourceChan)
	}()
	totalObjectList := &unstructured.UnstructuredList{}
	totalObjectList.Items = make([]unstructured.Unstructured, 0)
	for resource := range resourceChan {
		totalObjectList.Items = append(totalObjectList.Items, resource.Items...)
	}
	return totalObjectList, nil
}

func getProjectNamespace(ctx context.Context, projectIds []string) (map[string][]string, error) {
	clusters := client.ListOnlineClusters()
	wg := sync.WaitGroup{}
	nsChan := make(chan workspace.ClusterNamespace, 10)
	for _, cluster := range clusters {
		if cluster.GetName() == "cluster-top" {
			continue
		}
		wg.Add(len(projectIds))
		for _, projectId := range projectIds {
			go func(cluster client.Cluster, projectId string) {
				defer wg.Done()
				namespaceList := &v1.NamespaceList{}
				err := cluster.GetClient().GetCtrlClient().List(ctx, namespaceList, runtimeclient.MatchingLabels{"skyview/project": projectId})
				if apierrors.IsNotFound(err) {
					return
				}
				if err != nil {
					klog.Infof("get namespace error,cluster:%s", cluster.GetName())
				}
				clusterNamespace := workspace.ClusterNamespace{
					Cluster:       cluster.GetName(),
					NamespaceList: namespaceList.Items,
				}
				nsChan <- clusterNamespace
			}(cluster, projectId)
		}
	}
	go func() {
		// 等待所有 goroutine 完成后关闭通道
		wg.Wait()
		close(nsChan)
	}()
	var items []workspace.ClusterNamespace
	clusterNamespaceMap := make(map[string][]string, 0)

	for clusterNs := range nsChan {
		items = append(items, clusterNs)
	}

	for _, clusterNs := range items {
		_, exist := clusterNamespaceMap[clusterNs.Cluster]
		if len(clusterNs.NamespaceList) == 0 {
			continue
		}
		if exist {
			for _, ns := range clusterNs.NamespaceList {
				clusterNamespaceMap[clusterNs.Cluster] = append(clusterNamespaceMap[clusterNs.Cluster], ns.Name)
			}
		} else {
			nsNameList := make([]string, 0)
			for _, ns := range clusterNs.NamespaceList {
				nsNameList = append(nsNameList, ns.Name)
			}
			clusterNamespaceMap[clusterNs.Cluster] = nsNameList
		}
	}
	return clusterNamespaceMap, nil
}

func (w *workspaceHandler) getWorkspaceResource(ctx context.Context, resourceType string, result *unstructured.UnstructuredList, resourceInstance *workspace.ResourceInstance) ([]workspace.ProjectResourceInfo, error) {
	state := &[]workspace.ResourceState{}
	switch resourceType {
	case "container-service-Deployment":
		{
			deploymentList := &appV1.DeploymentList{}
			err := runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), deploymentList)
			if err != nil {
				return nil, errors.NewFromError(ctx, err)
			}
			info, err := getDeploymentInfo(deploymentList)
			if err != nil {
				return nil, err
			}
			state = &info
		}
	case "container-service-Statefulset":
		{
			statefulSetList := &appV1.StatefulSetList{}
			err := runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), statefulSetList)
			if err != nil {
				return nil, errors.NewFromError(ctx, err)
			}
			info, err := getStatefulset(statefulSetList)
			if err != nil {
				return nil, err
			}
			state = &info
		}
	case "container-service-Daemonset":
		{
			daemonSetList := &appV1.DaemonSetList{}
			err := runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), daemonSetList)
			if err != nil {
				return nil, errors.NewFromError(ctx, err)
			}
			info, err := getDaemonset(daemonSetList)
			if err != nil {
				return nil, err
			}
			state = &info
		}
	case "container-service-Job":
		{
			jobList := &batchv1.JobList{}
			err := runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), jobList)
			if err != nil {
				return nil, errors.NewFromError(ctx, err)
			}
			info, err := getJob(jobList)
			if err != nil {
				return nil, err
			}
			state = &info
		}
	case "container-service-CronJob":
		{
			cronJobList := &batchv1.CronJobList{}
			err := runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), cronJobList)
			if err != nil {
				return nil, errors.NewFromError(ctx, err)
			}
			info, err := getCronJob(cronJobList)
			if err != nil {
				return nil, err
			}
			state = &info
		}
	case "container-service-Pod":
		{
			pods := &v1.PodList{}
			err := runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), pods)
			if err != nil {
				return nil, errors.NewFromError(ctx, err)
			}
			info, err := getPod(pods)
			if err != nil {
				return nil, err
			}
			state = &info
		}
	case "container-service-ConfigMap":
		{
			configMapList := &v1.ConfigMapList{}
			err := runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), configMapList)
			if err != nil {
				return nil, errors.NewFromError(ctx, err)
			}
			info, err := getConfigMap(configMapList)
			if err != nil {
				return nil, err
			}
			state = &info
		}
	case "container-service-Secret":
		{
			secretList := &v1.SecretList{}
			err := runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), secretList)
			if err != nil {
				return nil, errors.NewFromError(ctx, err)
			}
			info, err := getSecret(secretList)
			if err != nil {
				return nil, err
			}
			state = &info
		}
	case "container-service-PersistentVolumeClaim":
		{
			persistentVolumeClaimList := &v1.PersistentVolumeClaimList{}
			err := runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), persistentVolumeClaimList)
			if err != nil {
				return nil, errors.NewFromError(ctx, err)
			}
			info, err := getPersistentVolumeClaim(persistentVolumeClaimList)
			if err != nil {
				return nil, err
			}
			state = &info
		}
	case "container-service-Service":
		{
			serviceList := &v1.ServiceList{}
			err := runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), serviceList)
			if err != nil {
				return nil, errors.NewFromError(ctx, err)
			}
			info, err := getService(serviceList)
			if err != nil {
				return nil, err
			}
			state = &info
		}
	}
	projectResourceInfo := workspace.ProjectResourceInfo{
		OrganId:              resourceInstance.OrganId,
		CreateTime:           resourceInstance.CreateTime,
		ProjectId:            resourceInstance.ResourceInstanceId,
		ProjectName:          resourceInstance.ResourceInstanceName,
		ProjectResourceState: *state,
	}

	return []workspace.ProjectResourceInfo{projectResourceInfo}, nil
}

func getService(list *v1.ServiceList) ([]workspace.ResourceState, error) {
	var clusterIp, nodePort int
	for _, service := range list.Items {
		switch service.Spec.Type {
		case "ClusterIP":
			clusterIp++
		case "NodePort":
			nodePort++
		}
	}

	clusterIpState := workspace.ResourceState{
		Count:              clusterIp,
		ResourceStateColor: "green",
		ResourceStateName:  "container-service-ClusterIP",
		ResourceStateAlias: "ClusterIP",
	}

	nodePortState := workspace.ResourceState{
		Count:              nodePort,
		ResourceStateColor: "blue",
		ResourceStateName:  "container-service-NodePort",
		ResourceStateAlias: "NodePort",
	}

	return []workspace.ResourceState{clusterIpState, nodePortState}, nil
}

func getIngress(ingressList *unstructured.UnstructuredList, apisixList *unstructured.UnstructuredList) ([]workspace.ResourceState, error) {
	var ingressCount, apisixCount int
	ingressCount = len(ingressList.Items)
	if apisixList == nil {
		apisixCount = 0
	} else {
		apisixCount = len(apisixList.Items)
	}
	clusterIpState := workspace.ResourceState{
		Count:              ingressCount,
		ResourceStateColor: "green",
		ResourceStateName:  "container-service-Nginx",
		ResourceStateAlias: "Nginx",
	}

	nodePortState := workspace.ResourceState{
		Count:              apisixCount,
		ResourceStateColor: "blue",
		ResourceStateName:  "container-service-APISIX",
		ResourceStateAlias: "APISIX",
	}

	return []workspace.ResourceState{clusterIpState, nodePortState}, nil
}

func getPersistentVolumeClaim(list *v1.PersistentVolumeClaimList) ([]workspace.ResourceState, error) {
	var lost, bound, pending int
	for _, pvc := range list.Items {
		switch pvc.Status.Phase {
		case "Lost":
			lost++
		case "Bound":
			bound++
		case "Pending":
			pending++
		}
	}

	boundState := workspace.ResourceState{
		Count:              bound,
		ResourceStateColor: "green",
		ResourceStateName:  "container-service-Bound",
		ResourceStateAlias: "绑定",
	}

	pendingState := workspace.ResourceState{
		Count:              pending,
		ResourceStateColor: "blue",
		ResourceStateName:  "container-service-Pending",
		ResourceStateAlias: "挂起",
	}

	lostState := workspace.ResourceState{
		Count:              lost,
		ResourceStateColor: "red",
		ResourceStateName:  "container-service-Lost",
		ResourceStateAlias: "丢失",
	}

	return []workspace.ResourceState{boundState, pendingState, lostState}, nil
}

func getSecret(list *v1.SecretList) ([]workspace.ResourceState, error) {
	var opaque, tls, other int
	for _, secret := range list.Items {
		switch secret.Type {
		case "Opaque":
			opaque++
		case "kubernetes.io/tls":
			tls++
		default:
			other++
		}
	}

	opaqueState := workspace.ResourceState{
		Count:              opaque,
		ResourceStateColor: "green",
		ResourceStateName:  "container-service-Opaque",
		ResourceStateAlias: "键值对",
	}

	tlsState := workspace.ResourceState{
		Count:              tls,
		ResourceStateColor: "blue",
		ResourceStateName:  "container-service-kubernetes.io/tls",
		ResourceStateAlias: "TLS数字证书",
	}

	otherState := workspace.ResourceState{
		Count:              other,
		ResourceStateColor: "grey",
		ResourceStateName:  "container-service-Secret-other",
		ResourceStateAlias: "其他",
		Tips:               "包括镜像仓库、用户名及密码、自定义类型",
	}

	return []workspace.ResourceState{opaqueState, tlsState, otherState}, nil
}

func getConfigMap(list *v1.ConfigMapList) ([]workspace.ResourceState, error) {
	count := len(list.Items)

	runningState := workspace.ResourceState{
		Count:              count,
		ResourceStateColor: "green",
		ResourceStateName:  "container-service-Succeed",
		ResourceStateAlias: "成功",
	}

	return []workspace.ResourceState{runningState}, nil
}

func getPod(pods *v1.PodList) ([]workspace.ResourceState, error) {
	var running, pending, other int
	for _, pod := range pods.Items {
		switch pod.Status.Phase {
		case "Running":
			isError := false
			for _, containerStatus := range pod.Status.ContainerStatuses {
				if containerStatus.State.Running == nil {
					isError = true
					break
				}
			}
			if isError {
				other++
			} else {
				running++
			}
		case "Pending":
			pending++
		default:
			other++
		}
	}

	runningState := workspace.ResourceState{
		Count:              running,
		ResourceStateColor: "green",
		ResourceStateName:  "container-service-Running",
		ResourceStateAlias: "运行中",
	}

	pendingState := workspace.ResourceState{
		Count:              pending,
		ResourceStateColor: "red",
		ResourceStateName:  "container-service-Pending",
		ResourceStateAlias: "等待中",
	}

	otherState := workspace.ResourceState{
		Count:              other,
		ResourceStateColor: "grey",
		ResourceStateName:  "container-service-Pod-other",
		ResourceStateAlias: "其他",
		Tips:               "包括已完成、失败、运行错误、未知错误",
	}

	return []workspace.ResourceState{runningState, pendingState, otherState}, nil
}

func getCronJob(list *batchv1.CronJobList) ([]workspace.ResourceState, error) {
	var running, paused int
	for _, cronJob := range list.Items {
		if !*cronJob.Spec.Suspend {
			running++
		} else {
			paused++
		}
	}

	runningState := workspace.ResourceState{
		Count:              running,
		ResourceStateColor: "green",
		ResourceStateName:  "container-service-Running",
		ResourceStateAlias: "运行中",
	}

	pausedState := workspace.ResourceState{
		Count:              paused,
		ResourceStateColor: "grey",
		ResourceStateName:  "container-service-Stopped",
		ResourceStateAlias: "暂停中",
	}

	return []workspace.ResourceState{runningState, pausedState}, nil
}

func getJob(jobList *batchv1.JobList) ([]workspace.ResourceState, error) {
	var running, succeed, other int
	for _, job := range jobList.Items {
		if job.Status.Active != 0 && job.Status.Active > 0 {
			running++
		} else if job.Status.Succeeded != 0 && job.Status.Succeeded > 0 {
			succeed++
		} else {
			other++
		}
	}

	runningState := workspace.ResourceState{
		Count:              running,
		ResourceStateColor: "green",
		ResourceStateName:  "container-service-Running",
		ResourceStateAlias: "运行中",
	}

	succeedState := workspace.ResourceState{
		Count:              succeed,
		ResourceStateColor: "blue",
		ResourceStateName:  "container-service-Succeed",
		ResourceStateAlias: "运行成功",
	}

	otherState := workspace.ResourceState{
		Count:              other,
		ResourceStateColor: "grey",
		ResourceStateName:  "container-service-Job-other",
		ResourceStateAlias: "其他",
		Tips:               "包括停止中、运行失败、未知错误",
	}

	return []workspace.ResourceState{runningState, succeedState, otherState}, nil
}

func getDaemonset(list *appV1.DaemonSetList) ([]workspace.ResourceState, error) {
	var started, starting int
	for _, daemonSet := range list.Items {
		if daemonSet.Status.NumberAvailable != 0 &&
			daemonSet.Status.DesiredNumberScheduled != 0 &&
			daemonSet.Status.NumberAvailable == daemonSet.Status.DesiredNumberScheduled {
			started++
		} else {
			starting++
		}
	}

	startedState := workspace.ResourceState{
		Count:              started,
		ResourceStateColor: "green",
		ResourceStateName:  "container-service-Started",
		ResourceStateAlias: "运行中",
	}

	createdState := workspace.ResourceState{
		Count:              starting,
		ResourceStateColor: "blue",
		ResourceStateName:  "container-service-Starting",
		ResourceStateAlias: "创建中",
	}

	return []workspace.ResourceState{startedState, createdState}, nil
}

func getStatefulset(list *appV1.StatefulSetList) ([]workspace.ResourceState, error) {
	var started, stopped, other int
	for _, statefulSet := range list.Items {
		if statefulSet.Spec.Replicas != nil && *statefulSet.Spec.Replicas > 0 &&
			statefulSet.Status.ReadyReplicas != 0 &&
			statefulSet.Status.ReadyReplicas == *statefulSet.Spec.Replicas {
			started++
		} else if statefulSet.Spec.Replicas != nil &&
			*statefulSet.Spec.Replicas == 0 &&
			statefulSet.Status.Replicas == 0 &&
			statefulSet.Status.ReadyReplicas == 0 {
			stopped++
		} else {
			other++
		}
	}

	startedState := workspace.ResourceState{
		Count:              started,
		ResourceStateColor: "green",
		ResourceStateName:  "container-service-Started",
		ResourceStateAlias: "运行中",
	}

	stoppedState := workspace.ResourceState{
		Count:              stopped,
		ResourceStateColor: "red",
		ResourceStateName:  "container-service-Stopped",
		ResourceStateAlias: "已停止",
	}

	otherState := workspace.ResourceState{
		Count:              other,
		ResourceStateColor: "grey",
		ResourceStateName:  "container-service-Statefulset-other",
		ResourceStateAlias: "其他",
		Tips:               "包括创建中、停止中、未知错误",
	}

	return []workspace.ResourceState{startedState, stoppedState, otherState}, nil
}

func processData(deploy *appV1.Deployment) error {
	//
	deploy.Annotations = nil
	return nil
}

func processDataWithVal(deploy appV1.Deployment) (appV1.Deployment, error) {
	deploy.Annotations = nil
	return deploy, nil
}

func getDeploymentInfo(list *appV1.DeploymentList) ([]workspace.ResourceState, error) {
	var started, stopped, other int
	for _, deployment := range list.Items {
		if deployment.Spec.Replicas != nil && *deployment.Spec.Replicas > 0 &&
			deployment.Status.UnavailableReplicas == 0 &&
			deployment.Status.ReadyReplicas != 0 &&
			deployment.Status.ReadyReplicas == *deployment.Spec.Replicas {
			started++
		} else if deployment.Spec.Replicas != nil &&
			*deployment.Spec.Replicas == 0 &&
			deployment.Status.Replicas == 0 &&
			deployment.Status.ReadyReplicas == 0 {
			stopped++
		} else {
			other++
		}
	}

	startedState := workspace.ResourceState{
		Count:              started,
		ResourceStateColor: "green",
		ResourceStateName:  "container-service-Started",
		ResourceStateAlias: "运行中",
	}

	stoppedState := workspace.ResourceState{
		Count:              stopped,
		ResourceStateColor: "red",
		ResourceStateName:  "container-service-Stopped",
		ResourceStateAlias: "已停止",
	}

	otherState := workspace.ResourceState{
		Count:              other,
		ResourceStateColor: "grey",
		ResourceStateName:  "container-service-Deployment-other",
		ResourceStateAlias: "其他",
		Tips:               "包括创建中、停止中、回滚中、未知错误",
	}

	return []workspace.ResourceState{startedState, stoppedState, otherState}, nil
}

func getProjectList(baseURL string, token string) (*workspace.ResourceInstanceInfo, error) {
	httpClient := http.Client{}
	u, err := url.Parse(baseURL)
	if err != nil {
		return nil, fmt.Errorf("error parsing URL %w", err)
	}
	request, err := http.NewRequestWithContext(context.Background(), http.MethodGet, u.String(), nil)
	request.Header.Add("Authorization", token)
	request.Header.Add("Amp-Organ-Id", "1")
	resp, err := httpClient.Do(request)
	if err != nil {
		klog.Errorf("err: %v", err)
		return nil, errors.NewFromCode(errors.Var.ConnectFailure)
	} else if resp.StatusCode == 401 {
		return nil, errors.NewFromCode(errors.Var.TokenExpire)
	}
	rawBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("error reading response body: %v", err)
	}
	a := &workspace.ResourceInstanceInfo{}
	if err := json.Unmarshal(rawBytes, &a); err != nil {
		klog.Infof("deserializing response body: %v", err)
		return nil, err
	}
	return a, nil
}

func (w *workspaceHandler) getResource(ctx context.Context, resourceType workspace.WorkspaceResourceType, result *unstructured.UnstructuredList) ([]workspace.Resource, error) {
	state := &[]workspace.ResourceState{}
	switch resourceType.ResourceName {
	case "container-service-Deployment":
		{
			deploymentList := &appV1.DeploymentList{}
			err := runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), deploymentList)
			if err != nil {
				return nil, errors.NewFromError(ctx, err)
			}
			info, err := getDeploymentInfo(deploymentList)
			if err != nil {
				return nil, err
			}
			state = &info
		}
	case "container-service-Statefulset":
		{
			statefulSetList := &appV1.StatefulSetList{}
			err := runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), statefulSetList)
			if err != nil {
				return nil, errors.NewFromError(ctx, err)
			}
			info, err := getStatefulset(statefulSetList)
			if err != nil {
				return nil, err
			}
			state = &info
		}
	case "container-service-Daemonset":
		{
			daemonSetList := &appV1.DaemonSetList{}
			err := runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), daemonSetList)
			if err != nil {
				return nil, errors.NewFromError(ctx, err)
			}
			info, err := getDaemonset(daemonSetList)
			if err != nil {
				return nil, err
			}
			state = &info
		}
	case "container-service-Job":
		{
			jobList := &batchv1.JobList{}
			err := runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), jobList)
			if err != nil {
				return nil, errors.NewFromError(ctx, err)
			}
			info, err := getJob(jobList)
			if err != nil {
				return nil, err
			}
			state = &info
		}
	case "container-service-CronJob":
		{
			cronJobList := &batchv1.CronJobList{}
			err := runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), cronJobList)
			if err != nil {
				return nil, errors.NewFromError(ctx, err)
			}
			info, err := getCronJob(cronJobList)
			if err != nil {
				return nil, err
			}
			state = &info
		}
	case "container-service-Pod":
		{
			pods := &v1.PodList{}
			err := runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), pods)
			if err != nil {
				return nil, errors.NewFromError(ctx, err)
			}
			info, err := getPod(pods)
			if err != nil {
				return nil, err
			}
			state = &info
		}
	case "container-service-ConfigMap":
		{
			configMapList := &v1.ConfigMapList{}
			err := runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), configMapList)
			if err != nil {
				return nil, errors.NewFromError(ctx, err)
			}
			info, err := getConfigMap(configMapList)
			if err != nil {
				return nil, err
			}
			state = &info
		}
	case "container-service-Secret":
		{
			secretList := &v1.SecretList{}
			err := runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), secretList)
			if err != nil {
				return nil, errors.NewFromError(ctx, err)
			}
			info, err := getSecret(secretList)
			if err != nil {
				return nil, err
			}
			state = &info
		}
	case "container-service-PersistentVolumeClaim":
		{
			persistentVolumeClaimList := &v1.PersistentVolumeClaimList{}
			err := runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), persistentVolumeClaimList)
			if err != nil {
				return nil, errors.NewFromError(ctx, err)
			}
			info, err := getPersistentVolumeClaim(persistentVolumeClaimList)
			if err != nil {
				return nil, err
			}
			state = &info
		}
	case "container-service-Service":
		{
			serviceList := &v1.ServiceList{}
			err := runtime.DefaultUnstructuredConverter.FromUnstructured(result.UnstructuredContent(), serviceList)
			if err != nil {
				return nil, errors.NewFromError(ctx, err)
			}
			info, err := getService(serviceList)
			if err != nil {
				return nil, err
			}
			state = &info
		}
	}

	sum := 0
	for _, value := range *state {
		sum += value.Count
	}
	resourceIcon := workspace.Icon{
		Name: resourceType.IconName,
		URL:  resourceType.IconUrl,
	}
	resource := workspace.Resource{
		Id:                 resourceType.ID,
		ResourceName:       resourceType.ResourceName,
		ResourceAlias:      resourceType.ResourceAlias,
		Icon:               resourceIcon,
		ResourceTotalCount: sum,
		ResourceStateList:  *state,
	}
	return []workspace.Resource{resource}, nil
}

func GetIngress(resourceType workspace.WorkspaceResourceType, ingressList *unstructured.UnstructuredList, apiSixList *unstructured.UnstructuredList) ([]workspace.Resource, error) {
	state := &[]workspace.ResourceState{}
	info, err := getIngress(ingressList, apiSixList)
	if err != nil {
		return nil, err
	}
	state = &info

	sum := 0
	for _, value := range *state {
		sum += value.Count
	}
	resourceIcon := workspace.Icon{
		Name: resourceType.IconName,
		URL:  resourceType.IconUrl,
	}
	resource := workspace.Resource{
		Id:                 resourceType.ID,
		ResourceName:       resourceType.ResourceName,
		ResourceAlias:      resourceType.ResourceAlias,
		Icon:               resourceIcon,
		ResourceTotalCount: sum,
		ResourceStateList:  *state,
	}
	return []workspace.Resource{resource}, nil
}

func GetWorkspaceIngress(ingressList *unstructured.UnstructuredList, apisixList *unstructured.UnstructuredList, resourceInstance *workspace.ResourceInstance) ([]workspace.ProjectResourceInfo, error) {
	state := &[]workspace.ResourceState{}
	info, err := getIngress(ingressList, apisixList)
	if err != nil {
		return nil, err
	}
	state = &info
	projectResourceInfo := workspace.ProjectResourceInfo{
		OrganId:              resourceInstance.OrganId,
		CreateTime:           resourceInstance.CreateTime,
		ProjectId:            resourceInstance.ResourceInstanceId,
		ProjectName:          resourceInstance.ResourceInstanceName,
		ProjectResourceState: *state,
	}

	return []workspace.ProjectResourceInfo{projectResourceInfo}, nil
}

func resourceRemoveEmptyAndSort(resourceList []workspace.Resource) []workspace.Resource {
	var newResourceList []workspace.Resource
	for _, resource := range resourceList {
		if resource.ResourceTotalCount != 0 {
			newResourceList = append(newResourceList, resource)
		}
	}
	sort.Slice(newResourceList, func(i, j int) bool {
		return newResourceList[i].Id < newResourceList[j].Id
	})
	return newResourceList
}

func filterAndSortProjectResourceInfoList(resourceInfoList []workspace.ProjectResourceInfo) []workspace.ProjectResourceInfo {
	var resInfoList []workspace.ProjectResourceInfo
	for _, info := range resourceInfoList {
		if len(info.ProjectResourceState) == 0 {
			continue
		}
		allZero := true
		for _, state := range info.ProjectResourceState {
			if state.Count != 0 {
				allZero = false
				break
			}
		}
		if allZero {
			continue
		}
		resInfoList = append(resInfoList, info)
	}

	sort.Slice(resInfoList, func(i, j int) bool {
		iTime, _ := time.Parse(time.DateTime, resInfoList[i].CreateTime)
		jTime, _ := time.Parse(time.DateTime, resInfoList[j].CreateTime)
		return iTime.Before(jTime)
	})

	return resInfoList
}
