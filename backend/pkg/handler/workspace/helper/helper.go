package helper

import (
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
)

// GetProjectResourceListURLFromAnnotations returns the URL of the project resource list based on the given annotations.
//
// It takes a map of string to string as the parameter `annotations`.
// The return type is string.
func GetProjectResourceListURLFromAnnotations(annotations map[string]string) string {
	if annotations == nil {
		return ""
	}
	return annotations[constants.CloudServiceProjectResourceListAnnotationKey]
}
