package workspace

import (
	"context"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/workspace"
	database_aop "harmonycloud.cn/unifiedportal/translate-sdk-golang/database-aop"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
)

func NewWorkspaceHandler() WorkspaceIntf {
	th := &translateHandler{}
	th.handler = &workspaceHandler{}
	return th
}

type WorkspaceIntf interface {
	GetCloudServicesCard(ctx context.Context) ([]string, error)
	GetCloudServiceResourceList(ctx context.Context, token string, userId int64, projectId string, organId string, cloudServiceName string, resourceType string, withProjectId string) (workspace.CloudServiceResource, error)
	GetProjectResources(ctx context.Context, group string, version string, resourceType string, projectIds []string) (*unstructured.UnstructuredList, error)
}

type translateHandler struct {
	handler WorkspaceIntf
}

func (t translateHandler) GetCloudServicesCard(ctx context.Context) ([]string, error) {
	result, err := t.handler.GetCloudServicesCard(ctx)
	return result, err
}

func (t translateHandler) GetCloudServiceResourceList(ctx context.Context, token string, userId int64, projectId string, organId string, cloudServiceName string, resourceType string, withProjectId string) (workspace.CloudServiceResource, error) {
	result, err := t.handler.GetCloudServiceResourceList(ctx, token, userId, projectId, organId, cloudServiceName, resourceType, withProjectId)
	database_aop.DoTranslate(ctx, &result, err)
	return result, err
}

func (t translateHandler) GetProjectResources(ctx context.Context, group string, version string, resourceType string, projectIds []string) (*unstructured.UnstructuredList, error) {
	result, err := t.handler.GetProjectResources(ctx, group, version, resourceType, projectIds)
	return result, err
}
