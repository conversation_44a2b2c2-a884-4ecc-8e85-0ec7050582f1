package draft

import (
	"context"
	"encoding/json"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/database"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/auth"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/draft"
)

func NewHandler() Handler {
	return &handler{
		authHandler: auth.NewHandler(),
	}
}

type Handler interface {
	// SaveDraft
	// 保存草稿
	SaveDraft(ctx context.Context, req *draft.Request) error

	// GetDraft
	// 读取草稿
	GetDraft(ctx context.Context, draftType string) (*draft.Response, error)

	// DeleteDraft
	// 删除草稿
	DeleteDraft(ctx context.Context, draftType string) error
}

type handler struct {
	authHandler auth.Handler
}

// SaveDraft
// 保存草稿
func (dh *handler) SaveDraft(ctx context.Context, req *draft.Request) error {
	currentUser, err := dh.authHandler.CurrentUser(ctx)
	if err != nil {
		return err
	}
	data, err := req.GetParamBytes()
	if err != nil {
		return err
	}

	userId := currentUser.GetUserId()
	userDraft := &dao.UserDraft{
		UserId: userId,
		Type:   req.Type,
		Data:   data,
	}
	return database.CaasDB.Clauses(clause.OnConflict{
		Columns:   []clause.Column{{Name: "user_id"}, {Name: "draft_type"}},
		DoUpdates: clause.AssignmentColumns([]string{"draft_data"}),
	}).Create(userDraft).Error
}

// GetDraft
// 读取草稿
func (dh *handler) GetDraft(ctx context.Context, draftType string) (*draft.Response, error) {
	currentUser, err := dh.authHandler.CurrentUser(ctx)
	if err != nil {
		return nil, err
	}
	userId := currentUser.GetUserId()
	userDraft := &dao.UserDraft{
		UserId: userId,
		Type:   draftType,
	}
	if err := database.CaasDB.Where(userDraft).Take(userDraft).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return &draft.Response{
				Status: draft.NotExists,
			}, nil
		}
		return nil, err
	}
	var data map[string]interface{}
	if err := json.Unmarshal(userDraft.Data, &data); err != nil {
		return nil, err
	}
	return &draft.Response{
		Status: draft.Exists,
		Data: &draft.DataResponse{
			Type:  userDraft.Type,
			Draft: data,
		},
	}, nil
}

// DeleteDraft
// 删除草稿
func (dh *handler) DeleteDraft(ctx context.Context, draftType string) error {
	currentUser, err := dh.authHandler.CurrentUser(ctx)
	if err != nil {
		return err
	}
	userId := currentUser.GetUserId()
	userDraft := &dao.UserDraft{
		UserId: userId,
		Type:   draftType,
	}
	return database.CaasDB.Where(userDraft).Delete(userDraft).Error

}
