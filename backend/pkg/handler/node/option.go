package node

import (
	"strings"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	"k8s.io/apimachinery/pkg/util/sets"
)

type ListControlNodeFilter interface {
	Filter(node.SimpleNodeResponseList) node.SimpleNodeResponseList
}

func NewSimpleNodeExcludeIpFilter(excludeIps string) ListControlNodeFilter {
	ips := strings.Split(excludeIps, ",")
	return simpleNodeExcludeIpFilter{
		excludeIpSet: sets.New[string](ips...),
	}
}

type simpleNodeExcludeIpFilter struct {
	excludeIpSet sets.Set[string]
}

func (filter simpleNodeExcludeIpFilter) Filter(items node.SimpleNodeResponseList) node.SimpleNodeResponseList {
	if len(filter.excludeIpSet) == 0 {
		return items
	}
	size := len(items)
	offset := 0
	for idx, _ := range items {
		if filter.excludeIpSet.Has(items[idx-offset].HostIP) {
			utils.SliceRemoveByIndex(items, idx, offset)
			offset++
		}
	}
	return items[0 : size-offset]
}

type NodeUpDownResponseFilter interface {
	Filter(responses node.NodeUpDownResponseList) node.NodeUpDownResponseList
}

func NewNodeUpDownResponseFilterWithStatuses(statuses string) NodeUpDownResponseFilter {
	var should bool
	var statusSet sets.Set[string]
	if statuses != "" {
		should = true
		statusSet = sets.New[string](strings.Split(statuses, ",")...)
	}
	return nodeUpDownResponseFilterWithStatuses{
		should:    should,
		statusSet: statusSet,
	}
}

type nodeUpDownResponseFilterWithStatuses struct {
	should    bool
	statusSet sets.Set[string]
}

func (f nodeUpDownResponseFilterWithStatuses) Filter(responses node.NodeUpDownResponseList) node.NodeUpDownResponseList {
	if !f.should {
		return responses
	}
	len := len(responses)
	offset := 0
	for index, _ := range responses {
		if !f.statusSet.Has(string(responses[index-offset].Status)) {
			utils.SliceRemoveByIndex(responses, index, offset)
			offset++
		}
	}
	return responses[0 : len-offset]
}

func NewNodeUpDownResponseFilterWithTypes(types string) NodeUpDownResponseFilter {
	var should bool
	var typesSet sets.Set[string]
	if types != "" {
		should = true
		typesSet = sets.New[string](strings.Split(types, ",")...)
	}
	return nodeUpDownResponseFilterWithTypes{
		should:   should,
		typesSet: typesSet,
	}
}

type nodeUpDownResponseFilterWithTypes struct {
	should   bool
	typesSet sets.Set[string]
}

func (f nodeUpDownResponseFilterWithTypes) Filter(responses node.NodeUpDownResponseList) node.NodeUpDownResponseList {
	if !f.should {
		return responses
	}
	len := len(responses)
	offset := 0
	for index, _ := range responses {
		if !f.typesSet.Has(string(responses[index-offset].Type)) {
			utils.SliceRemoveByIndex(responses, index, offset)
			offset++
		}
	}
	return responses[0 : len-offset]
}
