package node

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"strings"

	stellarisv1alhpha1 "harmonycloud.cn/stellaris/pkg/apis/stellaris/v1alpha1"
	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	hcclient "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	operatorconstant "harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/addon"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/log"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/installerutil"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/iputil"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/node/render"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/sisyphusutil"
	database_aop "harmonycloud.cn/unifiedportal/translate-sdk-golang/database-aop"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/util/sets"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

func NewNodeUpDownHandler() NodeUpDownHandler {
	return translateNodeUpDownHandler{
		handler: nodeUpDownHandler{
			r:                     render.NewIntf(),
			namespacePolicy:       cluster.NewNamespacePolicy(),
			nodeVerifyHandler:     NewNodeVerifyHandler(),
			controllerNodeHandler: NewControlNodeHandler(),
			installerLogHandler:   log.NewInstallerLog(),
			sisyphusConfigHandler: addon.NewSisyphusConfigHandler(),
		},
	}
}

type translateNodeUpDownHandler struct {
	handler nodeUpDownHandler
}

// Count 获取节点上显现任务数量
func (handler translateNodeUpDownHandler) Count(ctx context.Context, clusterName string, filters ...NodeUpDownResponseFilter) (*node.NodeUpDownCountResponse, error) {
	return handler.handler.Count(ctx, clusterName, filters...)
}

// Create 创建节点上下线任务
func (handler translateNodeUpDownHandler) Create(ctx context.Context, clusterName string, request node.NodeUpDownCreateRequest) error {
	return handler.handler.Create(ctx, clusterName, request)
}

// BatchCreate 批量更新
func (handler translateNodeUpDownHandler) BatchCreate(ctx context.Context, clusterName string, batchRequest node.NodeUpDownBatchCreateRequest) error {
	return handler.handler.BatchCreate(ctx, clusterName, batchRequest)
}

// List 获取节点上下线任务列表
func (handler translateNodeUpDownHandler) List(ctx context.Context, clusterName string, filters ...NodeUpDownResponseFilter) (node.NodeUpDownResponseList, error) {
	response, err := handler.handler.List(ctx, clusterName, filters...)
	database_aop.DoTranslate(ctx, response, err)
	return response, err
}

// Status 获取任务状态执行详情
func (handler translateNodeUpDownHandler) Status(ctx context.Context, clusterName, nodeIp string) (*node.NodeUpDownStatusResponse, error) {
	response, err := handler.handler.Status(ctx, clusterName, nodeIp)
	database_aop.DoTranslate(ctx, response, err)
	return response, err
}

// CreateResponse 节点上下线表单回显
func (handler translateNodeUpDownHandler) CreateResponse(ctx context.Context, clusterName, nodeIp string) (*node.NodeUpDownCreateResponse, error) {
	return handler.handler.CreateResponse(ctx, clusterName, nodeIp)
}

// Edit 编辑节点上下线信息
func (handler translateNodeUpDownHandler) Edit(ctx context.Context, clusterName, nodeIp string, request node.NodeUpDownCreateRequest) error {
	return handler.handler.Edit(ctx, clusterName, nodeIp, request)
}

// Retry 重试 相当于从失败处开始
func (handler translateNodeUpDownHandler) Retry(ctx context.Context, clusterName, nodeIp string) error {
	return handler.handler.Retry(ctx, clusterName, nodeIp)
}

// Delete 删除节点上下线任务
func (handler translateNodeUpDownHandler) Delete(ctx context.Context, clusterName, nodeIp string) error {
	return handler.handler.Delete(ctx, clusterName, nodeIp)
}

// Verify
// 节点上下线节点校验
func (handler translateNodeUpDownHandler) Verify(ctx context.Context, clusterName string, request node.NodeUpDownCreateRequest) (*node.NodeVerifyResponse, error) {
	return handler.handler.Verify(ctx, clusterName, request)
}

// UpDownLog
// 读取节点上下线日志过程中的日志
func (handler translateNodeUpDownHandler) UpDownLog(ctx context.Context, request node.UpDownLogQueryRequest) (<-chan []byte, context.Context, context.CancelFunc, error) {
	return handler.handler.UpDownLog(ctx, request)
}

type nodeUpDownHandler struct {
	r                     render.Intf
	namespacePolicy       cluster.CreateInstallerNamespacePolicy
	nodeVerifyHandler     NodeVerifyHandler
	controllerNodeHandler ControlNodeHandler
	installerLogHandler   log.InstallerLog
	// 读取下层集群的西西弗斯地址
	sisyphusConfigHandler addon.SisyphusConfigHandler
}

func (handler nodeUpDownHandler) Count(ctx context.Context, clusterName string, filters ...NodeUpDownResponseFilter) (*node.NodeUpDownCountResponse, error) {
	list, err := handler.List(ctx, clusterName, filters...)
	if err != nil {
		return nil, err
	}
	// convert
	var nodeUpDescription, nodeDownDescription = make(map[node.NodeUpDownStatus]int64), make(map[node.NodeUpDownStatus]int64)
	for _, item := range list {
		var operatorMap map[node.NodeUpDownStatus]int64
		if item.Type == node.NodeUpDownTypeNodeUp {
			operatorMap = nodeUpDescription
		} else if item.Type == node.NodeUpDownTypeNodeDown {
			operatorMap = nodeDownDescription
		}
		if operatorMap == nil {
			continue
		}
		operatorMap[item.Status] = operatorMap[item.Status] + 1
	}
	upTotal := getDescriptionCountMapTotal(nodeUpDescription)
	downTotal := getDescriptionCountMapTotal(nodeDownDescription)
	return &node.NodeUpDownCountResponse{
		Total: upTotal + downTotal,
		Description: node.NodeUpDownCountDescription{
			NodeUpTotal:         upTotal,
			NodeDownTotal:       downTotal,
			NodeUpDescription:   nodeUpDescription,
			NodeDownDescription: nodeDownDescription,
		},
	}, nil

}

func getDescriptionCountMapTotal(descriptionMap map[node.NodeUpDownStatus]int64) int64 {
	var total int64
	for _, val := range descriptionMap {
		total += val
	}
	return total
}

func (handler nodeUpDownHandler) Create(ctx context.Context, clusterName string, request node.NodeUpDownCreateRequest) error {
	if err := handler.createRequestFromVerify(ctx, clusterName, request); err != nil {
		return err
	}
	// 获取集群基线版本
	baselineVersion, err := handler.getNodeUpDownBaselineVersion(ctx, clusterName)
	if err != nil {
		return err
	}
	request.ClusterBaselineVersion = baselineVersion
	// 设置集群名称
	request.ClusterName = clusterName
	// 暂时根据基线版本设置CRI类型
	request.CRI = installerutil.GetCRITypeByBaseLineVersion(baselineVersion)

	// 校验集群下是否已存在同IP的任务
	if err := handler.ExistsVerify(ctx, clusterName, []node.NodeUpDownNodeConfigRequest{request.NodeConfig}); err != nil {
		return err
	}

	// 创建installer
	var installer installerv1alpha1.Installer
	if err := handler.r.CreateRequestRenderToInstaller(request, &installer); err != nil {
		return err
	}
	// 创建
	// 获取namespace 名称
	namespaceName, err := handler.namespacePolicy.Namespace(ctx, clusterName)
	if err != nil {
		return err
	}
	// 保证Namespace就绪（Namespace已存在）
	if err := handler.namespacePolicy.ShouldApplyNamespace(ctx, clusterName); err != nil {
		return err
	}
	// 设置installer 的分发Namespace
	installer.Namespace = namespaceName
	// 与集群上创建installer
	return hcclient.GetLocalCluster().GetClient().GetCtrlClient().Create(ctx, &installer)

}
func (handler nodeUpDownHandler) createRequestFromVerify(ctx context.Context, clusterName string, request node.NodeUpDownCreateRequest) error {
	// 集群校验
	_, err := hcclient.GetCluster(clusterName)
	if err != nil {
		return err
	}
	// 主控节点与非主控节点的IP不允许重复
	if request.ControlNode.Ip == request.NodeConfig.Ip {
		return errors.NewFromCodeWithMessage(errors.Var.ControlNodeCouldNotEqualsNodeUpDownNode, request.NodeConfig.Ip)
	}
	// 主控节点合法性校验
	if err := handler.controllerNodeHandler.Verify(ctx, clusterName, request.ControlNode.Ip); err != nil {
		return err
	}

	// 磁盘挂载校验
	if err := handler.DiskVerify(ctx, request.Type, []node.NodeUpDownNodeConfigRequest{request.NodeConfig}); err != nil {
		return err
	}
	// 任务类型与IP检查
	if err := handler.IPCheck(ctx, clusterName, request.Type, []node.NodeUpDownNodeConfigRequest{request.NodeConfig}); err != nil {
		return err
	}
	// timeServer 不可为nil
	if request.Type == node.NodeUpDownTypeNodeUp && request.TimeServer == nil {
		return errors.NewFromCodeWithMessage(errors.Var.ParamError, "timeServer address could not be nil")
	}
	return err
}

func (handler nodeUpDownHandler) BatchCreate(ctx context.Context, clusterName string, batchRequest node.NodeUpDownBatchCreateRequest) error {
	// 集群校验
	_, err := hcclient.GetCluster(clusterName)
	if err != nil {
		return err
	}
	batchRequest.ClusterName = clusterName
	// 获取集群基线版本
	baselineVersion, err := handler.getNodeUpDownBaselineVersion(ctx, clusterName)
	if err != nil {
		return err
	}
	batchRequest.ClusterBaselineVersion = baselineVersion
	batchRequest.ClusterCRI = installerutil.GetCRITypeByBaseLineVersion(baselineVersion)

	// 主控节点IP 与 非主空节点IP不允许重复
	for _, nodeConfig := range batchRequest.NodeConfigs {
		if nodeConfig.Ip == batchRequest.ControlNode.Ip {
			return errors.NewFromCodeWithMessage(errors.Var.ControlNodeCouldNotEqualsNodeUpDownNode, nodeConfig.Ip)
		}
	}
	// IP间不允许重复
	existIp := sets.NewString()
	for _, nodeConfig := range batchRequest.NodeConfigs {
		if existIp.Has(nodeConfig.Ip) {
			return errors.NewFromCodeWithMessage(errors.Var.ControlNodeCouldNotEqualsNodeUpDownNode, nodeConfig.Ip)
		}
		existIp.Insert(nodeConfig.Ip)
	}
	// 主控节点合法性校验
	if err := handler.controllerNodeHandler.Verify(ctx, clusterName, batchRequest.ControlNode.Ip); err != nil {
		return err
	}
	// 校验集群下是否已存在同IP的任务
	if err := handler.ExistsVerify(ctx, clusterName, batchRequest.NodeConfigs); err != nil {
		return err
	}
	// 磁盘挂载校验
	if err := handler.DiskVerify(ctx, batchRequest.Type, batchRequest.NodeConfigs); err != nil {
		return err
	}
	// 任务类型与IP检查
	if err := handler.IPCheck(ctx, clusterName, batchRequest.Type, batchRequest.NodeConfigs); err != nil {
		return err
	}
	// timeServer 不可为nil
	if batchRequest.TimeServer == nil {
		return errors.NewFromCodeWithMessage(errors.Var.ParamError, "timeServer address could not be nil")
	}
	// 创建 installer
	var installer installerv1alpha1.Installer
	if err := handler.r.BatchCreateRequestRenderToInstaller(batchRequest, &installer); err != nil {
		return err
	}

	// 获取namespace 名称
	namespaceName, err := handler.namespacePolicy.Namespace(ctx, clusterName)
	if err != nil {
		return err
	}
	// 保证Namespace就绪（Namespace已存在）
	if err := handler.namespacePolicy.ShouldApplyNamespace(ctx, clusterName); err != nil {
		return err
	}
	// 设置installer 的分发Namespace
	installer.Namespace = namespaceName
	// 设置installer template 分发的 namespace name
	for taskIdx, task := range installer.Spec.Tasks {
		if task.CreateInstallerByTemplate != nil {
			installer.Spec.Tasks[taskIdx].CreateInstallerByTemplate.Template.ObjectMeta.Namespace = namespaceName
		}
	}

	// 与集群上创建installer
	return hcclient.GetLocalCluster().GetClient().GetCtrlClient().Create(ctx, &installer)

}

func (handler nodeUpDownHandler) ExistsVerify(ctx context.Context, clusterName string, nodes []node.NodeUpDownNodeConfigRequest) error {

	_, err := hcclient.GetCluster(clusterName)
	if err != nil {
		return err
	}
	// 定义installer 查询条件
	var matchLabels = make(client.MatchingLabels)
	switch len(nodes) {
	case 1:
		// 记录节点上下线的IP
		matchLabels[operatorconstant.NodeUpDownIPLabelKey] = iputil.FormatLabelValueForIP(nodes[0].Ip)
		fallthrough
	default:
		// 标记类型 节点上下线
		matchLabels[operatorconstant.InstallTypeLabelKey] = operatorconstant.NodeUpDownInstallerTypeLabelValue
		// 标记集群名称
		matchLabels[operatorconstant.NodeUpDownClusterNameLabelKey] = clusterName
	}
	// 查询installer
	var installerList installerv1alpha1.InstallerList
	if err := hcclient.GetLocalCluster().GetClient().GetCtrlClient().List(ctx, &installerList, matchLabels); err != nil {
		return err
	}
	// 转化 installer 为DTO
	nodeUpDownList, err := handler.convertInstaller(installerList.Items)
	var ipSets = sets.New[string]()
	for _, nodeUpDown := range nodeUpDownList {
		ipSets = ipSets.Insert(nodeUpDown.Ip)
	}
	// 判断IP 是否存在
	var existIpsSet = sets.New[string]()
	for _, node := range nodes {
		ip := node.Ip
		if ipSets.Has(node.Ip) {
			existIpsSet.Insert(ip)
		}
	}

	if existIpsSet.Len() != 0 {
		existIps := existIpsSet.UnsortedList()
		sort.Strings(existIps)
		message := strings.Join(existIps, ",")
		return errors.NewFromCodeWithMessage(errors.Var.ExistNodeUpDownNode, message)
	}
	return nil

}

func (handler nodeUpDownHandler) DiskVerify(ctx context.Context, nodeUpDownType node.NodeUpDownType, nodes []node.NodeUpDownNodeConfigRequest) error {
	if nodeUpDownType != node.NodeUpDownTypeNodeUp {
		return nil
	}
	// 如果为自动挂载，则必须写kubelet、docker
	for index, node := range nodes {
		storageReq := node.NodeStorageRequest
		if storageReq.Type != clustermodel.NodeStorageTypeAuto {
			continue
		}
		if storageReq.DiskPath == nil || storageReq.DiskPath.Docker == "" || storageReq.DiskPath.Kubelet == "" {
			return errors.NewFromCodeWithMessage(errors.Var.NodeUpDownDiskParamMustWriteWhenNodeDiskTypeAuto, "ip="+node.Ip)
		}
		nodes[index].NodeStorageRequest.DiskPath.ETCD = ""
		nodes[index].NodeStorageRequest.DiskPath.System = ""
	}
	return nil
}

// IPCheck
// 节点下线必须集群中的已存在节点
// 节点上线必须是集群中的不存在节点
func (handler nodeUpDownHandler) IPCheck(ctx context.Context, clusterName string, nodeUpDownType node.NodeUpDownType, nodes []node.NodeUpDownNodeConfigRequest) error {
	cluster, err := hcclient.GetCluster(clusterName)
	if err != nil {
		return err
	}
	var nodeList v1.NodeList
	if err := cluster.GetClient().GetCtrlClient().List(ctx, &nodeList); err != nil {
		return err
	}
	k8sNodeIps := sets.New[string]()
	for _, node := range nodeList.Items {
		for _, address := range node.Status.Addresses {
			switch address.Type {
			case v1.NodeInternalIP:
				k8sNodeIps.Insert(address.Address)
			}
		}
	}
	// if nodeUpDownType == nodeUp
	//       if nodeInCluster
	//               return err
	// if nodeUpDownType == nodeDown
	//       if !nodeInCluster
	//               return err
	for _, excuteNode := range nodes {
		nodeInCluster := k8sNodeIps.Has(excuteNode.Ip)
		if nodeUpDownType == node.NodeUpDownTypeNodeUp && nodeInCluster {
			return errors.NewFromCodeWithMessage(errors.Var.NodeUpNodeMustNotInCluster, excuteNode.Ip)
		} else if nodeUpDownType == node.NodeUpDownTypeNodeDown && !nodeInCluster {
			return errors.NewFromCodeWithMessage(errors.Var.NodeDownNodeMustNotInCluster, excuteNode.Ip)
		}
	}
	return err

}

func (handler nodeUpDownHandler) List(ctx context.Context, clusterName string, filters ...NodeUpDownResponseFilter) (node.NodeUpDownResponseList, error) {
	// 查询installer
	var installerList installerv1alpha1.InstallerList
	if err := hcclient.GetLocalCluster().GetClient().GetCtrlClient().List(ctx, &installerList, client.MatchingLabels{
		operatorconstant.InstallTypeLabelKey:           operatorconstant.NodeUpDownInstallerTypeLabelValue, // 节点上下线类型
		operatorconstant.NodeUpDownClusterNameLabelKey: clusterName,                                        // 集群名称
	}, client.HasLabels{operatorconstant.NodeUpDownTypeLabelKey}); err != nil {
		return nil, err
	}
	result, err := handler.convertInstaller(installerList.Items)
	if err != nil {
		return nil, err
	}
	for _, f := range filters {
		result = f.Filter(result)
	}

	for idx, _ := range result {
		result[idx].Calculate()
	}

	return result, nil

}

func (handler nodeUpDownHandler) convertInstaller(installerList []installerv1alpha1.Installer) (node.NodeUpDownResponseList, error) {
	// 对批量 与 非批量进行分类
	var installers []installerv1alpha1.Installer      // 定义节点上下线类型的installer
	var batchInstallers []installerv1alpha1.Installer // 定义批量创建的installer
	for _, installer := range installerList {
		installer := installer
		typeValue := installer.Labels[operatorconstant.NodeUpDownTypeLabelKey]
		if typeValue == string(node.NodeUpDownTypeBatch) {
			batchInstallers = append(batchInstallers, installer)
		} else {
			installers = append(installers, installer)
		}
	}
	// 将 installers、batchInstallers 转化为 Response
	var nodeUpDownList = make(node.NodeUpDownResponseList, 0, len(installers))
	var nodeUpDownBatchList = make(node.NodeUpDownResponseList, 0, len(batchInstallers)<<1)
	// 处理非批量的
	for _, installer := range installers {
		var response node.NodeUpDownResponse
		if err := handler.r.InstallerToResponse(installer, &response); err != nil {
			return nil, err
		}
		nodeUpDownList = append(nodeUpDownList, response)
	}
	// 处理批量的
	for _, installer := range batchInstallers {
		var responseList = new(node.NodeUpDownResponseList)
		if err := handler.r.BatchInstallerToBatchResponse(installer, responseList); err != nil {
			return nil, err
		}
		nodeUpDownBatchList = append(nodeUpDownBatchList, *responseList...)
	}
	// 如果批量的已通过template 创建 installer 则可以忽略
	mapset := make(map[string]node.NodeUpDownResponse)
	for _, res := range nodeUpDownBatchList {
		mapset[res.Ip] = res
	}
	for _, res := range nodeUpDownList {
		mapset[res.Ip] = res
	}
	var result = make(node.NodeUpDownResponseList, 0, len(mapset))
	for _, value := range mapset {
		result = append(result, value)
	}
	sort.Sort(result)
	return result, nil
}

func (handler nodeUpDownHandler) Status(ctx context.Context, clusterName, nodeIp string) (*node.NodeUpDownStatusResponse, error) {
	installer, err := handler.getInstallerOrMockTemplate(ctx, clusterName, nodeIp)
	if err != nil {
		switch err.(type) {
		case errors.Error:
			e := err.(errors.Error)
			if e.ResponseCode != errors.Var.InstallerNotExist.ResponseCode {
				return nil, err
			}
			var nodeName *string
			cluster, err := hcclient.GetCluster(clusterName)
			if err != nil {
				return nil, err
			}
			var nodeList v1.NodeList
			if err := cluster.GetClient().GetCtrlClient().List(ctx, &nodeList); err != nil {
				return nil, err
			}
			for _, node := range nodeList.Items {
				for _, address := range node.Status.Addresses {
					switch address.Type {
					case v1.NodeInternalIP:
						if strings.EqualFold(address.Address, nodeIp) {
							nodeName = &node.Name
						}

					}
				}
				if nodeName != nil {
					break
				}
			}
			return &node.NodeUpDownStatusResponse{
				ClusterName: clusterName,
				Ip:          nodeIp,
				Status:      node.NodeUpDownStatusSuccess,
				NodeName:    nodeName,
			}, nil

		default:
			return nil, err
		}
	}

	var res node.NodeUpDownStatusResponse
	if err := handler.r.InstallerToStatusResponse(installer, &res); err != nil {
		return nil, err
	}
	return &res, nil
}

func (handler nodeUpDownHandler) CreateResponse(ctx context.Context, clusterName, nodeIp string) (*node.NodeUpDownCreateResponse, error) {
	installer, err := handler.getInstallerOrMockTemplate(ctx, clusterName, nodeIp)
	if err != nil {
		return nil, err
	}
	var createResponse node.NodeUpDownCreateResponse
	if err := handler.r.InstallerRenderToCreateResponse(installer, &createResponse); err != nil {
		return nil, err
	}
	return &createResponse, nil
}

func (handler nodeUpDownHandler) getInstallerOrMockTemplate(ctx context.Context, clusterName, nodeIp string) (installerv1alpha1.Installer, error) {
	// 先查询非批量installer
	installer, err := handler.getInstaller(ctx, clusterName, nodeIp)
	if err == nil {
		return installer, nil
	}
	if !errors.Is(err, errors.Var.InstallerNotExist) {
		return installerv1alpha1.Installer{}, err
	}

	// 非批量installer无法查询到=》查询批量的installer
	_, err = hcclient.GetCluster(clusterName)
	if err != nil {
		return installerv1alpha1.Installer{}, err
	}

	// 查询installer template
	matchLabels := client.MatchingLabels{}
	// 标记类型 节点上下线
	matchLabels[operatorconstant.InstallTypeLabelKey] = operatorconstant.NodeUpDownInstallerTypeLabelValue
	// 标记集群名称
	matchLabels[operatorconstant.NodeUpDownClusterNameLabelKey] = clusterName
	// 查找批量类型的
	matchLabels[operatorconstant.NodeUpDownTypeLabelKey] = string(node.NodeUpDownTypeBatch)
	var installerList installerv1alpha1.InstallerList
	if err := hcclient.GetLocalCluster().GetClient().GetCtrlClient().List(ctx, &installerList, matchLabels); err != nil {
		return installerv1alpha1.Installer{}, err
	}
	// 查找template
	for _, i := range installerList.Items {
		for _, task := range i.Spec.Tasks {
			if task.CreateInstallerByTemplate == nil || task.CreateInstallerByTemplate.Template.ObjectMeta.Labels == nil {
				continue
			}
			labelIP, exist := task.CreateInstallerByTemplate.Template.ObjectMeta.Labels[operatorconstant.NodeUpDownIPLabelKey]
			if !exist || labelIP != iputil.FormatLabelValueForIP(nodeIp) {
				continue
			}
			// 转换
			return render.TaskAsMockInstaller(task)
		}
	}
	return installerv1alpha1.Installer{}, errors.NewFromCodeWithMessage(errors.Var.InstallerNotExist, nodeIp)

}

// 获取installer
func (nodeUpDownHandler) getInstaller(ctx context.Context, clusterName, nodeIp string) (installerv1alpha1.Installer, error) {
	// 通过批量方式创建的Installer 在 Installer Template 未转化到 Installer 前，无法调用该接口
	// 因此这里直接查询Installer即可
	// 查找集群
	_, err := hcclient.GetCluster(clusterName)
	if err != nil {
		return installerv1alpha1.Installer{}, err
	}

	// 构建installer查询条件
	var matchLabels = make(client.MatchingLabels)
	// 记录节点上下线的IP
	matchLabels[operatorconstant.NodeUpDownIPLabelKey] = iputil.FormatLabelValueForIP(nodeIp)
	// 标记类型 节点上下线
	matchLabels[operatorconstant.InstallTypeLabelKey] = operatorconstant.NodeUpDownInstallerTypeLabelValue
	// 标记集群名称
	matchLabels[operatorconstant.NodeUpDownClusterNameLabelKey] = clusterName
	// 查询installer
	var installerList installerv1alpha1.InstallerList
	if err := hcclient.GetLocalCluster().GetClient().GetCtrlClient().List(ctx, &installerList, matchLabels); err != nil {
		return installerv1alpha1.Installer{}, err
	}
	switch len(installerList.Items) {
	case 0:
		// 不存在
		return installerv1alpha1.Installer{}, errors.NewFromCodeWithMessage(errors.Var.InstallerNotExist, nodeIp)
	case 1:
		// 合法
		return installerList.Items[0], nil
	default:
		// 存在脏数据
		return installerv1alpha1.Installer{}, errors.NewFromCodeWithMessage(errors.Var.InstallerRepeated, nodeIp)
	}

}

func (handler nodeUpDownHandler) Edit(ctx context.Context, clusterName, nodeIp string, request node.NodeUpDownCreateRequest) error {
	_, err := hcclient.GetCluster(clusterName)
	if err != nil {
		return err
	}

	// 表单合法性校验
	if err := handler.createRequestFromVerify(ctx, clusterName, request); err != nil {
		return err
	}
	request.ClusterName = clusterName

	// 获取集群基线版本
	baselineVersion, err := handler.getNodeUpDownBaselineVersion(ctx, clusterName)
	if err != nil {
		return err
	}
	request.ClusterBaselineVersion = baselineVersion
	request.CRI = installerutil.GetCRITypeByBaseLineVersion(baselineVersion)

	// 获取表单对应的Installer
	installer, err := handler.getInstaller(ctx, clusterName, nodeIp)
	if err != nil {
		return err
	}
	var response node.NodeUpDownResponse
	if err := handler.r.InstallerToResponse(installer, &response); err != nil {
		return err
	}

	// 更新时候必须是失败的状态
	if !node.NodeUpDownStatusFailedList.Has(response.Status) {
		return errors.NewFromCodeWithMessage(errors.Var.NodeUpDownOnlyUpdateInFailedStatus, fmt.Sprintf("current status is %s", response.Status))
	}
	installerDeepCopy := installer.DeepCopy()
	if err := handler.r.CreateRequestRenderToInstaller(request, &installer); err != nil {
		return err
	}
	installer.Spec.ReApply = true
	if err := hcclient.GetLocalCluster().GetClient().GetCtrlClient().Patch(ctx, &installer, client.MergeFrom(installerDeepCopy)); err != nil {
		return err
	}
	return nil
}

func (handler nodeUpDownHandler) Retry(ctx context.Context, clusterName, nodeIp string) error {
	createResponse, err := handler.CreateResponse(ctx, clusterName, nodeIp)
	if err != nil {
		return err
	}
	request := createResponse.Convert2CreateRequest(clusterName)
	request.StartFromFailed = true
	request.Reset = false
	return handler.Edit(ctx, clusterName, nodeIp, request)
}

func (handler nodeUpDownHandler) Delete(ctx context.Context, clusterName, nodeIp string) error {
	installer, err := handler.getInstaller(ctx, clusterName, nodeIp)
	if err != nil {
		return err
	}
	var response node.NodeUpDownResponse
	if err := handler.r.InstallerToResponse(installer, &response); err != nil {
		return err
	}
	// 删除时候必须是失败的状态
	if !node.NodeUpDownStatusFailedList.Has(response.Status) {
		return errors.NewFromCodeWithMessage(errors.Var.NodeUpDownOnlyDeleteInFailedStatus, fmt.Sprintf("current status is %s", response.Status))
	}
	// delete
	if err := hcclient.GetLocalCluster().GetClient().GetCtrlClient().Delete(ctx, &installer); err != nil {
		return err
	}
	return nil
}

// Verify
// 节点上下线节点校验
func (handler nodeUpDownHandler) Verify(ctx context.Context, clusterName string, request node.NodeUpDownCreateRequest) (*node.NodeVerifyResponse, error) {
	// 集群校验
	_, err := hcclient.GetCluster(clusterName)
	if err != nil {
		return nil, err
	}
	request.ClusterName = clusterName
	// 集群基线版本校验
	_, err = handler.getNodeUpDownBaselineVersion(ctx, clusterName)
	if err != nil {
		return nil, err
	}

	// 主控节点与非主控节点的IP不允许重复
	if request.ControlNode.Ip == request.NodeConfig.Ip {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ControlNodeCouldNotEqualsNodeUpDownNode, request.NodeConfig.Ip)
	}
	var reqeusts = make([]node.NodeVeriryRequest, 0, 2)
	// 处理主控节点
	controlNode := installerv1alpha1.SisyphusNodeInitialParam{
		IP:   request.ControlNode.Ip,
		Port: strconv.Itoa(request.ControlNode.Port),
	}
	if err := sisyphusutil.NodeAuthRequestToNodeInitialParam(request.ControlNode.Auth, &controlNode); err != nil {
		return nil, err
	}

	// 处理待上线 ｜ 下线节点
	upDownNode := installerv1alpha1.SisyphusNodeInitialParam{
		IP:   request.NodeConfig.Ip,
		Port: strconv.Itoa(request.NodeConfig.Port),
	}
	if err := sisyphusutil.NodeAuthRequestToNodeInitialParam(request.NodeConfig.Auth, &upDownNode); err != nil {
		return nil, err
	}
	// 转化为model
	controlPort, err := strconv.Atoi(controlNode.Port)
	if err != nil {
		return nil, err
	}
	reqeusts = append(reqeusts, node.NodeVeriryRequest{
		Host:         controlNode.IP,
		Post:         controlPort,
		Username:     controlNode.Username,
		Password:     controlNode.Password,
		SudoPassword: controlNode.SudoPassword,
	})

	// 转化为model
	upDownPort, err := strconv.Atoi(upDownNode.Port)
	if err != nil {
		return nil, err
	}
	reqeusts = append(reqeusts, node.NodeVeriryRequest{
		Host:         upDownNode.IP,
		Post:         upDownPort,
		Username:     upDownNode.Username,
		Password:     upDownNode.Password,
		SudoPassword: upDownNode.SudoPassword,
	})
	response, err := handler.nodeVerifyHandler.Verify(ctx, clusterName, reqeusts)
	if err != nil {
		return nil, err
	}
	return response, nil
}

// UpDownLog
// 读取节点上下线日志过程中的日志
func (handler nodeUpDownHandler) UpDownLog(ctx context.Context, request node.UpDownLogQueryRequest) (<-chan []byte, context.Context, context.CancelFunc, error) {
	sisyphusConfig, err := handler.sisyphusConfigHandler.GetSisyphusURL(ctx, request.ClusterName)
	if err != nil {
		return nil, nil, nil, err
	}

	installer, err := handler.getInstaller(ctx, request.ClusterName, request.IP)
	if err != nil {
		return nil, nil, nil, err
	}
	return handler.installerLogHandler.SisyphusLogForInstaller(ctx, client.ObjectKey{Namespace: installer.Namespace, Name: installer.Name}, sisyphusConfig)
}

// 获取集群基线版本
func (handler nodeUpDownHandler) getNodeUpDownBaselineVersion(ctx context.Context, clusterName string) (string, error) {
	_, err := hcclient.GetCluster(clusterName)
	if err != nil {
		return "", err
	}
	// 查找STC
	var stcCluster stellarisv1alhpha1.Cluster
	if err := hcclient.GetLocalCluster().GetClient().GetCtrlClient().Get(ctx, client.ObjectKey{Name: clusterName}, &stcCluster); err != nil {
		return "", err
	}
	value := utils.MapGetValue(stcCluster.Labels, "caas-infra-baseline", "")
	if value == "" {
		return "", errors.NewFromCode(errors.Var.NodeUpDownClusterBaselineVersionLost)
	}
	return value, nil
}
