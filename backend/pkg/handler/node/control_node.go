package node

import (
	"context"

	hcclient "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
	nodeconfig "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/node/config"
	v1 "k8s.io/api/core/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

func NewControlNodeHandler() ControlNodeHandler {
	return controlNodeHandler{}
}

type controlNodeHandler struct {
}

func (controlNodeHandler) Nodes(ctx context.Context, clusterName string, filters ...ListControlNodeFilter) (node.SimpleNodeResponseList, error) {
	cluster, err := hcclient.GetCluster(clusterName)
	if err != nil {
		return nil, err
	}
	var listOptions []client.ListOption
	if len(nodeconfig.NodeUPDownConfig.MasterNodeLabelKeys) != 0 {
		listOptions = append(listOptions, client.HasLabels(nodeconfig.NodeUPDownConfig.MasterNodeLabelKeys))
	}
	if len(nodeconfig.NodeUPDownConfig.MasterNodeLabelMaps) != 0 {
		listOptions = append(listOptions, client.MatchingLabels(nodeconfig.NodeUPDownConfig.MasterNodeLabelMaps))
	}

	// 获取nodeList
	var nodeList v1.NodeList
	if err := cluster.GetClient().GetCtrlClient().List(ctx, &nodeList, listOptions...); err != nil {
		return nil, err
	}
	var result = make(node.SimpleNodeResponseList, 0, len(nodeList.Items))
	for _, k8sNode := range nodeList.Items {
		var hostname, hostIP string
		for _, address := range k8sNode.Status.Addresses {
			switch address.Type {
			case v1.NodeHostName:
				hostname = address.Address
			case v1.NodeInternalIP:
				hostIP = address.Address
			}
		}

		result = append(result, node.SimpleNodeResponse{
			HostName: hostname,
			HostIP:   hostIP,
			Status:   k8sNode.Status.Phase,
		})
	}
	for _, f := range filters {
		result = f.Filter(result)
	}
	return result, nil

}

func (h controlNodeHandler) Verify(ctx context.Context, clusterName string, nodeIP string) error {
	nodeList, err := h.Nodes(ctx, clusterName)
	if err != nil {
		return err
	}
	var exist bool
	for _, node := range nodeList {
		if node.HostIP == nodeIP {
			exist = true
			break
		}
	}
	if !exist {
		return errors.NewFromCodeWithMessage(errors.Var.ControlNodeNotExist, nodeIP)
	}
	return nil
}
