package node

import (
	"context"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
)

type NodeUpDownHandler interface {
	// Count 获取节点上显现任务数量
	Count(ctx context.Context, clusterName string, filters ...NodeUpDownResponseFilter) (*node.NodeUpDownCountResponse, error)
	// Create 创建节点上下线任务
	Create(ctx context.Context, clusterName string, request node.NodeUpDownCreateRequest) error
	// BatchCreate 批量更新
	BatchCreate(ctx context.Context, clusterName string, batchRequest node.NodeUpDownBatchCreateRequest) error
	// List 获取节点上下线任务列表
	List(ctx context.Context, clusterName string, filters ...NodeUpDownResponseFilter) (node.NodeUpDownResponseList, error)

	// Status 获取任务状态执行详情
	Status(ctx context.Context, clusterName, nodeIp string) (*node.NodeUpDownStatusResponse, error)
	// CreateResponse 节点上下线表单回显
	CreateResponse(ctx context.Context, clusterName, nodeIp string) (*node.NodeUpDownCreateResponse, error)
	// Edit 编辑节点上下线信息
	Edit(ctx context.Context, clusterName, nodeIp string, request node.NodeUpDownCreateRequest) error
	// Retry 重试 相当于从失败处开始
	Retry(ctx context.Context, clusterName, nodeIp string) error
	// Delete 删除节点上下线任务
	Delete(ctx context.Context, clusterName, nodeIp string) error
	// Verify
	// 节点上下线节点校验
	Verify(ctx context.Context, clusterName string, request node.NodeUpDownCreateRequest) (*node.NodeVerifyResponse, error)
	// UpDownLog
	// 读取节点上下线日志过程中的日志
	UpDownLog(ctx context.Context, request node.UpDownLogQueryRequest) (<-chan []byte, context.Context, context.CancelFunc, error)
}

type ControlNodeHandler interface {
	Nodes(ctx context.Context, clusterName string, filters ...ListControlNodeFilter) (node.SimpleNodeResponseList, error)
	Verify(ctx context.Context, clusterName string, nodeIP string) error
}

type NodeVerifyHandler interface {
	Verify(ctx context.Context, clusterName string, requests []node.NodeVeriryRequest) (*node.NodeVerifyResponse, error)
}
