package node

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	cloudclient "harmonycloud.cn/unifiedportal/cloudservice-operator/pkg/handler/installer/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/addon"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
)

func NewNodeVerifyHandler() NodeVerifyHandler {
	return nodeVerifyHandler{
		sisyphusClient:        cloudclient.NewSisyphus(),
		sisyphusConfigHandler: addon.NewSisyphusConfigHandler(),
	}
}

type nodeVerifyHandler struct {
	sisyphusClient cloudclient.Sisyphus
	// 读取下层集群的西西弗斯地址
	sisyphusConfigHandler addon.SisyphusConfigHandler
}

func (handler nodeVerifyHandler) Verify(ctx context.Context, clusterName string, requests []node.NodeVeriryRequest) (*node.NodeVerifyResponse, error) {
	ipMap := make(map[string]node.NodeVeriryRequest)
	// 重复值校验
	for _, req := range requests {
		req := req
		_, exist := ipMap[req.Host]
		if !exist {
			ipMap[req.Host] = req
		} else {
			return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, "node ip is repeated")
		}
	}
	// 节点联通性检查
	sisyphusConfig, err := handler.sisyphusConfigHandler.GetSisyphusURL(ctx, clusterName)
	if err != nil {
		return nil, err
	}

	sisyphusRequestOptions := []cloudclient.OptionFunc{cloudclient.WithURL(sisyphusConfig.Address), cloudclient.WithAuth(sisyphusConfig.Address, sisyphusConfig.Username, sisyphusConfig.Password)}

	var params = make(v1alpha1.SisyphusNodeInitialParams, 0, len(requests))
	for _, req := range requests {
		params = append(params, v1alpha1.SisyphusNodeInitialParam{
			IP:           req.Host,
			Port:         strconv.Itoa(req.Post),
			Username:     req.Username,
			Password:     req.Password,
			SudoPassword: req.SudoPassword,
		})
	}

	resp, err := handler.sisyphusClient.ValidateNodes(ctx, params, sisyphusRequestOptions...)
	if err != nil {
		return nil, err
	}
	var messages []string
	for _, item := range resp.Data {
		if !item.Validate {
			msg := fmt.Sprintf("[%s]:'%s'", item.HostIP, strPtrAsValue(item.Msg))
			messages = append(messages, msg)
		}
	}
	if len(messages) == 0 {
		return &node.NodeVerifyResponse{Success: true}, nil
	}

	return &node.NodeVerifyResponse{Success: false, Message: strings.Join(messages, "\n")}, nil

}

func strPtrAsValue(strPtr *string) string {
	if strPtr == nil {
		return ""
	}
	return *strPtr
}
