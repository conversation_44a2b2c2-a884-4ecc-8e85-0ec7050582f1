package backuprestore

import (
	"context"
	"strconv"
	"time"

	"github.com/robfig/cron/v3"
	veleroV1 "github.com/vmware-tanzu/velero/pkg/apis/velero/v1"
	"harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/storageserver"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/velero"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	k8sError "k8s.io/apimachinery/pkg/api/errors"
	k8sV1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	runtimeclient "sigs.k8s.io/controller-runtime/pkg/client"
)

type schedulerHandler struct {
	veleroStorageServersInterface storageserver.VeleroStorageServersInterface
	backupInterface               BackupInterface
}

func (h *schedulerHandler) ListSchedulers(ctx context.Context, clusterName string) ([]velero.Schedules, error) {
	clusterList := make([]client.Cluster, 0)

	if clusterName == "" {
		clusterList = client.ListOnlineClusters()
	} else {
		clusterClient, clientError := client.GetCluster(clusterName)
		if clientError != nil {
			return nil, clientError
		}
		clusterList = append(clusterList, clusterClient)
	}

	ans := make([]velero.Schedules, 0)

	for _, clusterClient := range clusterList {
		schedulesList := veleroV1.ScheduleList{}

		_ = clusterClient.GetClient().GetCtrlClient().List(ctx, &schedulesList, runtimeclient.InNamespace(constants.VeleroNamespace))

		for _, scheduleItem := range schedulesList.Items {
			ans = append(ans, *utils.Convert2Schedules(clusterClient.GetName(), scheduleItem))
		}
	}

	return ans, nil
}

func (h *schedulerHandler) GetSchedulers(ctx context.Context, clusterName, schedulersName string) (*velero.Schedules, error) {
	clusterClient, clientError := client.GetCluster(clusterName)
	if clientError != nil {
		return nil, clientError
	}

	schedulesV1 := veleroV1.Schedule{}

	if err := clusterClient.GetClient().GetCtrlClient().Get(ctx, runtimeclient.ObjectKey{Name: schedulersName, Namespace: constants.VeleroNamespace}, &schedulesV1); err != nil {
		return nil, err
	}

	return utils.Convert2Schedules(clusterName, schedulesV1), nil
}

func (h *schedulerHandler) CreateSchedulers(ctx context.Context, clusterName string, schedules velero.Schedules) error {
	ss, getStorageServersError := h.veleroStorageServersInterface.GetStorageServers(ctx, schedules.StorageServers.StorageServersId)
	if getStorageServersError != nil {
		return getStorageServersError
	}

	if bsl, _ := h.veleroStorageServersInterface.GetBackupStorageLocation(ctx, clusterName, ss.GetBackupStorageLocationName()); bsl == nil {
		return errors.NewFromCode(errors.Var.SolutionStepNotExist)
	}

	schedules.StorageServers = *ss

	clusterClient, clientError := client.GetCluster(clusterName)
	if clientError != nil {
		return clientError
	}

	schedulesV1, createVeleroSchedulesError := CreateVeleroSchedules(schedules)
	if createVeleroSchedulesError != nil {
		return createVeleroSchedulesError
	}

	if err := clusterClient.GetClient().GetCtrlClient().Create(ctx, schedulesV1); err != nil {
		return err
	}

	if schedules.Execute {
		go func() {
			checkFuncCtx, cancel := context.WithCancel(ctx)
			defer cancel()

			checkFunc := func() {
				if err := clusterClient.GetClient().GetCtrlClient().Get(checkFuncCtx, runtimeclient.ObjectKey{Name: schedules.SchedulesName, Namespace: constants.VeleroNamespace}, &veleroV1.Schedule{}); err != nil {
					// 不是not found的报错则代表有问题，终止
					if !k8sError.IsNotFound(err) {
						cancel()
					}
					// 是not found则继续等
				} else {
					// 没报错代表找到了，则创建
					if err := h.backupInterface.CreateBackupsBySchedules(ctx, clusterName, schedules.SchedulesName); err != nil {
						return
					}
					cancel()
				}
			}

			go wait.Until(checkFunc, 25*time.Millisecond, checkFuncCtx.Done())

			// 最多等一秒
			time.Sleep(time.Second)
		}()
	}

	return nil
}

func (h *schedulerHandler) UpdateSchedulers(ctx context.Context, clusterName string, schedules velero.Schedules) error {
	ss, getStorageServersError := h.veleroStorageServersInterface.GetStorageServers(ctx, schedules.StorageServers.StorageServersId)
	if getStorageServersError != nil {
		return getStorageServersError
	}

	if bsl, _ := h.veleroStorageServersInterface.GetBackupStorageLocation(ctx, clusterName, ss.GetBackupStorageLocationName()); bsl == nil {
		return errors.NewFromCode(errors.Var.SolutionStepNotExist)
	}

	schedules.StorageServers = *ss

	clusterClient, clientError := client.GetCluster(clusterName)
	if clientError != nil {
		return clientError
	}

	oldSchedules := &veleroV1.Schedule{}

	if err := clusterClient.GetClient().GetCtrlClient().Get(ctx, runtimeclient.ObjectKey{Name: schedules.SchedulesName, Namespace: constants.VeleroNamespace}, oldSchedules); err != nil {
		return err
	}

	schedulesV1, updateVeleroSchedulesError := UpdateVeleroSchedules(schedules, oldSchedules)
	if updateVeleroSchedulesError != nil {
		return updateVeleroSchedulesError
	}

	if err := clusterClient.GetClient().GetCtrlClient().Update(ctx, schedulesV1); err != nil {
		return err
	}

	return nil
}

func (h *schedulerHandler) DeleteSchedulers(ctx context.Context, clusterName, schedulesName string) error {
	clusterClient, clientError := client.GetCluster(clusterName)
	if clientError != nil {
		return clientError
	}

	oldSchedules := &veleroV1.Schedule{}

	if err := clusterClient.GetClient().GetCtrlClient().Get(ctx, runtimeclient.ObjectKey{Name: schedulesName, Namespace: constants.VeleroNamespace}, oldSchedules); err != nil {
		return err
	}

	if err := clusterClient.GetClient().GetCtrlClient().Delete(ctx, oldSchedules); err != nil {
		return err
	}

	return nil
}

func CreateVeleroSchedules(schedules velero.Schedules) (*veleroV1.Schedule, error) {
	useOwnerReferencesInBackup := false

	includeNamespaces := schedules.Namespaces

	if schedules.AllNamespaces {
		includeNamespaces = []string{constants.Start}
	}

	duration, _ := time.ParseDuration(strconv.Itoa(schedules.Ttl*24) + constants.H)

	if schedules.Type == velero.Handle {
		schedules.Cron.CycleType = "custom"
		schedules.Cron.CronString = "0 0 31 2 *"
	} else {
		utils.SolveCronString(&schedules.Cron)

		_, parseStandardError := cron.ParseStandard(schedules.Cron.CronString)
		if parseStandardError != nil {
			return nil, parseStandardError
		}
	}

	var labelSelector *k8sV1.LabelSelector
	if len(schedules.IncludeResources) != 0 || len(schedules.ExcludeResources) != 0 {
		labelSelector = &k8sV1.LabelSelector{
			MatchLabels:      schedules.IncludeResources,
			MatchExpressions: utils.MapConvert2LabelSelectorRequirement(schedules.ExcludeResources),
		}
	} else {
		labelSelector = nil
	}

	paused := false
	if schedules.Type == velero.Handle {
		paused = true
	}

	return &veleroV1.Schedule{
		ObjectMeta: k8sV1.ObjectMeta{
			Name:      schedules.SchedulesName,
			Namespace: constants.VeleroNamespace,
			Annotations: map[string]string{
				constants.Description:       schedules.Description,
				constants.Type:              string(schedules.Type),
				constants.SchedulesCronType: schedules.Cron.CycleType,
			},
			Labels: map[string]string{
				constants.StorageServerId: schedules.StorageServers.StorageServersId,
			},
		},
		Spec: veleroV1.ScheduleSpec{
			Template: veleroV1.BackupSpec{
				IncludedNamespaces:       includeNamespaces,
				LabelSelector:            labelSelector,
				TTL:                      k8sV1.Duration{Duration: duration},
				StorageLocation:          schedules.StorageServers.GetBackupStorageLocationName(),
				DefaultVolumesToFsBackup: &schedules.DefaultVolumesToFsBackup,
			},
			Schedule:                   schedules.Cron.CronString,
			UseOwnerReferencesInBackup: &useOwnerReferencesInBackup,
			Paused:                     paused,
		},
	}, nil
}

func UpdateVeleroSchedules(schedule velero.Schedules, v1Schedules *veleroV1.Schedule) (*veleroV1.Schedule, error) {
	useOwnerReferencesInBackup := true

	includeNamespaces := schedule.Namespaces

	if schedule.AllNamespaces {
		includeNamespaces = []string{constants.Start}
	}

	duration, _ := time.ParseDuration(strconv.Itoa(schedule.Ttl*24) + constants.H)

	v1Schedules.Annotations[constants.Description] = schedule.Description
	v1Schedules.Annotations[constants.Type] = string(schedule.Type)
	v1Schedules.Annotations[constants.SchedulesCronType] = schedule.Cron.CycleType
	v1Schedules.Labels[constants.StorageServerId] = schedule.StorageServers.StorageServersId

	v1Schedules.Spec.Template.IncludedNamespaces = includeNamespaces

	if len(schedule.IncludeResources) != 0 || len(schedule.ExcludeResources) != 0 {
		v1Schedules.Spec.Template.LabelSelector = &k8sV1.LabelSelector{
			MatchLabels:      schedule.IncludeResources,
			MatchExpressions: utils.MapConvert2LabelSelectorRequirement(schedule.ExcludeResources),
		}
	} else {
		v1Schedules.Spec.Template.LabelSelector = nil
	}

	v1Schedules.Spec.Template.TTL = k8sV1.Duration{Duration: duration}
	v1Schedules.Spec.Template.StorageLocation = schedule.StorageServers.GetBackupStorageLocationName()
	v1Schedules.Spec.Template.DefaultVolumesToFsBackup = &schedule.DefaultVolumesToFsBackup

	if schedule.Type == velero.Handle {
		schedule.Cron.CycleType = "custom"
		schedule.Cron.CronString = "0 0 31 2 *"
	} else {
		utils.SolveCronString(&schedule.Cron)

		_, parseStandardError := cron.ParseStandard(schedule.Cron.CronString)
		if parseStandardError != nil {
			return nil, parseStandardError
		}
	}

	v1Schedules.Spec.Schedule = schedule.Cron.CronString
	v1Schedules.Spec.UseOwnerReferencesInBackup = &useOwnerReferencesInBackup

	paused := false
	if schedule.Type == velero.Handle {
		paused = true
	} else {
		paused = schedule.Execute
	}
	v1Schedules.Spec.Paused = paused

	return v1Schedules, nil
}
