package backuprestore

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"
	rds "github.com/redis/go-redis/v9"
	"github.com/samber/lo"
	veleroV1 "github.com/vmware-tanzu/velero/pkg/apis/velero/v1"
	"gopkg.in/yaml.v2"
	"harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/velero"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/downloadquest"
	appV1 "k8s.io/api/apps/v1"
	coreV1 "k8s.io/api/core/v1"
	k8sResource "k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/util/sets"
	"k8s.io/klog/v2"
	runtimeclient "sigs.k8s.io/controller-runtime/pkg/client"
)

type restoreHandler struct {
	podVolumeRestoreInterface PodVolumeRestoreInterface
	solveRestoreSizeLock      sync.Mutex
	redis                     *rds.Client
}

func (h *restoreHandler) ListRestore(ctx context.Context, clusterName string, matchLabels map[string]string) ([]velero.Restore, error) {
	clusterList := make([]client.Cluster, 0)

	if clusterName == "" {
		clusterList = client.ListOnlineClusters()
	} else {
		cluster, err := client.GetCluster(clusterName)
		if err != nil {
			return nil, err
		}
		clusterList = append(clusterList, cluster)
	}

	ans := make([]velero.Restore, 0)

	needSolveRestoreSize := false

	for _, clusterClient := range clusterList {
		restoreList := veleroV1.RestoreList{}

		_ = clusterClient.GetClient().GetCtrlClient().List(ctx, &restoreList, runtimeclient.InNamespace(constants.VeleroNamespace), runtimeclient.MatchingLabels(matchLabels))

		for _, restoreItem := range restoreList.Items {
			ans = append(ans, utils.Convert2Restore(clusterClient.GetName(), restoreItem))
			_, ok := restoreItem.Annotations[constants.ResourcesSize]
			if !ok {
				if restoreItem.Status.Phase == veleroV1.RestorePhaseCompleted ||
					restoreItem.Status.Phase == veleroV1.RestorePhasePartiallyFailed {
					needSolveRestoreSize = true
				}
			}
		}
	}

	if needSolveRestoreSize {
		go func() {
			h.SolveRestoreSize()
		}()
	}
	return ans, nil
}

func (h *restoreHandler) ListRestoresResources(ctx context.Context, clusterName, restoreName string) ([]velero.VeleroResources, error) {
	clusterClient, clientError := client.GetCluster(clusterName)
	if clientError != nil {
		return nil, clientError
	}

	return downloadquest.ListResources(ctx, clusterClient.GetClient().GetCtrlClient(), constants.VeleroNamespace, restoreName, veleroV1.DownloadTargetKindRestoreResourceList)
}

func (h *restoreHandler) CreateRestore(ctx context.Context, template velero.RestoreTemplate) error {
	clusterClient, clientError := client.GetCluster(template.TargetCluster)
	if clientError != nil {
		return clientError
	}

	name := template.RestoreTemplateName + constants.Dash + uuid.New().String()

	if template.IsModify {
		modifyRule := utils.ConvertModifyConfig(template.Modifys)
		str, _ := yaml.Marshal(modifyRule)
		config := &coreV1.ConfigMap{
			ObjectMeta: metav1.ObjectMeta{
				Name:      fmt.Sprintf("%s-modify", name),
				Namespace: constants.VeleroNamespace,
			},
			Data: map[string]string{
				constants.RestoreModifyConfigKey: string(str),
			},
		}
		if err := clusterClient.GetClient().GetCtrlClient().Create(ctx, config); err != nil {
			return err
		}
	}
	if err := clusterClient.GetClient().GetCtrlClient().Create(ctx, BuildVeleroRestore(name, template)); err != nil {
		return err
	}
	return nil
}

func (h *restoreHandler) DeleteRestore(ctx context.Context, clusterName, restoresTemplateId string) error {
	clusterClient, clientError := client.GetCluster(clusterName)
	if clientError != nil {
		return clientError
	}

	if err := clusterClient.GetClient().GetCtrlClient().DeleteAllOf(ctx, &veleroV1.Restore{}, runtimeclient.InNamespace(constants.VeleroNamespace), runtimeclient.MatchingLabels(map[string]string{constants.RestoreTemplateId: restoresTemplateId})); err != nil {
		return err
	}

	return nil
}

func (h *restoreHandler) LogsRestore(ctx context.Context, clusterName, restoreName string) (string, error) {
	clusterClient, clientError := client.GetCluster(clusterName)
	if clientError != nil {
		return constants.Blank, clientError
	}

	return downloadquest.Logs(ctx, clusterClient.GetClient().GetCtrlClient(), constants.VeleroNamespace, restoreName, veleroV1.DownloadTargetKindRestoreLog)
}

func (h *restoreHandler) SolveRestoreSize() {
	// 全局唯一锁
	// PS: TryLock为尝试并锁住，并返回尝试结果
	tryLock := h.solveRestoreSizeLock.TryLock()
	defer h.solveRestoreSizeLock.Unlock()

	// 锁成功代表是第一次，非第一次进来的锁全部跳过
	if tryLock {
		clusterList := client.ListOnlineClusters()

		for _, cluster := range clusterList {
			go func(cluster client.Cluster) {

				backgroundCtx, _ := context.WithTimeout(context.Background(), time.Minute)

				restoreList := veleroV1.RestoreList{}

				logger.GetLogger().Info(fmt.Sprintf("Cluster:%s solve restore size start", cluster.GetName()))

				if err := cluster.GetClient().GetCtrlClient().List(backgroundCtx, &restoreList, runtimeclient.InNamespace(constants.VeleroNamespace)); err != nil {
					return
				}

				for _, restore := range restoreList.Items {
					if restore.Status.Phase == veleroV1.RestorePhaseCompleted || restore.Status.Phase == veleroV1.RestorePhasePartiallyFailed {
						_, ok := restore.Annotations[constants.ResourcesSize]
						if !ok {
							logger.GetLogger().Info(fmt.Sprintf("Cluster:%s restore:%s resource refresh", cluster.GetName(), restore.Name))

							resourceList, _ := h.ListRestoresResources(backgroundCtx, cluster.GetName(), restore.Name)
							restore.Annotations[constants.ResourcesSize] = strconv.Itoa(len(resourceList))
							logger.GetLogger().Info(fmt.Sprintf("Cluster:%s restore:%s resouce size:%d", cluster.GetName(), restore.Name, len(resourceList)))

							volumesList, _ := h.podVolumeRestoreInterface.ListRestoreVolumes(backgroundCtx, cluster.GetName(), restore.Labels[constants.RestoreTemplateId], restore.Name)
							restore.Annotations[constants.PersistentVolumeSize] = strconv.Itoa(len(volumesList))
							logger.GetLogger().Info(fmt.Sprintf("Clusrer:%s restore:%s volumes size:%d", cluster.GetName(), restore.Name, len(volumesList)))

							if err := cluster.GetClient().GetCtrlClient().Update(backgroundCtx, &restore); err != nil {
								logger.GetLogger().Error(fmt.Sprintf("solve restore:%v size error, error:%+v", restore.Name, err))
							}
						}
					}
				}
			}(cluster)
		}
	}
}

func BuildVeleroRestore(name string, restoreTemplate velero.RestoreTemplate) *veleroV1.Restore {
	namespaceByte, _ := json.Marshal(restoreTemplate.Namespaces)

	var restoreType string
	if restoreTemplate.RestoreType == 0 {
		restoreType = constants.Original
	} else {
		restoreType = constants.Cross
	}

	restore := &veleroV1.Restore{
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: constants.VeleroNamespace,
			Annotations: map[string]string{
				constants.Description:       restoreTemplate.Description,
				constants.RestoreNamespaces: string(namespaceByte),
				constants.RestoreType:       restoreType,
				constants.FromCluster:       restoreTemplate.FromCluster,
			},
			Labels: map[string]string{
				constants.RestoreTemplateId:   restoreTemplate.RestoreTemplateId,
				constants.RestoreTemplateName: restoreTemplate.RestoreTemplateName,
			},
		},
		Spec: veleroV1.RestoreSpec{
			BackupName:         restoreTemplate.BackupsName,
			IncludedNamespaces: restoreTemplate.Namespaces,
			LabelSelector: &metav1.LabelSelector{
				MatchLabels:      restoreTemplate.IncludeResources,
				MatchExpressions: utils.MapConvert2LabelSelectorRequirement(restoreTemplate.ExcludeResources),
			},
		},
	}
	if restoreTemplate.IsModify {
		restore.Spec.ResourceModifier = &coreV1.TypedLocalObjectReference{
			APIGroup: lo.ToPtr(""),
			Kind:     "configmap",
			Name:     fmt.Sprintf("%s-modify", name),
		}
	}
	return restore
}

func (h *restoreHandler) PreflightRestoreTemplate(ctx context.Context, template *velero.RestoreTemplate) {

	expiredTime, _ := time.ParseDuration(config.PreflightExpiredTime.Value)
	//开始预检
	h.redis.Set(ctx, "PreflightRequestResult-"+template.RestoreTemplateId, "preflighting", expiredTime)
	h.redis.Set(ctx, "PreflightScResult-"+template.RestoreTemplateId, "preflighting", expiredTime)
	h.redis.Set(ctx, "PreflightNetworkResult-"+template.RestoreTemplateId, "preflighting", expiredTime)
	go func() {
		h.Preflight(ctx, template, expiredTime)
	}()
}

func (h *restoreHandler) Preflight(ctx context.Context, template *velero.RestoreTemplate, expiredTime time.Duration) {
	cluster, err := client.GetCluster(template.TargetCluster)
	if err != nil {
		h.preflightfailed(ctx, template.RestoreTemplateId, err.Error(), expiredTime)
		return
	}

	resp, streamError := downloadquest.Stream(ctx, cluster.GetClient().GetCtrlClient(), constants.VeleroNamespace, template.BackupsName, veleroV1.DownloadTargetKindBackupContents, time.Second, true, "")
	if streamError != nil {
		h.preflightfailed(ctx, template.RestoreTemplateId, streamError.Error(), expiredTime)
		return
	}

	//将文件存在本地
	fileName, saveErr := downloadquest.SaveFileInLocal(resp.Body, template.BackupsName)
	if saveErr != nil {
		h.preflightfailed(ctx, template.RestoreTemplateId, saveErr.Error(), expiredTime)
		return
	}

	filePaths, filePathErr := downloadquest.GetZipFolderPaths(fileName)

	if filePathErr != nil {
		h.preflightfailed(ctx, template.RestoreTemplateId, filePathErr.Error(), expiredTime)
		return
	}
	deploymentMap := make(map[string][]*velero.DownloadFile, 0)
	statefulsetMap := make(map[string][]*velero.DownloadFile, 0)
	daemonSetMap := make(map[string][]*velero.DownloadFile, 0)
	pvcMap := make(map[string][]*velero.DownloadFile, 0)

	for _, filePath := range filePaths {
		if strings.Contains(filePath, "resources/deployments.apps/namespaces/") {
			str := strings.ReplaceAll(filePath, "resources/deployments.apps/namespaces/", "")
			namespaceStr := strings.Split(str, "/")
			if !lo.Contains(template.Namespaces, namespaceStr[0]) {
				continue
			}
			files := deploymentMap[namespaceStr[0]]
			files = append(files, &velero.DownloadFile{
				Name:      strings.Split(namespaceStr[1], ".")[0],
				Namespace: namespaceStr[0],
				Path:      filePath,
			})
			deploymentMap[namespaceStr[0]] = files
		} else if strings.Contains(filePath, "resources/statefulsets.apps/namespaces/") {
			str := strings.ReplaceAll(filePath, "resources/statefulsets.apps/namespaces/", "")
			namespaceStr := strings.Split(str, "/")
			if !lo.Contains(template.Namespaces, namespaceStr[0]) {
				continue
			}
			files := statefulsetMap[namespaceStr[0]]
			files = append(files, &velero.DownloadFile{
				Name:      strings.Split(namespaceStr[1], ".")[0],
				Namespace: namespaceStr[0],
				Path:      filePath,
			})
			statefulsetMap[namespaceStr[0]] = files
		} else if strings.Contains(filePath, "resources/daemonsets.apps/namespaces/") {
			str := strings.ReplaceAll(filePath, "resources/daemonsets.apps/namespaces/", "")
			namespaceStr := strings.Split(str, "/")
			if !lo.Contains(template.Namespaces, namespaceStr[0]) {
				continue
			}
			files := daemonSetMap[namespaceStr[0]]
			files = append(files, &velero.DownloadFile{
				Name:      strings.Split(namespaceStr[1], ".")[0],
				Namespace: namespaceStr[0],
				Path:      filePath,
			})
			daemonSetMap[namespaceStr[0]] = files
		} else if strings.Contains(filePath, "resources/persistentvolumeclaims/namespaces/") {
			str := strings.ReplaceAll(filePath, "resources/persistentvolumeclaims/namespaces/", "")
			namespaceStr := strings.Split(str, "/")
			if !lo.Contains(template.Namespaces, namespaceStr[0]) {
				continue
			}
			files := daemonSetMap[namespaceStr[0]]
			files = append(files, &velero.DownloadFile{
				Name:      strings.Split(namespaceStr[1], ".")[0],
				Namespace: namespaceStr[0],
				Path:      filePath,
			})
			pvcMap[namespaceStr[0]] = files
		}
	}

	if err := downloadquest.GetDownloadFileData(deploymentMap, fileName); err != nil {
	}
	if err := downloadquest.GetDownloadFileData(statefulsetMap, fileName); err != nil {
	}
	if err := downloadquest.GetDownloadFileData(daemonSetMap, fileName); err != nil {
	}
	if err := downloadquest.GetDownloadFileData(pvcMap, fileName); err != nil {
	} //资源
	resourceMap := make(map[string]*velero.Resource)
	//网络
	networkMap := make(map[string][]string, 0)
	//hdareaList := make([]string, 0)
	////IP池
	//hdpoolList := make([]string, 0)
	//SC
	scMap := make(map[string][]string, 0)
	if err := caculateFromData(ctx, resourceMap, networkMap, nil, "Deployment", deploymentMap, cluster.GetClient()); err != nil {
		h.preflightfailed(ctx, template.RestoreTemplateId, err.Error(), expiredTime)
	}
	if err := caculateFromData(ctx, resourceMap, networkMap, nil, "StatefulSet", statefulsetMap, cluster.GetClient()); err != nil {
		h.preflightfailed(ctx, template.RestoreTemplateId, err.Error(), expiredTime)
	}
	if err := caculateFromData(ctx, resourceMap, networkMap, nil, "DaemonSet", daemonSetMap, cluster.GetClient()); err != nil {
		h.preflightfailed(ctx, template.RestoreTemplateId, err.Error(), expiredTime)
	}
	if err := caculateFromData(ctx, nil, nil, scMap, "PersistentVolumeClaim", pvcMap, cluster.GetClient()); err != nil {
		h.preflightfailed(ctx, template.RestoreTemplateId, err.Error(), expiredTime)
	}

	//modify资源计算
	caculateWithModify(resourceMap, scMap, template.Modifys)
	resourceResultMap := caculateFinal(resourceMap)

	requestRes := compareRequest(ctx, cluster.GetClient(), resourceResultMap)
	scRes := compareSc(ctx, cluster.GetClient(), scMap)
	networkRes := compareNetWork(ctx, cluster.GetClient(), networkMap)

	h.redis.Set(ctx, "PreflightRequestResult-"+template.RestoreTemplateId, requestRes.Result, expiredTime)
	h.redis.Set(ctx, "PreflightRequestReason-"+template.RestoreTemplateId, requestRes.Reason, expiredTime)

	h.redis.Set(ctx, "PreflightScResult-"+template.RestoreTemplateId, scRes.Result, expiredTime)
	h.redis.Set(ctx, "PreflightScReason-"+template.RestoreTemplateId, scRes.Reason, expiredTime)

	h.redis.Set(ctx, "PreflightNetworkResult-"+template.RestoreTemplateId, networkRes.Result, expiredTime)
	h.redis.Set(ctx, "PreflightNetworkReason-"+template.RestoreTemplateId, networkRes.Reason, expiredTime)

	//预检完成删除保存文件
	downloadquest.RemoveFile(fileName)
}

func caculateFromData(ctx context.Context, resourceMap map[string]*velero.Resource, networkMap map[string][]string, scMap map[string][]string, kind string, fileMap map[string][]*velero.DownloadFile, clusterClient client.Client) error {
	if kind == "Deployment" {
		for namespace, files := range fileMap {
			for _, file := range files {
				deployment := &appV1.Deployment{}
				if err := json.Unmarshal([]byte(file.Data), deployment); err != nil {
					klog.Infof("Failed to Unmarshal %v", err)
					continue
				}
				resource := &velero.Resource{
					Replicas: int(*deployment.Spec.Replicas),
				}
				//判断网络域与ip池
				annotations := deployment.Spec.Template.Annotations
				getNetworkAnnotation(annotations, networkMap)

				requestMap := make(map[string]k8sResource.Quantity)
				for num, container := range deployment.Spec.Template.Spec.Containers {
					for name, quantity := range container.Resources.Requests {
						value, exist := requestMap[name.String()]
						if exist {
							value.Add(quantity)
							requestMap[strconv.Itoa(num)+"&"+name.String()] = value
						} else {
							requestMap[strconv.Itoa(num)+"&"+name.String()] = quantity
						}
					}
				}
				resource.Requests = requestMap
				resourceMap[namespace+"&"+"deployments.apps"+"&"+deployment.Name] = resource
			}
		}
	} else if kind == "StatefulSet" {
		for namespace, files := range fileMap {
			for _, file := range files {
				statefulSet := &appV1.StatefulSet{}
				if err := json.Unmarshal([]byte(file.Data), statefulSet); err != nil {
					klog.Infof("Failed to Unmarshal %v", err)
					continue
				}
				resource := &velero.Resource{
					Replicas: int(*statefulSet.Spec.Replicas),
				}
				requestMap := make(map[string]k8sResource.Quantity)
				for num, container := range statefulSet.Spec.Template.Spec.Containers {
					for name, quantity := range container.Resources.Requests {
						value, exist := requestMap[name.String()]
						if exist {
							value.Add(quantity)
							requestMap[strconv.Itoa(num)+"&"+name.String()] = value
						} else {
							requestMap[strconv.Itoa(num)+"&"+name.String()] = quantity
						}
					}
				}
				resource.Requests = requestMap
				resourceMap[namespace+"&"+"statefulsets.apps"+"&"+statefulSet.Name] = resource
			}
		}
	} else if kind == "DaemonSet" {
		nodes := &coreV1.NodeList{}
		err := clusterClient.GetCtrlClient().List(ctx, nodes)
		if err != nil {
			return err
		}
		replicas := len(nodes.Items)
		for namespace, files := range fileMap {
			for _, file := range files {
				daemonSet := &appV1.DaemonSet{}
				if err := json.Unmarshal([]byte(file.Data), daemonSet); err != nil {
					klog.Infof("Failed to Unmarshal %v", err)
					continue
				}
				resource := &velero.Resource{
					Replicas: replicas,
				}
				requestMap := make(map[string]k8sResource.Quantity)
				for num, container := range daemonSet.Spec.Template.Spec.Containers {
					for name, quantity := range container.Resources.Requests {
						value, exist := requestMap[name.String()]
						if exist {
							value.Add(quantity)
							requestMap[strconv.Itoa(num)+"&"+name.String()] = value
						} else {
							requestMap[strconv.Itoa(num)+"&"+name.String()] = quantity
						}
					}
				}
				resource.Requests = requestMap
				resourceMap[namespace+"&"+"daemonsets.apps"+"&"+daemonSet.Name] = resource
			}
		}
	} else if kind == "PersistentVolumeClaim" {
		for namespace, files := range fileMap {
			for _, file := range files {
				pvc := &coreV1.PersistentVolumeClaim{}
				if err := json.Unmarshal([]byte(file.Data), pvc); err != nil {
					klog.Infof("Failed to Unmarshal %v", err)
					continue
				}
				value, exist := scMap[namespace]
				if exist {
					if lo.Contains(value, *pvc.Spec.StorageClassName) {
						continue
					} else {
						value = append(value, *pvc.Spec.StorageClassName)
						scMap[namespace] = value
					}
				} else {
					scMap[namespace] = []string{*pvc.Spec.StorageClassName}
				}
			}
		}
	}
	return nil
}

func getNetworkAnnotation(annotations map[string]string, networkMap map[string][]string) {
	hdareaSet := sets.New[string](networkMap["hdarea"]...)
	hdpoolSet := sets.New[string](networkMap["hdpool"]...)

	ipv4Hdarea, ipv4HdareaExsit := annotations[constants.Ipv4HdareaAnnotation]
	ipv4Hdareas := make([]string, 0)
	json.Unmarshal([]byte(ipv4Hdarea), &ipv4Hdareas)
	if ipv4HdareaExsit {
		hdareaSet.Insert(ipv4Hdareas...)
	}

	ipv6Hdarea, ipv6HdareaExsit := annotations[constants.Ipv6HdareaAnnotation]
	ipv6Hdareas := make([]string, 0)
	json.Unmarshal([]byte(ipv6Hdarea), &ipv6Hdareas)
	if ipv6HdareaExsit {
		hdareaSet.Insert(ipv6Hdareas...)
	}

	dualHdarea, dualHdareaExsit := annotations[constants.DualHdareaAnnotation]
	dualHdareas := make([]string, 0)
	json.Unmarshal([]byte(dualHdarea), &dualHdareas)
	if dualHdareaExsit {
		hdareaSet.Insert(dualHdareas...)
	}

	ipv4Hdpool, ipv4HdpoolExsit := annotations[constants.Ipv4HdpoolsAnnotation]
	ipv4Hdpools := make([]string, 0)
	json.Unmarshal([]byte(ipv4Hdpool), &ipv4Hdpools)
	if ipv4HdpoolExsit {
		hdpoolSet.Insert(ipv4Hdpools...)
	}

	ipv6Hdpool, ipv6HdpoolExsit := annotations[constants.Ipv6HdpoolsAnnotation]
	ipv6Hdpools := make([]string, 0)
	json.Unmarshal([]byte(ipv6Hdpool), &ipv6Hdpool)
	if ipv6HdpoolExsit {
		hdpoolSet.Insert(ipv6Hdpools...)
	}

	dualHdpool, dualHdpoolExsit := annotations[constants.DualHdpoolsAnnotation]
	dualHdpools := make([]string, 0)
	json.Unmarshal([]byte(dualHdpool), &dualHdpool)
	if dualHdpoolExsit {
		hdpoolSet.Insert(dualHdpools...)
	}
	networkMap["hdpool"] = hdpoolSet.UnsortedList()
	networkMap["hdarea"] = hdareaSet.UnsortedList()
}

func caculateWithModify(resourceMap map[string]*velero.Resource, scMap map[string][]string, modifies []velero.Modify) {
	for _, modify := range modifies {
		for _, content := range modify.Contents {
			//sc存储
			if modify.ResourceType == "persistentvolumeclaims" {
				if strings.Contains(content.Path, "/spec/storageClassName") {
					for _, namespace := range modify.Namespaces {
						if content.Type == "replace" {
							if lo.Contains(scMap[namespace.Name], content.Value) {
								continue
							} else {
								scMap[namespace.Name] = append(scMap[namespace.Name], content.Value)
							}
						}
					}
				}
			}
			//资源量
			if modify.ResourceType == "deployments.apps" || modify.ResourceType == "statefulsets.apps" || modify.ResourceType == "daemonsets.apps" {
				if strings.Contains(content.Path, "/resources/requests/") &&
					strings.Contains(content.Path, "/spec/template/spec/containers") {
					str := strings.ReplaceAll(content.Path, "/spec/template/spec/containers/", "")
					containerNum := strings.Split(str, "/")[0]
					requestName := strings.Split(content.Path, "/resources/requests/")[1]
					modifyRequest := k8sResource.Quantity{}
					if content.Type != "remove" {
						request, err := k8sResource.ParseQuantity(content.Value)
						if err != nil {
							klog.Infof("ParseQuantity failed %v", err)
							continue
						}
						modifyRequest = request
					}
					for _, namespace := range modify.Namespaces {
						for _, resourceName := range namespace.ResourceName {
							resource := resourceMap[namespace.Name+"&"+modify.ResourceType+"&"+resourceName]
							if content.Type == "add" || content.Type == "replace" {
								resource.Requests[containerNum+"&"+requestName] = modifyRequest
							} else if content.Type == "remove" {
								delete(resource.Requests, containerNum+"&"+requestName)
							}
						}
					}
				}
				if content.Path == "/spec/replicas" {
					replicas, err := strconv.Atoi(content.Value)
					if err != nil {
						klog.Infof("Atoi error : %v", err)
						continue
					}
					for _, namespace := range modify.Namespaces {
						for _, resourceName := range namespace.ResourceName {
							resource := resourceMap[namespace.Name+"&"+modify.ResourceType+"&"+resourceName]
							if content.Type == "replace" {
								resource.Replicas = replicas
							}
						}
					}
				}
			}
		}
	}
}

func caculateFinal(resourceMap map[string]*velero.Resource) map[string]*velero.Resource {
	result := make(map[string]*velero.Resource)
	for mixStr, requests := range resourceMap {
		mixStrList := strings.Split(mixStr, "&")
		namespace := mixStrList[0]
		for containerNumAndRequest, quantity := range requests.Requests {
			finalQuantity := addAllReplicas(requests.Replicas, quantity)
			requestName := strings.Split(containerNumAndRequest, "&")[1]
			resource, exist := result[namespace]
			if exist {
				value := resource.Requests[requestName]
				value.Add(finalQuantity)
				resource.Requests[requestName] = value
			} else {
				newRequest := make(map[string]k8sResource.Quantity)
				newRequest[requestName] = finalQuantity
				newResource := &velero.Resource{
					Requests: newRequest,
				}
				result[namespace] = newResource
			}
		}
	}
	return result
}

func addAllReplicas(replicas int, quantity k8sResource.Quantity) k8sResource.Quantity {
	res := quantity
	for i := 1; i < replicas; i++ {
		res.Add(quantity)
	}
	return res
}

func compareRequest(ctx context.Context, clusterClient client.Client, resourceMap map[string]*velero.Resource) velero.CompareResult {
	resourceQuotas := coreV1.ResourceQuotaList{}
	err := clusterClient.GetCtrlClient().List(ctx, &resourceQuotas)
	if err != nil {
		return velero.CompareResult{
			Kind:   "request",
			Result: "failed",
			Reason: "获取命名空间资源量失败",
		}
	}
	lessRequests := make(map[string][]string, 0)
	var flag = true
	for _, item := range resourceQuotas.Items {
		resource, exist := resourceMap[item.Namespace]
		less := make([]string, 0)
		if exist {
			for name, quantity := range resource.Requests {
				value, quaExist := item.Spec.Hard[coreV1.ResourceName(name)]
				if quaExist {
					if value.Cmp(quantity) > -1 {
						continue
					}
				}
				less = append(less, name)
				lessRequests[item.Namespace] = less
				flag = false
			}
		}
	}
	if !flag {
		var reason string
		for namesapce, request := range lessRequests {
			str := strings.Join(request, ",")
			reason = fmt.Sprintf("%s 命名空间：%s %s不满足资源用量", reason, namesapce, str)
		}
		return velero.CompareResult{
			Kind:   "request",
			Result: "failed",
			Reason: reason,
		}
	}
	return velero.CompareResult{
		Kind:   "request",
		Result: "success",
	}
}

func compareSc(ctx context.Context, clusterClient client.Client, scMap map[string][]string) velero.CompareResult {
	resourceQuotas := coreV1.ResourceQuotaList{}
	err := clusterClient.GetCtrlClient().List(ctx, &resourceQuotas)
	if err != nil {
		return velero.CompareResult{
			Kind:   "request",
			Result: "failed",
			Reason: "获取命名空间资源量失败",
		}
	}

	//scList := storageV1.StorageClassList{}
	//err := clusterClient.GetCtrlClient().List(ctx, &scList)
	if err != nil {
		result := velero.CompareResult{
			Kind:   "sc",
			Result: "failed",
			Reason: fmt.Sprintln("获取命名空间资源量失败,err: %v", err),
		}
		return result
	}
	unReach := make([]string, 0)
	for _, item := range resourceQuotas.Items {
		resources, exist := scMap[item.Namespace]
		if exist {
			scNames := make(map[string]bool, 0)
			for k, _ := range item.Spec.Hard {
				var scName string
				if strings.Contains(k.String(), ".storageclass.storage.k8s.io/requests.storage") {
					scName = strings.ReplaceAll(k.String(), ".storageclass.storage.k8s.io/requests.storage", "")
				}
				scNames[scName] = true
			}
			for _, resource := range resources {
				_, e := scNames[resource]
				if e {
					continue
				} else {
					unReach = append(unReach, resource)
				}
			}
			if len(unReach) != 0 {
				str := strings.Join(unReach, ",")
				result := velero.CompareResult{
					Kind:   "sc",
					Result: "failed",
					Reason: fmt.Sprintf("storageclasses not allocated : %s", str),
				}
				return result
			}
		}
	}

	//scNames := make([]string, 0)
	//for _, sc := range scList.Items {
	//	scNames = append(scNames, sc.Name)
	//}
	//unReach := make([]string, 0)
	//for _, scName := range scMap {
	//	if !lo.Contains(scNames, scName) {
	//		unReach = append(unReach, scName)
	//	}
	//}
	//if len(unReach) != 0 {
	//	str := strings.Join(unReach, ",")
	//	result := velero.CompareResult{
	//		Kind:   "sc",
	//		Result: "failed",
	//		Reason: fmt.Sprintf("unexpected storageclasses : %s", str),
	//	}
	//	return result
	//}
	return velero.CompareResult{
		Kind:   "sc",
		Result: "success",
	}
}

func compareNetWork(ctx context.Context, clusterClient client.Client, networkMap map[string][]string) velero.CompareResult {
	hdareaList := networkMap["hdarea"]
	hdpoolList := networkMap["hdpool"]
	hdAreaList, areaError := clusterClient.NewList("heimdallr.harmonycloud.cn", "v1alpha1", "HDArea")
	if areaError != nil {
		return velero.CompareResult{
			Kind:   "network",
			Result: "failed",
			Reason: fmt.Sprintf("get hdAreaList error : %v", areaError),
		}
	}
	hdPoolList, poolError := clusterClient.NewList("heimdallr.harmonycloud.cn", "v1alpha1", "HDPool")
	if poolError != nil {
		return velero.CompareResult{
			Kind:   "network",
			Result: "failed",
			Reason: fmt.Sprintf("get hdPoolList error : %v", poolError),
		}
	}

	areaUnreach := make([]string, 0)
	poolUnreach := make([]string, 0)

	areaError = clusterClient.GetCtrlClient().List(ctx, hdAreaList)
	hdAreas := &unstructured.UnstructuredList{}
	_ = utils.ToStruct(hdAreaList, hdAreas)
	for _, name := range hdareaList {
		flag := false
		for _, item := range hdAreas.Items {
			if item.GetName() == name {
				flag = true
				break
			}
		}
		if !flag {
			areaUnreach = append(areaUnreach, name)
		}
	}

	areaError = clusterClient.GetCtrlClient().List(ctx, hdPoolList)
	hdPools := &unstructured.UnstructuredList{}
	_ = utils.ToStruct(hdPoolList, hdPools)
	for _, name := range hdpoolList {
		flag := false
		for _, item := range hdPools.Items {
			if item.GetName() == name {
				flag = true
				break
			}
		}
		if !flag {
			poolUnreach = append(poolUnreach, name)
		}
	}
	var result = "success"
	var reason string
	if len(areaUnreach) != 0 {
		result = "failed"
		reason = fmt.Sprintf("%s 网络域缺失：%s", reason, strings.Join(areaUnreach, ","))
	}
	if len(poolUnreach) != 0 {
		result = "failed"
		reason = fmt.Sprintf("%s ip池缺失：%s", reason, strings.Join(poolUnreach, ","))
	}
	return velero.CompareResult{
		Kind:   "network",
		Result: result,
		Reason: reason,
	}
}

func (h *restoreHandler) preflightfailed(ctx context.Context, restoreTemplateId string, reason string, expiredTime time.Duration) {
	h.redis.Set(ctx, constants.PreflightRequestRedisResKey+constants.Dash+restoreTemplateId, "failed", expiredTime)
	h.redis.Set(ctx, constants.PreflightScRedisResKey+constants.Dash+restoreTemplateId, "failed", expiredTime)
	h.redis.Set(ctx, constants.PreflightNetworkRedisResKey+constants.Dash+restoreTemplateId, "failed", expiredTime)
	h.redis.Set(ctx, constants.PreflightRequestRedisReasonKey+constants.Dash+restoreTemplateId, reason, expiredTime)
	h.redis.Set(ctx, constants.PreflightScRedisReasonKey+constants.Dash+restoreTemplateId, reason, expiredTime)
	h.redis.Set(ctx, constants.PreflightNetworkRedisReasonKey+constants.Dash+restoreTemplateId, reason, expiredTime)
}
