package backuprestore

import (
	"context"
	"sync"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/database"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/storageserver"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/velero"
)

func NewBackupInterface() BackupInterface {
	return &backupHandler{
		podVolumeBackupInterface: NewPodVolumeBackupInterface(),
		solveBackupSizeLock:      sync.Mutex{},
	}
}

type BackupInterface interface {
	ListBackups(ctx context.Context, clusterName string, matchLabels map[string]string, backupStatus string) ([]velero.Backups, error)

	DeleteBackups(ctx context.Context, clusterName, backupName string) error

	CreateBackupsBySchedules(ctx context.Context, clusterName, schedulesName string) error

	ListBackupsResources(ctx context.Context, clusterName, schedulesName, backupName string) ([]velero.VeleroResources, error)

	LogsBackups(ctx context.Context, clusterName, schedulesName, backupName string) (string, error)

	SolveBackupSize()
}

func NewPodVolumeBackupInterface() PodVolumeBackupInterface {
	return &podVolumeBackupHandler{}
}

type PodVolumeBackupInterface interface {
	ListPodVolumeBackup(ctx context.Context, clusterName string, matchLabels map[string]string) ([]velero.PodVolumeBackup, error)

	ListBackupVolumes(ctx context.Context, clusterName, scheduleName, backupName string) ([]velero.VeleroVolumes, error)
}

func NewPodVolumeRestoreInterface() PodVolumeRestoreInterface {
	return &podVolumeRestoreHandle{}
}

type PodVolumeRestoreInterface interface {
	ListPodVolumeRestore(ctx context.Context, clusterName string, matchLabels map[string]string) ([]velero.PodVolumeRestore, error)

	ListRestoreVolumes(ctx context.Context, clusterName, restoreTemplateId, restoreName string) ([]velero.VeleroVolumes, error)
}

func NewRestoreInterface() RestoreInterface {
	return &restoreHandler{
		podVolumeRestoreInterface: NewPodVolumeRestoreInterface(),
		solveRestoreSizeLock:      sync.Mutex{},
		redis:                     database.RDS,
	}
}

type RestoreInterface interface {
	ListRestore(ctx context.Context, clusterName string, matchLabels map[string]string) ([]velero.Restore, error)

	ListRestoresResources(ctx context.Context, clusterName, restoreName string) ([]velero.VeleroResources, error)

	CreateRestore(ctx context.Context, template velero.RestoreTemplate) error

	DeleteRestore(ctx context.Context, clusterName, restoresTemplateId string) error

	LogsRestore(ctx context.Context, clusterName, restoreName string) (string, error)

	PreflightRestoreTemplate(ctx context.Context, template *velero.RestoreTemplate)

	SolveRestoreSize()
}

func NewRestoreTemplateInterface() RestoreTemplateInterface {
	return &restoreTemplateHandler{
		caasDB:           database.CaasDB,
		restoreInterface: NewRestoreInterface(),
		redis:            database.RDS,
	}
}

type RestoreTemplateInterface interface {
	ListRestoreTemplates(ctx context.Context, fromCluster string, targetCluster string) ([]velero.RestoreTemplate, error)

	// ListRestore id为空则有集群查集群所有，没集群差所有集群；id不为空则查该id对应的，集群传则按照集群查，集群不传则会先去数据库查对应集群
	ListRestore(ctx context.Context, clusterName, restoresTemplateId string) ([]velero.Restore, error)

	CreateRestoreTemplate(ctx context.Context, template velero.RestoreTemplate) (string, error)

	EditRestoreTemplate(ctx context.Context, restoresTemplateId string, template velero.RestoreTemplate) error

	ListResourceType(ctx context.Context, cluster string, backupName string, namespaces []string) ([]velero.ResourceByType, error)

	GetPreflightResult(ctx context.Context, restoreTemplateId string) []velero.CompareResult

	GetRestoreTemplate(ctx context.Context, restoresTemplateId string) (*velero.RestoreTemplate, error)

	DeleteRestoreTemplate(ctx context.Context, restoresTemplateId string) error
}

func NewSchedulerInterface() SchedulerInterface {
	return &schedulerHandler{
		veleroStorageServersInterface: storageserver.NewVeleroStorageServerInterface(),
		backupInterface:               NewBackupInterface(),
	}
}

type SchedulerInterface interface {
	ListSchedulers(ctx context.Context, clusterName string) ([]velero.Schedules, error)

	GetSchedulers(ctx context.Context, clusterName, schedulersName string) (*velero.Schedules, error)

	CreateSchedulers(ctx context.Context, clusterName string, schedules velero.Schedules) error

	UpdateSchedulers(ctx context.Context, clusterName string, schedules velero.Schedules) error

	DeleteSchedulers(ctx context.Context, clusterName, schedulesName string) error
}
