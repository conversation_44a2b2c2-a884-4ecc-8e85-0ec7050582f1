package backuprestore

import (
	"context"

	veleroV1 "github.com/vmware-tanzu/velero/pkg/apis/velero/v1"
	"harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/velero"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	runtimeclient "sigs.k8s.io/controller-runtime/pkg/client"
)

type podVolumeBackupHandler struct {
}

func (h *podVolumeBackupHandler) ListPodVolumeBackup(ctx context.Context, clusterName string, matchLabels map[string]string) ([]velero.PodVolumeBackup, error) {
	clusterClient, clientError := client.GetCluster(clusterName)
	if clientError != nil {
		return nil, clientError
	}

	podVolumeBackupList := veleroV1.PodVolumeBackupList{}

	_ = clusterClient.GetClient().GetCtrlClient().List(ctx, &podVolumeBackupList, runtimeclient.InNamespace(constants.VeleroNamespace), runtimeclient.MatchingLabels(matchLabels))

	ans := make([]velero.PodVolumeBackup, 0)

	for _, podVolumeBackup := range podVolumeBackupList.Items {
		ans = append(ans, *velero.Convert2PodVolumeBackup(podVolumeBackup))
	}

	return ans, nil
}

func (h *podVolumeBackupHandler) ListBackupVolumes(ctx context.Context, clusterName, scheduleName, backupName string) ([]velero.VeleroVolumes, error) {
	podVolumeBackupList, listPodVolumeBackupError := h.ListPodVolumeBackup(ctx, clusterName, map[string]string{veleroV1.BackupNameLabel: backupName})
	if listPodVolumeBackupError != nil {
		return nil, listPodVolumeBackupError
	}

	ans := make([]velero.VeleroVolumes, 0)

	for _, podVolumeBackup := range podVolumeBackupList {
		ans = append(ans, utils.Convert2VeleroVolumes(podVolumeBackup))
	}

	return ans, nil
}
