package backuprestore

import (
	"context"
	"encoding/json"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	rds "github.com/redis/go-redis/v9"
	veleroV1 "github.com/vmware-tanzu/velero/pkg/apis/velero/v1"
	"gorm.io/gorm"
	"harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/storageserver"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/velero"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/downloadquest"
)

type restoreTemplateHandler struct {
	caasDB           *gorm.DB
	restoreInterface RestoreInterface
	redis            *rds.Client
}

func (h *restoreTemplateHandler) ListRestoreTemplates(ctx context.Context, fromCluster string, targetCluster string) ([]velero.RestoreTemplate, error) {
	var restoreTemplateList []caas.RestoreTemplate

	db := h.caasDB.
		Model(&caas.RestoreTemplate{})
	if targetCluster != "" {
		db = db.Where("target_cluster = ?", targetCluster)
	}
	if fromCluster != "" {
		db = db.Where("from_cluster = ?", fromCluster)
	}

	if err := db.
		Find(&restoreTemplateList).
		Error; err != nil {
		return nil, err
	}

	restoreMap := make(map[string]velero.Restore)

	restoreList, listRestoreError := h.ListRestore(ctx, targetCluster, "")
	if listRestoreError != nil {
		return nil, listRestoreError
	}

	for _, restore := range restoreList {
		value, ok := restoreMap[restore.RestoreTemplateId+restore.TargetCluster]

		// 不存在或时间更新则更新
		if !ok || utils.TimeAfter(restore.CreateTime, value.CreateTime) {
			restoreMap[restore.RestoreTemplateId+restore.TargetCluster] = restore
		}
	}

	var ans = make([]velero.RestoreTemplate, 0)

	for _, restoreTemplateItem := range restoreTemplateList {
		value, ok := restoreMap[strconv.Itoa(int(restoreTemplateItem.ID))+restoreTemplateItem.TargetCluster]

		if ok {
			ans = append(ans, *restoreTemplateItem.RevertRestoreTemplate(value))
		} else {
			restoreTemplate := utils.RevertTemplateFromDb(restoreTemplateItem)
			//// 集群中没有restore就删掉数据库数据
			//go func() {
			//	_ = h.DeleteRestoreTemplate(ctx, strconv.FormatInt(restoreTemplateItem.ID, 10))
			//}()
			//说明未开始还原，获取预检结果

			restoreTemplate.Status = h.getRestoreTempCheckStatus(ctx, restoreTemplateItem)
			ans = append(ans, *restoreTemplate)
		}
	}

	return ans, nil
}

func (h *restoreTemplateHandler) ListRestore(ctx context.Context, clusterName, restoresTemplateId string) ([]velero.Restore, error) {
	// 集群为空但恢复策略id不为空则需要先找到恢复策略所在集群
	if clusterName == "" && restoresTemplateId != "" {
		var restoreTemplate caas.RestoreTemplate

		if err := h.caasDB.
			Model(&caas.RestoreTemplate{}).
			Where("id = ?", restoresTemplateId).
			Find(&restoreTemplate).
			Error; err != nil {
			return nil, err
		}

		clusterName = restoreTemplate.TargetCluster
	}

	// 恢复策略id不为空则代表要查该恢复策略
	matchLabels := make(map[string]string)
	if restoresTemplateId != "" {
		matchLabels[constants.RestoreTemplateId] = restoresTemplateId
	}

	// 集群为空查所有集群的实现逻辑在list里以实现
	restoreList, listRestoreErr := h.restoreInterface.ListRestore(ctx, clusterName, matchLabels)
	if listRestoreErr != nil {
		return nil, listRestoreErr
	}

	return restoreList, nil
}

func (h *restoreTemplateHandler) CreateRestoreTemplate(ctx context.Context, template velero.RestoreTemplate) (string, error) {
	id := int64(uuid.New().ID() % 2147483647)

	template.RestoreTemplateId = strconv.FormatInt(id, 10)
	var modify string
	if template.IsModify {
		modifyStr, err := json.Marshal(template.Modifys)
		if err != nil {
			return "", err
		}
		modify = string(modifyStr)
	}
	restore, jsonError := json.Marshal(BuildVeleroRestore(template.RestoreTemplateName, template))
	if jsonError != nil {
		return "", jsonError
	}

	restoreTemplate := &caas.RestoreTemplate{
		ID:            id,
		Restore:       string(restore),
		RestoreType:   template.RestoreType,
		ScheduleName:  template.ScheduleName,
		FromCluster:   template.FromCluster,
		TargetCluster: template.TargetCluster,
		IsModify:      template.IsModify,
	}
	if template.IsModify {
		restoreTemplate.Modify = modify
	}

	if err := h.caasDB.
		Model(&caas.RestoreTemplate{}).
		Create(restoreTemplate).Error; err != nil {
		return "", err
	}
	return strconv.Itoa(int(id)), nil
}

func (h *restoreTemplateHandler) EditRestoreTemplate(ctx context.Context, restoresTemplateId string, template velero.RestoreTemplate) error {
	var restoreTemplate caas.RestoreTemplate
	if err := h.caasDB.
		Model(&caas.RestoreTemplate{}).
		Where("id = ?", restoresTemplateId).
		Find(&restoreTemplate).
		Error; err != nil {
		return err
	}
	var modify string
	if template.IsModify {
		modifyStr, err := json.Marshal(template.Modifys)
		if err != nil {
			return err
		}
		modify = string(modifyStr)
	}
	restore, jsonError := json.Marshal(BuildVeleroRestore(template.RestoreTemplateName, template))
	if jsonError != nil {
		return jsonError
	}

	id, atoiError := strconv.Atoi(restoresTemplateId)
	if atoiError != nil {
		return atoiError
	}

	newRestoreTemplate := &caas.RestoreTemplate{
		ID:            int64(id),
		Restore:       string(restore),
		RestoreType:   template.RestoreType,
		ScheduleName:  template.ScheduleName,
		FromCluster:   template.FromCluster,
		TargetCluster: template.TargetCluster,
		IsModify:      template.IsModify,
	}
	if template.IsModify {
		newRestoreTemplate.Modify = modify
	}

	if err := h.caasDB.
		Model(newRestoreTemplate).
		Updates(newRestoreTemplate).Error; err != nil {
		return err
	}
	return nil

}

func (h *restoreTemplateHandler) GetRestoreTemplate(ctx context.Context, restoresTemplateId string) (*velero.RestoreTemplate, error) {
	var restoreTemplate caas.RestoreTemplate

	if err := h.caasDB.
		Model(&caas.RestoreTemplate{}).
		Where("id = ?", restoresTemplateId).
		Find(&restoreTemplate).
		Error; err != nil {
		return nil, err
	}

	restoreList, listRestoreError := h.ListRestore(ctx, restoreTemplate.TargetCluster, restoresTemplateId)
	if listRestoreError != nil {
		return nil, listRestoreError
	}

	var restore velero.Restore

	if len(restoreList) == 1 {
		restore = restoreList[0]
	} else if len(restoreList) > 1 {
		maxi := 0
		for i := 1; i < len(restoreList); i++ {
			if utils.TimeAfter(restoreList[i].CreateTime, restoreList[maxi].CreateTime) {
				maxi = i
			}
		}
		restore = restoreList[maxi]
	} else {
		restoreTemp := utils.RevertTemplateFromDb(restoreTemplate)
		restoreTemp.Status = h.getRestoreTempCheckStatus(ctx, restoreTemplate)
		return restoreTemp, nil
	}

	template := restoreTemplate.RevertRestoreTemplate(restore)

	schedule, error := NewSchedulerInterface().GetSchedulers(ctx, restoreTemplate.FromCluster, restoreTemplate.ScheduleName)
	if error != nil {
		template.StorageServersStatus = velero.SyncError
		template.StorageServersInfo = "未找到,请检查备份服务器是否存在"
	} else {
		s, _ := storageserver.NewStorageServerInterface().GetStorageServers(ctx, schedule.StorageServers.StorageServersId)
		// 隐藏不可展示信息
		s.ClearInformation()
		template.StorageServers = *s
		template.StorageServersStatus, template.StorageServersInfo = storageserver.NewVeleroStorageServerInterface().GetStorageServerStatus(ctx, restoreTemplate.FromCluster, schedule.StorageServers.StorageServersId)
	}

	return template, nil
}

func (h *restoreTemplateHandler) DeleteRestoreTemplate(ctx context.Context, restoresTemplateId string) error {
	id, atoiError := strconv.Atoi(restoresTemplateId)
	if atoiError != nil {
		return atoiError
	}

	if err := h.caasDB.
		Model(caas.RestoreTemplate{}).
		Delete(&caas.RestoreTemplate{
			ID: int64(id),
		}).Error; err != nil {
		return err
	}

	return nil
}

func (h *restoreTemplateHandler) ListResourceType(ctx context.Context, cluster string, backupName string, namespaces []string) ([]velero.ResourceByType, error) {
	clusterClient, _ := client.GetCluster(cluster)

	resp, streamError := downloadquest.Stream(ctx, clusterClient.GetClient().GetCtrlClient(), constants.VeleroNamespace, backupName, veleroV1.DownloadTargetKindBackupContents, time.Second, true, "")
	if streamError != nil {
		return nil, streamError
	}
	//将文件存在本地
	fileName, saveErr := downloadquest.SaveFileInLocal(resp.Body, backupName)
	if saveErr != nil {
		return nil, saveErr
	}
	filePaths, filePathErr := downloadquest.GetZipFolderPaths(fileName)
	if filePathErr != nil {
		return nil, filePathErr
	}

	resourceTypeMap := make(map[string][]velero.NamespaceResource, 0)

a:
	for _, filePath := range filePaths {
		strList := strings.Split(filePath, "/")
		if strList[0] != "resources" {
			continue
		}
		if strList[2] != "namespaces" {
			continue
		}
		resourcesType := strList[1]
		resource := strings.ReplaceAll(strList[4], ".json", "")
		value, exist := resourceTypeMap[resourcesType]
		if exist {
			for i, item := range value {
				if strList[3] == item.Namespace {
					value[i].ResourceNames = append(value[i].ResourceNames, resource)
					continue a
				}
			}
			namespaceResource := velero.NamespaceResource{
				Namespace:     strList[3],
				ResourceNames: []string{resource},
			}
			value = append(value, namespaceResource)
			resourceTypeMap[resourcesType] = value
		} else {
			namespaceResource := velero.NamespaceResource{
				Namespace:     strList[3],
				ResourceNames: []string{resource},
			}
			resourceTypeMap[resourcesType] = []velero.NamespaceResource{namespaceResource}
		}
	}
	ansList := make([]velero.ResourceByType, 0)
	for k, v := range resourceTypeMap {
		ans := velero.ResourceByType{
			ResourceType: k,
			Namespace:    v,
		}
		ansList = append(ansList, ans)
	}
	return ansList, nil
}

func (h *restoreTemplateHandler) GetPreflightResult(ctx context.Context, restoreTemplateId string) []velero.CompareResult {
	res := make([]velero.CompareResult, 0)
	res = append(res, h.getPreflightResult(ctx, restoreTemplateId, "request"))
	res = append(res, h.getPreflightResult(ctx, restoreTemplateId, "sc"))
	res = append(res, h.getPreflightResult(ctx, restoreTemplateId, "network"))
	return res
}

func (h *restoreTemplateHandler) getPreflightResult(ctx context.Context, restoreTempId string, kind string) velero.CompareResult {
	result := &rds.StringCmd{}
	reason := &rds.StringCmd{}
	if kind == "request" {
		result = h.redis.Get(ctx, constants.PreflightRequestRedisResKey+constants.Dash+restoreTempId)
		reason = h.redis.Get(ctx, constants.PreflightRequestRedisReasonKey+constants.Dash+restoreTempId)
	} else if kind == "sc" {
		result = h.redis.Get(ctx, constants.PreflightScRedisResKey+constants.Dash+restoreTempId)
		reason = h.redis.Get(ctx, constants.PreflightScRedisReasonKey+constants.Dash+restoreTempId)

	} else if kind == "network" {
		result = h.redis.Get(ctx, constants.PreflightNetworkRedisResKey+constants.Dash+restoreTempId)
		reason = h.redis.Get(ctx, constants.PreflightNetworkRedisReasonKey+constants.Dash+restoreTempId)
	}

	if result.Err() != nil {
		if result.Err() == rds.Nil {
			return velero.CompareResult{
				Kind:   kind,
				Result: "expired",
			}
		} else {
			return velero.CompareResult{
				Kind:   kind,
				Result: "failed",
				Reason: "获取预检结果失败",
			}
		}
	} else {
		return velero.CompareResult{
			Kind:   kind,
			Result: result.Val(),
			Reason: reason.Val(),
		}
	}
}

func (h *restoreTemplateHandler) getRestoreTempCheckStatus(ctx context.Context, restoreTemplateItem caas.RestoreTemplate) string {
	var expired, preflighting, failed bool
	requestResult := h.getPreflightResult(ctx, strconv.Itoa(int(restoreTemplateItem.ID)), "request")
	if requestResult.Result == "expired" {
		expired = true
	} else if requestResult.Result == "preflighting" {
		preflighting = true
	} else if requestResult.Result == "failed" {
		failed = true
	}
	scResult := h.getPreflightResult(ctx, strconv.Itoa(int(restoreTemplateItem.ID)), "sc")
	if scResult.Result == "expired" {
		expired = true
	} else if scResult.Result == "preflighting" {
		preflighting = true
	} else if scResult.Result == "failed" {
		failed = true
	}
	networkResult := h.getPreflightResult(ctx, strconv.Itoa(int(restoreTemplateItem.ID)), "network")
	if networkResult.Result == "expired" {
		expired = true
	} else if networkResult.Result == "preflighting" {
		preflighting = true
	} else if networkResult.Result == "failed" {
		failed = true
	}
	if expired {
		return "PreflightExpired"
	} else {
		if preflighting {
			return "Preflighting"
		} else {
			if failed {
				return "PreflightFailed"
			} else {
				return "PreflightSuccess"
			}
		}
	}
}
