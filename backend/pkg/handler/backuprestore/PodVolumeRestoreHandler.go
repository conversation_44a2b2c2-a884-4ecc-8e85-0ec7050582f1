package backuprestore

import (
	"context"

	veleroV1 "github.com/vmware-tanzu/velero/pkg/apis/velero/v1"
	"harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/velero"
	runtimeclient "sigs.k8s.io/controller-runtime/pkg/client"
)

type podVolumeRestoreHandle struct {
}

func (h *podVolumeRestoreHandle) ListPodVolumeRestore(ctx context.Context, clusterName string, matchLabels map[string]string) ([]velero.PodVolumeRestore, error) {
	clusterClient, clientError := client.GetCluster(clusterName)
	if clientError != nil {
		return nil, clientError
	}

	podVolumeRestoreList := veleroV1.PodVolumeRestoreList{}

	_ = clusterClient.GetClient().GetCtrlClient().List(ctx, &podVolumeRestoreList, runtimeclient.InNamespace(constants.VeleroNamespace), runtimeclient.MatchingLabels(matchLabels))

	ans := make([]velero.PodVolumeRestore, 0)

	for _, podVolumeRestore := range podVolumeRestoreList.Items {
		ans = append(ans, *velero.Convert2PodVolumeRestore(podVolumeRestore))
	}

	return ans, nil
}

func (h *podVolumeRestoreHandle) ListRestoreVolumes(ctx context.Context, clusterName, restoreTemplateId, restoreName string) ([]velero.VeleroVolumes, error) {
	podVolumeRestoreList, listPodVolumeRestoreError := h.ListPodVolumeRestore(ctx, clusterName, map[string]string{veleroV1.RestoreNameLabel: restoreName})
	if listPodVolumeRestoreError != nil {
		return nil, listPodVolumeRestoreError
	}

	ans := make([]velero.VeleroVolumes, 0)

	for _, podVolumeRestore := range podVolumeRestoreList {
		ans = append(ans, *podVolumeRestore.Convert2VeleroVolumes())
	}

	return ans, nil
}
