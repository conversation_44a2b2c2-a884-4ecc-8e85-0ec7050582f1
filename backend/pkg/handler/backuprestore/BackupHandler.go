package backuprestore

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"sync"
	"time"

	veleroV1 "github.com/vmware-tanzu/velero/pkg/apis/velero/v1"
	"harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/velero"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/downloadquest"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtimeclient "sigs.k8s.io/controller-runtime/pkg/client"
)

type backupHandler struct {
	podVolumeBackupInterface PodVolumeBackupInterface
	solveBackupSizeLock      sync.Mutex
}

func (h *backupHandler) ListBackups(ctx context.Context, clusterName string, matchLabels map[string]string, backupStatus string) ([]velero.Backups, error) {
	clusterList := make([]client.Cluster, 0)

	if clusterName == "" {
		clusterList = client.ListOnlineClusters()
	} else {
		clusterClient, clientError := client.GetCluster(clusterName)
		if clientError != nil {
			return nil, clientError
		}
		clusterList = append(clusterList, clusterClient)
	}

	ans := make([]velero.Backups, 0)

	needSolveBackupSize := false
	for _, clusterClient := range clusterList {
		backupsList := veleroV1.BackupList{}

		_ = clusterClient.GetClient().GetCtrlClient().List(ctx, &backupsList, runtimeclient.InNamespace(constants.VeleroNamespace), runtimeclient.MatchingLabels(matchLabels))

		for _, backup := range backupsList.Items {

			_, ok := backup.Annotations[constants.ResourcesSize]
			if !ok {
				if backup.Status.Phase == veleroV1.BackupPhaseCompleted ||
					backup.Status.Phase == veleroV1.BackupPhasePartiallyFailed {
					needSolveBackupSize = true
				}
			}
			if backupStatus == "complete" {
				if backup.Status.Phase == veleroV1.BackupPhaseCompleted ||
					backup.Status.Phase == veleroV1.BackupPhasePartiallyFailed {
					ans = append(ans, utils.Convert2Backups(backup))
				}
			} else {
				ans = append(ans, utils.Convert2Backups(backup))
			}
		}
	}

	if needSolveBackupSize {
		go func() {
			h.SolveBackupSize()
		}()
	}

	return ans, nil
}

func (h *backupHandler) DeleteBackups(ctx context.Context, clusterName, backupName string) error {
	clusterClient, clientError := client.GetCluster(clusterName)
	if clientError != nil {
		return clientError
	}

	backup := &veleroV1.Backup{}

	if err := clusterClient.GetClient().GetCtrlClient().Get(ctx, runtimeclient.ObjectKey{Name: backupName, Namespace: constants.VeleroNamespace}, backup); err != nil {
		return err
	}

	deleteBackupRequest := &veleroV1.DeleteBackupRequest{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: constants.VeleroNamespace,
			Labels: map[string]string{
				veleroV1.BackupNameLabel: backup.Name,
				veleroV1.BackupUIDLabel:  string(backup.UID),
			},
			GenerateName: backupName + "-",
		},
		Spec: veleroV1.DeleteBackupRequestSpec{
			BackupName: backupName,
		},
	}

	if err := clusterClient.GetClient().GetCtrlClient().Create(ctx, deleteBackupRequest); err != nil {
		return err
	}

	return nil
}

func (h *backupHandler) CreateBackupsBySchedules(ctx context.Context, clusterName, schedulesName string) error {
	clusterClient, clientError := client.GetCluster(clusterName)
	if clientError != nil {
		return clientError
	}

	schedules := veleroV1.Schedule{}

	if err := clusterClient.GetClient().GetCtrlClient().Get(ctx, runtimeclient.ObjectKey{Name: schedulesName, Namespace: constants.VeleroNamespace}, &schedules); err != nil {
		return err
	}

	backup := velero.BuildVeleroBackups(&schedules, clusterName)

	if err := clusterClient.GetClient().GetCtrlClient().Create(ctx, backup); err != nil {
		return err
	}

	return nil
}

func (h *backupHandler) ListBackupsResources(ctx context.Context, clusterName, schedulesName, backupName string) ([]velero.VeleroResources, error) {
	clusterClient, clientError := client.GetCluster(clusterName)
	if clientError != nil {
		return nil, clientError
	}

	return downloadquest.ListResources(ctx, clusterClient.GetClient().GetCtrlClient(), constants.VeleroNamespace, backupName, veleroV1.DownloadTargetKindBackupResourceList)
}

func (h *backupHandler) LogsBackups(ctx context.Context, clusterName, schedulesName, backupName string) (string, error) {
	clusterClient, clientError := client.GetCluster(clusterName)
	if clientError != nil {
		return constants.Blank, clientError
	}

	return downloadquest.Logs(ctx, clusterClient.GetClient().GetCtrlClient(), constants.VeleroNamespace, backupName, veleroV1.DownloadTargetKindBackupLog)
}

func (h *backupHandler) SolveBackupSize() {
	// 全局唯一锁
	// PS: TryLock为尝试并锁住，并返回尝试结果
	tryLock := h.solveBackupSizeLock.TryLock()
	defer h.solveBackupSizeLock.Unlock()

	// 锁成功代表是第一次，非第一次进来的锁全部跳过
	if tryLock {
		clusterList := client.ListOnlineClusters()

		for _, cluster := range clusterList {
			go func(cluster client.Cluster) {
				backgroundCtx, _ := context.WithTimeout(context.Background(), time.Minute)

				backupsList := veleroV1.BackupList{}

				logger.GetLogger().Info(fmt.Sprintf("Cluster:%s solve backup size start", cluster.GetName()))

				if err := cluster.GetClient().GetCtrlClient().List(backgroundCtx, &backupsList, runtimeclient.InNamespace(constants.VeleroNamespace)); err != nil {
					return
				}

				for _, backup := range backupsList.Items {
					if backup.Status.Phase == veleroV1.BackupPhaseCompleted || backup.Status.Phase == veleroV1.BackupPhasePartiallyFailed {
						_, ok := backup.Annotations[constants.ResourcesSize]
						if !ok {
							logger.GetLogger().Info(fmt.Sprintf("Cluster:%s backup:%s resource refresh", cluster.GetName(), backup.Name))

							resourceList, _ := h.ListBackupsResources(backgroundCtx, cluster.GetName(), backup.Labels[veleroV1.ScheduleNameLabel], backup.Name)
							backup.Annotations[constants.ResourcesSize] = strconv.Itoa(len(resourceList))
							logger.GetLogger().Info(fmt.Sprintf("Cluster:%s backup:%s resouce size:%d", cluster.GetName(), backup.Name, len(resourceList)))

							volumesList, _ := h.podVolumeBackupInterface.ListBackupVolumes(backgroundCtx, cluster.GetName(), backup.Labels[veleroV1.ScheduleNameLabel], backup.Name)
							backup.Annotations[constants.PersistentVolumeSize] = strconv.Itoa(len(volumesList))
							logger.GetLogger().Info(fmt.Sprintf("Clusrer:%s backup:%s volumes size:%d", cluster.GetName(), backup.Name, len(volumesList)))

							namespaces := make([]string, 0)
							for _, veleroResources := range resourceList {
								if veleroResources.ResourceType == "Namespace" {
									namespaces = append(namespaces, veleroResources.ResourceName)
								}
							}
							namespaceByte, _ := json.Marshal(namespaces)
							backup.Annotations[constants.BackupNamespaces] = string(namespaceByte)

							if err := cluster.GetClient().GetCtrlClient().Update(backgroundCtx, &backup); err != nil {
								logger.GetLogger().Error(fmt.Sprintf("solve backup:%v size error, error:%+v", backup.Name, err))
							}
						}
					}
				}
			}(cluster)
		}
	}
}
