package template

import (
	"bufio"
	"context"
	"mime/multipart"

	"github.com/xuri/excelize/v2"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/template"
)

type excelIntfSlice []excelIntf

func (arr excelIntfSlice) findByCode(templateCode string) excelIntf {
	var target excelIntf
	if len(arr) != 0 {
		for _, intf := range arr {
			if string(intf.getTemplateCode()) == templateCode {
				target = intf
				break
			}
		}
	}
	return target
}

func NewHandler() Handler {
	return &handler{
		excelIntfList: excelIntfSlice{
			newTranslateExcelIntf(newClusterCreateAllInOneNodesTemplateExcel()),
			newTranslateExcelIntf(newClusterCreateMinimizeHANodesTemplateExcel()),
			newTranslateExcelIntf(newClusterCreateStandardNoneHANodesTemplateExcel()),
			newTranslateExcelIntf(newClusterCreateStandardHANodesTemplateExcel()),
			newTranslateExcelIntf(newClusterNodeBatchAppendTemplateExcel()),
			newTranslateExcelIntf(newClusterUpgradeNodeTemplateExcel()),
		},
	}
}

type handler struct {
	excelIntfList excelIntfSlice
}

func (handler *handler) GetExcelTemplateFile(ctx context.Context, templateCode string) (string, *excelize.File, error) {
	excelIntf := handler.excelIntfList.findByCode(templateCode)
	if excelIntf == nil {
		return "", nil, errors.NewFromCode(errors.Var.TemplateCodeIsNotExist)
	}
	meta, err := excelIntf.ListExcelMeta(ctx)
	if err != nil {
		return "", nil, err
	}
	var fileName = meta.FileName
	var title = meta.Title
	var titleColor = meta.TitleColor
	var titleRows = meta.TitleRows
	var titleHeight = meta.TitleHeight
	var titleRowHeight = meta.TitleRowHeight

	f := excelize.NewFile()
	defer f.Close()
	// Create a new sheet.
	defaultSheetName := f.GetSheetName(0)

	// 设置title值
	if err := f.SetCellValue(defaultSheetName, "A1", title); err != nil {
		return "", nil, err
	}
	// 处理title 颜色为红色 与 垂直居中
	style, err := f.NewStyle(&excelize.Style{Font: &excelize.Font{Color: titleColor, Family: "宋体"}, Alignment: &excelize.Alignment{Vertical: "center", WrapText: true}})
	if err != nil {
		return "", nil, err
	}
	if err := f.SetCellStyle(defaultSheetName, "A1", "A1", style); err != nil {
		return "", nil, err
	}
	// 处理title 行高
	if err := f.SetRowHeight(defaultSheetName, 1, titleHeight); err != nil {
		return "", nil, err
	}
	// 需要合并单元格时合并单元格
	if len(titleRows) > 1 {
		mergeNumber, err := getExcelMergeRowCellName("A", len(titleRows)-1)
		if err != nil {
			return "", nil, err
		}
		if err := f.MergeCell(defaultSheetName, "A1", mergeNumber+"1"); err != nil {
			return "", nil, err
		}
	}

	rowTitleStyle, err := f.NewStyle(&excelize.Style{Fill: excelize.Fill{Type: "pattern", Color: []string{"#D0CECE"}, Pattern: 1}, Font: &excelize.Font{Family: "宋体"}, Alignment: &excelize.Alignment{Vertical: "center", WrapText: true}})
	if err != nil {
		return "", nil, err
	}

	// 处理表头
	for index, titleRow := range titleRows {
		cell, err := getExcelMergeRowCellName("A", index)
		if err != nil {
			return "", nil, err
		}
		if err := f.SetCellValue(defaultSheetName, cell+"2", titleRow.Name); err != nil {
			return "", nil, err
		}
		// 处理表头行高
		if err := f.SetRowHeight(defaultSheetName, 2, titleRowHeight); err != nil {
			return "", nil, err
		}
		// 设置列宽
		if err := f.SetColWidth(defaultSheetName, cell, cell, titleRow.Weight); err != nil {
			return "", nil, err
		}
		// 设置风格
		if titleRow.Style == nil {
			if err := f.SetCellStyle(defaultSheetName, cell+"2", cell+"2", rowTitleStyle); err != nil {
				return "", nil, err
			}
		} else {
			s, err := f.NewStyle(titleRow.Style)
			if err != nil {
				return "", nil, err
			}
			if err := f.SetCellStyle(defaultSheetName, cell+"2", cell+"2", s); err != nil {
				return "", nil, err
			}
		}
	}
	return fileName, f, nil
}
func (handler *handler) ExcelTemplateDownload(ctx context.Context, templateCode string) (*template.FileDownloadInfo, error) {
	fileName, f, err := handler.GetExcelTemplateFile(ctx, templateCode)
	if err != nil {
		return nil, err
	}
	// Save spreadsheet by the given path.
	buffer, err := f.WriteToBuffer()
	if err != nil {
		panic(err)
	}
	return &template.FileDownloadInfo{
		FileName: fileName,
		Buffer:   buffer,
	}, nil
}

func (handler *handler) ExcelTemplateUpdate(ctx context.Context, templateCode string, fileHeaders []*multipart.FileHeader) (interface{}, error) {
	// 将上传的excel文件中的用户填写的内容平铺开
	var fileNames []string
	var lines [][]string
	for _, fileHeader := range fileHeaders {
		ls, err := readExcelFileLines(fileHeader)
		if err != nil {
			return nil, err
		}
		if ls != nil {
			fileNames = append(fileNames, fileHeader.Filename)
			lines = append(lines, ls...)
		}
	}
	// 根据template 换取处理接口
	excelIntf := handler.excelIntfList.findByCode(templateCode)
	if excelIntf == nil {
		return nil, errors.NewFromCode(errors.Var.TemplateCodeIsNotExist)
	}
	return excelIntf.SugarExcelData(ctx, fileNames, lines)
}

// readFileLines
// 获取模版文件从第三行开始后的内容
func readExcelFileLines(fileHeader *multipart.FileHeader) ([][]string, error) {
	var lines [][]string
	f, err := fileHeader.Open()
	if err != nil {
		return nil, err
	}
	defer f.Close()
	reader := bufio.NewReader(f)
	excelF, err := excelize.OpenReader(reader)
	if err != nil {
		return nil, err
	}
	sheetList := excelF.GetSheetList()
	for _, sheet := range sheetList {
		sheetData, err := excelF.GetRows(sheet)
		if err != nil {
			return nil, err
		}
		lines = append(lines, sheetData[2:]...)
	}
	return lines, nil
}

var aToZBigger = []rune{'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'}
var aToZBiggerIndexMap = map[rune]int{}

func init() {
	for index, r := range aToZBigger {
		aToZBiggerIndexMap[r] = index
	}
}

// GetExcelMergeRowCellName
// 获取Excel 横向合并的最后的Row 名称
// demo 输入 A,10 则返回 J
// todo 目前只支持26 列 即A-Z相加 维续业务有需要在进行扩展
func getExcelMergeRowCellName(start string, addSize int) (string, error) {
	var result string
	// index 必须大于0
	if addSize < 0 {
		return result, errors.NewFromCode(errors.Var.ParamError)
	}
	if len(start) == 0 {
		start = string(aToZBigger[0])
	}
	// start 必修在aToZBiggerIndexMap中存在
	for _, r := range []rune(start) {
		if _, exist := aToZBiggerIndexMap[r]; !exist {
			return result, errors.NewFromCode(errors.Var.ParamError)
		}
	}
	// todo 目前只支持26 列 即A-Z相加 维续业务有需要在进行扩展
	result = string([]rune(start)[0] + rune(addSize))
	return result, nil
}
