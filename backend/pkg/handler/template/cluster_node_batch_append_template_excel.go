package template

import (
	"context"
	"strconv"
	"strings"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/template"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/network"
	"k8s.io/apimachinery/pkg/util/sets"
)

const (
	clusterNodeBatchAppendCodeHost             = "host"
	clusterNodeBatchAppendCodePort             = "port"
	clusterNodeBatchAppendCodeUsername         = "username"
	clusterNodeBatchAppendCodePassword         = "password"
	clusterNodeBatchAppendCodeSudoPassword     = "sudo_password"
	clusterNodeBatchAppendCodeSupportGPU       = "supportGPU"
	clusterNodeBatchAppendHostDiskAutoMount    = "host_disk_auto_mount"
	clusterNodeBatchAppendHostDockerMountPath  = "host_docker_mount_path"
	clusterNodeBatchAppendHostKubeletMountPath = "host_kubelet_mount_path"
)

type clusterNodeBatchAppendTemplateResponse struct {
	Filename    *string                             `json:"fileName"`
	Message     *string                             `json:"message"`
	NodeConfigs []node.NodeUpDownNodeConfigResponse `json:"nodeConfigs"`
}

type clusterNodeBatchAppendTemplateList []clusterNodeBatchAppendTemplate

func (arr clusterNodeBatchAppendTemplateList) Verify() error {
	if len(arr) == 0 {
		return errors.NewFromCode(errors.Var.DataEmpty)
	}
	// 单项值校验
	for _, node := range arr {
		if err := node.Verify(); err != nil {
			return err
		}
	}
	// 节点IP不允许重复
	nodeIps := sets.New[string]()
	for _, node := range arr {
		node := node
		if nodeIps.Has(node.Host) {
			return errors.NewFromCodeWithMessage(errors.Var.NodeIpRepeated, node.Host)
		}
		nodeIps.Insert(node.Host)
	}
	return nil
}
func (arr clusterNodeBatchAppendTemplateList) setDefault() {
	for i, _ := range arr {
		arr[i].setDefault()
	}
}

func (arr clusterNodeBatchAppendTemplateList) asResult() []node.NodeUpDownNodeConfigResponse {
	var result []node.NodeUpDownNodeConfigResponse
	for _, item := range arr {
		result = append(result, item.asResult())
	}
	return result
}

type clusterNodeBatchAppendTemplate struct {
	Host              string  `json:"host,omitempty"`
	Port              string  `json:"port,omitempty"`
	Username          string  `json:"username,omitempty"`
	Password          string  `json:"password,omitempty"`
	SudoPassword      *string `json:"sudo_password,omitempty"`
	IsGPU             string  `json:"supportGPU,omitempty"`
	HostDiskAutoMount string  `json:"host_disk_auto_mount,omitempty"`
	DockerPath        string  `json:"host_docker_mount_path,omitempty"`
	KubeletPah        string  `json:"host_kubelet_mount_path,omitempty"`
}

func (template clusterNodeBatchAppendTemplate) asResult() node.NodeUpDownNodeConfigResponse {
	var port int
	var supportGpu bool
	var authParam map[string]interface{}
	var err error
	if port, err = strconv.Atoi(template.Port); err != nil {
		port = 22
	}
	if "true" == strings.ToLower(template.IsGPU) {
		supportGpu = true
	}
	// 节点数据盘默认为手动挂载
	var nodeStorageType = cluster.NodeStorageTypeManual
	if "true" == strings.ToLower(template.HostDiskAutoMount) {
		nodeStorageType = cluster.NodeStorageTypeAuto
	}
	// 当且仅当 nodeStorageType = auto 时候，才需要填写相关参数
	var diskPath *cluster.NodeDiskPathResponse
	if nodeStorageType == cluster.NodeStorageTypeAuto {
		diskPath = new(cluster.NodeDiskPathResponse)
		diskPath.Kubelet = template.KubeletPah
		diskPath.Docker = template.DockerPath
	}

	var authStruct = cluster.NodeAuthUsernameAndPasswordParamRequest{
		Username:              template.Username,
		Password:              template.Password,
		AuthorizationPassword: template.SudoPassword,
	}
	_ = utils.BeanCopy(authStruct, &authParam)
	return node.NodeUpDownNodeConfigResponse{
		Ip:   template.Host,
		Port: port,
		Auth: &cluster.NodeAuthResponse{
			AuthType: cluster.UserNameAndPasswordAuthType,
			Param:    authParam,
		},
		SupportGpu: &supportGpu,
		NodeStorageResponse: &cluster.NodeStorageResponse{
			Type:     nodeStorageType,
			DiskPath: diskPath,
		},
	}
}

func (temp clusterNodeBatchAppendTemplate) Verify() error {

	if !network.IsIPv4IPv6(temp.Host) {
		return errors.NewFromCodeWithMessage(errors.Var.NodeIpIllegal, temp.Host)
	}
	if temp.Port != "" {
		if err := portVerifyIgnoreEmpty(temp.Port); err != nil {
			return err
		}
	}
	// 用户名校验
	if temp.Username == "" {
		return errors.NewFromCode(errors.Var.NodeUsernameNotEmpty)
	}

	// 密码校验
	if temp.Password == "" {
		return errors.NewFromCode(errors.Var.NodePasswordNotEmpty)
	}

	// 提权密码无需校验

	// IsGPU 校验
	if err := isGpuVerifyIgnoreEmpty(temp.IsGPU); err != nil {
		return err
	}

	// 自动挂载手动挂载与diskPath校验
	if err := nodeDiskVerify(temp.Host, temp.HostDiskAutoMount, temp.DockerPath, temp.KubeletPah); err != nil {
		return err
	}

	return nil

}

func (item *clusterNodeBatchAppendTemplate) setDefault() {
	if item.Port == "" {
		item.Port = "22"
	}

	if item.IsGPU == "" {
		item.IsGPU = "false"
	}
	item.IsGPU = strings.ToLower(item.IsGPU)

	if item.HostDiskAutoMount == "" {
		item.HostDiskAutoMount = "false"
	}
	item.HostDiskAutoMount = strings.ToLower(item.HostDiskAutoMount)
}

func newClusterNodeBatchAppendTemplateExcel() excelIntf {
	return &clusterNodeBatchAppendTemplateExcel{
		templateCode: template.ExcelTemplateCodeClusterNodeBatchAppend,
		fileName:     template.ExcelFinaNameClusterNodeBatchAppend,
		title:        template.ExcelTitleClusterNodeBatchAppend,
	}
}

type clusterNodeBatchAppendTemplateExcel struct {
	templateCode template.ExcelTemplateCode
	fileName     string
	title        string
}

// ListExcelMeta
// 获取excel 的元数据信息
func (excel *clusterNodeBatchAppendTemplateExcel) ListExcelMeta(ctx context.Context) (*template.ExcelMeta, error) {
	result := template.ExcelMeta{
		Code:           excel.getTemplateCode(),
		FileName:       excel.fileName,
		Title:          excel.title,
		TitleColor:     "#FF0000",
		TitleHeight:    55,
		TitleRowHeight: 70,
		TitleRows: []template.ExcelTitleRow{
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterNodeBatchAppendCodeHost,
				Name:      "节点IP",
				Weight:    39.04,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterNodeBatchAppendCodePort,
				Name:      "SSH端口（若为空，则使用默认值22）",
				Weight:    33.76,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterNodeBatchAppendCodeUsername,
				Name:      "登录用户名",
				Weight:    14,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterNodeBatchAppendCodePassword,
				Name:      "登录密码",
				Weight:    14,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterNodeBatchAppendCodeSudoPassword,
				Name:      "提权密码（若使用非root账号登录，请输入）",
				Weight:    30.07,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterNodeBatchAppendCodeSupportGPU,
				Name:      "是否为GPU节点（True/False，若为空，则使用默认值False）",
				Weight:    32.47,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterNodeBatchAppendHostDiskAutoMount,
				Name:      "节点数据盘自动挂载（True/False，若为空，则使用默认值False）",
				Weight:    32.47,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterNodeBatchAppendHostDockerMountPath,
				Name:      "Containerd数据盘（所有节点均需要填写）",
				Weight:    32.47,
				Style:     &excelYellowStyle,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterNodeBatchAppendHostKubeletMountPath,
				Name:      "Kubelet数据盘（所有节点均需要填写）",
				Weight:    32.47,
				Style:     &excelYellowStyle,
			},
		},
	}
	return &result, nil
}

// SugarExcelData
// 解析excel中的文件 以生成前端期望的接口
func (excel *clusterNodeBatchAppendTemplateExcel) SugarExcelData(ctx context.Context, fileName []string, lines [][]string) (interface{}, error) {
	if err := excel.VerifyExcelData(ctx, lines); err != nil {
		switch err.(type) {
		case errors.Error:
			transErr := errors.NewFromError(ctx, err)
			return &clusterNodeBatchAppendTemplateResponse{
				Message: &transErr.Message,
			}, nil

		default:
			return nil, err
		}
	}
	templates, err := getClusterNodeBatchAppendTemplates(ctx, excel, lines)
	if err != nil {
		return nil, err
	}
	templates.setDefault()

	return &clusterNodeBatchAppendTemplateResponse{
		Filename:    &fileName[0],
		NodeConfigs: templates.asResult(),
	}, nil
}

// VerifyExcelData
// 校验Excel中的文件
func (excel *clusterNodeBatchAppendTemplateExcel) VerifyExcelData(ctx context.Context, lines [][]string) error {
	// 基础校验
	templates, err := getClusterNodeBatchAppendTemplates(ctx, excel, lines)
	if err != nil {
		return err
	}
	var templateList clusterNodeBatchAppendTemplateList
	templateList = templates
	if err := templateList.Verify(); err != nil {
		return err
	}
	return nil
}

// getTemplateCode
// 获取excel 的code信息
func (excel *clusterNodeBatchAppendTemplateExcel) getTemplateCode() template.ExcelTemplateCode {
	return excel.templateCode
}

// getExcelDataMap
// 将解析excel 后传入的数据格式转化为clusterCreateAllInOneTemplate结构体列表
func getClusterNodeBatchAppendTemplates(ctx context.Context, excel excelIntf, lines [][]string) (clusterNodeBatchAppendTemplateList, error) {
	maps, err := getExcelDataMapList(ctx, excel, lines)
	if err != nil {
		return nil, err
	}
	var result []clusterNodeBatchAppendTemplate
	if err := utils.BeanCopy(maps, &result); err != nil {
		return nil, err
	}
	return result, nil
}
