package template

import (
	"context"
	"strconv"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/template"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/network"
	"k8s.io/apimachinery/pkg/util/sets"
)

const (
	clusterUpgradeNodeName             = "nodeName"
	clusterUpgradeNodeIp               = "ip"
	clusterUpgradeNodeCodePort         = "port"
	clusterUpgradeNodeCodeUsername     = "username"
	clusterUpgradeNodeCodePassword     = "password"
	clusterUpgradeNodeCodeSudoPassword = "sudo_password"
)

type clusterUpgradeNodeTemplateExcel struct {
	templateCode template.ExcelTemplateCode
	fileName     string
	title        string
}

func newClusterUpgradeNodeTemplateExcel() excelIntf {
	return &clusterUpgradeNodeTemplateExcel{
		templateCode: template.ExcelTemplateCodeClusterUpgradeNode,
		fileName:     template.ExcelFinaNameClusterUpgradeNode,
		title:        template.ExcelTitleClusterUpgradeNode,
	}
}

func (excel *clusterUpgradeNodeTemplateExcel) ListExcelMeta(ctx context.Context) (*template.ExcelMeta, error) {
	result := template.ExcelMeta{
		Code:           excel.getTemplateCode(),
		FileName:       excel.fileName,
		Title:          excel.title,
		TitleColor:     "#FF0000",
		TitleHeight:    55,
		TitleRowHeight: 70,
		TitleRows: []template.ExcelTitleRow{
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterUpgradeNodeName,
				Name:      "节点名称",
				Weight:    40,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterUpgradeNodeIp,
				Name:      "节点IP",
				Weight:    20,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterUpgradeNodeCodePort,
				Name:      "SSH端口(若为空，则使用默认值22)",
				Weight:    32,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterUpgradeNodeCodeUsername,
				Name:      "登录用户名",
				Weight:    15,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterUpgradeNodeCodePassword,
				Name:      "登录密码",
				Weight:    20,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterUpgradeNodeCodeSudoPassword,
				Name:      "提权密码(若使用非root账号登录，请输入)",
				Weight:    55,
			},
		},
	}
	return &result, nil
}

func (excel *clusterUpgradeNodeTemplateExcel) SugarExcelData(ctx context.Context, fileName []string, lines [][]string) (interface{}, error) {
	if err := excel.VerifyExcelData(ctx, lines); err != nil {
		return nil, err
	}
	nodeTemplates, err := getClusterUpgradeNodeTemplates(ctx, excel, lines)
	if err != nil {
		return nil, err
	}
	var results = make([]*cluster.UpgradeClusterNodeInfo, 0, len(nodeTemplates))
	for _, nodeInfo := range nodeTemplates {
		results = append(results, nodeInfo.asResult())
	}
	return results, nil
}

func (excel *clusterUpgradeNodeTemplateExcel) VerifyExcelData(ctx context.Context, lines [][]string) error {
	nodeTemplates, err := getClusterUpgradeNodeTemplates(ctx, excel, lines)
	if err != nil {
		return err
	}
	var nodeTemplateList clusterUpgradeTemplateList
	nodeTemplateList = nodeTemplates
	return nodeTemplateList.Verify(ctx)
}

func (excel *clusterUpgradeNodeTemplateExcel) getTemplateCode() template.ExcelTemplateCode {
	return excel.templateCode
}

func getClusterUpgradeNodeTemplates(ctx context.Context, excel excelIntf, lines [][]string) ([]clusterUpgradeNodeTemplate, error) {
	maps, err := getExcelDataMapList(ctx, excel, lines)
	if err != nil {
		return nil, err
	}
	var result []clusterUpgradeNodeTemplate
	if err := utils.BeanCopy(maps, &result); err != nil {
		return nil, err
	}
	return result, nil
}

type clusterUpgradeTemplateList []clusterUpgradeNodeTemplate

type clusterUpgradeNodeTemplate struct {
	NodeName     string  `json:"nodeName,omitempty"`
	IP           string  `json:"ip,omitempty"`
	Port         string  `json:"port,omitempty"`
	Username     string  `json:"username,omitempty"`
	Password     string  `json:"password,omitempty"`
	SudoPassword *string `json:"sudo_password,omitempty"`
}

func (arr clusterUpgradeTemplateList) Verify(ctx context.Context) error {
	if len(arr) == 0 {
		return errors.NewFromCode(errors.Var.DataEmpty)
	}
	// 节点名称IP不允许重复
	nodeIps := sets.New[string]()
	nodeNames := sets.New[string]()
	for _, node := range arr {
		if nodeNames.Has(node.NodeName) {
			return errors.NewFromCodeWithMessage(errors.Var.NodeNameRepeated, node.NodeName)
		}
		nodeNames.Insert(node.NodeName)
		if nodeIps.Has(node.IP) {
			return errors.NewFromCodeWithMessage(errors.Var.NodeIpRepeated, node.IP)
		}
		nodeIps.Insert(node.IP)
	}
	// 单项值校验
	for _, node := range arr {
		if err := node.Verify(ctx); err != nil {
			return err
		}
	}
	// 找到arr中是否缺少nodeMap中的节点
	nodeMapObj := ctx.Value(constants.ContextKeyClusterUpgradeNodes)
	if nodeMapObj != nil {
		nodeMap := nodeMapObj.(map[string]cluster.NodeInfo)
		for nodeName := range nodeMap {
			if !nodeNames.Has(nodeName) {
				return errors.NewFromCodeWithMessage(errors.Var.UploadNodeLack, nodeName)
			}
		}
	}
	return nil
}
func (template clusterUpgradeNodeTemplate) asResult() *cluster.UpgradeClusterNodeInfo {
	var port int
	var authParam map[string]interface{}
	var err error
	if port, err = strconv.Atoi(template.Port); err != nil {
		port = 22
	}
	var authStruct = cluster.NodeAuthUsernameAndPasswordParamRequest{
		Username:              template.Username,
		Password:              template.Password,
		AuthorizationPassword: template.SudoPassword,
	}
	_ = utils.BeanCopy(authStruct, &authParam)
	return &cluster.UpgradeClusterNodeInfo{
		NodeName: template.NodeName,
		Ip:       template.IP,
		Port:     port,
		Auth: &cluster.NodeAuthRequest{
			AuthType: cluster.UserNameAndPasswordAuthType,
			Param:    authParam,
		},
	}
}

func (template clusterUpgradeNodeTemplate) Verify(ctx context.Context) error {
	// 节点名称校验
	if template.NodeName == "" {
		return errors.NewFromCode(errors.Var.NodeNameNotEmpty)
	}
	// IP 校验
	if !network.IsIPv4IPv6(template.IP) {
		return errors.NewFromCodeWithMessage(errors.Var.NodeIpIllegal, template.IP)
	}

	// 端口校验
	if template.Port != "" {
		if err := portVerifyIgnoreEmpty(template.Port); err != nil {
			return err
		}
	}

	// 用户名校验
	if template.Username == "" {
		return errors.NewFromCode(errors.Var.NodeUsernameNotEmpty)
	}

	// 密码校验
	if template.Password == "" {
		return errors.NewFromCode(errors.Var.NodePasswordNotEmpty)
	}

	// sudo密码无需校验
	// 校验node是否在升级列表中
	// 节点名称和ip不能修改
	nodeMapObj := ctx.Value(constants.ContextKeyClusterUpgradeNodes)
	if nodeMapObj != nil {
		nodeMap := nodeMapObj.(map[string]cluster.NodeInfo)
		if nodeItem, ok := nodeMap[template.NodeName]; !ok {
			return errors.NewFromCode(errors.Var.NodeNotInUpgradeMap)
		} else {
			if nodeItem.Ip != template.IP {
				return errors.NewFromCodeWithMessage(errors.Var.NodeIpNotMatch, template.NodeName)
			}
		}
	} else {
		return errors.NewFromCode(errors.Var.NodeNotInUpgradeMap)
	}
	return nil
}
