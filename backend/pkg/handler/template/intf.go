package template

import (
	"context"
	"mime/multipart"

	"github.com/xuri/excelize/v2"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/template"
)

type Handler interface {
	GetExcelTemplateFile(ctx context.Context, templateCode string) (string, *excelize.File, error)
	ExcelTemplateDownload(ctx context.Context, templateCode string) (*template.FileDownloadInfo, error)
	ExcelTemplateUpdate(ctx context.Context, templateCode string, fileHeaders []*multipart.FileHeader) (interface{}, error)
}

type excelIntf interface {
	// ListExcelMeta
	// 获取excel 的元数据信息
	ListExcelMeta(ctx context.Context) (*template.ExcelMeta, error)

	// SugarExcelData
	// 解析excel中的文件 以生成前端期望的接口
	SugarExcelData(ctx context.Context, fileName []string, lines [][]string) (interface{}, error)

	// VerifyExcelData
	// 校验Excel中的文件
	VerifyExcelData(ctx context.Context, lines [][]string) error

	// getTemplateCode
	// 获取excel 的code信息
	getTemplateCode() template.ExcelTemplateCode
}
