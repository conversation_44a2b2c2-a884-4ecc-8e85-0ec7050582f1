package template

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	"github.com/xuri/excelize/v2"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/template"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/network"
	database_aop "harmonycloud.cn/unifiedportal/translate-sdk-golang/database-aop"
	"k8s.io/apimachinery/pkg/util/sets"
)

var excelYellowStyle = excelize.Style{Fill: excelize.Fill{Type: "pattern", Color: []string{"#FBF0D0"}, Pattern: 1}, Font: &excelize.Font{Family: "宋体"}, Alignment: &excelize.Alignment{Vertical: "center", WrapText: true}}

const (
	clusterCreateNodeCodeIp           = "ip"
	clusterCreateNodeCodePort         = "port"
	clusterCreateNodeCodeUsername     = "username"
	clusterCreateNodeCodePassword     = "password"
	clusterCreateNodeCodeSudoPassword = "sudo_password"
	clusterCreateNodeCodeIsGpu        = "is_gpu"
	clusterCreateHostDiskAutoMount    = "host_disk_auto_mount"
	clusterCreateHostEtcdMountPath    = "host_etcd_mount_path"
	clusterCreateHostSystemMountPath  = "host_system_mount_path"
	clusterCreateHostDockerMountPath  = "host_docker_mount_path"
	clusterCreateHostKubeletMountPath = "host_kubelet_mount_path"
)

type clusterCreateTemplateList []clusterCreateNodeTemplate

func (arr clusterCreateTemplateList) Verify() error {
	// 单项值校验
	for _, node := range arr {
		if err := node.Verify(); err != nil {
			return err
		}
	}
	// 节点IP不允许重复
	nodeIps := sets.New[string]()
	for _, node := range arr {
		node := node
		if nodeIps.Has(node.IP) {
			return errors.NewFromCodeWithMessage(errors.Var.NodeIpRepeated, node.IP)
		}
		nodeIps.Insert(node.IP)
	}
	return nil
}

type clusterCreateNodeTemplate struct {
	IP                string  `json:"ip,omitempty"`
	Port              string  `json:"port,omitempty"`
	Username          string  `json:"username,omitempty"`
	Password          string  `json:"password,omitempty"`
	SudoPassword      *string `json:"sudo_password,omitempty"`
	IsGPU             string  `json:"is_gpu,omitempty"`
	HostDiskAutoMount string  `json:"host_disk_auto_mount,omitempty"`
	ETCDPath          string  `json:"host_etcd_mount_path,omitempty"`
	DockerPath        string  `json:"host_docker_mount_path,omitempty"`
	SystemPath        string  `json:"host_system_mount_path,omitempty"`
	KubeletPah        string  `json:"host_kubelet_mount_path,omitempty"`
}

func (template clusterCreateNodeTemplate) Verify() error {
	// IP 校验
	if !network.IsIPv4IPv6(template.IP) {
		return errors.NewFromCodeWithMessage(errors.Var.NodeIpIllegal, template.IP)
	}

	// 端口校验
	if template.Port != "" {
		if err := portVerifyIgnoreEmpty(template.Port); err != nil {
			return err
		}
	}

	// 用户名校验
	if template.Username == "" {
		return errors.NewFromCode(errors.Var.NodeUsernameNotEmpty)
	}

	// 密码校验
	if template.Password == "" {
		return errors.NewFromCode(errors.Var.NodePasswordNotEmpty)
	}

	// sudo密码无需校验

	// IsGPU 校验
	if err := isGpuVerifyIgnoreEmpty(template.IsGPU); err != nil {
		return err
	}

	if err := nodeDiskVerify(template.IP, template.HostDiskAutoMount, template.DockerPath, template.KubeletPah); err != nil {
		return err
	}
	return nil
}

func (template clusterCreateNodeTemplate) asResult() *cluster.CreateNodeConfigResponse {
	var port int
	var supportGpu bool
	var authParam map[string]interface{}
	var err error
	if port, err = strconv.Atoi(template.Port); err != nil {
		port = 22
	}
	if "true" == strings.ToLower(template.IsGPU) {
		supportGpu = true
	}
	// 节点数据盘默认为手动挂载
	var nodeStorageType = cluster.NodeStorageTypeManual
	if "true" == strings.ToLower(template.HostDiskAutoMount) {
		nodeStorageType = cluster.NodeStorageTypeAuto
	}
	// 当且仅当 nodeStorageType = auto 时候，才需要填写相关参数
	var diskPath *cluster.NodeDiskPathResponse
	if nodeStorageType == cluster.NodeStorageTypeAuto {
		diskPath = new(cluster.NodeDiskPathResponse)
		diskPath.ETCD = template.ETCDPath
		diskPath.System = template.SystemPath
		diskPath.Kubelet = template.KubeletPah
		diskPath.Docker = template.DockerPath
	}

	var authStruct = cluster.NodeAuthUsernameAndPasswordParamRequest{
		Username:              template.Username,
		Password:              template.Password,
		AuthorizationPassword: template.SudoPassword,
	}
	_ = utils.BeanCopy(authStruct, &authParam)
	return &cluster.CreateNodeConfigResponse{
		Ip:         template.IP,
		Port:       port,
		SupportGpu: supportGpu,
		Auth: &cluster.NodeAuthResponse{
			AuthType: cluster.UserNameAndPasswordAuthType,
			Param:    authParam,
		},
		Storage: cluster.NodeStorageResponse{
			Type:     nodeStorageType,
			DiskPath: diskPath,
		},
	}
}

func newTranslateExcelIntf(intf excelIntf) excelIntf {
	return &translateExcelIntf{
		intf: intf,
	}
}

type translateExcelIntf struct {
	intf excelIntf
}

// ListExcelMeta
// 获取excel 的元数据信息
func (intf translateExcelIntf) ListExcelMeta(ctx context.Context) (*template.ExcelMeta, error) {
	meta, err := intf.intf.ListExcelMeta(ctx)
	database_aop.DoTranslate(ctx, meta, err)
	return meta, err
}

// SugarExcelData
// 解析excel中的文件 以生成前端期望的接口
func (intf translateExcelIntf) SugarExcelData(ctx context.Context, fileName []string, lines [][]string) (interface{}, error) {
	return intf.intf.SugarExcelData(ctx, fileName, lines)
}

// VerifyExcelData
// 校验Excel中的文件
func (intf translateExcelIntf) VerifyExcelData(ctx context.Context, lines [][]string) error {
	return intf.intf.VerifyExcelData(ctx, lines)
}

// getTemplateCode
// 获取excel 的code信息
func (intf translateExcelIntf) getTemplateCode() template.ExcelTemplateCode {
	return intf.intf.getTemplateCode()
}

func newClusterCreateAllInOneNodesTemplateExcel() excelIntf {
	return &clusterCreateAllInOneNodesTemplateExcel{
		templateCode: template.ExcelTemplateCodeClusterCreateNodesAllInOne,
		fileName:     template.ExcelFinaNameClusterCreateNodesAllInOne,
		title:        template.ExcelTitleClusterCreateNodesAllInOne,
	}
}

// clusterCreateAllInOneNodesTemplateExcel
// 集群创建节点模板excel - AllInOne
type clusterCreateAllInOneNodesTemplateExcel struct {
	templateCode template.ExcelTemplateCode
	fileName     string
	title        string
}

// ListExcelMeta
// 获取excel 的元数据信息
func (excel *clusterCreateAllInOneNodesTemplateExcel) ListExcelMeta(ctx context.Context) (*template.ExcelMeta, error) {
	result := template.ExcelMeta{
		Code:           excel.getTemplateCode(),
		FileName:       excel.fileName,
		Title:          excel.title,
		TitleColor:     "#FF0000",
		TitleHeight:    75,
		TitleRowHeight: 70,
		TitleRows: []template.ExcelTitleRow{
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateNodeCodeIp,
				Name:      "节点IP（All-in-One模式下，第1个IP将作为Master及系统节点，第2个及以后IP将作为普通Worker节点）",
				Weight:    39.04,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateNodeCodePort,
				Name:      "SSH端口（若为空，则使用默认值22）",
				Weight:    33.76,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateNodeCodeUsername,
				Name:      "登录用户名",
				Weight:    14,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateNodeCodePassword,
				Name:      "登录密码",
				Weight:    14,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateNodeCodeSudoPassword,
				Name:      "提权密码（若使用非root账号登录，请输入）",
				Weight:    30.07,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateNodeCodeIsGpu,
				Name:      "是否为GPU节点（True/False，若为空，则使用默认值False）",
				Weight:    32.47,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateHostDiskAutoMount,
				Name:      "节点数据盘自动挂载（True/False，若为空，则使用默认值False）",
				Weight:    32.47,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateHostEtcdMountPath,
				Name:      "ETCD数据盘（Master节点[第1台]必须填写，其他节点无需填写）",
				Weight:    32.47,
				Style:     &excelYellowStyle,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateHostSystemMountPath,
				Name:      "系统数据盘（系统节点[第1台]必须填写，其他节点无需填写）",
				Weight:    32.47,
				Style:     &excelYellowStyle,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateHostDockerMountPath,
				Name:      "Containerd数据盘（所有节点均需要填写）",
				Weight:    32.47,
				Style:     &excelYellowStyle,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateHostKubeletMountPath,
				Name:      "Kubelet数据盘（所有节点均需要填写）",
				Weight:    32.47,
				Style:     &excelYellowStyle,
			},
		},
	}
	return &result, nil
}

// SugarExcelData
// 解析excel中的文件 以生成前端期望的接口
func (excel *clusterCreateAllInOneNodesTemplateExcel) SugarExcelData(ctx context.Context, fileName []string, lines [][]string) (interface{}, error) {
	if err := excel.VerifyExcelData(ctx, lines); err != nil {
		return nil, err
	}
	nodeTemplates, err := getClusterCreateNodeTemplates(ctx, excel, lines)
	if err != nil {
		return nil, err
	}
	var results = make([]*cluster.CreateNodeConfigResponse, 0, len(nodeTemplates))
	for _, template := range nodeTemplates {
		results = append(results, template.asResult())
	}
	// 节点IP（All-in-One模式下，第1个IP将作为Master及系统节点）
	masters := results[:1]
	systemNodes := results[:1]
	defaults := results[:1]
	workers := results[1:]
	setNodeResultValue(masters, systemNodes, defaults, workers)
	// 对result 的 master、系统节点以及工作节点
	// Master 节点需要填写 ETCD + Docker + Kubelet
	// System 节点需要填写 System + Docker + Kubelet
	// 其他    节点需要填写 Docker + Kubelet
	if err := verifyNodeStorageMount(masters, systemNodes); err != nil {
		return nil, err
	}

	return results, nil
}

// VerifyExcelData
// 校验Excel中的文件
func (excel *clusterCreateAllInOneNodesTemplateExcel) VerifyExcelData(ctx context.Context, lines [][]string) error {
	// 基础校验
	nodeTemplates, err := getClusterCreateNodeTemplates(ctx, excel, lines)
	if err != nil {
		return err
	}
	var nodeTemplateList clusterCreateTemplateList
	nodeTemplateList = nodeTemplates
	if err := nodeTemplateList.Verify(); err != nil {
		return err
	}
	// 额外校验
	if len(nodeTemplateList) < 1 {
		return errors.NewFromCode(errors.Var.AllInOneNodeNumError)
	}
	return nil
}

// getTemplateCode
// 获取excel 的code信息
func (excel *clusterCreateAllInOneNodesTemplateExcel) getTemplateCode() template.ExcelTemplateCode {
	return excel.templateCode
}

func newClusterCreateMinimizeHANodesTemplateExcel() excelIntf {
	return &clusterCreateMinimizeHANodesTemplateExcel{
		templateCode: template.ExcelTemplateCodeClusterCreateNodesMinimizeHA,
		fileName:     template.ExcelFinaNameClusterCreateNodesMinimizeHA,
		title:        template.ExcelTitleClusterCreateNodesMinimizeHA,
	}
}

// clusterCreateMinimizeHANodesTemplateExcel
// 集群创建节点模板excel - MinimizeHA
type clusterCreateMinimizeHANodesTemplateExcel struct {
	templateCode template.ExcelTemplateCode
	fileName     string
	title        string
}

// ListExcelMeta
// 获取excel 的元数据信息
func (excel *clusterCreateMinimizeHANodesTemplateExcel) ListExcelMeta(ctx context.Context) (*template.ExcelMeta, error) {
	result := template.ExcelMeta{
		Code:           excel.getTemplateCode(),
		FileName:       excel.fileName,
		Title:          excel.title,
		TitleColor:     "#FF0000",
		TitleHeight:    75,
		TitleRowHeight: 70,
		TitleRows: []template.ExcelTitleRow{
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateNodeCodeIp,
				Name:      "节点IP（最小化高可用模式下，前3个IP将作为Master及系统节点，第4个及以后IP将作为普通Worker节点）",
				Weight:    39.04,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateNodeCodePort,
				Name:      "SSH端口（若为空，则使用默认值22）",
				Weight:    33.76,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateNodeCodeUsername,
				Name:      "登录用户名",
				Weight:    14,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateNodeCodePassword,
				Name:      "登录密码",
				Weight:    14,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateNodeCodeSudoPassword,
				Name:      "提权密码（若使用非root账号登录，请输入）",
				Weight:    30.07,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateNodeCodeIsGpu,
				Name:      "是否为GPU节点（True/False，若为空，则使用默认值False）",
				Weight:    32.47,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateHostDiskAutoMount,
				Name:      "节点数据盘自动挂载（True/False，若为空，则使用默认值False）",
				Weight:    32.47,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateHostEtcdMountPath,
				Name:      "ETCD数据盘（Master节点[第1,2,3台]必须填写，其他节点无需填写）",
				Weight:    32.47,
				Style:     &excelYellowStyle,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateHostSystemMountPath,
				Name:      "系统数据盘（系统节点[第1,2,3台]必须填写，其他节点无需填写）",
				Weight:    32.47,
				Style:     &excelYellowStyle,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateHostDockerMountPath,
				Name:      "Containerd数据盘（所有节点均需要填写）",
				Weight:    32.47,
				Style:     &excelYellowStyle,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateHostKubeletMountPath,
				Name:      "Kubelet数据盘（所有节点均需要填写）",
				Weight:    32.47,
				Style:     &excelYellowStyle,
			},
		},
	}
	return &result, nil
}

// SugarExcelData
// 解析excel中的文件 以生成前端期望的接口
func (excel *clusterCreateMinimizeHANodesTemplateExcel) SugarExcelData(ctx context.Context, fileName []string, lines [][]string) (interface{}, error) {
	if err := excel.VerifyExcelData(ctx, lines); err != nil {
		return nil, err
	}
	nodeTemplates, err := getClusterCreateNodeTemplates(ctx, excel, lines)
	if err != nil {
		return nil, err
	}
	var results = make([]*cluster.CreateNodeConfigResponse, 0, len(nodeTemplates))
	for _, template := range nodeTemplates {
		results = append(results, template.asResult())
	}
	// 节点IP（最小化高可用模式下，前3个IP将作为Master及系统节点）
	masters := results[:3]
	systemNodes := results[:3]
	defaults := results[:3]
	workers := results[3:]
	setNodeResultValue(masters, systemNodes, defaults, workers)

	// 对result 的 master、系统节点以及工作节点
	// Master 节点需要填写 ETCD + Docker + Kubelet
	// System 节点需要填写 System + Docker + Kubelet
	// 其他    节点需要填写 Docker + Kubelet
	if err := verifyNodeStorageMount(masters, systemNodes); err != nil {
		return nil, err
	}

	return results, nil
}

// VerifyExcelData
// 校验Excel中的文件
func (excel *clusterCreateMinimizeHANodesTemplateExcel) VerifyExcelData(ctx context.Context, lines [][]string) error {
	// 基础校验
	nodeTemplates, err := getClusterCreateNodeTemplates(ctx, excel, lines)
	if err != nil {
		return err
	}
	var nodeTemplateList clusterCreateTemplateList
	nodeTemplateList = nodeTemplates
	if err := nodeTemplateList.Verify(); err != nil {
		return err
	}
	// 额外校验
	if len(nodeTemplateList) < 3 {
		return errors.NewFromCode(errors.Var.MinimizeHANodeNumError)
	}
	return nil
}

// getTemplateCode
// 获取excel 的code信息
func (excel *clusterCreateMinimizeHANodesTemplateExcel) getTemplateCode() template.ExcelTemplateCode {
	return excel.templateCode
}

func newClusterCreateStandardNoneHANodesTemplateExcel() excelIntf {
	return &clusterCreateStandardNoneHANodesTemplateExcel{
		templateCode: template.ExcelTemplateCodeClusterCreateNodesStandardNoneHA,
		fileName:     template.ExcelFinaNameClusterCreateNodesStandardNoneHA,
		title:        template.ExcelTitleClusterCreateNodesStandardNoneHA,
	}
}

// clusterCreateStandardNoneHANodesTemplateExcel
// 集群创建节点模板excel - StandardNoneHA
type clusterCreateStandardNoneHANodesTemplateExcel struct {
	templateCode template.ExcelTemplateCode
	fileName     string
	title        string
}

// ListExcelMeta
// 获取excel 的元数据信息
func (excel *clusterCreateStandardNoneHANodesTemplateExcel) ListExcelMeta(ctx context.Context) (*template.ExcelMeta, error) {
	result := template.ExcelMeta{
		Code:           excel.getTemplateCode(),
		FileName:       excel.fileName,
		Title:          excel.title,
		TitleColor:     "#FF0000",
		TitleHeight:    75,
		TitleRowHeight: 70,
		TitleRows: []template.ExcelTitleRow{
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateNodeCodeIp,
				Name:      "节点IP（标准非高可用模式下，第1个IP将作为Master节点，第2-4个IP将作为Worker及系统节点，第5个及以后IP将作为普通Worker节点）",
				Weight:    39.04,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateNodeCodePort,
				Name:      "SSH端口（若为空，则使用默认值22）",
				Weight:    33.76,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateNodeCodeUsername,
				Name:      "登录用户名",
				Weight:    14,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateNodeCodePassword,
				Name:      "登录密码",
				Weight:    14,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateNodeCodeSudoPassword,
				Name:      "提权密码（若使用非root账号登录，请输入）",
				Weight:    30.07,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateNodeCodeIsGpu,
				Name:      "是否为GPU节点（True/False，若为空，则使用默认值False）",
				Weight:    32.47,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateHostDiskAutoMount,
				Name:      "节点数据盘自动挂载（True/False，若为空，则使用默认值False）",
				Weight:    32.47,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateHostEtcdMountPath,
				Name:      "ETCD数据盘（Master节点[第1台]必须填写，其他节点无需填写）",
				Weight:    32.47,
				Style:     &excelYellowStyle,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateHostSystemMountPath,
				Name:      "系统数据盘（系统节点[第2,3,4台]必须填写，其他节点无需填写）",
				Weight:    32.47,
				Style:     &excelYellowStyle,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateHostDockerMountPath,
				Name:      "Containerd数据盘（所有节点均需要填写）",
				Weight:    32.47,
				Style:     &excelYellowStyle,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateHostKubeletMountPath,
				Name:      "Kubelet数据盘（所有节点均需要填写）",
				Weight:    32.47,
				Style:     &excelYellowStyle,
			},
		},
	}
	return &result, nil
}

// SugarExcelData
// 解析excel中的文件 以生成前端期望的接口
func (excel *clusterCreateStandardNoneHANodesTemplateExcel) SugarExcelData(ctx context.Context, fileName []string, lines [][]string) (interface{}, error) {
	if err := excel.VerifyExcelData(ctx, lines); err != nil {
		return nil, err
	}
	nodeTemplates, err := getClusterCreateNodeTemplates(ctx, excel, lines)
	if err != nil {
		return nil, err
	}
	var results = make([]*cluster.CreateNodeConfigResponse, 0, len(nodeTemplates))
	for _, template := range nodeTemplates {
		results = append(results, template.asResult())
	}
	// 节点IP（标准非高可用模式下，第1个IP将作为Master节点，第2-4个IP将作为Worker及系统节点）
	masters := results[:1]
	systemNodes := results[1:4]
	defaults := results[:4]
	workers := results[1:]
	setNodeResultValue(masters, systemNodes, defaults, workers)

	// 对result 的 master、系统节点以及工作节点
	// Master 节点需要填写 ETCD + Docker + Kubelet
	// System 节点需要填写 System + Docker + Kubelet
	// 其他    节点需要填写 Docker + Kubelet
	if err := verifyNodeStorageMount(masters, systemNodes); err != nil {
		return nil, err
	}
	return results, nil
}

// VerifyExcelData
// 校验Excel中的文件
func (excel *clusterCreateStandardNoneHANodesTemplateExcel) VerifyExcelData(ctx context.Context, lines [][]string) error {
	// 基础校验
	nodeTemplates, err := getClusterCreateNodeTemplates(ctx, excel, lines)
	if err != nil {
		return err
	}
	var nodeTemplateList clusterCreateTemplateList
	nodeTemplateList = nodeTemplates
	if err := nodeTemplateList.Verify(); err != nil {
		return err
	}
	// 额外校验
	if len(nodeTemplateList) < 4 {
		return errors.NewFromCode(errors.Var.StandardNoneHANodeNumError)
	}
	return nil
}

// getTemplateCode
// 获取excel 的code信息
func (excel *clusterCreateStandardNoneHANodesTemplateExcel) getTemplateCode() template.ExcelTemplateCode {
	return excel.templateCode
}

func newClusterCreateStandardHANodesTemplateExcel() excelIntf {
	return &clusterCreateStandardHANodesTemplateExcel{
		templateCode: template.ExcelTemplateCodeClusterCreateNodesStandardHA,
		fileName:     template.ExcelFinaNameClusterCreateNodesStandardHA,
		title:        template.ExcelTitleClusterCreateNodesStandardHA,
	}
}

// clusterCreateStandardHANodesTemplateExcel
// 集群创建节点模板excel - StandardHA
type clusterCreateStandardHANodesTemplateExcel struct {
	templateCode template.ExcelTemplateCode
	fileName     string
	title        string
}

// ListExcelMeta
// 获取excel 的元数据信息
func (excel *clusterCreateStandardHANodesTemplateExcel) ListExcelMeta(ctx context.Context) (*template.ExcelMeta, error) {
	result := template.ExcelMeta{
		Code:           excel.getTemplateCode(),
		FileName:       excel.fileName,
		Title:          excel.title,
		TitleColor:     "#FF0000",
		TitleHeight:    75,
		TitleRowHeight: 70,
		TitleRows: []template.ExcelTitleRow{
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateNodeCodeIp,
				Name:      "节点IP（标准高可用模式下，前3个IP将作为Master节点，第4-7个IP将作为Worker及系统节点，第8个及以后IP将作为普通Worker节点）",
				Weight:    39.04,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateNodeCodePort,
				Name:      "SSH端口（若为空，则使用默认值22）",
				Weight:    33.76,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateNodeCodeUsername,
				Name:      "登录用户名",
				Weight:    14,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateNodeCodePassword,
				Name:      "登录密码",
				Weight:    14,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateNodeCodeSudoPassword,
				Name:      "提权密码（若使用非root账号登录，请输入）",
				Weight:    30.07,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateNodeCodeIsGpu,
				Name:      "是否为GPU节点（True/False，若为空，则使用默认值False）",
				Weight:    32.47,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateHostDiskAutoMount,
				Name:      "节点数据盘自动挂载（True/False，若为空，则使用默认值False）",
				Weight:    32.47,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateHostEtcdMountPath,
				Name:      "ETCD数据盘（Master节点[第1,2,3台]必须填写，其他节点无需填写）",
				Weight:    32.47,
				Style:     &excelYellowStyle,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateHostSystemMountPath,
				Name:      "系统数据盘（系统节点[第4,5,6,7台]必须填写，其他节点无需填写）",
				Weight:    32.47,
				Style:     &excelYellowStyle,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateHostDockerMountPath,
				Name:      "Containerd数据盘（所有节点均需要填写）",
				Weight:    32.47,
				Style:     &excelYellowStyle,
			},
			{
				ExcelCode: excel.getTemplateCode(),
				Code:      clusterCreateHostKubeletMountPath,
				Name:      "Kubelet数据盘（所有节点均需要填写）",
				Weight:    32.47,
				Style:     &excelYellowStyle,
			},
		},
	}
	return &result, nil
}

// SugarExcelData
// 解析excel中的文件 以生成前端期望的接口
func (excel *clusterCreateStandardHANodesTemplateExcel) SugarExcelData(ctx context.Context, fileName []string, lines [][]string) (interface{}, error) {
	if err := excel.VerifyExcelData(ctx, lines); err != nil {
		return nil, err
	}
	nodeTemplates, err := getClusterCreateNodeTemplates(ctx, excel, lines)
	if err != nil {
		return nil, err
	}
	var results = make([]*cluster.CreateNodeConfigResponse, 0, len(nodeTemplates))
	for _, template := range nodeTemplates {
		results = append(results, template.asResult())
	}
	// 节点IP（标准高可用模式下，前3个IP将作为Master节点，第4-7个IP将作为Worker及系统节点）
	masters := results[:3]
	systemNodes := results[3:7]
	defaults := results[:7]
	workers := results[3:]
	setNodeResultValue(masters, systemNodes, defaults, workers)

	// 对result 的 master、系统节点以及工作节点
	// Master 节点需要填写 ETCD + Docker + Kubelet
	// System 节点需要填写 System + Docker + Kubelet
	// 其他    节点需要填写 Docker + Kubelet
	if err := verifyNodeStorageMount(masters, systemNodes); err != nil {
		return nil, err
	}
	return results, nil
}

// VerifyExcelData
// 校验Excel中的文件
func (excel *clusterCreateStandardHANodesTemplateExcel) VerifyExcelData(ctx context.Context, lines [][]string) error {
	// 基础校验
	nodeTemplates, err := getClusterCreateNodeTemplates(ctx, excel, lines)
	if err != nil {
		return err
	}
	var nodeTemplateList clusterCreateTemplateList
	nodeTemplateList = nodeTemplates
	if err := nodeTemplateList.Verify(); err != nil {
		return err
	}
	// 额外校验
	if len(nodeTemplateList) < 7 {
		return errors.NewFromCode(errors.Var.StandardHANodeNumError)
	}
	return nil
}

// getTemplateCode
// 获取excel 的code信息
func (excel *clusterCreateStandardHANodesTemplateExcel) getTemplateCode() template.ExcelTemplateCode {
	return excel.templateCode
}

// getExcelDataMap
// 将解析excel 后传入的数据格式转化为clusterCreateAllInOneNodeTemplate结构体列表
func getClusterCreateNodeTemplates(ctx context.Context, excel excelIntf, lines [][]string) ([]clusterCreateNodeTemplate, error) {
	maps, err := getExcelDataMapList(ctx, excel, lines)
	if err != nil {
		return nil, err
	}
	var result []clusterCreateNodeTemplate
	if err := utils.BeanCopy(maps, &result); err != nil {
		return nil, err
	}
	return result, nil
}

// getExcelDataMap
// 将解析excel 后传入的数据格式转化为map数组
func getExcelDataMapList(ctx context.Context, excel excelIntf, lines [][]string) ([]map[string]string, error) {
	meta, err := excel.ListExcelMeta(ctx)
	if err != nil {
		return nil, err
	}
	var maps = make([]map[string]string, 0, len(lines))
	for _, line := range lines {
		if len(line) == 0 {
			continue
		}
		jsonMap := make(map[string]string, 16)
		for index, item := range line {
			if index > len(meta.TitleRows)-1 {
				break
			}
			jsonMap[meta.TitleRows[index].Code] = strings.TrimSpace(item)
		}
		maps = append(maps, jsonMap)
	}
	return maps, nil
}

func setNodeResultValue(masters, systemNodes, defaults, workers []*cluster.CreateNodeConfigResponse) {
	for _, master := range masters {
		master.Role = cluster.Master
	}
	for _, systemNode := range systemNodes {
		systemNode.SystemNode = true
	}
	for _, defaultNode := range defaults {
		defaultNode.IsDefault = true
	}
	for _, worker := range workers {
		worker.Role = cluster.Worker
	}
}

func portVerifyIgnoreEmpty(portStr string) error {
	// 忽略空值
	if portStr == "" {
		return nil
	}
	// 转换成数字
	port, err := strconv.Atoi(portStr)
	if err != nil {
		return errors.NewFromCodeWithMessage(errors.Var.PortNotEmpty, portStr)
	}
	// 要求端口在0-65535 之间
	if port < 0 || port > 65535 {
		return errors.NewFromCodeWithMessage(errors.Var.PortIllegal, portStr)
	}
	return nil
}

func isGpuVerifyIgnoreEmpty(isGpuStr string) error {
	if isGpuStr == "" {
		return nil
	}
	if err := trueOrFalseVerifyIgnoreEmpty(isGpuStr); err != nil {
		return errors.NewFromCodeWithMessage(errors.Var.IsGpuValueIllegal, isGpuStr)
	}
	return nil
}
func trueOrFalseVerifyIgnoreEmpty(boolValStr string) error {
	if boolValStr == "" {
		return nil
	}
	boolValStr = strings.ToLower(boolValStr)
	switch boolValStr {
	case "true":
	case "false":
	default:
		return errors.NewFromCodeWithMessage(errors.Var.BoolParamError, boolValStr)
	}
	return nil

}

func verifyNodeStorageMount(masters, systemNodes []*cluster.CreateNodeConfigResponse) error {
	// check master
	for _, master := range masters {
		// 若为自动挂载 校验ETCD数据盘位置
		if master.Storage.Type == cluster.NodeStorageTypeAuto {
			if master.Storage.DiskPath == nil || strings.EqualFold("", strings.TrimSpace(master.Storage.DiskPath.ETCD)) {
				return errors.NewFromCodeWithMessage(errors.Var.DiskTypeAutoMasterEtcdPathNullErr, fmt.Sprintf("ip=%s", master.Ip))
			}
		}
	}

	// check system
	for _, system := range systemNodes {
		// 若为自动挂载 校验system 校验系统数据盘位置
		if system.Storage.Type == cluster.NodeStorageTypeAuto {
			if system.Storage.DiskPath == nil || strings.EqualFold("", strings.TrimSpace(system.Storage.DiskPath.System)) {
				return errors.NewFromCodeWithMessage(errors.Var.DiskTypeAutoSystemDataPathNullErr, fmt.Sprintf("ip=%s", system.Ip))
			}
		}
	}
	return nil
}

func nodeDiskVerify(ip, autoMount, dockerPath, kubeletPath string) error {
	if err := trueOrFalseVerifyIgnoreEmpty(autoMount); err != nil {
		return err
	}
	if "true" != strings.ToLower(autoMount) {
		return nil
	}
	if strings.EqualFold("", strings.TrimSpace(dockerPath)) {
		return errors.NewFromCodeWithMessage(errors.Var.DiskTypeAutoDockerPathNullErr, fmt.Sprintf("ip=%s", ip))
	}

	if strings.EqualFold("", strings.TrimSpace(kubeletPath)) {
		return errors.NewFromCodeWithMessage(errors.Var.DiskTypeAutoKubeletPathNullErr, fmt.Sprintf("ip=%s", ip))
	}
	return nil
}
