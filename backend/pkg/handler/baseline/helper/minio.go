package helper

import (
	"context"
	"strings"

	"github.com/minio/minio-go/v7"
)

// IsMinioPathExists 判断minio路径标记是否存在（兼容文件和文件夹检查）
func IsMinioPathExists(ctx context.Context, minioClient *minio.Client, bucket, path string) (bool, error) {
	// 标准化路径：目录需以/结尾
	isDir := strings.HasSuffix(path, "/")
	normalizedPath := path
	if isDir && !strings.HasSuffix(path, "/") {
		normalizedPath += "/"
	}

	_, err := minioClient.StatObject(ctx, bucket, normalizedPath, minio.StatObjectOptions{})
	switch {
	case err == nil:
		return true, nil
	case minio.ToErrorResponse(err).Code == "NoSuchKey":
		return false, nil
	default:
		return false, err
	}
}

// EnsureMinioPath 递归创建minio路径（类似mkdir -p）
func EnsureMinioPath(ctx context.Context, minioClient *minio.Client, bucket, path string) error {
	// 路径标准化处理
	path = strings.TrimPrefix(path, "/")
	if !strings.HasSuffix(path, "/") {
		path += "/"
	}

	// 分割多级路径
	segments := strings.Split(path, "/")
	currentPath := ""

	for _, seg := range segments {
		if seg == "" {
			continue
		}

		currentPath += seg + "/"

		exists, err := IsMinioPathExists(ctx, minioClient, bucket, currentPath)
		if err != nil {
			return err
		}
		if exists {
			continue
		}

		// 创建当前层级的路径标记
		if _, err := minioClient.PutObject(
			ctx,
			bucket,
			currentPath,
			strings.NewReader(""),
			0,
			minio.PutObjectOptions{
				ContentType: "application/octet-stream",
				UserMetadata: map[string]string{
					"x-minio-path-type": "directory", // 添加元数据标记
				},
			},
		); err != nil {
			return err
		}
	}
	return nil
}
