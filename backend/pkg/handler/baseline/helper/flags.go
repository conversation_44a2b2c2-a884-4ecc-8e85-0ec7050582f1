package helper

import (
	"context"
	"strconv"
	"time"

	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

// IsFeatureBaselineEnabled 获取基线检查功能是否开启
func IsFeatureBaselineEnabled() bool {
	return GetFeatureBaselineEnabledConfigValue()
}

// GetFeatureBaselineEnabledConfigValue 获取基线检查功能是否开启配置值
// 从参数和环境变量中获取基线检查功能是否开启配置值
// 优先级：参数 > 环境变量
// 参数格式：true, false
// 环境变量：FEATURE_BASELINE_ENABLED
// 如果参数和环境变量都未设置，则返回默认值
func GetFeatureBaselineEnabledConfigValue() bool {
	return config.FeatureBaselineEnabled.Value == "true"
}

// GetBaselineResourceRemainTimeConfigValue 获取基线资源保留时间配置值
// 从参数和环境变量中获取基线资源保留时间配置值
// 优先级：参数 > 环境变量
// 参数格式：1d, 1h, 1m, 1s
// 环境变量：BASELINE_RESOURCE_REMAIN_TIME
// 如果参数和环境变量都未设置，则返回默认值
// 保留时间不可小于标准任务超时时间
func GetBaselineResourceRemainTimeConfigValue(ctx context.Context, db *gorm.DB) time.Duration {
	resourceRemainTime := config.BaselineResourceRemainTime.Value
	duration := utils.GetEnvDurationWithDefault(constants.BaselineResourceRemainTimeEnv, constants.BaselineResourceRemainTimeDefault)
	if resourceRemainTime != "" {
		tmpDuration, err := time.ParseDuration(resourceRemainTime)
		if err != nil {
			logger.GetSugared().Warnw("failed to parse baseline resource remain time, use default value", zap.Error(err),
				zap.String("value", resourceRemainTime))
		} else {
			duration = tmpDuration
		}
	}
	var systemConfig caas.SystemConfig
	if err := db.WithContext(ctx).Model(&caas.SystemConfig{}).
		Where("config_type = ?", constants.BaselineSystemConfigType).
		Where("config_name = ?", constants.BaselineResourceRemainTimeConfigKey).
		First(&systemConfig).Error; err == nil {
		tmpDuration, err := time.ParseDuration(systemConfig.ConfigValue)
		if err != nil {
			logger.GetSugared().Errorw("failed to parse baseline resource remain time, use default value", zap.Error(err),
				zap.String("value", systemConfig.ConfigValue))
		} else {
			duration = tmpDuration
		}
	}

	if duration < GetBaselineStandardJobTimeoutConfigValue() {
		// 保留时间不可小于标准任务超时时间
		return GetBaselineStandardJobTimeoutConfigValue()
	}
	return duration
}

// GetBaselineJobRecordRemainTime 获取基线任务记录保留时间
func GetBaselineJobRecordRemainTime(ctx context.Context, db *gorm.DB) time.Duration {
	remainTime := config.BaselineJobRecordRemainTime.Value
	duration := utils.GetEnvDurationWithDefault(constants.BaselineSystemConfigKeyJobRecordRemainTimeEnv, constants.BaselineJobRecordRemainTimeDefault)
	if remainTime != "" {
		tmpDuration, err := time.ParseDuration(remainTime)
		if err != nil {
			logger.GetSugared().Warnw("failed to parse baseline job record remain time, use default value", zap.Error(err), zap.String("value", remainTime))
		} else {
			duration = tmpDuration
		}
	}
	var systemConfig caas.SystemConfig
	if err := db.WithContext(ctx).Model(&caas.SystemConfig{}).
		Where("config_type = ?", constants.BaselineSystemConfigType).
		Where("config_name = ?", constants.BaselineSystemConfigKeyJobRecordRemainTimeConfigKey).First(&systemConfig).Error; err == nil {
		tmpDuration, err := time.ParseDuration(systemConfig.ConfigValue)
		if err != nil {
			logger.GetSugared().Errorw("failed to parse baseline job record remain time, use default value", zap.Error(err), zap.String("value", systemConfig.ConfigValue))
		} else {
			duration = tmpDuration
		}
	}
	if duration < GetBaselineStandardJobTimeoutConfigValue() {
		// 保留时间不可小于标准任务超时时间
		return GetBaselineStandardJobTimeoutConfigValue()
	}
	return duration
}

// GetBaselineStrategyJobMaxConcurrencyConfigValue 获取基线策略任务最大并发数配置值
// 从参数和环境变量中获取基线策略任务最大并发数配置值
// 优先级：参数 > 环境变量
// 参数格式：1
// 环境变量：BASELINE_STRATEGY_JOB_MAX_CONCURRENCY
// 如果参数和环境变量都未设置，则返回默认值
func GetBaselineStrategyJobMaxConcurrencyConfigValue() int {
	concurrency := config.BaselineStrategyJobMaxConcurrency.Value
	if concurrency != "" {
		concurrencyInt, err := strconv.Atoi(concurrency)
		if err != nil {
			logger.GetSugared().Errorw("failed to parse baseline strategy job max concurrency", zap.Error(err))
			return constants.BaselineStrategyJobMaxConcurrencyDefault
		}
		return concurrencyInt
	}
	return int(utils.GetEnvInt64WithDefault(constants.BaselineStrategyJobMaxConcurrencyEnv, constants.BaselineStrategyJobMaxConcurrencyDefault))
}

// GetBaselineStandardJobMaxConcurrencyConfigValue 获取基线标准任务最大并发数配置值
// 从参数和环境变量中获取基线标准任务最大并发数配置值
// 优先级：参数 > 环境变量
// 参数格式：1
// 环境变量：BASELINE_STANDARD_JOB_MAX_CONCURRENCY
// 如果参数和环境变量都未设置，则返回默认值
func GetBaselineStandardJobMaxConcurrencyConfigValue() int {
	concurrency := config.BaselineStandardJobMaxConcurrency.Value
	if concurrency != "" {
		concurrencyInt, err := strconv.Atoi(concurrency)
		if err != nil {
			logger.GetSugared().Errorw("failed to parse baseline standard job max concurrency", zap.Error(err))
			return constants.BaselineStandardJobMaxConcurrencyDefault
		}
		return concurrencyInt
	}
	return int(utils.GetEnvInt64WithDefault(constants.BaselineStandardJobMaxConcurrencyEnv, constants.BaselineStandardJobMaxConcurrencyDefault))
}

// GetBaselineStrategyJobTimeoutConfigValue 获取基线策略任务超时时间配置值
// 从参数和环境变量中获取基线策略任务超时时间配置值
// 优先级：参数 > 环境变量
// 参数格式：1d, 1h, 1m, 1s
// 环境变量：BASELINE_STRATEGY_JOB_TIMEOUT
// 如果参数和环境变量都未设置，则返回默认值
func GetBaselineStrategyJobTimeoutConfigValue() time.Duration {
	duration := utils.GetEnvDurationWithDefault(constants.BaselineStrategyJobTimeoutEnv, constants.BaselineStrategyJobTimeoutDefault)
	timeout := config.BaselineStrategyJobTimeout.Value
	if timeout != "" {
		tmpDuration, err := time.ParseDuration(timeout)
		if err != nil {
			logger.GetSugared().Errorw("failed to parse baseline strategy job timeout", zap.Error(err))
		} else {
			duration = tmpDuration
		}
	}
	if duration < GetBaselineStandardJobTimeoutConfigValue() {
		// 保留时间不可小于标准任务超时时间
		return GetBaselineStandardJobTimeoutConfigValue()
	}
	return duration
}

// GetBaselineStandardJobTimeoutConfigValue 获取基线标准任务超时时间配置值
// 从参数和环境变量中获取基线标准任务超时时间配置值
// 优先级：参数 > 环境变量
// 参数格式：1d, 1h, 1m, 1s
// 环境变量：BASELINE_STANDARD_JOB_TIMEOUT
// 如果参数和环境变量都未设置，则返回默认值
func GetBaselineStandardJobTimeoutConfigValue() time.Duration {
	timeout := config.BaselineStandardJobTimeout.Value
	if timeout != "" {
		duration, err := time.ParseDuration(timeout)
		if err != nil {
			logger.GetSugared().Errorw("failed to parse baseline standard job timeout", zap.Error(err))
			return utils.GetEnvDurationWithDefault(constants.BaselineStandardJobTimeoutEnv, constants.BaselineStandardJobTimeoutDefault)
		}
		return duration
	}
	return utils.GetEnvDurationWithDefault(constants.BaselineStandardJobTimeoutEnv, constants.BaselineStandardJobTimeoutDefault)
}

// GetBaselineScanTimeoutJobCronConfigValue 获取基线扫描超时任务Cron配置值
// 从配置表中获取基线扫描超时任务Cron配置值
func GetBaselineScanTimeoutJobCronConfigValue(ctx context.Context, db *gorm.DB) (spec string) {
	var systemConfig caas.SystemConfig
	if err := db.WithContext(ctx).Model(&caas.SystemConfig{}).
		Where("config_type = ?", constants.BaselineSystemConfigType).
		Where("config_name = ?", constants.BaselineScanTimeoutJobCronConfigKey).
		First(&systemConfig).Error; err != nil {
		spec = constants.BaselineScanTimeoutJobCronDefault
	}

	if systemConfig.ConfigValue != "" {
		if _, err := cron.ParseStandard(systemConfig.ConfigValue); err != nil {
			logger.GetSugared().Errorw("failed to parse baseline scan timeout job cron", zap.Error(err))
			return constants.BaselineScanTimeoutJobCronDefault
		}
		return systemConfig.ConfigValue
	}
	return constants.BaselineScanTimeoutJobCronDefault
}

// BaselineScanExpiredJobResourceConfigValue 获取基线扫描过期任务资源Cron配置值
// 从配置表中获取基线扫描过期任务资源Cron配置值
func BaselineScanExpiredJobResourceConfigValue(ctx context.Context, db *gorm.DB) (spec string) {

	var systemConfig caas.SystemConfig
	if err := db.WithContext(ctx).Model(&caas.SystemConfig{}).
		Where("config_type = ?", constants.BaselineSystemConfigType).
		Where("config_name = ?", constants.BaselineScanExpiredJobResourceCronConfigKey).
		First(&systemConfig).Error; err != nil {
		return constants.BaselineScanExpiredJobResourceCronDefault
	}

	if systemConfig.ConfigValue != "" {
		if _, err := cron.ParseStandard(systemConfig.ConfigValue); err != nil {
			logger.GetSugared().Errorw("failed to parse baseline scan expired job resource cron", zap.Error(err))
			return constants.BaselineScanExpiredJobResourceCronDefault
		}
		return systemConfig.ConfigValue
	}
	return constants.BaselineScanExpiredJobResourceCronDefault

}

// GetBaselineScanExpiredJobRecordCronConfigValue 获取基线扫描过期任务记录Cron配置值
// 从配置表中获取基线扫描过期任务记录Cron配置值
func GetBaselineScanExpiredJobRecordCronConfigValue(ctx context.Context, db *gorm.DB) (spec string) {

	var systemConfig caas.SystemConfig
	if err := db.WithContext(ctx).Model(&caas.SystemConfig{}).
		Where("config_type = ?", constants.BaselineSystemConfigType).
		Where("config_name = ?", constants.BaselineScanExpiredJobRecordCronConfigKey).
		First(&systemConfig).Error; err != nil {
		return constants.BaselineScanExpiredJobRecordCronDefault
	}

	if systemConfig.ConfigValue != "" {
		if _, err := cron.ParseStandard(systemConfig.ConfigValue); err != nil {
			logger.GetSugared().Errorw("failed to parse baseline scan expired job record cron", zap.Error(err))
			return constants.BaselineScanExpiredJobRecordCronDefault
		}
		return systemConfig.ConfigValue
	}
	return constants.BaselineScanExpiredJobRecordCronDefault
}

// GetMonitorHostPathPrefix 获取监控系统主机路径前缀
// 从环境变量中获取监控系统主机路径前缀
// 环境变量：BASELINE_MONITOR_HOST_PATH_PREFIX
// 如果环境变量未设置，则返回默认值
func GetMonitorHostPathPrefix() string {
	return utils.GetEnvStringWithDefault(constants.BaselineMonitorHostPathPrefixEnv, constants.BaselineMonitorHostPathPrefix)
}

// GetImportBuiltinBaselineCompletedConfigValue 获取导入内置基线完成状态配置值
// 从环境变量中获取导入内置基线完成状态配置值
// 环境变量：BASELINE_IMPORT_BUILTIN_BASELINE_COMPLETED
// 如果环境变量未设置，则返回默认值
func GetImportBuiltinBaselineCompletedConfigValue(ctx context.Context, db *gorm.DB) bool {
	var systemConfig caas.SystemConfig
	if err := db.WithContext(ctx).Model(&caas.SystemConfig{}).
		Where("config_type = ?", constants.BaselineSystemConfigType).
		Where("config_name = ?", constants.BaselineImportBuiltinBaselineCompletedConfigKey).
		First(&systemConfig).Error; err != nil {
		return constants.BaselineImportBuiltinBaselineCompletedDefault == "true"
	}
	return systemConfig.ConfigValue == constants.BaselineImportBuiltinBaselineCompletedTrue
}

// SetImportBuiltinBaselineCompletedConfigValue 设置导入内置基线完成状态配置值
// 设置导入内置基线完成状态配置值
func SetImportBuiltinBaselineCompletedConfigValue(ctx context.Context, db *gorm.DB, completed bool) error {
	return db.WithContext(ctx).Model(&caas.SystemConfig{}).
		Where("config_type = ?", constants.BaselineSystemConfigType).
		Where("config_name = ?", constants.BaselineImportBuiltinBaselineCompletedConfigKey).
		Update("config_value", completed).Error
}

// GetImportBuiltinBaselineIncludeBusinessCluster 是否一起导入业务集群内置基线
// 如果导入会与当前管控集群内置基线可能存在冲突
// 默认为false
func GetImportBuiltinBaselineIncludeBusinessCluster(ctx context.Context, db *gorm.DB) bool {
	var systemConfig caas.SystemConfig
	if err := db.WithContext(ctx).Model(&caas.SystemConfig{}).
		Where("config_type = ?", constants.BaselineSystemConfigType).
		Where("config_name = ?", constants.BaselineImportBuiltinBaselineIncludeBizClusterConfigKey).
		First(&systemConfig).Error; err != nil {
		return constants.BaselineImportBuiltinBaselineIncludeBizClusterConfigValueDefault
	}
	return systemConfig.ConfigValue == "true"
}
