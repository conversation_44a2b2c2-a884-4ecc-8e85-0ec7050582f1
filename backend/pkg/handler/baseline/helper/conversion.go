package helper

import (
	"context"
	"fmt"
	"path/filepath"
	"sort"
	"strconv"
	"strings"
	"text/template"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"
	checkerv1 "harmonycloud.cn/baseline-checker/api/v1"
	inframonitor "harmonycloud.cn/baseline-checker/pkg/models/monitor"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline/infra"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

// ConvertCreateCustomCheckerRequestToBaselineRule ...
func ConvertCreateCustomCheckerRequestToBaselineRule(req *models.CreateCustomCheckerRequest) (*caas.BaselineRule, error) {
	rule := &caas.BaselineRule{}
	rule.Name = req.Name
	rule.CheckResourceType = req.ResourceType
	rule.RiskLevel = req.RiskLevel
	rule.Description = req.Description
	rule.Suggestion = req.Suggestion
	rule.Kind = models.CustomCheckerKind
	if err := SetCheckerConfigToBaselineRule(rule, req.CheckerConfig); err != nil {
		return nil, err
	}
	rule.CheckType = string(models.NewAggregationCheckMode(req.CheckerConfig))
	return rule, nil
}

// ConvertEditCustomCheckerRequestToBaselineRule ...
func ConvertEditCustomCheckerRequestToBaselineRule(req *models.UpdateCustomCheckerRequest, rule *caas.BaselineRule) (*caas.BaselineRule, error) {
	rule.ID = req.ID
	rule.Name = req.Name
	rule.CheckResourceType = req.ResourceType
	rule.RiskLevel = req.RiskLevel
	rule.Description = req.Description
	rule.Suggestion = req.Suggestion
	if err := SetCheckerConfigToBaselineRule(rule, req.CheckerConfig); err != nil {
		return nil, err
	}
	rule.CheckType = string(models.NewAggregationCheckMode(req.CheckerConfig))
	return rule, nil
}

// SetCheckerConfigToBaselineRule ...
func SetCheckerConfigToBaselineRule(rule *caas.BaselineRule, config *models.CheckerConfig) error {
	if config == nil || rule == nil {
		return nil
	}
	rule.CheckType = string(models.NewAggregationCheckMode(config))
	rule.CheckMode = string(config.CheckMode)
	if len(config.NodeRoles) != 0 {
		rule.NodeRoles = strings.Join(lo.Filter(lo.Map(config.NodeRoles, func(item models.CheckerNodeRole, _ int) string {
			return string(item)
		}), func(item string, index int) bool {
			return len(item) != 0
		}), ",")
		nodeSelectors := models.NewNodeSelectorsFromNodeRoles(config.NodeRoles)
		if nodeSelectors != nil {
			rule.NodeSelectors = strings.Join(nodeSelectors.LabelSelectors, constants.BaselineNodeSelectorSeparator)
		}
	} else if config.NodeSelector != nil && len(config.NodeSelector.LabelSelectors) != 0 {
		rule.NodeSelectors = strings.Join(config.NodeSelector.LabelSelectors, constants.BaselineNodeSelectorSeparator)
	}
	if config.Command != nil {
		rule.Command = config.Command.Command
		rule.CommandMatchType = string(config.Command.MatchType)
		rule.CommandMatchValue = config.Command.MatchValue
	}
	if config.File != nil {
		rule.FileType = string(config.File.FileType)
		rule.FileLocationMode = string(config.File.FileLocation.Mode)
		rule.FileLocationPath = config.File.FileLocation.Path
		rule.K8sResourceAPIVersion = config.File.FileLocation.K8sResourceRef.APIVersion
		rule.K8sResourceKind = config.File.FileLocation.K8sResourceRef.Kind
		rule.K8sResourceNamespace = config.File.FileLocation.K8sResourceRef.Namespace
		rule.K8sResourceName = config.File.FileLocation.K8sResourceRef.Name
		rule.K8sResourceLabels = strings.Join(lo.MapToSlice(config.File.FileLocation.K8sResourceRef.Labels, func(key string, value string) string {
			return fmt.Sprintf("%s=%s", key, value)
		}), ",")
		if config.File.MatchContent != nil {
			rule.FileMatchContent = *config.File.MatchContent
		}
		if config.File.FileContent != nil {
			rule.FileContentID = config.File.FileContent.Id
			rule.FileContentName = config.File.FileContent.Name
			rule.FileContentPath = config.File.FileContent.Path
			rule.FileContentUniqueKey = config.File.FileContent.UniqueKey
			rule.FileContentLink = config.File.FileContent.Link
		}
	}
	return nil
}

// ConvertBaselineRuleToBaselineStandardRule 将基线规则转换为基线标准规则
func ConvertBaselineRuleToBaselineStandardRule(standardID int64, rule *caas.BaselineRule) *caas.BaselineStandardRule {
	checkerRawData, err := ConvertBaselineRuleToCheckRawData(rule)
	if err != nil {
		return nil
	}
	standardRule := &caas.BaselineStandardRule{
		StandardID:       standardID,
		RuleID:           rule.ID,
		RiskLevel:        rule.RiskLevel,
		CheckValuePrefix: string(checkerRawData.Value.Prefix),
	}
	SetCheckValueToBaselineStandardRule(standardRule, &checkerRawData.Value)
	return standardRule
}

// ConvertBaselineRuleToCheckRawData 将基线规则转换为检查项
func ConvertBaselineRuleToCheckRawData(rule *caas.BaselineRule) (*models.CheckRawData, error) {
	if rule == nil {
		return new(models.CheckRawData), nil
	}
	checkerConfig := &models.CheckerConfig{
		CheckMode: models.CheckerMode(rule.CheckMode),
		NodeRoles: lo.Ternary(rule.NodeRoles != "", lo.Map(strings.Split(rule.NodeRoles, ","), func(item string, _ int) models.CheckerNodeRole {
			return models.CheckerNodeRole(item)
		}), make([]models.CheckerNodeRole, 0)),
		NodeSelector: &models.NodeSelector{
			LabelSelectors: lo.Ternary(rule.NodeSelectors != "", strings.Split(rule.NodeSelectors, constants.BaselineNodeSelectorSeparator), make([]string, 0)),
		},
		Command: &models.CommandChecker{
			Command:    rule.Command,
			MatchType:  models.MatchType(rule.CommandMatchType),
			MatchValue: rule.CommandMatchValue,
		},
		File: &models.FileChecker{
			FileType: models.FileType(rule.FileType),
			FileLocation: models.FileLocationConfig{
				Mode: models.FileLocationMode(rule.FileLocationMode),
				Path: rule.FileLocationPath,
				K8sResourceRef: models.K8sResourceRef{
					APIVersion: rule.K8sResourceAPIVersion,
					Kind:       rule.K8sResourceKind,
					Namespace:  rule.K8sResourceNamespace,
					Name:       rule.K8sResourceName,
					Labels: lo.SliceToMap(strings.Split(rule.K8sResourceLabels, ","), func(item string) (string, string) {
						split := strings.Split(item, "=")
						if len(split) == 2 {
							return split[0], split[1]
						} else if len(split) == 1 {
							return split[0], ""
						} else {
							return item, ""
						}
					}),
				},
			},
			FileContent: &models.FileItem{
				Id:        rule.FileContentID,
				Name:      rule.FileContentName,
				Path:      rule.FileContentPath,
				UniqueKey: rule.FileContentUniqueKey,
				Link:      rule.FileContentLink,
			},
		},
	}
	checkValue := GetCheckValueFromCheckerConfig(checkerConfig)

	checkRawData := &models.CheckRawData{
		Value:  *checkValue,
		Config: *checkerConfig,
	}
	return checkRawData, nil
}

func GetCheckValueFromCheckerConfig(checkerConfig *models.CheckerConfig) *models.CheckValueItem {
	if checkerConfig == nil {
		return nil
	}
	checkValue := &models.CheckValueItem{
		Prefix: models.ValuePrefixInput,
		File:   nil,
		Value:  "",
	}
	switch checkerConfig.CheckMode {
	case models.CheckerModeCommand:
		if checkerConfig.Command != nil {
			switch checkerConfig.Command.MatchType {
			case models.MatchTypeFuzzy:
				checkValue.Prefix = models.ValuePrefixRegExp
			case models.MatchTypeExact:
				checkValue.Prefix = models.ValuePrefixInput
			}
			checkValue.Value = checkerConfig.Command.MatchValue
		}
	case models.CheckerModeFile:
		if checkerConfig.File != nil {
			if checkerConfig.File.MatchContent != nil && *checkerConfig.File.MatchContent != "" {
				checkValue.Prefix = models.ValuePrefixInput
				checkValue.Value = lo.FromPtr(checkerConfig.File.MatchContent)
			} else if checkerConfig.File.FileContent != nil {
				checkValue.Prefix = models.ValuePrefixFile
				checkValue.File = checkerConfig.File.FileContent
			}
		}
	}
	return checkValue
}

// ConvertBaselineRuleToCheckItem 将基线规则转换为检查项
func ConvertBaselineRuleToCheckItem(baselineRule *caas.BaselineRule) *models.CheckItem {
	checkConfig, _ := ConvertBaselineRuleToCheckRawData(baselineRule)
	return &models.CheckItem{
		ID:            baselineRule.ID,
		Name:          baselineRule.Name,
		Kind:          baselineRule.Kind,
		ResourceType:  baselineRule.CheckResourceType,
		RiskLevel:     baselineRule.RiskLevel,
		Builtin:       baselineRule.Builtin,
		Description:   baselineRule.Description,
		CheckType:     models.NewAggregationCheckMode(&checkConfig.Config),
		CheckerConfig: &checkConfig.Config,
		CreateTime:    baselineRule.CreateTime.Format(time.DateTime),
		UpdateTime:    baselineRule.UpdateTime.Format(time.DateTime),
	}
}

// ConvertBaselineRuleToCheckDetailItem ...
func ConvertBaselineRuleToCheckDetailItem(baselineRule *caas.BaselineRule) *models.CheckDetailItem {
	if baselineRule == nil {
		return nil
	}
	checkConfig, _ := ConvertBaselineRuleToCheckRawData(baselineRule)
	return &models.CheckDetailItem{
		ID:            baselineRule.ID,
		Name:          baselineRule.Name,
		ResourceType:  baselineRule.CheckResourceType,
		RiskLevel:     baselineRule.RiskLevel,
		Kind:          baselineRule.Kind,
		Builtin:       baselineRule.Builtin,
		Description:   baselineRule.Description,
		Suggestion:    baselineRule.Suggestion,
		CheckerConfig: &checkConfig.Config,
		CreateTime:    baselineRule.CreateTime.Format(time.DateTime),
		UpdateTime:    baselineRule.UpdateTime.Format(time.DateTime),
	}
}

// ConvertBaselineStandardToClusterStandardItem ...
func ConvertBaselineStandardToClusterStandardItem(clusterName string,
	standard *caas.BaselineStandard) models.ClusterStandardItem {
	return models.ClusterStandardItem{
		ID:          standard.ID,
		Name:        standard.Name,
		ClusterName: clusterName,
		CategoryID:  standard.GroupID,
		Builtin:     standard.Builtin,
		Description: standard.Description,
		CreateTime:  standard.CreateTime.Format(time.DateTime),
		UpdateTime:  standard.UpdateTime.Format(time.DateTime),
	}

}

// ConvertBaselineStandardRuleToStandardCheckItem ...
func ConvertBaselineStandardRuleToStandardCheckItem(standardRule *caas.BaselineStandardRule) models.StandardCheckItem {
	checkValue, _ := ConvertBaselineStandardRuleToCheckValue(standardRule)
	return models.StandardCheckItem{
		StandardCheckID: standardRule.ID,
		StandardID:      standardRule.StandardID,
		CheckItem: models.CheckItem{
			ID:        standardRule.RuleID,
			RiskLevel: standardRule.RiskLevel,
		},
		CheckValue: *checkValue,
	}

}

// ConvertBaselineStandardRuleToCheckValue ...
func ConvertBaselineStandardRuleToCheckValue(baselineRule *caas.BaselineStandardRule) (*models.CheckValueItem, error) {
	checkValue := &models.CheckValueItem{
		Prefix: models.ValuePrefix(baselineRule.CheckValuePrefix),
		Value:  baselineRule.CheckValueContent,
		File: &models.FileItem{
			Id:        baselineRule.FileContentID,
			Path:      baselineRule.FileContentPath,
			UniqueKey: baselineRule.FileContentUniqueKey,
			Name:      baselineRule.FileContentName,
			Link:      baselineRule.FileContentLink,
		},
	}

	return checkValue, nil
}

// SetCheckValueToBaselineStandardRule 设置检查值到基线标准规则
func SetCheckValueToBaselineStandardRule(stdRule *caas.BaselineStandardRule, checkValue *models.CheckValueItem) {
	if checkValue != nil {
		if checkValue.Value != "" {
			stdRule.CheckValueContent = fmt.Sprintf("%v", checkValue.Value)
		}
		if len(checkValue.Prefix) > 0 {
			stdRule.CheckValuePrefix = string(checkValue.Prefix)
		}
		switch models.ValuePrefix(checkValue.Prefix) {
		case models.ValuePrefixInput, models.ValuePrefixRegExp:
			stdRule.CheckValueContent = fmt.Sprintf("%v", checkValue.Value)
		case models.ValuePrefixFile:
			if checkValue.File != nil {
				if checkValue.File.Id > 0 {
					stdRule.FileContentID = checkValue.File.Id
				}
				if checkValue.File.Path != "" {
					stdRule.FileContentPath = checkValue.File.Path
				}
				if checkValue.File.UniqueKey != "" {
					stdRule.FileContentUniqueKey = checkValue.File.UniqueKey
				}
				if checkValue.File.Name != "" {
					stdRule.FileContentName = checkValue.File.Name
				}
				if checkValue.File.Link != "" {
					stdRule.FileContentLink = checkValue.File.Link
				}
			}
		}
	}
}

// ConvertSimpleBaselineCheckerToBaselineStandardRule ...
func ConvertSimpleBaselineCheckerToBaselineStandardRule(standardID int64, simpleChecker *models.SimpleBaselineChecker) *caas.BaselineStandardRule {
	if simpleChecker == nil {
		return nil
	}
	checkValue := simpleChecker.CheckValue
	stdRule := &caas.BaselineStandardRule{
		RefID:      simpleChecker.StandardCheckID,
		StandardID: standardID,
		RuleID:     simpleChecker.CheckID,
	}
	SetCheckValueToBaselineStandardRule(stdRule, checkValue)

	if simpleChecker.RiskLevel != "" {
		stdRule.RiskLevel = simpleChecker.RiskLevel
	}

	return stdRule
}

// ConvertBaselineStrategyToSimpleStrategyItem ...
func ConvertBaselineStrategyToSimpleStrategyItem(strategy *caas.BaselineStrategy) *models.SimpleStrategyItem {
	if strategy == nil {
		return nil
	}
	clusterNames := strings.Split(strategy.ClusterNames, ",")
	return &models.SimpleStrategyItem{
		Id:           strategy.ID,
		Name:         strategy.Name,
		ClusterCount: len(clusterNames),
		ClusterNames: clusterNames,
	}
}

// ExecutionConfigRenderParam 渲染执行配置的参数
type ExecutionConfigRenderParam struct {
	EndTime         string
	ExecutionTime   string
	ExecutionDate   string
	PerDays         string
	DaysOfWeek      string
	StartDayOfMonth string
	EndDayOfMonth   string
	CronExpression  string
}

// GenerateWeekEnum 返回对应语言的星期枚举
func GenerateWeekEnum(ctx context.Context) map[int]string {
	langCode := GetLanguageCode(ctx)
	weekEnum := map[string]map[int]string{
		constants.LanguageCodeZhCN: {
			1: "一",
			2: "二",
			3: "三",
			4: "四",
			5: "五",
			6: "六",
			0: "日",
		},
		constants.LanguageCodeZhHK: {
			1: "一",
			2: "二",
			3: "三",
			4: "四",
			5: "五",
			6: "六",
			0: "日",
		},
		constants.LanguageCodeEnUS: {
			1: "Mon",
			2: "Tue",
			3: "Wed",
			4: "Thu",
			5: "Fri",
			6: "Sat",
			0: "Sun",
		},
	}
	return weekEnum[langCode]
}

// GenerateExecutionHumanReadableTemplate 返回对应语言的模板
func GenerateExecutionHumanReadableTemplate(ctx context.Context) map[string]string {
	langCode := GetLanguageCode(ctx) // 获取语言代码，例如 "zh", "zh-tw", "en"
	var (
		TemplateKeyImmediate        = string(models.ExecutionTypeImmediate)
		TemplateKeyScheduled        = string(models.ExecutionTypeScheduled)
		TemplateKeyRecurringDaily   = fmt.Sprintf("%s/%s", models.ExecutionTypeRecurring, models.RecurringTypeDaily)
		TemplateKeyRecurringWeekly  = fmt.Sprintf("%s/%s", models.ExecutionTypeRecurring, models.RecurringTypeWeekly)
		TemplateKeyRecurringMonthly = fmt.Sprintf("%s/%s", models.ExecutionTypeRecurring, models.RecurringTypeMonthly)
		TemplateKeyRecurringCron    = fmt.Sprintf("%s/%s", models.ExecutionTypeRecurring, models.RecurringTypeCron)
	)
	templates := map[string]map[string]string{
		constants.LanguageCodeZhCN: {
			TemplateKeyImmediate:        "立即执行",
			TemplateKeyScheduled:        "{{.ExecutionDate}} 检查",
			TemplateKeyRecurringDaily:   "每 {{.PerDays}} 天 {{.ExecutionTime}} 检查",
			TemplateKeyRecurringWeekly:  "每周 {{.DaysOfWeek}} {{.ExecutionTime}} 检查",
			TemplateKeyRecurringMonthly: "每月 {{.StartDayOfMonth}} 号 至 {{.EndDayOfMonth}} 号 {{.ExecutionTime}} 检查",
			TemplateKeyRecurringCron:    "{{.CronExpression}}",
		},
		constants.LanguageCodeZhHK: {
			TemplateKeyImmediate:        "立即執行",
			TemplateKeyScheduled:        "{{.ExecutionDate}} 檢查",
			TemplateKeyRecurringDaily:   "每 {{.PerDays}} 天 {{.ExecutionTime}} 檢查",
			TemplateKeyRecurringWeekly:  "每週 {{.DaysOfWeek}} {{.ExecutionTime}} 檢查",
			TemplateKeyRecurringMonthly: "每月 {{.StartDayOfMonth}} 號 至 {{.EndDayOfMonth}} 號 {{.ExecutionTime}} 檢查",
			TemplateKeyRecurringCron:    "{{.CronExpression}}",
		},
		constants.LanguageCodeEnUS: {
			TemplateKeyImmediate:        "Execute immediately",
			TemplateKeyScheduled:        "Execute at {{.ExecutionDate}}",
			TemplateKeyRecurringDaily:   "Execute every {{.PerDays}} days at {{.ExecutionTime}}",
			TemplateKeyRecurringWeekly:  "Execute every week on {{.DaysOfWeek}} at {{.ExecutionTime}}",
			TemplateKeyRecurringMonthly: "Execute every month between {{.StartDayOfMonth}} and {{.EndDayOfMonth}} at {{.ExecutionTime}}",
			TemplateKeyRecurringCron:    "{{.CronExpression}}",
		},
	}
	return templates[langCode]
}

// ConvertExecutionConfigToHumanReadable 根据模板渲染可读字符串
func ConvertExecutionConfigToHumanReadable(ctx context.Context, config models.ExecutionConfig) string {
	templates := GenerateExecutionHumanReadableTemplate(ctx)
	key := string(config.ExecutionType)
	if config.ExecutionType == models.ExecutionTypeRecurring {
		key = fmt.Sprintf("%s/%s", config.ExecutionType, config.RecurringType)
	}
	templateStr, exists := templates[key]
	if !exists {
		return "Template not found"
	}

	tmpl, err := template.New("execution").Parse(templateStr)
	if err != nil {
		return fmt.Sprintf("Template parsing error: %v", err)
	}
	// asc sort
	sort.Slice(config.DaysOfWeek, func(i, j int) bool {
		return config.DaysOfWeek[i] < config.DaysOfWeek[j]
	})
	// asc sort days of month
	sort.Slice(config.DaysOfMonth, func(i, j int) bool {
		return config.DaysOfMonth[i] < config.DaysOfMonth[j]
	})
	var startDayOfMonth *string
	var endDayOfMonth *string
	if len(config.DaysOfMonth) >= 2 {
		startDayOfMonth = lo.ToPtr(fmt.Sprint(config.DaysOfMonth[0]))
		endDayOfMonth = lo.ToPtr(fmt.Sprint(config.DaysOfMonth[len(config.DaysOfMonth)-1]))
	}
	executionTimeStr := config.ExecutionTime
	t, err := ParseTime(config.ExecutionTime)
	if err == nil {
		executionTimeStr = t.Format(time.TimeOnly)
	}
	var result strings.Builder
	param := ExecutionConfigRenderParam{
		EndTime: config.EndTime,
		// 给 Recurring 周期 使用
		ExecutionTime: executionTimeStr,
		// 给 Scheduled 使用
		ExecutionDate: config.ExecutionDate,
		PerDays:       fmt.Sprint(config.PerDays),
		DaysOfWeek: strings.Join(lo.Map(config.DaysOfWeek, func(item int, _ int) string {
			return GenerateWeekEnum(ctx)[item]
		}), ","),
		StartDayOfMonth: lo.FromPtr(startDayOfMonth),
		EndDayOfMonth:   lo.FromPtr(endDayOfMonth),
		CronExpression:  config.CronExpression,
	}
	err = tmpl.Execute(&result, param)
	if err != nil {
		return fmt.Sprintf("Template execution error: %v", err)
	}

	return result.String()
}

// DaysStrToSliceInt 将 daysStr 转为 []int
func DaysStrToSliceInt(daysStr string) []int {
	days := lo.Filter(lo.Map(strings.Split(daysStr, ","), func(item string, _ int) int {
		day, err := strconv.Atoi(item)
		if err != nil {
			return -1
		}
		return day
	}), func(item int, _ int) bool {
		return item != -1
	})
	sort.Slice(days, func(i, j int) bool {
		return days[i] < days[j]
	})
	return days
}

func ConvertBaselineStrategyToExecutionConfig(ctx context.Context, strategy *caas.BaselineStrategy) *models.ExecutionConfig {
	if strategy == nil {
		return nil
	}
	daysOfMonth := DaysStrToSliceInt(strategy.ExecutionDaysOfMonth)
	if len(daysOfMonth) < 2 && len(daysOfMonth) > 0 {
		daysOfMonth = append(daysOfMonth, daysOfMonth[0])
	} else if len(daysOfMonth) == 0 {
		daysOfMonth = append(daysOfMonth, 1, 1)
	}
	return &models.ExecutionConfig{
		ExecutionType:   models.ExecutionType(strategy.ExecutionStrategy),
		ExecutionTime:   strategy.ExecutionTime.Format(time.TimeOnly),
		ExecutionDate:   strategy.ExecutionTime.Format(time.DateTime),
		RecurringType:   models.RecurringType(strategy.ExecutionRecurringType),
		PerDays:         int(strategy.ExecutionPerDays),
		DaysOfWeek:      DaysStrToSliceInt(strategy.ExecutionDaysOfWeek),
		DaysOfMonth:     daysOfMonth,
		StartDayOfMonth: daysOfMonth[0],
		EndDayOfMonth:   daysOfMonth[len(daysOfMonth)-1],
		CronExpression:  strategy.ExecutionCron,
		StartTime:       strategy.ExecutionStartedAt.Format(time.DateTime),
		EndTime:         strategy.ExecutionStoppedAt.Format(time.DateTime),
		Timezone:        strategy.ExecutionTimezone,
	}
}

// ConvertBaselineStrategyToStrategyItem 将 BaselineStrategy 转为 StrategyItem
func ConvertBaselineStrategyToStrategyItem(ctx context.Context, strategy *caas.BaselineStrategy) *models.StrategyItem {
	if strategy == nil {
		return nil
	}

	executionConfig := *ConvertBaselineStrategyToExecutionConfig(ctx, strategy)
	humanReadable := ConvertExecutionConfigToHumanReadable(ctx, executionConfig)
	item := models.StrategyItem{
		Id:                           strategy.ID,
		Name:                         strategy.Name,
		ExecutionConfigHumanReadable: humanReadable,
		ExecutionConfig:              FilterExecutionConfig(executionConfig),
		Enabled:                      strategy.Enabled == 1,
		CreateTime:                   strategy.CreateTime.Format(time.DateTime),
		UpdateTime:                   strategy.UpdateTime.Format(time.DateTime),
	}
	return &item
}

// SetReqExecutionConfigToBaselineStrategy 设置 ExecutionConfig 到 BaselineStrategy
func SetReqExecutionConfigToBaselineStrategy(_ context.Context, strategy *caas.BaselineStrategy, config *models.ExecutionConfig) error {
	if strategy == nil || config == nil {
		return nil
	}
	if config.ExecutionType == models.ExecutionTypeImmediate {
		strategy.ExecutionTime = time.Now()
	} else {
		if config.StartTime == "" {
			strategy.ExecutionStartedAt = time.Now()
		} else {
			startTime, err := ParseTime(config.StartTime)
			if err != nil {
				return fmt.Errorf("invalid start time format: %w", err)
			}
			strategy.ExecutionStartedAt = startTime
		}
		if config.ExecutionType == models.ExecutionTypeScheduled {
			executionDate, err := ParseUTCTime(config.ExecutionDate)
			if err != nil {
				return fmt.Errorf("invalid execution date format: %w", err)
			}
			strategy.ExecutionTime = executionDate
			strategy.ExecutionStoppedAt = executionDate
		} else if config.ExecutionType == models.ExecutionTypeRecurring {
			if config.RecurringType == models.RecurringTypeDaily ||
				config.RecurringType == models.RecurringTypeMonthly ||
				config.RecurringType == models.RecurringTypeWeekly {
				executionTime, err := ParseUTCTime(config.ExecutionTime)
				if err != nil {
					return fmt.Errorf("invalid execution time format: %w", err)
				}
				strategy.ExecutionTime = executionTime
			}
			endTime, err := ParseUTCTime(config.EndTime)
			if err != nil {
				return fmt.Errorf("invalid end time format: %w", err)
			}
			strategy.ExecutionStoppedAt = endTime
		}

	}
	strategy.ExecutionStrategy = string(config.ExecutionType)
	strategy.ExecutionRecurringType = string(config.RecurringType)
	strategy.ExecutionCron = config.CronExpression
	strategy.ExecutionRecurringType = string(config.RecurringType)
	strategy.ExecutionDaysOfWeek = strings.Join(lo.Map(config.DaysOfWeek, func(item int, _ int) string {
		return fmt.Sprint(item)
	}), ",")
	strategy.ExecutionDaysOfMonth = strings.Join(lo.Map(config.DaysOfMonth, func(item int, _ int) string {
		return fmt.Sprint(item)
	}), ",")
	strategy.ExecutionPerDays = int32(config.PerDays)
	strategy.ExecutionTimezone = lo.Ternary(config.Timezone == "", "UTC+08:00", config.Timezone)
	return nil
}

// ParseUTCTime ...
func ParseUTCTime(timeStr string) (time.Time, error) {
	// 使用 RFC3339 格式解析 UTC 时间字符串
	utcTime, err := time.Parse(time.RFC3339, timeStr)
	if err != nil {
		return time.Time{}, err
	}

	// 转换为本地时区时间
	localTime := utcTime.In(time.Local)
	return localTime, nil
}

// ParseTime 将时间字符串转为 time.Time
func ParseTime(timeStr string) (time.Time, error) {
	layoutList := []string{
		time.RFC3339,
		time.DateTime,
		time.TimeOnly,
	}
	var result time.Time
	var err error
	for _, layout := range layoutList {
		result, err = time.Parse(layout, timeStr)
		if err == nil {
			return result, nil
		}
	}
	return result, fmt.Errorf("invalid time format: %w, support format: %v", err, layoutList)
}

// ConvertExecutionConfigToCronExpressionWithSeconds 将 ExecutionConfig 转换为 Cron 表达式
func ConvertExecutionConfigToCronExpressionWithSeconds(e *models.ExecutionConfig) string {
	// Parse the ExecutionTime for hour, minute, day, month

	switch e.ExecutionType {
	case models.ExecutionTypeImmediate:
		return "" // No Cron for immediate execution
	case models.ExecutionTypeScheduled:
		if e.ExecutionDate != "" {
			t, err := ParseTime(e.ExecutionDate)
			if err != nil {
				return ""
			}
			// Extract the components from ExecutionTime
			day := t.Day()
			month := t.Month()
			hour := t.Hour()
			minute := t.Minute()
			second := t.Second()
			// For scheduled execution, use full date and time: minute hour day month
			// We don't need the year, so ignore it in the cron expression
			return fmt.Sprintf("%d %d %d %d %d *", second, minute, hour, day, int(month))
		}
	case models.ExecutionTypeRecurring:
		if e.ExecutionTime != "" {
			t, err := ParseTime(e.ExecutionTime)
			if err != nil {
				return ""
			}
			// Extract the components from ExecutionTime
			//day := t.Day()
			//month := t.Month()
			hour := t.Hour()
			minute := t.Minute()
			second := t.Second()
			switch e.RecurringType {
			case models.RecurringTypeDaily:
				return fmt.Sprintf("%d %d %d */%d * *", second, minute, hour, e.PerDays) // Daily execution
			case models.RecurringTypeWeekly:
				if len(e.DaysOfWeek) > 0 {
					// Convert DaysOfWeek to cron weekday format (0-6)
					return fmt.Sprintf("%d %d %d * * %s", second, minute, hour, strings.Join(lo.Map(e.DaysOfWeek,
						func(d int, _ int) string { return fmt.Sprintf("%d", d) }), ","))
				}
			case models.RecurringTypeMonthly:
				if len(e.DaysOfMonth) > 0 {
					// Use DaysOfMonth directly
					days := make([]string, len(e.DaysOfMonth))
					for i, d := range e.DaysOfMonth {
						days[i] = fmt.Sprintf("%d", d)
					}
					return fmt.Sprintf("%d %d %d %s * *", second, minute, hour, strings.Join(days, ","))
				}

			case models.RecurringTypeCron:
				// Return the CronExpression directly
				split := strings.Split(e.CronExpression, " ")
				if len(split) < 6 {
					newArr := make([]string, 6)
					newArr[0] = "0"
					for i := 1; i < 6; i++ {
						newArr[i] = split[i-1]
					}
					return strings.Join(newArr, " ")
				} else if len(split) == 6 {
					return e.CronExpression
				} else {
					return strings.Join(split[:6], " ")
				}
			}

		}

	}

	return ""
}

type UploadCheckerFileFunc func(ctx context.Context, fileName string, content string) (models.FileItem, error)

func ConvertV1CheckerAndMonitorToCheckerConfig(ctx context.Context, checker *checkerv1.Checker, monitor *checkerv1.Monitor,
	uploadCheckerFileFunc UploadCheckerFileFunc) (*models.CheckerConfig, error) {
	if monitor == nil {
		return nil, fmt.Errorf("monitor is nil")
	}
	config := &models.CheckerConfig{}
	switch monitor.Spec.Type {
	case inframonitor.MonitorTypeFile:
		config.CheckMode = models.CheckerModeFile
		if monitor.Spec.File != nil {
			nodeSelector := infra.ConvertInfraNodeSelectorToNodeSelector(&monitor.Spec.File.NodeSelector)
			if nodeSelector != nil {
				config.NodeSelector = nodeSelector
			}
			prefix := GetMonitorHostPathPrefix()
			filePath := lo.If(!strings.HasPrefix(monitor.Spec.File.Path, prefix), monitor.Spec.File.Path).
				Else(strings.TrimPrefix(monitor.Spec.File.Path, prefix))
			ext := filepath.Ext(filePath)
			config.File = &models.FileChecker{
				FileType: models.FileTypeTEXT,
				FileLocation: models.FileLocationConfig{
					Mode: models.FileLocationModeHostPath,
					Path: filePath,
				},
			}
			switch ext {
			case ".yaml", ".yml":
				config.File.FileType = models.FileTypeYAML
				if !utils.IsValidYaml(checker.Spec.ParseFile.MFileContent) {
					return nil, fmt.Errorf("invalid yaml file")
				}
			case ".json":
				config.File.FileType = models.FileTypeJSON
				if !utils.IsValidJson(checker.Spec.ParseFile.MFileContent) {
					return nil, fmt.Errorf("invalid json file")
				}
			default:
				config.File.FileType = models.FileTypeTEXT
			}
			if checker.Spec.ParseFile != nil {
				fileName := fmt.Sprintf("%s-mfile%s", checker.Name, ext)
				fileItem, err := uploadCheckerFileFunc(ctx, fileName, checker.Spec.ParseFile.MFileContent)
				if err != nil {
					return nil, err
				}
				config.File.FileContent = &fileItem
			} else if checker.Spec.CommonFile != nil {
				fileName := fmt.Sprintf("%s-mfile%s", checker.Name, ext)
				fileItem, err := uploadCheckerFileFunc(ctx, fileName, checker.Spec.CommonFile.Regex)
				if err != nil {
					return nil, err
				}
				config.File.FileContent = &fileItem
			}
		}
	case inframonitor.MonitorTypeK8sInstance:
		config.CheckMode = models.CheckerModeFile
		if monitor.Spec.K8sInstance != nil {
			config.File = &models.FileChecker{
				FileType: models.FileTypeYAML,
				FileLocation: models.FileLocationConfig{
					Mode: models.FileLocationModeK8sResource,
					K8sResourceRef: models.K8sResourceRef{
						APIVersion: monitor.Spec.K8sInstance.ApiVersion,
						Kind:       monitor.Spec.K8sInstance.Kind,
						Namespace:  monitor.Spec.K8sInstance.Namespace,
						Name:       monitor.Spec.K8sInstance.Name,
						Labels:     monitor.Spec.K8sInstance.Labels,
					},
				},
			}
			if checker.Spec.ParseFile != nil {
				fileName := fmt.Sprintf("%s-mfile.yaml", checker.Name)
				fileItem, err := uploadCheckerFileFunc(ctx, fileName, checker.Spec.ParseFile.MFileContent)
				if err != nil {
					return nil, err
				}
				config.File.FileContent = &fileItem
			}
		}
	case inframonitor.MonitorTypeCommand:
		nodeSelector := infra.ConvertInfraNodeSelectorToNodeSelector(&monitor.Spec.Command.NodeSelector)
		if nodeSelector != nil {
			config.NodeSelector = nodeSelector
		}
		config.CheckMode = models.CheckerModeCommand
		if monitor.Spec.Command != nil {
			config.Command = &models.CommandChecker{
				Command: strings.Join(monitor.Spec.Command.Exec, " "),
			}
			if checker.Spec.CommonFile != nil {
				config.Command.MatchType = models.MatchTypeFuzzy
				config.Command.MatchValue = checker.Spec.CommonFile.Regex
			}
			if monitor.Spec.Command.NodeSelector.NodeSelectorTerms != nil {
				var labelExpressions []string
				for _, term := range monitor.Spec.Command.NodeSelector.NodeSelectorTerms {
					term := term
					toStr, err := infra.NodeSelectorTermToString(term)
					if err != nil {
						logger.GetLogger().Debug("failed convert node selector term to string", zap.Any("term", term))
						continue
					}
					labelExpressions = append(labelExpressions, toStr)
				}
			}
		}
	}

	return config, nil
}

// ConvertCheckerAndMonitorToBaselineRule 将 Checker 和 Monitor 转换为 BaselineRule
func ConvertCheckerAndMonitorToBaselineRule(ctx context.Context, checker *checkerv1.Checker,
	monitor *checkerv1.Monitor, uploadCheckerFileFunc UploadCheckerFileFunc) (*caas.BaselineRule, error) {
	rule := &caas.BaselineRule{}
	checkerConfig, err := ConvertV1CheckerAndMonitorToCheckerConfig(ctx, checker, monitor, uploadCheckerFileFunc)
	if err != nil {
		return rule, err
	}
	if checkerConfig != nil {
		_ = SetCheckerConfigToBaselineRule(rule, checkerConfig)
		rule.CheckType = string(models.NewAggregationCheckMode(checkerConfig))
	}
	rule.RiskLevel = models.RiskLevelHigh

	if description, ok := GetObjectAnnotateAndLabelValue(checker, constants.BaselineDescriptionAnnotationKey); ok {
		rule.Description = description
	}
	if suggestion, ok := GetObjectAnnotateAndLabelValue(checker, constants.BaselineAdviceAnnotationKey); ok {
		rule.Suggestion = suggestion
	}
	if builtin, ok := GetObjectAnnotateAndLabelValue(checker, constants.BaselineInfraBuiltinLabelKey); ok && builtin == constants.BaselineInfraBuiltinLabelValueTrue {
		rule.Kind = models.BuiltinCheckerKind
		rule.Builtin = true
	}
	if strings.Contains(checker.Name, "kube") {
		rule.CheckResourceType = models.CheckerResourceTypeCoreComponent
	} else {
		rule.CheckResourceType = models.CheckerResourceTypeSystemComponent
	}
	if componentVersion, ok := GetObjectAnnotateAndLabelValue(checker, constants.BaselineComponentVersionLabelAnnotateKey); ok {
		// 组件版本
		rule.Version = componentVersion
	}
	if len(rule.Version) == 0 {
		rule.Name = checker.Name
	} else {
		if !strings.HasSuffix(checker.Name, rule.Version) {
			rule.Name = fmt.Sprintf("%s-%s", checker.Name, rule.Version)
		} else {
			rule.Name = checker.Name
		}
	}
	return rule, nil
}

// ConvertBaselineToBaselineStandard 将 Baseline 转换为 BaselineStandard
func ConvertBaselineToBaselineStandard(baseline *checkerv1.Baseline) *caas.BaselineStandard {
	standard := &caas.BaselineStandard{}
	standard.Name = baseline.Name
	if description, ok := GetObjectAnnotateAndLabelValue(baseline, constants.BaselineDescriptionAnnotationKey); ok {
		standard.Description = description
	}
	if builtin, ok := GetObjectAnnotateAndLabelValue(baseline, constants.BaselineInfraBuiltinLabelKey); ok && builtin == constants.BaselineInfraBuiltinLabelValueTrue {
		standard.Builtin = true
	}
	return standard
}

// ConvertCheckerToBaselineStandardRule 将 Checker 转换为 BaselineStandardRule
func ConvertCheckerToBaselineStandardRule(standardID int64,
	baselineRule *caas.BaselineRule) *caas.BaselineStandardRule {
	// TODO: 需要根据 checker 的类型来决定 rule 的类型
	// rule := &caas.BaselineStandardRule{}
	// rule.StandardID = standardID
	// rule.RuleID = baselineRule.ID
	// switch baselineRule.CheckType {
	// case string(models.CheckerModeFile):
	// 	rule.CheckValuePrefix = string(models.ValuePrefixFile)
	// 	rule.FileContentID = fileItem.Id
	// 	rule.FileContentPath = fileItem.Path
	// 	rule.FileContentUniqueKey = fileItem.UniqueKey
	// 	rule.FileContentName = fileItem.Name
	// 	rule.FileContentLink = fileItem.Link
	// case string(models.CheckerModeCommand):

	// 	rule.CheckValuePrefix = string(models.ValuePrefixInput)

	// }

	return nil
}
