package helper

import (
	"context"
	"errors"
	"fmt"

	"gorm.io/gorm"
	bizerrors "harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
)

// ValidateSaveBaselineRule 校验
func ValidateSaveBaselineRule(ctx context.Context, db *gorm.DB,
	rule *caas.BaselineRule) error {
	if rule.Name == "" {
		return bizerrors.NewFromCodeWithMessage(bizerrors.Var.ParamError, "name is required")
	}
	var rules []caas.BaselineRule
	tx := db.WithContext(ctx).Model(&caas.BaselineRule{}).
		Where("BINARY name = ?", rule.Name)
	if rule.ID > 0 {
		tx.Not("id = ?", rule.ID)
	}
	err := tx.Find(&rules).Error
	if errors.Is(err, gorm.ErrRecordNotFound) {
		return nil
	}
	if err != nil {
		return err
	}

	if len(rules) > 0 {
		return bizerrors.NewFromCodeWithMessage(bizerrors.Var.BaselineCustomCheckerNameRepeat,
			fmt.Sprintf("%v name repeat", rule.Name))
	}
	return nil
}

func ValidateCustomCheckerConfigFile(ctx context.Context, checkerConfig *models.CheckerConfig) error {

	if checkerConfig.CheckMode == models.CheckerModeFile && checkerConfig.File == nil {
		return bizerrors.NewFromCodeWithMessage(bizerrors.Var.ParamError, "file is required")
	}

	if checkerConfig.CheckMode == models.CheckerModeFile && checkerConfig.File != nil {
		if checkerConfig.File.FileType == "" {
			return bizerrors.NewFromCodeWithMessage(bizerrors.Var.ParamError, "fileType is required")
		}
		if checkerConfig.File.FileContent == nil {
			return bizerrors.NewFromCodeWithMessage(bizerrors.Var.ParamError, "fileContent is required")
		}
	}

	return nil

}
