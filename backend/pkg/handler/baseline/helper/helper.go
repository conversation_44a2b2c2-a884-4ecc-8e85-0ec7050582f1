package helper

import (
	"context"
	"errors"
	"fmt"
	"runtime"
	"strconv"
	"strings"
	"time"

	"bytes"
	"encoding/json"

	"github.com/minio/minio-go/v7"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"gorm.io/gorm"
	checkerv1 "harmonycloud.cn/baseline-checker/api/v1"
	clientmgr "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/addon"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline/infra"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
	translatecontext "harmonycloud.cn/unifiedportal/translate-sdk-golang/context"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/sets"
	ctrlclient "sigs.k8s.io/controller-runtime/pkg/client"
)

func SetCheckerConfigToChecker() {

}

type ExecutionStrategy string

func (e ExecutionStrategy) String() string {
	return string(e)
}

const (
	immediate ExecutionStrategy = "即时"
	recurring ExecutionStrategy = "周期"
	scheduled ExecutionStrategy = "定时"
)

func CalculateExecutionTime(strategy caas.BaselineStrategy) string {
	//TODO: 根据策略类型计算执行时间
	switch ExecutionStrategy(strategy.ExecutionStrategy) {
	case immediate:
		// 即时执行，返回开始时间
		if !time.Time.IsZero(strategy.ExecutionStartedAt) {
			return strategy.ExecutionStartedAt.Format(time.DateTime)
		}
		return ""
	case scheduled:
		// 延迟执行，计算延迟时间点
		delayedTime := time.Now().Add(time.Duration(strategy.ExecutionDeferredSeconds) * time.Second)
		return delayedTime.Format(time.DateTime)
	case recurring:
		return "" // 如果未设置开始时间，则返回空字符串
	default:
		// 未知策略类型
		return ""
	}
}

// GetLanguageCode 模拟语言获取
func GetLanguageCode(ctx context.Context) string {
	return translatecontext.GetLanguageCode(ctx)
}

// GetStrategyByID ...
func GetStrategyByID(ctx context.Context, db *gorm.DB, strategyId int64) (*caas.BaselineStrategy, error) {
	var strategy caas.BaselineStrategy
	err := db.WithContext(ctx).Model(&caas.BaselineStrategy{}).Where("id = ?", strategyId).First(&strategy).Error
	if err != nil {
		return nil, err
	}
	return &strategy, nil
}

// BuildStrategyJobTask 构建策略任务
func BuildStrategyJobTask(_ context.Context, record *caas.BaselineStrategy,
	clusterStandardsMap map[string][]caas.BaselineStandard,
	standardCheckList map[int64][]models.StandardCheckItem) *models.StrategyJobTask {
	strategyJobRecord := &caas.BaselineStrategyJob{
		StrategyID:   record.ID,
		ClusterNames: record.ClusterNames,
		Status:       string(models.JobStatusPending),
	}
	var stdJobTasks []*models.StandardJobTask
	var stdItems []models.ClusterStandardItem
	for clusterName, standards := range clusterStandardsMap {
		for _, standard := range standards {
			stdJobTask := models.StandardJobTask{}
			standardJobRecord := &caas.BaselineStandardJob{
				StrategyJobID: strategyJobRecord.ID,
				StrategyID:    record.ID,
				StandardID:    standard.ID,
				ClusterName:   clusterName,
				Passed:        false,
				Status:        string(models.JobStatusPending),
			}
			var checkJobRecords []*caas.BaselineStandardRuleJob
			for _, standardCheckItem := range standardCheckList[standard.ID] {
				checkJobRecord := &caas.BaselineStandardRuleJob{
					StrategyJobID:  strategyJobRecord.ID,
					StrategyID:     record.ID,
					StandardID:     standard.ID,
					RuleID:         standardCheckItem.ID,
					StandardRuleID: standardCheckItem.StandardCheckID,
					ClusterName:    clusterName,
					Status:         string(models.JobStatusPending),
					Passed:         false,
				}
				checkJobRecords = append(checkJobRecords, checkJobRecord)
			}
			stdJobTask.Job = standardJobRecord
			stdJobTask.CheckJobs = checkJobRecords
			stdJobTask.CheckItems = standardCheckList[standard.ID]
			stdJobTasks = append(stdJobTasks, &stdJobTask)
			clusterStandardItem := ConvertBaselineStandardToClusterStandardItem(clusterName, &standard)
			clusterStandardItem.CheckItems = standardCheckList[standard.ID]
			stdItems = append(stdItems, clusterStandardItem)
		}
	}
	jobTask := &models.StrategyJobTask{
		Record:               record,
		Job:                  strategyJobRecord,
		StandardJobs:         stdJobTasks,
		ClusterStandardItems: stdItems,
	}
	return jobTask
}

// InitCheckJobs ...
func InitCheckJobs(ctx context.Context, db *gorm.DB, checkJobs []*caas.BaselineStandardRuleJob) error {
	for _, checkJob := range checkJobs {
		if err := db.WithContext(ctx).Model(&caas.BaselineStandardRuleJob{}).Save(checkJob).Error; err != nil {
			return err
		}
	}
	return nil
}

// BuildCheckJobTask ...
func BuildCheckJobTask(_ context.Context,
	checkJobs []*caas.BaselineStandardRuleJob,
	standardCheckItems []models.StandardCheckItem) []*models.CheckJobTask {
	standardCheckItemsMap := make(map[int64]models.StandardCheckItem)
	for _, standardCheckItem := range standardCheckItems {
		standardCheckItemsMap[standardCheckItem.ID] = standardCheckItem
	}

	checkJobTasks := make([]*models.CheckJobTask, 0)
	for _, checkJob := range checkJobs {
		checkJobTask := &models.CheckJobTask{
			Job:       checkJob,
			CheckItem: standardCheckItemsMap[checkJob.RuleID],
		}
		checkJobTasks = append(checkJobTasks, checkJobTask)
	}
	return checkJobTasks
}

// InitStrategyJobTask 初始化策略任务，确保所有任务都成功初始化且关联关系正确
func InitStrategyJobTask(ctx context.Context, db *gorm.DB, jobTask *models.StrategyJobTask) error {
	// 参数验证
	if jobTask == nil || jobTask.Job == nil {
		return fmt.Errorf("invalid job task: job task or strategy job is nil")
	}

	// 开启事务执行数据写入
	// 1. 保存策略任务
	if err := db.WithContext(ctx).Create(jobTask.Job).Error; err != nil {
		return fmt.Errorf("failed to save strategy job: %w", err)
	}

	strategyJobID := jobTask.Job.ID // 保存策略任务ID用于关联

	// 2. 初始化标准任务
	for i := range jobTask.StandardJobs {
		if jobTask.StandardJobs[i] == nil || jobTask.StandardJobs[i].Job == nil {
			return fmt.Errorf("invalid standard job at index %d: job is nil", i)
		}

		// 设置策略任务ID
		jobTask.StandardJobs[i].Job.StrategyJobID = strategyJobID
		jobTask.StandardJobs[i].Job.StrategyID = jobTask.Job.StrategyID // 确保策略ID一致

		// 保存标准任务
		if err := db.WithContext(ctx).Create(jobTask.StandardJobs[i].Job).Error; err != nil {
			return fmt.Errorf("failed to save standard job: %w", err)
		}

		standardJobID := jobTask.StandardJobs[i].Job.ID // 保存标准任务ID用于关联

		// 3. 初始化检查任务
		for j := range jobTask.StandardJobs[i].CheckJobs {
			if jobTask.StandardJobs[i].CheckJobs[j] == nil {
				return fmt.Errorf("invalid check job at standard job %d, index %d: check job is nil", i, j)
			}

			// 设置关联ID
			jobTask.StandardJobs[i].CheckJobs[j].StandardJobID = standardJobID
			jobTask.StandardJobs[i].CheckJobs[j].StrategyJobID = strategyJobID
			jobTask.StandardJobs[i].CheckJobs[j].StrategyID = jobTask.Job.StrategyID
			jobTask.StandardJobs[i].CheckJobs[j].StandardID = jobTask.StandardJobs[i].Job.StandardID

			// 保存检查任务
			if err := db.WithContext(ctx).Create(jobTask.StandardJobs[i].CheckJobs[j]).Error; err != nil {
				return fmt.Errorf("failed to save check job for standard job %d: %w", standardJobID, err)
			}
		}
	}

	// 事务提交成功后进行数据一致性校验
	// 1. 验证标准任务数量
	var standardJobCount int64
	if err := db.WithContext(ctx).Model(&caas.BaselineStandardJob{}).
		Where("strategy_job_id = ?", jobTask.Job.ID).Count(&standardJobCount).Error; err != nil {
		return fmt.Errorf("failed to verify standard jobs count: %w", err)
	}

	if int(standardJobCount) != len(jobTask.StandardJobs) {
		return fmt.Errorf("data inconsistency after transaction: expected %d standard jobs, got %d",
			len(jobTask.StandardJobs), standardJobCount)
	}

	// 2. 验证每个标准任务的检查任务数量
	for _, standardJob := range jobTask.StandardJobs {
		var checkJobCount int64
		if err := db.WithContext(ctx).Model(&caas.BaselineStandardRuleJob{}).
			Where("standard_job_id = ?", standardJob.Job.ID).Count(&checkJobCount).Error; err != nil {
			return fmt.Errorf("failed to verify check jobs count for standard job %d: %w",
				standardJob.Job.ID, err)
		}

		if int(checkJobCount) != len(standardJob.CheckJobs) {
			return fmt.Errorf("data inconsistency after transaction: standard job %d expected %d check jobs, got %d",
				standardJob.Job.ID, len(standardJob.CheckJobs), checkJobCount)
		}
	}
	return nil
}

func GetStrategyClusterStandardsByIdAndClusters(ctx context.Context, db *gorm.DB, strategyId int64, clusterNames []string) (
	map[string][]caas.BaselineStandard, []int64, error) {

	tx := db.WithContext(ctx).Model(&caas.BaselineStrategyStandard{}).Where("strategy_id = ?", strategyId)
	if len(clusterNames) > 0 {
		tx = tx.Where("cluster_name in (?)", clusterNames)
	}
	var strategyStandards []caas.BaselineStrategyStandard
	tx.Find(&strategyStandards)
	clusterGroup := lo.GroupBy(strategyStandards, func(item caas.BaselineStrategyStandard) string {
		return item.ClusterName
	})
	uniqueStandardIds := sets.NewInt64()
	lo.ForEach(strategyStandards, func(item caas.BaselineStrategyStandard, index int) {
		uniqueStandardIds.Insert(item.StandardID)
	})
	// find caas.BaselineStandard
	var standards []caas.BaselineStandard
	if err := db.WithContext(ctx).Where("id in (?)", uniqueStandardIds.List()).Find(&standards).Error; err != nil {
		return nil, nil, err
	}
	// standards to map id -> caas.BaselineStandard
	standardsMap := make(map[int64]caas.BaselineStandard)
	for _, standard := range standards {
		standardsMap[standard.ID] = standard
	}
	clusterStandardsMap := make(map[string][]caas.BaselineStandard)
	for clusterName, list := range clusterGroup {
		standardIdSet := sets.NewInt64()
		for _, item := range list {
			if standard, ok := standardsMap[item.StandardID]; ok && !standardIdSet.Has(standard.ID) {
				standardIdSet.Insert(item.StandardID)
				clusterStandardsMap[clusterName] = append(clusterStandardsMap[clusterName], standard)
			}
		}
	}
	return clusterStandardsMap, uniqueStandardIds.UnsortedList(), nil
}

func GetStandardsByStrategyId(ctx context.Context, db *gorm.DB, strategyId int64) ([]caas.BaselineStandard, error) {
	var standards []caas.BaselineStrategyStandard
	err := db.WithContext(ctx).Where("strategy_id = ?", strategyId).Find(&standards).Error
	if err != nil {
		return nil, err
	}
	var standardIds []int64
	for _, standard := range standards {
		standardIds = append(standardIds, standard.StandardID)
	}

	var result []caas.BaselineStandard
	err = db.WithContext(ctx).Where("id in (?)", standardIds).Find(&result).Error
	if err != nil {
		return nil, err
	}

	return result, nil

}

func GetStandardCheckListByStandardIds(ctx context.Context, db *gorm.DB,
	standardIds []int64) (map[int64][]models.StandardCheckItem, error) {
	if len(standardIds) == 0 {
		return map[int64][]models.StandardCheckItem{}, nil
	}
	var standardRules []caas.BaselineStandardRule
	tx := db.WithContext(ctx).Model(&caas.BaselineStandardRule{})
	if len(standardIds) == 1 {
		tx = tx.Where("standard_id = ?", standardIds[0])
	} else {
		tx = tx.Where("standard_id in (?)", standardIds)
	}
	err := tx.Find(&standardRules).Error
	if err != nil {
		return nil, err
	}
	checkIdSet := sets.New(lo.Map(standardRules, func(item caas.BaselineStandardRule, _ int) int64 {
		return item.RuleID
	})...)

	var rules []caas.BaselineRule
	rTx := db.WithContext(ctx).Model(&caas.BaselineRule{})
	if checkIdSet.Len() != 0 {
		rTx.Where("id in (?)", checkIdSet.UnsortedList())
	}
	if err = rTx.Find(&rules).Error; err != nil {
		return nil, err
	}

	// build ruleIdMap
	ruleIdMap := make(map[int64]caas.BaselineRule)
	for _, rule := range rules {
		ruleIdMap[rule.ID] = rule
	}

	standardCheckMap := make(map[int64][]models.StandardCheckItem)
	for _, standardRule := range standardRules {
		rule := ruleIdMap[standardRule.RuleID]
		checkValue, _ := ConvertBaselineStandardRuleToCheckValue(&standardRule)
		standardCheckMap[standardRule.StandardID] = append(standardCheckMap[standardRule.StandardID], models.StandardCheckItem{
			CheckItem:       *ConvertBaselineRuleToCheckItem(&rule),
			StandardID:      standardRule.StandardID,
			StandardCheckID: standardRule.ID,
			CheckValue:      *checkValue,
			Suggestion:      rule.Suggestion,
		})
	}
	return standardCheckMap, nil
}

// BuildStandardCheckerParam 构建标准检查
func BuildStandardCheckerParam(ctx context.Context, clusterName string,
	strategyId, standardId int64,
	monitorName string,
	checkItem models.StandardCheckItem) models.CheckerParam {
	return models.CheckerParam{
		ClusterName:     clusterName,
		MonitorName:     monitorName,
		StrategyID:      strategyId,
		StandardID:      standardId,
		CheckID:         checkItem.ID,
		StandardCheckID: checkItem.StandardCheckID,
		CheckRawData: models.CheckRawData{
			Value:  checkItem.CheckValue,
			Config: *checkItem.CheckerConfig,
		},
	}
}

// BuildStandardCheckerParams 构建标准检查
func BuildStandardCheckerParams(_ context.Context, clusterName string,
	strategyId, standardId int64,
	checkItems []models.StandardCheckItem) []models.CheckerParam {
	var items []models.CheckerParam
	for _, checkItem := range checkItems {
		param := models.CheckerParam{
			ClusterName: clusterName,
			StrategyID:  strategyId,
			StandardID:  standardId,
			CheckID:     checkItem.ID,
			CheckRawData: models.CheckRawData{
				Value:  checkItem.CheckValue,
				Config: *checkItem.CheckerConfig,
			},
		}
		items = append(items, param)
	}
	return items
}

// SetCheckerNameToCheckJob 设置检查名称
func SetCheckerNameToCheckJob(ctx context.Context, db *gorm.DB,
	checkJobs []*caas.BaselineStandardRuleJob,
	checkItems []*models.InfraCheckerItem) error {
	// checkItems to map by checkId
	checkIdMap := make(map[int64]*models.InfraCheckerItem)
	for i := range checkItems {
		checkItem := checkItems[i]
		checkIdMap[checkItem.CheckID] = checkItem
	}
	for i := range checkJobs {
		checkJob := checkJobs[i]
		if checkItem, ok := checkIdMap[checkJob.RuleID]; ok {
			checkJob.CheckerName = checkItem.CheckerName
			tx := db.WithContext(ctx).Model(&caas.BaselineStandardRuleJob{})
			if checkJob.ID != 0 {
				tx.Where("id = ?", checkJob.ID)
			}
			if err := tx.Save(checkJob).Error; err != nil {
				_ = UpdateCheckJobStatusWithPassedAndReasonWithMessage(ctx, db, checkJobs[i],
					models.JobStatusCompleted, lo.ToPtr(false),
					lo.ToPtr(models.JobReasonSyncCheckerError),
					err.Error())
				return err
			}
		}
	}
	return nil
}

// SyncStandardCheckers 同步标准检查
func SyncStandardCheckers(ctx context.Context,
	log *zap.Logger,
	db *gorm.DB,
	checkerAdapter infra.CheckerAdapter,
	task *models.StandardJobTask) ([]*models.InfraCheckerItem, error) {
	clusterName := task.Job.ClusterName
	strategyId := task.Job.StrategyID
	standardId := task.Job.StandardID
	standardJobId := task.Job.ID
	checkJobs := task.CheckJobs
	checkItems := task.CheckItems
	if len(checkJobs) == 0 {
		return nil, nil
	}
	// ruleId -> standardCheckItem
	checkItemMap := make(map[int64]models.StandardCheckItem)

	for _, checkItem := range checkItems {
		checkItemMap[checkItem.ID] = checkItem
	}

	var infraCheckers []*models.InfraCheckerItem
	var errs []error
	for i, checkJob := range checkJobs {
		if checkItem, ok := checkItemMap[checkJob.RuleID]; ok {
			param := BuildStandardCheckerParam(ctx, clusterName, strategyId, standardId,
				checkJob.MonitorName, checkItem)
			param.StandardJobID = standardJobId
			param.CheckJobID = checkJob.ID
			infraChecker, err := checkerAdapter.CreateChecker(ctx, &param)
			if err != nil {
				_ = UpdateCheckJobStatusWithPassedAndReasonWithMessage(ctx, db, checkJobs[i],
					models.JobStatusCompleted, lo.ToPtr(false),
					lo.ToPtr(models.JobReasonCheckerCreateError),
					err.Error())
				log.With(zap.Any("check-job-id", checkJobs[i].ID)).With(zap.Any("check-rule-id", checkJobs[i].RuleID)).
					Warn("create checker error", zap.Error(err))
				errs = append(errs, err)
				continue
			}
			infraCheckers = append(infraCheckers, infraChecker)
		} else {
			_ = UpdateCheckJobStatusWithPassedAndReasonWithMessage(ctx, db, checkJobs[i],
				models.JobStatusCompleted, lo.ToPtr(false), lo.ToPtr(models.JobCheckItemsMissing),
				"check item missing in check items")
			errs = append(errs, fmt.Errorf("check item missing in check items"))
		}
	}
	if len(errs) > 0 {
		return nil, errors.Join(errs...)
	}
	return infraCheckers, nil
}

// SyncStandardMonitors 同步monitor
func SyncStandardMonitors(ctx context.Context,
	log *zap.Logger,
	db *gorm.DB,
	monitorAdapter infra.MonitorAdapter,
	strategy caas.BaselineStrategy,
	task *models.StandardJobTask) ([]*models.InfraMonitorItem, error) {

	strategyId := strategy.ID
	standardId := task.Job.StandardID
	checkJobs := task.CheckJobs
	checkItems := task.CheckItems
	if len(checkJobs) == 0 {
		return nil, nil
	}
	// ruleId -> standardCheckItem
	checkItemMap := make(map[int64]models.StandardCheckItem)
	for _, checkItem := range checkItems {
		checkItemMap[checkItem.StandardCheckID] = checkItem
	}
	var monitorItems []*models.InfraMonitorItem
	var errs []error

	// ListMonitor
	monitorItems, err := monitorAdapter.ListMonitor(ctx, &models.MonitorParam{
		ClusterName: checkJobs[0].ClusterName,
	})
	if err != nil {
		return nil, err
	}
	// monitorItems map by standardId strategyId checkId clusterName
	monitorItemsMap := lo.SliceToMap(monitorItems, func(item *models.InfraMonitorItem) (string, *models.InfraMonitorItem) {
		return fmt.Sprintf("%d/%s", item.CheckID, item.ClusterName), item
	})

	for i, checkJob := range checkJobs {
		if checkItem, ok := checkItemMap[checkJob.StandardRuleID]; ok {
			monitorParam := BuildMonitorParam(ctx, checkJob.ClusterName, strategyId, standardId, checkItem)
			monitorKey := fmt.Sprintf("%d/%s", checkJob.RuleID, checkJob.ClusterName)

			// 如果存在已监控项，更新监控名称
			if monitorItem, ok := monitorItemsMap[monitorKey]; ok {
				monitorParam.MonitorName = monitorItem.MonitorName
			}
			// 创建或更新监控
			monitorItem, err := monitorAdapter.CreateMonitor(ctx, &monitorParam)
			if err != nil {
				_ = UpdateCheckJobStatusWithPassedAndReasonWithMessage(ctx, db, checkJobs[i],
					models.JobStatusCompleted, lo.ToPtr(false),
					lo.ToPtr(models.JobReasonMonitorCreateError),
					err.Error())
				log.With(zap.Any("check-job-id", checkJobs[i].ID)).With(zap.Any("check-rule-id", checkJobs[i].RuleID)).
					Warn("create monitor error", zap.Error(err))
				errs = append(errs, err)
				continue
			}
			checkJobs[i].MonitorName = monitorItem.MonitorName
			monitorItems = append(monitorItems, monitorItem)
		} else {
			_ = UpdateCheckJobStatusWithPassedAndReasonWithMessage(ctx, db, checkJobs[i],
				models.JobStatusCompleted, lo.ToPtr(false), lo.ToPtr(models.JobCheckItemsMissing),
				"check item missing in check items")
			errs = append(errs, fmt.Errorf("check item missing in check items"))
		}
	}
	if len(errs) > 0 {
		return nil, errors.Join(errs...)
	}
	return monitorItems, nil
}

func BuildMonitorParam(ctx context.Context, clusterName string,
	strategyId, standardId int64,
	standardCheckItem models.StandardCheckItem) models.MonitorParam {
	monitorParam := models.MonitorParam{
		ClusterName:     clusterName,
		StandardID:      standardId,
		StrategyID:      strategyId,
		CheckID:         standardCheckItem.ID,
		StandardCheckID: standardCheckItem.StandardCheckID,
		CheckerConfig:   *standardCheckItem.CheckerConfig,
	}
	return monitorParam
}

// BuildMonitorParams ...
func BuildMonitorParams(_ context.Context, strategyId int64,
	clusterStandardsMap map[string][]caas.BaselineStandard,
	standardCheckList map[int64][]models.StandardCheckItem) []models.MonitorParam {
	monitorParams := make([]models.MonitorParam, 0)
	for clusterName, clusterStandards := range clusterStandardsMap {
		for _, standard := range clusterStandards {
			standardCheckItems := standardCheckList[standard.ID]
			for _, checkItem := range standardCheckItems {
				monitorParam := models.MonitorParam{
					ClusterName:     clusterName,
					StandardID:      standard.ID,
					StrategyID:      strategyId,
					CheckID:         checkItem.ID,
					StandardCheckID: checkItem.StandardCheckID,
					CheckerConfig:   *checkItem.CheckerConfig,
				}

				monitorParams = append(monitorParams, monitorParam)
			}
		}
	}

	return monitorParams
}

// UpdateStrategyJobStatusWithPassedAndReasonWithMessage 更新策略任务状态
func UpdateStrategyJobStatusWithPassedAndReasonWithMessage(ctx context.Context, db *gorm.DB,
	strategyJob *caas.BaselineStrategyJob,
	jobStatus models.JobStatus,
	passed *bool,
	reason *string,
	message string) error {
	if strategyJob.CompletedTime.IsZero() && jobStatus == models.JobStatusCompleted {
		strategyJob.CompletedTime = time.Now()
	}
	strategyJob.Status = string(jobStatus)

	if passed != nil {
		strategyJob.Passed = *passed
	}
	if reason != nil {
		strategyJob.Reason = *reason
	}
	if message != "" {
		strategyJob.Message = message
	}
	tx := db.WithContext(ctx)
	if strategyJob.ID != 0 {
		if err := tx.Omit("id", "strategy_id", "cluster_names", "create_user", "create_time").
			Updates(strategyJob).Error; err != nil {
			logger.GetSugared().Debugw("update strategy job failed", "job", strategyJob, "error", err)
			return fmt.Errorf("update strategy job failed, %w", err)
		}
	} else {
		if err := tx.Save(strategyJob).Error; err != nil {
			logger.GetSugared().Debugw("update strategy job failed", "job", strategyJob, "error", err)
			return fmt.Errorf("update strategy job failed, %w", err)
		}
	}

	return nil
}

// UpdateStandardJobStatusWithPassedAndReasonAndMessage 更新策略任务状态
func UpdateStandardJobStatusWithPassedAndReasonAndMessage(ctx context.Context, db *gorm.DB,
	standardJob *caas.BaselineStandardJob,
	jobStatus models.JobStatus,
	passed *bool,
	reason *string,
	message string) error {
	if standardJob.CompletedTime.IsZero() && jobStatus == models.JobStatusCompleted {
		standardJob.CompletedTime = time.Now()
	}
	standardJob.Status = string(jobStatus)

	if passed != nil {
		standardJob.Passed = *passed
	}
	if reason != nil {
		standardJob.Reason = *reason
	}
	if message != "" {
		standardJob.Message = message
	}
	tx := db.WithContext(ctx)
	if standardJob.ID != 0 {
		omittedFields := []string{"id", "strategy_job_id", "strategy_id", "standard_id", "cluster_name", "result", "request", "create_user", "create_time"}
		if err := tx.Omit(omittedFields...).Updates(standardJob).Error; err != nil {
			logger.GetSugared().Debugw("update standard job failed", "job", standardJob, "error", err)
			return fmt.Errorf("update standard job failed, %w", err)
		}
	} else {
		if err := tx.Save(standardJob).Error; err != nil {
			logger.GetSugared().Debugw("update standard job failed", "job", standardJob, "error", err)
			return fmt.Errorf("update standard job failed, %w", err)
		}
	}
	return nil
}

// UpdateCheckJobStatusWithPassedAndReasonWithMessage 更新策略任务状态
func UpdateCheckJobStatusWithPassedAndReasonWithMessage(ctx context.Context, db *gorm.DB, checkJob *caas.BaselineStandardRuleJob, jobStatus models.JobStatus, passed *bool, reason *string, message string) error {
	if checkJob.CompletedTime.IsZero() && jobStatus == models.JobStatusCompleted {
		checkJob.CompletedTime = time.Now()
	}
	checkJob.Status = string(jobStatus)

	if passed != nil {
		checkJob.Passed = *passed
	}
	if reason != nil {
		checkJob.Reason = *reason
	}
	if message != "" {
		checkJob.Message = message
	}
	tx := db.WithContext(ctx)
	if checkJob.ID != 0 {
		if err := tx.Omit("id", "strategy_job_id", "standard_job_id", "strategy_id", "standard_id", "rule_id", "standard_rule_id", "cluster_name", "create_user", "create_time").
			Updates(checkJob).Error; err != nil {
			logger.GetSugared().Debugw("update check job failed", "job", checkJob, "error", err)
			return fmt.Errorf("update check job failed, %w", err)
		}
	} else {
		if err := tx.Save(checkJob).Error; err != nil {
			logger.GetSugared().Debugw("update check job failed", "job", checkJob, "error", err)
			return fmt.Errorf("update check job failed, %w", err)
		}
	}
	return nil
}

// UpdateStrategyJobCheckStatus 更新策略任务状态
func UpdateStrategyJobCheckStatus(_ context.Context,
	strategyJob *caas.BaselineStrategyJob,
	standardJobs []*caas.BaselineStandardJob,
	standardItems []models.ClusterStandardItem) error {
	var (
		clusterNameSet               = sets.NewString(strings.Split(strategyJob.ClusterNames, ",")...)
		clusterRiskNames             = sets.NewString()
		clusterNoRiskNames           = sets.NewString()
		clusterFailedNames           = sets.NewString()
		clusterUncheckedNames        = sets.NewString()
		passedStandardIds            = sets.NewInt64()
		unpassedStandardIds          = sets.NewInt64()
		uncheckedClusterStandardKeys = sets.NewString()
		checkUnpassedCriticalCnt     = int32(0)
		checkUnpassedHighCnt         = int32(0)
		checkUnpassedMediumCnt       = int32(0)
		checkUnpassedLowCnt          = int32(0)
		checkUncheckedCnt            = int32(0)
		checkCnt                     = int32(0)
		checkPassedCnt               = int32(0)
	)
	keyFn := func(clusterName string, standardId int64) string {
		return fmt.Sprintf("%s/%d", clusterName, standardId)
	}
	// build standard job map by keyFn
	standardJobsMap := make(map[string]*caas.BaselineStandardJob)
	for _, standardJob := range standardJobs {
		key := keyFn(standardJob.ClusterName, standardJob.StandardID)
		standardJobsMap[key] = standardJob
	}
	clusterStandardMap := make(map[string][]models.ClusterStandardItem)
	for _, standardItem := range standardItems {
		clusterStandardMap[standardItem.ClusterName] = append(clusterStandardMap[standardItem.ClusterName], standardItem)
		key := keyFn(standardItem.ClusterName, standardItem.ID)
		if standardJob, ok := standardJobsMap[key]; ok {
			checkUnpassedHighCnt += standardJob.CheckUnpassedHighCount
			checkUnpassedMediumCnt += standardJob.CheckUnpassedMediumCount
			checkUnpassedLowCnt += standardJob.CheckUnpassedLowCount
			checkPassedCnt += standardJob.PassedCount
			checkCnt += standardJob.CheckCount
			checkUncheckedCnt += standardJob.UncheckedCount
			if standardJob.Passed {
				passedStandardIds.Insert(standardJob.ID)
			} else {
				unpassedStandardIds.Insert(standardJob.ID)
			}
		} else {
			checkCnt += int32(len(standardItem.CheckItems))
			for _, checkItem := range standardItem.CheckItems {
				switch checkItem.RiskLevel {
				case models.RiskLevelHigh:
					checkUnpassedHighCnt++
				case models.RiskLevelMedium:
					checkUnpassedMediumCnt++
				case models.RiskLevelLow:
					checkUnpassedLowCnt++
				}
			}
			checkUncheckedCnt += int32(len(standardItem.CheckItems))
			uncheckedClusterStandardKeys.Insert(key)
		}
	}

	for _, clusterName := range clusterNameSet.List() {
		if standardItems, ok := clusterStandardMap[clusterName]; ok {
			passedCnt := 0
			failedCnt := 0
			notPassedCnt := 0
			uncheckedCnt := 0
			for _, standardItem := range standardItems {
				key := keyFn(clusterName, standardItem.ID)
				if standardJob, ok := standardJobsMap[key]; ok {
					if standardJob.Passed {
						passedCnt++
					} else {
						if standardJob.Reason == models.JobReasonClusterError {
							failedCnt++
						} else {
							notPassedCnt++
						}
					}
				} else {
					uncheckedCnt++
				}
			}
			if failedCnt > 0 {
				clusterFailedNames.Insert(clusterName)
			} else if notPassedCnt > 0 {
				clusterRiskNames.Insert(clusterName)
			} else if uncheckedCnt > 0 && uncheckedCnt == len(standardItems) {
				clusterUncheckedNames.Insert(clusterName)
			} else {
				clusterNoRiskNames.Insert(clusterName)
			}
		} else {
			clusterUncheckedNames.Insert(clusterName)
		}
	}

	strategyJob.ClusterCount = int32(clusterNameSet.Len())
	strategyJob.ClusterNoRiskCount = int32(clusterNoRiskNames.Len())
	strategyJob.ClusterRiskCount = int32(clusterRiskNames.Len())
	strategyJob.ClusterFailedCount = int32(clusterFailedNames.Len())
	strategyJob.ClusterUncheckedCount = int32(clusterUncheckedNames.Len())
	strategyJob.CheckCount = checkCnt
	strategyJob.CheckUncheckedCount = checkUncheckedCnt
	strategyJob.CheckPassedCount = checkPassedCnt
	strategyJob.CheckUnpassedCriticalCount = checkUnpassedCriticalCnt
	strategyJob.CheckUnpassedHighCount = checkUnpassedHighCnt
	strategyJob.CheckUnpassedMediumCount = checkUnpassedMediumCnt
	strategyJob.CheckUnpassedLowCount = checkUnpassedLowCnt
	strategyJob.StandardCount = int32(len(standardItems))
	strategyJob.StandardUncheckedCount = int32(uncheckedClusterStandardKeys.Len())
	strategyJob.StandardPassedCount = int32(passedStandardIds.Len())
	strategyJob.StandardUnpassedCount = int32(unpassedStandardIds.Len())
	return nil
}

// UpdateStandardJobCheckStatus 更新策略任务状态
func UpdateStandardJobCheckStatus(_ context.Context,
	standardJob *caas.BaselineStandardJob,
	checkJobs []*caas.BaselineStandardRuleJob,
	standardCheckItems []models.StandardCheckItem) error {
	var (
		passedCheckCnt         = 0
		uncheckedCnt           = 0
		unPassedHighCheckCnt   = 0
		unPassedMediumCheckCnt = 0
		unPassedLowCheckCnt    = 0
	)
	// 1. first map standard check by id
	checkJobMap := make(map[int64]*caas.BaselineStandardRuleJob)
	for i, checkJob := range checkJobs {
		checkJobMap[checkJob.StandardRuleID] = checkJobs[i]
	}

	for _, standardCheckItem := range standardCheckItems {
		setRiskCntFn := func(riskLevel models.RiskLevel) {
			switch riskLevel {
			case models.RiskLevelHigh:
				unPassedHighCheckCnt++
			case models.RiskLevelMedium:
				unPassedMediumCheckCnt++
			case models.RiskLevelLow:
				unPassedLowCheckCnt++
			}
		}
		if checkJob, ok := checkJobMap[standardCheckItem.StandardCheckID]; ok && checkJob != nil {
			if checkJob.Passed {
				passedCheckCnt++
			} else {
				setRiskCntFn(standardCheckItem.RiskLevel)
			}
		} else {
			uncheckedCnt++
			setRiskCntFn(standardCheckItem.RiskLevel)
		}
	}

	// 2. update standard job status
	standardJob.UncheckedCount = int32(uncheckedCnt)
	standardJob.CheckUnpassedHighCount = int32(unPassedHighCheckCnt)
	standardJob.CheckUnpassedMediumCount = int32(unPassedMediumCheckCnt)
	standardJob.CheckUnpassedLowCount = int32(unPassedLowCheckCnt)
	standardJob.PassedCount = int32(passedCheckCnt)
	standardJob.CheckCount = int32(len(standardCheckItems))

	setStandardJobStatusFromCheckCntInfo(standardJob, unPassedHighCheckCnt,
		unPassedMediumCheckCnt, unPassedLowCheckCnt, passedCheckCnt, len(standardCheckItems))
	return nil
}

func setStandardJobStatusFromCheckCntInfo(standardJob *caas.BaselineStandardJob, unPassedHigh, unPassedMedium, unPassedLow, passedCount, totalCount int) {
	setFailedFn := func(message string) {
		if standardJob.Reason == "" {
			standardJob.Reason = models.JobCheckItemsNotPass
			standardJob.Message = message
		}
		standardJob.Passed = false
	}
	if unPassedHigh > 0 {
		msg := fmt.Sprintf("%d high risk items not passed", unPassedHigh)
		setFailedFn(msg)
	} else if unPassedMedium > 0 {
		msg := fmt.Sprintf("%d medium risk items not passed", unPassedMedium)
		setFailedFn(msg)
	} else if unPassedLow > 0 {
		msg := fmt.Sprintf("%d low risk items not passed", unPassedLow)
		setFailedFn(msg)
	} else if passedCount != totalCount {
		msg := fmt.Sprintf("%d check items not passed", passedCount)
		setFailedFn(msg)
	} else {
		standardJob.Passed = standardJob.Reason == ""
	}
}

// CheckClusterOnline 检查集群是否在线
func CheckClusterOnline(_ context.Context, clusterName string) bool {
	cluster, err := clientmgr.GetCluster(clusterName)
	if _, err := clientmgr.OnlineClusterAssert(cluster, err); err != nil {
		return false
	}
	return true
}

// CheckBaselineCheckerInstalled 检查baseline checker是否安装
func CheckBaselineCheckerInstalled(ctx context.Context, addonHandler addon.Handler, clusterName string) (bool, error) {
	// check baseline namespace exists
	component, err := addonHandler.GetComponent(ctx, clusterName, constants.BASELINE_CHECKER.EnName, false)
	if err != nil {
		return false, fmt.Errorf("baseline checker addon get failed, error: %w", err)
	}
	if component == nil {
		return false, fmt.Errorf("baseline checker addon not found")
	}
	if component.Status != constants.RUNNING {
		return false, fmt.Errorf("baseline checker addon not running")
	}
	if component.BaselineCheckerConfig.Ip == "" ||
		component.BaselineCheckerConfig.Port == 0 ||
		component.BaselineCheckerConfig.Protocol == "" {
		return false, fmt.Errorf("connect ip, port, protocol is empty, please configure the cluster %s baseline-checker addon", clusterName)
	}
	return true, nil
}

// GenerateRecurringJobId 生成定时检查id
func GenerateRecurringJobId(strategyId int64) string {
	return "baseline-recurring-job-" + strconv.FormatInt(strategyId, 10)
}

// GenerateStrategyBaselineConfigName 生成策略基线配置
func GenerateStrategyBaselineConfigName(strategyId int64) string {
	return "strategy-config-" + strconv.FormatInt(strategyId, 10)
}

// DeepEqualBaselineStandardRuleCheckValue ...
func DeepEqualBaselineStandardRuleCheckValue(s, t *caas.BaselineStandardRule) bool {
	if s == nil && t == nil {
		return true
	} else if t == nil || s == nil {
		return false
	} else {
		if s.CheckValueContent != t.CheckValueContent {
			return false
		}
		if s.CheckValuePrefix != t.CheckValuePrefix {
			return false
		}
		if s.RiskLevel != t.RiskLevel {
			return false
		}
		if s.FileContentName != t.FileContentName {
			return false
		}
		if s.FileContentID != t.FileContentID {
			return false
		}
		if s.FileContentPath != t.FileContentPath {
			return false
		}
		if s.FileContentLink != t.FileContentLink {
			return false
		}
		if s.FileContentUniqueKey != t.FileContentUniqueKey {
			return false
		}
	}
	return true
}

// NewSelectedCheckerInfo ...
func NewSelectedCheckerInfo() *models.SelectedCheckInfo {
	return &models.SelectedCheckInfo{
		CheckIds:         make([]int64, 0),
		StandardCheckIds: make([]int64, 0),
		Checkers:         make([]models.SimpleBaselineChecker, 0),
	}
}

// CheckIfReasonCauseError 检查原因是否导致错误
func CheckIfReasonCauseError(reason string) bool {
	switch reason {
	case models.JobReasonBaselineCreateError,
		models.JobReasonCheckerCreateError,
		models.JobReasonMonitorCreateError,
		models.JobReasonWaitMonitorsError,
		models.JobReasonGetReportError,
		models.JobReasonCheckTimeout,
		models.JobReasonClusterError,
		models.JobReasonAddonAbnormalError,
		models.JobReasonSystemAbort:
		return true
	default:
		return false
	}
}

// CheckIfNeedUpdateStrategyJobSummary 检查策略是否需要更新
func CheckIfNeedUpdateStrategyJobSummary(ctx context.Context, db *gorm.DB, strategyJob *caas.BaselineStrategyJob) (bool, error) {
	// if not complted no need update
	if strategyJob.Status == "" || strategyJob.Status == string(models.JobStatusCompleted) {
		return true, nil
	}
	startTime := strategyJob.CreateTime
	// 检查策略的更新时间是否比job更新时间晚
	// 如果比job更新时间晚,返回true
	strategy, err := GetStrategyByID(ctx, db, strategyJob.StrategyID)
	if err != nil {
		return false, err
	}
	if strategy.CreateTime.IsZero() || strategy.UpdateTime.After(startTime) {
		return true, nil
	}
	oldClusterNames := sets.NewString(strings.Split(strategyJob.ClusterNames, ",")...)
	newClusterNames := sets.NewString(strings.Split(strategy.ClusterNames, ",")...)
	if !oldClusterNames.Equal(newClusterNames) {
		return true, nil
	}
	// 策略包含的标准更新时间是否比job更新时间晚
	var strategyStandards []*caas.BaselineStrategyStandard
	if err := db.Where("strategy_id = ?", strategy.ID).Distinct("standard_id").
		Find(&strategyStandards).Error; err != nil {
		return false, err
	}
	standardIds := sets.NewInt64()
	for _, strategyStandard := range strategyStandards {
		standardIds = standardIds.Insert(strategyStandard.StandardID)
	}
	var standards []*caas.BaselineStandard
	if err := db.Where("id in (?)", standardIds.List()).Find(&standards).Error; err != nil {
		return false, err
	}
	for _, standard := range standards {
		if standard.UpdateTime.After(startTime) {
			return true, nil
		}
	}
	var standardJobs []*caas.BaselineStandardJob
	if err := db.Where("strategy_job_id = ?", strategyJob.ID).
		Distinct("standard_id").
		Select("standard_id").Find(&standardJobs).Error; err != nil {
		return false, err
	}
	jobStandardIds := sets.NewInt64()
	for _, standardJob := range standardJobs {
		jobStandardIds = jobStandardIds.Insert(standardJob.StandardID)
	}
	if !jobStandardIds.Equal(standardIds) {
		return true, nil
	}
	return false, nil
}

func UpdateStrategyJobSummary(ctx context.Context, log *zap.Logger, db *gorm.DB, strategyJob *caas.BaselineStrategyJob) error {
	startTime := strategyJob.CreateTime
	strategy, err := GetStrategyByID(ctx, db, strategyJob.StrategyID)
	if err != nil {
		return err
	}
	var strategyStandards []*caas.BaselineStrategyStandard
	if err := db.Where("strategy_id = ?", strategyJob.StrategyID).Distinct("standard_id", "cluster_name").
		Find(&strategyStandards).Error; err != nil {
		return err
	}
	standardIds := sets.NewInt64()
	clusterNames := sets.NewString()
	for _, strategyStandard := range strategyStandards {
		standardIds = standardIds.Insert(strategyStandard.StandardID)
		clusterNames = clusterNames.Insert(strategyStandard.ClusterName)
	}

	var standards []*caas.BaselineStandard
	if err := db.Where("id in (?)", standardIds.List()).Find(&standards).Error; err != nil {
		return err
	}
	// id -> standard
	standardIdMap := make(map[int64]*caas.BaselineStandard)
	for _, standard := range standards {
		standardIdMap[standard.ID] = standard
	}

	var standardJobs []*caas.BaselineStandardJob
	if err := db.Where("strategy_job_id = ?", strategyJob.ID).Find(&standardJobs).Error; err != nil {
		return err
	}
	var checkJobs []*caas.BaselineStandardRuleJob
	if err := db.WithContext(ctx).Model(&caas.BaselineStandardRuleJob{}).
		Where("strategy_id = ?", strategy.ID).
		Where("strategy_job_id = ? ", strategyJob.ID).
		Where("create_time >= ?", startTime).
		Find(&checkJobs).Error; err != nil {
		return err
	}

	checkJobGroupByStandardJobId := lo.GroupBy(checkJobs, func(item *caas.BaselineStandardRuleJob) int64 {
		return item.StandardJobID
	})

	var standardRules []*caas.BaselineStandardRule
	if err := db.WithContext(ctx).Model(&caas.BaselineStandardRule{}).
		Where("standard_id in (?)", standardIds.List()).
		Find(&standardRules).Error; err != nil {
		return err
	}

	standardRulesGroupByStandardId := lo.GroupBy(standardRules, func(item *caas.BaselineStandardRule) int64 {
		return item.StandardID
	})

	var clusterStandards []models.ClusterStandardItem
	for _, strategyStandard := range strategyStandards {
		standard := standardIdMap[strategyStandard.StandardID]
		if standard == nil {
			continue
		}
		item := ConvertBaselineStandardToClusterStandardItem(strategyStandard.ClusterName, standard)
		item.CheckItems = make([]models.StandardCheckItem, 0)
		for _, standardRule := range standardRulesGroupByStandardId[strategyStandard.StandardID] {
			checkItem := ConvertBaselineStandardRuleToStandardCheckItem(standardRule)
			item.CheckItems = append(item.CheckItems, checkItem)
		}
		clusterStandards = append(clusterStandards, item)
	}

	for i, standardJob := range standardJobs {
		checkJobs := checkJobGroupByStandardJobId[standardJob.ID]
		checkItems := standardRulesGroupByStandardId[standardJob.StandardID]
		need, err := CheckIfNeedUpdateStandardJobSummary(ctx, standardJob, checkJobs, checkItems)
		if err != nil {
			log.Error("check if need update standard job summary failed", zap.Error(err))
			return err
		}
		if need {
			if err := UpdateStandardJobSummary(ctx, db, standardJobs[i], checkJobs, checkItems); err != nil {
				log.Error("update standard job summary failed", zap.Error(err))
				return err
			}
		}
	}
	oldStatus := strategyJob.Status
	strategyJob.ClusterNames = strategy.ClusterNames
	err = UpdateStrategyJobCheckStatus(ctx, strategyJob, standardJobs, clusterStandards)
	if err != nil {
		return err
	}
	if oldStatus != strategyJob.Status && oldStatus != string(models.JobStatusCompleted) {
		strategyJob.Status = oldStatus
	}
	if strategyJob.ID == 0 {
		return nil
	}
	return UpdateStrategyJobStatusWithPassedAndReasonWithMessage(ctx, db, strategyJob,
		models.JobStatus(strategyJob.Status),
		lo.ToPtr(strategyJob.Passed),
		lo.ToPtr(strategyJob.Reason),
		strategyJob.Message)
}

// CheckIfNeedUpdateStandardJobSummary 检查标准任务是否需要更新
func CheckIfNeedUpdateStandardJobSummary(_ context.Context,
	standardJob *caas.BaselineStandardJob,
	checkJobs []*caas.BaselineStandardRuleJob,
	standardCheckItems []*caas.BaselineStandardRule) (bool, error) {

	// if not completed no need update
	if standardJob.Status == "" || standardJob.Status == string(models.JobStatusCompleted) {
		return true, nil
	}

	// 找到对应的检查项任务
	// 检查检查项任务是否有变化
	// 如果有变化,返回true

	// get check jobs standard_rule_id
	checkJobsStandardRuleIds := sets.NewInt64()
	for _, checkJob := range checkJobs {
		checkJobsStandardRuleIds = checkJobsStandardRuleIds.Insert(checkJob.StandardRuleID)
	}
	// get standard check items id
	standardCheckItemsIds := sets.NewInt64()
	for _, standardCheckItem := range standardCheckItems {
		standardCheckItemsIds = standardCheckItemsIds.Insert(standardCheckItem.ID)
	}
	// check if need update use by sets
	if !checkJobsStandardRuleIds.Equal(standardCheckItemsIds) {
		return true, nil
	}
	return false, nil

}

// UpdateStandardJobSummary 更新标准任务
func UpdateStandardJobSummary(ctx context.Context, db *gorm.DB,
	standardJob *caas.BaselineStandardJob,
	checkJobs []*caas.BaselineStandardRuleJob,
	checkItems []*caas.BaselineStandardRule) error {

	// get standard check item id set from standardCheckItems
	standardCheckItemsIds := sets.NewInt64()
	var standardCheckItems []models.StandardCheckItem
	for _, standardCheckItem := range checkItems {
		standardCheckItemsIds = standardCheckItemsIds.Insert(standardCheckItem.ID)
		standardCheckItems = append(standardCheckItems, ConvertBaselineStandardRuleToStandardCheckItem(standardCheckItem))
	}

	// filter check jobs by standardCheckItems
	// if checkJob standard_rule_id not in standardCheckItems, remove it
	var newCheckJobs []*caas.BaselineStandardRuleJob
	for _, checkJob := range checkJobs {
		if standardCheckItemsIds.Has(checkJob.StandardRuleID) {
			newCheckJobs = append(newCheckJobs, checkJob)
		}
	}
	// calculate new standard check summary from newCheckJobs
	oldStatus := standardJob.Status
	err := UpdateStandardJobCheckStatus(ctx, standardJob, newCheckJobs, standardCheckItems)
	if err != nil {
		return fmt.Errorf("calculate standard check summary error: %w", err)
	}
	if oldStatus != standardJob.Status && oldStatus != string(models.JobStatusCompleted) {
		standardJob.Status = oldStatus
	}
	if standardJob.ID == 0 {
		return nil
	}
	return UpdateStandardJobStatusWithPassedAndReasonAndMessage(ctx, db, standardJob, models.JobStatus(standardJob.Status),
		lo.ToPtr(standardJob.Passed), lo.ToPtr(standardJob.Reason), standardJob.Message)

}

func GetDebugStack() string {
	var stack []byte
	var size int
	for {
		stack = make([]byte, 1024*(size+1))
		n := runtime.Stack(stack, false)
		if n < len(stack) {
			stack = stack[:n]
			break
		}
		size++
	}
	lines := strings.Split(string(stack), "\n")

	skip := 1
	if skip*2 < len(lines) {
		lines = lines[skip*2:]
	}
	return strings.Join(lines, "\n")
}

// func GenerateMonitorNameByCheckJob(checkJob *caas.BaselineStandardRuleJob) string {
// 	return fmt.Sprintf("%s-%d-%d-%d", constants.BaselineMonitorPrefixName, checkJob.StrategyID, checkJob.StandardID, checkJob.RuleID)
// }

// GetObjectAnnotateAndLabelValue ...
func GetObjectAnnotateAndLabelValue(object ctrlclient.Object, key string) (value string, found bool) {
	found = false

	if object.GetLabels() != nil {
		value, found = object.GetLabels()[key]
		if found {
			return
		}
	}

	if object.GetAnnotations() != nil {
		value, found = object.GetAnnotations()[key]
		if found {
			return
		}
	}

	return
}

// DeepEqualBaselineRule 比较BaselineRule 是否相同
// 排除共用字段（创建用户、更新时间等）
func DeepEqualBaselineRule(a, b *caas.BaselineRule) bool {
	if a == nil || b == nil {
		return a == b
	}

	// Compare core identification fields
	if a.ID != b.ID ||
		a.Name != b.Name ||
		a.Description != b.Description ||
		a.Suggestion != b.Suggestion ||
		a.Kind != b.Kind ||
		a.Builtin != b.Builtin ||
		a.RiskLevel != b.RiskLevel ||
		a.Version != b.Version ||
		a.CheckType != b.CheckType ||
		a.CheckResourceType != b.CheckResourceType {
		return false
	}

	// Compare check mode specific fields
	if a.CheckMode != b.CheckMode {
		return false
	}

	// Compare node-related fields
	if a.NodeRoles != b.NodeRoles ||
		a.NodeSelectors != b.NodeSelectors {
		return false
	}

	// Compare file check specific fields
	if a.FileType != b.FileType ||
		a.FileLocationMode != b.FileLocationMode ||
		a.FileLocationPath != b.FileLocationPath ||
		a.K8sResourceAPIVersion != b.K8sResourceAPIVersion ||
		a.K8sResourceName != b.K8sResourceName ||
		a.K8sResourceNamespace != b.K8sResourceNamespace ||
		a.K8sResourceKind != b.K8sResourceKind ||
		a.K8sResourceLabels != b.K8sResourceLabels ||
		a.FileMatchContent != b.FileMatchContent ||
		a.FileContentID != b.FileContentID ||
		a.FileContentPath != b.FileContentPath ||
		a.FileContentUniqueKey != b.FileContentUniqueKey ||
		a.FileContentName != b.FileContentName ||
		a.FileContentLink != b.FileContentLink {
		return false
	}

	// Compare command check specific fields
	if a.Command != b.Command ||
		a.CommandMatchType != b.CommandMatchType ||
		a.CommandMatchValue != b.CommandMatchValue {
		return false
	}

	return true
}

// GetResourcesFromJobTask 从StandardJobTask中获取相关资源
func GetResourcesFromJobTask(ctx context.Context, ctrlclient ctrlclient.Client, task *models.StandardJobTask) ([]*checkerv1.Checker, []*checkerv1.Monitor, []*checkerv1.Baseline, error) {
	if task == nil || task.Job == nil {
		return nil, nil, nil, fmt.Errorf("invalid task or job")
	}
	var (
		checkers  []*checkerv1.Checker
		monitors  []*checkerv1.Monitor
		baselines []*checkerv1.Baseline
	)

	// 获取checker资源
	for _, checkJob := range task.CheckJobs {
		if checkJob.CheckerName == "" {
			continue
		}
		checker := &checkerv1.Checker{}
		if err := ctrlclient.Get(ctx,
			types.NamespacedName{Name: checkJob.CheckerName}, checker); err != nil {
			if !apierrors.IsNotFound(err) {
				return nil, nil, nil, fmt.Errorf("failed to get checker %s: %w", checkJob.CheckerName, err)
			}
			continue
		}
		checkers = append(checkers, checker)
	}

	// 获取monitor资源
	for _, checkJob := range task.CheckJobs {
		if checkJob.MonitorName == "" {
			continue
		}
		monitor := &checkerv1.Monitor{}
		if err := ctrlclient.Get(ctx,
			types.NamespacedName{Name: checkJob.MonitorName}, monitor); err != nil {
			if !apierrors.IsNotFound(err) {
				return nil, nil, nil, fmt.Errorf("failed to get monitor %s: %w", checkJob.MonitorName, err)
			}
			continue
		}
		monitors = append(monitors, monitor)
	}

	// 获取baseline资源
	if task.Job.BaselineName != "" {
		baseline := &checkerv1.Baseline{}
		if err := ctrlclient.Get(ctx,
			types.NamespacedName{Name: task.Job.BaselineName}, baseline); err != nil {
			if !apierrors.IsNotFound(err) {
				return nil, nil, nil, fmt.Errorf("failed to get baseline %s: %w", task.Job.BaselineName, err)
			}
		} else {
			baselines = append(baselines, baseline)
		}
	}

	return checkers, monitors, baselines, nil
}

// UploadResourcesToMinio 上传资源到Minio
func UploadResourcesToMinio(ctx context.Context,
	minioClient *minio.Client,
	clusterName string,
	bucketName string,
	checkers []*checkerv1.Checker,
	monitors []*checkerv1.Monitor,
	baselines []*checkerv1.Baseline) error {

	log := logger.GetSugared().With("cluster", clusterName)

	paths := []string{
		fmt.Sprintf("%s/checkers/", clusterName),
		fmt.Sprintf("%s/monitors/", clusterName),
		fmt.Sprintf("%s/baselines/", clusterName),
	}
	for _, path := range paths {
		if err := EnsureMinioPath(ctx, minioClient, bucketName, path); err != nil {
			log.Errorf("failed to ensure minio path %s: %w", path, err)
			return err
		}
	}
	// 上传checker资源
	for _, checker := range checkers {
		data, err := json.Marshal(checker)
		if err != nil {
			log.Errorf("failed to marshal checker %s: %w", checker.Name, err)
			continue
		}

		path := fmt.Sprintf("%s/checkers/%s.json", clusterName, checker.Name)
		if _, err := minioClient.PutObject(ctx,
			bucketName,
			path,
			bytes.NewReader(data),
			int64(len(data)),
			minio.PutObjectOptions{}); err != nil {
			log.Errorf("failed to upload checker %s: %w", checker.Name, err)
			continue
		}
	}

	// 上传monitor资源
	for _, monitor := range monitors {
		data, err := json.Marshal(monitor)
		if err != nil {
			log.Errorf("failed to marshal monitor %s: %w", monitor.Name, err)
			continue
		}

		path := fmt.Sprintf("%s/monitors/%s.json", clusterName, monitor.Name)
		if _, err := minioClient.PutObject(ctx,
			bucketName,
			path,
			bytes.NewReader(data),
			int64(len(data)),
			minio.PutObjectOptions{}); err != nil {
			log.Errorf("failed to upload monitor %s: %w", monitor.Name, err)
			continue
		}
	}

	// 上传baseline资源
	for _, baseline := range baselines {
		data, err := json.Marshal(baseline)
		if err != nil {
			log.Errorf("failed to marshal baseline %s: %w", baseline.Name, err)
			continue
		}

		path := fmt.Sprintf("%s/baselines/%s.json", clusterName, baseline.Name)
		if _, err := minioClient.PutObject(ctx,
			bucketName,
			path,
			bytes.NewReader(data),
			int64(len(data)),
			minio.PutObjectOptions{}); err != nil {
			log.Errorf("failed to upload baseline %s: %w", baseline.Name, err)
			continue
		}
	}
	return nil
}

// UploadJobTaskResourcesToMinio 上传StandardJobTask资源到Minio
func UploadJobTaskResourcesToMinio(ctx context.Context,
	minioClient *minio.Client,
	client ctrlclient.Client,
	clusterName string,
	bucketName string,
	task *models.StandardJobTask) error {
	checkers, monitors, baselines, err := GetResourcesFromJobTask(ctx, client, task)
	if err != nil {
		return fmt.Errorf("failed to get resources from job task: %w", err)
	}
	return UploadResourcesToMinio(ctx, minioClient, clusterName, bucketName, checkers, monitors, baselines)
}

func DeleteResourcesFromJobTask(ctx context.Context,
	client ctrlclient.Client,
	task *models.StandardJobTask) error {
	log := logger.GetSugared().With("cluster", task.Job.ClusterName)
	for _, checkJob := range task.CheckJobs {
		if checkJob.CheckerName == "" {
			continue
		}
		if err := client.Delete(ctx, &checkerv1.Checker{
			ObjectMeta: metav1.ObjectMeta{
				Name: checkJob.CheckerName,
			},
		}); err != nil {
			log.Errorf("failed to delete checker %s: %w", checkJob.CheckerName, err)
			continue
		}
	}
	if task.Job.BaselineName != "" {
		if err := client.Delete(ctx, &checkerv1.Baseline{
			ObjectMeta: metav1.ObjectMeta{
				Name: task.Job.BaselineName,
			},
		}); err != nil {
			log.Errorf("failed to delete baseline %s: %w", task.Job.BaselineName, err)
			return err
		}
	}
	return nil
}

// DeleteResourcesWhenStrategyDeleted  根据策略ID删除资源
func DeleteResourcesWhenStrategyDeleted(ctx context.Context,
	strategyID int64) error {
	log := logger.GetSugared().Named("DeleteResourcesWhenStrategyDeleted").With("strategy-id", strategyID)
	log.Errorw("start remove strategy resources in cluster")
	strategyLabels := map[string]string{
		constants.BaselineStrategyIDLabelKey: fmt.Sprintf("%d", strategyID),
	}
	clusters := clientmgr.ListOnlineClusters()
	for _, cluster := range clusters {
		cli := cluster.GetClient().GetCtrlClient()
		if err := cli.DeleteAllOf(ctx, &checkerv1.Checker{}, ctrlclient.MatchingLabels(strategyLabels)); err != nil {
			log.Errorw("failed to delete checkers", "error", err, "cluster", cluster.GetName())
			continue
		}
		if err := cli.DeleteAllOf(ctx, &checkerv1.Baseline{}, ctrlclient.MatchingLabels(strategyLabels)); err != nil {
			log.Errorw("failed to delete baselines", "error", err, "cluster", cluster.GetName())
			continue
		}
		monitors := &checkerv1.MonitorList{}
		if err := cli.List(ctx, monitors, ctrlclient.HasLabels{constants.BaselineStrategyIDLabelKey}); err != nil {
			log.Errorw("failed list monitors", "error", err, "cluster", cluster.GetName())
			continue
		}
		for _, monitor := range monitors.Items {
			mnt := monitor.DeepCopy()
			oldStrategyIDs := infra.AnnotationGetStrategyIDs(mnt)
			newStrategyIds := infra.AnnotationGetStrategyIDs(mnt)
			newStrategyIds.Delete(fmt.Sprintf("%d", strategyID))
			if len(newStrategyIds) == 0 {
				// remove mnt
				if err := cli.Delete(ctx, mnt); err != nil {
					log.Errorw("failed remove monitor", "error", err, "cluster", cluster.GetName(), "monitor", mnt.Name)
					continue
				}
			} else {
				if !oldStrategyIDs.Equal(newStrategyIds) {
					patch := map[string]any{
						"metadata": map[string]any{
							"annotations": map[string]any{
								constants.BaselineStrategyIDsAnnotationKey: strings.Join(sets.List(newStrategyIds), ","),
							},
						},
					}
					patchBytes, err := json.Marshal(patch)
					if err != nil {
						log.Errorw("failed remove monitor strategy id ", "error", err, "cluster", cluster.GetName(), "monitor", mnt.Name)
						continue
					}
					if err := cli.Patch(ctx, mnt, ctrlclient.RawPatch(types.MergePatchType, patchBytes)); err != nil {
						log.Errorw("failed remove monitor strategy id ", "error", err, "cluster", cluster.GetName(), "monitor", mnt.Name)
					}
				}
			}

		}

	}
	return nil
}

// DeleteResourcesFromStrategyIDAndStandardID 根据策略ID和标准ID删除资源
func DeleteResourcesFromStrategyIDAndStandardID(ctx context.Context,
	clusterName string,
	strategyID int64,
	standardID int64) error {
	cluster, err := clientmgr.GetCluster(clusterName)
	if err != nil {
		return fmt.Errorf("failed to get cluster: %w", err)
	}
	client := cluster.GetClient().GetCtrlClient()
	log := logger.GetSugared().Named("DeleteResourcesFromStrategyIDAndStandardID").With("strategy-id", strategyID)
	strategyLabels := map[string]string{
		constants.BaselineStrategyIDLabelKey:  fmt.Sprintf("%d", strategyID),
		constants.BaselineStandardIDLabelKey:  fmt.Sprintf("%d", standardID),
		constants.BaselineClusterNameLabelKey: fmt.Sprintf("%s", clusterName),
	}
	if err := client.DeleteAllOf(ctx, &checkerv1.Checker{}, ctrlclient.MatchingLabels(strategyLabels)); err != nil {
		log.Errorf("failed to delete checkers: %v", err)
		return err
	}
	if err := client.DeleteAllOf(ctx, &checkerv1.Baseline{}, ctrlclient.MatchingLabels(strategyLabels)); err != nil {
		log.Errorf("failed to delete baselines: %v", err)
		return err
	}
	return nil
}
