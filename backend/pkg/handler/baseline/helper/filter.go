package helper

import (
	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
)

// FilterExecutionConfig 根据执行类型过滤字段，返回新的 ExecutionConfig
func FilterExecutionConfig(config models.ExecutionConfig) models.ExecutionConfig {
	filtered := models.ExecutionConfig{
		ExecutionType: config.ExecutionType,
	}
	switch config.ExecutionType {
	case models.ExecutionTypeImmediate:
		filtered.ExecutionDate = config.ExecutionDate
	case models.ExecutionTypeScheduled:
		filtered.ExecutionDate = config.ExecutionDate
	case models.ExecutionTypeRecurring:
		filtered.ExecutionTime = config.ExecutionTime
		filtered.StartTime = config.StartTime
		filtered.EndTime = config.EndTime
		filtered.Timezone = config.Timezone
		filtered.RecurringType = config.RecurringType
		switch config.RecurringType {
		case models.RecurringTypeDaily:
			filtered.PerDays = config.PerDays
		case models.RecurringTypeWeekly:
			filtered.DaysOfWeek = config.DaysOfWeek
		case models.RecurringTypeMonthly:
			filtered.StartDayOfMonth = config.StartDayOfMonth
			filtered.EndDayOfMonth = config.EndDayOfMonth
		case models.RecurringTypeCron:
			filtered.CronExpression = config.CronExpression
		}
	}
	return filtered
}
