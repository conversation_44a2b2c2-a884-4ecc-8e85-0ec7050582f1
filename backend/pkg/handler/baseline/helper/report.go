package helper

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"sort"
	"strings"
	"time"

	"github.com/minio/minio-go/v7"
	minioutils "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/minio"

	"github.com/samber/lo"
	"github.com/xuri/excelize/v2"
	"gorm.io/gorm"
	infrachecker "harmonycloud.cn/baseline-checker/pkg/models/checker"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/feign/baseline_master"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
)

func SaveBaselineRequest(ctx context.Context, db *gorm.DB, job *caas.BaselineStandardJob,
	request *baseline_master.GetReportRequest) error {
	url := config.BaselineCheckerServerURL.Value
	dataRaw, _ := json.Marshal(request)
	reqStr := fmt.Sprintf(`curl -X GET '%s/report' -H 'Content-Type: application/json' -d '%s'`, url, dataRaw)
	if err := db.WithContext(ctx).Model(&caas.BaselineStandardJob{}).
		Where("id = ?", job.ID).Update("request", reqStr).Error; err != nil {
		return err
	}
	return nil
}

func SaveBaselineReport(ctx context.Context, db *gorm.DB, reportData string, job *caas.BaselineStandardJob) error {
	minioClient, err := minioutils.GetDefaultMinioClient()
	if err != nil {
		return err
	}
	// 保存策略任务报告
	baselineReportFileName := fmt.Sprintf("%s_job-%d_%s_report.json", job.BaselineName, job.ID, job.ClusterName)
	bytesReader := strings.NewReader(reportData)
	if _, err := minioClient.PutObject(ctx, constants.MinioBaselineReportBucketName, baselineReportFileName, bytesReader, bytesReader.Size(),
		minio.PutObjectOptions{
			ContentType: "application/json",
		}); err != nil {
		return err
	}
	reportUrl := fmt.Sprintf("/%s/%s", constants.MinioBaselineReportBucketName, baselineReportFileName)
	if err := db.WithContext(ctx).Model(&caas.BaselineStandardJob{}).
		Where("id = ?", job.ID).Update("result", reportUrl).Error; err != nil {
		return err
	}
	return nil
}

// VerifyStandardJobReport 保存策略任务报告
func VerifyStandardJobReport(ctx context.Context,
	db *gorm.DB,
	reportResp *baseline_master.GetReportResponse,
	standardJob *models.StandardJobTask) error {

	if len(reportResp.Data) == 0 {
		return fmt.Errorf("baseline report data is empty")
	}
	report := reportResp.Data[0]

	reportNameMap := make(map[string]infrachecker.CheckItem)
	for idx := range report.CheckItems {
		reportNameMap[report.CheckItems[idx].Name] = report.CheckItems[idx]
	}

	reportJson, _ := json.Marshal(report)
	if err := SaveBaselineReport(ctx, db, string(reportJson), standardJob.Job); err != nil {
		logger.GetSugared().Warnw("save baseline report failed", "error", err)
	}

	checkJobIdMap := make(map[int64]*caas.BaselineStandardRuleJob)
	for _, job := range standardJob.CheckJobs {
		job := job
		checkJobIdMap[job.ID] = job
	}

	failedUpdateJobIdMap := map[int64]error{}
	for i, checkJob := range standardJob.CheckJobs {
		if lo.Contains(standardJob.SkipCheckJobIds, checkJob.ID) {
			continue
		}
		if reportItem, ok := reportNameMap[checkJob.CheckerName]; ok {
			if err := CompareReportValue(ctx, db, standardJob.CheckJobs[i], reportItem); err != nil {
				failedUpdateJobIdMap[checkJob.ID] = err
			}
		} else {
			failedUpdateJobIdMap[checkJob.ID] = fmt.Errorf("checker %s not found in report", checkJob.CheckerName)
		}
	}
	var errs []error
	for jobId, err := range failedUpdateJobIdMap {
		checkJob := checkJobIdMap[jobId]
		if checkJob == nil {
			continue
		}
		if err := UpdateCheckJobStatusWithPassedAndReasonWithMessage(ctx, db, checkJobIdMap[jobId],
			models.JobStatusCompleted, lo.ToPtr(false),
			lo.ToPtr(models.JobReasonVerifyReportError), err.Error()); err != nil {
			errs = append(errs, err)
		}
	}
	return errors.Join(errs...)

}

// CheckIfCheckerReportSummaryPassed check if checker report summary passed
func CheckIfCheckerReportSummaryPassed(_ context.Context,
	reportSummary infrachecker.Summary) (bool, string) {
	switch reportSummary.Result {
	case infrachecker.ResultTypePass:
		return true, reportSummary.Reason
	case infrachecker.ResultTypeFail:
		return false, reportSummary.Reason
	case infrachecker.ResultTypeSkip:
		return false, fmt.Sprintf("checker skip, reason: %s", reportSummary.Reason)
	default:
		return false, "unknown"
	}
}

// CompareReportValue set checker status from report
func CompareReportValue(ctx context.Context,
	db *gorm.DB,
	checkJob *caas.BaselineStandardRuleJob,
	checkerReport infrachecker.CheckItem) error {
	if checkerReport.Name != checkJob.CheckerName {
		return fmt.Errorf("checker name not match")
	}
	// set report commonFile and parseFile to nil avoid too much data
	// sort checkerReport points by changeTime by desc
	sort.Slice(checkerReport.Points, func(i, j int) bool {
		beforeTime, err1 := time.Parse(checkerReport.Points[i].ChangeTime, time.DateTime)
		afterTime, err2 := time.Parse(checkerReport.Points[j].ChangeTime, time.DateTime)
		if err1 != nil || err2 != nil {
			return false
		}
		return beforeTime.After(afterTime)
	})
	if checkJob.Reason == "" {
		if len(checkerReport.Points) > 0 {
			var messages []string
			for _, point := range checkerReport.Points {
				passed, message := CheckIfCheckerReportSummaryPassed(ctx, point.Summary)
				if !passed {
					messages = append(messages, point.Path+","+message)
				}
			}
			if len(messages) > 0 {
				return UpdateCheckJobStatusWithPassedAndReasonWithMessage(ctx, db,
					checkJob, models.JobStatusCompleted,
					lo.ToPtr(false), lo.ToPtr(models.JobReasonReportValueNotMatch), strings.Join(messages, ";"))
			} else {
				return UpdateCheckJobStatusWithPassedAndReasonWithMessage(ctx, db, checkJob,
					models.JobStatusCompleted,
					lo.ToPtr(true), nil, "")
			}

		} else {
			return UpdateCheckJobStatusWithPassedAndReasonWithMessage(ctx, db, checkJob,
				models.JobStatusCompleted,
				lo.ToPtr(false),
				lo.ToPtr(models.JobReasonReportPointsEmpty),
				"checker report points is empty")
		}
	}
	return nil
}

// GenerateBaselineStandardCheckResultsTableHeaders ...
func GenerateBaselineStandardCheckResultsTableHeaders(ctx context.Context) []string {
	code := GetLanguageCode(ctx)
	templates := map[string][]string{
		constants.LanguageCodeZhCN: {
			"名称", "集群", "严重", "高危", "中危", "低危", "未通过", "已通过", "总数", "状态",
		},
		constants.LanguageCodeZhHK: {
			"名稱", "集群", "嚴重", "高危", "中危", "低危", "未通過", "已通過", "总数", "狀態",
		},
		constants.LanguageCodeEnUS: {
			"Name", "Cluster Name", "Critical", "High", "Medium", "Low", "Not Passed", "Passed Count", "Total", "Status",
		},
	}
	return templates[code]
}

// GenerateBaselineStandardCheckResultsReport ...
func GenerateBaselineStandardCheckResultsReport(ctx context.Context, items []*models.StandardCheckResultItem) (
	resp *models.DownloadBaselineStandardCheckResultsReportResponse,
	err error) {
	f := excelize.NewFile()
	sheetName := "Report"
	if err := f.SetSheetName("Sheet1", sheetName); err != nil {
		return nil, err
	}

	headers := GenerateBaselineStandardCheckResultsTableHeaders(ctx)
	// 设置表头
	for col, header := range headers {
		colName, _ := excelize.ColumnNumberToName(col + 1)
		if err := f.SetCellValue(sheetName, colName+"1", header); err != nil {
			return nil, err
		}
	}

	// 填充数据
	for i, item := range items {
		row := i + 2 // Excel 行号从 1 开始
		cellErrs := []error{
			f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), item.Name),
			f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), item.ClusterName),
			f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), item.CheckRiskSummary.Critical),
			f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), item.CheckRiskSummary.High),
			f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), item.CheckRiskSummary.Medium),
			f.SetCellValue(sheetName, fmt.Sprintf("F%d", row), item.CheckRiskSummary.Low),
			f.SetCellValue(sheetName, fmt.Sprintf("G%d", row), item.CheckRiskSummary.NotPassed),
			f.SetCellValue(sheetName, fmt.Sprintf("H%d", row), item.PassedCount),
			f.SetCellValue(sheetName, fmt.Sprintf("I%d", row), item.CheckRiskSummary.Total),
			f.SetCellValue(sheetName, fmt.Sprintf("J%d", row), string(item.Status)),
		}
		if errs := lo.Filter(cellErrs, func(item error, index int) bool {
			return err != nil
		}); len(errs) > 0 {
			return nil, errs[0]
		}
	}

	// 保存到缓冲区
	buf := new(bytes.Buffer)
	if err := f.Write(buf); err != nil {
		return nil, err
	}
	resp = &models.DownloadBaselineStandardCheckResultsReportResponse{}
	resp.FileName = fmt.Sprintf("standard-report-%d.xlsx", time.Now().Unix())
	resp.FileContent = buf.Bytes()
	return resp, nil

}

// GenerateCheckResultsTableHeaders 生成不同语言版本的检查结果表头
func GenerateCheckResultsTableHeaders(ctx context.Context) []string {
	code := GetLanguageCode(ctx)
	templates := map[string][]string{
		constants.LanguageCodeZhCN: {
			"检查项", "集群", "检查结果", "检查说明", "处理建议", "最后检查时间",
		},
		constants.LanguageCodeZhHK: {
			"檢查項", "集群", "檢查結果", "檢查說明", "處理建議", "最後檢查時間",
		},
		constants.LanguageCodeEnUS: {
			"Check Item", "Cluster Name", "Check Result", "Description", "Suggested Actions", "Last Check Time",
		},
	}

	// 返回根据当前语言代码获取的表头
	return templates[code]
}

// GenerateCheckResultsReport ...
func GenerateCheckResultsReport(ctx context.Context, items []*models.CheckResultItem) (
	resp *models.DownloadCheckResultsReportResponse,
	err error) {
	f := excelize.NewFile()
	sheetName := "Report"
	if err := f.SetSheetName("Sheet1", sheetName); err != nil {
		return nil, err
	}

	headers := GenerateCheckResultsTableHeaders(ctx)
	// 设置表头
	for col, header := range headers {
		colName, _ := excelize.ColumnNumberToName(col + 1)
		if err := f.SetCellValue(sheetName, colName+"1", header); err != nil {
			return nil, err
		}
	}

	// 填充数据
	for i, item := range items {
		row := i + 2 // Excel 行号从 1 开始
		cellErrs := []error{
			f.SetCellValue(sheetName, fmt.Sprintf("A%d", row), item.Name),
			f.SetCellValue(sheetName, fmt.Sprintf("B%d", row), item.ClusterName),
			f.SetCellValue(sheetName, fmt.Sprintf("C%d", row), item.Status),
			f.SetCellValue(sheetName, fmt.Sprintf("D%d", row), item.Description),
			f.SetCellValue(sheetName, fmt.Sprintf("E%d", row), item.Suggestion),
			f.SetCellValue(sheetName, fmt.Sprintf("F%d", row), item.LastCheckTime),
		}
		if errs := lo.Filter(cellErrs, func(item error, index int) bool {
			return err != nil
		}); len(errs) > 0 {
			return nil, errs[0]
		}
	}

	// 保存到缓冲区
	buf := new(bytes.Buffer)
	if err := f.Write(buf); err != nil {
		return nil, err
	}
	resp = &models.DownloadCheckResultsReportResponse{}
	resp.FileName = fmt.Sprintf("check-report-%d.xlsx", time.Now().Unix())
	resp.FileContent = buf.Bytes()
	return resp, nil

}
