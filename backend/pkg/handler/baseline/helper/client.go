package helper

import (
	"context"
	"fmt"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/feign/baseline_master"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/addon"
)

// NewReportServiceFromCluster 根据集群名称获取报告服务
func NewReportServiceFromCluster(ctx context.Context, addonHandler addon.Handler, clusterName string) (baseline_master.Service, error) {
	// find baseline checker addon
	bcAddon, err := addonHandler.GetComponent(ctx, clusterName, constants.BASELINE_CHECKER.EnName, false)
	if err != nil {
		return nil, err
	}
	if bcAddon == nil {
		return nil, fmt.Errorf("baseline checker addon not found")
	}
	// build url from addon
	bcConfig := bcAddon.BaselineCheckerConfig
	url := fmt.Sprintf("%s://%s:%d", bcConfig.Protocol, bcConfig.Ip, bcConfig.Port)
	return baseline_master.NewService(baseline_master.WithBaseURL(url)), nil
}
