package helper

import (
	"bytes"
	"fmt"

	"github.com/xuri/excelize/v2"
)

// GenerateImportCheckerExcelTemplate 生成导入检查项文件模板
func GenerateImportCheckerExcelTemplate() (string, error) {
	f := excelize.NewFile()
	sheetName := "检查项导入模板"

	// 创建新的工作表
	index, err := f.NewSheet(sheetName)
	if err != nil {
		return "", fmt.Errorf("create sheet failed: %v", err)
	}

	// 设置为活动工作表
	f.SetActiveSheet(index)

	// 删除默认的Sheet1
	f.DeleteSheet("Sheet1")

	description := `检查项导入模板说明：
1. 检查项名称：检查项的名称（必填）
2. 检查资源类型：检查项检查的资源类型（必填）
3. 风险级别：检查项的风险级别（必填，请从下拉列表选择）
4. 检查项说明：检查项的说明（必填）
5. 处理建议：检查项的处理建议（必填）
6. 检查类型：检查项的类型（必填，请从下拉列表选择）
7. 执行命令：当检查类型为命令时必填
8. 命令匹配模式：当检查类型为命令时必填（请从下拉列表选择）
9. 命令匹配内容：当检查类型为命令时必填
10. 文件类型：当检查类型为文件时必填（请从下拉列表选择）
11. 节点范围：检查项的节点范围（请从下拉列表选择）
12. 文件匹配内容：当检查类型为文件时必填
13. 文件指定方式：当检查类型为文件时必填（请从下拉列表选择）
14. 主机路径：当文件指定方式为主机路径时必填
15. Kubernetes资源命名空间：当文件指定方式为Kubernetes资源时必填
16. Kubernetes资源名称：当文件指定方式为Kubernetes资源时必填
17. Kubernetes资源API版本：当文件指定方式为Kubernetes资源时必填
18. Kubernetes资源类型：当文件指定方式为Kubernetes资源时必填`

	// 合并第一行单元格
	if err := f.MergeCell(sheetName, "A1", "R1"); err != nil {
		return "", fmt.Errorf("merge cells failed: %v", err)
	}

	// 设置说明文字
	if err := f.SetCellValue(sheetName, "A1", description); err != nil {
		return "", fmt.Errorf("set description failed: %v", err)
	}

	// 创建绿色样式
	style, err := f.NewStyle(&excelize.Style{
		Fill: excelize.Fill{
			Type:    "pattern",
			Color:   []string{"#C6EFCE"},
			Pattern: 1,
		},
		Alignment: &excelize.Alignment{
			Horizontal: "left",
			Vertical:   "center",
			WrapText:   true,
		},
	})
	if err != nil {
		return "", fmt.Errorf("create style failed: %v", err)
	}

	// 应用样式到合并单元格
	if err := f.SetCellStyle(sheetName, "A1", "R1", style); err != nil {
		return "", fmt.Errorf("set cell style failed: %v", err)
	}

	// 设置行高
	if err := f.SetRowHeight(sheetName, 1, 400); err != nil {
		return "", fmt.Errorf("set row height failed: %v", err)
	}

	headers := []string{
		"检查项名称",
		"检查资源类型",
		"风险级别",
		"检查项说明",
		"处理建议",
		"检查类型",
		"执行命令",
		"命令匹配模式",
		"命令匹配内容",
		"文件类型",
		"节点范围",
		"文件匹配内容",
		"文件指定方式",
		"主机路径",
		"Kubernetes资源命名空间",
		"Kubernetes资源名称",
		"Kubernetes资源API版本",
		"Kubernetes资源类型",
	}

	// 设置表头
	for i, header := range headers {
		colName, err := excelize.ColumnNumberToName(i + 1)
		if err != nil {
			return "", fmt.Errorf("convert column number failed: %v", err)
		}
		if err := f.SetCellValue(sheetName, colName+"2", header); err != nil {
			return "", fmt.Errorf("set header failed: %v", err)
		}
	}

	// 设置数据验证
	// 风险级别
	riskLevels := []string{"高", "中", "低"}
	for i, level := range riskLevels {
		cell, err := excelize.CoordinatesToCellName(3, i+3)
		if err != nil {
			return "", fmt.Errorf("convert coordinates failed: %v", err)
		}
		if err := f.SetCellValue(sheetName, cell, level); err != nil {
			return "", fmt.Errorf("set cell value failed: %v", err)
		}
	}
	dvRisk := excelize.NewDataValidation(true)
	dvRisk.Sqref = "C3:C1048576"
	dvRisk.Type = "list"
	dvRisk.Formula1 = "$C$3:$C$5"
	if err := f.AddDataValidation(sheetName, dvRisk); err != nil {
		return "", fmt.Errorf("set risk level validation failed: %v", err)
	}

	// 检查类型
	checkTypes := []string{"命令", "文件"}
	for i, checkType := range checkTypes {
		cell, err := excelize.CoordinatesToCellName(6, i+3)
		if err != nil {
			return "", fmt.Errorf("convert coordinates failed: %v", err)
		}
		if err := f.SetCellValue(sheetName, cell, checkType); err != nil {
			return "", fmt.Errorf("set cell value failed: %v", err)
		}
	}
	dvType := excelize.NewDataValidation(true)
	dvType.Sqref = "F3:F1048576"
	dvType.Type = "list"
	dvType.Formula1 = "$F$3:$F$4"
	if err := f.AddDataValidation(sheetName, dvType); err != nil {
		return "", fmt.Errorf("set check type validation failed: %v", err)
	}

	// 命令匹配模式
	matchModes := []string{"正则", "精确"}
	for i, mode := range matchModes {
		cell, err := excelize.CoordinatesToCellName(8, i+3)
		if err != nil {
			return "", fmt.Errorf("convert coordinates failed: %v", err)
		}
		if err := f.SetCellValue(sheetName, cell, mode); err != nil {
			return "", fmt.Errorf("set cell value failed: %v", err)
		}
	}
	dvMode := excelize.NewDataValidation(true)
	dvMode.Sqref = "H3:H1048576"
	dvMode.Type = "list"
	dvMode.Formula1 = "$H$3:$H$4"
	if err := f.AddDataValidation(sheetName, dvMode); err != nil {
		return "", fmt.Errorf("set command match mode validation failed: %v", err)
	}

	// 文件类型
	fileTypes := []string{"YAML", "JSON"}
	for i, fileType := range fileTypes {
		cell, err := excelize.CoordinatesToCellName(10, i+3)
		if err != nil {
			return "", fmt.Errorf("convert coordinates failed: %v", err)
		}
		if err := f.SetCellValue(sheetName, cell, fileType); err != nil {
			return "", fmt.Errorf("set cell value failed: %v", err)
		}
	}
	dvFileType := excelize.NewDataValidation(true)
	dvFileType.Sqref = "J3:J1048576"
	dvFileType.Type = "list"
	dvFileType.Formula1 = "$J$3:$J$4"
	if err := f.AddDataValidation(sheetName, dvFileType); err != nil {
		return "", fmt.Errorf("set file type validation failed: %v", err)
	}

	// 节点范围
	nodeScopes := []string{"Master", "Node", "Worker"}
	for i, scope := range nodeScopes {
		cell, err := excelize.CoordinatesToCellName(11, i+3)
		if err != nil {
			return "", fmt.Errorf("convert coordinates failed: %v", err)
		}
		if err := f.SetCellValue(sheetName, cell, scope); err != nil {
			return "", fmt.Errorf("set cell value failed: %v", err)
		}
	}
	dvNodeScope := excelize.NewDataValidation(true)
	dvNodeScope.Sqref = "K3:K1048576"
	dvNodeScope.Type = "list"
	dvNodeScope.Formula1 = "$K$3:$K$5"
	if err := f.AddDataValidation(sheetName, dvNodeScope); err != nil {
		return "", fmt.Errorf("set node scope validation failed: %v", err)
	}

	// 文件指定方式
	fileModes := []string{"主机路径", "Kubernetes资源"}
	for i, mode := range fileModes {
		cell, err := excelize.CoordinatesToCellName(13, i+3)
		if err != nil {
			return "", fmt.Errorf("convert coordinates failed: %v", err)
		}
		if err := f.SetCellValue(sheetName, cell, mode); err != nil {
			return "", fmt.Errorf("set cell value failed: %v", err)
		}
	}
	dvFileMode := excelize.NewDataValidation(true)
	dvFileMode.Sqref = "M3:M1048576"
	dvFileMode.Type = "list"
	dvFileMode.Formula1 = "$M$3:$M$4"
	if err := f.AddDataValidation(sheetName, dvFileMode); err != nil {
		return "", fmt.Errorf("set file specification mode validation failed: %v", err)
	}

	// 隐藏选项值所在的行
	if err := f.SetRowVisible(sheetName, 3, false); err != nil {
		return "", fmt.Errorf("hide row 3 failed: %v", err)
	}
	if err := f.SetRowVisible(sheetName, 4, false); err != nil {
		return "", fmt.Errorf("hide row 4 failed: %v", err)
	}
	if err := f.SetRowVisible(sheetName, 5, false); err != nil {
		return "", fmt.Errorf("hide row 5 failed: %v", err)
	}

	// 设置列宽
	for i := 1; i <= len(headers); i++ {
		colName, err := excelize.ColumnNumberToName(i)
		if err != nil {
			return "", fmt.Errorf("convert column number failed: %v", err)
		}
		if err := f.SetColWidth(sheetName, colName, colName, 20); err != nil {
			return "", fmt.Errorf("set column width failed: %v", err)
		}
	}

	// 保存文件
	buf := new(bytes.Buffer)
	if err := f.Write(buf); err != nil {
		return "", fmt.Errorf("write excel failed: %v", err)
	}

	return buf.String(), nil
}

// DownloadImportCheckerFileTemplate 下载导入检查项文件模板
// 下载导入检查项文件模板，并返回文件信息
// 支持的文件类型：
// - csv
// - excel
// - json
// - yaml
