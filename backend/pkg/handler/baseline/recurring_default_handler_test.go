package baseline

import (
	"testing"
	"time"

	"log"

	"go.uber.org/zap"
	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
)

func TestDefaultRecurringHandler_Add(t *testing.T) {
	loc := time.Now().Location()
	executionConfig := models.ExecutionConfig{
		StartTime: "2025-05-09 17:57:29",
		EndTime:   "2025-05-11 10:50:00",
	}
	startTime, err := time.ParseInLocation(time.DateTime, executionConfig.StartTime, loc)
	if err != nil {
		log.Println("failed to parse execution time", err)
		return
	}
	endTime, err := time.ParseInLocation(time.DateTime, executionConfig.EndTime, loc)
	if err != nil {
		log.Println("failed to parse end time", zap.Error(err))
		return
	}
	// todo: 判断时区，默认先东八区
	now := time.Now()
	timeFormat := time.DateTime
	log.Println("get now, startTime , endTime, ", "now", now.Format(timeFormat),
		"start", startTime.Format(timeFormat), "end", endTime.Format(timeFormat),
		"loc", loc)
	if now.Before(startTime) || now.After(endTime) {
		log.Println("recurring job not match current time", zap.String("current-time", now.Format(time.DateTime)), zap.String("start-time", executionConfig.StartTime), zap.String("end-time", executionConfig.EndTime))
		return
	}
}
