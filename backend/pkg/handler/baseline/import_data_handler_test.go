package baseline

import (
	"bytes"
	"fmt"
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/xuri/excelize/v2"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline/helper"
)

func TestGenerateImportCheckerExcelTemplate(t *testing.T) {
	// 调用模板生成函数
	content, err := helper.GenerateImportCheckerExcelTemplate()
	assert.NoError(t, err, "生成模板不应该返回错误")
	assert.NotEmpty(t, content, "生成的模板内容不应该为空")

	os.WriteFile("template.xlsx", []byte(content), 0644)
	// 从内容创建Excel文件
	f, err := excelize.OpenReader(bytes.NewReader([]byte(content)))
	assert.NoError(t, err, "打开生成的Excel文件不应该返回错误")
	defer f.Close()

	// 测试工作表是否存在
	sheetName := "检查项导入模板"
	sheetIndex, err := f.GetSheetIndex(sheetName)
	assert.NoError(t, err, "获取工作表索引不应该返回错误")
	assert.NotEqual(t, -1, sheetIndex, "应该存在名为'检查项导入模板'的工作表")

	// 测试说明文字
	cell, err := f.GetCellValue(sheetName, "A1")
	assert.NoError(t, err, "获取单元格A1的值不应该返回错误")
	assert.Contains(t, cell, "检查项导入模板说明", "A1单元格应该包含模板说明")

	// 测试单元格合并
	mergedCells, err := f.GetMergeCells(sheetName)
	assert.NoError(t, err, "获取合并单元格不应该返回错误")
	foundMerge := false
	for _, mergeCell := range mergedCells {
		if mergeCell.GetStartAxis() == "A1" && mergeCell.GetEndAxis() == "R1" {
			foundMerge = true
			break
		}
	}
	assert.True(t, foundMerge, "A1到R1应该被合并")

	// 测试表头
	expectedHeaders := []string{
		"检查项名称",
		"检查资源类型",
		"风险级别",
		"检查项说明",
		"处理建议",
		"检查类型",
		"执行命令",
		"命令匹配模式",
		"命令匹配内容",
		"文件类型",
		"节点范围",
		"文件匹配内容",
		"文件指定方式",
		"主机路径",
		"Kubernetes资源命名空间",
		"Kubernetes资源名称",
		"Kubernetes资源API版本",
		"Kubernetes资源类型",
	}

	for i, header := range expectedHeaders {
		col, err := excelize.ColumnNumberToName(i + 1)
		assert.NoError(t, err, "列号转换不应该返回错误")
		cell, err := f.GetCellValue(sheetName, col+"2")
		assert.NoError(t, err, "获取表头单元格的值不应该返回错误")
		assert.Equal(t, header, cell, "表头应该匹配预期值")
	}

	// 测试数据验证
	dvs, err := f.GetDataValidations(sheetName)
	assert.NoError(t, err, "获取数据验证不应该返回错误")

	// 验证风险级别
	found := false
	for _, dv := range dvs {
		if dv.Sqref == "C3:C1048576" {
			found = true
			assert.Equal(t, "list", dv.Type, "风险级别应该是列表类型")
			assert.Equal(t, "$C$3:$C$5", dv.Formula1, "风险级别的选项应该正确")
			assert.True(t, dv.ShowDropDown, "风险级别应该显示下拉列表")
		}
	}
	assert.True(t, found, "应该存在风险级别的数据验证")

	// 验证检查类型
	found = false
	for _, dv := range dvs {
		if dv.Sqref == "F3:F1048576" {
			found = true
			assert.Equal(t, "list", dv.Type, "检查类型应该是列表类型")
			assert.Equal(t, "$F$3:$F$4", dv.Formula1, "检查类型的选项应该正确")
			assert.True(t, dv.ShowDropDown, "检查类型应该显示下拉列表")
		}
	}
	assert.True(t, found, "应该存在检查类型的数据验证")

	// 验证命令匹配模式
	found = false
	for _, dv := range dvs {
		if dv.Sqref == "H3:H1048576" {
			found = true
			assert.Equal(t, "list", dv.Type, "命令匹配模式应该是列表类型")
			assert.Equal(t, "$H$3:$H$4", dv.Formula1, "命令匹配模式的选项应该正确")
			assert.True(t, dv.ShowDropDown, "命令匹配模式应该显示下拉列表")
		}
	}
	assert.True(t, found, "应该存在命令匹配模式的数据验证")

	// 验证文件类型
	found = false
	for _, dv := range dvs {
		if dv.Sqref == "J3:J1048576" {
			found = true
			assert.Equal(t, "list", dv.Type, "文件类型应该是列表类型")
			assert.Equal(t, "$J$3:$J$4", dv.Formula1, "文件类型的选项应该正确")
			assert.True(t, dv.ShowDropDown, "文件类型应该显示下拉列表")
		}
	}
	assert.True(t, found, "应该存在文件类型的数据验证")

	// 验证节点范围
	found = false
	for _, dv := range dvs {
		if dv.Sqref == "K3:K1048576" {
			found = true
			assert.Equal(t, "list", dv.Type, "节点范围应该是列表类型")
			assert.Equal(t, "$K$3:$K$5", dv.Formula1, "节点范围的选项应该正确")
			assert.True(t, dv.ShowDropDown, "节点范围应该显示下拉列表")
		}
	}
	assert.True(t, found, "应该存在节点范围的数据验证")

	// 验证文件指定方式
	found = false
	for _, dv := range dvs {
		if dv.Sqref == "M3:M1048576" {
			found = true
			assert.Equal(t, "list", dv.Type, "文件指定方式应该是列表类型")
			assert.Equal(t, "$M$3:$M$4", dv.Formula1, "文件指定方式的选项应该正确")
			assert.True(t, dv.ShowDropDown, "文件指定方式应该显示下拉列表")
		}
	}
	assert.True(t, found, "应该存在文件指定方式的数据验证")

	// 验证选项值是否被隐藏
	for i := 3; i <= 5; i++ {
		visible, err := f.GetRowVisible(sheetName, i)
		assert.NoError(t, err, fmt.Sprintf("获取第%d行可见性不应该返回错误", i))
		assert.False(t, visible, fmt.Sprintf("第%d行应该被隐藏", i))
	}

	// 测试样式 - 检查第一行的样式
	styleID, err := f.GetCellStyle(sheetName, "A1")
	assert.NoError(t, err, "获取单元格样式不应该返回错误")
	assert.NotEqual(t, 0, styleID, "应该应用了自定义样式")
}
