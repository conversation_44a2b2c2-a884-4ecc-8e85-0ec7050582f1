package baseline

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/redis/go-redis/v9"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline/helper"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
)

// 添加常量
const (
	maxRetries = 3                      // 最大重试次数
	retryDelay = 100 * time.Millisecond // 重试间隔
)

// StrategyTaskEvent 任务事件结构体
type StrategyTaskEvent struct {
	EventType  string                    `json:"eventType"`
	StrategyID int64                     `json:"strategyId"`
	Config     *baseline.ExecutionConfig `json:"config"`
	Timestamp  int64                     `json:"timestamp"`
}

// StrategyTaskManager 基线任务管理器
type StrategyTaskManager struct {
	db         *gorm.DB
	client     *redis.Client
	jobHandler RecurringJobInterface
	log        *zap.Logger
	stopChan   chan struct{}
	mutex      sync.RWMutex
}

// NewTaskManager 创建任务管理器实例
func NewTaskManager(db *gorm.DB, client *redis.Client, jobHandler RecurringJobInterface) *StrategyTaskManager {
	return &StrategyTaskManager{
		db:         db,
		client:     client,
		jobHandler: jobHandler,
		log:        logger.GetLogger().Named("baseline-task-manager"),
		stopChan:   make(chan struct{}),
	}
}

// Start 启动任务管理器
func (tm *StrategyTaskManager) Start(ctx context.Context) error {
	// 订阅Redis消息

	// 启动消息处理
	go tm.startSubscriber(ctx)

	// 启动定时扫描
	go tm.startPeriodicScan(ctx)

	// 初始化时执行一次全量扫描
	if err := tm.fullScan(ctx); err != nil {
		tm.log.Error("Initial full scan failed", zap.Error(err))
	}

	return nil
}

// Stop 停止任务管理器
func (tm *StrategyTaskManager) Stop() {
	close(tm.stopChan)
}

func (tm *StrategyTaskManager) shouldAddTask(strategyID int64, config *baseline.ExecutionConfig) (bool, error) {
	now := time.Now()
	endTime, err := time.ParseInLocation(time.DateTime, config.EndTime, now.Location())
	if err != nil {
		return false, err
	}
	result := now.Before(endTime)
	tm.log.Debug("shouldAddTask compare endTime and now",
		zap.Int64("strategyID", strategyID),
		zap.String("now", now.Format(time.DateTime)),
		zap.String("endTime", endTime.Format(time.DateTime)),
		zap.Bool("result", result))
	return result, nil
}

// PublishTaskEvent 发布任务事件
func (tm *StrategyTaskManager) PublishTaskEvent(ctx context.Context, eventType string, strategyID int64, config *baseline.ExecutionConfig) error {
	event := StrategyTaskEvent{
		EventType:  eventType,
		StrategyID: strategyID,
		Config:     config,
		Timestamp:  time.Now().Unix(),
	}

	data, err := json.Marshal(event)
	if err != nil {
		return err
	}
	tm.log.Info("publish task event", zap.String("event-type", eventType), zap.Int64("strategy-id", strategyID))
	return tm.client.Publish(ctx, constants.BaselineStrategyTaskChannel, data).Err()
}

// PublishTaskEventWithRetry 带重试的发布方法
func (tm *StrategyTaskManager) PublishTaskEventWithRetry(ctx context.Context, eventType string, strategyID int64, config *baseline.ExecutionConfig) error {
	var lastErr error
	for i := 0; i < maxRetries; i++ {
		if i > 0 {
			// 重试前等待一段时间
			time.Sleep(retryDelay * time.Duration(i))
		}

		err := tm.PublishTaskEvent(ctx, eventType, strategyID, config)
		if err == nil {
			return nil
		}

		lastErr = err
		tm.log.Warn("failed to publish task event, will retry",
			zap.Error(err),
			zap.Int64("strategy-id", strategyID),
			zap.String("event-type", eventType),
			zap.Int("retry", i+1))
	}

	return fmt.Errorf("failed to publish task event after %d retries: %w", maxRetries, lastErr)
}

// startSubscriber 处理Redis消息
func (tm *StrategyTaskManager) startSubscriber(ctx context.Context) {
	pubSub := tm.client.Subscribe(ctx, constants.BaselineStrategyTaskChannel)
	defer func(pubsub *redis.PubSub) {
		err := pubsub.Close()
		if err != nil {
			tm.log.Error("failed subscribe baseline strategy task chan",
				zap.Error(err))
		}
	}(pubSub)
	// 订阅确认
	if _, err := pubSub.ReceiveTimeout(ctx, 3*time.Second); err != nil {
		tm.log.Error("subscription failed", zap.Error(err))
	}
	// 检查实际订阅数
	subs, err := tm.client.PubSubNumSub(ctx, constants.BaselineStrategyTaskChannel).Result()
	if err != nil || subs[constants.BaselineStrategyTaskChannel] == 0 {
		tm.log.Error("no active subscriptions")
	}
	for {
		select {
		case <-tm.stopChan:
			return
		case <-ctx.Done():
			return
		case msg, ok := <-pubSub.Channel(redis.WithChannelHealthCheckInterval(5 * time.Second)):
			if !ok {
				continue
			}
			if msg == nil {
				tm.log.Debug("Received nil message")
				continue
			}
			tm.log.Info("Received message", zap.Any("payload", msg.Payload), zap.String("channel", msg.Channel))
			var event StrategyTaskEvent
			if err := json.Unmarshal([]byte(msg.Payload), &event); err != nil {
				tm.log.Error("Failed to unmarshal task event", zap.Error(err))
				continue
			}

			if err := tm.handleTaskEvent(ctx, event.EventType, event.StrategyID, event.Config); err != nil {
				tm.log.Error("Failed to handle task event",
					zap.Error(err),
					zap.Int64("strategyId", event.StrategyID),
					zap.String("eventType", event.EventType))
			}
		}
	}
}

// handleTaskEvent 处理任务事件
func (tm *StrategyTaskManager) handleTaskEvent(ctx context.Context, eventType string, strategyID int64, config *baseline.ExecutionConfig) error {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()

	switch eventType {
	case constants.BaselineStrategyTaskEventAdd, constants.BaselineStrategyTaskEventUpdate:
		if ok, err := tm.shouldAddTask(strategyID, config); ok {
			return tm.addOrUpdateTask(ctx, strategyID, config)
		} else {
			tm.log.Info("The task has exceeded the effective execution time or endTime is Empty", zap.Int64("strategyID", strategyID),
				zap.Any("config", config), zap.Error(err))
			return nil
		}
	case constants.BaselineStrategyTaskEventDelete:
		return tm.deleteTask(ctx, strategyID, true)
	default:
		tm.log.Info("Unknown event type", zap.String("type", eventType))
		return nil
	}
}

// addOrUpdateTask 添加或更新任务
func (tm *StrategyTaskManager) addOrUpdateTask(ctx context.Context, strategyID int64, config *baseline.ExecutionConfig) error {
	// 获取当前任务状态
	resp, err := tm.jobHandler.Get(ctx, &baseline.GetRecurringJobRequest{
		StrategyID: strategyID,
	})
	if err != nil {
		return fmt.Errorf("failed to get job status: %w", err)
	}
	// 如果任务不存在，添加新任务
	if !resp.Existed {
		if _, err := tm.jobHandler.Add(ctx, &baseline.AddRecurringJobRequest{
			StrategyID: strategyID,
		}); err != nil {
			return fmt.Errorf("failed to add job: %w", err)
		}
	} else {
		newExpr := helper.ConvertExecutionConfigToCronExpressionWithSeconds(config)
		oldExpr := helper.ConvertExecutionConfigToCronExpressionWithSeconds(resp.ExecutionConfig)
		if newExpr != oldExpr {
			// 如果任务存在，但cron表达式不同，更新任务
			tm.log.Info("Updating recurring job", zap.Int64("strategy-id", strategyID), zap.String("new-cron", newExpr), zap.String("old-cron", oldExpr))
			if _, err := tm.jobHandler.Delete(ctx, &baseline.DeleteRecurringJobRequest{
				StrategyID: strategyID,
			}); err != nil {
				return fmt.Errorf("failed to delete job: %w", err)
			}
			if _, err := tm.jobHandler.Add(ctx, &baseline.AddRecurringJobRequest{
				StrategyID: strategyID,
			}); err != nil {
				return fmt.Errorf("failed to add job: %w", err)
			}
		} else {
			tm.log.Info("No need to update recurring job, cron is the same", zap.Int64("strategy-id", strategyID), zap.String("cron", newExpr))
		}
	}

	return nil
}

// deleteTask 删除任务
func (tm *StrategyTaskManager) deleteTask(ctx context.Context, strategyID int64, removeResources bool) error {
	if _, err := tm.jobHandler.Delete(ctx, &baseline.DeleteRecurringJobRequest{
		StrategyID: strategyID,
	}); err != nil {
		return fmt.Errorf("failed to delete job: %w", err)
	}
	if removeResources {
		return helper.DeleteResourcesWhenStrategyDeleted(ctx, strategyID)
	}
	return nil
}

// startPeriodicScan 启动定期扫描
func (tm *StrategyTaskManager) startPeriodicScan(ctx context.Context) {
	ticker := time.NewTicker(10 * time.Minute)
	defer ticker.Stop()

	for {
		select {
		case <-tm.stopChan:
			return
		case <-ticker.C:
			if err := tm.fullScan(ctx); err != nil {
				tm.log.Error("Periodic full scan failed", zap.Error(err))
			}
		}
	}
}

// fullScan 执行全量扫描
func (tm *StrategyTaskManager) fullScan(ctx context.Context) error {
	tm.mutex.Lock()
	defer tm.mutex.Unlock()
	// 获取数据库中所有需要执行的策略任务
	tasks, err := tm.getAllStrategies(ctx)
	if err != nil {
		return err
	}
	tm.log.Info("Strat FullScan Recurring/Scheduled Strategy Tasks", zap.Int("taskCount", len(tasks)))
	// 获取当前所有任务
	resp, err := tm.jobHandler.List(ctx)
	if err != nil {
		return fmt.Errorf("failed to list current jobs: %w", err)
	}

	// 创建当前任务的映射
	currentJobs := make(map[string]struct{})
	for _, item := range resp.Items {
		currentJobs[item.ID] = struct{}{}
	}

	// 同步任务
	for _, task := range tasks {
		task := task
		if ok, err := tm.shouldAddTask(task.StrategyID, task.Config); ok {
			if task.Enabled {
				strategyKey := fmt.Sprint(task.StrategyID)
				if err := tm.addOrUpdateTask(ctx, task.StrategyID, task.Config); err != nil {
					tm.log.Error("Failed to add/update task during sync",
						zap.Error(err),
						zap.Int64("strategyId", task.StrategyID))
					continue
				}
				delete(currentJobs, strategyKey)
			}
		} else {
			tm.log.Debug("Strategy no need add/update task , skip ...",
				zap.Int64("strategyId", task.StrategyID),
				zap.Error(err),
				zap.Bool("enabled", task.Enabled))
		}
	}

	// 删除不再需要的任务
	for strategyID := range currentJobs {
		sid, err := strconv.ParseInt(strategyID, 10, 64)
		if err != nil {
			tm.log.Error("Failed to parse strategy ID",
				zap.Error(err),
				zap.String("strategyId", strategyID))
			continue
		}
		if err := tm.deleteTask(ctx, sid, false); err != nil {
			tm.log.Error("Failed to delete task during sync",
				zap.Error(err),
				zap.String("strategyId", strategyID))
		}
	}

	return nil
}

// getAllStrategies 获取所有周期策略任务
func (tm *StrategyTaskManager) getAllStrategies(ctx context.Context) ([]struct {
	StrategyID int64
	Enabled    bool
	Config     *baseline.ExecutionConfig
}, error) {
	batchSize := 500
	var result []struct {
		StrategyID int64
		Enabled    bool
		Config     *baseline.ExecutionConfig
	}
	var strategies []caas.BaselineStrategy
	// 使用 FindInBatches 分批查询所有启用的策略
	err := tm.db.WithContext(ctx).
		Model(&caas.BaselineStrategy{}).
		Where("enabled = ?", 1).
		Where("execution_strategy IN ?", []string{
			string(models.ExecutionTypeScheduled),
			string(models.ExecutionTypeRecurring),
		}).
		FindInBatches(&strategies, batchSize, func(tx *gorm.DB, batch int) error {
			for _, strategy := range strategies {
				// 转换执行配置
				strategy := strategy
				config := helper.ConvertBaselineStrategyToExecutionConfig(ctx, &strategy)
				// 如果开启添加
				if strategy.Enabled == 1 {
					result = append(result, struct {
						StrategyID int64
						Enabled    bool
						Config     *baseline.ExecutionConfig
					}{
						StrategyID: strategy.ID,
						Enabled:    strategy.Enabled == 1,
						Config:     config,
					})
				}
			}
			return nil
		}).Error

	if err != nil {
		tm.log.Error("failed to get all strategies", zap.Error(err))
		return nil, fmt.Errorf("failed to get all strategies: %w", err)
	}
	return result, nil
}
