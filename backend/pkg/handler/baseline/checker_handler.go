package baseline

import (
	"context"
	"errors"
	"fmt"

	"github.com/go-logr/logr"
	"github.com/samber/lo"
	"gorm.io/gorm"
	bizerrors "harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline/helper"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	pagemodels "harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
)

var _ CheckerInterface = (*checkerHandler)(nil)

const (
	checkerLoggerName = "baseline/checker"
)

func NewCheckerHandler(db *gorm.DB) CheckerInterface {
	return &checkerHandler{
		db:  db,
		log: logger.GetLogr().WithName(checkerLoggerName),
	}
}

type checkerHandler struct {
	db  *gorm.DB
	log logr.Logger
}

// GetCustomCheckerNameExisted implements CheckerInterface.
func (c *checkerHandler) GetCustomCheckerNameExisted(ctx context.Context,
	req *models.GetCustomCheckerNameExistedRequest) (*models.GetCustomCheckerNameExistedResponse, error) {
	count := int64(0)
	if err := c.db.WithContext(ctx).Model(&caas.BaselineRule{}).Where("BINARY name = ?", req.Name).Count(&count).Error; err != nil {
		return &models.GetCustomCheckerNameExistedResponse{}, err
	}
	return &models.GetCustomCheckerNameExistedResponse{
		Existed: count > 0,
	}, nil
}

// QueryCustomCheckers ...
func (c *checkerHandler) QueryCustomCheckers(ctx context.Context, req *models.QueryCustomCheckersRequest) (*models.QueryCustomCheckersResponse, error) {
	var rules []caas.BaselineRule
	tx := c.db.WithContext(ctx).Model(&caas.BaselineRule{})
	if req.Builtin != nil {
		tx.Where("builtin = ?", lo.Ternary(*req.Builtin, 1, 0))
	} else {
		tx.Where("builtin = ?", 0)
	}
	tx, total := req.Filter.ApplyToDB(tx, &caas.BaselineRule{}, req.ToFieldMapping())
	if err := tx.Find(&rules).Error; err != nil {
		return nil, err
	}
	var items []*models.CheckItem
	for _, rule := range rules {
		items = append(items, helper.ConvertBaselineRuleToCheckItem(&rule))
	}
	return &models.QueryCustomCheckersResponse{PageableResponse: &pagemodels.PageableResponse[*models.CheckItem]{
		Items:      items,
		TotalCount: int(total),
	}}, nil
}

// GetCustomCheckerDetails ...
func (c *checkerHandler) GetCustomCheckerDetails(ctx context.Context, req *models.GetCustomCheckerDetailsRequest) (*models.GetCustomCheckerDetailsResponse, error) {
	tx := c.db.WithContext(ctx).Model(&caas.BaselineRule{})
	var rule caas.BaselineRule
	if err := tx.Where("id = ?", req.ID).First(&rule).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, bizerrors.NewFromCodeWithMessage(bizerrors.Var.BaselineCheckerNotFound, err.Error())
		}
		return nil, err
	}
	item := helper.ConvertBaselineRuleToCheckDetailItem(&rule)
	return &models.GetCustomCheckerDetailsResponse{
		CheckDetailItem: item,
	}, nil
}

// GetAssignStandardCheckers ...
func (c *checkerHandler) GetAssignStandardCheckers(ctx context.Context, req *models.GetAssignStandardCheckersRequest) (*models.GetAssignStandardCheckersResponse, error) {
	rTx := c.db.WithContext(ctx).Model(&caas.BaselineRule{})
	sTx := c.db.WithContext(ctx).Model(&caas.BaselineStandard{})
	srTx := c.db.WithContext(ctx).Model(&caas.BaselineStandardRule{})

	var standard caas.BaselineStandard
	var standardRules []caas.BaselineStandardRule
	if req.StandardId != 0 {
		if err := sTx.Where("id = ?", req.StandardId).First(&standard).Error; err != nil {
			return nil, err
		}
		if err := srTx.Where("standard_id = ?", req.StandardId).Find(&standardRules).Error; err != nil {
			return nil, err
		}
	}
	var customRules []caas.BaselineRule
	rTx.Where("builtin = ?", 0)
	if err := rTx.Find(&customRules).Error; err != nil {
		return nil, err
	}

	standardRuleStringMap := lo.SliceToMap(standardRules, func(item caas.BaselineStandardRule) (string, caas.BaselineStandardRule) {
		return fmt.Sprintf("%d/%d", item.RuleID, item.RefID), item
	})

	var customCheckIds []int64
	var items []*models.StandardCheckItem
	var selectedCheckIds []int64
	var selectedItems []models.SimpleBaselineChecker
	for _, rule := range customRules {
		item := helper.ConvertBaselineRuleToCheckItem(&rule)
		checkRawData, _ := helper.ConvertBaselineRuleToCheckRawData(&rule)
		customCheckIds = append(customCheckIds, rule.ID)
		if v, ok := standardRuleStringMap[fmt.Sprintf("%d/%d", item.ID, 0)]; ok {
			checkValue, _ := helper.ConvertBaselineStandardRuleToCheckValue(&v)
			checkRawData.Value = *checkValue
			item.RiskLevel = v.RiskLevel
			selectedCheckIds = append(selectedCheckIds, rule.ID)
		}
		standardCheckItem := &models.StandardCheckItem{
			CheckItem:  *item,
			CheckValue: checkRawData.Value,
			Suggestion: rule.Suggestion,
		}
		items = append(items, standardCheckItem)
	}
	resp := &models.GetAssignStandardCheckersResponse{}
	resp.CheckIds = customCheckIds
	if !req.IsCopy {
		resp.SelectedCheckInfo = new(models.SelectedCheckInfo)
		resp.SelectedCheckInfo.CheckIds = selectedCheckIds
		resp.SelectedCheckInfo.Checkers = selectedItems
	}
	pageResp, err := req.Filter.FilterResult(items)
	if err != nil {
		return nil, err
	}
	resp.PageableResponse = *pageResp
	return resp, nil

}

// CreateCustomChecker 创建自定义检查项
func (c *checkerHandler) CreateCustomChecker(ctx context.Context, req *models.CreateCustomCheckerRequest) (*models.CreateCustomCheckerResponse, error) {
	tx := c.db.WithContext(ctx).Model(&caas.BaselineRule{})
	var resp models.CreateCustomCheckerResponse
	err := tx.Transaction(func(t *gorm.DB) error {
		rule, err := helper.ConvertCreateCustomCheckerRequestToBaselineRule(req)
		if err != nil {
			return bizerrors.NewFromCodeWithMessage(bizerrors.Var.BaselineCheckerSaveError, err.Error())
		}
		if err := helper.ValidateSaveBaselineRule(ctx, tx, rule); err != nil {
			return err
		}
		if err := tx.Save(rule).Error; err != nil {
			return bizerrors.NewFromCodeWithMessage(bizerrors.Var.BaselineCheckerSaveError, err.Error())
		}
		resp.ID = rule.ID
		return nil
	})
	if err != nil {
		return nil, err
	}
	return &resp, nil
}

// UpdateCustomChecker 编辑自定义检查项
func (c *checkerHandler) UpdateCustomChecker(ctx context.Context, req *models.UpdateCustomCheckerRequest) (*models.UpdateCustomCheckerResponse, error) {

	var resp models.UpdateCustomCheckerResponse
	rule := &caas.BaselineRule{}
	if err := c.db.WithContext(ctx).Where("id = ?", req.ID).First(rule).Error; err != nil {
		return &resp, bizerrors.NewFromCodeWithMessage(bizerrors.Var.BaselineCheckerNotFound, err.Error())
	}
	err := c.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		//tx = tx.WithContext(ctx)
		// get rule
		rule, err := helper.ConvertEditCustomCheckerRequestToBaselineRule(req, rule)
		if err != nil {
			return bizerrors.NewFromCodeWithMessage(bizerrors.Var.BaselineCheckerSaveError, err.Error())
		}
		if err := helper.ValidateSaveBaselineRule(ctx, tx, rule); err != nil {
			return err
		}
		resp.ID = rule.ID
		if err := tx.Save(rule).Error; err != nil {
			return bizerrors.NewFromCodeWithMessage(bizerrors.Var.BaselineCheckerSaveError, err.Error())
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return &resp, nil
}

// DeleteCustomChecker ...
func (c *checkerHandler) DeleteCustomChecker(ctx context.Context, req *models.DeleteCustomCheckerRequest) (*models.DeleteCustomCheckerResponse, error) {
	if len(req.Ids) == 0 {
		return &models.DeleteCustomCheckerResponse{}, nil
	}
	var rules []caas.BaselineRule
	if err := c.db.WithContext(ctx).Model(&caas.BaselineRule{}).Where("id in (?)", req.Ids).Find(&rules).Error; err != nil {
		return nil, err
	}
	deleteIds := lo.Map(rules, func(rule caas.BaselineRule, _ int) int64 {
		return rule.ID
	})
	err := c.db.Transaction(func(tx *gorm.DB) error {
		var standardRule []caas.BaselineStandardRule
		if err := tx.WithContext(ctx).Model(&caas.BaselineStandardRule{}).Where("rule_id in (?)", deleteIds).Find(&standardRule).Error; err != nil {
			return err
		}
		if err := tx.WithContext(ctx).
			Model(&caas.BaselineRule{}).
			Where("id in (?)", deleteIds).Delete(&caas.BaselineRule{}).Error; err != nil {
			return err
		}
		if err := tx.WithContext(ctx).Model(&caas.BaselineStandardRule{}).
			Where("rule_id in (?)", deleteIds).
			Delete(&caas.BaselineStandardRule{}).Error; err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return &models.DeleteCustomCheckerResponse{
		Ids: deleteIds,
	}, nil
}

// QueryAssociatedBaselines 查询关联的基线标准
func (c *checkerHandler) QueryAssociatedBaselines(ctx context.Context, req *models.QueryAssociatedBaselinesRequest) (*models.QueryAssociatedBaselinesResponse, error) {
	tx := c.db.WithContext(ctx).Model(&caas.BaselineStandardRule{})
	var standardRules []caas.BaselineStandardRule
	if err := tx.Where("rule_id = ?", req.Id).Find(&standardRules).Error; err != nil {
		return nil, err
	}
	if len(standardRules) == 0 {
		return &models.QueryAssociatedBaselinesResponse{}, nil
	}
	var standardIds []int64
	for _, standardRule := range standardRules {
		standardIds = append(standardIds, standardRule.StandardID)
	}
	var standardGroups []caas.BaselineStandardGroup
	if err := c.db.WithContext(ctx).Model(&caas.BaselineStandardGroup{}).Find(&standardGroups).Error; err != nil {
		return nil, err
	}
	groupMap := lo.SliceToMap(standardGroups, func(item caas.BaselineStandardGroup) (int64, caas.BaselineStandardGroup) {
		return item.ID, item
	})
	sTx := c.db.WithContext(ctx).Model(&caas.BaselineStandard{})
	var standards []caas.BaselineStandard
	if err := sTx.Where("id in (?)", standardIds).Find(&standards).Error; err != nil {
		return nil, err
	}
	var items []models.AssociateBaselineItem
	for i := range standards {
		standard := standards[i]
		categoryName := ""
		if v, ok := groupMap[standard.GroupID]; ok {
			categoryName = v.Name
		}
		item := models.AssociateBaselineItem{
			StandardId:   standard.ID,
			StandardName: standard.Name,
			CategoryName: categoryName,
		}
		items = append(items, item)
	}
	return &models.QueryAssociatedBaselinesResponse{Items: items}, nil
}

// UpdateCustomCheckerBindingStandardsResponse 解绑的基线标准
func (c *checkerHandler) UpdateCustomCheckerBindingStandardsResponse(ctx context.Context, req *models.UpdateCustomCheckerBindingStandardsRequest) (*models.UpdateCustomCheckerBindingStandardsResponse, error) {
	var standardRules []caas.BaselineStandardRule
	rtx := c.db.WithContext(ctx).Model(&caas.BaselineStandardRule{}).Where("rule_id = ?", req.ID)
	if len(req.UnbindStandardIds) > 0 {
		rtx.Where("standard_id in (?)", req.UnbindStandardIds)
	}
	if err := rtx.Find(&standardRules).Error; err != nil {
		return nil, err
	}
	if len(standardRules) == 0 {
		return &models.UpdateCustomCheckerBindingStandardsResponse{}, nil
	}
	var standardIds []int64
	for _, standardRule := range standardRules {
		standardIds = append(standardIds, standardRule.StandardID)
	}
	err := c.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.WithContext(ctx).
			Where("standard_id in (?)", standardIds).Delete(&caas.BaselineStandardRule{}).Error; err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return &models.UpdateCustomCheckerBindingStandardsResponse{}, nil
}
