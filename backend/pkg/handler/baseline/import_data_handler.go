package baseline

import (
	"context"
	"errors"
	"fmt"
	"time"

	rds "github.com/redis/go-redis/v9"
	"github.com/robfig/cron/v3"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"gorm.io/gorm"
	checkerv1 "harmonycloud.cn/baseline-checker/api/v1"
	clientmgr "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline/helper"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/redislock"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

type ImportDataInterface interface {
	// ImportBuiltinBaseline 导入内置基线
	// 从底座获取内置基线，并导入到数据库中
	ImportBuiltinBaseline(ctx context.Context) error
	// Strat 启动定时任务
	Strat(ctx context.Context) error
}

func NewImportDataHandler(
	cronInstance *cron.Cron,
	db *gorm.DB,
	rds *rds.Client,
	checkerFileHandler CheckerFileInterface) ImportDataInterface {
	return &ImportDataHandler{
		log:                logger.GetLogger().Named("import-data-handler"),
		cron:               cronInstance,
		db:                 db,
		rds:                rds,
		checkerFileHandler: checkerFileHandler,
	}
}

type ImportDataHandler struct {
	log                *zap.Logger
	cron               *cron.Cron
	db                 *gorm.DB
	rds                *rds.Client
	checkerFileHandler CheckerFileInterface
}

// Strat 导入内置基线
// 从底座获取内置基线，并导入到数据库中
func (d *ImportDataHandler) Strat(ctx context.Context) error {
	log := d.log.Named("import-builtin-baseline-task")
	//注册定时任务，每30分钟执行一次

	go func() {
		// 等待1分钟再执行，初始化完毕再执行
		time.Sleep(time.Minute * 1)
		if err := d.ImportBuiltinBaseline(ctx); err != nil {
			log.Error("导入内置基线失败", zap.Error(err))
		}
	}()
	jobCron := d.cron
	if _, err := jobCron.AddFunc("*/60 * * * *", func() {
		if err := d.ImportBuiltinBaseline(ctx); err != nil {
			log.Error("导入内置基线失败", zap.Error(err))
		}
	}); err != nil {
		log.Error("注册导入内置基线定时任务失败", zap.Error(err))
		return err
	}
	return nil
}

// ImportBuiltinBaseline 导入内置基线
func (d *ImportDataHandler) ImportBuiltinBaseline(ctx context.Context) error {
	log := d.log.Named("import-builtin-baseline")
	//注册定时任务，每60分钟执行一次
	// 创建分布式锁
	lockKey := "import-builtin-baseline-lock"
	lock := redislock.NewLock(d.rds, lockKey, redislock.SimpleLock)
	// 自动加锁和解锁
	acquired, err := lock.TryLock(ctx, 5*time.Minute)
	if err != nil || !acquired {
		log.Error("获取锁失败", zap.Any("lockKey", lockKey), zap.Error(err))
		return err
	}

	log.Info("开始导入内置基线")
	// 使用事务导入内置基线
	defer func() {
		if err := lock.Release(ctx); err != nil {
			log.Error("释放锁失败", zap.Any("lockKey", lockKey), zap.Error(err))
		}
		if r := recover(); r != nil {
			log.Error("导入内置基线失败", zap.Any("error", r))
		}
	}()
	includeBusinessCluster := helper.GetImportBuiltinBaselineIncludeBusinessCluster(ctx, d.db)
	log.Info("if need import business cluster", zap.Bool("value", includeBusinessCluster))
	for _, cluster := range clientmgr.ListOnlineClusters() {
		if !includeBusinessCluster {
			if !cluster.IsHub() {
				continue
			}
		}
		if err := d.ImportBuiltinBaselineFromCluster(ctx, cluster); err != nil {
			log.Error("导入内置基线失败", zap.String("cluster", cluster.GetName()), zap.Error(err))
		}
	}
	log.Info("导入内置基线完成")
	return nil
}

// groupCheckerByComponentTypeAndName 将检查项按照组件类型和组件名称分组
func (d *ImportDataHandler) groupCheckerByComponentTypeAndName(checkers []checkerv1.Checker) map[string]map[string][]checkerv1.Checker {
	checkersByComponentTypeAndName := make(map[string]map[string][]checkerv1.Checker)
	for _, checker := range checkers {
		checker := checker.DeepCopy()
		componentType, _ := helper.GetObjectAnnotateAndLabelValue(checker, constants.BaselineComponentTypeLabelAnnotateKey)
		componentName, _ := helper.GetObjectAnnotateAndLabelValue(checker, constants.BaselineComponentNameLabelAnnotateKey)
		if checkersByComponentTypeAndName[componentType] == nil {
			checkersByComponentTypeAndName[componentType] = make(map[string][]checkerv1.Checker)
		}
		if checkersByComponentTypeAndName[componentType][componentName] == nil {
			checkersByComponentTypeAndName[componentType][componentName] = make([]checkerv1.Checker, 0)
		}
		checkersByComponentTypeAndName[componentType][componentName] = append(checkersByComponentTypeAndName[componentType][componentName], *checker)
	}
	return checkersByComponentTypeAndName
}

// ImportBuiltinBaselineFromCluster 导入内置基线
// 从底座获取内置基线，并导入到数据库中
func (d *ImportDataHandler) ImportBuiltinBaselineFromCluster(ctx context.Context, cluster clientmgr.Cluster) error {
	c := cluster.GetClient().GetCtrlClient()
	d.log.Info("开始导入内置基线", zap.String("cluster", cluster.GetName()))
	builtinLabelSelector := helper.BuildBuiltinLabelSelector()
	checkerList := &checkerv1.CheckerList{}
	if err := c.List(ctx, checkerList, client.MatchingLabelsSelector{Selector: builtinLabelSelector}); err != nil {
		return err
	}
	monitorList := &checkerv1.MonitorList{}
	if err := c.List(ctx, monitorList, client.MatchingLabelsSelector{Selector: builtinLabelSelector}); err != nil {
		return err
	}
	baselineList := &checkerv1.BaselineList{}
	if err := c.List(ctx, baselineList, client.MatchingLabelsSelector{Selector: builtinLabelSelector}); err != nil {
		return err
	}
	if len(baselineList.Items) == 0 {
		err := d.importStandardFromCheckersAndMonitors(ctx, cluster, checkerList, monitorList)
		if err != nil {
			return err
		}
	} else {
		err := d.importStandardFromBaseline(ctx, cluster, baselineList, checkerList, monitorList)
		if err != nil {
			return err
		}
	}

	return nil
}

func (d *ImportDataHandler) importStandardFromBaseline(ctx context.Context, cluster clientmgr.Cluster,
	baselineList *checkerv1.BaselineList,
	checkerList *checkerv1.CheckerList, monitorList *checkerv1.MonitorList) error {

	checkerMap := lo.SliceToMap(checkerList.Items, func(item checkerv1.Checker) (string, checkerv1.Checker) {
		return item.Name, item
	})
	db := d.db.WithContext(ctx)
	var failedBaselineStandards []string
	for _, baseline := range baselineList.Items {
		d.log.Info("导入内置基线", zap.String("cluster", cluster.GetName()), zap.String("baseline", baseline.GetName()))
		checkers := make([]checkerv1.Checker, 0)
		for _, rule := range baseline.Spec.Rules {
			if checker, ok := checkerMap[rule.CheckerName]; ok {
				checkers = append(checkers, checker)
			}
		}
		componentType, _ := helper.GetObjectAnnotateAndLabelValue(baseline.DeepCopy(), constants.BaselineComponentTypeLabelAnnotateKey)
		componentName, _ := helper.GetObjectAnnotateAndLabelValue(baseline.DeepCopy(), constants.BaselineComponentNameLabelAnnotateKey)
		categoryID := models.CategoryDefaultID
		if componentType != "" {
			var category caas.BaselineStandardGroup
			if err := db.WithContext(ctx).Where("name = ?", componentType).First(&category).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					category := caas.BaselineStandardGroup{
						Name: componentType,
					}
					if err := db.WithContext(ctx).Create(&category).Error; err != nil {
						d.log.Error("create baseline category failed", zap.String("cluster", cluster.GetName()), zap.String("category", componentType), zap.Error(err))
					} else {
						categoryID = int(category.ID)

					}
				} else {
					d.log.Error("get baseline category failed", zap.String("cluster", cluster.GetName()), zap.String("category", componentType), zap.Error(err))
				}

			} else {
				categoryID = int(category.ID)
			}
		}
		err := db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
			d.log.Info("导入内置基线检查项", zap.String("cluster", cluster.GetName()), zap.String("componentName", componentName), zap.Int("componentNameCheckersCount", len(checkers)))
			successBaselineRules, _, err := d.ImportBuiltinCheckerFromCheckersAndMonitors(ctx, cluster, checkers, monitorList.Items)
			if err != nil {
				return err
			}
			standardName := baseline.Name
			existingStandard := &caas.BaselineStandard{}
			if err := tx.Where("name = ?", standardName).First(&existingStandard).Error; err != nil {
				if !errors.Is(err, gorm.ErrRecordNotFound) {
					d.log.Error("get baseline failed", zap.String("cluster", cluster.GetName()), zap.String("baseline-standard", standardName), zap.Error(err))
					failedBaselineStandards = append(failedBaselineStandards, standardName)
					return err
				}
			}
			standardId := existingStandard.ID
			expectedDescription := "builtin standard, component: " + componentName
			if existingStandard.ID == 0 {
				standard := &caas.BaselineStandard{
					Name:        standardName,
					Builtin:     true,
					GroupID:     int64(categoryID),
					Description: expectedDescription,
					CreateUser:  "system",
					UpdateUser:  "system",
				}
				if err := tx.Create(standard).Error; err != nil {
					d.log.Error("create baseline failed", zap.String("cluster", cluster.GetName()), zap.String("baseline", standardName), zap.Error(err))
					failedBaselineStandards = append(failedBaselineStandards, standardName)
					return err
				}
				standardId = standard.ID
			} else {
				// Compare existing with new values before updating
				needsUpdate := false
				if existingStandard.GroupID != int64(categoryID) {
					existingStandard.GroupID = int64(categoryID)
					needsUpdate = true
				}
				if existingStandard.Description != expectedDescription {
					existingStandard.Description = expectedDescription
					needsUpdate = true
				}
				if needsUpdate {
					existingStandard.UpdateUser = "system" // Ensure UpdateUser is set on updates
					if err := tx.Save(existingStandard).Error; err != nil {
						d.log.Error("update baseline failed", zap.String("cluster", cluster.GetName()), zap.String("baseline", standardName), zap.Error(err))
						failedBaselineStandards = append(failedBaselineStandards, standardName)
						return err
					}
				}
			}
			// Synchronize BaselineStandardRule with checkers from Baseline CRD
			// successBaselineRules contains []*caas.BaselineRule derived from the CRD's checkers

			// 1. Get all existing BaselineStandardRule for the current standardId from DB
			var existingDbStandardRules []*caas.BaselineStandardRule
			if err := tx.Where("standard_id = ?", standardId).Find(&existingDbStandardRules).Error; err != nil {
				d.log.Error("failed to get existing baseline standard rules for standard",
					zap.Int64("standardId", standardId), // Corrected
					zap.String("baselineName", standardName),
					zap.Error(err))
				failedBaselineStandards = append(failedBaselineStandards, standardName)
				return fmt.Errorf("failed to get existing rules for standard %s: %w", standardName, err)
			}

			// Map existing DB rules by RuleID for efficient lookup and to track rules for deletion
			dbRuleIDToStandardRuleMap := make(map[int64]*caas.BaselineStandardRule) // Corrected
			for _, sr := range existingDbStandardRules {
				// Ensure sr is a pointer to avoid issues with range variable reuse if taking address later
				ruleCopy := sr
				dbRuleIDToStandardRuleMap[sr.RuleID] = ruleCopy // sr.RuleID is int64
			}

			var currentTransactionFailedRules []*caas.BaselineStandardRule

			// 2. Iterate over rules derived from CRD (successBaselineRules)
			// Add if not in DB, skip if exists (as per requirement)
			for _, crdRule := range successBaselineRules { // crdRule is *caas.BaselineRule
				if existedRule, existsInDB := dbRuleIDToStandardRuleMap[crdRule.ID]; !existsInDB { // crdRule.ID is int64
					// CRD checker exists, DB rule does not -> Add
					newStandardRule := helper.ConvertBaselineRuleToBaselineStandardRule(standardId, crdRule)
					d.log.Info("creating new baseline standard rule",
						zap.String("baseline", standardName),
						zap.Int64("standardId", standardId), // Corrected
						zap.Int64("ruleId", crdRule.ID))     // Corrected
					if err := tx.Create(newStandardRule).Error; err != nil {
						d.log.Error("failed to create baseline standard rule",
							zap.String("baseline", standardName),
							zap.Int64("ruleId", crdRule.ID), // Corrected
							zap.Error(err))
						currentTransactionFailedRules = append(currentTransactionFailedRules, newStandardRule)
					}
				} else {
					newStandardRule := helper.ConvertBaselineRuleToBaselineStandardRule(standardId, crdRule)
					if d.shouldUpdateBaselineStandardRule(existedRule, newStandardRule) {
						newStandardRule.ID = existedRule.ID
						if err := tx.Save(newStandardRule).Error; err != nil {
							d.log.Error("failed to create baseline standard rule",
								zap.String("baseline", standardName),
								zap.Int64("ruleId", crdRule.ID), // Corrected
								zap.Error(err))
							currentTransactionFailedRules = append(currentTransactionFailedRules, newStandardRule)
						}
					}
					// CRD checker exists, DB rule also exists -> Skip (and remove from map to mark as processed)
					d.log.Info("baseline standard rule already exists in DB, skipping",
						zap.String("baseline", standardName),
						zap.Int64("standardId", standardId), // Corrected
						zap.Int64("ruleId", crdRule.ID))     // Corrected
					delete(dbRuleIDToStandardRuleMap, crdRule.ID) // crdRule.ID is int64
				}
			}

			// 3. Delete rules from DB that are no longer in CRD
			// Any rules remaining in dbRuleIDToStandardRuleMap are present in DB but not in CRD's checkers
			for ruleIDToDelete, standardRuleToDelete := range dbRuleIDToStandardRuleMap { // ruleIDToDelete is int64
				d.log.Info("deleting baseline standard rule as corresponding checker not in CRD",
					zap.String("baseline", standardName),
					zap.Int64("standardId", standardId), // Corrected
					zap.Int64("ruleId", ruleIDToDelete)) // Corrected
				if err := tx.Delete(standardRuleToDelete).Error; err != nil {
					d.log.Error("failed to delete baseline standard rule",
						zap.String("baseline", standardName),
						zap.Int64("ruleId", ruleIDToDelete), // Corrected
						zap.Error(err))
					currentTransactionFailedRules = append(currentTransactionFailedRules, standardRuleToDelete)
				}
			}

			if len(currentTransactionFailedRules) > 0 {
				d.log.Error("one or more baseline standard rule operations failed during transaction",
					zap.String("cluster", cluster.GetName()),
					zap.String("baseline", standardName))
				// Mark this baseline standard as failed for overall reporting
				failedBaselineStandards = append(failedBaselineStandards, standardName)
				// Construct an error message detailing failed rules if necessary, or a generic one
				return fmt.Errorf("import builtin baseline rule operations failed for baseline %s, %d rules affected", standardName, len(currentTransactionFailedRules))
			}

			return nil
		})
		if err != nil {
			// Error from transaction will be logged here.
			// failedBaselineStandards might have been appended within the transaction.
			d.log.Error("transaction failed for import builtin baseline", zap.String("cluster", cluster.GetName()), zap.String("baselineName", baseline.Name), zap.String("componentName", componentName), zap.Error(err))
		}
	}
	if len(failedBaselineStandards) > 0 {
		d.log.Error("import builtin baseline failed", zap.String("cluster", cluster.GetName()), zap.Strings("failedBaselines", failedBaselineStandards))
		return fmt.Errorf("import builtin baseline failed, failedBaselines: %v", failedBaselineStandards)
	}
	return nil
}

func (d *ImportDataHandler) importStandardFromCheckersAndMonitors(ctx context.Context, cluster clientmgr.Cluster, checkerList *checkerv1.CheckerList, monitorList *checkerv1.MonitorList) error {
	groupCheckerByComponentTypeAndName := d.groupCheckerByComponentTypeAndName(checkerList.Items)
	d.log.Info("导入内置基线", zap.String("cluster", cluster.GetName()),
		zap.Int("componentTypeCount", len(groupCheckerByComponentTypeAndName)),
		zap.Int("checkerCount", len(checkerList.Items)),
		zap.Int("monitorCount", len(monitorList.Items)))
	var failedBaselineStandards []string
	db := d.db
	for componentType, componentTypeCheckerMap := range groupCheckerByComponentTypeAndName {
		d.log.Info("导入内置基线", zap.String("cluster", cluster.GetName()), zap.String("componentType", componentType),
			zap.Int("componentTypeCheckerCount", len(componentTypeCheckerMap)))
		categoryID := models.CategoryDefaultID
		if componentType != "" {
			var category caas.BaselineStandardGroup
			if err := db.WithContext(ctx).Where("name = ?", componentType).First(&category).Error; err != nil {
				if errors.Is(err, gorm.ErrRecordNotFound) {
					category := caas.BaselineStandardGroup{
						Name: componentType,
					}
					if err := db.WithContext(ctx).Create(&category).Error; err != nil {
						d.log.Error("create baseline category failed", zap.String("cluster", cluster.GetName()), zap.String("category", componentType), zap.Error(err))
					} else {
						categoryID = int(category.ID)

					}
				} else {
					d.log.Error("get baseline category failed", zap.String("cluster", cluster.GetName()), zap.String("category", componentType), zap.Error(err))
				}

			} else {
				categoryID = int(category.ID)
			}
		}
		for componentName, checkers := range componentTypeCheckerMap {
			err := db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
				d.log.Info("导入内置基线检查项", zap.String("cluster", cluster.GetName()), zap.String("componentName", componentName), zap.Int("componentNameCheckersCount", len(checkers)))
				successBaselineRules, _, err := d.ImportBuiltinCheckerFromCheckersAndMonitors(ctx, cluster, checkers, monitorList.Items)
				if err != nil {
					return err
				}
				standardName := fmt.Sprintf("%s-%s", cluster.GetName(), componentName)
				existingStandard := &caas.BaselineStandard{}
				if err := tx.Where("name = ?", standardName).First(&existingStandard).Error; err != nil {
					if !errors.Is(err, gorm.ErrRecordNotFound) {
						d.log.Error("get baseline failed", zap.String("cluster", cluster.GetName()), zap.String("baseline-standard", standardName), zap.Error(err))
						failedBaselineStandards = append(failedBaselineStandards, standardName)
						return err
					}
				}
				standardId := existingStandard.ID
				if existingStandard.ID == 0 {
					standard := &caas.BaselineStandard{
						Name:        standardName,
						Builtin:     true,
						GroupID:     int64(categoryID),
						Description: "builtin baseline standard",
						CreateUser:  "system",
						UpdateUser:  "system",
					}
					if err := tx.Create(standard).Error; err != nil {
						d.log.Error("create baseline failed", zap.String("cluster", cluster.GetName()), zap.String("baseline", standardName), zap.Error(err))
						failedBaselineStandards = append(failedBaselineStandards, standardName)
						return err
					}
					standardId = standard.ID
				} else {
					// Compare existing with new values before updating
					needsUpdate := false
					expectedDescription := "builtin baseline standard" // Ensure this matches the creation logic if it can vary
					if existingStandard.GroupID != int64(categoryID) {
						existingStandard.GroupID = int64(categoryID)
						needsUpdate = true
					}
					if existingStandard.Description != expectedDescription {
						existingStandard.Description = expectedDescription
						needsUpdate = true
					}
					if needsUpdate {
						existingStandard.UpdateUser = "system" // Ensure UpdateUser is set on updates
						if err := tx.Save(existingStandard).Error; err != nil {
							d.log.Error("update baseline failed", zap.String("cluster", cluster.GetName()), zap.String("baseline", standardName), zap.Error(err))
							failedBaselineStandards = append(failedBaselineStandards, standardName)
							return err
						}
					}
				}
				// find all rules in baseline
				failedStandardRules := make([]*caas.BaselineStandardRule, 0)
				for _, rule := range successBaselineRules {
					standardRule := helper.ConvertBaselineRuleToBaselineStandardRule(standardId, rule)
					existingRule := &caas.BaselineStandardRule{}
					if err := tx.Where("standard_id = ? AND rule_id = ?", standardId, rule.ID).First(&existingRule).Error; err != nil {
						if !errors.Is(err, gorm.ErrRecordNotFound) {
							d.log.Error("get baseline rule failed", zap.String("cluster", cluster.GetName()), zap.String("baseline", standardName), zap.Error(err))
							failedStandardRules = append(failedStandardRules, standardRule)
							continue
						}
					}
					if existingRule.ID == 0 {
						if err := tx.Create(standardRule).Error; err != nil {
							d.log.Error("create baseline rule failed", zap.String("cluster", cluster.GetName()), zap.String("baseline", standardName), zap.Error(err))
							failedStandardRules = append(failedStandardRules, standardRule)
							continue
						}
					} else {
						standardRule.ID = existingRule.ID
						if err := tx.Save(standardRule).Error; err != nil {
							d.log.Error("update baseline rule failed", zap.String("cluster", cluster.GetName()), zap.String("baseline", standardName), zap.Error(err))
							failedStandardRules = append(failedStandardRules, standardRule)
							continue
						}
					}
				}
				if len(failedStandardRules) > 0 {
					d.log.Error("import builtin baseline rule failed", zap.String("cluster", cluster.GetName()), zap.String("baseline", standardName))
					failedBaselineStandards = append(failedBaselineStandards, standardName)
					return fmt.Errorf("import builtin baseline rule failed, failedStandardRules: %v", failedStandardRules)
				}
				return nil
			})
			if err != nil {
				d.log.Error("import builtin baseline failed", zap.String("cluster", cluster.GetName()), zap.String("componentName", componentName), zap.Error(err))
			}
		}
	}
	if len(failedBaselineStandards) > 0 {
		d.log.Error("import builtin baseline failed", zap.String("cluster", cluster.GetName()), zap.Strings("failedBaselines", failedBaselineStandards))
		return fmt.Errorf("import builtin baseline failed, failedBaselines: %v", failedBaselineStandards)
	}
	return nil
}

// UploadBuiltinCheckerFile 上传内置检查项文件
// 上传内置检查项文件，并返回文件信息
func (d *ImportDataHandler) UploadBuiltinCheckerFile(ctx context.Context,
	fileName string, content string) (models.FileItem, error) {
	if content == "" {
		//return models.FileItem{}, fmt.Errorf("checker %s has no content", fileName)
		d.log.Warn("checker has no content", zap.String("checker", fileName))
	}

	// 创建上传请求
	request := &models.UploadCheckerFileRequest{
		Content:  []byte(content),
		FileName: fileName,
	}

	// 上传文件
	response, err := d.checkerFileHandler.UploadCheckerFile(ctx, request)
	if err != nil {
		return models.FileItem{}, fmt.Errorf("upload file failed: %v", err)
	}
	return response.FileItem, nil
}

//	ImportBuiltinCheckerFromCheckersAndMonitors 导入内置检查项
//
// 从底座获取内置检查项，并导入到数据库中
// 返回导入成功和失败的内置BaselineRule
func (d *ImportDataHandler) ImportBuiltinCheckerFromCheckersAndMonitors(ctx context.Context,
	cluster clientmgr.Cluster,
	builtinCheckers []checkerv1.Checker,
	builtinMonitors []checkerv1.Monitor) (
	successBaselineRules []*caas.BaselineRule,
	failedBaselineRules []string,
	err error) {
	builtinMonitorsMap := make(map[string]checkerv1.Monitor)
	for _, monitor := range builtinMonitors {
		builtinMonitorsMap[monitor.Name] = monitor
	}
	db := d.db
	for _, checker := range builtinCheckers {
		monitorName := checker.Spec.MonitorSelector.Name
		monitor, ok := builtinMonitorsMap[monitorName]
		if !ok {
			d.log.Error("builtin monitor not found", zap.String("name", monitorName))
			continue
		}
		rule, err := helper.ConvertCheckerAndMonitorToBaselineRule(ctx, &checker, &monitor, d.UploadBuiltinCheckerFile)
		if err != nil {
			d.log.Error("convert checker and monitor to baseline rule failed", zap.String("cluster", cluster.GetName()),
				zap.String("checker", checker.Name), zap.Error(err))
			failedBaselineRules = append(failedBaselineRules, rule.Name)
			continue
		}
		existingRule := &caas.BaselineRule{}
		if err := db.WithContext(ctx).Where("name = ?", rule.Name).First(&existingRule).Error; err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				d.log.Error("get baseline rule failed", zap.String("name", rule.Name), zap.Error(err))
				failedBaselineRules = append(failedBaselineRules, rule.Name)
				continue
			}
		}
		if existingRule.ID == 0 {
			if err := db.WithContext(ctx).Create(rule).Error; err != nil {
				d.log.Error("create baseline rule failed", zap.String("cluster", cluster.GetName()),
					zap.String("checker", checker.Name), zap.Error(err))
				failedBaselineRules = append(failedBaselineRules, rule.Name)
				continue
			}
			successBaselineRules = append(successBaselineRules, rule)
		} else {
			// Preserve ID and other non-updatable fields from existingRule
			newRuleData := *rule // Copy new data
			newRuleData.ID = existingRule.ID
			// CreateUser is likely set by GORM or helper, ensure it's preserved if not set by import
			if newRuleData.CreateUser == "" && existingRule.CreateUser != "" { // Example: Preserve if not set by conversion
				newRuleData.CreateUser = existingRule.CreateUser
			}

			if d.shouldUpdateBaselineRule(existingRule, &newRuleData) {
				newRuleData.UpdateUser = "system" // Set by system during import
				if err := db.WithContext(ctx).Save(&newRuleData).Error; err != nil {
					d.log.Error("update baseline rule failed", zap.String("cluster", cluster.GetName()),
						zap.String("checker", checker.Name), zap.Error(err))
					failedBaselineRules = append(failedBaselineRules, newRuleData.Name)
					continue
				}
				successBaselineRules = append(successBaselineRules, &newRuleData)
			} else {
				// No changes, add existing rule to success list
				successBaselineRules = append(successBaselineRules, existingRule)
			}
		}
	}
	if len(failedBaselineRules) > 0 {
		d.log.Error("import builtin checker from cluster failed", zap.String("cluster", cluster.GetName()), zap.Strings("failedNames", failedBaselineRules))
		return nil, failedBaselineRules, fmt.Errorf("import builtin checker from cluster failed, failedNames: %v", failedBaselineRules)
	}
	return successBaselineRules, failedBaselineRules, nil
}

// shouldUpdateBaselineStandardRule ...
func (d *ImportDataHandler) shouldUpdateBaselineStandardRule(oldObj, newObj *caas.BaselineStandardRule) bool {
	if oldObj == nil || newObj == nil {
		return true
	}
	return oldObj.RefID != newObj.RefID ||
		oldObj.StandardID != newObj.StandardID ||
		oldObj.RuleID != newObj.RuleID ||
		oldObj.RiskLevel != newObj.RiskLevel ||
		oldObj.CheckValuePrefix != newObj.CheckValuePrefix ||
		oldObj.CheckValueContent != newObj.CheckValueContent ||
		oldObj.FileContentID != newObj.FileContentID ||
		oldObj.FileContentPath != newObj.FileContentPath ||
		oldObj.FileContentUniqueKey != newObj.FileContentUniqueKey ||
		oldObj.FileContentName != newObj.FileContentName ||
		oldObj.FileContentLink != newObj.FileContentLink

}

// shouldUpdateBaselineRule compares two BaselineRule instances to determine if an update is needed.
// Note: For 'Params', if it's a map/slice, a simple '!=' might not be enough for deep comparison.
// Consider using reflect.DeepEqual or a custom comparison for Params if needed.
func (d *ImportDataHandler) shouldUpdateBaselineRule(existing, new *caas.BaselineRule) bool {
	if existing.Description != new.Description {
		return true
	}
	if existing.Suggestion != new.Suggestion {
		return true
	}
	if existing.Kind != new.Kind {
		return true
	}
	if existing.RiskLevel != new.RiskLevel {
		return true
	}
	if existing.Version != new.Version {
		return true
	}
	if existing.CheckType != new.CheckType {
		return true
	}
	if existing.CheckResourceType != new.CheckResourceType {
		return true
	}
	if existing.CheckMode != new.CheckMode {
		return true
	}
	if existing.NodeRoles != new.NodeRoles {
		return true
	}
	if existing.NodeSelectors != new.NodeSelectors {
		return true
	}
	if existing.FileType != new.FileType {
		return true
	}
	if existing.FileLocationMode != new.FileLocationMode {
		return true
	}
	if existing.FileLocationPath != new.FileLocationPath {
		return true
	}
	if existing.K8sResourceAPIVersion != new.K8sResourceAPIVersion {
		return true
	}
	if existing.K8sResourceName != new.K8sResourceName {
		return true
	}
	if existing.K8sResourceNamespace != new.K8sResourceNamespace {
		return true
	}
	if existing.K8sResourceKind != new.K8sResourceKind {
		return true
	}
	if existing.K8sResourceLabels != new.K8sResourceLabels {
		return true
	}
	if existing.FileMatchContent != new.FileMatchContent {
		return true
	}
	if existing.FileContentID != new.FileContentID {
		return true
	}
	if existing.Command != new.Command {
		return true
	}
	if existing.CommandMatchType != new.CommandMatchType {
		return true
	}
	if existing.CommandMatchValue != new.CommandMatchValue {
		return true
	}

	// Fields like ID, Name, Builtin, CreateUser, UpdateUser, CreateTime, UpdateTime, DeleteTime
	// are generally not compared here as they are either identifiers, audit fields,
	// or handled differently (e.g., UpdateUser is set if this function returns true).
	return false
}

// ImportCheckerFromFiles 导入检查项文件
// 从上传文件中导入检查项
func (d *ImportDataHandler) ImportCheckerFromFiles(ctx context.Context, cluster clientmgr.Cluster) error {
	return nil
}

func (d *ImportDataHandler) DownloadImportCheckerFileTemplate(ctx context.Context) error {
	return nil
}
