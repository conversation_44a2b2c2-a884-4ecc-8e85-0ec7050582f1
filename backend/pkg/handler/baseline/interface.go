package baseline

import (
	"context"

	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
)

// CheckerFileInterface  ...
type CheckerFileInterface interface {
	// UploadCheckerFile upload checker needed file to minio
	UploadCheckerFile(ctx context.Context, req *models.UploadCheckerFileRequest) (*models.UploadCheckerFileResponse, error)
}

// CheckerInterface 检查项
type CheckerInterface interface {

	// GetCustomCheckerNameExisted ...
	GetCustomCheckerNameExisted(ctx context.Context, req *models.GetCustomCheckerNameExistedRequest) (*models.GetCustomCheckerNameExistedResponse, error)

	// QueryCustomChe<PERSON> retrieves a paginated list of custom checkers with filters.
	QueryCustomCheckers(ctx context.Context, req *models.QueryCustomCheckersRequest) (*models.QueryCustomCheckersResponse, error)

	// GetCustomCheckerDetails retrieves details of a custom checker by ID.
	GetCustomCheckerDetails(ctx context.Context, req *models.GetCustomCheckerDetailsRequest) (*models.GetCustomCheckerDetailsResponse, error)

	// GetAssignStandardCheckers 获取分配给基线标准的检查项
	GetAssignStandardCheckers(ctx context.Context, req *models.GetAssignStandardCheckersRequest) (*models.GetAssignStandardCheckersResponse, error)

	// CreateCustomChecker creates a new custom checker.
	CreateCustomChecker(ctx context.Context, req *models.CreateCustomCheckerRequest) (*models.CreateCustomCheckerResponse, error)

	// UpdateCustomChecker edits an existing custom checker.
	UpdateCustomChecker(ctx context.Context, req *models.UpdateCustomCheckerRequest) (*models.UpdateCustomCheckerResponse, error)

	// DeleteCustomChecker deletes a custom checker by ID.
	DeleteCustomChecker(ctx context.Context, req *models.DeleteCustomCheckerRequest) (*models.DeleteCustomCheckerResponse, error)

	// QueryAssociatedBaselines retrieves the baselines associated with a custom checker.
	QueryAssociatedBaselines(ctx context.Context, req *models.QueryAssociatedBaselinesRequest) (*models.QueryAssociatedBaselinesResponse, error)

	// UpdateCustomCheckerBindingStandardsResponse update custom checkers binding association
	UpdateCustomCheckerBindingStandardsResponse(ctx context.Context, req *models.UpdateCustomCheckerBindingStandardsRequest) (*models.UpdateCustomCheckerBindingStandardsResponse, error)
}

type StandardInterface interface {

	// GetBaselineNameExisted ...
	GetBaselineNameExisted(ctx context.Context, req *models.GetBaselineNameExistedRequest) (*models.GetBaselineNameExistedResponse, error)

	// QueryBaselines retrieves a paginated list of baselines with filters.
	QueryBaselines(ctx context.Context, req *models.QueryBaselinesRequest) (*models.QueryBaselinesResponse, error)

	// GetBaselineDetails retrieves details of a baseline by ID.
	GetBaselineDetails(ctx context.Context, req *models.GetBaselineDetailsRequest) (*models.GetBaselineDetailsResponse, error)

	// GetBaselineCheckers get baseline checkers by checkIds And ID
	GetBaselineCheckers(ctx context.Context, req *models.GetBaselineCheckersRequest) (*models.GetBaselineCheckersResponse, error)

	// CreateBaseline creates a new baseline.
	CreateBaseline(ctx context.Context, req *models.CreateBaselineRequest) (*models.CreateBaselineResponse, error)

	// UpdateBaseline edits an existing baseline.
	UpdateBaseline(ctx context.Context, req *models.UpdateBaselineRequest) (*models.UpdateBaselineResponse, error)

	// DeleteBaseline deletes a baseline by ID.
	DeleteBaseline(ctx context.Context, req *models.DeleteBaselineRequest) (*models.DeleteBaselineResponse, error)

	// GetCategoryNameExisted ...
	GetCategoryNameExisted(ctx context.Context, req *models.GetCategoryNameExistedRequest) (*models.GetCategoryNameExistedResponse, error)

	// QueryCategoryStandards query category standards
	QueryCategoryStandards(ctx context.Context, req *models.QueryCategoryStandardsRequest) (*models.QueryCategoryStandardsResponse, error)

	// QueryBaselineCategories retrieves baseline standard categories.
	QueryBaselineCategories(ctx context.Context, req *models.QueryBaselineCategoriesRequest) (*models.QueryBaselineCategoriesResponse, error)

	// CreateBaselineCategory creates a new baseline category.
	CreateBaselineCategory(ctx context.Context, req *models.CreateBaselineCategoryRequest) (*models.CreateBaselineCategoryResponse, error)

	// EditBaselineCategory edits an existing baseline category.
	EditBaselineCategory(ctx context.Context, req *models.EditBaselineCategoryRequest) (*models.EditBaselineCategoryResponse, error)

	// DeleteBaselineCategory deletes a baseline category by ID.
	DeleteBaselineCategory(ctx context.Context, req *models.DeleteBaselineCategoryRequest) (*models.DeleteBaselineCategoryResponse, error)

	// QueryAssociatedStrategies retrieves the strategies associated with a baseline.
	QueryAssociatedStrategies(ctx context.Context, req *models.QueryAssociatedStrategiesRequest) (*models.QueryAssociatedStrategiesResponse, error)

	// UpdateBindingStrategies ...
	UpdateBindingStrategies(ctx context.Context, req *models.UpdateBindingStrategiesRequest) (*models.UpdateBindingStrategiesResponse, error)
}

// StrategyInterface defines the interface for managing baseline strategies.
type StrategyInterface interface {

	// GetBaselineStrategyNameExisted ...
	GetBaselineStrategyNameExisted(ctx context.Context, req *models.GetBaselineStrategyNameExistedRequest) (*models.GetBaselineStrategyNameExistedResponse, error)

	// QueryBaselineStrategies retrieves a paginated list of baseline strategies with filters.
	QueryBaselineStrategies(ctx context.Context, req *models.QueryBaselineStrategiesRequest) (*models.QueryBaselineStrategiesResponse, error)

	// GetBaselineStrategyDetails retrieves details of a baseline strategy by ID.
	GetBaselineStrategyDetails(ctx context.Context, req *models.GetBaselineStrategyDetailsRequest) (*models.GetBaselineStrategyDetailsResponse, error)

	// CreateBaselineStrategy creates a new baseline strategy.
	CreateBaselineStrategy(ctx context.Context, req *models.CreateBaselineStrategyRequest) (*models.CreateBaselineStrategyResponse, error)

	// UpdateBaselineStrategy edits an existing baseline strategy.
	UpdateBaselineStrategy(ctx context.Context, req *models.UpdateBaselineStrategyRequest) (*models.UpdateBaselineStrategyResponse, error)

	// DeleteBaselineStrategy deletes a baseline strategy by ID.
	DeleteBaselineStrategy(ctx context.Context, req *models.DeleteBaselineStrategyRequest) (*models.DeleteBaselineStrategyResponse, error)

	// SwitchBaselineStrategy enables or disables a baseline strategy.
	SwitchBaselineStrategy(ctx context.Context, req *models.SwitchBaselineStrategyRequest) (*models.SwitchBaselineStrategyResponse, error)

	// GetLastCheckJobStatus get last check job status
	GetLastCheckJobStatus(ctx context.Context, req *models.GetLastCheckJobStatusRequest) (*models.GetLastCheckJobStatusResponse, error)

	// GetBaselineStrategySummary  get strategy summary
	GetBaselineStrategySummary(ctx context.Context, req *models.GetBaselineStrategySummaryRequest) (*models.GetBaselineStrategySummaryResponse, error)

	// ExecuteCheckJob execute the strategy check job
	ExecuteCheckJob(ctx context.Context, req *models.ExecuteCheckJobRequest) (*models.ExecuteCheckJobResponse, error)

	// GetClusterCheckResults get cluster check job report
	GetClusterCheckResults(ctx context.Context, req *models.GetClusterCheckResultsRequest) (*models.GetClusterCheckResultsResponse, error)

	// GetBaselineStandardCheckResults get baseline standard check job report
	GetBaselineStandardCheckResults(ctx context.Context, req *models.GetBaselineStandardCheckResultsRequest) (*models.GetBaselineStandardCheckResultsResponse, error)

	// DownloadBaselineStandardCheckResultsReport download baseline standard check results report table
	DownloadBaselineStandardCheckResultsReport(ctx context.Context,
		req *models.DownloadBaselineStandardCheckResultsReportRequest) (*models.DownloadBaselineStandardCheckResultsReportResponse, error)

	// GetCheckResults get strategy check item job report
	GetCheckResults(ctx context.Context, req *models.GetCheckResultsRequest) (*models.GetCheckResultsResponse, error)

	// DownloadCheckResultsReport download strategy check results report table
	DownloadCheckResultsReport(ctx context.Context, req *models.DownloadCheckResultsReportRequest) (*models.DownloadCheckResultsReportResponse, error)

	// UpdateBindingBaselineStandards ...
	UpdateBindingBaselineStandards(ctx context.Context, req *models.UpdateBindingBaselineStandardsRequest) (*models.UpdateBindingBaselineStandardsResponse, error)
}

type StrategyJobInterface interface {
	// ExecuteJob 执行标准检查
	ExecuteJob(ctx context.Context, req *models.ExecuteCheckJobRequest) (resp *models.ExecuteCheckJobResponse, err error)
}

type RecurringJobInterface interface {
	// Get 获取定时任务
	Get(ctx context.Context, req *models.GetRecurringJobRequest) (*models.GetRecurringJobResponse, error)
	// Add 添加定时任务
	Add(ctx context.Context, req *models.AddRecurringJobRequest) (*models.AddRecurringJobResponse, error)
	// Delete 删除定时任务
	Delete(ctx context.Context, req *models.DeleteRecurringJobRequest) (*models.DeleteRecurringJobResponse, error)
	// List 获取定时任务
	List(ctx context.Context) (*models.ListRecurringJobResponse, error)
}
