package infra

import (
	"context"

	baselinemodels "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
)

// MonitorAdapter Monitor 监视器
type MonitorAdapter interface {
	// ListMonitor List 获取监视项列表
	ListMonitor(ctx context.Context, param *baselinemodels.MonitorParam) ([]*baselinemodels.InfraMonitorItem, error)
	// CreateMonitor Create 创建监视项
	CreateMonitor(ctx context.Context, param *baselinemodels.MonitorParam) (*baselinemodels.InfraMonitorItem, error)
	// DeleteMonitor Delete 删除监视项
	DeleteMonitor(ctx context.Context, param *baselinemodels.MonitorParam) error
	// EnsureMonitorsRunning 确保 monitor 正在运行
	EnsureMonitorsRunning(ctx context.Context, param *baselinemodels.MonitorParam) (ready bool, runningMonitors, abnormalMonitors []baselinemodels.MonitorStatusResult, err error)
}

// Checker 检查项
type CheckerAdapter interface {
	// ListChecker List 获取检查项
	ListChecker(ctx context.Context, param *baselinemodels.CheckerParam) ([]*baselinemodels.InfraCheckerItem, error)
	// CreateChecker Create 创建检查项
	CreateChecker(ctx context.Context, param *baselinemodels.CheckerParam) (*baselinemodels.InfraCheckerItem, error)
	// DeleteChecker Delete 删除检查项
	DeleteChecker(ctx context.Context, param *baselinemodels.CheckerParam) error
}

// BaselineAdapter ...
type BaselineAdapter interface {
	// ListBaseline List 获取基线
	ListBaseline(ctx context.Context, param *baselinemodels.BaselineParam) ([]*baselinemodels.InfraBaselineItem, error)
	// CreateBaseline Create 创建基线
	CreateBaseline(ctx context.Context, param *baselinemodels.BaselineParam) (*baselinemodels.InfraBaselineItem, error)
	// DeleteBaseline Delete 删除基线
	DeleteBaseline(ctx context.Context, param *baselinemodels.BaselineParam) error
}
