package infra

import (
	"context"
	"fmt"
	"strconv"

	"github.com/rs/xid"
	"go.uber.org/zap"
	v1 "harmonycloud.cn/baseline-checker/api/v1"
	infrachecker "harmonycloud.cn/baseline-checker/pkg/models/checker"
	clientmgr "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtimclient "sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
)

func NewV1BaselineAdapter() BaselineAdapter {
	return &V1BaselineAdapter{
		log:            logger.GetLogger().Named("v1-baseline-adapter"),
		checkerAdapter: NewV1CheckerAdapter(),
		monitorAdapter: NewV1MonitorAdapter(),
	}
}

type V1BaselineAdapter struct {
	log            *zap.Logger
	checkerAdapter CheckerAdapter
	monitorAdapter MonitorAdapter
}

// CreateBaseline 每次检查创建基线资源
// 名称生成规则: baseline-strategyID-standardID
func (v *V1BaselineAdapter) CreateBaseline(ctx context.Context, param *models.BaselineParam) (*models.InfraBaselineItem, error) {
	if !param.Daemon && (param.StrategyID == 0 || param.ClusterName == "" || param.StandardID == 0) {
		return nil, fmt.Errorf("invalid param, strategy id: %d, cluster name: %s, standard id: %d", param.StrategyID, param.ClusterName, param.StandardID)
	}
	if param.Daemon && param.StrategyID == 0 {
		return nil, fmt.Errorf("invalid param, strategy id: %d", param.StrategyID)
	}
	checkParams := param.Checkers
	// create monitor and checker
	var baselineCheckers []models.InfraBaselineCheckerItem
	var rules []infrachecker.Rule
	for _, checkParam := range checkParams {
		// create checker
		var checkerName string
		if checkParam.CheckerName == "" {
			checker, err := v.checkerAdapter.CreateChecker(ctx, &baseline.CheckerParam{
				ClusterName:  param.ClusterName,
				StandardID:   param.StandardID,
				StrategyID:   param.StrategyID,
				CheckID:      checkParam.CheckID,
				CheckRawData: checkParam.CheckRawData,
			})
			if err != nil {
				return nil, err
			}
			if checker == nil {
				return nil, fmt.Errorf("create checker failed, strategy id: %d, cluster name: %s, standard id: %d, check id: %d", param.StrategyID, param.ClusterName, param.StandardID, checkParam.CheckID)
			}
			checkerName = checker.CheckerName
		} else {
			checkerName = checkParam.CheckerName
		}

		rules = append(rules, infrachecker.Rule{
			CheckerName: checkerName,
		})
		baselineCheckers = append(baselineCheckers, models.InfraBaselineCheckerItem{
			CheckerName: checkerName,
			CheckID:     checkParam.CheckID,
		})
	}

	labels := map[string]string{
		constants.BaselineStrategyIDLabelKey:                     fmt.Sprint(param.StrategyID),
		constants.BaselineClusterNameLabelKey:                    param.ClusterName,
		constants.BaselineStandardIDLabelKey:                     fmt.Sprint(param.StandardID),
		constants.BaselineStandardJobIDLabelKey:                  fmt.Sprint(param.StandardJobID),
		constants.BaselineStrategyExecutionTypeLabelKey:          string(param.ExecutionConfig.ExecutionType),
		constants.BaselineStrategyExecutionRecurringTypeLabelKey: string(param.ExecutionConfig.RecurringType),
		constants.BaselineStrategyExecutionDaemonLabelKey:        fmt.Sprintf("%v", param.Daemon),
	}

	//baselineName := fmt.Sprintf("%s-%d-%d-%s-", constants.BaselineResourcePrefixName, param.StrategyID, param.StandardID, param.ClusterName)
	baselineName := fmt.Sprintf("%s%s", "b", xid.New().String())
	if param.Daemon {
		baselineName = fmt.Sprintf("strategy-%d", param.StrategyID)
		delete(labels, constants.BaselineStandardJobIDLabelKey)
		delete(labels, constants.BaselineClusterNameLabelKey)
		delete(labels, constants.BaselineStandardIDLabelKey)
	}

	baseline := &v1.Baseline{
		ObjectMeta: metav1.ObjectMeta{
			//GenerateName: baselineName,
			Labels: labels,
			Name:   baselineName,
		},
		Spec: infrachecker.Baseline{
			Rules: rules,
		},
	}

	cluster, err := clientmgr.OnlineClusterAssert(clientmgr.GetCluster(param.ClusterName))
	if err != nil {
		return nil, err
	}
	ctrlclient := cluster.GetClient().GetCtrlClient()
	updateBaseline := baseline.DeepCopy()
	if _, err := controllerutil.CreateOrPatch(ctx, ctrlclient, updateBaseline, func() error {
		updateBaseline.Labels = utils.MergeMap(baseline.Labels, updateBaseline.Labels)
		updateBaseline.Annotations = utils.MergeMap(baseline.Annotations, updateBaseline.Annotations)
		updateBaseline.Spec = baseline.Spec
		return nil
	}); err != nil {
		return nil, err
	}

	return &models.InfraBaselineItem{
		BaselineName: updateBaseline.Name,
		ClusterName:  param.ClusterName,
		StandardID:   param.StandardID,
		StrategyID:   param.StrategyID,
		Checkers:     baselineCheckers,
	}, nil
}

// DeleteBaseline implements BaselineAdapter.
func (v *V1BaselineAdapter) DeleteBaseline(ctx context.Context, param *baseline.BaselineParam) error {
	if param.BaselineName != "" || param.StrategyID == 0 || param.ClusterName == "" || param.StandardID == 0 {
		return fmt.Errorf("invalid param, strategy id: %d, cluster name: %s, standard id: %d", param.StrategyID, param.ClusterName, param.StandardID)
	}
	if param.BaselineName != "" && param.ClusterName != "" {
		// first get baseline
		cluster, err := clientmgr.OnlineClusterAssert(clientmgr.GetCluster(param.ClusterName))
		if err != nil {
			return err
		}
		ctrlclient := cluster.GetClient().GetCtrlClient()
		baseline := &v1.Baseline{}
		if err := ctrlclient.Get(ctx, runtimclient.ObjectKey{Name: param.BaselineName}, baseline); err != nil {
			if apierrors.IsNotFound(err) {
				return nil
			}
			return err
		}
		var errs []error
		// delete baseline and checker
		for _, rule := range baseline.Spec.Rules {
			if err := v.checkerAdapter.DeleteChecker(ctx, &models.CheckerParam{ClusterName: param.ClusterName, CheckerName: rule.CheckerName}); err != nil {
				if !apierrors.IsNotFound(err) {
					errs = append(errs, err)
				}
			}
		}
		if len(errs) > 0 {
			return errors.Join(errs...)
		}
		if err := ctrlclient.Delete(ctx, &v1.Baseline{ObjectMeta: metav1.ObjectMeta{Name: param.BaselineName}}); err != nil {
			return err
		}
		return nil
	}

	matchLabels := map[string]string{}
	matchLabels[constants.BaselineStrategyIDLabelKey] = fmt.Sprint(param.StrategyID)
	matchLabels[constants.BaselineClusterNameLabelKey] = param.ClusterName
	matchLabels[constants.BaselineStandardIDLabelKey] = fmt.Sprint(param.StandardID)
	ctrlclient := clientmgr.GetLocalCluster().GetClient().GetCtrlClient()

	if err := ctrlclient.DeleteAllOf(ctx, &v1.Baseline{}, runtimclient.MatchingLabels(matchLabels)); err != nil {
		return err
	}
	return nil
}

// ListBaseline implements BaselineAdapter.
func (v *V1BaselineAdapter) ListBaseline(ctx context.Context, param *baseline.BaselineParam) ([]*baseline.InfraBaselineItem, error) {
	cluster, err := clientmgr.OnlineClusterAssert(clientmgr.GetCluster(param.ClusterName))
	if err != nil {
		return nil, err
	}
	ctrlclient := cluster.GetClient().GetCtrlClient()
	baselineList := &v1.BaselineList{}
	matchLabels := map[string]string{}
	if param.StrategyID > 0 {
		matchLabels[constants.BaselineStrategyIDLabelKey] = fmt.Sprint(param.StrategyID)
	}
	if param.ClusterName != "" {
		matchLabels[constants.BaselineClusterNameLabelKey] = param.ClusterName
	}
	if param.StandardID > 0 {
		matchLabels[constants.BaselineStandardIDLabelKey] = fmt.Sprint(param.StandardID)
	}
	if param.Daemon {
		matchLabels[constants.BaselineStrategyExecutionDaemonLabelKey] = constants.BaselineStrategyExecutionDaemonLabelValueTrue
	}
	if err := ctrlclient.List(ctx, baselineList, runtimclient.MatchingLabels(matchLabels)); err != nil {
		return nil, err
	}

	var items []*baseline.InfraBaselineItem
	for _, item := range baselineList.Items {
		items = append(items, v.ToInfraBaseline(&item))
	}
	return items, nil
}

func (v *V1BaselineAdapter) ToInfraBaseline(baseline *v1.Baseline) *models.InfraBaselineItem {
	stdId, _ := strconv.ParseInt(baseline.Labels[constants.BaselineStandardIDLabelKey], 10, 64)
	strId, _ := strconv.ParseInt(baseline.Labels[constants.BaselineStrategyIDLabelKey], 10, 64)
	b := models.InfraBaselineItem{
		BaselineName: baseline.Name,
		ClusterName:  baseline.Labels[constants.BaselineClusterNameLabelKey],
		StandardID:   stdId,
		StrategyID:   strId,
	}

	for _, rule := range baseline.Spec.Rules {
		b.Checkers = append(b.Checkers, models.InfraBaselineCheckerItem{
			CheckerName: rule.CheckerName,
		})
	}
	return &b
}
