package infra

import (
	"context"
	"errors"
	"fmt"
	"io"
	"reflect"
	"strconv"
	"strings"

	"github.com/minio/minio-go/v7"
	"github.com/rs/xid"
	"go.uber.org/zap"
	v1 "harmonycloud.cn/baseline-checker/api/v1"
	infrachecker "harmonycloud.cn/baseline-checker/pkg/models/checker"
	infraselector "harmonycloud.cn/baseline-checker/pkg/models/selector"
	clientmgr "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	minioutils "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/minio"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/util/retry"
	runtimeclient "sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
)

func NewV1CheckerAdapter() CheckerAdapter {
	return &V1CheckerAdapter{
		log: logger.GetLogger().Named("v1-checker-adapter"),
	}
}

type V1CheckerAdapter struct {
	log *zap.Logger
}

// CreateChecker implements CheckerAdapter.
func (v *V1CheckerAdapter) CreateChecker(ctx context.Context, param *baseline.CheckerParam) (*baseline.InfraCheckerItem, error) {
	cluster, err := clientmgr.OnlineClusterAssert(clientmgr.GetCluster(param.ClusterName))
	if err != nil {
		return nil, err
	}
	ctrlclient := cluster.GetClient().GetCtrlClient()
	checker := v.ToV1Checker(param)
	if err := v.setMonitorSelector(checker, param); err != nil {
		return nil, err
	}
	updateObj := checker.DeepCopy()
	// updateObj.SetResourceVersion("")

	err = retry.RetryOnConflict(retry.DefaultBackoff, func() error {
		_, err := controllerutil.CreateOrPatch(ctx, ctrlclient, updateObj, func() error {
			// 在更新时检查和设置 Labels, Annotations, Spec
			if !reflect.DeepEqual(updateObj.Labels, checker.Labels) {
				updateObj.Labels = utils.MergeMap(checker.Labels, updateObj.Labels)
			}
			if !reflect.DeepEqual(updateObj.Annotations, checker.Annotations) {
				updateObj.Annotations = utils.MergeMap(checker.Annotations, updateObj.Annotations)
			}
			if !reflect.DeepEqual(updateObj.Spec, checker.Spec) {
				updateObj.Spec = checker.Spec
			}
			// updateObj.SetResourceVersion("") // 删除这行
			return nil
		})
		if err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return &baseline.InfraCheckerItem{
		ClusterName:     param.ClusterName,
		StandardID:      param.StandardID,
		StrategyID:      param.StrategyID,
		CheckID:         param.CheckID,
		CheckerName:     updateObj.Name,
		CheckerResource: *updateObj,
	}, nil
}

// DeleteChecker implements CheckerAdapter.
func (v *V1CheckerAdapter) DeleteChecker(ctx context.Context, param *baseline.CheckerParam) error {

	if param.CheckerName == "" || param.ClusterName == "" || param.StandardID == 0 || param.StrategyID == 0 || param.CheckID == 0 {
		return errors.New("invalid param")
	}
	cluster, err := clientmgr.OnlineClusterAssert(clientmgr.GetCluster(param.ClusterName))
	if err != nil {
		return err
	}
	ctrlclient := cluster.GetClient().GetCtrlClient()
	if param.CheckerName != "" && param.ClusterName != "" {

		checker := &v1.Checker{}
		if err := ctrlclient.Get(ctx, runtimeclient.ObjectKey{Name: param.CheckerName}, checker); err != nil {
			if apierrors.IsNotFound(err) {
				return nil
			}
			return err
		}
		if err := ctrlclient.Delete(ctx, checker); err != nil {
			return err
		}
		return nil
	}

	selectorLabels := map[string]string{
		constants.BaselineStandardIDLabelKey:  fmt.Sprintf("%d", param.StandardID),
		constants.BaselineStrategyIDLabelKey:  fmt.Sprintf("%d", param.StrategyID),
		constants.BaselineCheckerIDLabelKey:   fmt.Sprintf("%d", param.CheckID),
		constants.BaselineClusterNameLabelKey: param.ClusterName,
	}
	checker := &v1.Checker{}
	if err := ctrlclient.DeleteAllOf(ctx, checker, runtimeclient.MatchingLabels(selectorLabels)); err != nil {
		return err
	}

	return nil
}

// ListChecker implements CheckerAdapter.
func (v *V1CheckerAdapter) ListChecker(ctx context.Context, param *baseline.CheckerParam) ([]*baseline.InfraCheckerItem, error) {
	cluster, err := clientmgr.OnlineClusterAssert(clientmgr.GetCluster(param.ClusterName))
	if err != nil {
		return nil, err
	}
	ctrlclient := cluster.GetClient().GetCtrlClient()
	checkers := &v1.CheckerList{}
	if err := ctrlclient.List(ctx, checkers); err != nil {
		return nil, err
	}
	var items []*baseline.InfraCheckerItem
	for _, item := range checkers.Items {
		labels := item.Labels
		if labels == nil {
			labels = make(map[string]string)
		}
		if param.ClusterName != "" && param.ClusterName != item.Labels[constants.BaselineClusterNameLabelKey] {
			continue
		}
		if param.StandardID != 0 && fmt.Sprintf("%d", param.StandardID) != labels[constants.BaselineStandardIDLabelKey] {
			continue
		}
		if param.StrategyID != 0 && fmt.Sprintf("%d", param.StrategyID) != labels[constants.BaselineStrategyIDLabelKey] {
			continue
		}
		if param.CheckID != 0 && fmt.Sprintf("%d", param.CheckID) != labels[constants.BaselineCheckerIDLabelKey] {
			continue
		}
		standardId, err := strconv.ParseInt(labels[constants.BaselineStandardIDLabelKey], 10, 64)
		if err != nil {
			v.log.Warn("parse standard id failed", zap.Error(err))
		}
		strategyId, err := strconv.ParseInt(labels[constants.BaselineStrategyIDLabelKey], 10, 64)
		if err != nil {
			v.log.Warn("parse strategy id failed", zap.Error(err))
		}
		checkId, err := strconv.ParseInt(labels[constants.BaselineCheckerIDLabelKey], 10, 64)
		if err != nil {
			v.log.Warn("parse check id failed", zap.Error(err))
		}
		items = append(items, &baseline.InfraCheckerItem{
			ClusterName:     item.Labels[constants.BaselineClusterNameLabelKey],
			StandardID:      standardId,
			StrategyID:      strategyId,
			CheckID:         checkId,
			CheckerName:     item.Name,
			CheckerResource: item,
		})
	}
	return items, nil

}

func (v *V1CheckerAdapter) GetMatchContentFromCheckRawData(checkRawData baseline.CheckRawData) string {
	checkValue, checkerConfig := checkRawData.Value, checkRawData.Config
	matchContent := fmt.Sprintf("%s", checkValue.Value)
	if checkerConfig.Command != nil {
		switch checkerConfig.Command.MatchType {
		case baseline.MatchTypeFuzzy:
			if checkValue.Prefix == baseline.ValuePrefixRegExp {
				matchContent = fmt.Sprintf("%v", checkValue.Value)
			}
		case baseline.MatchTypeExact:
			if checkValue.Prefix == baseline.ValuePrefixInput {
				matchContent = fmt.Sprintf("^%s\\n?$", checkValue.Value)
			}
		}
	}
	if checkerConfig.File != nil {
		if checkValue.Prefix == baseline.ValuePrefixFile {
			if checkValue.File != nil && checkValue.File.UniqueKey != "" {
				c, err := minioutils.GetDefaultMinioClient()
				if err != nil {
					v.log.Error("get minio client failed", zap.Error(err))
					return matchContent
				}
				obj, err := c.GetObject(context.Background(), checkValue.File.Path, checkValue.File.UniqueKey, minio.GetObjectOptions{})
				if err != nil {
					v.log.Error("get minio object failed", zap.Error(err))
					return ""
				}
				objBytes, err := io.ReadAll(obj)
				if err != nil {
					v.log.Error("read minio object failed", zap.Error(err))
					return ""
				}
				matchContent = string(objBytes)
			}
		}

	}

	return matchContent
}

// setMonitorSelector set monitor selector
func (v *V1CheckerAdapter) setMonitorSelector(checker *v1.Checker, param *baseline.CheckerParam) error {
	checker.Spec.MonitorSelector = infraselector.MonitorSelector{
		Name:       param.MonitorName,
		LabelValue: param.ClusterName,
	}
	return nil
}

// ToV1Checker implements CheckerAdapter.
func (v *V1CheckerAdapter) ToV1Checker(param *baseline.CheckerParam) *v1.Checker {
	checkerName := fmt.Sprintf("%s%s", "c", xid.New().String())
	checkerObject := &v1.Checker{
		ObjectMeta: metav1.ObjectMeta{
			//GenerateName: fmt.Sprintf("%s-%d-%d-", constants.BaselineCheckerPrefixName, param.StandardID, param.CheckID),
			Name: checkerName,
			Labels: map[string]string{
				constants.BaselineCheckerIDLabelKey:       fmt.Sprintf("%d", param.CheckID),
				constants.BaselineClusterNameLabelKey:     param.ClusterName,
				constants.BaselineStandardCheckIDLabelKey: fmt.Sprintf("%d", param.StandardCheckID),
				constants.BaselineCheckJobIDLabelKey:      fmt.Sprintf("%d", param.CheckJobID),
				constants.BaselineStandardIDLabelKey:      fmt.Sprintf("%d", param.StandardID),
				constants.BaselineStandardJobIDLabelKey:   fmt.Sprintf("%d", param.StandardJobID),
				constants.BaselineStrategyIDLabelKey:      fmt.Sprintf("%d", param.StrategyID),
			},
			Annotations: map[string]string{},
		},
		Spec: infrachecker.Checker{},
	}
	checkConfig := param.CheckRawData.Config
	matchContent := v.GetMatchContentFromCheckRawData(param.CheckRawData)
	switch checkConfig.CheckMode {
	case baseline.CheckerModeCommand:
		checkerObject.Spec.Type = infrachecker.CheckerCommonFile
		checkerObject.Spec.CommonFile = &infrachecker.CommonFile{
			Regex: matchContent,
		}
	case baseline.CheckerModeFile:
		if checkConfig.File != nil {
			switch checkConfig.File.FileLocation.Mode {
			case baseline.FileLocationModeHostPath:
				switch checkConfig.File.FileType {
				case baseline.FileTypeJSON, baseline.FileTypeYAML:
					checkerObject.Spec.Type = infrachecker.CheckerParseFile
					if checkerObject.Spec.ParseFile == nil {
						checkerObject.Spec.ParseFile = &infrachecker.ParseFile{}
					}
					checkerObject.Spec.ParseFile.Type = strings.ToLower(string(checkConfig.File.FileType))
					checkerObject.Spec.ParseFile.MFileContent = matchContent
				case baseline.FileTypeTEXT:
					fallthrough
				default:
					if checkerObject.Spec.CommonFile == nil {
						checkerObject.Spec.CommonFile = &infrachecker.CommonFile{}
					}
					checkerObject.Spec.Type = infrachecker.CheckerCommonFile
					checkerObject.Spec.CommonFile.Regex = matchContent
				}
			case baseline.FileLocationModeK8sResource:
				checkerObject.Spec.Type = infrachecker.CheckerParseFile
				if checkerObject.Spec.ParseFile == nil {
					checkerObject.Spec.ParseFile = &infrachecker.ParseFile{}
				}
				checkerObject.Spec.ParseFile.Type = strings.ToLower(string(checkConfig.File.FileType))
				checkerObject.Spec.ParseFile.MFileContent = matchContent
			default:
				if checkerObject.Spec.CommonFile == nil {
					checkerObject.Spec.CommonFile = &infrachecker.CommonFile{}
				}
				checkerObject.Spec.Type = infrachecker.CheckerCommonFile
				checkerObject.Spec.CommonFile.Regex = matchContent
			}
		}
	}
	return checkerObject
}
