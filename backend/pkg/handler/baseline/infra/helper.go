package infra

import (
	"fmt"
	"os"
	"strings"

	"github.com/samber/lo"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/util/sets"
	ctrlclient "sigs.k8s.io/controller-runtime/pkg/client"
)

// GetGroupNodesByRole ...
func GetGroupNodesByRole(nodes []corev1.Node) map[models.CheckerNodeRole][]corev1.Node {
	nodeRoleGroup := lo.GroupBy(nodes, func(item corev1.Node) models.CheckerNodeRole {
		if item.Labels == nil {
			return models.CheckerNodeRoleWorker
		}
		if _, ok := item.Labels[constants.NodeMasterRoleLabelKey]; ok {
			return models.CheckerNodeRoleMaster
		}
		if _, ok := item.Labels[constants.NodeControlPlaneRoleLabelKey]; ok {
			return models.CheckerNodeRoleMaster
		}
		if _, ok := item.Labels[constants.NodeSystemRoleLabelKey]; ok {
			return models.CheckerNodeRoleSystem
		}
		return models.CheckerNodeRoleWorker
	})
	return nodeRoleGroup
}

// GetHostPathPrefix ...
func GetHostPathPrefix() string {
	getenv := os.Getenv(constants.BaselineMonitorHostPathPrefixEnv)
	if getenv == "" {
		return constants.BaselineMonitorHostPathPrefix
	}
	return getenv
}

// FilterNodesByRoles 过滤节点
func FilterNodesByRoles(nodes []corev1.Node, roles []models.CheckerNodeRole) []corev1.Node {
	filteredNodes := make([]corev1.Node, 0)
	for _, node := range nodes {
		for _, role := range roles {
			switch role {
			case models.CheckerNodeRoleMaster:
				if _, ok := node.Labels[constants.NodeMasterRoleLabelKey]; ok {
					filteredNodes = append(filteredNodes, node)
					continue
				}
				if _, ok := node.Labels[constants.NodeControlPlaneRoleLabelKey]; ok {
					filteredNodes = append(filteredNodes, node)
					continue
				}
			case models.CheckerNodeRoleSystem:
				if _, ok := node.Labels[constants.NodeSystemRoleLabelKey]; ok {
					filteredNodes = append(filteredNodes, node)
					continue
				}
			case models.CheckerNodeRoleWorker:
				_, masterRoleExist := node.Labels[constants.NodeMasterRoleLabelKey]
				_, controlPlaneRoleExist := node.Labels[constants.NodeControlPlaneRoleLabelKey]
				_, systemRoleExist := node.Labels[constants.NodeSystemRoleLabelKey]
				if !masterRoleExist && !controlPlaneRoleExist && !systemRoleExist {
					filteredNodes = append(filteredNodes, node)
				}
			}
		}
	}
	return lo.UniqBy(filteredNodes, func(item corev1.Node) string {
		return item.Name
	})
}

func AnnotationGetStrategyIDs(object ctrlclient.Object) sets.Set[string] {
	if object.GetAnnotations() == nil {
		object.SetAnnotations(make(map[string]string))
	}
	StrategyIds := sets.New(lo.Filter(strings.Split(object.GetAnnotations()[constants.BaselineStrategyIDsAnnotationKey], ","),
		func(item string, index int) bool {
			return item != ""
		})...)
	return StrategyIds
}

// AnnotationAppendStrategyID add ids
func AnnotationAppendStrategyID(object ctrlclient.Object, strategyID int64) {
	if object.GetAnnotations() == nil {
		object.SetAnnotations(make(map[string]string))
	}
	StrategyIds := sets.New(lo.Filter(strings.Split(object.GetAnnotations()[constants.BaselineStrategyIDsAnnotationKey], ","),
		func(item string, index int) bool {
			return item != ""
		})...)
	StrategyIds.Insert(fmt.Sprintf("%d", strategyID))
	object.GetAnnotations()[constants.BaselineStrategyIDsAnnotationKey] = strings.Join(sets.List(StrategyIds), ",")
}

// AnnotationRemoveStrategyID from ids
func AnnotationRemoveStrategyID(object ctrlclient.Object, strategyID int64) {
	if object.GetAnnotations() == nil {
		object.SetAnnotations(make(map[string]string))
	}
	StrategyIds := sets.New(lo.Filter(strings.Split(object.GetAnnotations()[constants.BaselineStrategyIDsAnnotationKey], ","),
		func(item string, index int) bool {
			return item != ""
		})...)
	StrategyIds.Delete(fmt.Sprintf("%d", strategyID))
	object.GetAnnotations()[constants.BaselineStrategyIDsAnnotationKey] = strings.Join(sets.List(StrategyIds), ",")
}
