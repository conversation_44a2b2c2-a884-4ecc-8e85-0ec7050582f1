package infra

import (
	"sort"
	"testing"

	"github.com/stretchr/testify/assert"
	infraselector "harmonycloud.cn/baseline-checker/pkg/models/selector"
)

func TestNodeSelectorConversion(t *testing.T) {
	tests := []struct {
		name     string
		term     infraselector.NodeSelectorTerm
		selector string
		wantErr  bool
	}{
		{
			name: "simple In/NotIn",
			term: infraselector.NodeSelectorTerm{
				MatchExpressions: []infraselector.MatchExpression{
					{Key: "disk", Operator: infraselector.NodeSelectorOpIn, Values: []string{"ssd", "nvme"}},
					{Key: "gpu", Operator: infraselector.NodeSelectorOpNotIn, Values: []string{"amd"}},
				},
			},
			selector: "disk in (ssd,nvme),gpu notin (amd)",
		},
		{
			name: "with Exists/DoesNotExist",
			term: infraselector.NodeSelectorTerm{
				MatchExpressions: []infraselector.MatchExpression{
					{Key: "topology", Operator: infraselector.NodeSelectorOpExists},
					{Key: "legacy", Operator: infraselector.NodeSelectorOpDoesNotExist},
				},
			},
			selector: "topology,!legacy",
		},
		{
			name: "with Gt/Lt",
			term: infraselector.NodeSelectorTerm{
				MatchExpressions: []infraselector.MatchExpression{
					{Key: "memory", Operator: infraselector.NodeSelectorOpGt, Values: []string{"8"}},
					{Key: "cpu", Operator: infraselector.NodeSelectorOpLt, Values: []string{"4"}},
				},
			},
			selector: "memory > 8,cpu < 4",
		},
		{
			name: "invalid Gt value",
			term: infraselector.NodeSelectorTerm{
				MatchExpressions: []infraselector.MatchExpression{
					{Key: "memory", Operator: infraselector.NodeSelectorOpGt, Values: []string{"abc"}},
				},
			},
			wantErr: true,
		},
	}

	sortMatchExpressions := func(exprs []infraselector.MatchExpression) {
		sort.Slice(exprs, func(i, j int) bool {
			if exprs[i].Key != exprs[j].Key {
				return exprs[i].Key < exprs[j].Key
			}
			return exprs[i].Operator < exprs[j].Operator
		})
		for i := range exprs {
			sort.Strings(exprs[i].Values)
		}
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test NodeSelectorTerm -> String
			gotStr, err := NodeSelectorTermToString(tt.term)
			if tt.wantErr {
				assert.Error(t, err)
				return
			}
			assert.NoError(t, err)
			assert.Equal(t, tt.selector, gotStr)

			// Test String -> NodeSelectorTerm (round-trip)
			gotTerm, err := StringToNodeSelectorTerm(tt.selector)
			assert.NoError(t, err)
			sortMatchExpressions(tt.term.MatchExpressions)
			sortMatchExpressions(gotTerm.MatchExpressions)
			assert.Equal(t, tt.term.MatchExpressions, gotTerm.MatchExpressions)
		})
	}
}
