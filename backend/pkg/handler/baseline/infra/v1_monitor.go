package infra

import (
	"context"
	"fmt"
	"reflect"
	"sort"
	"strconv"
	"strings"

	"github.com/rs/xid"
	"github.com/samber/lo"
	"go.uber.org/zap"
	checkerv1 "harmonycloud.cn/baseline-checker/api/v1"
	inframonitor "harmonycloud.cn/baseline-checker/pkg/models/monitor"
	infraselector "harmonycloud.cn/baseline-checker/pkg/models/selector"
	clientmgr "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/sets"
	"k8s.io/client-go/util/retry"
	ctrlclient "sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
)

const (
	DefaultPeriodSeconds = 60

	// monitorStatusGetterHasDomainKeyword 是否有新的数据
	monitorStatusGetterHasDomainKeyword = "get new domainInfo"
)

func NewV1MonitorAdapter() MonitorAdapter {
	return &V1MonitorAdapter{}
}

type V1MonitorAdapter struct {
}

// ToV1Monitor implements MonitorAdapter.
func (v *V1MonitorAdapter) ToV1Monitor(param *baseline.MonitorParam) *checkerv1.Monitor {
	checkerConfig := param.CheckerConfig
	var monitorSpec inframonitor.Monitor
	switch checkerConfig.CheckMode {
	case baseline.CheckerModeCommand:
		monitorSpec = inframonitor.Monitor{
			Type: inframonitor.MonitorTypeCommand,
			Command: &inframonitor.Command{
				EnterHost: true,
				Exec: []string{
					"/bin/sh", "-c", checkerConfig.Command.Command,
				},
				PeriodSeconds: DefaultPeriodSeconds,
			},
		}
	case baseline.CheckerModeFile:
		switch checkerConfig.File.FileLocation.Mode {
		case baseline.FileLocationModeHostPath:
			monitorSpec = inframonitor.Monitor{
				Type: inframonitor.MonitorTypeFile,
				File: &inframonitor.File{
					Path: strings.Join([]string{GetHostPathPrefix(), checkerConfig.File.FileLocation.Path}, ""),
				},
			}
		case baseline.FileLocationModeK8sResource:
			k8sInstance := &inframonitor.K8sInstance{
				ApiVersion: checkerConfig.File.FileLocation.K8sResourceRef.APIVersion,
				Kind:       checkerConfig.File.FileLocation.K8sResourceRef.Kind,
				Namespace:  checkerConfig.File.FileLocation.K8sResourceRef.Namespace,
			}
			if checkerConfig.File.FileLocation.K8sResourceRef.Name != "" {
				k8sInstance.Name = checkerConfig.File.FileLocation.K8sResourceRef.Name
			} else if checkerConfig.File.FileLocation.K8sResourceRef.Labels != nil {
				k8sInstance.Labels = checkerConfig.File.FileLocation.K8sResourceRef.Labels
				delete(k8sInstance.Labels, "")
			}

			monitorSpec = inframonitor.Monitor{
				Type:        inframonitor.MonitorTypeK8sInstance,
				K8sInstance: k8sInstance,
			}
		}
	}
	return &checkerv1.Monitor{
		TypeMeta: metav1.TypeMeta{},
		ObjectMeta: metav1.ObjectMeta{
			Name: fmt.Sprintf("m%s", xid.New().String()),
			Annotations: map[string]string{
				constants.BaselineStrategyIDsAnnotationKey: fmt.Sprintf("%d", param.StrategyID),
				//constants.BaselineStandardIDsAnnotationKey: fmt.Sprintf("%d", param.StandardID),
			},
			Labels: map[string]string{
				constants.BaselineCheckerIDLabelKey:      fmt.Sprintf("%d", param.CheckID),
				constants.BaselineClusterNameLabelKey:    param.ClusterName,
				constants.BaselineMonitorClusterLabelKey: param.ClusterName,
				constants.BaselineStrategyIDLabelKey:     "",
				//constants.BaselineStandardIDLabelKey:     "",
			},
		},
		Spec: monitorSpec,
	}
}

// LabelAddClusterName ...
func (v *V1MonitorAdapter) LabelAddClusterName(object ctrlclient.Object, clusterName string) {
	if object.GetLabels() == nil {
		object.SetLabels(make(map[string]string))
	}
	object.GetLabels()[constants.BaselineMonitorClusterLabelKey] = clusterName
}

func (v *V1MonitorAdapter) setMonitorNodeSelector(ctx context.Context, clusterName string, param *baseline.MonitorParam, monitor *checkerv1.Monitor) error {
	cluster, err := clientmgr.GetCluster(clusterName)
	if err != nil {
		return nil
	}
	// list nodes
	nodeList := &corev1.NodeList{}
	if err := cluster.GetClient().GetCtrlClient().List(ctx, nodeList); err != nil {
		return err
	}
	var filteredNodes []corev1.Node
	nodeSelector := infraselector.NodeSelector{
		NodeSelectorTerms: make([]infraselector.NodeSelectorTerm, 0),
	}
	if param.CheckerConfig.NodeSelector != nil && len(param.CheckerConfig.NodeSelector.LabelSelectors) > 0 {
		for _, expr := range param.CheckerConfig.NodeSelector.LabelSelectors {
			selectorTerm, err := StringToNodeSelectorTerm(expr)
			if err != nil {
				logger.GetLogger().Warn("failed convert expr to node selector term ", zap.Any("expr", expr))
				continue
			}
			nodeSelector.NodeSelectorTerms = append(nodeSelector.NodeSelectorTerms, *selectorTerm)
		}
	} else {
		filteredNodes = FilterNodesByRoles(nodeList.Items, param.CheckerConfig.NodeRoles)
		nodeNames := lo.Map(filteredNodes, func(item corev1.Node, _ int) string {
			return item.Name
		})
		nodeSelector.NodeSelectorTerms = append(nodeSelector.NodeSelectorTerms, infraselector.NodeSelectorTerm{
			MatchExpressions: []infraselector.MatchExpression{
				{
					Key:      constants.KubernetesHostNameLabelKey,
					Operator: infraselector.NodeSelectorOpIn,
					Values:   nodeNames,
				},
			},
		})
	}
	switch {
	case monitor.Spec.File != nil:
		monitor.Spec.File.NodeSelector = nodeSelector
	case monitor.Spec.Command != nil:
		if (param.CheckerConfig.NodeRoles == nil || len(param.CheckerConfig.NodeRoles) == 0) &&
			(param.CheckerConfig.NodeSelector == nil || len(param.CheckerConfig.NodeSelector.LabelSelectors) == 0) {
			masterNodes := FilterNodesByRoles(nodeList.Items, []models.CheckerNodeRole{models.CheckerNodeRoleMaster})
			// sort masterNodes by creation timestamp
			sort.Slice(masterNodes, func(i, j int) bool {
				return masterNodes[i].CreationTimestamp.Before(&masterNodes[j].CreationTimestamp)
			})
			if len(masterNodes) > 0 {
				masterNodeNames := lo.Map(masterNodes, func(item corev1.Node, _ int) string {
					return item.Name
				})
				monitor.Spec.Command.NodeSelector = infraselector.NodeSelector{
					NodeSelectorTerms: []infraselector.NodeSelectorTerm{
						{
							MatchExpressions: []infraselector.MatchExpression{
								{
									Key:      constants.KubernetesHostNameLabelKey,
									Operator: infraselector.NodeSelectorOpIn,
									Values:   masterNodeNames[:1],
								},
							},
						},
					},
				}
			}
		} else {
			monitor.Spec.Command.NodeSelector = nodeSelector
		}

	}
	return nil
}

// CreateMonitor implements MonitorAdapter.
func (v *V1MonitorAdapter) CreateMonitor(ctx context.Context, param *baseline.MonitorParam) (*baseline.InfraMonitorItem, error) {
	clusterName := param.ClusterName
	monitor := v.ToV1Monitor(param)
	cluster, err := clientmgr.GetCluster(clusterName)
	if err != nil {
		return nil, err
	}
	if param.MonitorName != "" {
		monitor.Name = param.MonitorName
	}
	// set checker node monitor
	if err := v.setMonitorNodeSelector(ctx, clusterName, param, monitor); err != nil {
		return nil, err
	}
	updateMonitor := monitor.DeepCopy()
	if err := retry.RetryOnConflict(retry.DefaultBackoff, func() error {
		if _, err := controllerutil.CreateOrPatch(ctx, cluster.GetClient().GetCtrlClient(), updateMonitor, func() error {
			if !reflect.DeepEqual(updateMonitor.Labels, monitor.Labels) {
				updateMonitor.Labels = utils.MergeMap(updateMonitor.Labels, monitor.Labels)
			}
			if !reflect.DeepEqual(updateMonitor.Annotations, monitor.Annotations) {
				AnnotationAppendStrategyID(updateMonitor, param.StrategyID)
				delete(monitor.Annotations, constants.BaselineStrategyIDsAnnotationKey)
				updateMonitor.Annotations = utils.MergeMap(updateMonitor.Annotations, monitor.Annotations)
			}
			if !reflect.DeepEqual(updateMonitor.Spec, monitor.Spec) {
				updateMonitor.Spec = monitor.Spec
			}
			return nil
		}); err != nil {
			return err
		}
		return nil
	}); err != nil {
		return nil, err
	}
	return &baseline.InfraMonitorItem{
		ClusterName:     clusterName,
		MonitorName:     updateMonitor.Name,
		CheckID:         param.CheckID,
		StandardID:      param.StandardID,
		StrategyID:      param.StrategyID,
		StandardCheckID: param.StandardCheckID,
	}, nil
}

// DeleteMonitor implements MonitorAdapter.
func (v *V1MonitorAdapter) DeleteMonitor(ctx context.Context, param *baseline.MonitorParam) error {
	cluster, err := clientmgr.GetCluster(param.ClusterName)
	if err != nil {
		return err
	}
	// first get monitor
	monitorName := param.MonitorName

	// delete monitor
	if err := cluster.GetClient().GetCtrlClient().Delete(ctx, &checkerv1.Monitor{ObjectMeta: metav1.ObjectMeta{Name: monitorName}}); err != nil {
		return err
	}

	return nil
}

// ListMonitor implements MonitorAdapter.
func (v *V1MonitorAdapter) ListMonitor(ctx context.Context, param *baseline.MonitorParam) ([]*baseline.InfraMonitorItem, error) {
	var monitorItems []*baseline.InfraMonitorItem

	cluster, err := clientmgr.GetCluster(param.ClusterName)
	if err != nil {
		return nil, err
	}
	matchingLabels := map[string]string{}
	if param.CheckID != 0 {
		matchingLabels[constants.BaselineCheckerIDLabelKey] = fmt.Sprintf("%d", param.CheckID)
	}
	monitors := &checkerv1.MonitorList{}
	if err := cluster.GetClient().GetCtrlClient().List(ctx, monitors, ctrlclient.MatchingLabels(matchingLabels)); err != nil {
		return nil, err
	}
	for _, monitor := range monitors.Items {
		checkID, err := strconv.ParseInt(monitor.Labels[constants.BaselineCheckerIDLabelKey], 10, 64)
		if err != nil {
			continue
		}
		// skip is deleted
		if !monitor.DeletionTimestamp.IsZero() {
			continue
		}
		monitorItems = append(monitorItems, &baseline.InfraMonitorItem{
			ClusterName:     param.ClusterName,
			CheckID:         checkID,
			MonitorName:     monitor.Name,
			StandardID:      param.StandardID,
			StrategyID:      param.StrategyID,
			StandardCheckID: param.StandardCheckID,
		})
	}
	return monitorItems, nil
}

// EnsureMonitorsRunning 确保 monitor 正在运行
func (v *V1MonitorAdapter) EnsureMonitorsRunning(ctx context.Context, param *baseline.MonitorParam) (ready bool,
	runningMonitors, abnormalMonitors []baseline.MonitorStatusResult, err error) {
	matchLabels := map[string]string{}
	if param.CheckID != 0 {
		matchLabels[constants.BaselineCheckerIDLabelKey] = fmt.Sprintf("%d", param.CheckID)
	}
	cluster, err := clientmgr.GetCluster(param.ClusterName)
	if err != nil {
		return false, runningMonitors, abnormalMonitors, err
	}
	ensureMonitorNames := sets.NewString()
	if len(param.EnsureMonitorNames) > 0 {
		ensureMonitorNames.Insert(param.EnsureMonitorNames...)
	}
	monitors := &checkerv1.MonitorList{}
	if err := cluster.GetClient().GetCtrlClient().List(ctx, monitors, ctrlclient.MatchingLabels(matchLabels)); err != nil {
		abnormalMonitors = lo.Map(param.EnsureMonitorNames, func(monitorName string, _ int) baseline.MonitorStatusResult {
			return baseline.MonitorStatusResult{
				Name: monitorName,
				Getter: baseline.MonitorGetterStatus{
					Reason:  "GetMonitorError",
					Message: err.Error(),
				},
			}
		})
		return false, runningMonitors, abnormalMonitors, err
	}
	filteredMonitors := lo.Filter(monitors.Items, func(monitor checkerv1.Monitor, _ int) bool {
		if len(ensureMonitorNames) > 0 && !ensureMonitorNames.Has(monitor.Name) {
			return false
		}
		return true
	})
	if len(filteredMonitors) == 0 && len(ensureMonitorNames) > 0 {
		for _, monitorName := range ensureMonitorNames.List() {
			abnormalMonitors = append(abnormalMonitors, baseline.MonitorStatusResult{
				Name: monitorName,
				Getter: baseline.MonitorGetterStatus{
					Reason:  "monitor not found",
					Message: fmt.Sprintf("monitor %s not found", monitorName),
				},
			})
		}
		return false, runningMonitors, abnormalMonitors, nil
	}

	// all ready return true
	ready = true
	for _, monitor := range filteredMonitors {
		monitor := monitor
		if reflect.DeepEqual(monitor.Status, checkerv1.MonitorStatus{}) || monitor.Status.Getters == nil || len(monitor.Status.Getters) == 0 {
			abnormalMonitors = append(abnormalMonitors, baseline.MonitorStatusResult{
				Name: monitor.Name,
				Getter: baseline.MonitorGetterStatus{
					Reason:  "GettersEmpty",
					Message: fmt.Sprintf("monitor %s getters not ready, getters is empty", monitor.Name),
				},
			})
			ready = false
			continue
		}
		domainLen := len(monitor.Status.Getters)
		runningGettersCnt := 0
		getterMessages := make([]string, 0)
		for _, getterStatus := range monitor.Status.Getters {
			if getterStatus.Status == inframonitor.GetterStatusRunning &&
				strings.Contains(getterStatus.Reason, monitorStatusGetterHasDomainKeyword) {
				runningGettersCnt++
			} else if getterStatus.Status == inframonitor.GetterStatusRunning && getterStatus.Reason == "" {
				getterMessages = append(getterMessages, fmt.Sprintf("domain: %s, status: %s, reason: %s",
					getterStatus.Domain, getterStatus.Status, "no domainInfo data received, please check monitor object status or wait some time recheck."))
				ready = false
			} else {
				getterMessages = append(getterMessages, fmt.Sprintf("domain: %s, status: %s, reason: %s",
					getterStatus.Domain, getterStatus.Status, getterStatus.Reason))
				ready = false
			}
		}
		if domainLen != runningGettersCnt {
			abnormalMonitors = append(abnormalMonitors, baseline.MonitorStatusResult{
				Name: monitor.Name,
				Getter: baseline.MonitorGetterStatus{
					Reason:  monitor.Status.GettersGenError,
					Message: strings.Join(getterMessages, ";"),
				},
			})
			ready = false
		} else {
			runningMonitors = append(runningMonitors, baseline.MonitorStatusResult{
				Name: monitor.Name,
			})
		}
	}
	if !ready {
		logger.GetSugared().Infow("monitors not ready",
			"cluster", param.ClusterName,
			"strategyID", param.StrategyID,
			"standardID", param.StandardID,
			"abnormalMonitors", abnormalMonitors)
	}
	return ready, runningMonitors, abnormalMonitors, nil
}
