package infra

import (
	"fmt"
	"strconv"
	"strings"

	infraselector "harmonycloud.cn/baseline-checker/pkg/models/selector"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/selection"
)

// NodeSelectorTermToString 将 NodeSelectorTerm 转换为 labelSelector 风格字符串
// 支持所有 Operator (In/NotIn/Exists/DoesNotExist/Gt/Lt)
func NodeSelectorTermToString(term infraselector.NodeSelectorTerm) (string, error) {
	var parts []string
	for _, expr := range term.MatchExpressions {
		switch expr.Operator {
		case infraselector.NodeSelectorOpIn:
			if len(expr.Values) == 0 {
				return "", fmt.Errorf("empty values for In operator")
			}
			parts = append(parts, fmt.Sprintf("%s in (%s)", expr.Key, strings.Join(expr.Values, ",")))
		case infraselector.NodeSelectorOpNotIn:
			if len(expr.Values) == 0 {
				return "", fmt.Errorf("empty values for NotIn operator")
			}
			parts = append(parts, fmt.Sprintf("%s notin (%s)", expr.Key, strings.Join(expr.Values, ",")))
		case infraselector.NodeSelectorOpExists:
			parts = append(parts, expr.Key)
		case infraselector.NodeSelectorOpDoesNotExist:
			parts = append(parts, fmt.Sprintf("!%s", expr.Key))
		case infraselector.NodeSelectorOpGt:
			if len(expr.Values) != 1 {
				return "", fmt.Errorf("%s operator requires exactly one value", expr.Operator)
			}
			if _, err := strconv.Atoi(expr.Values[0]); err != nil {
				return "", fmt.Errorf("%s value must be a number: %v", expr.Operator, err)
			}
			parts = append(parts, fmt.Sprintf("%s > %s", expr.Key, expr.Values[0]))
		case infraselector.NodeSelectorOpLt:
			if len(expr.Values) != 1 {
				return "", fmt.Errorf("%s operator requires exactly one value", expr.Operator)
			}
			if _, err := strconv.Atoi(expr.Values[0]); err != nil {
				return "", fmt.Errorf("%s value must be a number: %v", err, expr.Operator)
			}
			parts = append(parts, fmt.Sprintf("%s < %s", expr.Key, expr.Values[0]))
		default:
			return "", fmt.Errorf("unsupported operator: %s", expr.Operator)
		}
	}
	return strings.Join(parts, ","), nil
}

// StringToNodeSelectorTerm 将字符串解析为 NodeSelectorTerm
func StringToNodeSelectorTerm(selector string) (*infraselector.NodeSelectorTerm, error) {
	term := &infraselector.NodeSelectorTerm{}
	requirements, err := labels.ParseToRequirements(selector)
	if err != nil {
		return nil, fmt.Errorf("failed to parse selector: %v", err)
	}

	for _, req := range requirements {
		expr := infraselector.MatchExpression{
			Key: req.Key(),
		}
		switch req.Operator() {
		case selection.In:
			expr.Operator = infraselector.NodeSelectorOpIn
			expr.Values = req.Values().List()
		case selection.NotIn:
			expr.Operator = infraselector.NodeSelectorOpNotIn
			expr.Values = req.Values().List()
		case selection.Exists:
			expr.Operator = infraselector.NodeSelectorOpExists
		case selection.DoesNotExist:
			expr.Operator = infraselector.NodeSelectorOpDoesNotExist
		case selection.GreaterThan:
			expr.Operator = infraselector.NodeSelectorOpGt
			expr.Values = []string{req.Values().List()[0]}
		case selection.LessThan:
			expr.Operator = infraselector.NodeSelectorOpLt
			expr.Values = []string{req.Values().List()[0]}
		default:
			return nil, fmt.Errorf("unsupported operator: %s", req.Operator())
		}

		term.MatchExpressions = append(term.MatchExpressions, expr)
	}
	return term, nil
}

func ConvertInfraNodeSelectorToNodeSelector(infraSelector *infraselector.NodeSelector) *models.NodeSelector {
	if infraSelector == nil {
		return nil
	}
	selectors := &models.NodeSelector{
		LabelSelectors: []string{},
	}
	for _, term := range infraSelector.NodeSelectorTerms {
		toString, err := NodeSelectorTermToString(term)
		if err != nil {
			logger.GetLogger().Warn("failed convert term to label string")
			continue
		}
		selectors.LabelSelectors = append(selectors.LabelSelectors, toString)
	}
	return selectors
}
