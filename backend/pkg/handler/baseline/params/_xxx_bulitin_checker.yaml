apiVersion: harmonycloud.cn/v1
kind: Monitor
metadata:
  labels:
    app: olympus-core
    baseline.harmonycloud.cn/monitor-cluster: "cluster-57"
    baseline.harmonycloud.cn/builtin: "true"
  name: olympus-core-pod
spec:
  k8sInstance:
    apiVersion: v1
    kind: Pod
    label:
      app: olympus-core
    namespace: caas-system
  type: k8sInstance
status: {}
---
apiVersion: harmonycloud.cn/v1
kind: Checker
metadata:
  name: olympus-core-pod-checker
  labels:
    baseline.harmonycloud.cn/builtin: "true"
spec:
  monitorSelector:
    labelValue: cluster-57
    name: olympus-core-pod
  type: parseFile
  parseFile:
    type: yaml
    mFileContent: |-
      containers:
        - name: olympus-core
          image: 10.10.103.155/k8s-deploy/olympus-core:v3.6.0-513019fd1
          imagePullPolicy: Always