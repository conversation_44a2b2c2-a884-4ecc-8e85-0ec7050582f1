package baseline

import (
	"context"
	"fmt"
	"log"
	"testing"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
)

func Test_remove_minio_obj(t *testing.T) {
	duration, _ := time.ParseDuration("144h")
	expirationTime := time.Now().Add(-duration)
	var (
		minioEndpoint  = "10.10.103.188:29050"
		minioAccessKey = "minio"
		minioSecretKey = "Hc@Cloud01"
	)
	client, err := minio.New(minioEndpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(minioAccessKey, minioSecretKey, ""),
		Secure: false,
	})
	if err != nil {
		log.Fatal(err)
	}
	ctx := context.Background()
	bucketName := constants.MinioBaselineReportBucketName
	objects := client.ListObjects(ctx, bucketName, minio.ListObjectsOptions{
		Recursive: true,
	})
	var errs []error
	for object := range objects {
		if object.Err != nil {
			errs = append(errs, fmt.Errorf("error listing object: %w", object.Err))
			continue
		}
		if object.LastModified.Before(expirationTime) {
			log.Printf("strat check object %s last modified time %s", object.Key, object.LastModified.Format(time.DateTime))
			if err := client.RemoveObject(ctx, bucketName, object.Key, minio.RemoveObjectOptions{}); err != nil {
				errs = append(errs, fmt.Errorf("failed to remove object %s: %w", object.Key, err))
				continue
			}
			log.Print(fmt.Sprintf("Deleted expired object: %s (last modified: %v)", object.Key, object.LastModified))
		}
	}
}
