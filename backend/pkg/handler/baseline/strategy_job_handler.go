package baseline

import (
	"context"
	goerrors "errors"
	"fmt"
	"os"
	"os/signal"
	"strings"
	"sync"
	"syscall"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/redis/go-redis/v9"
	rds "github.com/redis/go-redis/v9"
	"github.com/robfig/cron/v3"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"gorm.io/gorm"
	checkerv1 "harmonycloud.cn/baseline-checker/api/v1"
	infrachecker "harmonycloud.cn/baseline-checker/pkg/models/checker"
	clientmgr "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/database"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/feign/baseline_master"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/addon"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline/helper"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline/infra"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
	minioutils "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/minio"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/redislock"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/meta"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/selection"
	"k8s.io/apimachinery/pkg/util/sets"
	"k8s.io/apimachinery/pkg/util/wait"
	ctrlclient "sigs.k8s.io/controller-runtime/pkg/client"
)

const (
	baselineExecuteStrategyTaskJobLock       = "baseline-execute-strategy-task-job-lock"
	baselineScanTimeoutJobLock               = "baseline-scan-timeout-job-lock"
	baselineScanResourceRemainTimeoutJobLock = "baseline-scan-resource-remain-timeout-job-lock"
	baselineScanJobRecordLock                = "baseline-scan-job-record-lock"
)

var (
	strategyJobHandlerInstance *strategyJobHandler
	strategyJobHandlerOnce     sync.Once
	singletonCron              *cron.Cron
)

func NewStrategyJobHandler(cron *cron.Cron,
	db *gorm.DB,
	rds *redis.Client,
	baselineAdapter infra.BaselineAdapter,
	checkerAdapter infra.CheckerAdapter,
	monitorAdapter infra.MonitorAdapter,
	addonHandler addon.Handler) *strategyJobHandler {
	//remainTime := helper.GetBaselineResourceRemainTimeConfigValue(context.Background(), db)
	standardTimeout := helper.GetBaselineStandardJobTimeoutConfigValue()
	standardPoolSize := helper.GetBaselineStandardJobMaxConcurrencyConfigValue()
	strategyTimeout := helper.GetBaselineStrategyJobTimeoutConfigValue()
	strategyPoolSize := helper.GetBaselineStrategyJobMaxConcurrencyConfigValue()
	quitSigs := make(chan os.Signal, 1)
	signal.Notify(quitSigs, syscall.SIGINT, syscall.SIGTERM)

	minioClient, err := minioutils.GetDefaultMinioClient()
	if err != nil {
		logger.GetLogger().Fatal("init minio client error", zap.Error(err))
	}

	handler := &strategyJobHandler{
		db:                  db,
		log:                 logger.GetLogger().Named("strategy-job-handler"),
		rds:                 rds,
		standardJobTimeout:  standardTimeout,
		standardJobTaskPool: NewJobTaskPool(standardPoolSize, standardTimeout),
		strategyJobTimeout:  strategyTimeout,
		strategyJobTaskPool: NewJobTaskPool(strategyPoolSize, strategyTimeout),
		baselineAdapter:     baselineAdapter,
		monitorAdapter:      monitorAdapter,
		checkerAdapter:      checkerAdapter,
		reportService:       baseline_master.NewService(),
		addonHandler:        addonHandler,
		minioClient:         minioClient,
		cron:                cron,
		quitSigs:            quitSigs,
	}

	if err := handler.registerCronTask(); err != nil {
		logger.GetLogger().Fatal("register cron task error", zap.Error(err))
	}

	// 启动扫描任务
	return handler
}

// newStrategyJobHandler 私有构造函数
func newStrategyJobHandler(jobCron *cron.Cron) *strategyJobHandler {
	standardTimeout := helper.GetBaselineStandardJobTimeoutConfigValue()
	standardPoolSize := helper.GetBaselineStandardJobMaxConcurrencyConfigValue()
	strategyTimeout := helper.GetBaselineStrategyJobTimeoutConfigValue()
	strategyPoolSize := helper.GetBaselineStrategyJobMaxConcurrencyConfigValue()
	quitSigs := make(chan os.Signal, 1)
	signal.Notify(quitSigs, syscall.SIGINT, syscall.SIGTERM)

	minioClient, err := minioutils.GetDefaultMinioClient()
	if err != nil {
		logger.GetLogger().Fatal("init minio client error", zap.Error(err))
	}

	handler := &strategyJobHandler{
		db:                  database.CaasDB,
		log:                 logger.GetLogger().Named("strategy-job-handler"),
		rds:                 database.RDS,
		standardJobTimeout:  standardTimeout,
		standardJobTaskPool: NewJobTaskPool(standardPoolSize, standardTimeout),
		strategyJobTimeout:  strategyTimeout,
		strategyJobTaskPool: NewJobTaskPool(strategyPoolSize, strategyTimeout),
		baselineAdapter:     infra.NewV1BaselineAdapter(),
		monitorAdapter:      infra.NewV1MonitorAdapter(),
		checkerAdapter:      infra.NewV1CheckerAdapter(),
		reportService:       baseline_master.NewService(),
		addonHandler:        addon.NewHandler(),
		minioClient:         minioClient,
		cron:                jobCron,
		quitSigs:            quitSigs,
	}

	// 启动扫描任务
	return handler
}

type strategyJobHandler struct {
	log *zap.Logger
	db  *gorm.DB
	rds *rds.Client
	// standardJobTimeout 标准任务超时时间
	standardJobTimeout time.Duration
	// standardJobTaskPool 任务池
	standardJobTaskPool *JobTaskPool
	// strategyJobTimeout 策略任务超时时间
	strategyJobTimeout time.Duration
	// strategyJobTaskPool 策略任务池
	strategyJobTaskPool *JobTaskPool
	// baselineAdapter 基线适配器
	baselineAdapter infra.BaselineAdapter
	// monitorAdapter 监控适配器
	monitorAdapter infra.MonitorAdapter
	// checkerAdapter 检查适配器
	checkerAdapter infra.CheckerAdapter
	// reportService 报告服务
	reportService baseline_master.Service
	// addonHandler addon handler
	addonHandler addon.Handler
	// minioClient minio
	minioClient *minio.Client
	// cron 调度器
	cron *cron.Cron
	// quitSigs 监听系统退出信号
	quitSigs chan os.Signal
}

func (s *strategyJobHandler) registerCronTask() error {
	ctx := context.Background()

	// 扫描超时任务
	if _, err := s.cron.AddFunc(helper.GetBaselineScanTimeoutJobCronConfigValue(ctx, s.db), func() {
		s.log.Info("start scan timeout jobs")
		if err := s.scanTimeoutJobs(ctx); err != nil {
			s.log.Error("scan timeout jobs error", zap.Error(err))
		}
		s.log.Info("scan timeout jobs completed")
	}); err != nil {
		return err
	}

	// 扫描资源残留超时任务
	if _, err := s.cron.AddFunc(helper.BaselineScanExpiredJobResourceConfigValue(ctx, s.db), func() {
		s.log.Info("start scan resource remain timeout jobs")
		if err := s.scanResourceRemainJobs(ctx); err != nil {
			s.log.Error("scan resource remain timeout jobs error", zap.Error(err))
		}
		s.log.Info("scan resource remain timeout jobs completed")
	}); err != nil {
		return err
	}

	// 扫描记录残留超时任务
	if _, err := s.cron.AddFunc(helper.GetBaselineScanExpiredJobRecordCronConfigValue(ctx, s.db), func() {
		s.log.Info("start scan strategy remain timeout jobs")
		if err := s.scanJobRecord(ctx); err != nil {
			s.log.Error("scan strategy remain timeout jobs error", zap.Error(err))
		}
		s.log.Info("scan strategy remain timeout jobs completed")
	}); err != nil {
		return err
	}

	return nil
}

// handleShutdown 处理系统退出
func (s *strategyJobHandler) handleShutdown(ctx context.Context, task *models.StrategyJobTask) {
	<-s.quitSigs
	s.log.Info("received system exit signal, starting cleanup process")
	if err := s.abortAllRunningJobs(ctx, task); err != nil {
		s.log.Error("failed to abort all running jobs", zap.Error(err))
	}
	// 停止接收新的任务并释放任务池
	s.standardJobTaskPool.Release()
	s.strategyJobTaskPool.Release()
	s.log.Info("cleanup process completed")

}

// abortAllRunningJobs 中止所有正在运行的任务
func (s *strategyJobHandler) abortAllRunningJobs(ctx context.Context, task *models.StrategyJobTask) error {
	var errs []error

	// 翻译错误信息
	abortMessage := "system exit caused job aborted"

	// 更新所有运行中的标准任务状态

	// strategyJobIds
	// standardJobIds
	// checkJobIds
	var checkJobIds []int64
	var standardJobIds []int64

	for _, standardJobTask := range task.StandardJobs {
		for _, checkJob := range standardJobTask.CheckJobs {
			checkJobIds = append(checkJobIds, checkJob.ID)
		}
		standardJobIds = append(standardJobIds, standardJobTask.Job.ID)
	}
	s.log.Info("abortAllRunningJobs:: abort all check jobs", zap.Any("check-job-ids", checkJobIds))
	if err := s.db.Model(&caas.BaselineStandardRuleJob{}).
		Where("id in (?)", checkJobIds).
		Where("status in (?)", []string{string(models.JobStatusRunning), string(models.JobStatusPending)}).
		Updates(map[string]any{
			"status":         string(models.JobStatusCompleted),
			"passed":         false,
			"reason":         models.JobReasonSystemAbort,
			"message":        abortMessage,
			"completed_time": time.Now(),
		}).Error; err != nil {
		errs = append(errs, fmt.Errorf("failed to update standard rule job: %w", err))
	}

	s.log.Info("abortAllRunningJobs:: abort all standard jobs", zap.Any("standard-job-ids", standardJobIds))
	if err := s.db.Model(&caas.BaselineStandardJob{}).
		Where("id in (?)", standardJobIds).
		Where("status in (?)", []string{string(models.JobStatusRunning), string(models.JobStatusPending)}).
		Updates(map[string]any{
			"status":         string(models.JobStatusCompleted),
			"passed":         false,
			"reason":         models.JobReasonSystemAbort,
			"message":        abortMessage,
			"completed_time": time.Now(),
		}).Error; err != nil {
		errs = append(errs, fmt.Errorf("failed to update standard job: %w", err))
	}

	// 更新所有运行中的策略任务状态
	s.log.Info("abortAllRunningJobs:: abort all strategy job", zap.Any("strategy-job-id", task.Job.ID))
	if err := s.db.Model(&caas.BaselineStrategyJob{}).
		Where("id = ?", task.Job.ID).
		Where("status in (?)", []string{string(models.JobStatusRunning), string(models.JobStatusPending)}).
		Updates(map[string]any{
			"status":         string(models.JobStatusCompleted),
			"passed":         false,
			"reason":         models.JobReasonSystemAbort,
			"message":        abortMessage,
			"completed_time": time.Now(),
		}).Error; err != nil {
		errs = append(errs, fmt.Errorf("failed to update strategy job: %w", err))
	}
	err := task.Lock.Release(ctx)
	if err != nil {
		errs = append(errs, fmt.Errorf("release key failed, err: %w", err))
	}

	if len(errs) > 0 {
		return errors.Join(errs...)
	}
	return nil
}

// scanJobRecord 扫描基线任务记录
func (s *strategyJobHandler) scanJobRecord(ctx context.Context) error {
	lock := redislock.NewLock(s.rds, baselineScanJobRecordLock, redislock.SimpleLock)
	defer lock.Release(ctx)
	acquired, err := lock.TryLock(ctx, time.Minute*5)
	if err != nil {
		s.log.Error("failed to acquire lock for task", zap.Error(err))
		return fmt.Errorf("failed to acquire lock for task: %v", err)
	}
	if !acquired {
		return nil
	}
	// 获取基线任务记录保留时间
	remainTime := helper.GetBaselineJobRecordRemainTime(ctx, s.db)
	now := time.Now()
	bucketName := constants.MinioBaselineReportBucketName
	s.log.Info("scanJobRecord task start", zap.Any("remainTime", remainTime), zap.Any("now", now.Format(time.DateTime)),
		zap.Any("expiredTime", now.Add(-remainTime).Format(time.DateTime)))
	// 删除超过保留时间的基线任务记录
	// 批量读取，批量删除 防止单次删除太多导致数据库压力过大。
	// 1. 获取每个strategy_id的最新baseline_strategy_job记录
	var latestStrategyJobs []*caas.BaselineStrategyJob
	if err := s.db.WithContext(ctx).
		Table("baseline_strategy_job").
		Select("strategy_id, MAX(id) as id").
		Group("strategy_id").
		Find(&latestStrategyJobs).Error; err != nil {
		s.log.Error("failed to get latest strategy jobs", zap.Error(err))
		return fmt.Errorf("failed to get latest strategy jobs: %w", err)
	}

	// 将最新的strategy job id放入map中
	latestStrategyJobIDs := make([]int64, 0, len(latestStrategyJobs))
	for _, job := range latestStrategyJobs {
		latestStrategyJobIDs = append(latestStrategyJobIDs, job.ID)
	}

	// 2. 获取最新strategy job关联的standard job
	var latestStandardJobs []*caas.BaselineStandardJob
	if err := s.db.WithContext(ctx).
		Select("standard_id, strategy_id, MAX(id) as id").
		Where("strategy_job_id IN ?", latestStrategyJobIDs).
		Group("standard_id, strategy_id").
		Find(&latestStandardJobs).Error; err != nil {
		s.log.Error("failed to get latest standard jobs", zap.Error(err))
		return fmt.Errorf("failed to get latest standard jobs: %w", err)
	}

	// 将最新的standard job id放入map中
	latestStandardJobIDs := make([]int64, 0, len(latestStandardJobs))
	for _, job := range latestStandardJobs {
		latestStandardJobIDs = append(latestStandardJobIDs, job.ID)
	}

	// 3. 删除超过保留时间的baseline_strategy_job（排除最新记录）

	batchSize := 1000
	var strategyJobs []caas.BaselineStrategyJob
	err = s.db.WithContext(ctx).
		Where("create_time < ? AND id NOT IN ?", now.Add(-remainTime), latestStrategyJobIDs).
		FindInBatches(&strategyJobs, batchSize, func(tx *gorm.DB, batch int) error {
			if len(strategyJobs) == 0 {
				return nil // 没有数据时直接返回
			}
			if err := tx.Delete(&strategyJobs).Error; err != nil {
				s.log.Error("failed to delete strategy jobs batch",
					zap.Int("batch", batch),
					zap.Int("count", len(strategyJobs)),
					zap.Error(err))
				return fmt.Errorf("failed to delete batch %d: %w", batch, err)
			}

			s.log.Info("successfully deleted strategy jobs batch",
				zap.Int("batch", batch),
				zap.Int("count", len(strategyJobs)))
			return nil
		}).Error
	if err != nil {
		s.log.Error("failed to process strategy jobs deletion", zap.Error(err))
		return fmt.Errorf("failed to process strategy jobs deletion: %w", err)
	}

	// 4. 删除超过保留时间的baseline_standard_job（排除最新记录）
	var standardJobs []caas.BaselineStandardJob
	err = s.db.WithContext(ctx).
		Where("create_time < ? AND id NOT IN ?", now.Add(-remainTime), latestStandardJobIDs).
		FindInBatches(&standardJobs, batchSize, func(tx *gorm.DB, batch int) error {
			if len(standardJobs) == 0 {
				return nil // 没有数据时直接返回
			}
			// 收集需要删除的报告文件
			var reportFiles []string
			for _, job := range standardJobs {
				if job.Result != "" && strings.HasPrefix(job.Result, "/") && strings.HasSuffix(job.Result, ".json") {
					reportFiles = append(reportFiles, job.Result)
				}
				if job.ClusterName != "" {
					if job.BaselineName != "" {
						reportFiles = append(reportFiles, fmt.Sprintf("%s/baselines/%s.json", job.ClusterName, job.BaselineName))
					}
				}

			}
			// 异步删除报告文件
			if len(reportFiles) > 0 {
				go func(files []string) {
					ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
					defer cancel()
					// 使用更高效的方式批量删除对象
					objectsCh := make(chan minio.ObjectInfo, len(files))
					for _, file := range files {
						objectsCh <- minio.ObjectInfo{Key: file}
					}
					close(objectsCh)
					for err := range s.minioClient.RemoveObjects(ctx, bucketName, objectsCh, minio.RemoveObjectsOptions{}) {
						if err.Err != nil {
							s.log.Error("failed to remove report file",
								zap.String("file", err.ObjectName),
								zap.Error(err.Err))
						}
					}
				}(reportFiles)
			}
			// 删除数据库记录
			if err := tx.Delete(&standardJobs).Error; err != nil {
				s.log.Error("failed to delete standard jobs batch",
					zap.Int("batch", batch),
					zap.Int("count", len(standardJobs)),
					zap.Error(err))
				return fmt.Errorf("failed to delete batch %d: %w", batch, err)
			}
			s.log.Info("successfully processed standard jobs batch",
				zap.Int("batch", batch),
				zap.Int("count", len(standardJobs)),
				zap.Int("report_files", len(reportFiles)))
			return nil
		}).Error
	if err != nil {
		s.log.Error("failed to process standard jobs deletion", zap.Error(err))
		return fmt.Errorf("failed to process standard jobs deletion: %w", err)
	}

	// 5. 删除超过保留时间的baseline_standard_rule_job（排除关联最新standard job的记录）
	var ruleJobs []caas.BaselineStandardRuleJob
	err = s.db.WithContext(ctx).
		Where("create_time < ? AND standard_job_id NOT IN ?", now.Add(-remainTime), latestStandardJobIDs).
		FindInBatches(&ruleJobs, batchSize, func(tx *gorm.DB, batch int) error {
			if len(ruleJobs) == 0 {
				return nil // 没有数据时直接返回
			}
			// 收集需要删除的报告文件
			var reportFiles []string
			for _, job := range ruleJobs {
				if job.ClusterName != "" {
					if job.CheckerName != "" {
						reportFiles = append(reportFiles, fmt.Sprintf("%s/checkers/%s.json", job.ClusterName, job.CheckerName))
					}
					if job.MonitorName != "" {
						reportFiles = append(reportFiles, fmt.Sprintf("%s/monitors/%s.json", job.ClusterName, job.MonitorName))
					}
				}
			}
			// 异步删除报告文件
			if len(reportFiles) > 0 {
				go func(files []string) {
					ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
					defer cancel()
					// 使用更高效的方式批量删除对象
					objectsCh := make(chan minio.ObjectInfo, len(files))
					for _, file := range files {
						objectsCh <- minio.ObjectInfo{Key: file}
					}
					close(objectsCh)
					for err := range s.minioClient.RemoveObjects(ctx, bucketName, objectsCh, minio.RemoveObjectsOptions{}) {
						if err.Err != nil {
							s.log.Error("failed to remove report file",
								zap.String("file", err.ObjectName),
								zap.Error(err.Err))
						}
					}
				}(reportFiles)
			}
			if err := tx.Delete(&ruleJobs).Error; err != nil {
				s.log.Error("failed to delete rule jobs batch",
					zap.Int("batch", batch),
					zap.Int("count", len(ruleJobs)),
					zap.Error(err))
				return fmt.Errorf("failed to delete batch %d: %w", batch, err)
			}
			s.log.Info("successfully deleted rule jobs batch",
				zap.Int("batch", batch),
				zap.Int("count", len(ruleJobs)))
			return nil
		}).Error
	if err != nil {
		s.log.Error("failed to process rule jobs deletion", zap.Error(err))
		return fmt.Errorf("failed to process rule jobs deletion: %w", err)
	}

	expirationTime := now.Add(-remainTime)
	objects := s.minioClient.ListObjects(ctx, bucketName, minio.ListObjectsOptions{
		Recursive: true,
	})
	var errs []error
	for object := range objects {
		if object.Err != nil {
			errs = append(errs, fmt.Errorf("error listing object: %w", object.Err))
			continue
		}
		if object.LastModified.Before(expirationTime) {
			if err := s.minioClient.RemoveObject(ctx, bucketName, object.Key, minio.RemoveObjectOptions{}); err != nil {
				errs = append(errs, fmt.Errorf("failed to remove object %s: %w", object.Key, err))
				continue
			}
			s.log.Info(fmt.Sprintf("Deleted expired object: %s (last modified: %v)", object.Key, object.LastModified))
		}
	}
	if len(errs) > 0 {
		return fmt.Errorf("encountered %d errors during cleanup", len(errs))
	}
	return nil
}

func (s *strategyJobHandler) scanResourceRemainJobs(ctx context.Context) error {
	lock := redislock.NewLock(s.rds, baselineScanResourceRemainTimeoutJobLock, redislock.SimpleLock)
	defer lock.Release(ctx)
	acquired, err := lock.TryLock(ctx, time.Minute*20)
	if err != nil {
		s.log.Error("failed to acquire lock for task", zap.Error(err))
		return fmt.Errorf("failed to acquire lock for task: %v", err)
	}
	if !acquired {
		return nil
	}
	remainTime := helper.GetBaselineResourceRemainTimeConfigValue(ctx, s.db)

	now := time.Now()
	expiredTime := now.Add(-remainTime).UTC()
	s.log.Info("scanResourceRemainJobs task start", zap.Any("remainTime", remainTime), zap.Any("now", now.Format(time.DateTime)),
		zap.Any("expiredTime", expiredTime.Format(time.RFC3339)))

	var errs []error
	baselineList := checkerv1.BaselineList{}
	if err := s.cleanExpiredResources(ctx, expiredTime, &baselineList); err != nil {
		s.log.Error("failed to clean expired resources", zap.Error(err))
		errs = append(errs, fmt.Errorf("failed to clean expired baseline resources: %w", err))
	}

	checkerList := checkerv1.CheckerList{}
	if err := s.cleanExpiredResources(ctx, expiredTime, &checkerList); err != nil {
		s.log.Error("failed to clean expired resources", zap.Error(err))
		errs = append(errs, fmt.Errorf("failed to clean expired checker resources: %w", err))
	}

	monitorList := checkerv1.MonitorList{}
	if err := s.cleanExpiredResources(ctx, expiredTime, &monitorList); err != nil {
		s.log.Error("failed to clean expired resources", zap.Error(err))
		errs = append(errs, fmt.Errorf("failed to clean expired monitor resources: %w", err))
	}

	//if err := s.cleanExpiredStrategyConfigMaps(ctx); err != nil {
	//	s.log.Error("failed to clean expired strategy config maps", zap.Error(err))
	//	errs = append(errs, fmt.Errorf("failed to clean expired strategy config maps: %w", err))
	//}

	if len(errs) > 0 {
		return goerrors.Join(errs...)
	}

	return nil
}

// cleanExpiredStrategyConfigMaps 清理过期的策略配置
func (s *strategyJobHandler) cleanExpiredStrategyConfigMaps(ctx context.Context) error {
	labelExist, err := labels.NewRequirement(constants.BaselineStrategyIDLabelKey, selection.Exists, []string{})
	if err != nil {
		return fmt.Errorf("failed to create label requirement: %w", err)
	}
	labelSelector := labels.NewSelector().Add(*labelExist)
	batchSize := int64(500)
	client := clientmgr.GetHubCluster().GetClient().GetCtrlClient()
	continueToken := ""
	configmaps := &corev1.ConfigMapList{}
	for {
		opts := &ctrlclient.ListOptions{
			LabelSelector: labelSelector,
			Limit:         batchSize,
			Continue:      continueToken,
		}
		if err := client.List(ctx, configmaps, opts); err != nil {
			return fmt.Errorf("failed to list configmaps: %w", err)
		}
		continueToken = configmaps.ListMeta.Continue
		if continueToken == "" || len(configmaps.Items) == 0 {
			break
		}
		for _, configmap := range configmaps.Items {
			strategyID := configmap.Labels[constants.BaselineStrategyIDLabelKey]
			if strategyID == "" {
				continue
			}
			// check if strategy id has been deleted in db
			var strategy caas.BaselineStrategy
			if err := s.db.WithContext(ctx).Where("id = ?", strategyID).First(&strategy).Error; err != nil {
				if goerrors.Is(err, gorm.ErrRecordNotFound) {
					// delete configmap
					if err := client.Delete(ctx, &corev1.ConfigMap{
						ObjectMeta: metav1.ObjectMeta{
							Name:      configmap.Name,
							Namespace: constants.BaselineNamespace,
						},
					}); err != nil {
						s.log.Error("failed to delete configmap", zap.String("name", configmap.Name), zap.Error(err))
					}
				}
				s.log.Error("failed to get strategy", zap.String("name", configmap.Name), zap.String("strategy id", strategyID), zap.Error(err))
			}
		}
	}
	return nil
}

// cleanExpiredMonitors 清理过期的监控
func (s *strategyJobHandler) cleanExpiredMonitors(ctx context.Context, monitors *checkerv1.MonitorList) error {
	labelExist, err := labels.NewRequirement(constants.BaselineStandardJobIDLabelKey, selection.Exists, []string{})
	if err != nil {
		return fmt.Errorf("failed to create label requirement: %w", err)
	}
	labelSelector := labels.NewSelector().Add(*labelExist)
	batchSize := int64(500)
	clusters := clientmgr.ListOnlineClusters()
	for _, cluster := range clusters {
		c := cluster.GetClient().GetCtrlClient()
		continueToken := ""
		for {
			opts := &ctrlclient.ListOptions{
				LabelSelector: labelSelector,
				Limit:         batchSize,
				Continue:      continueToken,
				Raw: &metav1.ListOptions{
					AllowWatchBookmarks: false,
				},
			}
			if err := c.List(ctx, monitors, opts); err != nil {
				return fmt.Errorf("failed to list monitors: %w", err)
			}
			continueToken = monitors.ListMeta.Continue
			if continueToken == "" || len(monitors.Items) == 0 {
				break
			}
			for _, monitor := range monitors.Items {
				if monitor.GetDeletionTimestamp().IsZero() {
					continue
				}
				// get strategy id from label
				strategyID, ok := monitor.Labels[constants.BaselineStrategyIDLabelKey]
				if !ok || strategyID == "" {
					continue
				}
				// check if strategy id has been deleted in db
				var strategy caas.BaselineStrategy
				if err := s.db.WithContext(ctx).Where("id = ?", strategyID).First(&strategy).Error; err != nil {
					if goerrors.Is(err, gorm.ErrRecordNotFound) {
						// delete monitor
						if err := c.Delete(ctx, &monitor); err != nil {
							s.log.Error("failed to delete monitor", zap.String("name", monitor.GetName()), zap.String("strategy id", strategyID), zap.Error(err))
						}
					}
					s.log.Error("failed to get strategy", zap.String("name", monitor.GetName()), zap.String("strategy id", strategyID), zap.Error(err))
				}
			}
		}
	}
	return nil
}

func (s *strategyJobHandler) cleanExpiredResources(ctx context.Context, expiredTime time.Time, listObj ctrlclient.ObjectList) error {
	continueToken := ""
	labelExist, err := labels.NewRequirement(constants.BaselineStandardJobIDLabelKey, selection.Exists, []string{})
	if err != nil {
		return err
	}
	labelSelector := labels.NewSelector().Add(*labelExist)
	batchSize := int64(500)
	clusters := clientmgr.ListOnlineClusters()
	for _, cluster := range clusters {
		c := cluster.GetClient().GetCtrlClient()
		for {
			opts := &ctrlclient.ListOptions{
				LabelSelector: labelSelector,
				Limit:         batchSize,
				Continue:      continueToken,
				Raw: &metav1.ListOptions{
					AllowWatchBookmarks: false,
				},
			}
			listAccessor, err := meta.ListAccessor(listObj)
			if err != nil {
				return fmt.Errorf("invalid list object: %w", err)
			}
			if err := c.List(ctx, listObj, opts); err != nil {
				return fmt.Errorf("list failed: %w", err)
			}
			runtimeObjs, err := meta.ExtractList(listObj)
			if err != nil {
				return fmt.Errorf("extract list failed: %w", err)
			}
			items := make([]ctrlclient.Object, 0, len(runtimeObjs))
			for _, runtimeObj := range runtimeObjs {
				obj, ok := runtimeObj.(ctrlclient.Object)
				if !ok {
					continue
				}
				items = append(items, obj)
			}
			for _, item := range items {
				item := item
				creationTime := item.GetCreationTimestamp().Time
				if creationTime.Before(expiredTime) && item.GetDeletionTimestamp().IsZero() {
					if err := c.Delete(ctx, item); err != nil {
						s.log.Debug("failed to delete resource", zap.String("name", item.GetName()),
							zap.String("namespace", item.GetNamespace()),
							zap.String("kind", fmt.Sprintf("%T", listObj)),
							zap.Time("creationTime", creationTime),
							zap.Time("expiredTime", expiredTime),
							zap.Error(err))
						continue
					}
				}

				switch listObj.(type) {
				case *checkerv1.MonitorList:
					// get strategy ids from labels or annotations
					strategyIdsValue, _ := helper.GetObjectAnnotateAndLabelValue(item, constants.BaselineStrategyIDsAnnotationKey)
					ids := sets.New(lo.Filter(strings.Split(strategyIdsValue, ","), func(item string, index int) bool {
						return item != ""
					})...)
					if len(ids) == 0 {
						if err := c.Delete(ctx, item); err != nil {
							s.log.Error("failed to already deleted resource",
								zap.String("name", item.GetName()),
								zap.String("resourceKind", fmt.Sprintf("%T", item)))
						}
					} else {
						// check if strategy id has been deleted in db
						var strategies []caas.BaselineStrategy
						if err := s.db.WithContext(ctx).Where("id in (?)", ids).Find(&strategies).Error; err != nil {
							s.log.Error("failed to list strategy",
								zap.String("name", item.GetName()),
								zap.Any("strategyIds", ids),
								zap.String("resourceKind", fmt.Sprintf("%T", item)),
								zap.Error(err))
							continue
						}
						if len(strategies) == 0 {
							if err := c.Delete(ctx, item); err != nil {
								s.log.Error("failed to already deleted resource",
									zap.String("name", item.GetName()),
									zap.String("resourceKind", fmt.Sprintf("%T", item)))
							}
						}
					}
				default:
					// get strategy id from label
					strategyID, ok := item.GetLabels()[constants.BaselineStrategyIDLabelKey]
					if !ok || strategyID == "" {
						continue
					}
					// check if strategy id has been deleted in db
					var strategies []caas.BaselineStrategy
					if err := s.db.WithContext(ctx).Where("id = ?", strategyID).Find(&strategies).Limit(1).Error; err != nil {
						s.log.Error("failed to get strategy",
							zap.String("name", item.GetName()),
							zap.String("strategy id", strategyID),
							zap.String("resourceKind", fmt.Sprintf("%T", item)),
							zap.Error(err))
						continue
					}
					if len(strategies) == 0 {
						if err := c.Delete(ctx, item); err != nil {
							s.log.Error("failed to already deleted resource",
								zap.String("name", item.GetName()),
								zap.String("resourceKind", fmt.Sprintf("%T", item)),
								zap.String("strategy id", strategyID), zap.Error(err))
						}
					}
				}

			}
			continueToken = listAccessor.GetContinue()
			if continueToken == "" || len(items) == 0 {
				break
			}
		}
	}

	return nil
}

// scanTimeoutJobs 扫描超时任务
func (s *strategyJobHandler) scanTimeoutJobs(ctx context.Context) error {
	// 获取分布式锁
	lock := redislock.NewLock(s.rds, baselineScanTimeoutJobLock, redislock.SimpleLock)
	defer lock.Release(ctx)

	acquired, err := lock.TryLock(ctx, time.Minute*5)
	if err != nil {
		s.log.Error("failed to acquire lock for task", zap.Error(err))
		return fmt.Errorf("failed to acquire lock for task: %v", err)
	}
	if !acquired {
		return nil
	}

	var errs []error
	timeout := time.Now().Add(-s.standardJobTimeout)
	updateFields := map[string]interface{}{
		"status":      models.JobStatusCompleted,
		"update_user": "system",
		"passed":      false,
		"reason":      models.JobReasonCheckTimeout,
		"message":     fmt.Sprintf("execute job time out after %s", s.standardJobTimeout.String()),
	}
	// 处理三种作业类型的通用函数
	processJobs := func(model interface{}, jobType string, total *int) error {
		startTime := time.Now()
		result := s.db.WithContext(ctx).
			Model(model).
			Where("status IN (?)", []string{
				string(models.JobStatusPending),
				string(models.JobStatusRunning)}).
			Where("create_time < ?", timeout).
			Updates(updateFields)

		if result.Error != nil {
			return fmt.Errorf("failed to process %s jobs: %v", jobType, result.Error)
		}

		*total = int(result.RowsAffected)
		s.log.Info(fmt.Sprintf("updated timeout %s jobs", jobType),
			zap.Int("count", *total),
			zap.Duration("duration", time.Since(startTime)))
		return nil
	}

	var (
		checkJobTotal    int
		standardJobTotal int
		strategyJobTotal int
	)

	if err := processJobs(&caas.BaselineStandardRuleJob{}, "check", &checkJobTotal); err != nil {
		errs = append(errs, err)
	}
	if err := processJobs(&caas.BaselineStandardJob{}, "standard", &standardJobTotal); err != nil {
		errs = append(errs, err)
	}
	if err := processJobs(&caas.BaselineStrategyJob{}, "strategy", &strategyJobTotal); err != nil {
		errs = append(errs, err)
	}

	totalJobs := checkJobTotal + standardJobTotal + strategyJobTotal
	s.log.Info("total timeout jobs processed",
		zap.Int("check_jobs", checkJobTotal),
		zap.Int("standard_jobs", standardJobTotal),
		zap.Int("strategy_jobs", strategyJobTotal),
		zap.Int("total", totalJobs))

	return errors.Join(errs...)
}

func (s *strategyJobHandler) ExecuteJob(ctx context.Context, req *models.ExecuteCheckJobRequest) (resp *models.ExecuteCheckJobResponse, err error) {
	resp = new(models.ExecuteCheckJobResponse)
	scope := models.GetExecuteStrategyCheckScopeByJobRequestIfScopeEmpty(*req)
	switch scope {
	case models.ExecuteCheckJobScopeCluster:
		if err := s.ExecuteClusterJob(ctx, req.StrategyID, req.ClusterRequest); err != nil {
			return nil, err
		}
	case models.ExecuteCheckJobScopeCheck:
		if err := s.ExecuteCheckJob(ctx, req.StrategyID, req.CheckRequest); err != nil {
			return nil, err
		}
	case models.ExecuteCheckJobScopeStandard:
		if err := s.ExecuteStandardJob(ctx, req.StrategyID, req.StandardRequest); err != nil {
			return nil, err
		}
	case models.ExecuteCheckJobScopeStrategy: // 默认执行全量检查
		fallthrough
	default:
		if err := s.ExecuteStrategyJob(ctx, req.StrategyID); err != nil {
			return nil, err
		}
	}
	return
}

// ExecuteStrategyJob 执行全量检查
func (s *strategyJobHandler) ExecuteStrategyJob(ctx context.Context, strategyId int64) error {
	strategy, err := s.getStrategyByID(ctx, strategyId)
	if err != nil {
		return err
	}

	lockKey := getRunStrategyJobLockKey(strategyId)
	lock := redislock.NewSimpleLock(s.rds, lockKey)
	if lock.IsLocked() {
		return errors.NewFromCodeWithMessage(errors.Var.BaselineRunStrategyJobAlreadyRunning, "")
	}
	acquired, err := lock.TryLock(ctx, s.strategyJobTimeout)
	if err != nil || !acquired {
		errMsg := fmt.Sprintf("try lock failed err: %v", err)
		s.log.Error(errMsg, zap.Any("strategy-id", strategyId))
		return errors.NewFromCodeWithMessage(errors.Var.BaselineRunStrategyCheckJobFailedGetLock, errMsg)
	}
	// 根据 strategyId 找到关联的集群和标准
	clusterStandardsMap, standardIds, err := s.getStrategyClusterStandardIdsByIdAndClusters(ctx, strategyId,
		strings.Split(strategy.ClusterNames, ","))
	if err != nil {
		return err
	}
	standardCheckList, err := s.getStandardCheckListByStandardIds(ctx, standardIds)
	if err != nil {
		return err
	}
	// 构建BuildStrategyJobTask
	jobTask := helper.BuildStrategyJobTask(ctx, strategy, clusterStandardsMap, standardCheckList)
	if err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := helper.InitStrategyJobTask(ctx, tx, jobTask); err != nil {
			return err
		}
		return nil
	}); err != nil {
		return err
	}
	jobTask.Lock = lock
	// 处理退出信号
	go s.handleShutdown(ctx, jobTask)

	go func(ctx context.Context, jobTask *models.StrategyJobTask) {
		if err := s.RunStrategyJob(ctx, jobTask); err != nil {
			s.log.With(zap.Int64("strategy-id", jobTask.Record.ID)).
				With(zap.Int64("strategy-job-id", jobTask.Job.ID)).
				Error("run strategy job failed", zap.Error(err))
			return
		}

	}(ctx, jobTask)

	return nil
}

// ExecuteClusterJob 执行集群检查
func (s *strategyJobHandler) ExecuteClusterJob(ctx context.Context, strategyId int64, req models.CheckClusterListRequest) error {
	strategy, err := s.getStrategyByID(ctx, strategyId)
	if err != nil {
		return err
	}
	// 根据 strategyId 找到关联的集群和标准
	clusterStandardsMap, standardIds, err := s.getStrategyClusterStandardIdsByIdAndClusters(ctx, strategyId, req.ClusterNames)
	if err != nil {
		return err
	}
	standardCheckList, err := s.getStandardCheckListByStandardIds(ctx, standardIds)
	if err != nil {
		return err
	}

	// 构建BuildStrategyJobTask
	jobTask := helper.BuildStrategyJobTask(ctx, strategy, clusterStandardsMap, standardCheckList)
	if err := helper.InitStrategyJobTask(ctx, s.db, jobTask); err != nil {
		return err
	}

	// 异步执行 提交任务
	s.standardJobTaskPool.Submit(ctx, func(taskCtx context.Context) error {
		if err := s.RunStrategyJob(taskCtx, jobTask); err != nil {
			s.log.With(zap.Int64("strategy-id", jobTask.Record.ID)).
				With(zap.Int64("strategy-job-id", jobTask.Job.ID)).
				Error("run strategy job failed", zap.Error(err))
			return err
		}
		return nil
	})

	return nil
}

// ExecuteStandardJob 执行基线检查
func (s *strategyJobHandler) ExecuteStandardJob(ctx context.Context, strategyId int64, req models.CheckStandardListRequest) error {
	strategy, err := s.getStrategyByID(ctx, strategyId)
	if err != nil {
		return err
	}
	// 根据 strategyId 找到关联的集群和标准
	clusterStandardsMap, _, err := s.getStrategyClusterStandardIdsByIdAndClusters(ctx, strategyId, req.ClusterNames)
	if err != nil {
		return err
	}
	filteredMap := make(map[string][]caas.BaselineStandard)
	for clusterName, clusterStandards := range clusterStandardsMap {
		standardIds := lo.Map(clusterStandards, func(standard caas.BaselineStandard, _ int) int64 { return standard.ID })
		if lo.Contains(standardIds, req.StandardID) {
			filteredMap[clusterName] = clusterStandards
		}
	}
	clusterStandardsMap = filteredMap
	standardCheckList, err := s.getStandardCheckListByStandardIds(ctx, []int64{req.StandardID})
	if err != nil {
		return err
	}
	// 构建BuildStrategyJobTask
	jobTask := helper.BuildStrategyJobTask(ctx, strategy, clusterStandardsMap, standardCheckList)
	if err := helper.InitStrategyJobTask(ctx, s.db, jobTask); err != nil {
		return err
	}

	// 异步执行 提交任务
	s.standardJobTaskPool.Submit(ctx, func(taskCtx context.Context) error {
		if err := s.RunStrategyJob(taskCtx, jobTask); err != nil {
			s.log.With(zap.Int64("strategy-id", jobTask.Record.ID)).
				With(zap.Int64("strategy-job-id", jobTask.Job.ID)).
				Error("run strategy job failed", zap.Error(err))
			return err
		}
		return nil
	})
	return nil
}

// ExecuteCheckJob 执行标准检查
func (s *strategyJobHandler) ExecuteCheckJob(ctx context.Context, strategyId int64, req models.CheckStandardCheckListRequest) error {
	strategy, err := s.getStrategyByID(ctx, strategyId)
	if err != nil {
		return err
	}
	// 根据 strategyId 找到关联的集群和标准
	clusterStandardsMap, _, err := s.getStrategyClusterStandardIdsByIdAndClusters(ctx, strategyId, []string{req.ClusterName})
	if err != nil {
		return err
	}
	standardCheckList, err := s.getStandardCheckListByStandardIds(ctx, []int64{req.StandardID})
	if err != nil {
		return err
	}
	standardCheckers := make([]models.StandardCheckItem, len(req.CheckIDs))
	for _, standardChecker := range standardCheckers {
		if lo.Contains(req.CheckIDs, standardChecker.ID) {
			standardCheckList[req.StandardID] = append(standardCheckList[req.StandardID], standardChecker)
			break
		}
	}

	// 构建BuildStrategyJobTask
	jobTask := helper.BuildStrategyJobTask(ctx, strategy, clusterStandardsMap, standardCheckList)
	if len(jobTask.StandardJobs) == 0 {
		return nil
	}
	standardJobTask := jobTask.StandardJobs[0]
	if err := helper.InitCheckJobs(ctx, s.db, standardJobTask.CheckJobs); err != nil {
		return err
	}
	checkJobTasks := helper.BuildCheckJobTask(ctx, standardJobTask.CheckJobs, standardCheckList[req.StandardID])
	// 异步执行 提交任务
	s.standardJobTaskPool.Submit(ctx, func(taskCtx context.Context) error {
		if err := s.RunCheckJobs(taskCtx, standardJobTask.Job, checkJobTasks); err != nil {
			s.log.With(zap.Int64("strategy-id", standardJobTask.Job.StrategyID)).
				With(zap.Int64("standard-id", standardJobTask.Job.StandardID)).
				With(zap.Any("check-ids", req.CheckIDs)).
				With(zap.String("cluster-name", standardJobTask.Job.ClusterName)).
				Error("run strategy job failed", zap.Error(err))
			return err
		}
		return nil
	})
	return nil
}

// RunCheckJobs 执行标准检查
// TODO: 该方法需要重构 1. 获取报告的方式要改变
func (s *strategyJobHandler) RunCheckJobs(ctx context.Context,
	stdJob *caas.BaselineStandardJob, jobTasks []*models.CheckJobTask) error {

	log := s.log.With(zap.String("cluster-name", stdJob.ClusterName)).
		With(zap.Int64("strategy-id", stdJob.StrategyID)).
		With(zap.Int64("standard-id", stdJob.StandardID))
	var errMap = make(map[int64][]error)
	var checkerNames []string
	checkJobTaskMap := lo.SliceToMap(jobTasks, func(item *models.CheckJobTask) (int64, *models.CheckJobTask) {
		return item.Job.ID, item
	})
	// 1. 处理错误
	defer func() {
		if len(errMap) > 0 {
			for id, errs := range errMap {
				if len(errs) > 0 {
					reason, message := models.JobReasonUnknown, errs[0].Error()
					log.Error("run check job failed", zap.Error(errs[0]), zap.Int64("job-id", id))
					job := checkJobTaskMap[id].Job
					if job != nil {
						if job.Status == string(models.JobStatusCompleted) {
							reason, message = job.Reason, lo.Ternary(message == job.Message, message,
								message+job.Message)
						}
						_ = helper.UpdateCheckJobStatusWithPassedAndReasonWithMessage(ctx, s.db, job,
							models.JobStatusCompleted, lo.ToPtr(false), lo.ToPtr(reason), message)
					}
				}
			}
		}
	}()
	for i := range jobTasks {
		// 2. 更新状态
		job := jobTasks[i].Job
		checkItem := jobTasks[i].CheckItem
		if err := helper.UpdateCheckJobStatusWithPassedAndReasonWithMessage(ctx, s.db,
			job, models.JobStatusRunning, nil, nil, ""); err != nil {
			errMap[job.ID] = append(errMap[job.ID], err)
		}
		// 3. 创建资源
		checkerParam := &models.CheckerParam{
			ClusterName: job.ClusterName,
			StandardID:  job.StandardID,
			StrategyID:  job.StrategyID,
			CheckID:     checkItem.ID,
			CheckRawData: models.CheckRawData{
				Value:  checkItem.CheckValue,
				Config: *checkItem.CheckerConfig,
			},
		}
		checker, err := s.checkerAdapter.CreateChecker(ctx, checkerParam)
		if err != nil {
			newErr := fmt.Errorf("create checker failed, err: %v", err)
			if err := helper.UpdateCheckJobStatusWithPassedAndReasonWithMessage(ctx, s.db,
				job, models.JobStatusCompleted, lo.ToPtr(false),
				lo.ToPtr(models.JobReasonCheckerCreateError),
				newErr.Error()); err != nil {
				errMap[job.ID] = append(errMap[job.ID], err)
			}
			errMap[job.ID] = append(errMap[job.ID], newErr)
			continue
		}
		checkerNames = append(checkerNames, checker.CheckerName)
		if err := helper.SetCheckerNameToCheckJob(ctx, s.db, []*caas.BaselineStandardRuleJob{job}, []*models.InfraCheckerItem{checker}); err != nil {
			errMap[job.ID] = append(errMap[job.ID], err)
		}
	}

	// 3. 执行检查
	checkersReportResp, err := s.reportService.GetCheckersReport(ctx, baseline_master.NewGetCheckersReportRequest(checkerNames))
	if err != nil {
		newErr := fmt.Errorf("get checkers report failed, err: %v", err)
		for i := range jobTasks {
			if err := helper.UpdateCheckJobStatusWithPassedAndReasonWithMessage(ctx, s.db,
				jobTasks[i].Job, models.JobStatusCompleted, lo.ToPtr(false),
				lo.ToPtr(models.JobReasonGetReportError),
				newErr.Error()); err != nil {
				return err
			}
		}
		log.Error("get checkers report failed", zap.Error(err))
		return newErr
	}
	checkReports := checkersReportResp.Data.CheckItems

	checkersReport := make(map[string]infrachecker.CheckItem, len(checkReports))
	for _, checkReport := range checkReports {
		checkersReport[checkReport.Name] = checkReport
	}

	// 4. 更新状态
	for i := range jobTasks {
		job := jobTasks[i].Job
		checkItem := jobTasks[i].CheckItem
		if err := helper.CompareReportValue(ctx, s.db, job, checkersReport[checkItem.Name]); err != nil {
			errMap[job.ID] = append(errMap[job.ID], err)
		}
		if err := helper.UpdateCheckJobStatusWithPassedAndReasonWithMessage(ctx, s.db,
			job, models.JobStatusCompleted, nil, nil, ""); err != nil {
			errMap[job.ID] = append(errMap[job.ID], err)
		}
	}
	return nil
}

func (s *strategyJobHandler) PollGetReport(ctx context.Context,
	task *models.StandardJobTask,
	request *baseline_master.GetReportRequest) (*baseline_master.GetReportResponse, error) {
	stdJob := task.Job
	checkJobs := task.CheckJobs
	log := s.log.Named("PollGetReport").With(zap.String("cluster", stdJob.ClusterName),
		zap.String("baseline", stdJob.BaselineName), zap.Int64("standardId", stdJob.ID))
	start := time.Now()
	// 配置退避策略
	backoff := wait.Backoff{
		// 初始间隔时间
		Duration: 5 * time.Second,
		// 间隔时间增长因子 factor * duration
		Factor: 1.3,
		// 抖动 duration * jitter
		Jitter: 0.1,
		// 最大重试次数
		Steps: 20,
		// 间隔时间上限
		Cap: 120 * time.Second,
	}

	var lastResult *baseline_master.GetReportResponse
	var err error

	requiredPointsNoneEmptyFn := func(result *baseline_master.GetReportResponse) (pass bool, emptyCnt int, pointCnt int) {
		skipIds := sets.NewInt64(task.SkipCheckJobIds...)
		checkerNameSets := sets.NewString()
		for i := range checkJobs {
			if skipIds.Has(checkJobs[i].ID) {
				continue
			}
			checkerNameSets.Insert(checkJobs[i].CheckerName)
		}
		if result != nil && result.Data != nil && len(result.Data) > 0 {
			lastResult = result
			report := result.Data[0]
			if report.ReportMetadata.BaselineName != request.BaselineName {
				return false, len(checkJobs), 0
			}
			for i := range report.CheckItems {
				checkItem := report.CheckItems[i]
				if checkerNameSets.Has(checkItem.Name) {
					checkerNameSets.Delete(checkItem.Name)
				}
				if len(checkItem.Points) > 0 {
					pointCnt++
				} else {
					emptyCnt++
				}
			}
			// 检查是否已经获取到所有的检查项
			if len(report.CheckItems) == pointCnt && len(checkerNameSets) == 0 {
				pass = true
				return
			}
		}
		return
	}
	reportService, err := helper.NewReportServiceFromCluster(ctx, s.addonHandler, stdJob.ClusterName)
	if err != nil {
		return nil, fmt.Errorf("failed to get report service: %w", err)
	}
	var gotErr error
	condition := func(ctx context.Context) (bool, error) {
		newReq := baseline_master.NewGetReportRequest(request.BaselineName)
		request.EndTime = newReq.EndTime
		_ = helper.SaveBaselineRequest(ctx, s.db, stdJob, request)
		log.Info("get report from baseline checker service", zap.Any("request", request))

		if result, err := reportService.GetReport(ctx, newReq); err != nil {
			log.Error("failed get report from baseline checker service", zap.Error(gotErr), zap.Any("request", request))
			gotErr = fmt.Errorf("failed get report from baseline-checker service, please check baseline-checker clusterAddon, err: %w", err)
			return false, nil // 继续重试
		} else {
			pass, _, _ := requiredPointsNoneEmptyFn(result)
			if pass {
				gotErr = nil
			}
			return pass, nil
		}
	}

	conditionErr := wait.ExponentialBackoffWithContext(ctx, backoff, condition)
	switch {
	case gotErr == nil && conditionErr == nil:
		log.Info("PollGetReport get report success from baseline checker service",
			zap.Any("elapsed", time.Since(start)/time.Second*time.Second), zap.Any("request", request))
		return lastResult, nil
	case wait.Interrupted(conditionErr):
		pass, emptyCnt, pointCnt := requiredPointsNoneEmptyFn(lastResult)
		if lastResult != nil && gotErr == nil && pass && emptyCnt == 0 {
			return lastResult, nil
		}
		if lastResult != nil && gotErr == nil && pointCnt > 0 {
			return lastResult, nil
		}
		elapsed := time.Since(start)
		log.Error("PollGetReport get report timeout from baseline checker service",
			zap.Any("elapsed", elapsed/time.Second*time.Second), zap.Any("request", request), zap.Error(gotErr))
		errMsg := fmt.Sprintf("report data is empty after %v ", elapsed/time.Second*time.Second)
		if !pass && emptyCnt > 0 {
			errMsg = fmt.Sprintf("report data has %v checkers have empty points, after %v", emptyCnt, elapsed/time.Second*time.Second)
		}
		if gotErr != nil {
			errMsg += ", err: " + gotErr.Error()
		}
		return lastResult, goerrors.New(errMsg)
	case gotErr != nil:
		return lastResult, fmt.Errorf("failed to fetch report: %w", err)
	}
	return lastResult, nil
}

// EnsureMonitorsRunning 确保 monitor 正在运行
func (s *strategyJobHandler) EnsureMonitorsRunning(ctx context.Context, strategy caas.BaselineStrategy, task *models.StandardJobTask) ([]string, []models.MonitorStatusResult, error) {
	strategyId := strategy.ID
	standardId := task.Job.StandardID
	checkJobs := task.CheckJobs

	start := time.Now()

	var clusterName string
	if len(checkJobs) > 0 {
		clusterName = checkJobs[0].ClusterName
	} else {
		return nil, nil, nil
	}
	ensureMonitorNames := lo.Map(checkJobs, func(item *caas.BaselineStandardRuleJob, _ int) string {
		return item.MonitorName
	})

	steps := 20
	stepCnt := 0
	// 2. 确保 monitor 正在运行
	runningMonitors := sets.NewString()
	abnormalMonitors := []models.MonitorStatusResult{}
	ensureFn := func(ctx context.Context) (bool, error) {
		stepCnt++
		s.log.Info("ensuring monitors running", zap.Int64("standard-job-id", task.Job.ID),
			zap.String("cluster", clusterName), zap.Int("retry", stepCnt), zap.Int("steps", steps),
			zap.Any("monitors", ensureMonitorNames),
		)
		ready, runnings, abnormals, err := s.monitorAdapter.EnsureMonitorsRunning(ctx, &models.MonitorParam{
			ClusterName:        clusterName,
			StrategyID:         strategyId,
			StandardID:         standardId,
			EnsureMonitorNames: ensureMonitorNames,
		})
		abnormalMonitors = abnormals
		for _, running := range runnings {
			runningMonitors.Insert(running.Name)
		}
		if err != nil {
			return false, err
		}
		if !ready {
			s.log.Info("ensure monitors running failed", zap.Int64("standard-job-id", task.Job.ID),
				zap.String("cluster", clusterName), zap.Int("retry", stepCnt), zap.Int("steps", steps), zap.Error(err))
			return false, nil
		}
		return true, nil
	}
	// 10-15 分钟
	backoff := wait.Backoff{
		Duration: 5 * time.Second,
		Factor:   1.3,
		Jitter:   0.1,
		Steps:    steps,
		Cap:      120 * time.Second,
	}
	// 确保 monitor 正在运行
	// only record the error when the monitor is not running
	if err := wait.ExponentialBackoffWithContext(ctx, backoff, ensureFn); err != nil {
		switch {
		case goerrors.Is(err, wait.ErrorInterrupted(err)):
			return runningMonitors.List(), abnormalMonitors, fmt.Errorf("failed to ensure monitors running, error: %w", err)
		case goerrors.Is(err, context.DeadlineExceeded):
			s.log.Sugar().Errorf("failed to ensure monitors running after %v seconds, timeout, error: %w", time.Since(start).Seconds(), err)
			return runningMonitors.List(), abnormalMonitors, fmt.Errorf("failed to ensure monitors running, error: %w", err)
		default:
			return runningMonitors.List(), abnormalMonitors, fmt.Errorf("failed to ensure monitors running, error: %w", err)
		}
	}
	return runningMonitors.List(), abnormalMonitors, nil
}

// RunStandardJob 执行标准检查
func (s *strategyJobHandler) RunStandardJob(ctx context.Context, strategy *caas.BaselineStrategy,
	jobTask *models.StandardJobTask) error {

	timeoutDuration := s.standardJobTimeout
	// 设置超时时间为 30 分钟
	ctx, cancel := context.WithTimeout(ctx, timeoutDuration)
	defer cancel() // 确保释放资源

	log := s.log.With(zap.Int64("strategy-id", strategy.ID)).
		With(zap.String("cluster-name", jobTask.Job.ClusterName)).
		With(zap.Int64("standard-id", jobTask.Job.StandardID))

	// 处理错误和超时
	defer func() {
		if re := recover(); re != nil {
			errorMessage := fmt.Sprintf("Panic: %v\nStack Trace:\n%s", re, string(helper.GetDebugStack()))
			s.log.Error("RunStandardJob recover", zap.Any("error", re))
			_ = helper.UpdateStandardJobStatusWithPassedAndReasonAndMessage(ctx, s.db, jobTask.Job,
				models.JobStatusCompleted, lo.ToPtr(false), lo.ToPtr(models.JobReasonPanicError), errorMessage)
		}
		if err := NewUpdateStandardJobStatusStep(s).Execute(ctx, *strategy, jobTask); err != nil {
			log.Error("RunStandardJob update job status failed", zap.Error(err))
		}
	}()

	// 创建步骤注册表并执行所有步骤
	registry := NewStandardJobStepRegistry(s)
	if err := registry.ExecuteSteps(ctx, *strategy, jobTask); err != nil {
		log.Error("execute standard job steps failed", zap.Error(err))
		return err
	}

	return nil
}

// getRunStrategyJobLockKey ...
func getRunStrategyJobLockKey(strategyId int64) string {
	locKey := fmt.Sprintf("%s/%d", baselineExecuteStrategyTaskJobLock, strategyId)
	return locKey
}

// RunStrategyJob 执行策略检查
func (s *strategyJobHandler) RunStrategyJob(ctx context.Context, jobTask *models.StrategyJobTask) error {
	log := s.log.Named("strategy-job-handler").
		With(zap.Int64("strategy-job-id", jobTask.Job.ID)).
		With(zap.Int64("strategy-id", jobTask.Record.ID))
	defer jobTask.Lock.Release(ctx)

	// 创建一个新的context，用于监听系统退出信号
	ctx, cancel := context.WithTimeout(ctx, s.strategyJobTimeout)
	defer cancel()

	// 处理错误和超时
	defer func() {
		if re := recover(); re != nil {
			s.log.Error("RunStrategyJob recover", zap.Any("error", re))
			errorMessage := fmt.Sprintf("Panic: %v\nStack Trace:\n%s", re, helper.GetDebugStack())
			_ = helper.UpdateStrategyJobStatusWithPassedAndReasonWithMessage(ctx, s.db, jobTask.Job,
				models.JobStatusCompleted, lo.ToPtr(false), lo.ToPtr(models.JobReasonPanicError), errorMessage)
		}
		if err := NewSummaryStrategyStatusStep(s).Execute(ctx, jobTask); err != nil {
			log.Error("RunStrategyJob update job status failed", zap.Error(err))
		}
	}()
	// 创建步骤注册表并执行所有步骤
	registry := NewStrategyJobStepRegistry(s)
	if err := registry.ExecuteSteps(ctx, jobTask); err != nil {
		log.Error("execute strategy job steps failed", zap.Error(err))
		return err
	}

	return nil
}

// getStrategyByID ...
func (s *strategyJobHandler) getStrategyByID(ctx context.Context, strategyId int64) (*caas.BaselineStrategy, error) {
	strategy, err := helper.GetStrategyByID(ctx, s.db, strategyId)
	if err != nil {
		return nil, fmt.Errorf("failed get strategy by id %d, err: %w", strategyId, err)
	}
	return strategy, nil
}

// getStrategyClusterStandardIdsByIdAndClusters 获取策略关联的集群和标准
func (s *strategyJobHandler) getStrategyClusterStandardIdsByIdAndClusters(ctx context.Context,
	strategyId int64, clusterNames []string) (map[string][]caas.BaselineStandard, []int64, error) {
	return helper.GetStrategyClusterStandardsByIdAndClusters(ctx, s.db, strategyId, clusterNames)
}

// getStandardCheckListByStandardIds get standard check list
func (s *strategyJobHandler) getStandardCheckListByStandardIds(ctx context.Context, standardIds []int64) (map[int64][]models.StandardCheckItem, error) {
	return helper.GetStandardCheckListByStandardIds(ctx, s.db, standardIds)
}
