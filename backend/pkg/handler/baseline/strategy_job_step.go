package baseline

import (
	"context"
	goerrors "errors"
	"fmt"
	"sync"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline/helper"
	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
	"k8s.io/apimachinery/pkg/util/sets"
)

const (
	// 步骤名称常量
	StepStrategyInitJob       = "StrategyInitJob"
	StepRunStandardJobs       = "RunStandardJobs"
	StepSummaryStrategyStatus = "SummaryStrategyStatus"
)

// StrategyJobStep 定义策略作业步骤接口
type StrategyJobStep interface {
	// Execute 执行步骤
	Execute(ctx context.Context, task *models.StrategyJobTask) error
	// Name 获取步骤名称
	Name() string
}

// StrategyJobStepRegistry 步骤注册表
type StrategyJobStepRegistry struct {
	steps []StrategyJobStep
}

func NewStrategyJobStepRegistry(handler *strategyJobHandler) *StrategyJobStepRegistry {
	registry := &StrategyJobStepRegistry{}
	// 按顺序注册所有步骤
	registry.RegisterStep(NewStrategyInitJobStep(handler))
	registry.RegisterStep(NewRunStandardJobsStep(handler))
	registry.RegisterStep(NewSummaryStrategyStatusStep(handler))
	return registry
}

func (r *StrategyJobStepRegistry) RegisterStep(step StrategyJobStep) {
	r.steps = append(r.steps, step)
}

func (r *StrategyJobStepRegistry) ExecuteSteps(ctx context.Context, task *models.StrategyJobTask) error {
	for _, step := range r.steps {
		if err := step.Execute(ctx, task); err != nil {
			return fmt.Errorf("step %s failed: %w", step.Name(), err)
		}
	}
	return nil
}

// BaseStrategyStep 基础步骤实现
type BaseStrategyStep struct {
	handler *strategyJobHandler
	log     *zap.Logger
}

// updateJobCheckStatus 更新任务状态
func (s *BaseStrategyStep) updateJobCheckStatus(ctx context.Context, task *models.StrategyJobTask, reason string, stepErr error) error {
	var allStandardJobs []*caas.BaselineStandardJob
	for i := range task.StandardJobs {
		allStandardJobs = append(allStandardJobs, task.StandardJobs[i].Job)
	}
	if goerrors.Is(ctx.Err(), context.DeadlineExceeded) {
		timeoutMsg := fmt.Sprintf("execute check job time out after %s", s.handler.standardJobTimeout.String())
		if task.Job.Status != string(models.JobStatusCompleted) {
			_ = helper.UpdateStrategyJobStatusWithPassedAndReasonWithMessage(ctx, s.handler.db, task.Job,
				models.JobStatusCompleted, lo.ToPtr(false), lo.ToPtr(models.JobReasonCheckTimeout), timeoutMsg)
		}
		lo.ForEach(task.StandardJobs, func(item *models.StandardJobTask, index int) {
			if item.Job.Status != string(models.JobStatusCompleted) {
				_ = helper.UpdateStandardJobStatusWithPassedAndReasonAndMessage(ctx, s.handler.db, item.Job,
					models.JobStatusCompleted, lo.ToPtr(false), lo.ToPtr(models.JobReasonCheckTimeout), timeoutMsg)
			}
			lo.ForEach(item.CheckJobs, func(checkJob *caas.BaselineStandardRuleJob, index int) {
				if checkJob.Status != string(models.JobStatusCompleted) {
					_ = helper.UpdateCheckJobStatusWithPassedAndReasonWithMessage(ctx, s.handler.db, checkJob,
						models.JobStatusCompleted, lo.ToPtr(false), lo.ToPtr(models.JobReasonCheckTimeout), timeoutMsg)
				}
			})
		})

	} else if stepErr != nil {
		if task.Job.Status != string(models.JobStatusCompleted) {
			_ = helper.UpdateStrategyJobStatusWithPassedAndReasonWithMessage(ctx, s.handler.db, task.Job,
				models.JobStatusCompleted,
				lo.ToPtr(false),
				lo.ToPtr(reason),
				stepErr.Error())
		}
	}

	lo.ForEach(task.StandardJobs, func(item *models.StandardJobTask, index int) {
		if item.Job.Status != string(models.JobStatusCompleted) {
			if !task.Job.Passed && item.Job.Reason == "" {
				_ = helper.UpdateStandardJobStatusWithPassedAndReasonAndMessage(ctx, s.handler.db, item.Job,
					models.JobStatusCompleted, lo.ToPtr(false), lo.ToPtr(task.Job.Reason), task.Job.Message)
			}

		}
		lo.ForEach(item.CheckJobs, func(checkJob *caas.BaselineStandardRuleJob, index int) {
			if checkJob.Status != string(models.JobStatusCompleted) {
				if !task.Job.Passed && checkJob.Reason == "" {
					_ = helper.UpdateCheckJobStatusWithPassedAndReasonWithMessage(ctx, s.handler.db, checkJob,
						models.JobStatusCompleted, lo.ToPtr(false), lo.ToPtr(task.Job.Reason), task.Job.Message)
				}

			}
		})
	})

	if checkErr := helper.UpdateStrategyJobCheckStatus(ctx, task.Job, allStandardJobs, task.ClusterStandardItems); checkErr != nil {
		s.log.Error("RunStrategyJob update strategy job check status failed", zap.Error(checkErr))
		_ = helper.UpdateStrategyJobStatusWithPassedAndReasonWithMessage(ctx, s.handler.db, task.Job,
			models.JobStatusCompleted, lo.ToPtr(false), lo.ToPtr(models.JobReasonUnknown), checkErr.Error())
		return checkErr
	}

	return helper.UpdateStrategyJobStatusWithPassedAndReasonWithMessage(ctx, s.handler.db, task.Job,
		models.JobStatusCompleted, lo.ToPtr(task.Job.Passed), lo.ToPtr(task.Job.Reason), task.Job.Message)
}

// handleStepError 处理步骤执行错误
func (s *BaseStrategyStep) handleStepError(ctx context.Context, task *models.StrategyJobTask, stepName string, err *error) {
	if r := recover(); r != nil {
		s.log.Error(fmt.Sprintf("%s step panic", stepName), zap.Any("error", r))
		panicErr := fmt.Errorf("step %s panic: %v\nStack: %s", stepName, r, string(helper.GetDebugStack()))
		*err = panicErr
	}
	if *err != nil {
		// 更新策略任务状态
		var reason string
		switch stepName {
		case StepStrategyInitJob:
			reason = models.JobReasonInitJobError
		case StepRunStandardJobs:
			reason = models.JobReasonRunStandardJobsError
		default:
			reason = models.JobReasonUnknown
		}
		// 如果不是最后的状态更新步骤，则更新任务状态
		if stepName != StepSummaryStrategyStatus {
			if err := s.updateJobCheckStatus(ctx, task, reason, *err); err != nil {
				s.log.Debug("update job check status failed", zap.Error(err),
					zap.String("reason", reason),
					zap.String("message", err.Error()),
					zap.Any("strategy-id", task.Job.StrategyID),
					zap.Any("strategy-job-id", task.Job.ID), zap.Any("step", stepName))
			}
		}
	}
}

// StrategyInitJobStep 初始化策略任务步骤
type StrategyInitJobStep struct {
	BaseStrategyStep
}

func NewStrategyInitJobStep(handler *strategyJobHandler) StrategyJobStep {
	return &StrategyInitJobStep{
		BaseStrategyStep: BaseStrategyStep{
			handler: handler,
			log:     handler.log.Named(StepStrategyInitJob),
		},
	}
}

func (s *StrategyInitJobStep) Name() string {
	return StepStrategyInitJob
}

func (s *StrategyInitJobStep) Execute(ctx context.Context, task *models.StrategyJobTask) (err error) {
	s.log.Info("init strategy job step started", zap.Any("strategy-job-id", task.Job.ID))
	defer s.handleStepError(ctx, task, s.Name(), &err)
	if updateErr := helper.UpdateStrategyJobStatusWithPassedAndReasonWithMessage(ctx, s.handler.db, task.Job,
		models.JobStatusRunning, nil, nil, ""); updateErr != nil {
		s.log.Error("update strategy job status failed", zap.Error(updateErr))
		err = updateErr
		return updateErr
	}
	s.log.Info("init strategy job step completed", zap.Any("strategy-job-id", task.Job.ID))
	return
}

// RunStandardJobsStep 执行标准作业步骤
type RunStandardJobsStep struct {
	BaseStrategyStep
}

func NewRunStandardJobsStep(handler *strategyJobHandler) StrategyJobStep {
	return &RunStandardJobsStep{
		BaseStrategyStep: BaseStrategyStep{
			handler: handler,
			log:     handler.log.Named(StepRunStandardJobs),
		},
	}
}

func (s *RunStandardJobsStep) Name() string {
	return StepRunStandardJobs
}

func (s *RunStandardJobsStep) Execute(ctx context.Context, task *models.StrategyJobTask) (err error) {
	s.log.Info("run standard jobs step started", zap.Any("strategy-job-id", task.Job.ID))
	defer s.handleStepError(ctx, task, s.Name(), &err)
	var wg sync.WaitGroup
	errChan := make(chan error, len(task.StandardJobs))
	skipStandardJobIds := sets.NewInt64(task.SkipStandardJobIds...)
	for i := range task.StandardJobs {
		if skipStandardJobIds.Has(task.StandardJobs[i].Job.StandardID) {
			continue
		}
		wg.Add(1)
		job := task.StandardJobs[i]
		s.handler.strategyJobTaskPool.Submit(ctx, func(ctx context.Context) error {
			defer wg.Done()
			if taskErr := s.handler.RunStandardJob(ctx, task.Record, job); taskErr != nil {
				select {
				case errChan <- fmt.Errorf("job %d failed: %w", task.Record.ID, taskErr):
				case <-ctx.Done():
					s.log.Warn("RunStrategyJob failed to send run standard job error, context canceled")
				}
				return taskErr
			}
			return nil
		})
	}

	go func() {
		wg.Wait()
		close(errChan)
	}()

	errs := lo.ChannelToSlice(errChan)
	if len(errs) > 0 {
		newErr := fmt.Errorf("run strategy job failed: %v", errors.Join(errs...))
		if updateErr := helper.UpdateStrategyJobStatusWithPassedAndReasonWithMessage(ctx, s.handler.db, task.Job,
			models.JobStatusCompleted,
			lo.ToPtr(false),
			lo.ToPtr(models.JobReasonCheckStandardError),
			newErr.Error()); updateErr != nil {
			err = updateErr
			return updateErr
		}
		err = newErr
		return newErr
	}
	s.log.Info("run standard jobs step completed", zap.Any("strategy-job-id", task.Job.ID))
	return nil
}

// SummaryStrategyStatusStep 更新策略作业状态步骤
type SummaryStrategyStatusStep struct {
	BaseStrategyStep
}

func NewSummaryStrategyStatusStep(handler *strategyJobHandler) StrategyJobStep {
	return &SummaryStrategyStatusStep{
		BaseStrategyStep: BaseStrategyStep{
			handler: handler,
			log:     handler.log.Named(StepSummaryStrategyStatus),
		},
	}
}

func (s *SummaryStrategyStatusStep) Name() string {
	return StepSummaryStrategyStatus
}

func (s *SummaryStrategyStatusStep) Execute(ctx context.Context, task *models.StrategyJobTask) (err error) {
	s.log.Info("summary strategy status step started", zap.Any("strategy-job-id", task.Job.ID))
	defer s.handleStepError(ctx, task, s.Name(), &err)
	// 如果任务已经完成，直接返回
	if task.Completed {
		return nil
	}
	if updateErr := s.updateJobCheckStatus(ctx, task, "", nil); updateErr != nil {
		err = updateErr
		return updateErr
	}

	_ = helper.UpdateStrategyJobStatusWithPassedAndReasonWithMessage(ctx, s.handler.db, task.Job,
		models.JobStatusCompleted, nil, nil, "")
	task.Completed = true
	s.log.Info("summary strategy status step completed", zap.Any("strategy-job-id", task.Job.ID))
	return nil
}
