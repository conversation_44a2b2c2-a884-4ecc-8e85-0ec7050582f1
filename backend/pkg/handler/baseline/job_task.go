package baseline

import (
	"context"
	goerrors "errors"
	"time"

	"github.com/panjf2000/ants/v2"
	"go.uber.org/zap"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"k8s.io/klog/v2"
)

type JobFunc func(ctx context.Context) error

// NewJobTaskPool 创建任务池
func NewJobTaskPool(poolSize int, timeout time.Duration) *JobTaskPool {
	pool, err := ants.NewPool(poolSize,
		ants.WithMaxBlockingTasks(poolSize/2),
		ants.WithExpiryDuration(1*time.Minute),
		ants.WithNonblocking(false),
	)
	if err != nil {
		klog.Fatal("init baseline job task pool error")
	}
	return &JobTaskPool{
		log:     logger.GetLogger().Named("baseline-job-task-pool"),
		pool:    pool,
		timeout: timeout,
	}

}

type JobTaskPool struct {
	log     *zap.Logger
	pool    *ants.Pool
	timeout time.Duration
}

// Release 释放任务池资源
func (p *JobTaskPool) Release() {
	if p.pool != nil {
		p.log.Info("releasing job task pool",
			zap.Int("running_workers", p.pool.Running()),
			zap.Int("waiting_tasks", p.pool.Waiting()))
		// 等待所有任务完成并释放资源
		p.pool.Release()
	}
}

// Submit 提交任务
func (p *JobTaskPool) Submit(ctx context.Context, jobFuncs ...JobFunc) {
	for _, jobFunc := range jobFuncs {
		if err := p.pool.Submit(func() {
			// 创建带超时的子上下文
			ctx, cancel := context.WithTimeout(ctx, p.timeout)
			defer cancel()
			select {
			case <-ctx.Done():
				// 如果上下文超时或被取消，记录错误
				if ctx.Err() != nil {
					if goerrors.Is(ctx.Err(), context.DeadlineExceeded) {
						p.log.Error("job task timeout", zap.Error(ctx.Err()))
					} else {
						p.log.Error("job task error", zap.Error(ctx.Err()))
					}
				}
				return // 上下文被取消或超时时直接返回
			default:
				// 如果上下文未超时，执行任务
				if err := jobFunc(ctx); err != nil {
					p.log.Error("submit job task error", zap.Error(err))
				}
			}
		}); err != nil {
			p.log.Error("submit job task error", zap.Error(err))
			return
		}
	}
}
