package baseline

import (
	"context"
	goerrors "errors"
	"fmt"
	"strings"
	"time"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline/helper"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	pagemodels "harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
	"k8s.io/apimachinery/pkg/util/sets"
)

const (
	standardLoggerName = "baseline/standard"
)

var _ StandardInterface = (*standardHandler)(nil)

func NewStandardHandler(db *gorm.DB) StandardInterface {
	return &standardHandler{
		db:  db,
		log: logger.GetLogger().Named(standardLoggerName),
	}
}

type standardHandler struct {
	db  *gorm.DB
	log *zap.Logger
}

// GetBaselineNameExisted implements StandardInterface.
func (s *standardHandler) GetBaselineNameExisted(ctx context.Context, req *models.GetBaselineNameExistedRequest) (*models.GetBaselineNameExistedResponse, error) {
	s.log.Info("get baseline name existed", zap.Any("req", req))
	var count int64
	err := s.db.WithContext(ctx).Model(&caas.BaselineStandard{}).Where("BINARY name = ?", req.Name).Count(&count).Error
	if err != nil {
		s.log.Error("failed to get baseline name existed", zap.Error(err))
		return nil, fmt.Errorf("failed to get baseline name existed: %w", err)
	}

	return &models.GetBaselineNameExistedResponse{
		Existed: count > 0,
	}, nil
}

func (s *standardHandler) QueryBaselines(ctx context.Context, req *models.QueryBaselinesRequest) (*models.QueryBaselinesResponse, error) {
	s.log.Info("query baselines", zap.Any("req", req))
	// STEP 1. 查询数据库
	// STEP 1.1 查询基线标准分类
	var baselineGroups []caas.BaselineStandardGroup
	tx := s.db.WithContext(ctx).Model(&caas.BaselineStandardGroup{})
	if req.CategoryBuiltin != nil {
		tx = tx.Where("builtin = ?", lo.Ternary(*req.CategoryBuiltin, 1, 0))
	}
	if req.CategoryID != nil {
		tx = tx.Where("id = ?", *req.CategoryID)
	}
	err := tx.Find(&baselineGroups).Error
	if err != nil {
		s.log.Error("query baseline groups failed", zap.Error(err))
		return nil, fmt.Errorf("query baseline groups failed: %w", err)
	}
	var baselineGroupIDs []int64
	baselineGroupMap := lo.SliceToMap(baselineGroups, func(group caas.BaselineStandardGroup) (int64, caas.BaselineStandardGroup) {
		baselineGroupIDs = append(baselineGroupIDs, group.ID)
		return group.ID, group
	})
	if len(baselineGroupIDs) == 0 {
		return &models.QueryBaselinesResponse{
			PageableResponse: pagemodels.PageableResponse[*models.StandardItem]{},
		}, nil
	}
	// STEP 1.2 查询基线标准
	var baselines []caas.BaselineStandard
	bTx := s.db.WithContext(ctx).Model(&caas.BaselineStandard{}).Where("group_id in ?", baselineGroupIDs)
	if req.Builtin != nil {
		bTx = bTx.Where("builtin = ?", lo.Ternary(*req.Builtin, 1, 0))
	}
	err = bTx.Find(&baselines).Error
	if err != nil {
		s.log.Error("query baselines failed", zap.Error(err))
		return nil, fmt.Errorf("query baselines failed: %w", err)
	}
	// STEP 2. 构造响应
	responseItems := lo.Map(baselines, func(baseline caas.BaselineStandard, _ int) *models.StandardItem {
		group := baselineGroupMap[baseline.GroupID]
		return &models.StandardItem{
			ID:              baseline.ID,
			Name:            baseline.Name,
			Builtin:         baseline.Builtin,
			CategoryID:      group.ID,
			CategoryName:    group.Name,
			CategoryBuiltin: group.Builtin,
			Description:     baseline.Description,
			CreateTime:      baseline.CreateTime.Format(time.DateTime),
			UpdateTime:      baseline.UpdateTime.Format(time.DateTime),
		}
	})
	result, err := req.Filter.FilterResult(responseItems)
	if err != nil {
		s.log.Error("filter baselines failed", zap.Error(err))
		return nil, fmt.Errorf("filter baselines failed: %w", err)
	}
	return &models.QueryBaselinesResponse{
		PageableResponse: pagemodels.PageableResponse[*models.StandardItem]{
			Items:      result.Items,
			TotalCount: result.TotalCount,
		},
	}, nil
}

func (s *standardHandler) GetBaselineDetails(ctx context.Context, req *models.GetBaselineDetailsRequest) (*models.GetBaselineDetailsResponse, error) {
	s.log.Info("get baseline details", zap.Any("req", req))

	// STEP 1. 查询数据库
	// STEP 1.1 查询基线标准
	var baselineStandard caas.BaselineStandard
	if err := s.db.WithContext(ctx).Where("id = ?", req.ID).First(&baselineStandard).Error; err != nil {
		s.log.Error("query baseline standards failed", zap.Error(err))
		return nil, fmt.Errorf("query baseline standards failed: %w", err)
	}

	// STEP 1.2 查询基线标准分类
	var baselineGroup caas.BaselineStandardGroup
	if err := s.db.WithContext(ctx).Where("id = ?", baselineStandard.GroupID).First(&baselineGroup).Error; err != nil {
		s.log.Error("query baseline group failed", zap.Error(err))
		return nil, fmt.Errorf("query baseline group failed: %w", err)
	}

	// STEP 1.3 基线标准与检查项关联表
	var baselineStandardRules []caas.BaselineStandardRule
	if err := s.db.WithContext(ctx).Where("standard_id = ?", req.ID).Find(&baselineStandardRules).Error; err != nil {
		s.log.Error("query baseline standard rules failed", zap.Error(err))
		return nil, fmt.Errorf("query baseline standard rules failed: %w", err)
	}

	//// 构造关联表映射
	//baselinesStandardRuleMap := lo.SliceToMap(baselineStandardRules, func(item caas.BaselineStandardRule) (int64, caas.BaselineStandardRule) {
	//	return item.RuleID, item
	//})

	// STEP 1.4 检查项
	ruleIds := lo.Map(baselineStandardRules, func(item caas.BaselineStandardRule, _ int) int64 {
		return item.RuleID
	})

	var baselineRules []caas.BaselineRule
	if err := s.db.WithContext(ctx).Where("id IN (?)", ruleIds).Find(&baselineRules).Error; err != nil {
		s.log.Error("query baseline rules failed", zap.Error(err))
		return nil, fmt.Errorf("query baseline rules failed: %w", err)
	}

	baselineRuleMap := lo.SliceToMap(baselineRules, func(item caas.BaselineRule) (int64, caas.BaselineRule) {
		return item.ID, item
	})

	// STEP 2. 构造检查项
	var checkIds []int64
	var standardCheckIds []int64
	var standardCheckItems []*models.StandardCheckItem
	for _, standardRule := range baselineStandardRules {
		baselineRule, ok := baselineRuleMap[standardRule.RuleID]
		if !ok {
			continue
		}
		checkItem := helper.ConvertBaselineRuleToCheckItem(&baselineRule)
		checkItem.RiskLevel = standardRule.RiskLevel
		value, _ := helper.ConvertBaselineStandardRuleToCheckValue(&standardRule)
		var checkValue models.CheckValueItem
		if value != nil {
			checkValue = *value
		} else {
			if v, _ := helper.ConvertBaselineRuleToCheckRawData(&baselineRule); v != nil {
				checkValue = v.Value
			}
		}
		checkIds = append(checkIds, standardRule.RuleID)
		standardCheckIds = append(standardCheckIds, standardRule.ID)
		standardCheckItems = append(standardCheckItems, &models.StandardCheckItem{
			CheckItem:       *checkItem,
			StandardID:      standardRule.StandardID,
			StandardCheckID: standardRule.ID,
			CheckValue:      checkValue,
			Suggestion:      baselineRule.Suggestion,
		})
	}

	resp := &models.GetBaselineDetailsResponse{
		ID:               baselineStandard.ID,
		Name:             baselineStandard.Name,
		Description:      baselineStandard.Description,
		CategoryID:       baselineGroup.ID,
		CategoryName:     baselineGroup.Name,
		CategoryBuiltin:  baselineGroup.Builtin,
		Builtin:          baselineStandard.Builtin,
		CheckIds:         checkIds,
		StandardCheckIds: standardCheckIds,
		PageableResponse: pagemodels.PageableResponse[*models.StandardCheckItem]{},
		CreateTime:       baselineStandard.CreateTime.Format(time.DateTime),
		UpdateTime:       baselineStandard.UpdateTime.Format(time.DateTime),
	}

	// 如果传递了 parentID 则需要查出当前基线标准被父基线标准选择的检查项
	if req.ParentID != nil {
		selectedInfo := new(models.SelectedCheckInfo)
		var parentStandardRules []caas.BaselineStandardRule
		if err := s.db.WithContext(ctx).Where("standard_id = ?", req.ParentID).Find(&parentStandardRules).Error; err != nil {
			return nil, err
		}
		keyFunc := func(checkId int64, standardCheckId int64) string {
			return fmt.Sprintf("%d/%d", checkId, standardCheckId)
		}
		parentStandardRulesMap := lo.SliceToMap(parentStandardRules, func(item caas.BaselineStandardRule) (string, caas.BaselineStandardRule) {
			return keyFunc(item.RuleID, item.RefID), item
		})
		var newStandardCheckItems []*models.StandardCheckItem
		for i := range standardCheckItems {
			item := standardCheckItems[i]
			if rule, ok := parentStandardRulesMap[keyFunc(item.ID, item.StandardCheckID)]; ok {
				checkValue, _ := helper.ConvertBaselineStandardRuleToCheckValue(&rule)
				selectedInfo.CheckIds = append(selectedInfo.CheckIds, rule.ID)
				selectedInfo.StandardCheckIds = append(selectedInfo.StandardCheckIds, item.StandardCheckID)
				selectedInfo.Checkers = append(selectedInfo.Checkers, models.SimpleBaselineChecker{
					CheckID:         rule.ID,
					StandardCheckID: item.ID,
				})
				item.CheckValue = *checkValue
				item.RiskLevel = rule.RiskLevel
			}
			newStandardCheckItems = append(newStandardCheckItems, item)

		}
		standardCheckItems = newStandardCheckItems
		if len(selectedInfo.CheckIds) > 0 {
			resp.SelectedCheckInfo = selectedInfo
		}
	}

	// STEP 3. 构造返回值
	filteredResult, err := req.CheckFilter.FilterResult(standardCheckItems)
	if err != nil {
		s.log.Error("filter result failed", zap.Error(err))
		return nil, fmt.Errorf("filter result failed: %w", err)
	}

	resp.PageableResponse = pagemodels.PageableResponse[*models.StandardCheckItem]{
		Items:      filteredResult.Items,
		TotalCount: filteredResult.TotalCount,
	}

	return resp, nil
}

func (s *standardHandler) GetBaselineCheckers(ctx context.Context, req *models.GetBaselineCheckersRequest) (*models.GetBaselineCheckersResponse, error) {
	s.log.Info("get baseline checkers", zap.Any("request", req))
	var (
		dbTx = s.db.WithContext(ctx)
		sTx  = dbTx.Model(&caas.BaselineStandard{})
		scTx = dbTx.Model(&caas.BaselineStandardRule{})
		cTx  = dbTx.Model(&caas.BaselineRule{})
	)

	// 查询 BaselineStandard
	if err := sTx.Where("id = ?", req.ID).First(&caas.BaselineStandard{}).Error; err != nil {
		s.log.Error("query checker failed", zap.Error(err))
		return nil, fmt.Errorf("query checker failed for standard ID %d: %w", req.ID, err)
	}

	// 查询 BaselineStandardRule
	var baselineStandardRules []caas.BaselineStandardRule
	query := scTx.Where("standard_id = ?", req.ID)
	if len(req.CheckIds) > 0 {
		query = query.Where("rule_id IN ?", req.CheckIds)
	}
	if err := query.Find(&baselineStandardRules).Error; err != nil {
		s.log.Error("query baseline standard rules failed", zap.Error(err))
		return nil, fmt.Errorf("query baseline standard rules failed: %w", err)
	}

	// 提取 Rule IDs
	ruleIds := lo.Map(baselineStandardRules, func(item caas.BaselineStandardRule, index int) int64 {
		return item.RuleID
	})
	if len(ruleIds) == 0 {
		return &models.GetBaselineCheckersResponse{}, nil
	}

	// 查询 BaselineRule
	var baselineRules []caas.BaselineRule
	if err := cTx.Where("id IN (?)", ruleIds).Find(&baselineRules).Error; err != nil {
		s.log.Error("query baseline rules failed", zap.Error(err))
		return nil, fmt.Errorf("query baseline rules failed: %w", err)
	}

	// 构造返回值
	var standardCheckItems []*models.StandardCheckItem
	ruleIdMap := lo.SliceToMap(baselineStandardRules, func(item caas.BaselineStandardRule) (int64, caas.BaselineStandardRule) {
		return item.RuleID, item
	})
	for _, baselineRule := range baselineRules {
		checkItem := helper.ConvertBaselineRuleToCheckItem(&baselineRule)
		checkValue := &models.CheckValueItem{}
		stdRule, ok := ruleIdMap[baselineRule.ID]
		if ok {
			checkItem.RiskLevel = ruleIdMap[baselineRule.ID].RiskLevel
			checkValue, _ = helper.ConvertBaselineStandardRuleToCheckValue(&stdRule)
		}

		standardCheckItems = append(standardCheckItems, &models.StandardCheckItem{
			CheckItem:       *checkItem,
			StandardID:      stdRule.StandardID,
			StandardCheckID: stdRule.ID,
			CheckValue:      *checkValue,
			Suggestion:      baselineRule.Suggestion,
		})
	}

	// 返回分页数据
	return &models.GetBaselineCheckersResponse{
		PageableResponse: pagemodels.PageableResponse[*models.StandardCheckItem]{
			Items:      standardCheckItems,
			TotalCount: len(standardCheckItems),
		},
	}, nil
}

// validateRuleIdsExists
func (s *standardHandler) validateRuleIdsExists(ctx context.Context, ids []int64) error {
	if len(ids) == 0 {
		return nil
	}
	idSet := sets.New(ids...)
	var rules []caas.BaselineRule
	if err := s.db.WithContext(ctx).Model(&caas.BaselineRule{}).Where("id in (?)", ids).Find(&rules).Error; err != nil {
		s.log.Error("query baseline rules failed", zap.Error(err))
		return fmt.Errorf("query baseline rules failed: %w", err)
	}
	var notExistedIds []int64
	for _, rule := range rules {
		if !idSet.Has(rule.ID) {
			notExistedIds = append(notExistedIds, rule.ID)
		}
	}
	if len(notExistedIds) > 0 {
		return fmt.Errorf("not found baseline rules: %v", notExistedIds)
	}
	return nil
}

func (s *standardHandler) validateCategoryIdExist(ctx context.Context, id int64) error {
	var baselineStandardGroup caas.BaselineStandardGroup
	err := s.db.WithContext(ctx).Model(&caas.BaselineStandardGroup{}).Where("id = ?", id).First(&baselineStandardGroup).Error
	if err != nil {
		s.log.Error("query baseline category failed", zap.Error(err))
		return fmt.Errorf("query baseline category failed: %w", err)
	}
	return nil
}

func (s *standardHandler) CreateBaseline(ctx context.Context, req *models.CreateBaselineRequest) (*models.CreateBaselineResponse, error) {
	s.log.Info("create baseline", zap.Any("request", req))
	// STEP 1.1 是否重复
	var exitsBaselineStandard int64
	err := s.db.WithContext(ctx).Model(&caas.BaselineStandard{}).Where("name = ?", req.Name).Count(&exitsBaselineStandard).Error
	if err != nil {
		s.log.Error("query baseline standard failed", zap.Error(err))
		return nil, fmt.Errorf("query baseline standard failed: %w", err)
	}
	if exitsBaselineStandard > 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.BaselineStandardNameRepeat, "baseline standard name already exists")
	}
	if req.CategoryID == nil {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, "category id is required")
	}
	if err := s.validateCategoryIdExist(ctx, *req.CategoryID); err != nil {
		return nil, err
	}
	err = s.db.WithContext(ctx).Model(&caas.BaselineStandardGroup{}).Where("id = ?", *req.CategoryID).
		First(&caas.BaselineStandardGroup{}).Error
	if err != nil {
		s.log.Error("query baseline category failed", zap.Error(err))
		return nil, fmt.Errorf("query baseline category failed: %w", err)
	}
	ids := lo.Map(req.Checkers, func(item models.SimpleBaselineChecker, _ int) int64 {
		return item.CheckID
	})
	ids = append(ids, lo.Map(req.StandardCheckers, func(item models.SimpleBaselineChecker, index int) int64 {
		return item.CheckID
	})...)
	if err := s.validateRuleIdsExists(ctx, sets.NewInt64(ids...).List()); err != nil {
		return nil, err
	}
	// STEP 1.2 创建标准
	err = s.db.Transaction(func(tx *gorm.DB) error {
		baselineStandard := &caas.BaselineStandard{
			Name:        req.Name,
			Description: req.Description,
			GroupID:     *req.CategoryID,
		}
		err = tx.WithContext(ctx).Create(baselineStandard).Error
		if err != nil {
			s.log.Error("create baseline standard failed", zap.Error(err))
			return fmt.Errorf("create baseline standard failed: %w", err)
		}
		// create standard rule from another standard rule
		stdRuleIds := lo.Map(req.StandardCheckers, func(item models.SimpleBaselineChecker, index int) int64 {
			return item.StandardCheckID
		})
		if len(stdRuleIds) > 0 {
			var standardRules []caas.BaselineStandardRule
			if err := tx.WithContext(ctx).Model(&caas.BaselineStandardRule{}).
				Where("id in (?)", stdRuleIds).Find(&standardRules).Error; err != nil {
				return err
			}
			stdRuleIdMap := lo.SliceToMap(standardRules, func(item caas.BaselineStandardRule) (int64, caas.BaselineStandardRule) {
				return item.ID, item
			})
			for i := range req.StandardCheckers {
				checker := req.StandardCheckers[i]
				if stdRule, ok := stdRuleIdMap[checker.StandardCheckID]; ok {
					rule := &caas.BaselineRule{}
					if err := tx.WithContext(ctx).Where("id = ?", stdRule.RuleID).First(rule).Error; err != nil {
						return fmt.Errorf("%d baseline rule found failed, err:%w", stdRule.RuleID, err)
					}
					checkRawData, _ := helper.ConvertBaselineRuleToCheckRawData(rule)
					checkValue, _ := helper.ConvertBaselineStandardRuleToCheckValue(&stdRule)
					if checker.RiskLevel == "" {
						checker.RiskLevel = stdRule.RiskLevel
					}
					if checker.CheckValue == nil {
						checker.CheckValue = checkValue
					}
					if checker.CheckValue != nil {
						checkRawData.Value = *checker.CheckValue
						if err := checkRawData.Validate(); err != nil {
							return err
						}
					}
					checker.CheckID = stdRule.RuleID
					saveRule := helper.ConvertSimpleBaselineCheckerToBaselineStandardRule(baselineStandard.ID, &checker)
					err := tx.WithContext(ctx).Create(saveRule).Error
					if err != nil {
						s.log.Error("create baseline standard rule failed", zap.Error(err))
						return fmt.Errorf("create baseline standard rule failed: %w", err)
					}

				}
			}
		}
		// create custom checkers from standard rules
		ruleIds := lo.Map(req.Checkers, func(item models.SimpleBaselineChecker, index int) int64 {
			return item.CheckID
		})
		if len(ruleIds) > 0 {
			var rules []caas.BaselineRule
			if err := tx.WithContext(ctx).Model(&caas.BaselineRule{}).
				Where("id in (?)", ruleIds).Find(&rules).Error; err != nil {
				return err
			}
			ruleIdMap := lo.SliceToMap(rules, func(item caas.BaselineRule) (int64, caas.BaselineRule) {
				return item.ID, caas.BaselineRule{}
			})
			for _, checker := range req.Checkers {
				if rule, ok := ruleIdMap[checker.CheckID]; ok {
					checkRawData, _ := helper.ConvertBaselineRuleToCheckRawData(&rule)
					if checker.RiskLevel == "" {
						checker.RiskLevel = rule.RiskLevel
					}
					if checker.CheckValue == nil {
						checker.CheckValue = &checkRawData.Value
					}
					if checker.CheckValue != nil {
						checkRawData.Value = *checker.CheckValue
						if err := checkRawData.Validate(); err != nil {
							return err
						}
					}
					standardRule := helper.ConvertSimpleBaselineCheckerToBaselineStandardRule(baselineStandard.ID, &checker)
					err = tx.WithContext(ctx).Create(standardRule).Error
					if err != nil {
						s.log.Error("create baseline standard rule failed from custom checkers", zap.Error(err))
						return fmt.Errorf("create baseline standard rule failed from custom checkers : %w", err)
					}
				}
			}
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return &models.CreateBaselineResponse{}, nil
}

func (s *standardHandler) UpdateBaseline(ctx context.Context, req *models.UpdateBaselineRequest) (*models.UpdateBaselineResponse, error) {
	s.log.Info("edit baseline", zap.Any("request", req))
	var existsBaselineStandard int64
	err := s.db.WithContext(ctx).Model(&caas.BaselineStandard{}).
		Where("name = ?", req.Name).
		Where("id != ?", req.ID).
		Count(&existsBaselineStandard).Error
	if err != nil {
		s.log.Error("query baseline standard failed", zap.Error(err))
		return nil, fmt.Errorf("query baseline standard failed: %w", err)
	}
	if existsBaselineStandard > 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.BaselineStandardNameRepeat, "baseline standard name already exists")
	}
	if req.CategoryID == nil {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, "category id is required")
	}
	if err := s.validateCategoryIdExist(ctx, *req.CategoryID); err != nil {
		return nil, err
	}
	ids := lo.Map(req.Checkers, func(item models.SimpleBaselineChecker, _ int) int64 {
		return item.CheckID
	})
	if err := s.validateRuleIdsExists(ctx, ids); err != nil {
		return nil, err
	}

	err = s.db.Transaction(func(tx *gorm.DB) error {
		// 1. 更新基线标准
		baselineStandard := &caas.BaselineStandard{}
		if err := tx.WithContext(ctx).Where("id = ?", req.ID).First(baselineStandard).Error; err != nil {
			return fmt.Errorf("query baseline standard failed: %w", err)
		}
		baselineStandard.Name = req.Name
		baselineStandard.Description = req.Description
		baselineStandard.GroupID = *req.CategoryID
		if err := tx.WithContext(ctx).
			Select("name", "description", "group_id", "update_user", "update_time").
			Save(baselineStandard).Error; err != nil {
			return err
		}

		if len(req.Checkers) == 0 && len(req.StandardCheckers) == 0 {
			return nil
		}

		keyFunc := func(checkId, stdCheckRefId int64) string {
			return fmt.Sprintf("%d/%d", checkId, stdCheckRefId)
		}

		// 2. 获取新提交的 rule_id -> ref_id 映射
		saveStandardCheckIds := make([]int64, 0)
		if len(req.StandardCheckers) > 0 {
			saveStandardCheckIds = lo.Map(req.StandardCheckers, func(item models.SimpleBaselineChecker, _ int) int64 {
				return item.StandardCheckID
			})
		}
		for _, checker := range req.StandardCheckers {
			saveStandardCheckIds = append(saveStandardCheckIds, checker.StandardCheckID)
		}
		var standardRules []caas.BaselineStandardRule
		if err := tx.WithContext(ctx).Model(&caas.BaselineStandardRule{}).
			Where("id in (?)", saveStandardCheckIds).Find(&standardRules).Error; err != nil {
			return err
		}
		stdRuleIdMap := lo.SliceToMap(standardRules, func(item caas.BaselineStandardRule) (int64, caas.BaselineStandardRule) {
			return item.ID, item
		})

		newStandardRuleMap := make(map[string]caas.BaselineStandardRule)
		newCheckerMap := make(map[string]models.SimpleBaselineChecker)
		saveCheckIds := make([]int64, 0)
		for i, checker := range req.Checkers {
			// custom checkers
			key := keyFunc(checker.CheckID, 0)
			newStandardRuleMap[key] =
				*helper.ConvertSimpleBaselineCheckerToBaselineStandardRule(req.ID, &req.Checkers[i])
			newCheckerMap[key] = req.Checkers[i]
			saveCheckIds = append(saveCheckIds, checker.CheckID)
		}
		var rules []caas.BaselineRule
		if err := tx.WithContext(ctx).Model(&caas.BaselineRule{}).
			Where("id in (?)", saveCheckIds).Find(&rules).Error; err != nil {
			return err
		}
		ruleIdMap := lo.SliceToMap(rules, func(item caas.BaselineRule) (int64, caas.BaselineRule) {
			return item.ID, caas.BaselineRule{}
		})

		for i := range req.StandardCheckers {
			checker := req.StandardCheckers[i]
			checker.CheckID = stdRuleIdMap[checker.StandardCheckID].RuleID
			key := keyFunc(checker.CheckID, checker.StandardCheckID)
			newStandardRuleMap[key] =
				*helper.ConvertSimpleBaselineCheckerToBaselineStandardRule(req.ID, &checker)
			newCheckerMap[key] = checker
		}

		// 3. 查询现有的 BaselineStandardRule
		existedStandardRules := make([]caas.BaselineStandardRule, 0)
		err = tx.WithContext(ctx).Model(&caas.BaselineStandardRule{}).
			Where("standard_id = ?", req.ID).
			Find(&existedStandardRules).Error
		if err != nil {
			s.log.Error("query baseline standard rules failed", zap.Error(err))
			return fmt.Errorf("query baseline standard rules failed: %w", err)
		}

		// 4. 构建现有的 rule_id -> ref_id 映射
		existedStandardRuleMap := make(map[string]caas.BaselineStandardRule) // key: "rule_id-ref_id"
		for _, record := range existedStandardRules {
			key := keyFunc(record.RuleID, record.RefID)
			existedStandardRuleMap[key] = record
		}

		// 5. 计算需要删除的 rule_id
		toDelete := make([]caas.BaselineStandardRule, 0)
		for _, record := range existedStandardRules {
			key := keyFunc(record.RuleID, record.RefID)
			if _, exists := newStandardRuleMap[key]; !exists {
				toDelete = append(toDelete, record)
			}
		}

		// 6. 删除不再需要的规则
		if len(toDelete) > 0 {
			// 删除原有的规则
			deleteIDs := make([]int64, 0)
			for _, del := range toDelete {
				if del.ID > 0 {
					deleteIDs = append(deleteIDs, del.ID)
				}
			}
			if len(deleteIDs) > 0 {
				if err = tx.WithContext(ctx).Model(&caas.BaselineStandardRule{}).Where("id IN (?)", deleteIDs).Delete(&caas.BaselineStandardRule{}).Error; err != nil {
					s.log.Error("delete baseline standard rules failed", zap.Error(err))
					return fmt.Errorf("delete baseline standard rules failed: %w", err)
				}

				if err := tx.WithContext(ctx).Model(&caas.BaselineStandardRule{}).Where("ref_id in (?)", deleteIDs).Delete(&caas.BaselineStandardRule{}).Error; err != nil {
					s.log.Error("delete baseline standard ref rules failed", zap.Error(err))
					return fmt.Errorf("delete baseline standard ref rules failed: %w", err)
				}
			}

		}

		// 7. 计算需要插入或更新的规则
		toInsert := make([]caas.BaselineStandardRule, 0)
		toUpdate := make([]caas.BaselineStandardRule, 0)
		for key, newRecord := range newStandardRuleMap {
			if existingRecord, exists := existedStandardRuleMap[key]; exists {
				// 需要更新
				if !helper.DeepEqualBaselineStandardRuleCheckValue(&existingRecord, &newRecord) {
					newRecord.ID = existingRecord.ID
					toUpdate = append(toUpdate, newRecord)
				}
			} else {
				// 需要插入
				toInsert = append(toInsert, newRecord)
			}
		}
		// 8. 批量插入
		if len(toInsert) > 0 {

			for i := range toInsert {
				insertRecord := toInsert[i]
				key := keyFunc(insertRecord.RuleID, insertRecord.RefID)
				if insertRecord.RefID > 0 {
					if stdRule, ok := stdRuleIdMap[insertRecord.RefID]; ok {
						rule := &caas.BaselineRule{}
						if err := tx.WithContext(ctx).Where("id = ?", stdRule.RuleID).First(rule).Error; err != nil {
							return fmt.Errorf("%d baseline rule found failed, err:%w", stdRule.RuleID, err)
						}
						checkRawData, _ := helper.ConvertBaselineRuleToCheckRawData(rule)
						checkValue, _ := helper.ConvertBaselineStandardRuleToCheckValue(&stdRule)
						checker := newCheckerMap[key]
						if checker.RiskLevel == "" {
							checker.RiskLevel = stdRule.RiskLevel
						}
						if checker.CheckValue == nil {
							checker.CheckValue = checkValue
						}
						if checker.CheckValue != nil {
							checkRawData.Value = *checker.CheckValue
							if err := checkRawData.Validate(); err != nil {
								return err
							}
						}
						saveRule := helper.ConvertSimpleBaselineCheckerToBaselineStandardRule(req.ID, &checker)
						err := tx.WithContext(ctx).Create(saveRule).Error
						if err != nil {
							s.log.Error("create baseline standard rule failed", zap.Error(err))
							return fmt.Errorf("create baseline standard rule failed: %w", err)
						}

					}
				} else {
					if rule, ok := ruleIdMap[insertRecord.RuleID]; ok {
						checkRawData, _ := helper.ConvertBaselineRuleToCheckRawData(&rule)
						checker := newCheckerMap[key]
						if checker.RiskLevel == "" {
							checker.RiskLevel = rule.RiskLevel
						}
						if checker.CheckValue == nil {
							checker.CheckValue = &checkRawData.Value
						}
						standardRule := helper.ConvertSimpleBaselineCheckerToBaselineStandardRule(req.ID, &checker)
						if checker.CheckValue != nil {
							checkRawData.Value = *checker.CheckValue
							if err := checkRawData.Validate(); err != nil {
								return err
							}
						}
						err = tx.WithContext(ctx).Create(standardRule).Error
						if err != nil {
							s.log.Error("create baseline standard rule failed from custom checkers", zap.Error(err))
							return fmt.Errorf("create baseline standard rule failed from custom checkers : %w", err)
						}
					}
				}
			}

		}

		// 9. 批量更新
		if len(toUpdate) > 0 {
			for _, updateRecord := range toUpdate {
				err = tx.WithContext(ctx).Model(&caas.BaselineStandardRule{}).
					Where("id = ?", updateRecord.ID).
					Updates(updateRecord).Error
				if err != nil {
					s.log.Error("update baseline standard rules failed", zap.Error(err))
					return fmt.Errorf("update baseline standard rules failed: %w", err)
				}
			}
		}

		//for _, checker := range req.Checkers {
		//	standardRule := helper.ConvertSimpleBaselineCheckerToBaselineStandardRule(req.ID, &checker)
		//	tx := tx.WithContext(ctx).Model(&caas.BaselineStandardRule{})
		//	var err error
		//	if record, ok := existedStandardRuleMap[checker.CheckID]; ok {
		//		// 如果已经存在且不需要更新跳过
		//		if checker.CheckValue == nil && len(checker.RiskLevel) == 0 {
		//			continue
		//		} else {
		//			standardRule.ID = record.ID
		//			err = tx.Where("id = ?", record.ID).Updates(standardRule).Error
		//		}
		//	} else {
		//		err = tx.WithContext(ctx).Save(standardRule).Error
		//	}
		//	if err != nil {
		//		s.log.Error("create baseline standard rule failed", zap.Error(err))
		//		return fmt.Errorf("create baseline standard rule failed: %w", err)
		//	}
		//}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return &models.UpdateBaselineResponse{}, nil
}

func (s *standardHandler) DeleteBaseline(ctx context.Context, req *models.DeleteBaselineRequest) (*models.DeleteBaselineResponse, error) {
	s.log.Info("delete baseline", zap.Any("request", req))
	var strategies []caas.BaselineStrategyStandard
	err := s.db.WithContext(ctx).Where("standard_id = ?", req.ID).Find(&strategies).Error
	if err != nil {
		s.log.Error("failed to query baseline strategies: %v", zap.Error(err))
		return nil, fmt.Errorf("failed to query baseline strategies: %w", err)
	}
	if len(strategies) > 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.BaselineStandardInUse, "baseline standard is in use")
	}
	var existedStandard caas.BaselineStandard
	if err := s.db.WithContext(ctx).Where("id = ?", req.ID).Find(&existedStandard).Error; err != nil {
		if goerrors.Is(err, gorm.ErrRecordNotFound) {
			return &models.DeleteBaselineResponse{}, nil
		} else {
			return nil, err
		}
	} else {
		if existedStandard.Builtin {
			return nil, fmt.Errorf("builtin standard can not deleted")
		}
	}
	err = s.db.Transaction(func(tx *gorm.DB) error {
		err = tx.WithContext(ctx).Where("id = ?", req.ID).Delete(&caas.BaselineStandard{}).Error
		if err != nil {
			s.log.Error("delete baseline standard failed", zap.Error(err))
			return fmt.Errorf("delete baseline standard failed: %w", err)
		}
		err = tx.WithContext(ctx).Where("standard_id = ?", req.ID).Delete(&caas.BaselineStandardRule{}).Error
		if err != nil {
			s.log.Error("delete baseline standard rules failed", zap.Error(err))
			return fmt.Errorf("delete baseline standard rules failed: %w", err)
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return &models.DeleteBaselineResponse{}, nil
}

func (s *standardHandler) QueryCategoryStandards(ctx context.Context, req *models.QueryCategoryStandardsRequest) (*models.QueryCategoryStandardsResponse, error) {
	s.log.Info("query category standards", zap.Any("request", req))

	// STEP 1. 查询数据库
	// STEP 1.1 查询基线标准分类
	var categories []caas.BaselineStandardGroup
	tx := s.db.WithContext(ctx).Model(&caas.BaselineStandardGroup{})
	if req.Builtin != nil {
		tx.Where("builtin = ?", *req.Builtin)
	}
	if err := tx.Model(&caas.BaselineStandardGroup{}).Find(&categories).Error; err != nil {
		s.log.Error("query baseline categories failed", zap.Error(err))
		return nil, fmt.Errorf("query baseline categories failed: %w", err)
	}

	// 使用 lo.Map 构造分类映射
	var categoryIds []int64
	categoryMap := lo.SliceToMap(categories, func(category caas.BaselineStandardGroup) (int64, *models.CategoryItem) {
		return category.ID, &models.CategoryItem{
			ID:          category.ID,
			Name:        category.Name,
			Description: category.Description,
			Builtin:     category.Builtin,
		}
	})

	// STEP 1.2 查询基线标准
	var standards []caas.BaselineStandard
	db := s.db.WithContext(ctx).Model(&caas.BaselineStandard{})
	// 如果是复制页面放开选中自己的限制
	if !req.IsCopy {
		db.Not("id", req.StandardId)
	}
	if len(categoryIds) > 0 {
		db.Where("group_id in (?)", categoryIds)
	}
	if err := db.Find(&standards).Error; err != nil {
		s.log.Error("query baseline standards failed", zap.Error(err))
		return nil, fmt.Errorf("query baseline standards failed: %w", err)
	}

	// 使用分组基线标准
	standardMap := lo.GroupBy(standards, func(standard caas.BaselineStandard) int64 {
		return standard.GroupID
	})

	var standardRules []caas.BaselineStandardRule
	if err := s.db.WithContext(ctx).Find(&standardRules).Error; err != nil {
		return nil, err
	}
	standardRuleMap := lo.GroupBy(standardRules, func(item caas.BaselineStandardRule) int64 {
		return item.StandardID
	})

	// 转换基线标准为响应格式
	var standardIds []int64
	standardItems := lo.MapValues(standardMap, func(standards []caas.BaselineStandard, groupID int64) []models.StandardItem {
		return lo.Map(standards, func(standard caas.BaselineStandard, _ int) models.StandardItem {
			standardIds = append(standardIds, standard.ID)
			categoryItem := models.CategoryItem{}
			if v, ok := categoryMap[groupID]; ok && v != nil {
				categoryItem = *v
			}
			standardRules := standardRuleMap[standard.ID]
			checkIds := make([]int64, 0)
			standardCheckIds := make([]int64, 0)
			standardCheckIDItems := lo.Map(standardRules, func(item caas.BaselineStandardRule, index int) models.StandardCheckIDItem {
				checkIds = append(checkIds, item.RuleID)
				standardCheckIds = append(standardCheckIds, item.ID)
				return models.StandardCheckIDItem{
					StandardCheckID: item.ID,
					ID:              item.RuleID,
				}
			})
			return models.StandardItem{
				ID:               standard.ID,
				Name:             standard.Name,
				CategoryID:       categoryItem.ID,
				CategoryName:     categoryItem.Name,
				CategoryBuiltin:  categoryItem.Builtin,
				Builtin:          standard.Builtin,
				CheckIds:         checkIds,
				StandardCheckIds: standardCheckIds,
				CheckItem: pagemodels.PageableResponse[models.StandardCheckIDItem]{
					Items:      standardCheckIDItems,
					TotalCount: len(standardRules),
				},
				SelectedCheckInfo: nil,
				Description:       standard.Description,
				CreateTime:        standard.CreateTime.Format(time.DateTime),
				UpdateTime:        standard.UpdateTime.Format(time.DateTime),
			}
		})
	})

	// set req standard ids
	if req.StandardId != nil {
		var standard caas.BaselineStandard
		if err := s.db.WithContext(ctx).Model(&caas.BaselineStandard{}).Where("id = ?", *req.StandardId).Find(&standard).Error; err != nil {
			s.log.Error("query baseline standard failed", zap.Error(err))
			return nil, fmt.Errorf("query baseline standard failed: %w", err)
		}
		standardIds = append(standardIds, *req.StandardId)
		var rules []caas.BaselineStandardRule
		db = s.db.WithContext(ctx).Model(&caas.BaselineStandardRule{})
		if len(standardIds) > 0 {
			db.Where("standard_id in (?)", standardIds).Find(&rules)
		}
		if err := db.Find(&rules).Error; err != nil {
			s.log.Error("query baseline standard rules failed", zap.Error(err))
			return nil, fmt.Errorf("query baseline standard rules failed: %w", err)
		}
		stdChecksMap := lo.GroupBy(rules, func(item caas.BaselineStandardRule) int64 {
			return item.StandardID
		})
		stdChecks := stdChecksMap[*req.StandardId]
		stdCheckIdSet := sets.New(lo.Map(stdChecks, func(item caas.BaselineStandardRule, index int) int64 {
			return item.RefID
		})...)

		if req.IsCopy {
			// 如果是 copy 默认只选中自己
			stdCheckIdSet.Clear()
			stdCheckIdSet.Insert(lo.Map(stdChecks, func(item caas.BaselineStandardRule, index int) int64 {
				return item.ID
			})...)
		}
		for _, items := range standardItems {
			for i := range items {
				items[i].SelectedCheckInfo = helper.NewSelectedCheckerInfo()
				for _, item := range stdChecksMap[items[i].ID] {
					if stdCheckIdSet.Has(item.ID) {
						items[i].SelectedCheckInfo.CheckIds = append(items[i].SelectedCheckInfo.CheckIds, item.RuleID)
						items[i].SelectedCheckInfo.StandardCheckIds = append(items[i].SelectedCheckInfo.StandardCheckIds, item.ID)
						checkValueItem, _ := helper.ConvertBaselineStandardRuleToCheckValue(&item)
						items[i].SelectedCheckInfo.Checkers = append(items[i].SelectedCheckInfo.Checkers, models.SimpleBaselineChecker{
							CheckID:    item.RuleID,
							RiskLevel:  item.RiskLevel,
							CheckValue: checkValueItem,
						})
					}
				}
			}
		}
	}

	// STEP 2. 构造响应
	responseItems := lo.Map(categories, func(category caas.BaselineStandardGroup, _ int) *models.CategoryStandardsItem {
		return &models.CategoryStandardsItem{
			CategoryItem: *categoryMap[category.ID],
			Standards:    standardItems[category.ID],
		}
	})

	// 过滤结果
	req.Filter.WithFn(func(item *models.CategoryStandardsItem) bool {
		return item != nil && len(item.Standards) > 0
	})
	filteredResult, err := req.Filter.FilterResultWithoutPage(responseItems)
	if err != nil {
		s.log.Error("filter categories and standards failed", zap.Error(err))
		return nil, fmt.Errorf("filter categories and standards failed: %w", err)
	}

	// 构造最终响应
	response := &models.QueryCategoryStandardsResponse{
		PageableResponse: pagemodels.PageableResponse[*models.CategoryStandardsItem]{
			Items:      filteredResult.Items,
			TotalCount: filteredResult.TotalCount,
		},
	}

	// STEP 3. 返回响应
	return response, nil
}

// GetCategoryNameExisted implements StandardInterface.
func (s *standardHandler) GetCategoryNameExisted(ctx context.Context, req *models.GetCategoryNameExistedRequest) (*models.GetCategoryNameExistedResponse, error) {
	s.log.Info("get category name existed", zap.Any("request", req))
	var count int64
	err := s.db.WithContext(ctx).Model(&caas.BaselineStandardGroup{}).Where("BINARY name = ?", req.Name).Count(&count).Error
	if err != nil {
		s.log.Error("query baseline category failed", zap.Error(err))
		return nil, fmt.Errorf("query baseline category failed: %w", err)
	}
	return &models.GetCategoryNameExistedResponse{
		Existed: count > 0,
	}, nil
}

func (s *standardHandler) QueryBaselineCategories(ctx context.Context, req *models.QueryBaselineCategoriesRequest) (*models.QueryBaselineCategoriesResponse, error) {
	s.log.Info("query baseline categories", zap.Any("request", req))
	// STEP 1. 查询数据库
	dbItems := []struct {
		caas.BaselineStandardGroup
		StandardCount int
	}{}
	query := s.db.WithContext(ctx).
		Table(fmt.Sprintf("%s AS g", caas.TableNameBaselineStandardGroup)).
		Select("g.*, COUNT(s.id) AS standard_count").
		Joins(`LEFT JOIN (?) AS s ON s.group_id = g.id`,
			s.db.WithContext(ctx).Model(&caas.BaselineStandard{})).
		Group("g.id")
	if req.Name != "" {
		query = query.Where("name LIKE ?", "%"+req.Name+"%")
	}
	err := query.Find(&dbItems).Error
	if err != nil {
		s.log.Error("query baseline categories failed", zap.Error(err))
		return nil, fmt.Errorf("query baseline categories failed: %w", err)
	}
	// STEP 2. 构造响应
	responseItems := make([]*models.CategoryItem, len(dbItems))
	for i, category := range dbItems {
		responseItems[i] = &models.CategoryItem{
			ID:            category.ID,
			Name:          category.Name,
			Description:   category.Description,
			Builtin:       category.Builtin,
			StandardCount: int(category.StandardCount),
		}
	}
	result, err := req.Filter.FilterResultWithoutPage(responseItems)
	if err != nil {
		s.log.Error("filter categories failed", zap.Error(err))
		return nil, fmt.Errorf("filter categories failed: %w", err)
	}
	response := &models.QueryBaselineCategoriesResponse{
		PageableResponse: pagemodels.PageableResponse[*models.CategoryItem]{
			Items:      result.Items,
			TotalCount: result.TotalCount,
		},
	}
	// STEP 3. 返回响应
	return response, nil
}

func (s *standardHandler) CreateBaselineCategory(ctx context.Context, req *models.CreateBaselineCategoryRequest) (*models.CreateBaselineCategoryResponse, error) {
	s.log.Info("create baseline category", zap.Any("request", req))
	// STEP 1.1 是否重名
	var existCount int64
	err := s.db.WithContext(ctx).
		Model(&caas.BaselineStandardGroup{}).
		Where("BINARY name = ?", req.Name).
		Count(&existCount).Limit(1).Error
	if err != nil {
		s.log.Error("query baseline category failed", zap.Error(err))
		return nil, fmt.Errorf("query baseline category failed: %w", err)
	}
	if existCount > 0 {
		return nil, errors.NewFromCode(errors.Var.BaselineCategoryNameRepeat)
	}
	// STEP 1.2 构造数据
	var category caas.BaselineStandardGroup
	category.Name = req.Name
	category.Description = req.Description
	// STEP 2. 插入数据库
	err = s.db.WithContext(ctx).Create(&category).Error
	if err != nil {
		s.log.Error("create baseline category failed", zap.Error(err))
		return nil, fmt.Errorf("create baseline category failed: %w", err)
	}
	return &models.CreateBaselineCategoryResponse{
		ID: category.ID,
	}, nil
}

func (s *standardHandler) EditBaselineCategory(ctx context.Context, req *models.EditBaselineCategoryRequest) (*models.EditBaselineCategoryResponse, error) {
	s.log.Info("edit baseline category", zap.Any("request", req))
	// STEP 1. 更新指定字段
	// STEP 1.1 查询名字是否被使用
	var existCount int64
	err := s.db.WithContext(ctx).Model(&caas.BaselineStandardGroup{}).
		Where("BINARY name = ?", req.Name).
		Where("id != ?", req.ID).
		Count(&existCount).Limit(1).Error
	if err != nil {
		s.log.Error("query baseline category failed", zap.Error(err))
		return nil, fmt.Errorf("query baseline category failed: %w", err)
	}
	if existCount > 0 {
		return nil, errors.NewFromCode(errors.Var.BaselineCategoryNameRepeat)
	}
	// STEP 1.2 更新
	err = s.db.WithContext(ctx).
		Model(&caas.BaselineStandardGroup{}).
		Where("id = ?", req.ID).
		Updates(map[string]interface{}{
			"name":        req.Name,
			"description": req.Description,
		}).Error
	if err != nil {
		s.log.Error("edit baseline category failed", zap.Error(err))
		return nil, fmt.Errorf("edit baseline category failed: %w", err)
	}

	return &models.EditBaselineCategoryResponse{
		ID: req.ID,
	}, nil
}

func (s *standardHandler) DeleteBaselineCategory(ctx context.Context, req *models.DeleteBaselineCategoryRequest) (*models.DeleteBaselineCategoryResponse, error) {
	s.log.Info("delete baseline category", zap.Any("request", req))
	// STEP 1. 查询基线标准是否内置
	var category caas.BaselineStandardGroup
	err := s.db.WithContext(ctx).Model(&caas.BaselineStandardGroup{}).Where("id = ?", req.ID).First(&category).Error
	if err != nil {
		s.log.Error("query baseline category failed", zap.Error(err))
		return nil, fmt.Errorf("query baseline category failed: %w", err)
	}
	if category.Builtin {
		s.log.Error("can not delete builtin baseline category")
		return nil, fmt.Errorf("can not delete builtin baseline category")
	}
	// STEP 2. 是否有基线标准
	var baselineCount int64
	err = s.db.WithContext(ctx).Model(&caas.BaselineStandard{}).Where("group_id = ?", req.ID).Count(&baselineCount).Error
	if err != nil {
		s.log.Error("query baseline category failed", zap.Error(err))
		return nil, fmt.Errorf("query baseline category failed: %w", err)
	}
	if baselineCount > 0 {
		s.log.Error("can not delete baseline category with standards")
		return nil, errors.NewFromCodeWithMessage(errors.Var.BaselineGroupHasStandards, "can not delete baseline category with standards")
	}

	err = s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		// STEP 3. 删除
		err = tx.WithContext(ctx).Delete(&caas.BaselineStandardGroup{}, req.ID).Error
		if err != nil {
			s.log.Error("delete baseline category failed", zap.Error(err))
			return fmt.Errorf("delete baseline category failed: %w", err)
		}
		// STEP 4. 将关联的基线标准分类置为未分类
		err = tx.WithContext(ctx).Model(&caas.BaselineStandard{}).Where("group_id = ?", req.ID).
			Updates(map[string]interface{}{
				"group_id": 0,
			}).Error
		if err != nil {
			s.log.Error("delete baseline category failed", zap.Error(err))
			return fmt.Errorf("delete baseline category failed: %w", err)
		}
		return nil
	})
	if err != nil {
		s.log.Error("delete baseline category failed", zap.Error(err))
		return nil, fmt.Errorf("delete baseline category failed: %w", err)
	}

	return &models.DeleteBaselineCategoryResponse{}, nil
}

// QueryAssociatedStrategies implements StandardInterface.
func (s *standardHandler) QueryAssociatedStrategies(ctx context.Context, req *models.QueryAssociatedStrategiesRequest) (*models.QueryAssociatedStrategiesResponse, error) {
	s.log.Info("query associated strategies", zap.Any("request", req))
	// STEP 1. 查询基线标准
	var sampleStandards []caas.BaselineStrategyStandard
	err := s.db.WithContext(ctx).Model(&caas.BaselineStrategyStandard{}).
		Distinct("strategy_id").
		Where("standard_id = ?", req.ID).
		Find(&sampleStandards).Error
	if err != nil {
		s.log.Error("query baseline standard failed", zap.Error(err))
		return nil, fmt.Errorf("query baseline standard failed: %w", err)
	}
	strategyIds := lo.Map(sampleStandards, func(item caas.BaselineStrategyStandard, _ int) int64 {
		return item.StrategyID
	})
	if len(strategyIds) == 0 {
		return &models.QueryAssociatedStrategiesResponse{}, nil
	}
	var strategies []caas.BaselineStrategy
	if err := s.db.WithContext(ctx).
		Model(&caas.BaselineStrategy{}).
		Where("id in (?)", strategyIds).
		Find(&strategies).Error; err != nil {
		return nil, fmt.Errorf("query baseline strategy failed: %w", err)
	}
	resp := &models.QueryAssociatedStrategiesResponse{}
	resp.Strategies = lo.Map(strategies, func(item caas.BaselineStrategy, _ int) models.SimpleStrategyItem {
		simpleItem := helper.ConvertBaselineStrategyToSimpleStrategyItem(&item)
		return *simpleItem
	})
	return resp, nil
}

func (s *standardHandler) UpdateBindingStrategies(ctx context.Context, req *models.UpdateBindingStrategiesRequest) (*models.UpdateBindingStrategiesResponse, error) {
	var strategyStandards []caas.BaselineStrategyStandard
	rtx := s.db.WithContext(ctx).Model(&caas.BaselineStrategyStandard{}).Where("standard_id = ?", req.ID)
	if len(req.UnbindStrategyIds) > 0 {
		rtx.Where("strategy_id in (?)", req.UnbindStrategyIds)
	}
	if err := rtx.Find(&strategyStandards).Error; err != nil {
		return nil, err
	}
	if len(strategyStandards) == 0 {
		return &models.UpdateBindingStrategiesResponse{}, nil
	}
	var ids []int64
	for _, item := range strategyStandards {
		ids = append(ids, item.StrategyID)
	}
	err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		var strategyStandards []caas.BaselineStrategyStandard
		if err := tx.WithContext(ctx).Where("strategy_id in (?)", ids).Distinct("strategy_id", "standard_id").Find(&strategyStandards).Error; err != nil {
			return err
		}
		strategyStandardIdsMap := make(map[int64][]int64)
		for _, item := range strategyStandards {
			if _, ok := strategyStandardIdsMap[item.StrategyID]; !ok {
				strategyStandardIdsMap[item.StrategyID] = make([]int64, 0)
			}
			if item.StandardID == req.ID {
				continue
			}
			strategyStandardIdsMap[item.StrategyID] = append(strategyStandardIdsMap[item.StrategyID], item.StandardID)
		}
		// 找到对应strategy 的 standardIds 为空的 strategy
		var emptyStrategyIds []int64
		for strategyId, standardIds := range strategyStandardIdsMap {
			if len(standardIds) == 0 {
				emptyStrategyIds = append(emptyStrategyIds, strategyId)
			}
		}
		if len(emptyStrategyIds) != 0 {
			var strategies []caas.BaselineStrategy
			if err := tx.WithContext(ctx).Where("id in (?)", emptyStrategyIds).Find(&strategies).Error; err != nil {
				return err
			}
			strategyNames := lo.Map(strategies, func(item caas.BaselineStrategy, index int) string {
				return item.Name
			})
			if len(strategyNames) > 0 {
				strategyNameString := strings.Join(strategyNames, ",")
				return errors.NewFromCodeFormatMessageWithDetail(errors.Var.BaselineStrategyUnbindMustHasOneStandard,
					fmt.Sprintf("Cannot unbind baseline from [%s] strategy; at least one baseline is required", strategyNameString),
					strategyNameString)
			}
		}

		// 更新关联的基线标准
		if err := tx.WithContext(ctx).
			Where("strategy_id in (?)", ids).Where("standard_id = ?", req.ID).Delete(&caas.BaselineStrategyStandard{}).Error; err != nil {
			return err
		}
		return nil
	})
	if err != nil {
		return nil, err
	}
	return &models.UpdateBindingStrategiesResponse{}, nil
}
