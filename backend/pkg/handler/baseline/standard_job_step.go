package baseline

import (
	"context"
	"encoding/json"
	goerrors "errors"
	"fmt"

	"github.com/samber/lo"
	"go.uber.org/zap"
	"gorm.io/gorm"
	clientmgr "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/feign/baseline_master"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline/helper"
	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
	"k8s.io/apimachinery/pkg/util/sets"
)

const (
	// 步骤名称常量
	StepInitJob        = "InitJob"
	StepCheckCluster   = "CheckCluster"
	StepCheckAddon     = "CheckAddon"
	StepSyncMonitors   = "SyncMonitors"
	StepWaitMonitors   = "WaitMonitors"
	StepSyncCheckers   = "SyncCheckers"
	StepCreateBaseline = "CreateBaseline"
	StepGetReport      = "GetReport"
	StepVerifyReport   = "StepVerifyReport"
	StepCleanResources = "StepCleanResources"
	StepSummaryStatus  = "SummaryStatus"
)

// StandardJobStep 定义标准作业步骤接口
type StandardJobStep interface {
	// Execute 执行步骤，strategy 参数为只读，确保不可变
	Execute(ctx context.Context, strategy caas.BaselineStrategy, task *models.StandardJobTask) error
	// Name 获取步骤名称
	Name() string
}

// StandardJobStepRegistry 步骤注册表
type StandardJobStepRegistry struct {
	steps []StandardJobStep
}

func NewStandardJobStepRegistry(handler *strategyJobHandler) *StandardJobStepRegistry {
	registry := &StandardJobStepRegistry{}
	// 按顺序注册所有步骤
	steps := []StandardJobStep{
		NewStandardInitJobStep(handler),
		NewCheckClusterStep(handler),
		NewCheckAddonStep(handler),
		NewSyncMonitorsStep(handler),
		NewWaitMonitorsStep(handler),
		NewSyncCheckersStep(handler),
		NewCreateBaselineStep(handler),
		NewGetReportStep(handler),
		NewSaveReportStep(handler),
		NewCleanResourcesStep(handler),
		NewUpdateStandardJobStatusStep(handler),
	}
	for _, step := range steps {
		registry.RegisterStep(step)
	}
	return registry
}

func (r *StandardJobStepRegistry) RegisterStep(step StandardJobStep) {
	r.steps = append(r.steps, step)
}

func (r *StandardJobStepRegistry) ExecuteSteps(ctx context.Context, strategy caas.BaselineStrategy, task *models.StandardJobTask) error {
	for _, step := range r.steps {
		if err := step.Execute(ctx, strategy, task); err != nil {
			return fmt.Errorf("step %s failed: %w", step.Name(), err)
		}
	}
	return nil
}

// BaseStep 基础步骤实现
type BaseStep struct {
	handler *strategyJobHandler
	log     *zap.Logger
}

func (s *BaseStep) updateJobCheckStatus(ctx context.Context, task *models.StandardJobTask, reason string, stepErr error) error {
	if goerrors.Is(ctx.Err(), context.DeadlineExceeded) {
		s.log.Info("execute check job timeout should update status", zap.Any("standard-job-id", task.Job.ID),
			zap.Any("check-job-count", len(task.CheckJobs)))
		timeoutMsg := fmt.Sprintf("execute check job time out after %s", s.handler.standardJobTimeout.String())
		if updateErr := s.handler.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error { // 更新所有未完成的 checkJob 状态
			for index := range task.CheckJobs {
				item := task.CheckJobs[index]
				if item.Status != string(models.JobStatusCompleted) {
					return helper.UpdateCheckJobStatusWithPassedAndReasonWithMessage(ctx, s.handler.db, task.CheckJobs[index],
						models.JobStatusCompleted, lo.ToPtr(false), lo.ToPtr(models.JobReasonCheckTimeout),
						timeoutMsg)
				}
			}
			if task.Job.Status != string(models.JobStatusCompleted) {
				return helper.UpdateStandardJobStatusWithPassedAndReasonAndMessage(ctx, s.handler.db, task.Job,
					models.JobStatusCompleted, lo.ToPtr(false), lo.ToPtr(models.JobReasonCheckTimeout), timeoutMsg)
			}
			return nil
		}); updateErr != nil {
			return updateErr
		}
		s.log.Info("execute check job timeout update status completed", zap.Any("standard-job-id", task.Job.ID),
			zap.Any("check-job-count", len(task.CheckJobs)))
	} else if stepErr != nil {
		if task.Job.Status != string(models.JobStatusCompleted) {
			_ = helper.UpdateStandardJobStatusWithPassedAndReasonAndMessage(ctx, s.handler.db, task.Job,
				models.JobStatusCompleted,
				lo.ToPtr(false),
				lo.ToPtr(reason),
				stepErr.Error())
		}
	}
	// 更新未完成的检查任务状态
	lo.ForEach(task.CheckJobs, func(item *caas.BaselineStandardRuleJob, index int) {
		if item.Status != string(models.JobStatusCompleted) {
			var reason *string
			var message string
			if !task.Job.Passed && item.Reason == "" {
				reason = lo.ToPtr(task.Job.Reason)
				message = task.Job.Message
			}
			_ = helper.UpdateCheckJobStatusWithPassedAndReasonWithMessage(ctx, s.handler.db, task.CheckJobs[index],
				models.JobStatusCompleted, nil, reason, message)
		}
	})

	// 如果任务已经有错误原因，直接更新状态
	if checkErr := helper.UpdateStandardJobCheckStatus(ctx, task.Job, task.CheckJobs, task.CheckItems); checkErr != nil {
		return fmt.Errorf("update standard job check status failed: %w", checkErr)
	}
	return helper.UpdateStandardJobStatusWithPassedAndReasonAndMessage(ctx, s.handler.db, task.Job,
		models.JobStatusCompleted,
		&task.Job.Passed,
		&task.Job.Reason,
		task.Job.Message)
}

// uploadJobTaskResourcesToMinio 上传StandardJobTask资源到Minio
func (s *BaseStep) uploadJobTaskResourcesToMinio(ctx context.Context, task *models.StandardJobTask) error {
	cluster, err := clientmgr.GetCluster(task.Job.ClusterName)
	if err != nil {
		return fmt.Errorf("failed to get cluster: %w", err)
	}
	ctrlclient := cluster.GetClient().GetCtrlClient()
	return helper.UploadJobTaskResourcesToMinio(ctx, s.handler.minioClient, ctrlclient,
		task.Job.ClusterName, constants.MinioBaselineReportBucketName, task)
}

func (s *BaseStep) deleteResourcesFromTask(ctx context.Context, task *models.StandardJobTask) error {
	cluster, err := clientmgr.GetCluster(task.Job.ClusterName)
	if err != nil {
		return fmt.Errorf("failed to get cluster: %w", err)
	}
	ctrlclient := cluster.GetClient().GetCtrlClient()
	return helper.DeleteResourcesFromJobTask(ctx, ctrlclient, task)
}

// handleStepError 处理步骤执行错误
func (s *BaseStep) handleStepError(ctx context.Context, task *models.StandardJobTask, stepName string, err *error) {
	if r := recover(); r != nil {
		s.log.Error(fmt.Sprintf("%s step panic", stepName), zap.Any("error", r))
		panicErr := fmt.Errorf("step %s panic: %v\nStack: %s", stepName, r, helper.GetDebugStack())
		*err = panicErr
	}
	if err != nil && *err != nil {
		if uploadErr := s.uploadJobTaskResourcesToMinio(ctx, task); uploadErr != nil {
			s.log.Warn("failed to upload resources from task", zap.Error(uploadErr))
		}
		if delErr := s.deleteResourcesFromTask(ctx, task); delErr != nil {
			s.log.Warn("failed to delete resources from task", zap.Error(delErr))
		}
		s.log.Error(fmt.Sprintf("%s step failed", stepName), zap.Error(*err))
		var reason string
		switch stepName {
		case StepInitJob:
			reason = models.JobReasonInitJobError
		case StepCheckCluster:
			reason = models.JobReasonClusterError
		case StepCheckAddon:
			reason = models.JobReasonAddonAbnormalError
		case StepSyncMonitors:
			reason = models.JobReasonSyncMonitorsError
		case StepWaitMonitors:
			reason = models.JobReasonWaitMonitorsError
		case StepSyncCheckers:
			reason = models.JobReasonSyncCheckerError
		case StepCreateBaseline:
			reason = models.JobReasonBaselineCreateError
		case StepGetReport:
			reason = models.JobReasonGetReportError
		case StepVerifyReport:
			reason = models.JobReasonVerifyReportError
		default:
			reason = models.JobReasonUnknown
		}

		// 如果不是最后的状态更新步骤，则更新任务状态
		if stepName != StepSummaryStatus && task.Job.Status != string(models.JobStatusCompleted) {
			if err := s.updateJobCheckStatus(ctx, task, reason, *err); err != nil {
				s.log.Debug("failed update standard job status after execute step",
					zap.Any("strategy-id", task.Job.StrategyID),
					zap.Any("standard-id", task.Job.ID),
					zap.Any("standard-job-id", task.Job.ID), zap.Any("step", stepName), zap.Error(err))
			}

		}
	}
}

// InitJobStep 初始化任务步骤
type InitJobStep struct {
	BaseStep
}

func NewStandardInitJobStep(handler *strategyJobHandler) *InitJobStep {
	return &InitJobStep{
		BaseStep: BaseStep{
			handler: handler,
			log:     handler.log.Named(StepInitJob),
		},
	}
}

func (s *InitJobStep) Name() string {
	return StepInitJob
}

func (s *InitJobStep) Execute(ctx context.Context, strategy caas.BaselineStrategy, task *models.StandardJobTask) (err error) {
	defer s.handleStepError(ctx, task, s.Name(), &err)
	log := s.log.With(zap.Any("standard-job-id", task.Job.ID), zap.Any("cluster", task.Job.ClusterName), zap.Any("standard-id", task.Job.StandardID), zap.Any("strategy-id", task.Job.StrategyID))

	log.Info("init job step started")
	if len(task.CheckJobs) == 0 {
		log.Info("no check jobs, update job status to completed")
		_ = helper.UpdateStandardJobStatusWithPassedAndReasonAndMessage(ctx, s.handler.db, task.Job,
			models.JobStatusCompleted, nil, nil, "")
	}

	_ = helper.UpdateStandardJobStatusWithPassedAndReasonAndMessage(ctx, s.handler.db, task.Job,
		models.JobStatusRunning, nil, nil, "")

	for _, job := range task.CheckJobs {
		if err := helper.UpdateCheckJobStatusWithPassedAndReasonWithMessage(ctx, s.handler.db, job,
			models.JobStatusRunning, nil, nil, ""); err != nil {
			return fmt.Errorf("update check job status failed: %w", err)
		}
	}

	if err := s.deleteResourcesFromStrategyIDAndStandardID(ctx, task); err != nil {
		log.Warn("failed to delete resources from strategy id", zap.Error(err))
	}
	log.Info("init job step completed")
	return nil
}

func (s *InitJobStep) deleteResourcesFromStrategyIDAndStandardID(ctx context.Context, task *models.StandardJobTask) error {
	// delete standard job task resources
	return helper.DeleteResourcesFromStrategyIDAndStandardID(ctx, task.Job.ClusterName, task.Job.StrategyID, task.Job.StandardID)
}

// CheckClusterStep 检查集群步骤
type CheckClusterStep struct {
	BaseStep
}

func NewCheckClusterStep(handler *strategyJobHandler) StandardJobStep {
	return &CheckClusterStep{
		BaseStep: BaseStep{
			handler: handler,
			log:     handler.log.Named(StepCheckCluster),
		},
	}
}

func (s *CheckClusterStep) Name() string {
	return StepCheckCluster
}

// Execute 执行步骤
func (s *CheckClusterStep) Execute(ctx context.Context, _ caas.BaselineStrategy, task *models.StandardJobTask) (err error) {
	defer s.handleStepError(ctx, task, s.Name(), &err)
	log := s.log.With(zap.Any("standard-job-id", task.Job.ID), zap.Any("cluster", task.Job.ClusterName), zap.Any("standard-id", task.Job.StandardID), zap.Any("strategy-id", task.Job.StrategyID))
	log.Info("check cluster step started")
	if !helper.CheckClusterOnline(ctx, task.Job.ClusterName) {
		return fmt.Errorf("cluster %s is offline", task.Job.ClusterName)
	}

	log.Info("check cluster step completed")
	return nil
}

type CheckAddonStep struct {
	BaseStep
}

func NewCheckAddonStep(handler *strategyJobHandler) StandardJobStep {
	return &CheckAddonStep{
		BaseStep: BaseStep{
			handler: handler,
			log:     handler.log.Named(StepCheckAddon),
		},
	}
}

func (s *CheckAddonStep) Name() string {
	return StepCheckAddon
}

func (s *CheckAddonStep) Execute(ctx context.Context, _ caas.BaselineStrategy, task *models.StandardJobTask) (err error) {
	defer s.handleStepError(ctx, task, s.Name(), &err)
	log := s.log.With(zap.Any("standard-job-id", task.Job.ID), zap.Any("cluster", task.Job.ClusterName), zap.Any("standard-id", task.Job.StandardID), zap.Any("strategy-id", task.Job.StrategyID))
	log.Info("check addon step started")

	if ready, err := helper.CheckBaselineCheckerInstalled(ctx, s.handler.addonHandler, task.Job.ClusterName); !ready || err != nil {
		return fmt.Errorf("cluster %s baseline checker abnormal: %w", task.Job.ClusterName, err)
	}
	log.Info("check addon step completed")
	return nil
}

// SyncMonitorsStep 同步监控步骤
type SyncMonitorsStep struct {
	BaseStep
}

func NewSyncMonitorsStep(handler *strategyJobHandler) StandardJobStep {
	return &SyncMonitorsStep{
		BaseStep: BaseStep{
			handler: handler,
			log:     handler.log.Named(StepSyncMonitors),
		},
	}
}

func (s *SyncMonitorsStep) Name() string {
	return StepSyncMonitors
}

func (s *SyncMonitorsStep) Execute(ctx context.Context, strategy caas.BaselineStrategy, task *models.StandardJobTask) (err error) {
	defer s.handleStepError(ctx, task, s.Name(), &err)
	log := s.log.With(zap.Any("standard-job-id", task.Job.ID), zap.Any("cluster", task.Job.ClusterName), zap.Any("standard-id", task.Job.StandardID), zap.Any("strategy-id", task.Job.StrategyID))
	log.Info("sync monitors step started")
	// 1. 同步 monitor 到集群
	monitors, syncErr := helper.SyncStandardMonitors(ctx, s.log, s.handler.db, s.handler.monitorAdapter, strategy, task)
	if syncErr != nil {
		monitorMap := lo.SliceToMap(monitors, func(item *models.InfraMonitorItem) (string, *models.InfraMonitorItem) {
			return item.MonitorName, item
		})
		for _, checkJob := range task.CheckJobs {
			monitorName := checkJob.MonitorName
			if _, ok := monitorMap[monitorName]; !ok {
				task.SkipCheckJobIds = append(task.SkipCheckJobIds, checkJob.ID)
			}
		}
		err = fmt.Errorf("sync monitors failed: %w", syncErr)
		return err
	}
	// map by standardCheckID
	standardCheckIDMap := lo.SliceToMap(monitors, func(item *models.InfraMonitorItem) (int64, *models.InfraMonitorItem) {
		return item.StandardCheckID, item
	})
	for _, checkJob := range task.CheckJobs {
		if _, ok := standardCheckIDMap[checkJob.StandardRuleID]; !ok {
			task.SkipCheckJobIds = append(task.SkipCheckJobIds, checkJob.ID)
		} else {
			_ = helper.UpdateCheckJobStatusWithPassedAndReasonWithMessage(ctx, s.handler.db, checkJob, models.JobStatusRunning, nil, nil, "")
		}
	}
	log.Info("sync monitors step completed", zap.Any("skip-check-job-ids", task.SkipCheckJobIds))
	return err
}

// SyncCheckersStep 同步检查器步骤
type SyncCheckersStep struct {
	BaseStep
}

func NewSyncCheckersStep(handler *strategyJobHandler) StandardJobStep {
	return &SyncCheckersStep{
		BaseStep: BaseStep{
			handler: handler,
			log:     handler.log.Named(StepSyncCheckers),
		},
	}
}

func (s *SyncCheckersStep) Name() string {
	return StepSyncCheckers
}

func (s *SyncCheckersStep) Execute(ctx context.Context, strategy caas.BaselineStrategy, task *models.StandardJobTask) (err error) {
	defer s.handleStepError(ctx, task, s.Name(), &err)
	log := s.log.With(zap.Any("standard-job-id", task.Job.ID), zap.Any("cluster", task.Job.ClusterName), zap.Any("standard-id", task.Job.StandardID), zap.Any("strategy-id", task.Job.StrategyID))
	log.Info("sync checkers step started")
	checkerItems, syncErr := helper.SyncStandardCheckers(ctx, s.log, s.handler.db, s.handler.checkerAdapter, task)
	if syncErr != nil {
		checkerNameSet := sets.NewString(lo.Map(checkerItems, func(item *models.InfraCheckerItem, _ int) string {
			return item.CheckerName
		})...)
		for _, checkJob := range task.CheckJobs {
			if !checkerNameSet.Has(checkJob.CheckerName) {
				task.SkipCheckJobIds = append(task.SkipCheckJobIds, checkJob.ID)
			}
		}
		return fmt.Errorf("sync checkers failed: %w", syncErr)
	}

	if setErr := helper.SetCheckerNameToCheckJob(ctx, s.handler.db, task.CheckJobs, checkerItems); setErr != nil {
		if err != nil {
			err = fmt.Errorf("%v; set checker name failed: %w", err, setErr)
		} else {
			err = fmt.Errorf("set checker name failed: %w", setErr)
		}
		return fmt.Errorf("set checker name failed: %w", setErr)
	}

	emptyCheckerNameCnt := 0
	for _, checkJob := range task.CheckJobs {
		if checkJob.CheckerName == "" {
			emptyCheckerNameCnt++
			task.SkipCheckJobIds = append(task.SkipCheckJobIds, checkJob.ID)
		}
	}
	if emptyCheckerNameCnt == len(task.CheckJobs) && len(task.CheckJobs) > 0 {
		if err != nil {
			err = fmt.Errorf("%v; has %d checkers not sync to cluster", err, emptyCheckerNameCnt)
		} else {
			err = fmt.Errorf("has %d checkers not sync to cluster", emptyCheckerNameCnt)
		}
	}
	log.Info("sync checkers step completed", zap.Any("skip-check-job-ids", task.SkipCheckJobIds), zap.Any("empty-checker-name-cnt", emptyCheckerNameCnt))
	return err
}

// WaitMonitorsStep 等待监控步骤
type WaitMonitorsStep struct {
	BaseStep
}

func NewWaitMonitorsStep(handler *strategyJobHandler) StandardJobStep {
	return &WaitMonitorsStep{
		BaseStep: BaseStep{
			handler: handler,
			log:     handler.log.Named(StepWaitMonitors),
		},
	}
}

func (s *WaitMonitorsStep) Name() string {
	return StepWaitMonitors
}

func (s *WaitMonitorsStep) Execute(ctx context.Context, strategy caas.BaselineStrategy, task *models.StandardJobTask) (err error) {
	defer s.handleStepError(ctx, task, s.Name(), &err)
	log := s.log.With(zap.Any("standard-job-id", task.Job.ID), zap.Any("cluster", task.Job.ClusterName), zap.Any("standard-id", task.Job.StandardID), zap.Any("strategy-id", task.Job.StrategyID))
	log.Info("wait monitors step started")
	runningMonitors, abnormalMonitors, ensureErr := s.handler.EnsureMonitorsRunning(ctx, strategy, task)
	if ensureErr != nil {
		s.log.Error("ensure monitors running failed", zap.Error(ensureErr), zap.Any("runningMonitors", runningMonitors), zap.Any("standard-job-id", task.Job.ID), zap.Any("cluster", task.Job.ClusterName))
		if len(task.CheckJobs) > 0 && len(runningMonitors) == 0 && len(abnormalMonitors) == 0 {
			return fmt.Errorf("ensure monitors running failed: %w", ensureErr)
		}
	}

	abnormalMonitorReasons := map[string]string{}
	for _, mnt := range abnormalMonitors {
		abnormalMonitorReasons[mnt.Name] = lo.If(mnt.Getter.Reason != "", mnt.Getter.Reason+","+mnt.Getter.Message).Else(mnt.Getter.Message)
	}
	for i, checkJob := range task.CheckJobs {
		mntName := checkJob.MonitorName
		if reason, ok := abnormalMonitorReasons[mntName]; ok && reason != "" {
			task.SkipCheckJobIds = append(task.SkipCheckJobIds, checkJob.ID)
			_ = helper.UpdateCheckJobStatusWithPassedAndReasonWithMessage(ctx, s.handler.db,
				task.CheckJobs[i], models.JobStatusCompleted, lo.ToPtr(false),
				lo.ToPtr(models.JobReasonWaitMonitorsError), reason)
		}
	}
	log.Info("wait monitors step completed", zap.Any("skip-check-job-ids", task.SkipCheckJobIds), zap.Any("running-monitors", runningMonitors))
	return nil
}

// CreateBaselineStep 创建基线步骤
type CreateBaselineStep struct {
	BaseStep
}

func NewCreateBaselineStep(handler *strategyJobHandler) StandardJobStep {
	return &CreateBaselineStep{
		BaseStep: BaseStep{
			handler: handler,
			log:     handler.log.Named(StepCreateBaseline),
		},
	}
}

func (s *CreateBaselineStep) Name() string {
	return StepCreateBaseline
}

func (s *CreateBaselineStep) Execute(ctx context.Context, strategy caas.BaselineStrategy, task *models.StandardJobTask) (err error) {
	defer s.handleStepError(ctx, task, s.Name(), &err)
	log := s.log.With(zap.Any("standard-job-id", task.Job.ID), zap.Any("cluster", task.Job.ClusterName), zap.Any("standard-id", task.Job.StandardID), zap.Any("strategy-id", task.Job.StrategyID))
	log.Info("create baseline step started")
	if len(task.CheckJobs) == 0 {
		log.Info("create baseline step completed")
		return
	}
	// 过滤掉需要跳过的检查项
	activeCheckJobs := lo.Filter(task.CheckJobs, func(job *caas.BaselineStandardRuleJob, _ int) bool {
		return !lo.Contains(task.SkipCheckJobIds, job.ID)
	})
	if len(activeCheckJobs) == 0 {
		return fmt.Errorf("no active check jobs, no need to create baseline")
	}

	baselineParam := models.BaselineParam{
		ClusterName:     task.Job.ClusterName,
		StrategyID:      task.Job.StrategyID,
		StandardID:      task.Job.StandardID,
		StandardJobID:   task.Job.ID,
		ExecutionConfig: *helper.ConvertBaselineStrategyToExecutionConfig(ctx, &strategy),
		Checkers: lo.Map(activeCheckJobs, func(item *caas.BaselineStandardRuleJob, _ int) models.CheckerParam {
			return models.CheckerParam{
				CheckID:     item.RuleID,
				CheckerName: item.CheckerName,
			}
		}),
	}

	baseline, err := s.handler.baselineAdapter.CreateBaseline(ctx, &baselineParam)
	if err != nil {
		return fmt.Errorf("create baseline failed: %w", err)
	}

	task.Job.BaselineName = baseline.BaselineName
	if err := s.handler.db.WithContext(ctx).Model(&caas.BaselineStandardJob{}).
		Where("id = ?", task.Job.ID).Update("baseline_name", baseline.BaselineName).Error; err != nil {
		return fmt.Errorf("update baseline failed: %w", err)
	}

	log.Info("create baseline step completed", zap.Any("baseline-name", baseline.BaselineName), zap.Any("baseline", baseline))
	return nil
}

// GetReportStep 获取报告步骤
type GetReportStep struct {
	BaseStep
}

func NewGetReportStep(handler *strategyJobHandler) StandardJobStep {
	return &GetReportStep{
		BaseStep: BaseStep{
			handler: handler,
			log:     handler.log.Named(StepGetReport),
		},
	}
}

func (s *GetReportStep) Name() string {
	return StepGetReport
}

func (s *GetReportStep) Execute(ctx context.Context, _ caas.BaselineStrategy, task *models.StandardJobTask) (err error) {
	defer s.handleStepError(ctx, task, s.Name(), &err)
	log := s.log.With(zap.Any("standard-job-id", task.Job.ID), zap.Any("cluster", task.Job.ClusterName), zap.Any("standard-id", task.Job.StandardID), zap.Any("strategy-id", task.Job.StrategyID))
	log.Info("get report step started")
	if len(task.CheckJobs) == 0 {
		log.Info("get report step completed")
		return
	}

	request := baseline_master.NewGetReportRequest(task.Job.BaselineName)
	reportResp, err := s.handler.PollGetReport(ctx, task, request)
	if err != nil {
		if reportResp == nil {
			reportResp = new(baseline_master.GetReportResponse)
		}
		if len(reportResp.Data) > 0 {
			reportJson, _ := json.Marshal(reportResp.Data[0])
			if saveErr := helper.SaveBaselineReport(ctx, s.handler.db, string(reportJson), task.Job); saveErr != nil {
				s.log.Warn("save baseline report failed", zap.Error(saveErr))
			}
		} else {
			reportJson, _ := json.Marshal(reportResp)
			if saveErr := helper.SaveBaselineReport(ctx, s.handler.db, string(reportJson), task.Job); saveErr != nil {
				s.log.Warn("save baseline report failed", zap.Error(saveErr))
			}
		}
		return fmt.Errorf("get report failed: %w", err)
	}
	task.Report = reportResp
	log.Info("get report step completed", zap.Any("request", request))
	return nil
}

// VerifyReportStep 保存报告步骤
type VerifyReportStep struct {
	BaseStep
}

func NewSaveReportStep(handler *strategyJobHandler) StandardJobStep {
	return &VerifyReportStep{
		BaseStep: BaseStep{
			handler: handler,
			log:     handler.log.Named(StepVerifyReport),
		},
	}
}

func (s *VerifyReportStep) Name() string {
	return StepVerifyReport
}

func (s *VerifyReportStep) Execute(ctx context.Context, _ caas.BaselineStrategy, task *models.StandardJobTask) (err error) {
	defer s.handleStepError(ctx, task, s.Name(), &err)
	log := s.log.With(zap.Any("standard-job-id", task.Job.ID), zap.Any("cluster", task.Job.ClusterName), zap.Any("standard-id", task.Job.StandardID), zap.Any("strategy-id", task.Job.StrategyID))
	log.Info("save report step started")
	if len(task.CheckJobs) == 0 {
		log.Info("save report step completed")
		return
	}
	if err := helper.VerifyStandardJobReport(ctx, s.handler.db, task.Report, task); err != nil {
		return fmt.Errorf("save standard job report failed: %w", err)
	}
	log.Info("save report step completed")
	return nil
}

type CleanResourcesStep struct {
	BaseStep
}

func NewCleanResourcesStep(handler *strategyJobHandler) StandardJobStep {
	return &CleanResourcesStep{BaseStep{
		handler: handler,
		log:     handler.log.Named(StepCleanResources),
	}}

}

func (s *CleanResourcesStep) Name() string {
	return StepCleanResources
}

func (s *CleanResourcesStep) Execute(ctx context.Context, strategy caas.BaselineStrategy, task *models.StandardJobTask) (err error) {
	defer s.handleStepError(ctx, task, s.Name(), &err)
	log := s.log.With(zap.Any("strategy-id", task.Job.StrategyID),
		zap.Any("standard-id", task.Job.ID),
		zap.Any("standard-job-id", task.Job.ID))

	// save baseline and checker to minio
	if uploadErr := s.uploadJobTaskResourcesToMinio(ctx, task); uploadErr != nil {
		log.Error("failed upload job task resources to minio", zap.Error(uploadErr))
	}
	// before clean resources
	if deleteErr := s.deleteResourcesFromTask(ctx, task); deleteErr != nil {
		log.Error("failed to delete resources from job task", zap.Error(deleteErr))
	}
	return nil
}

// SummaryStatus 最后汇总作业状态
type SummaryStatus struct {
	BaseStep
}

func NewUpdateStandardJobStatusStep(handler *strategyJobHandler) StandardJobStep {
	return &SummaryStatus{
		BaseStep: BaseStep{
			handler: handler,
			log:     handler.log.Named(StepSummaryStatus),
		},
	}
}

func (s *SummaryStatus) Name() string {
	return StepSummaryStatus
}

func (s *SummaryStatus) Execute(ctx context.Context, _ caas.BaselineStrategy,
	task *models.StandardJobTask) (err error) {
	defer func() {
		s.handleStepError(ctx, task, s.Name(), &err)
		task.Completed = true
	}()
	log := s.log.With(zap.Any("standard-job-id", task.Job.ID), zap.Any("cluster", task.Job.ClusterName), zap.Any("standard-id", task.Job.StandardID), zap.Any("strategy-id", task.Job.StrategyID))
	log.Info("update job status step started")
	// 如果任务已经完成，直接返回
	if task.Completed {
		return nil
	}
	if updateErr := s.updateJobCheckStatus(ctx, task, "", nil); updateErr != nil {
		s.log.Debug("failed update standard job status after execute step",
			zap.Any("strategy-id", task.Job.StrategyID),
			zap.Any("standard-id", task.Job.ID),
			zap.Any("standard-job-id", task.Job.ID), zap.Error(err))
		err = updateErr
		return updateErr
	}
	if finalErr := helper.UpdateStandardJobStatusWithPassedAndReasonAndMessage(ctx, s.handler.db, task.Job,
		models.JobStatusCompleted, nil, nil, ""); finalErr != nil {
		err = finalErr
		return fmt.Errorf("update standard job status failed: %w", finalErr)
	}
	log.Info("update job status step completed")
	return nil
}
