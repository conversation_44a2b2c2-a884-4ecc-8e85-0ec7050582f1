package baseline

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"slices"
	"sort"
	"strings"
	"time"

	clientmgr "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/addon"
	ctrlclient "sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"

	"github.com/robfig/cron/v3"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	bizerrors "harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/feign/baseline_master"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline/helper"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline/infra"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/resources"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/util/sets"
)

const (
	strategyLoggerName = "baseline/strategy"
)

var _ StrategyInterface = (*strategyHandler)(nil)

// NewStrategyHandler 创建策略处理器
func NewStrategyHandler(db *gorm.DB,
	baselineAdapter infra.BaselineAdapter,
	monitorAdapter infra.MonitorAdapter,
	addonHandler addon.Handler,
	jobHandler StrategyJobInterface,
	taskManager *StrategyTaskManager) StrategyInterface {
	// 初始化导入数据处理器

	return &strategyHandler{
		db:              db,
		reportService:   baseline_master.NewService(),
		baselineAdapter: baselineAdapter,
		monitorAdapter:  monitorAdapter,
		addonHandler:    addonHandler,
		jobHandler:      jobHandler,
		log:             logger.GetLogger().Named(strategyLoggerName),
		taskManager:     taskManager,
	}
}

type strategyHandler struct {
	db              *gorm.DB
	reportService   baseline_master.Service
	baselineAdapter infra.BaselineAdapter
	monitorAdapter  infra.MonitorAdapter
	addonHandler    addon.Handler
	jobHandler      StrategyJobInterface
	taskManager     *StrategyTaskManager
	log             *zap.Logger
}

// GetBaselineStrategyNameExisted implements StrategyInterface.
func (s *strategyHandler) GetBaselineStrategyNameExisted(ctx context.Context,
	req *models.GetBaselineStrategyNameExistedRequest) (*models.GetBaselineStrategyNameExistedResponse, error) {
	var count int64
	err := s.db.WithContext(ctx).Model(&caas.BaselineStrategy{}).Where("BINARY name = ?", req.Name).Count(&count).Error
	if err != nil {
		s.log.Error("GetBaselineStrategyNameExisted error", zap.Error(err))
		return nil, fmt.Errorf("GetBaselineStrategyNameExisted error: %w", err)
	}
	return &models.GetBaselineStrategyNameExistedResponse{Existed: count > 0}, nil
}

// SyncRecurringStrategyConfig 同步定时任务
func (s *strategyHandler) SyncRecurringStrategyConfig(ctx context.Context, strategy *caas.BaselineStrategy) error {
	cli := clientmgr.GetLocalCluster().GetClient().GetCtrlClient()
	labels := map[string]string{
		constants.BaselineStrategyIDLabelKey:                     fmt.Sprint(strategy.ID),
		constants.BaselineStrategyExecutionTypeLabelKey:          string(strategy.ExecutionStrategy),
		constants.BaselineStrategyExecutionRecurringTypeLabelKey: string(strategy.ExecutionRecurringType),
		constants.BaselineStrategyExecutionDaemonLabelKey:        fmt.Sprintf("%v", true),
	}
	annotations := map[string]string{
		constants.BaselineClusterNameLabelKey: strategy.ClusterNames,
	}
	executionConfig := helper.ConvertBaselineStrategyToExecutionConfig(ctx, strategy)
	executionConfigRawData, err := json.Marshal(executionConfig)
	if err != nil {
		return err
	}
	baselineConfigs := &corev1.ConfigMap{
		TypeMeta: metav1.TypeMeta{},
		ObjectMeta: metav1.ObjectMeta{
			Labels:      labels,
			Annotations: annotations,
			Name:        helper.GenerateStrategyBaselineConfigName(strategy.ID),
			Namespace:   constants.BaselineNamespace,
		},
		Data: map[string]string{
			"config": string(executionConfigRawData),
		},
	}
	updateObj := baselineConfigs.DeepCopy()
	result, err := controllerutil.CreateOrPatch(ctx, cli, updateObj, func() error {
		updateObj.Labels = baselineConfigs.Labels
		updateObj.Annotations = baselineConfigs.Annotations
		updateObj.Data = baselineConfigs.Data
		return nil
	})
	if err != nil {
		s.log.Debug("failed to create or patch baseline config", zap.Error(err), zap.Any("result", result))
		return err
	}
	return nil
}

func (s *strategyHandler) QueryBaselineStrategies(ctx context.Context, req *models.QueryBaselineStrategiesRequest) (*models.QueryBaselineStrategiesResponse, error) {
	s.log.Info("QueryBaselineStrategies", zap.Any("req", req))

	// STEP 1.1.获取策略列表
	var strategies []caas.BaselineStrategy
	db := s.db.WithContext(ctx).Model(&caas.BaselineStrategy{})
	if req.Name != "" {
		db = db.Where("name like ?", "%"+req.Name+"%")
	}
	if req.Enabled != nil {
		db = db.Where("enabled = ?", lo.Ternary(*req.Enabled, 1, 0))
	}
	dbFilter := req.Filter.DeepCopy()
	dbFilter.Offset = nil
	dbFilter.Limit = nil
	tx, _ := dbFilter.ApplyToDB(db, &caas.BaselineStrategy{}, req.ToFieldMapping())
	err := tx.Find(&strategies).Error
	if err != nil {
		s.log.Error("QueryBaselineStrategies error", zap.Error(err))
		return nil, fmt.Errorf("QueryBaselineStrategies error: %w", err)
	}
	// STEP 1.2.预处理所有的 standardIds 和 clusterNames
	standardIDSet := sets.New[int64]()
	strategyStandardsMap := make(map[int64][]caas.BaselineStrategyStandard)
	var baselineStrategyStandards []caas.BaselineStrategyStandard
	err = s.db.WithContext(ctx).Find(&baselineStrategyStandards).Error
	if err != nil {
		s.log.Error("QueryBaselineStrategies error during batch query", zap.Error(err))
		return nil, fmt.Errorf("QueryBaselineStrategies error: %w", err)
	}
	for _, baselineStrategyStandard := range baselineStrategyStandards {
		standardIDSet.Insert(baselineStrategyStandard.StandardID)
		strategyStandardsMap[baselineStrategyStandard.StrategyID] = append(strategyStandardsMap[baselineStrategyStandard.StrategyID], baselineStrategyStandard)
	}
	// 将 set 转为 slice，方便批量查询
	uniqueStandardIDs := standardIDSet.UnsortedList()
	// STEP 1.3. 批量查询检查项数量
	checkItemCounts := make(map[int64]int) // 缓存每个 standard_id 的检查项数量
	var results []struct {
		StandardID int64
		Count      int
	}

	err = s.db.Model(&caas.BaselineStandardRule{}).
		Select("standard_id, COUNT(id) AS count").
		Where("standard_id IN ?", uniqueStandardIDs).
		Group("standard_id").
		Find(&results).Error
	if err != nil {
		s.log.Error("QueryBaselineStrategies error during batch query", zap.Error(err))
		return nil, fmt.Errorf("QueryBaselineStrategies error: %w", err)
	}

	// 填充检查项数量缓存
	for _, result := range results {
		checkItemCounts[result.StandardID] = result.Count
	}

	// STEP 1.4. 生成响应数据
	responseItems := make([]*models.StrategyItem, 0, len(strategies))
	for _, strategy := range strategies {
		strategyStandards := strategyStandardsMap[strategy.ID]
		standardIDCount := len(strategyStandards)
		checkClusterCount := len(strings.Split(strategy.ClusterNames, ","))

		// 计算检查项数量
		var checkItemCount int
		for _, standards := range strategyStandards {
			checkItemCount += checkItemCounts[standards.StandardID]
		}
		responseItem := helper.ConvertBaselineStrategyToStrategyItem(ctx, &strategy)
		responseItem.CheckItemCount = checkItemCount
		responseItem.CheckClusterCount = checkClusterCount
		responseItem.BaselineStandardCount = standardIDCount
		responseItems = append(responseItems, responseItem)
	}
	result, err := req.Filter.FilterResult(responseItems)
	if err != nil {
		return nil, err
	}

	// STEP 3. 返回结果
	return &models.QueryBaselineStrategiesResponse{
		PageableResponse: *result,
	}, nil
}

// GetBaselineStrategyDetails 获取基线策略详情
func (s *strategyHandler) GetBaselineStrategyDetails(ctx context.Context, req *models.GetBaselineStrategyDetailsRequest) (*models.GetBaselineStrategyDetailsResponse, error) {
	s.log.Info("GetBaselineStrategyDetails", zap.Any("req", req))

	// STEP 1. 查询策略
	strategy := caas.BaselineStrategy{}
	err := s.db.Debug().WithContext(ctx).Where("ID = ?", req.ID).First(&strategy).Error
	if err != nil {
		s.log.Error("GetBaselineStrategyDetails error", zap.Error(err))
		return nil, fmt.Errorf("GetBaselineStrategyDetails error: %w", err)
	}

	// STEP 2. 解析标准ID和集群名称
	var baselineStrategyStandards []caas.BaselineStrategyStandard
	s.db.WithContext(ctx).Distinct("standard_id").Find(&baselineStrategyStandards, "strategy_id = ?", strategy.ID)
	standardIntIDs := lo.Map(baselineStrategyStandards, func(item caas.BaselineStrategyStandard, _ int) int64 { return item.StandardID })
	slices.Sort(standardIntIDs)
	clusterNames := strings.Split(strings.TrimSpace(strategy.ClusterNames), ",")
	executionConfig := *helper.ConvertBaselineStrategyToExecutionConfig(ctx, &strategy)
	// STEP 3. 构造返回值
	return &models.GetBaselineStrategyDetailsResponse{
		ID:                           strategy.ID,
		Name:                         strategy.Name,
		Description:                  strategy.Description,
		ExecutionConfigHumanReadable: helper.ConvertExecutionConfigToHumanReadable(ctx, executionConfig),
		ExecutionConfig:              helper.FilterExecutionConfig(executionConfig),
		SelectedBaselineIds:          standardIntIDs,
		SelectedClusterNames:         clusterNames,
		Enabled:                      strategy.Enabled == 1,
	}, nil
}

// Validate RecurringType when
func (s *strategyHandler) validateRecurringConfig(_ context.Context, executionConfig *models.ExecutionConfig) error {
	if executionConfig == nil {
		return bizerrors.NewFromCodeWithMessage(bizerrors.Var.BaselineRecurringParamsInvalid, "execution config is empty")
	}
	if executionConfig.ExecutionType != models.ExecutionTypeRecurring {
		return nil
	}
	recurringType, daysOfWeek, daysOfMonth, cronExpr := executionConfig.RecurringType, executionConfig.DaysOfWeek,
		executionConfig.DaysOfMonth, executionConfig.CronExpression
	weekFullDays := sets.New(lo.RangeFrom(0, 7)...)
	monthFullDays := sets.New(lo.RangeFrom(1, 32)...)
	switch recurringType {
	case models.RecurringTypeWeekly:
		if len(daysOfWeek) == 0 {
			return bizerrors.NewFromCodeWithMessage(bizerrors.Var.BaselineRecurringParamsInvalid,
				"daysOfWeek is required when recurringType is Weekly")
		}
		if !weekFullDays.HasAll(sets.New(daysOfWeek...).UnsortedList()...) {
			return bizerrors.NewFromCodeWithMessage(bizerrors.Var.BaselineRecurringParamsInvalid,
				"daysOfWeek only allow 0-6 when recurringType is Weekly, sunday is 0 ")
		}
	case models.RecurringTypeMonthly:
		if len(daysOfMonth) != 0 {
			if !monthFullDays.HasAll(sets.New(daysOfMonth...).UnsortedList()...) {
				return bizerrors.NewFromCodeWithMessage(bizerrors.Var.BaselineRecurringParamsInvalid,
					"daysOfMonth only allow 1-31 when recurringType is Monthly")
			}
		} else {
			startDayOfMonth, endDayOfMonth := executionConfig.StartDayOfMonth, executionConfig.EndDayOfMonth
			if startDayOfMonth > endDayOfMonth {
				return bizerrors.NewFromCodeWithMessage(bizerrors.Var.BaselineRecurringParamsInvalid, "start day cannot greater than end day")
			}
			if !monthFullDays.HasAll(sets.New(startDayOfMonth, endDayOfMonth).UnsortedList()...) {
				return bizerrors.NewFromCodeWithMessage(bizerrors.Var.BaselineRecurringParamsInvalid,
					"daysOfMonth only allow 1-31 when recurringType is Monthly")
			}
			executionConfig.DaysOfMonth = lo.Ternary(startDayOfMonth == endDayOfMonth, []int{startDayOfMonth},
				lo.RangeFrom(startDayOfMonth, endDayOfMonth-startDayOfMonth+1))
		}
	case models.RecurringTypeDaily:
		if executionConfig.PerDays <= 0 {
			return bizerrors.NewFromCodeWithMessage(bizerrors.Var.BaselineRecurringParamsInvalid,
				"perDays cannot less euqal 0")
		}
	case models.RecurringTypeCron:
		_, err := cron.ParseStandard(cronExpr)
		if err != nil {
			return bizerrors.NewFromCodeWithMessage(bizerrors.Var.BaselineCronExpressionInvalid,
				fmt.Sprintf("invalid cron expression: %v", err))
		}
	default:
		return fmt.Errorf("unknown recurringType: %s", recurringType)
	}
	return nil
}

// validateNameRepeat ...
func (s *strategyHandler) validateNameRepeat(ctx context.Context, id int64, name string) error {
	tx := s.db.WithContext(ctx)
	var records []caas.BaselineStrategy
	if id > 0 {
		tx = tx.Not("id", id).Where("BINARY name = ?", name)
	} else {
		tx = tx.Where("BINARY name = ?", name)
	}
	if err := tx.Find(&records).Error; err != nil {
		return err
	}
	if len(records) > 0 {
		return bizerrors.NewFromCode(bizerrors.Var.BaselineStrategyNameRepeat)
	}
	return nil
}

func (s *strategyHandler) CreateBaselineStrategy(ctx context.Context, req *models.CreateBaselineStrategyRequest) (*models.CreateBaselineStrategyResponse, error) {
	s.log.Info("CreateBaselineStrategy", zap.Any("req", req))
	if err := s.validateNameRepeat(ctx, 0, req.Name); err != nil {
		return nil, err
	}
	// STEP 1. 构造策略
	clusterNames := strings.Join(req.SelectedClusterNames, ",")
	// 解析 RecurringType
	executionConfig := &req.ExecutionConfig
	err := s.validateRecurringConfig(ctx, executionConfig)
	if err != nil {
		s.log.Error("invalid recurring config", zap.Error(err))
		return nil, err
	}
	strategy := &caas.BaselineStrategy{
		Name:         req.Name,
		Description:  req.Description,
		ClusterNames: clusterNames,
	}
	if err := helper.SetReqExecutionConfigToBaselineStrategy(ctx, strategy, executionConfig); err != nil {
		return nil, err
	}

	// STEP 2. 创建策略
	err = s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		//  create default enabled
		strategy.Enabled = 1
		err = tx.WithContext(ctx).Create(&strategy).Error
		if err != nil {
			s.log.Error("failed to create baseline strategy", zap.Error(err))
			return err
		}
		for _, clusterName := range strings.Split(strategy.ClusterNames, ",") {
			for _, standardId := range req.SelectedBaselineIds {
				err = tx.WithContext(ctx).Save(&caas.BaselineStrategyStandard{
					StrategyID:  strategy.ID,
					StandardID:  standardId,
					ClusterName: clusterName,
				}).Error
				if err != nil {
					s.log.Error("UpdateBaselineStrategy error", zap.Error(err))
					return err
				}
			}
		}
		return nil
	})
	if err != nil {
		s.log.Error("failed to create baseline strategy", zap.Error(err))
		return nil, fmt.Errorf("failed to create baseline strategy: %w", err)
	}
	switch req.ExecutionConfig.ExecutionType {
	case models.ExecutionTypeImmediate:
		s.log.Info("start run immediate job", zap.Any("strategy-id", strategy.ID))
		if _, err := s.jobHandler.ExecuteJob(ctx, &models.ExecuteCheckJobRequest{
			StrategyID: strategy.ID,
		}); err != nil {
			s.log.Error("failed to execute baseline strategy", zap.Error(err))
			return nil, err
		}
	case models.ExecutionTypeScheduled, models.ExecutionTypeRecurring:
		// 如果开启配置定时和周期任务
		if strategy.Enabled == 1 {
			s.log.Info("publish task event", zap.String("event", constants.BaselineStrategyTaskEventUpdate), zap.Int64("strategyID", strategy.ID))
			config := helper.ConvertBaselineStrategyToExecutionConfig(ctx, strategy)
			if err := s.taskManager.PublishTaskEventWithRetry(ctx, constants.BaselineStrategyTaskEventUpdate, strategy.ID, config); err != nil {
				s.log.Error("failed to publish task event", zap.Error(err))
				// 不返回错误,继续执行
			}
		}
	}

	return &models.CreateBaselineStrategyResponse{
		ID: strategy.ID,
	}, nil
}

func (s *strategyHandler) UpdateBaselineStrategy(ctx context.Context, req *models.UpdateBaselineStrategyRequest) (*models.UpdateBaselineStrategyResponse, error) {
	s.log.Info("UpdateBaselineStrategy", zap.Any("req", req))
	if err := s.validateNameRepeat(ctx, req.ID, req.Name); err != nil {
		return nil, err
	}
	strategy := new(caas.BaselineStrategy)
	if err := s.db.WithContext(ctx).Where("id = ?", req.ID).Find(strategy).Error; err != nil {
		return nil, err
	}
	// STEP 1. 构造策略
	clusterNames := strings.Join(req.SelectedClusterNames, ",")
	executionConfig := &req.ExecutionConfig
	err := s.validateRecurringConfig(ctx, executionConfig)
	if err != nil {
		s.log.Error("invalid recurring config", zap.Error(err))
		return nil, err
	}
	strategy.Name = req.Name
	strategy.Description = req.Description
	strategy.ClusterNames = clusterNames
	if err := helper.SetReqExecutionConfigToBaselineStrategy(ctx, strategy, executionConfig); err != nil {
		return nil, err
	}

	// STEP 2. 更新策略
	err = s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		err = tx.WithContext(ctx).Save(strategy).Error
		if err != nil {
			s.log.Error("UpdateBaselineStrategy error", zap.Error(err))
			return err
		}
		var existStrategyStandards []*caas.BaselineStrategyStandard
		err = tx.WithContext(ctx).Model(&caas.BaselineStrategyStandard{}).Where("strategy_id = ?", req.ID).
			Find(&existStrategyStandards).Error
		if err != nil {
			s.log.Error("UpdateBaselineStrategy error", zap.Error(err))
			return err
		}
		keyFn := func(clusterName string, standardId int64) string {
			return fmt.Sprintf("%s/%d", clusterName, standardId)
		}
		existStrategyStandardMap := make(map[string]*caas.BaselineStrategyStandard, len(existStrategyStandards))
		deleteIdMap := map[string]int64{}
		for i, ss := range existStrategyStandards {
			key := keyFn(ss.ClusterName, ss.StandardID)
			existStrategyStandardMap[key] = existStrategyStandards[i]
			deleteIdMap[key] = ss.ID
		}

		for _, clusterName := range strings.Split(strategy.ClusterNames, ",") {
			for _, standardId := range req.SelectedBaselineIds {
				key := keyFn(clusterName, standardId)
				delete(deleteIdMap, key)
				if _, ok := existStrategyStandardMap[key]; ok {
					continue
				} else {
					err = tx.WithContext(ctx).Save(&caas.BaselineStrategyStandard{
						StrategyID:  req.ID,
						StandardID:  standardId,
						ClusterName: clusterName,
					}).Error
					if err != nil {
						s.log.Error("UpdateBaselineStrategy error", zap.Error(err))
						return err
					}
				}
			}
		}
		for _, deleteId := range deleteIdMap {
			err := tx.WithContext(ctx).Delete(&caas.BaselineStrategyStandard{ID: deleteId}).Error
			if err != nil {
				s.log.Error("delete strategyStandard failed", zap.Error(err), zap.Any("id", deleteId))
				return err
			}

		}

		return nil
	})
	if err != nil {
		s.log.Error("UpdateBaselineStrategy error", zap.Error(err))
		return nil, fmt.Errorf("UpdateBaselineStrategy error: %w", err)
	}

	switch req.ExecutionConfig.ExecutionType {
	case models.ExecutionTypeImmediate:
		if _, err := s.jobHandler.ExecuteJob(ctx, &models.ExecuteCheckJobRequest{
			StrategyID: strategy.ID,
		}); err != nil {
			s.log.Error("failed to execute baseline strategy", zap.Error(err))
			return nil, err
		}
	case models.ExecutionTypeScheduled, models.ExecutionTypeRecurring:
		// 如果开启配置定时和周期任务
		if strategy.Enabled == 1 {
			config := helper.ConvertBaselineStrategyToExecutionConfig(ctx, strategy)
			s.log.Info("publish task event", zap.String("event", constants.BaselineStrategyTaskEventUpdate), zap.Int64("strategyID", strategy.ID))
			if err := s.taskManager.PublishTaskEventWithRetry(ctx, constants.BaselineStrategyTaskEventUpdate, strategy.ID, config); err != nil {
				s.log.Error("failed to publish task event", zap.Error(err))
				// 不返回错误,继续执行
			}
		}

	}

	return &models.UpdateBaselineStrategyResponse{}, nil
}

func (s *strategyHandler) DeleteBaselineStrategy(ctx context.Context, req *models.DeleteBaselineStrategyRequest) (*models.DeleteBaselineStrategyResponse, error) {
	s.log.Info("DeleteBaselineStrategy", zap.Any("req", req))
	// STEP 1. 删除策略
	err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		err := tx.WithContext(ctx).
			Model(&caas.BaselineStrategy{}).
			Where("id = ?", req.ID).
			Delete(&caas.BaselineStrategy{}).Error
		if err != nil {
			s.log.Error("DeleteBaselineStrategy error", zap.Error(err))
			return fmt.Errorf("failed to delete baseline strategy: %w", err)
		}
		// STEP 2. 删除绑定关系
		err = tx.WithContext(ctx).Model(&caas.BaselineStrategyStandard{}).Where("strategy_id = ?", req.ID).Delete(&caas.BaselineStrategyStandard{}).Error
		if err != nil {
			s.log.Error("DeleteBaselineStrategy error", zap.Error(err))
			return fmt.Errorf("failed to delete baseline strategy: %w", err)
		}
		// 删除配置的configmaps
		hubCluster := clientmgr.GetHubCluster()
		if hubCluster == nil {
			s.log.Error("DeleteBaselineStrategy error", zap.Error(fmt.Errorf("hub cluster not found")))
			return fmt.Errorf("hub cluster not found")
		}
		configmaps := &corev1.ConfigMapList{}
		if err := hubCluster.GetClient().GetCtrlClient().List(ctx, configmaps, &ctrlclient.ListOptions{
			LabelSelector: labels.SelectorFromSet(map[string]string{
				constants.BaselineStrategyIDLabelKey: fmt.Sprintf("%d", req.ID),
			}),
		}); err != nil {
			s.log.Error("DeleteBaselineStrategy error", zap.Error(err))
			return fmt.Errorf("failed to list configmaps: %w", err)
		}
		for _, configmap := range configmaps.Items {
			if configmap.DeletionTimestamp.IsZero() {
				if err := hubCluster.GetClient().GetCtrlClient().Delete(ctx, &configmap); err != nil {
					s.log.Error("DeleteBaselineStrategy error", zap.Error(err))
					return fmt.Errorf("failed to delete configmap: %w", err)
				}
			}
		}
		// 删除对应job记录
		if err := s.db.WithContext(ctx).Model(&caas.BaselineStrategyJob{}).Where("strategy_id = ?", req.ID).Delete(&caas.BaselineStrategyJob{}).Error; err != nil {
			s.log.Error("DeleteBaselineStrategy error", zap.Error(err))
			return fmt.Errorf("failed to delete baseline strategy job: %w", err)
		}
		// 删除对应standardJob记录
		if err := s.db.WithContext(ctx).Model(&caas.BaselineStandardJob{}).Where("strategy_id = ?", req.ID).Delete(&caas.BaselineStandardJob{}).Error; err != nil {
			s.log.Error("DeleteBaselineStrategy error", zap.Error(err))
			return fmt.Errorf("failed to delete baseline strategy standard job: %w", err)
		}
		// 删除对应standardRuleJob记录
		if err := s.db.WithContext(ctx).Model(&caas.BaselineStandardRuleJob{}).Where("strategy_id = ?", req.ID).Delete(&caas.BaselineStandardRuleJob{}).Error; err != nil {
			s.log.Error("DeleteBaselineStrategy error", zap.Error(err))
			return fmt.Errorf("failed to delete baseline strategy standard rule job: %w", err)
		}
		// 在事务最后添加发布删除事件
		if err := s.taskManager.PublishTaskEventWithRetry(ctx, constants.BaselineStrategyTaskEventDelete, req.ID, nil); err != nil {
			s.log.Error("failed to publish delete task event", zap.Error(err))
			// 不返回错误,继续执行
		}
		return nil
	})
	if err != nil {
		return nil, err
	}

	return &models.DeleteBaselineStrategyResponse{}, nil
}

func (s *strategyHandler) SwitchBaselineStrategy(ctx context.Context, req *models.SwitchBaselineStrategyRequest) (*models.SwitchBaselineStrategyResponse, error) {
	s.log.Info("SwitchBaselineStrategy", zap.Any("req", req))
	// STEP 1 更新策略
	enabled := 0
	if *req.Enabled {
		enabled = 1
	}
	err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if err := tx.Model(&caas.BaselineStrategy{}).
			Where("id = ?", req.ID).
			Update("enabled", enabled).
			Error; err != nil {
			return err
		}
		// 获取策略信息用于发布事件
		var strategy caas.BaselineStrategy
		if err := tx.Where("id = ?", req.ID).First(&strategy).Error; err != nil {
			return err
		}
		executionConfig := helper.ConvertBaselineStrategyToExecutionConfig(ctx, &strategy)
		eventType := constants.BaselineStrategyTaskEventAdd
		if enabled == 0 {
			eventType = constants.BaselineStrategyTaskEventDelete
		}
		switch executionConfig.ExecutionType {
		case models.ExecutionTypeScheduled, models.ExecutionTypeRecurring:
			if err := s.taskManager.PublishTaskEventWithRetry(ctx, eventType, req.ID, executionConfig); err != nil {
				s.log.Error("failed to publish task event", zap.Error(err))
				// 不返回错误,继续执行
			}
		}
		return nil
	})
	if err != nil {
		action := "disable"
		if *req.Enabled {
			action = "enable"
		}
		return nil, fmt.Errorf("failed to %s baseline strategy: %w", action, err)
	}
	return &models.SwitchBaselineStrategyResponse{}, nil
}

func (s *strategyHandler) getStrategyByID(ctx context.Context, id int64) (*caas.BaselineStrategy, error) {
	var strategy caas.BaselineStrategy
	err := s.db.WithContext(ctx).Model(&caas.BaselineStrategy{}).Where("id = ?", id).First(&strategy).Error
	if err != nil {
		s.log.Error("getStrategyByID error", zap.Error(err))
		return nil, err
	}
	return &strategy, nil

}

// GetLastCheckJobStatus 获取最新的检查任务结果
func (s *strategyHandler) GetLastCheckJobStatus(ctx context.Context, req *models.GetLastCheckJobStatusRequest) (*models.GetLastCheckJobStatusResponse, error) {
	var lastCheckJob caas.BaselineStrategyJob
	err := s.db.WithContext(ctx).Model(&caas.BaselineStrategyJob{}).Where("strategy_id = ?", req.ID).
		Order("create_time desc").
		First(&lastCheckJob).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &models.GetLastCheckJobStatusResponse{
				JobID:  0,
				Status: "",
			}, nil
		}
		return nil, fmt.Errorf("getLastCheckJobStatus error: %w", err)
	}
	return &models.GetLastCheckJobStatusResponse{
		JobID:         lastCheckJob.ID,
		Status:        models.JobStatus(lastCheckJob.Status),
		CheckTime:     lastCheckJob.CreateTime.Format(time.DateTime),
		CompletedTime: lo.Ternary(lastCheckJob.CompletedTime.IsZero(), "", lastCheckJob.CompletedTime.Format(time.DateTime)),
	}, nil
}

// ExecuteCheckJob 执行标准检查
func (s *strategyHandler) ExecuteCheckJob(ctx context.Context, req *models.ExecuteCheckJobRequest) (*models.ExecuteCheckJobResponse, error) {
	return s.jobHandler.ExecuteJob(ctx, req)
}

func (s *strategyHandler) getLastStrategyJob(ctx context.Context, strategyID int64) (*caas.BaselineStrategyJob, error) {
	var lastCheckJob caas.BaselineStrategyJob
	err := s.db.WithContext(ctx).Model(&caas.BaselineStrategyJob{}).Where("strategy_id = ?", strategyID).
		Order("create_time desc").
		First(&lastCheckJob).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return &caas.BaselineStrategyJob{
				ID:           0,
				StrategyID:   strategyID,
				ClusterNames: "",
				Status:       string(models.JobStatusPending),
			}, nil
		}
		s.log.Error("getLastStrategyJob error", zap.Error(err))
		return nil, fmt.Errorf("getLastStrategyJob error: %w", err)
	}
	return &lastCheckJob, nil
}

func (s *strategyHandler) GetBaselineStrategySummary(ctx context.Context, req *models.GetBaselineStrategySummaryRequest) (*models.GetBaselineStrategySummaryResponse, error) {
	s.log.Info("GetBaselineStrategySummary", zap.Any("req", req))
	// 查询基线策略任务表，获取最近一次执行时间
	lastCheckJob, err := s.getLastStrategyJob(ctx, req.ID)
	if err != nil {
		return nil, err
	}
	if lastCheckJob.ID == 0 {
		var record caas.BaselineStrategy
		err := s.db.WithContext(ctx).Where("id = ?", req.ID).First(&record).Error
		if err != nil {
			return nil, fmt.Errorf("failed to get baseline strategy: %w", err)
		}
		lastCheckJob = &caas.BaselineStrategyJob{
			ID:           0,
			StrategyID:   req.ID,
			ClusterNames: record.ClusterNames,
		}
	}
	if err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		if need, err := helper.CheckIfNeedUpdateStrategyJobSummary(ctx, tx, lastCheckJob); err != nil {
			return err
		} else {
			if need {
				if err := helper.UpdateStrategyJobSummary(ctx, s.log, tx, lastCheckJob); err != nil {
					return err
				}
			}
		}
		return nil
	}); err != nil {
		return nil, fmt.Errorf("strategy job has changed, when update summary, error: %w", err)
	}
	var resultStatus models.ResultStatus
	if lastCheckJob.ID == 0 {
		resultStatus = models.ResultStatusUnchecked
	} else {
		if lastCheckJob.Status != string(models.JobStatusCompleted) {
			resultStatus = models.ResultStatusChecking
		} else {
			if lastCheckJob.Passed {
				resultStatus = models.ResultStatusPassed
			} else {
				resultStatus = models.ResultStatusFailed
			}
		}
	}

	// STEP 2. 返回结果
	return &models.GetBaselineStrategySummaryResponse{
		ClusterRiskSummary: models.ClusterRiskSummary{
			Total:      int(lastCheckJob.ClusterCount),
			Risk:       int(lastCheckJob.ClusterRiskCount),
			NoRisk:     int(lastCheckJob.ClusterNoRiskCount),
			NotChecked: int(lastCheckJob.ClusterUncheckedCount),
			Failed:     int(lastCheckJob.ClusterFailedCount),
		},
		StandardRiskSummary: models.StandardRiskSummary{
			Total:      int(lastCheckJob.StandardCount),
			NotPassed:  int(lastCheckJob.StandardUnpassedCount),
			Passed:     int(lastCheckJob.StandardPassedCount),
			NotChecked: int(lastCheckJob.StandardUncheckedCount),
		},
		CheckRiskSummary: models.CheckRiskSummary{
			Critical:  int(lastCheckJob.CheckUnpassedCriticalCount),
			High:      int(lastCheckJob.CheckUnpassedHighCount),
			Medium:    int(lastCheckJob.CheckUnpassedMediumCount),
			Low:       int(lastCheckJob.CheckUnpassedLowCount),
			NotPassed: int(lastCheckJob.CheckCount - lastCheckJob.CheckPassedCount),
			Total:     int(lastCheckJob.CheckCount),
		},
		Status:         resultStatus,
		LastCheckJobID: lastCheckJob.ID,
		LastCheckTime:  lo.If(lastCheckJob.CreateTime.IsZero(), "").Else(lastCheckJob.CreateTime.Format(time.DateTime)),
	}, nil
}

func (s *strategyHandler) getClusterStandardsMap(ctx context.Context, strategyID int64, clusterNames []string) (map[string][]*caas.BaselineStandard, error) {
	tx := s.db.WithContext(ctx).Model(&caas.BaselineStrategyStandard{}).Where("strategy_id = ?", strategyID)
	if len(clusterNames) > 0 {
		tx = tx.Where("cluster_name in (?)", clusterNames)
	}
	var clusterStandards []caas.BaselineStrategyStandard
	tx.Find(&clusterStandards)

	standardIds := sets.New[int64]()
	for _, clusterStandard := range clusterStandards {
		standardIds.Insert(clusterStandard.StandardID)
	}
	var standards []*caas.BaselineStandard
	if err := s.db.WithContext(ctx).Model(&caas.BaselineStandard{}).Where("id in (?)", standardIds.UnsortedList()).Find(&standards).Error; err != nil {
		return nil, err
	}
	standardIdMap := make(map[int64]*caas.BaselineStandard)
	for _, standard := range standards {
		standardIdMap[standard.ID] = standard
	}
	clusterStandardsMap := make(map[string][]*caas.BaselineStandard)
	for _, clusterStandard := range clusterStandards {
		standard, ok := standardIdMap[clusterStandard.StandardID]
		if !ok {
			continue
		}
		clusterStandardsMap[clusterStandard.ClusterName] = append(clusterStandardsMap[clusterStandard.ClusterName], standard)
	}
	return clusterStandardsMap, nil
}

func (s *strategyHandler) GetClusterCheckResults(ctx context.Context, req *models.GetClusterCheckResultsRequest) (*models.GetClusterCheckResultsResponse, error) {
	// 1. GetStrategyByID
	strategy, err := s.getStrategyByID(ctx, req.StrategyID)
	if err != nil {
		return nil, err
	}
	startTime := strategy.CreateTime
	lastJob, err := s.getLastStrategyJob(ctx, req.StrategyID)
	if err != nil {
		return nil, err
	}
	if !lastJob.CreateTime.IsZero() {
		startTime = lastJob.CreateTime
	}
	clusterNames := strings.Split(strategy.ClusterNames, ",")
	if req.ClusterName != "" {
		clusterNames = []string{req.ClusterName}
	}

	// 2. find standards job in caas.BaselineStandardJob
	var jobs []caas.BaselineStandardJob
	if err := s.db.WithContext(ctx).
		Model(&caas.BaselineStandardJob{}).
		Where("strategy_job_id", lastJob.ID).
		Where("cluster_name in (?)", clusterNames).
		Where("create_time >= ?", startTime).
		Find(&jobs).Error; err != nil {
		return nil, err
	}
	var strategyStandard []caas.BaselineStrategyStandard
	if err := s.db.WithContext(ctx).
		Model(&caas.BaselineStrategyStandard{}).
		Where("strategy_id = ?", req.StrategyID).
		Find(&strategyStandard).Error; err != nil {
		return nil, err
	}
	standardIds := lo.Map(strategyStandard, func(job caas.BaselineStrategyStandard, _ int) int {
		return int(job.StandardID)
	})
	clusterStrategyStandards := make(map[string][]caas.BaselineStrategyStandard, len(strategyStandard))
	for i := range strategyStandard {
		standard := strategyStandard[i]
		clusterStrategyStandards[standard.ClusterName] = append(clusterStrategyStandards[standard.ClusterName], standard)
	}
	for _, clusterName := range clusterNames {
		if _, ok := clusterStrategyStandards[clusterName]; !ok {
			clusterStrategyStandards[clusterName] = []caas.BaselineStrategyStandard{}
		} else {
			// sort by clusterStrategyStandard.CreateTime desc
			sort.Slice(clusterStrategyStandards[clusterName], func(i, j int) bool {
				return clusterStrategyStandards[clusterName][i].CreateTime.Before(clusterStrategyStandards[clusterName][j].CreateTime)
			})
		}
	}

	clusterStandardJobMap := lo.SliceToMap(jobs, func(job caas.BaselineStandardJob) (string, caas.BaselineStandardJob) {
		return fmt.Sprintf("%s/%d", job.ClusterName, job.StandardID), job
	})

	var checkJobs []*caas.BaselineStandardRuleJob
	if err := s.db.WithContext(ctx).Model(&caas.BaselineStandardRuleJob{}).
		Where("strategy_id = ?", req.StrategyID).
		Where("strategy_job_id = ? ", lastJob.ID).
		Where("cluster_name in (?)", clusterNames).
		Where("standard_id in (?)", standardIds).
		Where("create_time >= ?", startTime).
		Find(&checkJobs).Error; err != nil {
		return nil, err
	}
	checkJobGroupByStandardJobId := lo.GroupBy(checkJobs, func(item *caas.BaselineStandardRuleJob) int64 {
		return item.StandardJobID
	})

	var standardRules []*caas.BaselineStandardRule
	if err := s.db.WithContext(ctx).Model(&caas.BaselineStandardRule{}).
		Where("standard_id in (?)", standardIds).
		Find(&standardRules).Error; err != nil {
		return nil, err
	}

	standardRulesGroupByStandardId := lo.GroupBy(standardRules, func(item *caas.BaselineStandardRule) int64 {
		return item.StandardID
	})

	clusterStandardsMap, err := s.getClusterStandardsMap(ctx, req.StrategyID, clusterNames)
	if err != nil {
		return nil, err
	}
	var clusterItems []*models.ClusterCheckResultItem
	for _, clusterName := range clusterNames {
		item := &models.ClusterCheckResultItem{
			ClusterName: clusterName,
			Status:      models.ResultStatusUnchecked,
			Progress:    0,
		}
		clusterStandards, exist := clusterStandardsMap[clusterName]
		if exist && len(clusterStandards) > 0 {
			uncheckedCnt := 0
			passedCheckCnt := 0
			totalCheckCnt := 0
			checkingCnt := 0
			clusterFailedCnt := 0
			for i := range clusterStandards {
				clusterStandard := clusterStandards[i]
				totalCheckCnt += len(standardRulesGroupByStandardId[clusterStandard.ID])
				key := fmt.Sprintf("%s/%d", clusterName, clusterStandard.ID)
				job, ok := clusterStandardJobMap[key]
				if !ok {
					uncheckedCnt++
				} else {
					if job.UpdateTime.Before(clusterStandard.UpdateTime) {
						checkJobs := checkJobGroupByStandardJobId[job.ID]
						checkItems := standardRulesGroupByStandardId[job.StandardID]
						if err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
							needChanged, err := helper.CheckIfNeedUpdateStandardJobSummary(ctx, &job, checkJobs, checkItems)
							if err != nil {
								return err
							}
							if needChanged {
								if err := helper.UpdateStandardJobSummary(ctx, tx, &job, checkJobs, checkItems); err != nil {
									return err
								}
							}
							return nil
						}); err != nil {
							s.log.Error("update standard job summary error", zap.Error(err),
								zap.Any("standard-job-id", job.ID), zap.Any("standard-id", clusterStandard.ID))
							return nil, fmt.Errorf("standard has changed, but update standard job summary error: %w", err)
						}

					}
					//totalCheckCnt += int(job.CheckCount)
					switch models.JobStatus(job.Status) {
					case models.JobStatusRunning, models.JobStatusPending:
						checkingCnt++
					case models.JobStatusCompleted:
						passedCheckCnt += int(job.PassedCount)
						if !job.Passed {
							if job.Reason == models.JobReasonClusterError {
								clusterFailedCnt++
								item.Message = job.Reason + "," + job.Message
							}
						}
					default:
						uncheckedCnt++
					}
				}
			}
			if clusterFailedCnt > 0 {
				item.Status = models.ResultStatusFailed
			} else if checkingCnt > 0 {
				item.Status = models.ResultStatusChecking
			} else if uncheckedCnt == len(clusterStandards) {
				item.Status = models.ResultStatusUnchecked
			} else {
				item.Status = models.ResultStatusPassed
				if totalCheckCnt != 0 {
					item.Progress = int((float64(passedCheckCnt) / float64(totalCheckCnt)) * 100)
				}
			}
		} else {
			item.Status = models.ResultStatusUnchecked
		}
		clusterItems = append(clusterItems, item)
	}
	var statusOrder = map[models.ResultStatus]int{
		models.ResultStatusChecking:  0,
		models.ResultStatusFailed:    1,
		models.ResultStatusPassed:    2,
		models.ResultStatusUnchecked: 3,
	}

	lessFunc := func(a, b *models.ClusterCheckResultItem) bool {
		if req.Filter.SortName == "riskInfo" {
			riskLessFunc := func() bool {
				if statusOrder[a.Status] != statusOrder[b.Status] {
					return statusOrder[a.Status] < statusOrder[b.Status]
				}
				if a.Progress != b.Progress {
					return a.Progress < b.Progress
				}
				return a.ClusterName < b.ClusterName
			}
			return lo.If(req.Filter.SortOrder == resources.SortOrderAsc, !riskLessFunc()).Else(riskLessFunc())
		} else {
			aList, bList := clusterStrategyStandards[a.ClusterName], clusterStrategyStandards[b.ClusterName]
			aTime, bTime := time.Time{}, time.Time{}
			if len(aList) > 0 {
				aTime = aList[0].CreateTime
			}
			if len(bList) > 0 {
				bTime = bList[0].CreateTime
			}
			if aTime != bTime {
				return aTime.Before(bTime)
			}
		}
		return a.ClusterName < b.ClusterName
	}
	sort.SliceStable(clusterItems, func(i, j int) bool {
		return lessFunc(clusterItems[i], clusterItems[j])
	})

	resp, err := req.Filter.FilterResult(clusterItems)
	if err != nil {
		return nil, err
	}

	return &models.GetClusterCheckResultsResponse{
		PageableResponse: *resp,
	}, nil
}

// listStandardIDsByStrategyID 获取基线标准id
func (s *strategyHandler) listStandardIDsByStrategyID(ctx context.Context, strategyID int64) ([]int, error) {
	var standards []caas.BaselineStrategyStandard
	if err := s.db.WithContext(ctx).Model(&caas.BaselineStrategyStandard{}).Select("distinct(standard_id)").
		Where("strategy_id = ?", strategyID).
		Find(&standards).Error; err != nil {
		return nil, err
	}

	var standardIds []int
	for _, standard := range standards {
		standardIds = append(standardIds, int(standard.StandardID))
	}
	return sets.New(standardIds...).UnsortedList(), nil
}

// GetBaselineStandardCheckResults 获取基线标准检查结果
func (s *strategyHandler) GetBaselineStandardCheckResults(ctx context.Context, req *models.GetBaselineStandardCheckResultsRequest) (*models.GetBaselineStandardCheckResultsResponse, error) {
	// 1. GetStrategyByID
	strategy, err := s.getStrategyByID(ctx, req.StrategyID)
	if err != nil {
		return nil, err
	}
	startTime := strategy.CreateTime
	lastJob, err := s.getLastStrategyJob(ctx, strategy.ID)
	if err != nil {
		return nil, err
	}
	if !lastJob.CreateTime.IsZero() {
		startTime = lastJob.CreateTime
	}

	// 2. find standards job in caas.BaselineStandardJob
	var jobs []caas.BaselineStandardJob
	if err := s.db.WithContext(ctx).Model(&caas.BaselineStandardJob{}).
		Where("strategy_id = ?", req.StrategyID).
		Where("strategy_job_id = ? ", lastJob.ID).
		Where("cluster_name in (?) ", strings.Split(req.ClusterName, ",")).
		Where("create_time >= ?", startTime).
		Omit("result").
		Find(&jobs).Error; err != nil {
		return nil, err
	}

	var strategyStandards []caas.BaselineStrategyStandard
	if err := s.db.WithContext(ctx).Model(&caas.BaselineStrategyStandard{}).
		Where("strategy_id = ?", req.StrategyID).
		Select("standard_id, MIN(create_time) AS create_time").
		Group("standard_id").
		Order("create_time asc").
		Find(&strategyStandards).Error; err != nil {
		return nil, err
	}

	strategyStandardIdMap := map[int64]caas.BaselineStrategyStandard{}
	for _, ss := range strategyStandards {
		// if exist skip
		if _, ok := strategyStandardIdMap[ss.StandardID]; ok {
			continue
		}
		strategyStandardIdMap[ss.StandardID] = ss
	}

	var checkJobs []*caas.BaselineStandardRuleJob
	if err := s.db.WithContext(ctx).Model(&caas.BaselineStandardRuleJob{}).
		Where("strategy_id = ?", req.StrategyID).
		Where("strategy_job_id = ? ", lastJob.ID).
		Where("cluster_name in (?)  ", strings.Split(req.ClusterName, ",")).
		Where("create_time >= ?", startTime).
		Find(&checkJobs).Error; err != nil {
		return nil, err
	}

	checkJobGroupByStandardJobId := lo.GroupBy(checkJobs, func(item *caas.BaselineStandardRuleJob) int64 {
		return item.StandardJobID
	})

	standardIds, err := s.listStandardIDsByStrategyID(ctx, req.StrategyID)
	if err != nil {
		return nil, err
	}
	// 3. find standards
	var standards []*caas.BaselineStandard
	if err := s.db.WithContext(ctx).Model(&caas.BaselineStandard{}).Where("id in (?)", standardIds).Find(&standards).Error; err != nil {
		return nil, err
	}

	var standardRules []*caas.BaselineStandardRule
	if err := s.db.WithContext(ctx).Model(&caas.BaselineStandardRule{}).
		Where("standard_id in (?)", standardIds).
		Find(&standardRules).Error; err != nil {
		return nil, err
	}

	standardRulesGroupByStandardId := lo.GroupBy(standardRules, func(item *caas.BaselineStandardRule) int64 {
		return item.StandardID
	})

	standardJobMap := lo.SliceToMap(jobs, func(job caas.BaselineStandardJob) (string, caas.BaselineStandardJob) {
		return fmt.Sprintf("%d", job.StandardID), job
	})
	var standardItems []*models.StandardCheckResultItem
	for _, standard := range standards {
		job, ok := standardJobMap[fmt.Sprintf("%d", standard.ID)]
		if ok {
			if err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
				checkJobs := checkJobGroupByStandardJobId[job.ID]
				checkItems := standardRulesGroupByStandardId[standard.ID]
				if needChanged, err := helper.CheckIfNeedUpdateStandardJobSummary(ctx, &job, checkJobs, checkItems); err != nil {
					s.log.Error("check if need update standard job summary error", zap.Error(err),
						zap.Any("standard-job-id", job.ID), zap.Any("standard-id", standard.ID))
				} else {
					if needChanged {
						if err := helper.UpdateStandardJobSummary(ctx, tx, &job, checkJobs, checkItems); err != nil {
							s.log.Error("update standard job summary error", zap.Error(err),
								zap.Any("standard-job-id", job.ID), zap.Any("standard-id", standard.ID))
						}
					}
				}
				return nil
			}); err != nil {
				s.log.Error("update standard job summary error", zap.Error(err),
					zap.Any("standard-job-id", job.ID), zap.Any("standard-id", standard.ID))
			}
		}
		item := &models.StandardCheckResultItem{
			ID: standard.ID,

			Name:        standard.Name,
			ClusterName: req.ClusterName,
			CheckRiskSummary: models.CheckRiskSummary{
				Critical: 0,
				High:     int(job.CheckUnpassedHighCount),
				Medium:   int(job.CheckUnpassedMediumCount),
				Low:      int(job.CheckUnpassedLowCount),
				Total:    int(job.CheckCount),
			},
			PassedCount:    int(job.PassedCount),
			Reason:         job.Reason,
			Message:        job.Message,
			BaselineName:   job.BaselineName,
			LastCheckJobID: job.ID,
			LastCheckTime: lo.If(job.CreateTime.IsZero(), "").
				Else(job.CreateTime.Format(time.DateTime)),
			LastCompletedTime: lo.If(job.CompletedTime.IsZero(), "").
				Else(job.CompletedTime.Format(time.DateTime)),
		}
		if !ok {
			item.Status = models.ResultStatusUnchecked
			item.CheckRiskSummary.Total = len(standardRulesGroupByStandardId[standard.ID])
			standardItems = append(standardItems, item)
			continue
		}
		switch models.JobStatus(job.Status) {
		case models.JobStatusRunning, models.JobStatusPending:
			item.Status = models.ResultStatusChecking
		case models.JobStatusCompleted:
			if job.Passed {
				item.Status = models.ResultStatusPassed
			} else {
				item.Status = models.ResultStatusFailed
			}
		default:
			item.Status = models.ResultStatusUnchecked
		}
		standardItems = append(standardItems, item)
	}

	var statusOrder = map[models.ResultStatus]int{
		models.ResultStatusChecking:  0,
		models.ResultStatusFailed:    1,
		models.ResultStatusPassed:    2,
		models.ResultStatusUnchecked: 3,
	}

	lessRiskFnc := func(a, b *models.StandardCheckResultItem) bool {
		if statusOrder[a.Status] != statusOrder[b.Status] {
			return statusOrder[a.Status] < statusOrder[b.Status]
		}
		if a.CheckRiskSummary.Critical != b.CheckRiskSummary.Critical {
			return a.CheckRiskSummary.Critical > b.CheckRiskSummary.Critical
		}
		if a.CheckRiskSummary.High != b.CheckRiskSummary.High {
			return a.CheckRiskSummary.High > b.CheckRiskSummary.High
		}
		if a.CheckRiskSummary.Medium != b.CheckRiskSummary.Medium {
			return a.CheckRiskSummary.Medium > b.CheckRiskSummary.Medium
		}
		if a.CheckRiskSummary.Low != b.CheckRiskSummary.Low {
			return a.CheckRiskSummary.Low > b.CheckRiskSummary.Low
		}
		if a.PassedCount != b.PassedCount {
			return a.PassedCount > b.PassedCount
		}
		return a.Name < b.Name
	}

	lessFunc := func(a, b *models.StandardCheckResultItem) bool {
		if req.Filter.SortName == "riskInfo" {
			return lo.If(req.Filter.SortOrder == "asc", !lessRiskFnc(a, b)).Else(lessRiskFnc(b, a))
		} else {
			aTime, bTime := time.Time{}, time.Time{}
			if standard, ok := strategyStandardIdMap[a.ID]; ok {
				aTime = standard.CreateTime
			}
			if standard, ok := strategyStandardIdMap[b.ID]; ok {
				bTime = standard.CreateTime
			}
			if aTime != bTime {
				return aTime.Before(bTime)
			}
		}
		return a.Name < b.Name
	}

	sort.SliceStable(standardItems, func(i, j int) bool {
		a := standardItems[i]
		b := standardItems[j]
		return lessFunc(a, b)
	})

	resp, err := req.Filter.FilterResult(standardItems)
	if err != nil {
		return nil, err
	}

	return &models.GetBaselineStandardCheckResultsResponse{PageableResponse: *resp}, nil
}

// GetCheckResults 获取基线标准检查结果
func (s *strategyHandler) GetCheckResults(ctx context.Context, req *models.GetCheckResultsRequest) (*models.GetCheckResultsResponse, error) {

	// 1. GetStrategyByID
	strategy, err := s.getStrategyByID(ctx, req.StrategyID)
	if err != nil {
		return nil, err
	}
	startTime := strategy.CreateTime
	lastJob, err := s.getLastStrategyJob(ctx, strategy.ID)
	if err != nil {
		return nil, err
	}
	if !lastJob.CreateTime.IsZero() {
		startTime = lastJob.CreateTime
	}
	var standardJob caas.BaselineStandardJob
	if err := s.db.WithContext(ctx).Model(&caas.BaselineStandardJob{}).
		Where("strategy_job_id = ?", lastJob.ID).
		Where("strategy_id = ?", req.StrategyID).
		Where("standard_id = ?", req.StandardID).
		Where("cluster_name = ?", req.ClusterName).
		First(&standardJob).Error; err != nil {
		if err != gorm.ErrRecordNotFound {
			return nil, err
		}
	}

	// 2. find standards job in caas.BaselineStandardRuleJob
	var jobs []caas.BaselineStandardRuleJob
	if err := s.db.WithContext(ctx).Model(&caas.BaselineStandardRuleJob{}).
		Where("strategy_id = ?", req.StrategyID).
		Where("strategy_job_id = ?", lastJob.ID).
		Where("standard_id = ?", req.StandardID).
		Where("standard_job_id = ?", standardJob.ID).
		Where("cluster_name = ?", req.ClusterName).
		Where("create_time >= ?", startTime).
		Find(&jobs).Error; err != nil {
		return nil, err
	}
	if len(jobs) == 0 {
		// find last standard job
		var lastStandardJob caas.BaselineStandardJob
		if err := s.db.WithContext(ctx).Model(&caas.BaselineStandardJob{}).
			Where("strategy_id = ?", req.StrategyID).
			Where("standard_id = ?", req.StandardID).
			Where("cluster_name = ?", req.ClusterName).
			Order("create_time desc").
			First(&lastStandardJob).Error; err != nil {
			if err != gorm.ErrRecordNotFound {
				return nil, err
			}
		}
		if err := s.db.WithContext(ctx).Model(&caas.BaselineStandardRuleJob{}).
			Where("standard_job_id = ?", lastStandardJob.ID).
			Where("standard_id = ?", req.StandardID).
			Where("strategy_job_id = ?", lastJob.ID).
			Where("strategy_id = ?", req.StrategyID).
			Where("cluster_name = ?", req.ClusterName).
			Order("create_time desc").
			Find(&jobs).Error; err != nil {
			return nil, err
		}
	}
	jobMap := lo.SliceToMap(jobs, func(job caas.BaselineStandardRuleJob) (string, caas.BaselineStandardRuleJob) {
		return fmt.Sprintf("%d-%s-%d", job.StrategyID, job.ClusterName, job.StandardRuleID), job
	})

	// 3. find rules
	var standardRules []caas.BaselineStandardRule
	if err := s.db.WithContext(ctx).Model(&caas.BaselineStandardRule{}).Where("standard_id = ?", req.StandardID).Find(&standardRules).Error; err != nil {
		return nil, err
	}
	var rules []caas.BaselineRule
	checkIds := lo.Map(standardRules, func(item caas.BaselineStandardRule, _ int) int64 { return item.RuleID })
	if err := s.db.WithContext(ctx).Model(&caas.BaselineRule{}).Where("id in (?)", checkIds).Find(&rules).Error; err != nil {
		return nil, err
	}
	ruleMap := lo.SliceToMap(rules, func(rule caas.BaselineRule) (string, caas.BaselineRule) {
		return fmt.Sprintf("%d", rule.ID), rule
	})
	var ruleItems []*models.CheckResultItem

	for _, standardRule := range standardRules {
		key := fmt.Sprintf("%d-%s-%d", req.StrategyID, req.ClusterName, standardRule.ID)
		job, ok := jobMap[key]
		rule := ruleMap[fmt.Sprintf("%d", standardRule.RuleID)]
		item := &models.CheckResultItem{
			ID:             standardRule.ID,
			Name:           rule.Name,
			Description:    rule.Description,
			Suggestion:     rule.Suggestion,
			StrategyID:     req.StrategyID,
			CheckID:        standardRule.RuleID,
			StandardID:     standardRule.StandardID,
			ClusterName:    req.ClusterName,
			RiskLevel:      rule.RiskLevel,
			Status:         models.ResultStatusUnchecked,
			Reason:         job.Reason,
			Message:        job.Message,
			CheckerName:    job.CheckerName,
			MonitorName:    job.MonitorName,
			LastCheckJobID: job.ID,
			LastCheckTime: lo.If(job.CreateTime.IsZero(), "").
				Else(job.CreateTime.Format(time.DateTime)),
			LastCompletedTime: lo.If(job.CompletedTime.IsZero(), "").
				Else(job.CompletedTime.Format(time.DateTime)),
		}
		if !ok {
			item.Status = models.ResultStatusUnchecked
			ruleItems = append(ruleItems, item)
			continue
		}
		if helper.CheckIfReasonCauseError(job.Reason) {
			item.Status = models.ResultStatusError
		} else {
			switch models.JobStatus(job.Status) {
			case models.JobStatusRunning, models.JobStatusPending:
				item.Status = models.ResultStatusChecking
			case models.JobStatusCompleted:
				if job.Passed {
					item.Status = models.ResultStatusPassed
				} else {
					item.Status = models.ResultStatusFailed
				}
			default:
				item.Status = models.ResultStatusUnchecked
			}
		}
		ruleItems = append(ruleItems, item)
	}
	sort.Slice(ruleItems, func(i, j int) bool {
		before := ruleItems[i]
		after := ruleItems[j]
		if before.Status != after.Status {
			return before.Status < after.Status
		}
		return before.ID > after.ID
	})
	filteredResp, err := req.Filter.FilterResult(ruleItems)
	if err != nil {
		return nil, err
	}

	return &models.GetCheckResultsResponse{PageableResponse: *filteredResp}, nil

}

// DownloadBaselineStandardCheckResultsReport ...
func (s *strategyHandler) DownloadBaselineStandardCheckResultsReport(ctx context.Context, req *models.DownloadBaselineStandardCheckResultsReportRequest) (*models.DownloadBaselineStandardCheckResultsReportResponse, error) {
	dataReq := models.GetBaselineStandardCheckResultsRequest(*req)
	results, err := s.GetBaselineStandardCheckResults(ctx, &dataReq)
	if err != nil {
		return nil, err
	}
	resp, err := helper.GenerateBaselineStandardCheckResultsReport(ctx, results.Items)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// DownloadCheckResultsReport ...
func (s *strategyHandler) DownloadCheckResultsReport(ctx context.Context, req *models.DownloadCheckResultsReportRequest) (*models.DownloadCheckResultsReportResponse, error) {
	dataReq := models.GetCheckResultsRequest(*req)
	results, err := s.GetCheckResults(ctx, &dataReq)
	if err != nil {
		return nil, err
	}
	resp, err := helper.GenerateCheckResultsReport(ctx, results.Items)
	if err != nil {
		return nil, err
	}
	return resp, nil
}

// UpdateBindingBaselineStandards implements StrategyInterface.
func (s *strategyHandler) UpdateBindingBaselineStandards(ctx context.Context, req *models.UpdateBindingBaselineStandardsRequest) (*models.UpdateBindingBaselineStandardsResponse, error) {

	// check strategy existed
	if err := s.db.WithContext(ctx).Model(&caas.BaselineStrategy{}).Where("strategy_id = ?").Find(&caas.BaselineStrategy{}).Error; err != nil {
		return nil, fmt.Errorf(" found strategy error: %w", err)
	}

	var resp models.UpdateBindingBaselineStandardsResponse
	if err := s.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		tx = tx.Where("strategy_id = ?", req.StrategyID)
		if len(req.UnbindStandardIDs) > 0 {
			tx = tx.Where("standard_id in (?)", req.UnbindStandardIDs)
		}
		if err := tx.Delete(&caas.BaselineStrategyStandard{}).Error; err != nil {
			return err
		}
		return nil
	}); err != nil {
		return nil, err
	}
	resp.UnbindStandardIDs = req.UnbindStandardIDs
	return &resp, nil
}
