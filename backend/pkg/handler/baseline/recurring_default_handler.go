package baseline

import (
	"context"
	"fmt"
	"sort"
	"strconv"
	"sync"
	"time"

	rds "github.com/redis/go-redis/v9"
	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline/helper"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
)

const recurringJobLockKeyPrefix = "baseline::recurringjob::lock::"

type internalEntry struct {
	entryId         cron.EntryID
	expression      string
	executionConfig *models.ExecutionConfig
}

type DefaultRecurringHandler struct {
	log                *zap.Logger
	rds                *rds.Client
	lockTTL            time.Duration
	db                 *gorm.DB
	cron               *cron.Cron
	jobMap             map[string]internalEntry
	jobMapMux          sync.Mutex
	strategyJobHandler StrategyJobInterface
}

// NewDefaultRecurringHandler 创建新的定时任务处理器
// cron require support seconds
func NewDefaultRecurringHandler(cron *cron.Cron, db *gorm.DB, rds *rds.Client, strategyJobHandler StrategyJobInterface) *DefaultRecurringHandler {
	return &DefaultRecurringHandler{
		log:                logger.GetLogger().Named("default-recurring-handler"),
		rds:                rds,
		lockTTL:            time.Minute * 5,
		db:                 db,
		cron:               cron,
		strategyJobHandler: strategyJobHandler,
		jobMap:             make(map[string]internalEntry),
	}
}

// Get 获取定时任务
func (d *DefaultRecurringHandler) Get(_ context.Context, req *models.GetRecurringJobRequest) (*models.GetRecurringJobResponse, error) {
	d.jobMapMux.Lock()
	defer d.jobMapMux.Unlock()

	strategyKey := fmt.Sprint(req.StrategyID)
	if entry, exists := d.jobMap[strategyKey]; exists {
		cronEntry := d.cron.Entry(entry.entryId)
		if cronEntry.ID == entry.entryId {
			return &models.GetRecurringJobResponse{
				JobID:           int64(entry.entryId),
				Existed:         true,
				ExecutionConfig: entry.executionConfig,
			}, nil
		}
		// 如果cron中不存在该任务，清理本地映射
		delete(d.jobMap, strategyKey)
	}

	return &models.GetRecurringJobResponse{
		JobID:   0,
		Existed: false,
	}, nil
}

// Add 添加定时任务
func (d *DefaultRecurringHandler) Add(ctx context.Context, req *models.AddRecurringJobRequest) (*models.AddRecurringJobResponse, error) {
	d.jobMapMux.Lock()
	defer d.jobMapMux.Unlock()

	strategyKey := fmt.Sprint(req.StrategyID)
	// 检查是否已存在有效任务
	if entry, exists := d.jobMap[strategyKey]; exists {
		if cronEntry := d.cron.Entry(entry.entryId); cronEntry.ID == entry.entryId {
			return &models.AddRecurringJobResponse{JobId: fmt.Sprint(entry.entryId)}, nil
		}
		// 清理无效任务
		delete(d.jobMap, strategyKey)
	}
	d.log.Info("start to add recurring job", zap.Any("strategy-id", req.StrategyID))
	strategy, err := helper.GetStrategyByID(ctx, d.db, req.StrategyID)
	if err != nil {
		return nil, err
	}

	executionConfig := helper.ConvertBaselineStrategyToExecutionConfig(ctx, strategy)
	expression := helper.ConvertExecutionConfigToCronExpressionWithSeconds(executionConfig)
	if expression == "" {
		if executionConfig.ExecutionType == models.ExecutionTypeImmediate {
			// if execution type is immediate, return success
			return &models.AddRecurringJobResponse{}, nil
		}
	}

	d.log.Debug("adding recurring job",
		zap.Int64("strategy-id", req.StrategyID),
		zap.String("expression", expression))

	entryID, err := d.cron.AddFunc(expression, func() {
		log := d.log.With(zap.Int64("strategy-id", req.StrategyID))
		log.Info("start to run recurring job")

		// 检查策略是否启用
		strategy, err := helper.GetStrategyByID(ctx, d.db, req.StrategyID)
		if err != nil {
			log.Error("failed to get strategy", zap.Error(err))
			return
		}
		if strategy.Enabled != 1 {
			log.Info("strategy is disabled")
			return
		}
		switch executionConfig.ExecutionType {
		case models.ExecutionTypeScheduled:
			yearTime, err := helper.ParseTime(executionConfig.ExecutionDate)
			if err != nil {
				log.Error("failed to parse execution time", zap.Error(err), zap.String("execution-date", executionConfig.ExecutionDate))
				return
			}
			if yearTime.Year() != time.Now().Year() {
				log.Info("scheduled job not match current year", zap.String("execution-date", executionConfig.ExecutionDate))
				return
			}
			for i := 0; i < 60; i++ {
				if i < yearTime.Second()-1 {
					i++
					time.Sleep(1 * time.Second)
				} else {
					break
				}
			}
		case models.ExecutionTypeRecurring:
			loc := time.Now().Location()
			startTime, err := time.ParseInLocation(time.DateTime, executionConfig.StartTime, loc)
			if err != nil {
				log.Error("failed to parse execution time", zap.Error(err))
				return
			}
			endTime, err := time.ParseInLocation(time.DateTime, executionConfig.EndTime, loc)
			if err != nil {
				log.Error("failed to parse end time", zap.Error(err))
				return
			}
			// todo: 判断时区，默认先东八区
			now := time.Now()
			log.Info("get now, startTime , endTime, ", zap.Any("now", now.Format(time.RFC3339)),
				zap.Any("start", startTime.Format(time.RFC3339)), zap.Any("end", endTime.Format(time.RFC3339)),
				zap.Any("loc", loc))
			if now.Before(startTime) || now.After(endTime) {
				log.Info("recurring job not match current time", zap.String("current-time", now.Format(time.DateTime)), zap.String("start-time", executionConfig.StartTime), zap.String("end-time", executionConfig.EndTime))
				return
			}
		}
		lockKey := fmt.Sprintf("%s%d", recurringJobLockKeyPrefix, req.StrategyID)
		lockAcquired, err := d.acquireLock(lockKey)
		if err != nil {
			log.Error("failed to acquire lock", zap.Error(err))
			return
		}
		if !lockAcquired {
			log.Info("another job instance is running")
			return
		}
		defer d.releaseLock(lockKey)

		// 执行任务
		if _, err := d.strategyJobHandler.ExecuteJob(ctx, &models.ExecuteCheckJobRequest{
			StrategyID: req.StrategyID,
			Scope:      models.ExecuteCheckJobScopeStrategy,
		}); err != nil {
			log.Error("failed to execute job", zap.Error(err))
			return
		}
	})

	if err != nil {
		return nil, err
	}

	d.jobMap[strategyKey] = internalEntry{
		entryId:         entryID,
		expression:      expression,
		executionConfig: executionConfig,
	}

	d.log.Info("added recurring job", zap.Any("strategy-id", req.StrategyID), zap.Any("entry-id", entryID))

	return &models.AddRecurringJobResponse{JobId: fmt.Sprint(entryID)}, nil
}

// Delete 删除定时任务
func (d *DefaultRecurringHandler) Delete(ctx context.Context, req *models.DeleteRecurringJobRequest) (*models.DeleteRecurringJobResponse, error) {
	d.jobMapMux.Lock()
	defer d.jobMapMux.Unlock()
	d.log.Info("start to delete recurring job", zap.Int64("strategy-id", req.StrategyID))

	strategyKey := fmt.Sprint(req.StrategyID)
	if entry, exists := d.jobMap[strategyKey]; exists {
		d.log.Info("removing recurring job", zap.Int64("strategy-id", req.StrategyID))
		d.cron.Remove(entry.entryId)
		delete(d.jobMap, strategyKey)
	}

	return &models.DeleteRecurringJobResponse{}, nil
}

// List 列出所有定时任务
func (d *DefaultRecurringHandler) List(ctx context.Context) (*models.ListRecurringJobResponse, error) {
	var items []models.RecurringJobItem
	entries := d.cron.Entries()

	// 只返回cron中实际存在的任务
	for strategyID, entry := range d.jobMap {
		for _, cronEntry := range entries {
			if cronEntry.ID == entry.entryId {
				items = append(items, models.RecurringJobItem{
					ID:             strategyID,
					CronJobID:      fmt.Sprint(entry.entryId),
					CronExpression: entry.expression,
					Next:           cronEntry.Next.Format(time.RFC3339),
					Prev:           cronEntry.Prev.Format(time.RFC3339),
					Config:         entry.executionConfig,
				})
				break
			}
		}
	}
	sort.Slice(items, func(i, j int) bool {
		iNum, err1 := strconv.ParseInt(items[i].ID, 10, 64)
		jNum, err2 := strconv.ParseInt(items[j].ID, 10, 64)
		if err1 == nil && err2 == nil {
			return iNum < jNum
		}
		return items[i].ID < items[j].ID
	})

	return &models.ListRecurringJobResponse{Items: items}, nil
}

// acquireLock 获取分布式锁
func (d *DefaultRecurringHandler) acquireLock(lockKey string) (bool, error) {
	status := d.rds.SetNX(context.Background(), lockKey, "locked", d.lockTTL)
	return status.Val(), status.Err()
}

// releaseLock 释放分布式锁
func (d *DefaultRecurringHandler) releaseLock(lockKey string) {
	d.rds.Del(context.Background(), lockKey)
}
