package baseline

import (
	"context"

	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
)

func NewRecurringK8sHandler() *RecurringK8sHandler {
	return &RecurringK8sHandler{}

}

type RecurringK8sHandler struct {
}

func (r RecurringK8sHandler) Get(ctx context.Context, req *models.GetRecurringJobRequest) (*models.GetRecurringJobResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (r RecurringK8sHandler) Add(ctx context.Context, req *models.AddRecurringJobRequest) (*models.AddRecurringJobResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (r RecurringK8sHandler) Delete(ctx context.Context, req *models.DeleteRecurringJobRequest) (*models.DeleteRecurringJobResponse, error) {
	//TODO implement me
	panic("implement me")
}

func (r RecurringK8sHandler) List(ctx context.Context) (*models.ListRecurringJobResponse, error) {
	//TODO implement me
	panic("implement me")
}
