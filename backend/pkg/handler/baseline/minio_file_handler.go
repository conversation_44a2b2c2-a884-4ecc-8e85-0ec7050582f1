package baseline

import (
	"bytes"
	"context"
	"crypto/md5"
	"encoding/hex"
	"errors"
	"fmt"
	"io"
	"path/filepath"
	"time"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/database"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/appmanagement"
)

// NewMinioFileHandler initializes and returns a MinioFileHandler instance.
func NewMinioFileHandler() MinioFileHandler {
	client, err := minio.New(config.MinioEndpoint.Value, &minio.Options{
		Creds:  credentials.NewStaticV4(config.MinioAccessKey.Value, config.MinioSecretKey.Value, ""),
		Secure: false,
	})
	if err != nil {
		panic(fmt.Sprintf("failed to initialize Minio client: %v", err))
	}

	return MinioFileHandler{
		client: client,
		ampDB:  database.AmpDB,
		bucket: constants.MinioBaselineBucketName,
		prefix: config.MinioURLPrefix.Value,
		log:    logger.GetSugared().Named("minio-file-handler"),
	}
}

// MinioFileHandler is responsible for handling file uploads and metadata.
type MinioFileHandler struct {
	client *minio.Client
	ampDB  *gorm.DB
	bucket string
	prefix string
	log    *zap.SugaredLogger
}

// UploadCheckerFile uploads a file to MinIO and manages file records in the database.
func (m MinioFileHandler) UploadCheckerFile(ctx context.Context, req *models.UploadCheckerFileRequest) (*models.UploadCheckerFileResponse, error) {
	fileName := req.FileName
	fileSize := int64(len(req.Content))
	if req.File != nil {
		fileName = req.File.Filename
		fileSize = req.File.Size
	}
	// Generate MD5 hash for a unique file name
	md5Name := generateMD5Name(fileName)
	var reader io.ReadCloser
	if req.File != nil {
		reader, err := req.File.Open()
		if err != nil {
			return nil, fmt.Errorf("failed to open file: %w", err)
		}
		defer reader.Close()
	} else {
		reader = io.NopCloser(bytes.NewReader(req.Content))
		defer reader.Close()
	}

	// Upload file to MinIO
	link, err := m.uploadFile(ctx, reader, md5Name)
	if err != nil {
		return nil, fmt.Errorf("file upload failed: %w", err)
	}

	// Add or update file record in the database
	fileResult, err := m.addFileRecord(md5Name, fileName, m.bucket, link, fileSize)
	if err != nil {
		return nil, fmt.Errorf("failed to add file record: %w", err)
	}

	// Build response
	return &models.UploadCheckerFileResponse{
		FileItem: models.FileItem{
			Id:        fileResult.Id,
			Name:      fileResult.Name,
			Path:      fileResult.Path,
			UniqueKey: fileResult.UniqueKey,
			Link:      fileResult.Link,
		},
	}, nil
}

// uploadFile uploads a file to MinIO and returns the file's link.
func (m MinioFileHandler) uploadFile(ctx context.Context, reader io.Reader, md5Name string) (string, error) {
	// get reader size
	readerSize, err := io.ReadAll(reader)
	if err != nil {
		return "", fmt.Errorf("failed to read file: %w", err)
	}

	_, err = m.client.PutObject(ctx, m.bucket, md5Name, bytes.NewReader(readerSize), int64(len(readerSize)), minio.PutObjectOptions{})
	if err != nil {
		return "", fmt.Errorf("failed to upload file to MinIO: %w", err)
	}

	link := fmt.Sprintf("%s/%s/%s", m.prefix, m.bucket, md5Name)
	return link, nil
}

// addFileRecord adds or updates a file record in the database.
func (m MinioFileHandler) addFileRecord(uniqueKey, filename, bucketName, link string, size int64) (*appmanagement.SysFile, error) {
	tx := m.ampDB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
			m.log.Errorw("transaction rollback due to panic", "reason", r)
		}
	}()

	// Check if file already exists by unique key and name
	existingFile := new(appmanagement.SysFile)
	if err := tx.Where("path = ? AND unique_key = ?", bucketName, uniqueKey).First(existingFile).Error; err == nil {
		// File with same unique key and name already exists
		return existingFile, nil
	}

	// Check for files with the same name
	var fileNameList []appmanagement.SysFile
	if err := tx.Where("path = ? AND name = ?", bucketName, filename).Find(&fileNameList).Error; err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		tx.Rollback()
		return nil, fmt.Errorf("failed to query files by name: %w", err)
	}

	newFile := &appmanagement.SysFile{
		Path:       bucketName,
		Name:       filename,
		UniqueKey:  uniqueKey,
		Size:       fmt.Sprintf("%d", size),
		Link:       link,
		Version:    1,
		CreateTime: time.Now(),
		UpdateTime: time.Now(),
		DelFlg:     false,
	}

	if len(fileNameList) == 0 {
		// No files with the same name: insert a new record
		if err := tx.Save(newFile).Error; err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("failed to save file record: %w", err)
		}
	} else {
		// File with the same name exists, handle versioning
		latestFile := fileNameList[0]
		for _, file := range fileNameList {
			if file.Version > latestFile.Version {
				latestFile = file
			}
		}
		newFile.Version = latestFile.Version + 1
		if err := tx.Save(newFile).Error; err != nil {
			tx.Rollback()
			return nil, fmt.Errorf("failed to save new version file record: %w", err)
		}
	}

	tx.Commit()
	return newFile, nil
}

// generateMD5Name generates a unique MD5 file name.
func generateMD5Name(filename string) string {
	hash := md5.New()
	hash.Write([]byte(filename + time.Now().String()))
	return hex.EncodeToString(hash.Sum(nil)) + filepath.Ext(filename)
}
