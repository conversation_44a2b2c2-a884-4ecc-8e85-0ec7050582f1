package baseline

import (
	"context"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	app_management "harmonycloud.cn/unifiedportal/portal/backend/pkg/feign/app-management"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
)

func NewAmpFileHandler(ampService app_management.Service) CheckerFileInterface {
	return &AmpFileHandler{
		ampService: &ampService,
	}
}

type AmpFileHandler struct {
	ampService *app_management.Service
}

// UploadCheckerFile upload checker needed file to minio
func (a *AmpFileHandler) UploadCheckerFile(ctx context.Context, req *baseline.UploadCheckerFileRequest) (*baseline.UploadCheckerFileResponse, error) {
	uploadOneReq := app_management.UploadOneFileRequest{
		BucketName: constants.MinioBaselineBucketName,
		File:       req.File,
		Content:    req.Content,
		FileName:   req.FileName,
	}

	resp, err := a.ampService.UploadOneFile(ctx, &uploadOneReq)
	if err != nil {
		return nil, err
	}

	return &baseline.UploadCheckerFileResponse{
		FileItem: baseline.FileItem{
			Id:        resp.Id,
			Path:      resp.Path,
			UniqueKey: resp.UniqueKey,
			Name:      resp.Name,
			Link:      resp.Link,
		},
	}, nil
}
