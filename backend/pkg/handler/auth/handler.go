package auth

import (
	"context"
	"fmt"

	midwareauth "harmonycloud.cn/unifiedportal/midware-go/midwares/auth"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
)

func NewHandler() Handler {
	return &services{}
}

type services struct {
}

func (svc *services) CurrentUser(ctx context.Context) (midwareauth.CurrentUser, error) {
	//todo 处理caas-amp中 redis session 删除后用户退出登录的逻辑
	user, status := midwareauth.GetCurrentUser(ctx)
	switch status {
	case midwareauth.UnLogin:
		return nil, errors.NewFromCode(errors.Var.UserUnLogin)
	case midwareauth.ConvertUserFail:
		return nil, errors.NewFromCode(errors.Var.IllegalUserStruct)
	case midwareauth.NotEffective:
		return nil, errors.NewFromCodeWithMessage(errors.Var.TokenEffective, fmt.Sprintf("token tack effect at %v", user.GetEffectTime().Format("2006-01-02 15:04:05")))
	case midwareauth.Expire:
		return nil, errors.NewFromCodeWithMessage(errors.Var.TokenExpire, fmt.Sprintf("token expired at %v", user.GetEffectTime().Format("2006-01-02 15:04:05")))
	case midwareauth.Success:
		return user, nil
	default:
		return nil, errors.NewFromCodeWithMessage(errors.Var.IllegalUserStatus, fmt.Sprintf("user status code %d is un know", user.GetStatus()))
	}
}
