package common

import (
	"context"
	"sort"
	"time"

	clientmgr "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	cluterclient "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	appsv1 "k8s.io/api/apps/v1"
	coreV1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/meta"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/util/json"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/yaml"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/k8s"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/rbac"
)

var clusterAPIResourceListMap map[string][]*metav1.APIResourceList

var clusterNamespacedAPIResourceListMap map[string][]*metav1.APIResourceList

func init() {
	initAPIResourcesCache()
	go func() {
		handleAPIResourcesCache()
		t := time.NewTicker(5 * time.Minute)
		for {
			select {
			case <-t.C:
				handleAPIResourcesCache()
			}
		}
	}()
}

func NewCommonHandler() CommonIntf {
	return &CommonHandler{}
}

type CommonHandler struct {
}

func (ch *CommonHandler) ListAPIResources(ctx context.Context, p *ListAPIResourceParam) (*models.PageableResponse[unstructured.Unstructured], error) {
	var (
		apiResources []*metav1.APIResourceList
		err          error
	)
	if cluster, err := clientmgr.GetCluster(p.Cluster); err == nil {
		c := cluster.GetClient().GetDiscoveryClient()
		if p.Namespaced {
			if cache, exist := clusterNamespacedAPIResourceListMap[p.Cluster]; exist {
				apiResources = cache
			} else {
				apiResources, err = c.ServerPreferredNamespacedResources()
				if err != nil {
					return nil, errors.NewFromError(ctx, err)
				} else {
					clusterNamespacedAPIResourceListMap[p.Cluster] = apiResources
				}
			}
		} else {
			if cache, exist := clusterAPIResourceListMap[p.Cluster]; exist {
				apiResources = cache
			} else {
				apiResources, err = c.ServerPreferredResources()
				if err != nil {
					return nil, errors.NewFromError(ctx, err)
				} else {
					clusterAPIResourceListMap[p.Cluster] = apiResources
				}
			}
		}
	} else {
		return nil, errors.NewFromError(ctx, err)
	}
	var items []unstructured.Unstructured
	for _, apiResource := range apiResources {
		toUnstructured, err := runtime.DefaultUnstructuredConverter.ToUnstructured(apiResource)
		if err != nil {
			return nil, errors.NewFromError(ctx, err)
		}
		items = append(items, unstructured.Unstructured{Object: toUnstructured})
	}
	filterResult, err := p.Filter.FilterResult(items)
	if err != nil {
		logger.GetSugared().Errorf("filter tenants list fail,err: %v", err)
		return nil, err
	}
	return &models.PageableResponse[unstructured.Unstructured]{
		TotalCount: filterResult.TotalCount,
		Items:      filterResult.Items,
	}, nil
}

func (ch *CommonHandler) AllocatedOFResources(ctx context.Context, p WriterParam) (*models.PageableResponse[unstructured.Unstructured], error) {
	panic("not implement")
	//resp := &models.PageableResponse{
	//	TotalCount: 0,
	//	Items:      []corev1.Namespace{},
	//}
	//
	//resource, err := ch.Get(ctx, &ReadParam{
	//	Cluster:      p.Cluster,
	//	Group:        p.Group,
	//	Version:      p.Version,
	//	Namespace:    p.Namespace,
	//	ResourceType: p.ResourceType,
	//	Name:         p.Name,
	//})
	//if err != nil {
	//	return nil, err
	//}
	//requestResGVK := resource.GetObjectKind().GroupVersionKind()
	//
	//resourceType := multitenant.MappingResourceType(p.ResourceType)
	//if resourceType == "" {
	//	return resp, nil
	//}
	//
	//gvk := multitenant.AllocateResourceAPIGVKList[resourceType]
	//if gvk.Version != requestResGVK.Version || gvk.Kind != requestResGVK.Kind || gvk.Group != requestResGVK.Group {
	//	return resp, nil
	//}
	//
	//options := &client.ListOptions{
	//	LabelSelector: labels.SelectorFromSet(map[string]string{
	//		utils.TenantLabelKey: utils.TenantLabelValue,
	//	}),
	//}
	//tenantList := &corev1.NamespaceList{}
	//if err := ch.client.GetHubCtrlClient().List(ctx, tenantList, options); err != nil {
	//	return nil, errors.NewFromCode(errors.ERROR_LIST_TENANT_FAILED, "")
	//}
	//
	//allocateTenants := make([]models.Tenant, 0)
	//for _, tenant := range tenantList.Items {
	//	allocateClusterResources := multitenant.GetAllocateClusterResources(resourceType, tenant.Annotations)
	//	if resources, exist := allocateClusterResources[p.Cluster]; exist {
	//		if commonutil.InSlice(p.Name, resources) {
	//			allocateTenants = append(allocateTenants, models.Tenant{
	//				Name:        tenant.Name,
	//				DisplayName: convert.GetDisplayName(tenant.ObjectMeta),
	//				Description: convert.GetDescription(tenant.ObjectMeta)},
	//			)
	//		}
	//	}
	//}
	//resp.Items = allocateTenants
	//resp.TotalCount = len(allocateTenants)
	//return resp, nil
}

func handleAPIResourcesCache() {
	var clusters []clientmgr.Cluster
	for i := 0; i <= 5; i++ {
		clusters = clientmgr.ListOnlineClusters()
		if len(clusters) == 0 {
			time.Sleep(time.Second * 5)
		} else {
			break
		}
	}
	for _, cluster := range clusters {
		c := cluster.GetClient().GetDiscoveryClient()
		if apiResources, err := c.ServerPreferredNamespacedResources(); err != nil {
			logger.GetSugared().Errorf("cache cluster:(%s) namespaced apiresources failed,err: %v", cluster, err)
		} else {
			clusterAPIResourceListMap[cluster.GetName()] = apiResources
		}
		if apiResources, err := c.ServerPreferredResources(); err != nil {
			logger.GetSugared().Errorf("cache cluster:(%s) apiresources failed,err: %v", cluster, err)
		} else {
			clusterNamespacedAPIResourceListMap[cluster.GetName()] = apiResources
		}
		logger.GetSugared().Infof("add cluster(%s) apiresources cache", cluster)
	}
}

// convertRawDataToUnstructured ...
func convertRawDataToJSONByContentType(rawData []byte, contentType string) ([]byte, error) {
	var jsonString string
	switch contentType {
	case runtime.ContentTypeYAML:
		yamlToJSON, err := yaml.YAMLToJSON(rawData)
		if err != nil {
			return nil, err
		}
		jsonString = string(yamlToJSON)
	case runtime.ContentTypeJSON:
	default:
		jsonString = string(rawData)
	}
	var obj map[string]any
	if err := json.Unmarshal([]byte(jsonString), &obj); err != nil {
		return nil, err
	}
	return []byte(jsonString), nil
}

func (ch *CommonHandler) ApiResourceByGVK(ctx context.Context, p *ListAPIResourceParam) (*models.ApiResourceByGVK, error) {
	var (
		apiResources []*metav1.APIResourceList
	)

	// 获取集群客户端
	cluster, err := clientmgr.GetCluster(p.Cluster)
	if err != nil {
		return nil, errors.NewFromError(ctx, err)
	}

	c := cluster.GetClient().GetDiscoveryClient()

	// 获取服务器 API 资源
	apiResources, err = c.ServerPreferredResources()
	if err != nil {
		return nil, errors.NewFromError(ctx, err)
	}
	var (
		clusterApiResourceNum   int
		nameSpaceApiResourceNum int
	)
	// 处理并构建 clusterApiResourceByGVKListMap
	clusterApiResourceByGVKListMap := make(map[string][]*models.APIResource)
	for _, apiResourcesList := range apiResources {
		for _, apiResource := range apiResourcesList.APIResources {
			if !hasListPermission(apiResource) {
				continue // 如果没有list权限，跳过该资源
			}
			apiResourceSimple := &models.APIResource{
				Name:         apiResource.Name,
				SingularName: apiResource.SingularName,
				Namespaced:   apiResource.Namespaced,
				Group:        apiResource.Group,
				Version:      apiResource.Version,
				Kind:         apiResource.Kind,
			}
			gv, err := schema.ParseGroupVersion(apiResourcesList.GroupVersion)
			if err != nil {
				logger.GetSugared().Errorf("parse groupversion failed, err: %v", err)
				continue
			}
			// 处理空的 Group 和 Version
			if len(apiResourceSimple.Group) == 0 {
				apiResourceSimple.Group = gv.Group
			}
			if len(apiResourceSimple.Version) == 0 {
				apiResourceSimple.Version = gv.Version
			}
			gvk := schema.GroupVersionKind{
				Group:   apiResourceSimple.Group,
				Version: apiResourceSimple.Version,
				Kind:    apiResourceSimple.Kind,
			}
			isWorkLoad := false
			for _, workloadGVK := range workloadGVKs {
				if workloadGVK == gvk {
					isWorkLoad = true
				}
			}
			apiResourceSimple.IsWorkLoad = isWorkLoad
			clusterApiResourceByGVKListMap[apiResourcesList.GroupVersion] = append(clusterApiResourceByGVKListMap[apiResourcesList.GroupVersion], apiResourceSimple)
		}
	}

	var (
		items []*models.ApiResourceByGV
	)
	// 遍历并统计各资源类型
	for k, v := range clusterApiResourceByGVKListMap {
		for _, apiResourceSimple := range v {
			if apiResourceSimple.Namespaced {
				nameSpaceApiResourceNum += 1
			} else {
				clusterApiResourceNum += 1
			}
		}
		// 按资源名称排序
		sort.Slice(v, func(i, j int) bool {
			return v[i].Name < v[j].Name
		})

		item := &models.ApiResourceByGV{
			GroupVersion: k,
			Resources:    v,
		}
		items = append(items, item)
	}

	// 对原生资源按 GroupVersion 排序
	sort.SliceStable(items, func(i, j int) bool {
		iGroupVersion, ierr := schema.ParseGroupVersion(items[i].GroupVersion)
		jGroupVersion, jerr := schema.ParseGroupVersion(items[j].GroupVersion)
		if ierr != nil || jerr != nil {
			return false
		}
		iGroupIndex := k8s.GetAPIResourceGroupIndex(iGroupVersion.Group)
		jGroupIndex := k8s.GetAPIResourceGroupIndex(jGroupVersion.Group)
		if iGroupIndex == -1 {
			iGroupIndex = len(k8s.APIResourceGroupOrder) + i
		}
		if jGroupIndex == -1 {
			jGroupIndex = len(k8s.APIResourceGroupOrder) + j
		}
		if !(iGroupIndex > len(k8s.APIResourceGroupOrder) && jGroupIndex > len(k8s.APIResourceGroupOrder)) {
			return iGroupIndex < jGroupIndex
		}
		if iGroupVersion.String() != jGroupVersion.String() {
			return iGroupVersion.String() < jGroupVersion.String()
		}
		return false
	})
	return &models.ApiResourceByGVK{
		TotalNum:                clusterApiResourceNum + nameSpaceApiResourceNum,
		ClusterApiResourceNum:   clusterApiResourceNum,
		NameSpaceApiResourceNum: nameSpaceApiResourceNum,
		Items:                   items,
	}, nil
}

func hasListPermission(resource metav1.APIResource) bool {
	return utils.Contains(resource.Verbs, rbac.VerbList)
}

// 工作期望的gvk
var workloadGVKs = []schema.GroupVersionKind{
	{Group: appsv1.SchemeGroupVersion.Group, Version: appsv1.SchemeGroupVersion.Version, Kind: "Deployment"},
	{Group: appsv1.SchemeGroupVersion.Group, Version: appsv1.SchemeGroupVersion.Version, Kind: "StatefulSet"},
	{Group: appsv1.SchemeGroupVersion.Group, Version: appsv1.SchemeGroupVersion.Version, Kind: "DaemonSet"},
	{Group: appsv1.SchemeGroupVersion.Group, Version: appsv1.SchemeGroupVersion.Version, Kind: "ReplicaSet"},
	{Group: coreV1.SchemeGroupVersion.Group, Version: coreV1.SchemeGroupVersion.Version, Kind: "Pod"},
}

func (ch *CommonHandler) GetResources(ctx context.Context, p ReaderParam) (*models.ResourcesList, error) {
	if len(p.GetCluster()) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.UnKnow, "cluster is empty")
	}
	if len(p.GetResourceType()) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.UnKnow, "resource type is  empty")
	}
	cluster, err := cluterclient.OnlineClusterAssert(cluterclient.GetCluster(p.GetCluster()))
	if err != nil {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ClusterStatusNotOnline, err.Error())
	}

	list, err := NewList(cluster, p.GetGroup(), p.GetVersion(), p.GetResourceType())
	if err != nil {
		return nil, errors.NewFromError(ctx, err)
	}
	lo := &client.ListOptions{}
	if len(p.GetNamespace()) > 0 {
		lo.Namespace = p.GetNamespace()
	}
	if p.GetLabelSelector() != nil {
		lo.LabelSelector = p.GetLabelSelector()
	}
	gvr := schema.GroupVersionResource{Group: p.GetGroup(), Version: p.GetVersion(), Resource: p.GetResourceType()}
	gvk, err := cluster.GetClient().GetCtrlClient().RESTMapper().KindFor(gvr)
	if err != nil {
		return nil, errors.NewFromError(ctx, err)
	}
	mapping, err := cluster.GetClient().GetCtrlClient().RESTMapper().RESTMapping(gvk.GroupKind(), gvk.Version)
	if err != nil {
		logger.GetSugared().Errorf("can not convert, gvr: %v / %v / %v", p.GetGroup(), p.GetVersion(), p.GetResourceType())
		return nil, errors.NewFromError(ctx, err)
	}
	var (
		allowNamespace    []string
		isNamespaceScoped bool
		namespaceList     coreV1.NamespaceList
	)

	isNamespaceScoped = mapping.Scope.Name() == meta.RESTScopeNameNamespace
	var labelsSet labels.Set
	if v, ok := p.GetOptionValue("labels"); ok {
		if s, ok := v.(labels.Set); ok {
			labelsSet = s
		}
	}
	if err := cluster.GetClient().GetCtrlClient().List(ctx, &namespaceList, &client.ListOptions{LabelSelector: labels.SelectorFromSet(labelsSet)}); err != nil {
		return nil, errors.NewFromError(ctx, err)
	}
	for _, namespace := range namespaceList.Items {
		allowNamespace = append(allowNamespace, namespace.Name)
	}
	if err := cluster.GetClient().GetCtrlClient().List(ctx, list, lo); err != nil {
		return nil, errors.NewFromError(ctx, err)
	}
	uList := &unstructured.UnstructuredList{}
	_ = utils.ToStruct(list, uList)
	uList, err = filterUnstructuredListByFieldSelectors(uList, p.GetFieldSelector())
	if err != nil {
		return nil, errors.NewFromError(ctx, err)
	}

	resourceInfoList := []*models.ResourceInfo{}

	isWorkLoad := false
	for _, workloadGVK := range workloadGVKs {
		if workloadGVK == gvk {
			isWorkLoad = true
		}
	}

	for _, v := range uList.Items {
		if isNamespaceScoped && !utils.Contains(allowNamespace, v.GetNamespace()) {
			continue
		}
		workloadInfo, err := ch.GetWorkloadReplicas(gvk, &v)
		if err != nil {
			logger.GetSugared().Errorf("GetWorkloadReplicas方法报错", err)
			continue
		}
		workLoadInfo := workloadInfo
		resourceInfo := &models.ResourceInfo{
			Name:       v.GetName(),
			Namespace:  v.GetNamespace(),
			IsWorkLoad: isWorkLoad,
		}
		createTime := v.GetCreationTimestamp().UTC()
		if !createTime.IsZero() {
			resourceInfo.CreateTime = &createTime
		}
		if isWorkLoad {
			resourceInfo.WorkLoad = workLoadInfo
		}
		resourceInfoList = append(resourceInfoList, resourceInfo)
	}
	result := &models.ResourcesList{
		TotalNum: len(resourceInfoList),
		Items:    resourceInfoList,
	}

	return result, nil
}

func (ch *CommonHandler) GetWorkloadReplicas(gvk schema.GroupVersionKind, v *unstructured.Unstructured) (workLoadInfo models.WorkloadInfo, err error) {
	switch gvk {
	case workloadGVKs[0]:
		workload := appsv1.Deployment{}
		err = runtime.DefaultUnstructuredConverter.FromUnstructured(v.Object, &workload)
		if err != nil {
			return workLoadInfo, err
		}
		workLoadInfo.Ready = int(workload.Status.ReadyReplicas)
		workLoadInfo.Desired = int(*workload.Spec.Replicas)
	case workloadGVKs[1]:
		workload := appsv1.StatefulSet{}
		err = runtime.DefaultUnstructuredConverter.FromUnstructured(v.Object, &workload)
		if err != nil {
			return workLoadInfo, err
		}
		workLoadInfo.Ready = int(workload.Status.ReadyReplicas)
		workLoadInfo.Desired = int(*workload.Spec.Replicas)
	case workloadGVKs[2]:
		workload := appsv1.DaemonSet{}
		err = runtime.DefaultUnstructuredConverter.FromUnstructured(v.Object, &workload)
		if err != nil {
			return workLoadInfo, err
		}
		workLoadInfo.Ready = int(workload.Status.NumberReady)
		workLoadInfo.Desired = int(workload.Status.DesiredNumberScheduled)
	case workloadGVKs[3]:
		workload := appsv1.ReplicaSet{}
		err = runtime.DefaultUnstructuredConverter.FromUnstructured(v.Object, &workload)
		if err != nil {
			return workLoadInfo, err
		}
		workLoadInfo.Ready = int(workload.Status.ReadyReplicas)
		workLoadInfo.Desired = int(*workload.Spec.Replicas)
	case workloadGVKs[4]:
		workload := coreV1.Pod{}
		err = runtime.DefaultUnstructuredConverter.FromUnstructured(v.Object, &workload)
		if err != nil {
			return workLoadInfo, err
		}
		workLoadInfo.Ready = 0
		if workload.Status.Phase == coreV1.PodRunning {
			workLoadInfo.Ready = 1
		}
		workLoadInfo.Desired = 1
	}
	return

}
