package common

import (
	"encoding/json"
	"fmt"
	"strings"

	"gopkg.in/yaml.v3"
	clientmgr "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/runtime/serializer"
	klog "k8s.io/klog/v2"
	ctrlclient "sigs.k8s.io/controller-runtime/pkg/client"
)

func genGroupVersionKind(group, version, kind string) schema.GroupVersionKind {
	gvk := schema.GroupVersionKind{Group: group, Version: version, Kind: kind}
	return gvk
}

// SupportedFieldSelectorGVKs 支持的field selector 列表
// https://kubernetes.io/zh-cn/docs/concepts/overview/working-with-objects/field-selectors/#%E6%94%AF%E6%8C%81%E5%AD%97%E6%AE%B5%E5%88%97%E8%A1%A8
var SupportedFieldSelectorGVKs = map[schema.GroupVersionKind][]string{
	genGroupVersionKind("", "v1", "Pod"): {
		"metadata.name",
		"metadata.namespace",
		"spec.nodeName",
		"spec.restartPolicy",
		"spec.schedulerName",
		"spec.serviceAccountName",
		"spec.hostNetwork",
		"status.phase",
		"status.podIP",
		"status.nominatedNodeName",
	},
	genGroupVersionKind("", "v1", "Event"): {
		"metadata.name",
		"metadata.namespace",
		"involvedObject.kind",
		"involvedObject.namespace",
		"involvedObject.name",
		"involvedObject.uid",
		"involvedObject.apiVersion",
		"involvedObject.resourceVersion",
		"involvedObject.fieldPath",
		"reason",
		"reportingComponent",
		"source",
		"type",
	},
	genGroupVersionKind("", "v1", "Secret"): {
		"metadata.name",
		"metadata.namespace",
		"type",
	},
	genGroupVersionKind("", "v1", "Namespace"): {
		"metadata.name",
		"status.phase",
	},
	genGroupVersionKind("apps", "v1", "ReplicaSet"): {
		"metadata.name",
		"metadata.namespace",
		"status.replicas",
	},
	genGroupVersionKind("", "v1", "ReplicationController"): {
		"metadata.name",
		"metadata.namespace",
		"status.replicas",
	},
	genGroupVersionKind("batch", "v1", "Job"): {
		"metadata.name",
		"metadata.namespace",
		"status.successful",
	},
	genGroupVersionKind("", "v1", "Node"): {
		"metadata.name",
		"spec.unschedulable",
	},
	genGroupVersionKind("certificates.k8s.io", "v1", "CertificateSigningRequest"): {
		"metadata.name",
		"spec.signerName",
	},
}

// getSupportedFields ...
func getSupportedFields(object runtime.Object) []string {
	if object == nil {
		return nil
	}
	var gvk schema.GroupVersionKind
	gvk = object.GetObjectKind().GroupVersionKind()
	if gvk.Empty() {
		gvk = getGroupVersionKindFromScheme(object)
	}
	if supportedFields, ok := SupportedFieldSelectorGVKs[gvk]; !ok {
		return []string{"metadata.name", "metadata.namespace"}
	} else {
		return supportedFields
	}
}

// buildFieldsSetFroUnstructured ...
func buildFieldsSetFroUnstructured(obj unstructured.Unstructured, supportedFields []string) (fields.Set, error) {
	fieldSet := fields.Set{}

	for _, field := range supportedFields {
		value, found, err := unstructured.NestedFieldNoCopy(obj.Object, strings.Split(field, ".")...)
		if err != nil {
			return nil, fmt.Errorf("failed to get field '%s': %w", field, err)
		}
		if found {
			fieldSet[field] = fmt.Sprintf("%v", value)
		}
	}
	return fieldSet, nil
}

// getGroupVersionKindFromScheme 从全局 Scheme 获取 GVK
func getGroupVersionKindFromScheme(obj runtime.Object) schema.GroupVersionKind {
	gvks, _, err := clientmgr.Scheme.ObjectKinds(obj)
	if err != nil || len(gvks) == 0 {
		return schema.GroupVersionKind{}
	}
	return gvks[0]
}

func initAPIResourcesCache() {
	if clusterAPIResourceListMap == nil {
		clusterAPIResourceListMap = make(map[string][]*metav1.APIResourceList)
	}
	if clusterNamespacedAPIResourceListMap == nil {
		clusterNamespacedAPIResourceListMap = make(map[string][]*metav1.APIResourceList)
	}
}

func newObjectFromJSON(cluster, group, version, resourcetype string, raw []byte, contentType string) (ctrlclient.Object, error) {
	c := clientmgr.GetLocalCluster().GetClient().GetCtrlClient()
	if c == nil {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ClusterNotExist, fmt.Sprintf("%s cluster not found", cluster))
	}
	var err error
	var gvk schema.GroupVersionKind
	var obj ctrlclient.Object
	pGvk := schema.GroupVersionResource{
		Group:    group,
		Version:  version,
		Resource: resourcetype,
	}

	createUnstructuredObject := func() (*unstructured.Unstructured, error) {
		apiResource := findApiResourceInClusteredAndNamespacedList(cluster, group, version, resourcetype)
		if apiResource == nil {
			return nil, errors.NewFromCodeWithMessage(errors.Var.K8sNotFound,
				fmt.Sprintf("group: %s version: %s resource: %s in cluster %s not found",
					group, version, resourcetype, cluster))
		}
		m := make(map[string]any)
		switch contentType {
		case runtime.ContentTypeYAML:
			if err := yaml.Unmarshal(raw, &m); err != nil {
				return nil, errors.NewFromCodeWithMessage(errors.Var.K8sError,
					fmt.Sprintf("group: %s version: %s resource: %s in unmarshal error: %v",
						group, version, resourcetype, err))
			}
		default:
			if err := json.Unmarshal(raw, &m); err != nil {
				return nil, errors.NewFromCodeWithMessage(errors.Var.K8sError,
					fmt.Sprintf("group: %s version: %s resource: %s in unmarshal error: %v",
						group, version, resourcetype, err))
			}
		}
		unObj := &unstructured.Unstructured{Object: m}
		gvk.Kind = apiResource.Kind
		gvk.Group = apiResource.Group
		gvk.Version = apiResource.Version
		uGvk := unObj.GetObjectKind().GroupVersionKind()
		if uGvk.Empty() {
			unObj.SetGroupVersionKind(gvk)
		} else {
			if uGvk.String() != gvk.String() {
				return nil, errors.NewFromCodeWithMessage(errors.Var.K8sError, fmt.Sprintf(
					"the group version kind does not match the resource kind, want apiVersion: %s kind: %s but got apiVersion: %s kind: %s",
					gvk.GroupVersion(), gvk.Kind, uGvk.GroupVersion(), uGvk.Kind))
			}
		}
		return unObj, nil
	}
	u, err := createUnstructuredObject()
	if err != nil {
		return nil, err
	}
	gvk, err = c.RESTMapper().KindFor(pGvk)
	if err != nil {
		klog.Warningf("failed to get register group: %s version: %s resource: %s obj, err: %v",
			group, version, resourcetype, err)
		obj = u
	} else {
		o, err := clientmgr.Scheme.New(gvk)
		if err != nil {
			klog.Warningf("failed to get register group: %s version: %s resource: %s obj, err: %v",
				group, version, resourcetype, err)
			obj = u
			return obj, nil
		}
		var mediaType = runtime.ContentTypeJSON
		if len(contentType) != 0 {
			mediaType = contentType
		}
		codecFactory := serializer.NewCodecFactory(clientmgr.Scheme)
		supportedMediaTypes := codecFactory.SupportedMediaTypes()
		info, ok := runtime.SerializerInfoForMediaType(supportedMediaTypes, mediaType)
		if !ok {
			return nil, errors.NewFromCodeWithMessage(errors.Var.K8sError, fmt.Sprintf("unable to locate encoder -- %q is not a supported media type, err: %+v", mediaType, err))
		}
		dec := codecFactory.DecoderToVersion(info.Serializer, gvk.GroupVersion())
		o, _, err = dec.Decode(raw, &gvk, o)
		if err != nil {
			return nil, errors.NewFromCodeWithMessage(errors.Var.K8sError, fmt.Sprintf("%+v", err))
		}
		obj, ok = o.(ctrlclient.Object)
		if !ok {
			return nil, errors.NewFromCodeWithMessage(errors.Var.K8sError, fmt.Sprintf("%+v", err))
		}

	}
	return obj, nil
}

// addFieldSelectorToFilterSelector
func filterUnstructuredListByFieldSelectors(list *unstructured.UnstructuredList, fieldSelector fields.Selector) (*unstructured.UnstructuredList, error) {
	if list == nil || fieldSelector == nil || fieldSelector.Empty() {
		return list, nil
	}
	var result []unstructured.Unstructured
	for i := range list.Items {
		item := list.Items[i]
		supportedFields := getSupportedFields(&item)
		fieldsSet, err := buildFieldsSetFroUnstructured(item, supportedFields)
		if err != nil {
			return nil, err
		}
		if fieldSelector.Matches(fieldsSet) {
			result = append(result, item)
		}
	}
	unstructuredList := list.DeepCopy()
	unstructuredList.Items = result
	return unstructuredList, nil
}

// findApiResourceInClusteredAndNamespacedList ...
func findApiResourceInClusteredAndNamespacedList(cluster, group, version, resourcetype string) *metav1.APIResource {
	clusterList := clusterAPIResourceListMap[cluster]
	namespaceList := clusterNamespacedAPIResourceListMap[cluster]
	for _, list := range append(clusterList, namespaceList...) {
		gv, err := schema.ParseGroupVersion(list.GroupVersion)
		if err != nil {
			klog.Warningf("failed to parse group version %s, err: %v", list.GroupVersion, err)
			continue
		}
		if gv.Group == group && gv.Version == version {
			for _, item := range list.APIResources {
				if resourcetype == item.Name {
					item.Group = gv.Group
					item.Version = gv.Version
					return item.DeepCopy()
				}
			}
		}
	}
	return nil

}

// NewObject ...
func NewObject(cluster clientmgr.Cluster, group, version, resourcetype string) (ctrlclient.Object, error) {

	obj, err := cluster.GetClient().NewObject(group, version, resourcetype)
	if err != nil {
		klog.Warningf("failed to get register group: %s version: %s resource: %s obj, err: %v",
			group, version, resourcetype, err)
		apiResource := findApiResourceInClusteredAndNamespacedList(cluster.GetName(), group, version, resourcetype)
		if apiResource == nil {
			return nil, errors.NewFromCodeWithMessage(errors.Var.K8sNotFound,
				fmt.Sprintf("group: %s version: %s resource: %s in cluster %s not found",
					group, version, resourcetype, cluster.GetName()))
		}
		obj := &unstructured.Unstructured{}
		obj.SetGroupVersionKind(schema.GroupVersionKind{
			Group:   apiResource.Group,
			Version: apiResource.Version,
			Kind:    apiResource.Kind,
		})
		return obj, nil
	}
	return obj, nil
}

// NewList ...
func NewList(cluster clientmgr.Cluster, group, version, resourcetype string) (ctrlclient.ObjectList, error) {

	list, err := cluster.GetClient().NewList(group, version, resourcetype)
	if err != nil {
		klog.Warningf("failed to get register group: %s version: %s resource: %s list, err: %v",
			group, version, resourcetype, err)
		apiResource := findApiResourceInClusteredAndNamespacedList(cluster.GetName(), group, version, resourcetype)
		if apiResource == nil {
			return nil, errors.NewFromCodeWithMessage(errors.Var.K8sNotFound,
				fmt.Sprintf("group: %s version: %s resource: %s in cluster %s not found",
					group, version, resourcetype, cluster.GetName()))
		}
		list := &unstructured.UnstructuredList{}
		list.SetGroupVersionKind(schema.GroupVersionKind{
			Group:   apiResource.Group,
			Version: apiResource.Version,
			Kind:    apiResource.Kind,
		})
		return list, nil
	}
	return list, nil
}
