package common

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	clientmgr "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/validator"
	coreV1 "k8s.io/api/core/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/meta"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/util/retry"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
)

func (ch *CommonHandler) CreateBackupStorageLocation(ctx context.Context, p *WriteParam) (client.Object, error) {

	//if len(p.GetCluster()) == 0 {
	//	return nil, fmt.Errorf("k8s cluster name is empty")
	//}
	//if len(p.GetVersion()) == 0 {
	//	return nil, fmt.Errorf("resource version is empty")
	//}
	//if len(p.GetResourceType()) == 0 {
	//	return nil, fmt.Errorf("resource type is empty")
	//}
	//if len(p.GetRaw()) == 0 {
	//	return nil, fmt.Errorf("body is empty")
	//}
	//cluster, err := clientmgr.GetCluster(p.GetCluster())
	//if err != nil {
	//	return nil, fmt.Errorf("k8s cluster %s not found, error: %+v", p.GetCluster(), err)
	//}
	//
	//obj, err := newObjectFromJSON(p.GetCluster(), p.GetGroup(), p.GetVersion(), p.GetResourceType(), p.GetRaw())
	//if err != nil {
	//	logger.GetSugared().Errorf("can not convert, gvr: %v / %v / %v", p.GetGroup(), p.GetVersion(), p.GetResourceType())
	//	return nil, errors.NewFromError(ctx, err)
	//}
	//obj.SetNamespace(p.GetNamespace())

	return nil, nil
}

// Create ...
func (ch *CommonHandler) Create(ctx context.Context, p WriterParam) (client.Object, error) {
	if len(p.GetCluster()) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, "cluster is empty")
	}
	if len(p.GetVersion()) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, "version is empty")
	}
	if len(p.GetResourceType()) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, "resource type is empty")
	}
	if len(p.GetRaw()) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, "body is empty")
	}
	cluster, err := clientmgr.OnlineClusterAssert(clientmgr.GetCluster(p.GetCluster()))
	if err != nil {
		return nil, errors.NewFromError(ctx, err)
	}
	obj, err := newObjectFromJSON(p.GetCluster(), p.GetGroup(), p.GetVersion(), p.GetResourceType(), p.GetRaw(), p.GetContentType())
	if err != nil {
		logger.GetSugared().Errorf("can not get object from group: %v version: %v resourceType: %v, error: %v", p.GetGroup(), p.GetVersion(), p.GetResourceType(), err)
		return nil, errors.NewFromError(ctx, err)
	}
	if p.GetNamespace() != "" {
		obj.SetNamespace(p.GetNamespace())
	}
	c := cluster.GetClient().GetCtrlClient()
	opts := make([]client.CreateOption, 0)
	if p.GetDryRun() {
		opts = append(opts, client.DryRunAll)
	}
	if err := c.Create(ctx, obj, opts...); err != nil {
		return nil, errors.NewFromError(ctx, errors.HandleK8sError(err))
	}
	ch.checkCache(c, obj.GetNamespace(), obj.GetName(), obj.DeepCopyObject().(client.Object), false)
	return obj, nil
}

func (ch *CommonHandler) Update(ctx context.Context, p WriterParam) (client.Object, error) {
	if len(p.GetCluster()) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, "cluster is empty")
	}
	if len(p.GetVersion()) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, "version is empty")
	}
	if len(p.GetResourceType()) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, "resource type is empty")
	}
	if len(p.GetName()) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, "name is empty")
	}
	if len(p.GetRaw()) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, "body is empty")
	}
	obj, err := newObjectFromJSON(p.GetCluster(), p.GetGroup(), p.GetVersion(), p.GetResourceType(), p.GetRaw(), p.GetContentType())
	if err != nil {
		logger.GetSugared().Errorf("can not convert, gvr: %v / %v / %v", p.GetGroup(), p.GetVersion(), p.GetResourceType())
		return nil, errors.NewFromError(ctx, err)
	}
	if p.GetNamespace() != "" {
		obj.SetNamespace(p.GetNamespace())
	}
	if p.GetName() != "" {
		obj.SetName(p.GetName())
	}

	opts := make([]client.UpdateOption, 0)
	if p.GetDryRun() {
		opts = append(opts, client.DryRunAll)
	}
	if c, err := clientmgr.OnlineClusterAssert(clientmgr.GetCluster(p.GetCluster())); err == nil {
		gvr, err := c.GetClient().GetCtrlClient().RESTMapper().KindFor(schema.GroupVersionResource{
			Group:    p.GetGroup(),
			Version:  p.GetVersion(),
			Resource: p.GetResourceType(),
		})
		if err != nil {
			return nil, errors.NewFromError(ctx, err)
		}
		objMap, err := runtime.DefaultUnstructuredConverter.ToUnstructured(obj)
		if err != nil {
			return nil, err
		}
		if err := validator.ValidateResource(&unstructured.Unstructured{Object: objMap}, gvr); err != nil {
			return nil, errors.NewFromCodeWithMessage(errors.Var.K8sError, err.Error())
		}
		if err := c.GetClient().GetCtrlClient().Update(ctx, obj, opts...); err != nil {
			return nil, errors.NewFromError(ctx, err)
		}
		ch.checkCache(c.GetClient().GetCtrlClient(), obj.GetNamespace(), obj.GetName(), obj.DeepCopyObject().(client.Object), false)
	} else {
		return nil, errors.NewFromError(ctx, err)
	}

	return obj, nil
}

func (ch *CommonHandler) Patch(ctx context.Context, p WriterParam) (client.Object, error) {
	if len(p.GetCluster()) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, "cluster is empty")
	}
	if len(p.GetVersion()) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, "version is empty")
	}
	if len(p.GetResourceType()) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, "resource type is empty")
	}
	if len(p.GetName()) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, "name is empty")
	}
	if len(p.GetRaw()) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, "body is empty")
	}

	obj, err := newObjectFromJSON(p.GetCluster(), p.GetGroup(), p.GetVersion(), p.GetResourceType(), p.GetRaw(), p.GetContentType())
	if err != nil {
		logger.GetSugared().Errorf("can not convert, gvr: %v / %v / %v", p.GetGroup(), p.GetVersion(), p.GetResourceType())
		return nil, errors.NewFromError(ctx, err)
	}
	if p.GetNamespace() != "" {
		obj.SetNamespace(p.GetNamespace())
	}
	if p.GetName() != "" {
		obj.SetName(p.GetName())
	}
	c, err := clientmgr.OnlineClusterAssert(clientmgr.GetCluster(p.GetCluster()))
	if err != nil {
		return nil, errors.NewFromError(ctx, err)
	}
	var opts []client.PatchOption
	if p.GetDryRun() {
		opts = append(opts, client.DryRunAll)
	}
	if err := c.GetClient().GetCtrlClient().Patch(ctx, obj, client.RawPatch(types.PatchType(p.GetContentType()), p.GetRaw()), opts...); err != nil {
		return nil, errors.NewFromError(ctx, err)
	}
	ch.checkCache(c.GetClient().GetCtrlClient(), obj.GetNamespace(), obj.GetName(), obj.DeepCopyObject().(client.Object), false)
	return obj, nil
}

func (ch *CommonHandler) Delete(ctx context.Context, p WriterParam) error {
	if len(p.GetCluster()) == 0 {
		return errors.NewFromCodeWithMessage(errors.Var.ParamError, "cluster is empty")
	}
	if len(p.GetVersion()) == 0 {
		return errors.NewFromCodeWithMessage(errors.Var.ParamError, "version is empty")
	}
	if len(p.GetResourceType()) == 0 {
		return errors.NewFromCodeWithMessage(errors.Var.ParamError, "resource type is empty")
	}
	if len(p.GetName()) == 0 {
		return errors.NewFromCodeWithMessage(errors.Var.ParamError, "name is empty")
	}
	c, err := clientmgr.OnlineClusterAssert(clientmgr.GetCluster(p.GetCluster()))
	if err != nil {
		return errors.NewFromError(ctx, err)
	}
	obj, err := NewObject(c, p.GetGroup(), p.GetVersion(), p.GetResourceType())
	if err != nil {
		logger.GetSugared().Errorf("can not convert, gvr: %v / %v / %v", p.GetGroup(), p.GetVersion(), p.GetResourceType())
		return errors.NewFromError(ctx, err)
	}
	obj.SetNamespace(p.GetNamespace())
	obj.SetName(p.GetName())
	pp := metav1.DeletePropagationBackground
	opts := make([]client.DeleteOption, 0)
	opts = append(opts, client.PropagationPolicy(pp))
	if len(p.GetRaw()) > 0 {
		opt := client.DeleteOptions{}
		if err := json.Unmarshal(p.GetRaw(), &opt); err != nil {
			return errors.NewFromError(ctx, err)
		}
		opts = append(opts, &opt)
	}
	if p.GetDryRun() {
		opts = append(opts, client.DryRunAll)
	}
	if err := c.GetClient().GetCtrlClient().Delete(ctx, obj, opts...); err != nil {
		return errors.NewFromError(ctx, err)
	}
	ch.checkCache(c.GetClient().GetCtrlClient(), obj.GetNamespace(), obj.GetName(), obj.DeepCopyObject().(client.Object), true)

	return nil
}

func (ch *CommonHandler) DeleteAll(ctx context.Context, p WriterParam) error {
	if len(p.GetCluster()) == 0 {
		return errors.NewFromCodeWithMessage(errors.Var.ParamError, "cluster is empty")
	}
	if len(p.GetVersion()) == 0 {
		return errors.NewFromCodeWithMessage(errors.Var.ParamError, "version is empty")
	}
	if len(p.GetResourceType()) == 0 {
		return errors.NewFromCodeWithMessage(errors.Var.ParamError, "resource type is empty")
	}
	c, err := clientmgr.OnlineClusterAssert(clientmgr.GetCluster(p.GetCluster()))
	if err != nil {
		return errors.NewFromError(ctx, err)
	}
	obj, err := NewObject(c, p.GetGroup(), p.GetVersion(), p.GetResourceType())
	if err != nil {
		logger.GetSugared().Errorf("can not convert, gvr: %v / %v / %v", p.GetGroup(), p.GetVersion(), p.GetResourceType())
		return errors.NewFromError(ctx, err)
	}
	if p.GetNamespace() != "" {
		obj.SetNamespace(p.GetNamespace())
	}
	if p.GetName() != "" {
		obj.SetName(p.GetName())
	}
	var opts []client.DeleteAllOfOption
	deleteAllOpt := &client.DeleteAllOfOptions{
		ListOptions: client.ListOptions{
			Namespace:     p.GetNamespace(),
			LabelSelector: p.GetLabelSelector(),
		},
		DeleteOptions: client.DeleteOptions{},
	}
	if len(p.GetRaw()) > 0 {
		opt := metav1.DeleteOptions{}
		if err := json.Unmarshal(p.GetRaw(), &opt); err != nil {
			return errors.NewFromError(ctx, err)
		}
		deleteAllOpt.DeleteOptions.Raw = &opt
	}
	opts = append(opts, deleteAllOpt)
	if p.GetDryRun() {
		opts = append(opts, client.DryRunAll)
	}
	if err := c.GetClient().GetCtrlClient().DeleteAllOf(ctx, obj, opts...); err != nil {
		return errors.NewFromError(ctx, err)
	}

	return nil
}

func (ch *CommonHandler) DeleteApiResources(ctx context.Context, p *WriteParam, req models.K8sResourceDeleteRequest) error {
	if len(p.GetCluster()) == 0 {
		return errors.NewFromCodeWithMessage(errors.Var.ParamError, "cluster is empty")
	}

	// 获取集群客户端
	c, err := clientmgr.OnlineClusterAssert(clientmgr.GetCluster(p.GetCluster()))
	if err != nil {
		return errors.NewFromError(ctx, err)
	}
	gvr := schema.GroupVersionResource{
		Group:    p.GetGroup(),
		Version:  p.GetVersion(),
		Resource: p.GetResourceType(),
	}
	gvk, err := c.GetClient().GetCtrlClient().RESTMapper().KindFor(gvr)
	if err != nil {
		logger.GetSugared().Errorf("can not convert, gvr: %v / %v / %v", p.GetGroup(), p.GetVersion(), p.GetResourceType())
		return errors.NewFromError(ctx, err)
	}
	mapping, err := c.GetClient().GetCtrlClient().RESTMapper().RESTMapping(gvk.GroupKind(), gvk.Version)
	if err != nil {
		logger.GetSugared().Errorf("can not convert, gvr: %v / %v / %v", p.GetGroup(), p.GetVersion(), p.GetResourceType())
		return errors.NewFromError(ctx, err)
	}
	isNamespaceScoped := mapping.Scope.Name() == meta.RESTScopeNameNamespace
	// 只允许项目的命名空间进行操作

	var labelsSet labels.Set
	if v, ok := p.GetOptionValue("labels"); ok {
		if s, ok := v.(labels.Set); ok {
			labelsSet = s
		}
	}
	var (
		allowNamespace []string
		namespaceList  coreV1.NamespaceList
		errs           []error
	)
	if isNamespaceScoped {
		if err := c.GetClient().GetCtrlClient().List(ctx, &namespaceList, &client.ListOptions{
			LabelSelector: labels.SelectorFromSet(labelsSet),
		}); err != nil {
			return errors.NewFromError(ctx, err)
		}
		for _, namespace := range namespaceList.Items {
			allowNamespace = append(allowNamespace, namespace.Name)
		}
	}
	for _, resource := range req.Resources {
		obj, err := NewObject(c, p.GetGroup(), p.GetVersion(), p.GetResourceType())
		obj.SetName(resource.Name)
		if isNamespaceScoped {
			if resource.Namespace == "" {
				resource.Namespace = coreV1.NamespaceDefault
			}
			if !utils.Contains(allowNamespace, resource.Namespace) {
				logger.GetSugared().Errorf("can not delete resource %v,%v, namespace %v is not allowed", resource.Namespace, resource.Name, resource.Namespace)
				errs = append(errs, errors.NewFromCodeWithMessage(errors.Var.K8sForbidden, fmt.Sprintf("namespace %v is not allowed", resource.Namespace)))
				continue
			}
			obj.SetNamespace(resource.Namespace)

		}
		if err != nil {
			logger.GetSugared().Errorf("can not convert, gvr: %v / %v / %v", p.GetGroup(), p.GetVersion(), p.GetResourceType())
			errs = append(errs, err)
			continue
		}
		if err := c.GetClient().GetCtrlClient().Get(ctx, types.NamespacedName{Namespace: resource.Namespace, Name: resource.Name}, obj); err != nil {
			if k8sErrors.IsNotFound(err) {
				logger.GetSugared().Warnf("gvr: %v / %v / %v , resource %v not found", p.GetGroup(), p.GetVersion(), p.GetResourceType(), resource.Namespace+"/"+resource.Name)
				continue
			}
		}
		if !obj.GetDeletionTimestamp().IsZero() {
			return errors.NewFromCodeWithMessage(errors.Var.K8sIsDeleting, "resource is deleting")
		}
		if len(obj.GetFinalizers()) > 0 {
			return errors.NewFromCodeWithMessage(errors.Var.K8sForbiddenDeletedByFinalizer, "resource protected by finalizer")
		}
		if err := c.GetClient().GetCtrlClient().Delete(ctx, obj); err != nil {
			return errors.NewFromError(ctx, err)
		}
		ch.checkCache(c.GetClient().GetCtrlClient(), obj.GetNamespace(), obj.GetName(), obj.DeepCopyObject().(client.Object), true)
	}
	if len(errs) > 0 {
		return errors.NewFromError(ctx, errors.Join(errs...))
	}
	return nil
}

func (ch *CommonHandler) checkCache(c client.Client, ns, name string, obj client.Object, delete bool) {
	rv := obj.GetResourceVersion()

	err := retry.OnError(wait.Backoff{
		Steps:    5,
		Duration: 10 * time.Millisecond,
		Factor:   4.0,
		Jitter:   0.1,
	}, func(err error) bool {
		return err != nil
	}, func() error {
		obj.SetResourceVersion("0")
		err := c.Get(context.Background(), types.NamespacedName{Namespace: ns, Name: name}, obj)
		if delete {
			if err != nil && k8sErrors.IsNotFound(err) {
				return nil
			}
			return fmt.Errorf("cache need update")
		}
		if err != nil {
			return err
		}
		newRv := obj.GetResourceVersion()
		objResourceVersion, err := strconv.ParseInt(newRv, 10, 64)
		if err != nil {
			return err
		}
		oldObjResourceVersion, err := strconv.ParseInt(rv, 10, 64)
		if err != nil {
			return err
		}

		if oldObjResourceVersion <= objResourceVersion {
			return nil
		}
		return fmt.Errorf("cache need update")
	})
	if err != nil {
		return
	}
}
