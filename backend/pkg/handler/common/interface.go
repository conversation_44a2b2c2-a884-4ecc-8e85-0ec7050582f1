package common

import (
	"context"
	"io"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/resources"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/apimachinery/pkg/labels"
	ctrlclient "sigs.k8s.io/controller-runtime/pkg/client"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
)

type ReaderAndWriterParam interface {
	GetOptionValue(optionKey string) (value any, existed bool)
	SetOptionValue(optionKey string, value any)
	GetCluster() string
	SetCluster(cluster string)
	GetGroup() string
	SetGroup(group string)
	GetVersion() string
	SetVersion(version string)
	GetNamespace() string
	SetNamespace(namespace string)
	GetResourceType() string
	SetResourceType(resourceType string)
	GetName() string
	SetName(name string)
	GetLabelSelector() labels.Selector
	SetLabelSelector(labelSelector labels.Selector)
	GetFieldSelector() fields.Selector
	SetFieldSelector(field fields.Selector)
}

type ReaderParam interface {
	ReaderAndWriterParam
	GetFilter() resources.Filter[unstructured.Unstructured]
	SetFilter(filter resources.Filter[unstructured.Unstructured])
}

type WriterParam interface {
	ReaderAndWriterParam
	GetContentType() string
	SetContentType(contentType string)
	GetRaw() []byte
	SetRaw(raw []byte)
	GetDryRun() bool
	SetDryRun(dryRun bool)
}

type Reader interface {
	// Get ...
	Get(ctx context.Context, param ReaderParam) (ctrlclient.Object, error)
	// List ...
	List(ctx context.Context, param ReaderParam) (*models.PageableResponse[unstructured.Unstructured], error)

	// Describe ...
	Describe(ctx context.Context, param ReaderParam) (string, error)
}

type Writer interface {
	// Create ...
	Create(ctx context.Context, param WriterParam) (ctrlclient.Object, error)
	// Update ...
	Update(ctx context.Context, param WriterParam) (ctrlclient.Object, error)
	// Patch ...
	Patch(ctx context.Context, param WriterParam) (ctrlclient.Object, error)
	// Delete ...
	Delete(ctx context.Context, param WriterParam) error
	// DeleteAll ...
	DeleteAll(ctx context.Context, param WriterParam) error
	// DeleteResources ...
}

type CommonIntf interface {
	Reader
	Writer
	// ListAPIResources ...
	ListAPIResources(ctx context.Context, p *ListAPIResourceParam) (*models.PageableResponse[unstructured.Unstructured], error)
	// AllocatedOFResources ...
	AllocatedOFResources(ctx context.Context, p WriterParam) (*models.PageableResponse[unstructured.Unstructured], error)
	// PodLogs ...
	PodLogs(ctx context.Context, p *models.PodLogRequest) (io.ReadCloser, error)
	// ListWithoutPage ...
	ListWithoutPage(ctx context.Context, p ReaderParam) (*unstructured.UnstructuredList, error)
	// DeleteApiResources ...
	DeleteApiResources(ctx context.Context, p *WriteParam, req models.K8sResourceDeleteRequest) error
	// ApiResourceByGVK ...
	ApiResourceByGVK(ctx context.Context, p *ListAPIResourceParam) (*models.ApiResourceByGVK, error)

	GetResources(ctx context.Context, p ReaderParam) (*models.ResourcesList, error)
}
