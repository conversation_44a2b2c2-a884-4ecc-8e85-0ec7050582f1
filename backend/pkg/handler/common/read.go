package common

import (
	"context"
	"fmt"
	"k8s.io/apimachinery/pkg/api/meta"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/restmapper"

	cluterclient "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/kubectl/pkg/describe"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

func (ch *CommonHandler) Get(ctx context.Context, p ReaderParam) (client.Object, error) {
	//if len(p.Cluster) == 0 {
	//	return nil, errors.NewFromCodeWithMessage(errors.Var.UnKnow, "cluster is empty")
	//}
	if len(p.GetCluster()) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.UnKnow, "cluster is empty")
	}
	if len(p.GetVersion()) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.UnKnow, "version is empty")
	}
	if len(p.GetName()) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.UnKnow, "name is empty")
	}
	if len(p.GetResourceType()) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.UnKnow, "resource type is empty")
	}
	cluster, err := cluterclient.OnlineClusterAssert(cluterclient.GetCluster(p.GetCluster()))
	if err != nil {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ClusterStatusNotOnline, err.Error())
	}

	obj, err := NewObject(cluster, p.GetGroup(), p.GetVersion(), p.GetResourceType())
	if err != nil {
		return nil, errors.NewFromError(ctx, err)
	}
	if err := cluster.GetClient().GetCtrlClient().Get(ctx, types.NamespacedName{Namespace: p.GetNamespace(), Name: p.GetName()}, obj); err != nil {
		return nil, errors.NewFromError(ctx, err)
	}

	obj.SetManagedFields(nil)

	return obj, nil
}

func (ch *CommonHandler) List(ctx context.Context, p ReaderParam) (*models.PageableResponse[unstructured.Unstructured], error) {
	if len(p.GetCluster()) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.UnKnow, "cluster is empty")
	}
	if len(p.GetResourceType()) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.UnKnow, "resource type is  empty")
	}
	cluster, err := cluterclient.OnlineClusterAssert(cluterclient.GetCluster(p.GetCluster()))
	if err != nil {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ClusterStatusNotOnline, err.Error())
	}

	list, err := NewList(cluster, p.GetGroup(), p.GetVersion(), p.GetResourceType())
	if err != nil {
		return nil, errors.NewFromError(ctx, err)
	}
	lo := &client.ListOptions{}
	if len(p.GetNamespace()) > 0 {
		lo.Namespace = p.GetNamespace()
	}
	if p.GetLabelSelector() != nil {
		lo.LabelSelector = p.GetLabelSelector()
	}
	if err := cluster.GetClient().GetCtrlClient().List(ctx, list, lo); err != nil {
		return nil, errors.NewFromError(ctx, err)
	}
	uList := &unstructured.UnstructuredList{}
	_ = utils.ToStruct(list, uList)
	uList, err = filterUnstructuredListByFieldSelectors(uList, p.GetFieldSelector())
	if err != nil {
		return nil, errors.NewFromError(ctx, err)
	}
	filter := p.GetFilter()
	filterResult, err := filter.FilterResult(uList.Items)
	if err != nil {
		return nil, err
	}
	return filterResult, nil
}

func (ch *CommonHandler) Describe(ctx context.Context, p ReaderParam) (string, error) {
	var result string
	if len(p.GetCluster()) == 0 {
		return result, errors.NewFromCodeWithMessage(errors.Var.UnKnow, "cluster is empty")
	}
	if len(p.GetResourceType()) == 0 {
		return result, errors.NewFromCodeWithMessage(errors.Var.UnKnow, "resource type is  empty")
	}
	cluster, err := cluterclient.OnlineClusterAssert(cluterclient.GetCluster(p.GetCluster()))
	if err != nil {
		return result, errors.NewFromCodeWithMessage(errors.Var.ClusterStatusNotOnline, err.Error())
	}

	// Get GVK FROM GVR
	apiResources, err := restmapper.GetAPIGroupResources(cluster.GetClient().GetDiscoveryClient())
	if err != nil {
		return result, err
	}
	mapper := restmapper.NewDiscoveryRESTMapper(apiResources)
	gvr := schema.GroupVersionResource{
		Group:    p.GetGroup(),
		Version:  p.GetVersion(),
		Resource: p.GetResourceType(),
	}
	gvk, err := mapper.KindFor(gvr)
	if err != nil {
		return result, err
	}
	describeApi, err := describerFun(cluster.GetClient().GetConfig(), &meta.RESTMapping{
		GroupVersionKind: gvk,
		Resource:         gvr,
	})
	return describeApi.Describe(p.GetNamespace(), p.GetName(), describe.DescriberSettings{ShowEvents: true})
}

func describerFun(clientConfig *rest.Config, mapping *meta.RESTMapping) (describe.ResourceDescriber, error) {
	// try to get a describer
	if describer, ok := describe.DescriberFor(mapping.GroupVersionKind.GroupKind(), clientConfig); ok {
		return describer, nil
	}
	// if this is a kind we don't have a describer for yet, go generic if possible
	if genericDescriber, ok := describe.GenericDescriberFor(mapping, clientConfig); ok {
		return genericDescriber, nil
	}
	// otherwise return an unregistered error
	return nil, fmt.Errorf("no description has been implemented for %s", mapping.GroupVersionKind.String())
}

func (ch *CommonHandler) ListWithoutPage(ctx context.Context, p ReaderParam) (*unstructured.UnstructuredList, error) {
	if len(p.GetCluster()) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.UnKnow, "cluster is empty")
	}

	if len(p.GetVersion()) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.UnKnow, "version is empty")
	}
	if len(p.GetResourceType()) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.UnKnow, "resource type is empty")
	}
	cluster, err := cluterclient.OnlineClusterAssert(cluterclient.GetCluster(p.GetCluster()))
	if err != nil {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ClusterStatusNotOnline, err.Error())
	}

	// /api/v1/namespaces/{namespaceName}/secrets
	// group = ""
	// version = "v1"
	// resourceType = "Secret"
	// /apis/stellaris.harmonycloud.cn/v1/clusters
	// group  ="stellaris.harmonycloud.cn"
	// version = "v1"
	// kind = "Cluster"
	// list -> DeploymentList
	// get -> Deployment
	// gvk.Kind + "List"
	list, err := NewList(cluster, p.GetGroup(), p.GetVersion(), p.GetResourceType())
	if err != nil {
		return nil, errors.NewFromError(ctx, err)
	}
	lo := &client.ListOptions{}
	if len(p.GetNamespace()) > 0 {
		lo.Namespace = p.GetNamespace()
	}
	if p.GetLabelSelector() != nil {
		lo.LabelSelector = p.GetLabelSelector()
	}
	if err := cluster.GetClient().GetCtrlClient().List(ctx, list, lo); err != nil {
		return nil, errors.NewFromError(ctx, err)
	}
	uList := &unstructured.UnstructuredList{}
	_ = utils.ToStruct(list, uList)

	return uList, nil
}
