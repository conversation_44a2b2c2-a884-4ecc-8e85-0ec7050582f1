package common

import (
	"context"
	"io"

	"harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	v1 "k8s.io/api/core/v1"
)

func (ch *CommonHandler) PodLogs(ctx context.Context, p *models.PodLogRequest) (io.ReadCloser, error) {
	cluster, err := client.OnlineClusterAssert(client.GetCluster(p.Cluster))
	if err != nil {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ClusterStatusNotOnline, err.Error())
	}
	return cluster.GetClient().GetKubeClient().CoreV1().Pods(p.Namespace).GetLogs(p.Name, &v1.PodLogOptions{
		Container:    p.Container,
		Follow:       p.Follow,
		Previous:     p.Previous,
		SinceSeconds: p.Since<PERSON>econd<PERSON>,
		TailLines:    p.<PERSON>,
	}).Stream(ctx)
}
