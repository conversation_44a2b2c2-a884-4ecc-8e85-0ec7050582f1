package common

import (
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/resources"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/apimachinery/pkg/labels"
)

var _ WriterParam = (*WriteParam)(nil)
var _ ReaderParam = (*ReadParam)(nil)

type WriteParam struct {
	*unstructured.Unstructured
	Cluster       string
	Group         string
	Version       string
	ResourceType  string
	Namespace     string
	Name          string
	Raw           []byte
	ContentType   string
	DryRun        bool
	LabelSelector labels.Selector
	FieldSelector fields.Selector
}

func (in *WriteParam) DeepCopy() *WriteParam {
	if in == nil {
		return nil
	}

	out := &WriteParam{}

	if in.Unstructured != nil {
		out.Unstructured = in.Unstructured.DeepCopy()
	}

	out.Cluster = in.Cluster
	out.Group = in.Group
	out.Version = in.Version
	out.ResourceType = in.ResourceType
	out.Namespace = in.Namespace
	out.Name = in.Name

	if in.Raw != nil {
		out.Raw = make([]byte, len(in.Raw))
		copy(out.Raw, in.Raw)
	}

	out.ContentType = in.ContentType
	out.DryRun = in.DryRun

	if in.LabelSelector != nil {
		out.LabelSelector = in.LabelSelector.DeepCopySelector()
	}

	if in.FieldSelector != nil {
		out.FieldSelector = in.FieldSelector.DeepCopySelector()
	}

	return out
}

func (w *WriteParam) GetOptionValue(optionKey string) (value any, existed bool) {
	if w.Object != nil {
		value, existed = w.Object[optionKey]

	}
	return
}

func (w *WriteParam) SetOptionValue(optionKey string, value any) {
	if w.Object == nil {
		w.Object = make(map[string]any)
	}
	w.Object[optionKey] = value
}

func (w *WriteParam) GetFieldSelector() fields.Selector {
	return w.FieldSelector
}

func (w *WriteParam) SetFieldSelector(fieldSelector fields.Selector) {
	w.FieldSelector = fieldSelector
}

func (w *WriteParam) GetDryRun() bool {
	return w.DryRun
}

func (w *WriteParam) SetDryRun(dryRun bool) {
	w.DryRun = dryRun
}

func (w *WriteParam) GetCluster() string {
	return w.Cluster
}

func (w *WriteParam) SetCluster(cluster string) {
	w.Cluster = cluster
}

func (w *WriteParam) GetGroup() string {
	return w.Group
}

func (w *WriteParam) SetGroup(group string) {
	w.Group = group
}

func (w *WriteParam) GetVersion() string {
	return w.Version
}

func (w *WriteParam) SetVersion(version string) {
	w.Version = version
}

func (w *WriteParam) GetNamespace() string {
	return w.Namespace
}

func (w *WriteParam) SetNamespace(namespace string) {
	w.Namespace = namespace
}

func (w *WriteParam) GetResourceType() string {
	return w.ResourceType
}

func (w *WriteParam) SetResourceType(resourceType string) {
	w.ResourceType = resourceType
}

func (w *WriteParam) GetName() string {
	return w.Name
}

func (w *WriteParam) SetName(name string) {
	w.Name = name
}

func (w *WriteParam) GetContentType() string {
	return w.ContentType
}

func (w *WriteParam) SetContentType(contentType string) {
	w.ContentType = contentType
}

func (w *WriteParam) GetRaw() []byte {
	return w.Raw
}

func (w *WriteParam) SetRaw(raw []byte) {
	w.Raw = raw
}

func (w *WriteParam) GetLabelSelector() labels.Selector {
	return w.LabelSelector
}

func (w *WriteParam) SetLabelSelector(labelSelector labels.Selector) {
	w.LabelSelector = labelSelector
}

type ReadParam struct {
	*unstructured.Unstructured
	Cluster       string
	Group         string
	Version       string
	ResourceType  string
	Namespace     string
	Name          string
	Filter        resources.Filter[unstructured.Unstructured]
	LabelSelector labels.Selector
	FieldSelector fields.Selector
}

func (in *ReadParam) DeepCopy() *ReadParam {
	if in == nil {
		return nil
	}

	out := &ReadParam{}

	if in.Unstructured != nil {
		out.Unstructured = in.Unstructured.DeepCopy()
	}

	out.Cluster = in.Cluster
	out.Group = in.Group
	out.Version = in.Version
	out.ResourceType = in.ResourceType
	out.Namespace = in.Namespace
	out.Name = in.Name
	out.Filter = in.Filter
	if in.LabelSelector != nil {
		out.LabelSelector = in.LabelSelector.DeepCopySelector()
	}

	if in.FieldSelector != nil {
		out.FieldSelector = in.FieldSelector.DeepCopySelector()
	}

	return out
}

func (r *ReadParam) GetOptionValue(optionKey string) (value any, existed bool) {
	if r.Object != nil {
		value, existed = r.Object[optionKey]
	}
	return
}

func (r *ReadParam) SetOptionValue(optionKey string, value any) {
	if r.Object == nil {
		r.Object = make(map[string]any)
	}
	r.Object[optionKey] = value
}

func (r *ReadParam) GetFieldSelector() fields.Selector {
	return r.FieldSelector
}

func (r *ReadParam) SetFieldSelector(fieldSelector fields.Selector) {
	r.FieldSelector = fieldSelector
}

func (r *ReadParam) GetCluster() string {
	// get cluster
	return r.Cluster
}

func (r *ReadParam) SetCluster(cluster string) {
	r.Cluster = cluster
}

func (r *ReadParam) GetGroup() string {
	return r.Group
}

func (r *ReadParam) SetGroup(group string) {
	r.Group = group
}

func (r *ReadParam) GetVersion() string {
	return r.Version
}

func (r *ReadParam) SetVersion(version string) {
	r.Version = version
}

func (r *ReadParam) GetNamespace() string {
	return r.Namespace
}

func (r *ReadParam) SetNamespace(namespace string) {
	r.Namespace = namespace
}

func (r *ReadParam) GetResourceType() string {
	return r.ResourceType
}

func (r *ReadParam) SetResourceType(resourceType string) {
	r.ResourceType = resourceType
}

func (r *ReadParam) GetName() string {
	return r.Name
}

func (r *ReadParam) SetName(name string) {
	r.Name = name
}

func (r *ReadParam) GetFilter() resources.Filter[unstructured.Unstructured] {
	return r.Filter
}

func (r *ReadParam) SetFilter(filter resources.Filter[unstructured.Unstructured]) {
	r.Filter = filter
}

func (r *ReadParam) GetLabelSelector() labels.Selector {
	return r.LabelSelector
}

func (r *ReadParam) SetLabelSelector(labelSelector labels.Selector) {
	r.LabelSelector = labelSelector
}

type ListAPIResourceParam struct {
	Cluster    string
	Namespaced bool
	Filter     resources.Filter[unstructured.Unstructured]
}

//type PodLogParam struct {
//	Cluster      string
//	Namespace    string
//	Name         string
//	Container    string
//	Follow       bool
//	Previous     bool
//	TailLines    *int64
//	SinceSeconds *int64
//}
