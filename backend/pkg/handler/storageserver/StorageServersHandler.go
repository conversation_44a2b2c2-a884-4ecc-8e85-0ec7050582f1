package storageserver

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/database"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	backupHandler "harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/backupserver"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/velero"
)

type storageServersHandler struct {
	caasDB                        *gorm.DB
	veleroStorageServersInterface VeleroStorageServersInterface
}

func (h *storageServersHandler) ListStorageServers(ctx context.Context, cluster string, backupServerName string, strFilter string) ([]velero.StorageServers, error) {
	var storageServerList []caas.StorageServer
	backupServerIds := make([]int64, 0)

	if backupServerName != "" {
		var backServerList []caas.BackupServer
		names := strings.Split(backupServerName, ",")
		if dbErr := h.caasDB.Model(&caas.BackupServer{}).Where("nickname in ?", names).Find(&backServerList).Error; dbErr != nil {
			return nil, dbErr
		}
		for _, backServer := range backServerList {
			backupServerIds = append(backupServerIds, backServer.Id)
		}
	}

	db := h.caasDB.Model(&caas.StorageServer{})
	if cluster != "" {
		clusters := strings.Split(cluster, ",")
		var storageLocationList []caas.StorageLocation
		storageServerIds := make([]int64, 0)
		if dbErr := h.caasDB.Model(&caas.StorageLocation{}).Where("cluster in ?", clusters).Find(&storageLocationList).Error; dbErr != nil {
			return nil, dbErr
		}
		for _, storageLocation := range storageLocationList {
			storageServerIds = append(storageServerIds, storageLocation.StorageServerId)
		}
		db.Where("id in ?", storageServerIds)
	}

	if len(backupServerIds) != 0 {
		db.Where("backup_server_id in ?", backupServerIds)
	}

	if strFilter != "" {
		db.Where("nickname like ?", "%"+strFilter+"%").Or("bucket like ?", "%"+strFilter+"%")
	}

	if err := db.Find(&storageServerList).Error; err != nil {
		return nil, err
	}

	var ans = make([]velero.StorageServers, 0, len(storageServerList))

	for _, storageServerItem := range storageServerList {
		var backupServer caas.BackupServer
		if dbError := database.CaasDB.Model(caas.BackupServer{}).
			Where("id = ?", storageServerItem.BackupServerId).Find(&backupServer).Error; dbError != nil {
			return nil, dbError
		}
		var storageLocations []caas.StorageLocation
		if dbErr := database.CaasDB.Model(caas.StorageLocation{}).
			Where("storage_server_id = ?", storageServerItem.ID).Find(&storageLocations).Error; dbErr != nil {
			return nil, dbErr
		}
		storageServer := storageServerItem.RevertStorageServer(backupServer.Config)
		for _, storageLocation := range storageLocations {
			storageServer.Clusters = append(storageServer.Clusters, storageLocation.Cluster)
		}
		storageServer.BackupServerName = backupServer.Nickname
		ans = append(ans, *storageServer)
	}

	return ans, nil
}

func (h *storageServersHandler) GetStorageServers(ctx context.Context, storageServerId string) (*velero.StorageServers, error) {
	var storageServers caas.StorageServer

	if err := h.caasDB.
		Model(&caas.StorageServer{}).
		Where("id = ?", storageServerId).
		Find(&storageServers).
		Error; err != nil {
		return nil, err
	}

	//获取备份服务器config配置信息
	var backupServer caas.BackupServer
	if dbError := database.CaasDB.Model(caas.BackupServer{}).
		Where("id = ?", storageServers.BackupServerId).Find(&backupServer).Error; dbError != nil {
		return nil, dbError
	}
	var storageLocations []caas.StorageLocation
	if dbErr := database.CaasDB.Model(caas.StorageLocation{}).
		Where("storage_server_id = ?", storageServers.ID).Find(&storageLocations).Error; dbErr != nil {
		return nil, dbErr
	}
	storageServer := storageServers.RevertStorageServer(backupServer.Config)
	for _, storageLocation := range storageLocations {
		storageServer.Clusters = append(storageServer.Clusters, storageLocation.Cluster)
	}
	storageServer.BackupServerName = backupServer.Nickname
	bucketQuota, err := backupHandler.NewResource().GetBucketQuota(ctx, storageServers.BackupServerId, storageServer.Bucket)
	if err != nil {
		return nil, err
	}
	storageServer.BucketQuota = bucketQuota.Quota
	storageServer.BucketUsage = bucketQuota.Size
	return storageServer, nil
}

func (h *storageServersHandler) CreateStorageServers(ctx context.Context, storageServers velero.StorageServers) error {
	var count int64
	if dbError := h.caasDB.
		Model(caas.StorageServer{}).
		Where("nickname = ?", storageServers.NickName).
		Count(&count).Error; dbError != nil {
		return dbError
	}

	if count > 0 {
		return errors.NewFromCode(errors.Var.StorageServerNameRepeat)
	}

	if preError := storageServers.PreStorageServerConfig(); preError != nil {
		return preError
	}

	//config, getConfigError := storageServers.GetStorageServerConfigString()
	//if getConfigError != nil {
	//	return getConfigError
	//}

	id := uuid.New().ID() % 2147483647
	storageServers.StorageServersId = strconv.Itoa(int(id))
	if err := database.CaasDB.Model(caas.StorageServer{}).Create(&caas.StorageServer{
		ID:             int64(id),
		NickName:       storageServers.NickName,
		Description:    storageServers.Description,
		BackupServerId: storageServers.BackupServerId,
		BslName:        storageServers.GetBackupStorageLocationName(),
		Bucket:         storageServers.Bucket,
	}).Error; err != nil {
		return err
	}
	for _, cluster := range storageServers.Clusters {
		if err := database.CaasDB.Model(caas.StorageLocation{}).Create(&caas.StorageLocation{
			StorageServerId: int64(id),
			Cluster:         cluster,
		}).Error; err != nil {
			return err
		}
	}
	//获取备份服务器config配置信息
	var backupServer caas.BackupServer
	if dbError := database.CaasDB.Model(caas.BackupServer{}).
		Where("id = ?", storageServers.BackupServerId).Find(&backupServer).Error; dbError != nil {
		return dbError
	}

	config := backupServer.RevertS3StorageServersConfig()
	storageServers.S3StorageServersConfig.Url = velero.ServersUrl{
		Protocol: config.Url.Protocol,
		Ip:       config.Url.Ip,
		Port:     config.Url.Port,
	}
	storageServers.S3StorageServersConfig.Username = config.Username
	storageServers.S3StorageServersConfig.Password = config.Password
	storageServers.S3StorageServersConfig.Bucket = storageServers.Bucket

	for _, cluster := range storageServers.Clusters {
		if err := h.veleroStorageServersInterface.CreateBackupStorageLocation(ctx, cluster, storageServers); err != nil {
			_ = h.DeleteStorageServer(ctx, storageServers.StorageServersId)
			return err
		}
	}

	return nil
}

func (h *storageServersHandler) UpdateStorageServers(ctx context.Context, storageServers velero.StorageServers) error {
	oldStorageServer, getStorageServersErr := h.GetStorageServers(ctx, storageServers.StorageServersId)
	if getStorageServersErr != nil {
		return getStorageServersErr
	}

	var count int64
	if dbError := h.caasDB.
		Model(caas.StorageServer{}).
		Where("nickname = ?", storageServers.NickName).
		Where("id != ?", storageServers.StorageServersId).
		Count(&count).Error; dbError != nil {
		return dbError
	}
	if count > 0 {
		return errors.NewFromCode(errors.Var.StorageServerNameRepeat)
	}

	if preError := storageServers.UpdateProStorageServerConfig(oldStorageServer); preError != nil {
		return preError
	}

	id, atoiError := strconv.Atoi(storageServers.StorageServersId)
	if atoiError != nil {
		return atoiError
	}

	//clusters := strings.Join(storageServers.Clusters, ",")
	if err := h.caasDB.
		Model(caas.StorageServer{}).
		Where("id = ?", id).
		Updates(map[string]interface{}{
			"nickName":    storageServers.NickName,
			"description": storageServers.Description,
		}).Error; err != nil {
		return err
	}
	if err := h.caasDB.Where("storage_server_id = ?", int64(id)).Delete(&caas.StorageLocation{}).
		Error; err != nil {
		return err
	}

	for _, cluster := range storageServers.Clusters {
		if err := database.CaasDB.Model(caas.StorageLocation{}).Create(&caas.StorageLocation{
			StorageServerId: int64(id),
			Cluster:         cluster,
		}).Error; err != nil {
			return err
		}
	}
	var backupServer caas.BackupServer
	if dbError := database.CaasDB.Model(caas.BackupServer{}).
		Where("id = ?", storageServers.BackupServerId).Find(&backupServer).Error; dbError != nil {
		return dbError
	}
	config := backupServer.RevertS3StorageServersConfig()
	storageServers.S3StorageServersConfig.Url = velero.ServersUrl{
		Protocol: config.Url.Protocol,
		Ip:       config.Url.Ip,
		Port:     config.Url.Port,
	}
	storageServers.S3StorageServersConfig.Username = config.Username
	storageServers.S3StorageServersConfig.Password = config.Password
	storageServers.S3StorageServersConfig.Bucket = storageServers.Bucket

	tCtx, _ := context.WithTimeout(context.Background(), 1000*time.Second)
	for _, cluster := range storageServers.Clusters {
		go func(clusterName string) {
			if err := h.veleroStorageServersInterface.UpdateBackupStorageLocation(tCtx, clusterName, storageServers); err != nil {
				logger.GetLogger().Error(fmt.Sprintf("Cluster:%s update StorageServer:%s error. error: %+v", clusterName, storageServers.StorageServersId, err))
			}
		}(cluster)
	}

	return nil
}

func (h *storageServersHandler) DeleteStorageServer(ctx context.Context, storageServersId string) error {
	storageServers, getStorageServersErr := h.GetStorageServers(ctx, storageServersId)
	if getStorageServersErr != nil {
		return getStorageServersErr
	}

	for _, cluster := range storageServers.Clusters {
		schedulesList, _ := h.veleroStorageServersInterface.ListSchedules(ctx, cluster)

		for _, schedule := range schedulesList {
			if schedule.StorageServers.StorageServersId == storageServersId {
				return errors.NewFromCode(errors.Var.StorageServerUsages)
			}
		}
	}

	// backuprestore 删除各个集群的bsl
	for _, cluster := range storageServers.Clusters {
		_ = h.veleroStorageServersInterface.DeleteBackupStorageLocation(ctx, cluster, storageServers.GetBackupStorageLocationName())
	}

	id, atoiError := strconv.Atoi(storageServersId)
	if atoiError != nil {
		return atoiError
	}

	if err := h.caasDB.
		Model(caas.StorageServer{}).
		Delete(&caas.StorageServer{
			ID: int64(id),
		}).Error; err != nil {
		return err
	}

	return nil
}
