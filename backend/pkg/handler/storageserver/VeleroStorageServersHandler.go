package storageserver

import (
	"context"
	"fmt"
	"reflect"

	veleroV1 "github.com/vmware-tanzu/velero/pkg/apis/velero/v1"
	"gorm.io/gorm"
	"harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/database"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
	veleroModels "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/velero"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	k8sV1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	runtimeclient "sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
)

type veleroStorageServerHandler struct {
	veleroStorageServersInterface VeleroStorageServersInterface
	caasDB                        *gorm.DB
}

func (h *veleroStorageServerHandler) GetStorageServers(ctx context.Context, storageServerId string) (*veleroModels.StorageServers, error) {
	var storageServers caas.StorageServer

	if err := h.caasDB.
		Model(&caas.StorageServer{}).
		Where("id = ?", storageServerId).
		Find(&storageServers).
		Error; err != nil {
		return nil, err
	}

	var backupServer caas.BackupServer
	if dbError := database.CaasDB.Model(caas.BackupServer{}).
		Where("id = ?", storageServers.BackupServerId).Find(&backupServer).Error; dbError != nil {
		return nil, dbError
	}
	var storageLocations []caas.StorageLocation
	if dbErr := database.CaasDB.Model(caas.StorageLocation{}).
		Where("storage_server_id = ?", storageServers.ID).Find(&storageLocations).Error; dbErr != nil {
		return nil, dbErr
	}
	storageServer := storageServers.RevertStorageServer(backupServer.Config)
	for _, storageLocation := range storageLocations {
		storageServer.Clusters = append(storageServer.Clusters, storageLocation.Cluster)
	}
	return storageServer, nil
}

func (h *veleroStorageServerHandler) ListBackupStorageLocation(ctx context.Context, clusterName string) ([]veleroV1.BackupStorageLocation, error) {
	clusterClient, clientError := client.GetCluster(clusterName)
	if clientError != nil {
		return nil, clientError
	}

	bslList := &veleroV1.BackupStorageLocationList{}

	if err := clusterClient.GetClient().GetCtrlClient().List(ctx, bslList, runtimeclient.InNamespace(constants.VeleroNamespace)); err != nil {
		return nil, err
	}

	return bslList.Items, nil
}

func (h *veleroStorageServerHandler) GetBackupStorageLocation(ctx context.Context, clusterName, backupStorageLocationName string) (*veleroV1.BackupStorageLocation, error) {
	clusterClient, clientError := client.GetCluster(clusterName)
	if clientError != nil {
		return nil, clientError
	}

	bsl := &veleroV1.BackupStorageLocation{}

	if err := clusterClient.GetClient().GetCtrlClient().Get(ctx, runtimeclient.ObjectKey{Namespace: constants.VeleroNamespace, Name: backupStorageLocationName}, bsl); err != nil {
		return nil, err
	}

	return bsl, nil
}

func (h *veleroStorageServerHandler) CreateBackupStorageLocation(ctx context.Context, clusterName string, storageServers veleroModels.StorageServers) error {
	clusterClient, clientError := client.GetCluster(clusterName)
	if clientError != nil {
		return clientError
	}

	bsl, secret := storageServers.S3StorageServersConfig.CreateBackupStorageLocation(storageServers.StorageServersId)

	if err := clusterClient.GetClient().GetCtrlClient().Create(ctx, secret); err != nil {
		return err
	}

	if err := clusterClient.GetClient().GetCtrlClient().Create(ctx, bsl); err != nil {
		defer func() {
			_ = clusterClient.GetClient().GetCtrlClient().Delete(ctx, secret)
		}()
		return err
	}

	return nil
}

func (h *veleroStorageServerHandler) UpdateBackupStorageLocation(ctx context.Context, clusterName string, storageServers veleroModels.StorageServers) error {
	logger.GetLogger().Info(fmt.Sprintf("Cluster:%s update BackupStorageLocation:%s", clusterName, storageServers.GetBackupStorageLocationName()))

	cluster, clientError := client.GetCluster(clusterName)
	clusterClient := cluster.GetClient().GetCtrlClient()
	if clientError != nil {
		return clientError
	}

	oldBsl := &veleroV1.BackupStorageLocation{}
	if err := clusterClient.Get(ctx, runtimeclient.ObjectKey{Namespace: constants.VeleroNamespace, Name: storageServers.GetBackupStorageLocationName()}, oldBsl); err != nil {
		if errors.IsNotFound(err) {
			if err := h.CreateBackupStorageLocation(ctx, clusterName, storageServers); err != nil {
				return err
			}
		}
		return err
	}

	oldSecret := &k8sV1.Secret{}
	if err := clusterClient.Get(ctx, runtimeclient.ObjectKey{Namespace: constants.VeleroNamespace, Name: storageServers.GetBackupStorageLocationName()}, oldSecret); err != nil {
		return err
	}

	bsl, secret := storageServers.S3StorageServersConfig.UpdateBackupStorageLocation(storageServers.StorageServersId, oldBsl, oldSecret)
	secretDeepCopy := oldSecret.DeepCopy()
	if _, err := controllerutil.CreateOrUpdate(ctx, clusterClient, secretDeepCopy, func() error {
		oldSecret.DeepCopyInto(secretDeepCopy)
		return nil
	}); err != nil {
		return err
	}
	logger.GetLogger().Info(fmt.Sprintf("Cluster:%s update Secret:%s succeed", clusterName, secret.Name))
	bslDeepCopy := oldBsl.DeepCopy()
	if _, err := controllerutil.CreateOrUpdate(ctx, clusterClient, bslDeepCopy, func() error {
		bsl.DeepCopyInto(bslDeepCopy)
		return nil
	}); err != nil {
		return err
	}
	logger.GetLogger().Info(fmt.Sprintf("Cluster:%s update BackupStorageLocation:%s succeed", clusterName, bsl.Name))
	return nil
}

func (h *veleroStorageServerHandler) DeleteBackupStorageLocation(ctx context.Context, clusterName, backupStorageLocationName string) error {
	clusterClient, clientError := client.GetCluster(clusterName)
	if clientError != nil {
		return clientError
	}

	bsl := &veleroV1.BackupStorageLocation{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "backupStorageLocationName",
			Namespace: constants.VeleroNamespace,
		},
	}

	secret := &k8sV1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "backupStorageLocationName",
			Namespace: constants.VeleroNamespace,
		},
	}

	if err := clusterClient.GetClient().GetCtrlClient().Delete(ctx, secret); err != nil {
		if errors.IsNotFound(err) {
			return nil
		}
		return err
	}

	if err := clusterClient.GetClient().GetCtrlClient().Delete(ctx, bsl); err != nil {
		if errors.IsNotFound(err) {
			return nil
		}
		return err
	}

	return nil
}

func (h *veleroStorageServerHandler) ListSchedules(ctx context.Context, clusterName string) ([]veleroModels.Schedules, error) {
	clusterClient, clientError := client.GetCluster(clusterName)
	if clientError != nil {
		return nil, clientError
	}

	schedulesList := veleroV1.ScheduleList{}

	if err := clusterClient.GetClient().GetCtrlClient().List(ctx, &schedulesList, runtimeclient.InNamespace(constants.VeleroNamespace)); err != nil {
		return nil, err
	}

	ans := make([]veleroModels.Schedules, 0, len(schedulesList.Items))

	for _, scheduleItem := range schedulesList.Items {
		ans = append(ans, *utils.Convert2Schedules(clusterName, scheduleItem))
	}

	return ans, nil
}

func (h *veleroStorageServerHandler) GetStorageServerStatus(ctx context.Context, clusterName, storageServerId string) (veleroModels.StorageServersStatus, string) {
	var storageServers caas.StorageServer

	if err := h.caasDB.
		Model(&caas.StorageServer{}).
		Where("id = ?", storageServerId).
		Find(&storageServers).
		Error; err != nil {
		return veleroModels.SyncError, "数据库获取失败"
	}
	var backupServer caas.BackupServer
	if dbError := database.CaasDB.Model(caas.BackupServer{}).
		Where("id = ?", storageServers.BackupServerId).Find(&backupServer).Error; dbError != nil {
		return veleroModels.SyncError, "数据库获取失败"
	}
	var storageLocations []caas.StorageLocation
	if dbErr := database.CaasDB.Model(caas.StorageLocation{}).
		Where("storage_server_id = ?", storageServers.ID).Find(&storageLocations).Error; dbErr != nil {
		return veleroModels.SyncError, "数据库获取失败"
	}
	dbStorageServer := storageServers.RevertStorageServer(backupServer.Config)
	for _, storageLocation := range storageLocations {
		dbStorageServer.Clusters = append(dbStorageServer.Clusters, storageLocation.Cluster)
	}

	clusterClient, clientError := client.GetCluster(clusterName)
	if clientError != nil {
		return veleroModels.SyncError, "集群获取失败"
	}

	bsl := veleroV1.BackupStorageLocation{}

	if err := clusterClient.GetClient().GetCtrlClient().Get(ctx, runtimeclient.ObjectKey{Namespace: constants.VeleroNamespace, Name: dbStorageServer.GetBackupStorageLocationName()}, &bsl); err != nil {
		return veleroModels.SyncError, "集群BackupStorageLocation资源获取失败"
	}

	secret := k8sV1.Secret{}

	if err := clusterClient.GetClient().GetCtrlClient().Get(ctx, runtimeclient.ObjectKey{Namespace: constants.VeleroNamespace, Name: bsl.Spec.Credential.Name}, &secret); err != nil {
		return veleroModels.SyncError, "集群BackupStorageLocation的连接账号的Secret获取失败"
	}

	dbBsl, dbSecret := dbStorageServer.S3StorageServersConfig.CreateBackupStorageLocation(dbStorageServer.StorageServersId)

	if veleroModels.EqualBackupStorageLocation(bsl, *dbBsl) && reflect.DeepEqual(secret.Data, dbSecret.Data) {
		if bsl.Status.Phase == veleroV1.BackupStorageLocationPhaseAvailable {
			return veleroModels.Normal, ""
		} else {
			return veleroModels.ConnectError, bsl.Status.Message
		}
	} else {
		logger.GetLogger().Error("storage server not equal")
		return veleroModels.SyncError, "服务器信息同步异常"
	}
}

func (h *veleroStorageServerHandler) SyncStorageServer(ctx context.Context, clusterName, storageServerId string) error {
	var storageServers caas.StorageServer

	if err := h.caasDB.
		Model(&caas.StorageServer{}).
		Where("id = ?", storageServerId).
		Find(&storageServers).
		Error; err != nil {
		return err
	}

	var backupServer caas.BackupServer
	if dbError := database.CaasDB.Model(caas.BackupServer{}).
		Where("id = ?", storageServers.BackupServerId).Find(&backupServer).Error; dbError != nil {
		return dbError
	}
	var storageLocations []caas.StorageLocation
	if dbErr := database.CaasDB.Model(caas.StorageLocation{}).
		Where("storage_server_id = ?", storageServers.ID).Find(&storageLocations).Error; dbErr != nil {
		return dbErr
	}
	dbStorageServer := storageServers.RevertStorageServer(backupServer.Config)
	for _, storageLocation := range storageLocations {
		dbStorageServer.Clusters = append(dbStorageServer.Clusters, storageLocation.Cluster)
	}

	clusterClient, clientError := client.GetCluster(clusterName)
	if clientError != nil {
		return clientError
	}

	oldBsl := &veleroV1.BackupStorageLocation{}
	if err := clusterClient.GetClient().GetCtrlClient().Get(ctx, runtimeclient.ObjectKey{Namespace: constants.VeleroNamespace, Name: dbStorageServer.GetBackupStorageLocationName()}, oldBsl); err != nil {
		if errors.IsNotFound(err) {
			oldBsl = nil
		} else {
			return err
		}
	}

	oldSecret := &k8sV1.Secret{}
	if err := clusterClient.GetClient().GetCtrlClient().Get(ctx, runtimeclient.ObjectKey{Namespace: constants.VeleroNamespace, Name: dbStorageServer.GetBackupStorageLocationName()}, oldSecret); err != nil {
		if errors.IsNotFound(err) {
			oldSecret = nil
		} else {
			return err
		}
	}

	if oldBsl == nil || oldSecret == nil {
		newBsl, newSecret := dbStorageServer.S3StorageServersConfig.CreateBackupStorageLocation(dbStorageServer.StorageServersId)

		if oldBsl == nil {
			if err := clusterClient.GetClient().GetCtrlClient().Create(ctx, newBsl); err != nil {
				return err
			}
			oldBsl = newBsl
		}

		if oldSecret == nil {
			if err := clusterClient.GetClient().GetCtrlClient().Create(ctx, newSecret); err != nil {
				return err
			}
			oldSecret = newSecret
		}
	}

	bsl, secret := dbStorageServer.S3StorageServersConfig.UpdateBackupStorageLocation(dbStorageServer.StorageServersId, oldBsl, oldSecret)

	if err := clusterClient.GetClient().GetCtrlClient().Update(ctx, secret); err != nil {
		return err
	}

	if err := clusterClient.GetClient().GetCtrlClient().Update(ctx, bsl); err != nil {
		return err
	}

	return nil
}
