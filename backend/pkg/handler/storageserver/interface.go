package storageserver

import (
	"context"

	veleroV1 "github.com/vmware-tanzu/velero/pkg/apis/velero/v1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/database"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/velero"
	veleroModels "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/velero"
)

func NewStorageServerInterface() StorageServersInterface {
	return &storageServersHandler{
		caasDB:                        database.CaasDB,
		veleroStorageServersInterface: NewVeleroStorageServerInterface(),
	}
}

type StorageServersInterface interface {
	ListStorageServers(ctx context.Context, cluster string, backupServerName string, strFilter string) ([]velero.StorageServers, error)

	GetStorageServers(ctx context.Context, storageServerId string) (*velero.StorageServers, error)

	CreateStorageServers(ctx context.Context, storageServers velero.StorageServers) error

	UpdateStorageServers(ctx context.Context, storageServers velero.StorageServers) error

	DeleteStorageServer(ctx context.Context, storageServersId string) error
}

func NewVeleroStorageServerInterface() VeleroStorageServersInterface {
	return &veleroStorageServerHandler{
		caasDB: database.CaasDB,
	}
}

type VeleroStorageServersInterface interface {
	GetStorageServers(ctx context.Context, storageServerId string) (*velero.StorageServers, error)

	ListBackupStorageLocation(ctx context.Context, clusterName string) ([]veleroV1.BackupStorageLocation, error)

	GetBackupStorageLocation(ctx context.Context, clusterName, backupStorageLocationName string) (*veleroV1.BackupStorageLocation, error)

	CreateBackupStorageLocation(ctx context.Context, clusterName string, storageServers velero.StorageServers) error

	UpdateBackupStorageLocation(ctx context.Context, clusterName string, storageServers velero.StorageServers) error

	DeleteBackupStorageLocation(ctx context.Context, clusterName, backupStorageLocationName string) error

	ListSchedules(ctx context.Context, clusterName string) ([]velero.Schedules, error)

	GetStorageServerStatus(ctx context.Context, clusterName, storageServerId string) (veleroModels.StorageServersStatus, string)

	SyncStorageServer(ctx context.Context, clusterName, storageServerId string) error
}
