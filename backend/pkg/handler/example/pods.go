package example

import (
	"context"

	"harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	v1 "k8s.io/api/core/v1"
	runtimeclient "sigs.k8s.io/controller-runtime/pkg/client"
)

type podExampleHandler struct {
}

func (hf *podExampleHandler) ListNamespacePods(ctx context.Context, clusterId, namespaceName string) (*v1.PodList, error) {
	cluster, err := client.OnlineClusterAssert(client.GetCluster(clusterId))
	if err != nil {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ClusterStatusNotOnline, err.Error())
	}
	podList := v1.PodList{}
	err = cluster.GetClient().GetCtrlClient().List(ctx, &podList, runtimeclient.InNamespace(namespaceName))
	return &podList, err

}
