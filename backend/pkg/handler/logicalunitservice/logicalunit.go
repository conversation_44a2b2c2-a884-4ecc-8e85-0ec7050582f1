package logicalunitservice

import (
	"context"
	"encoding/json"
	"github.com/samber/lo"
	"go.uber.org/zap"
	"gorm.io/gorm"
	ingress "harmonycloud.cn/ingress-expose-helper/pkg/apis/expose.helper/v1"
	stellarisv1alhpha1 "harmonycloud.cn/stellaris/pkg/apis/stellaris/v1alpha1"
	clientmgr "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/database"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/addon"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/logicalunit"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/snowflake"
	v1 "k8s.io/api/core/v1"
	storagev1 "k8s.io/api/storage/v1"
	"k8s.io/apimachinery/pkg/util/sets"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sort"
	"strconv"
	"strings"
)

func NewLogicalUnitHandler() LogicalUnitIntf {
	return &LogicalUnitHandler{
		caasDB:    database.CaasDB,
		log:       logger.GetLogger(),
		snowflake: snowflake.NewSnowflakeIntf(),
		handler:   cluster.NewHandler(),
	}
}

type LogicalUnitHandler struct {
	caasDB    *gorm.DB
	log       *zap.Logger
	snowflake snowflake.SnowflakeIntf
	handler   cluster.Handler
}

func (luh *LogicalUnitHandler) GetOrganLogicalUnitClusters(ctx context.Context, logicalUnitId string, dataCenterId string, organId string) ([]logicalunit.LogicalUnitClusteStatus, error) {
	organs := []caas.OrganQuota{}
	results := make([]logicalunit.LogicalUnitClusteStatus, 0)
	if err := luh.caasDB.Model(&caas.OrganQuota{}).
		Where("organ_id = ?", organId).Group("cluster_name").
		Find(&organs).Error; err != nil {
		luh.log.Error("unable to find organQuota", zap.Error(err))
		return nil, err
	}
	clusterInfos, err := luh.GetLogicalUnitClusters(ctx, logicalUnitId, dataCenterId)

	clusterNames := sets.New[string]()
	for _, organ := range organs {
		clusterNames.Insert(organ.ClusterName)
	}

	organIcs := []caas.OrganIC{}
	if err := luh.caasDB.Model(&caas.OrganIC{}).
		Where("organ_id = ?", organId).Group("cluster_name").
		Find(&organIcs).Error; err != nil {
		luh.log.Error("unable to find organtQuota", zap.Error(err))
		return nil, err
	}

	for _, organIc := range organIcs {
		clusterNames.Insert(organIc.ClusterName)
	}

	if err != nil {
		return nil, err
	}
	for _, clusterInfo := range clusterInfos {
		for _, clusterName := range clusterNames.UnsortedList() {
			if clusterName == clusterInfo.ClusterName {
				results = append(results, clusterInfo)
				break
			}
		}
	}
	return results, nil
}

func (luh *LogicalUnitHandler) GetProjectLogicalUnitClusters(ctx context.Context, logicalUnitId string, dataCenterId string, organId string, projectId string) ([]logicalunit.LogicalUnitClusteStatus, error) {
	projects := []caas.ProjectQuota{}
	results := make([]logicalunit.LogicalUnitClusteStatus, 0)
	if err := luh.caasDB.Model(&caas.ProjectQuota{}).
		Where("organ_id = ?", organId).
		Where("project_id = ?", projectId).Group("cluster_name").
		Find(&projects).Error; err != nil {
		luh.log.Error("unable to find projectQuota", zap.Error(err))
		return nil, err
	}
	clusterInfos, err := luh.GetLogicalUnitClusters(ctx, logicalUnitId, dataCenterId)
	if err != nil {
		return nil, err
	}
	clusterNames := sets.New[string]()
	for _, project := range projects {
		clusterNames.Insert(project.ClusterName)
	}

	projectIcs := []caas.ProjectIC{}
	if err := luh.caasDB.Model(&caas.ProjectIC{}).
		Where("organ_id = ?", organId).
		Where("project_id = ?", projectId).Group("cluster_name").
		Find(&projectIcs).Error; err != nil {
		luh.log.Error("unable to find projectQuota", zap.Error(err))
		return nil, err
	}

	for _, projectIc := range projectIcs {
		clusterNames.Insert(projectIc.ClusterName)
	}

	for _, clusterInfo := range clusterInfos {
		for _, clusterName := range clusterNames.UnsortedList() {
			if clusterName == clusterInfo.ClusterName {
				results = append(results, clusterInfo)
				break
			}
		}
	}
	return results, nil
}

func (luh *LogicalUnitHandler) ListDataCenter(ctx context.Context) ([]logicalunit.DataCenter, error) {
	// 获取数据中心表内数据
	dataCenters := []caas.DataCenter{}
	if err := luh.caasDB.Model(&caas.DataCenter{}).
		Find(&dataCenters).Error; err != nil {
		luh.log.Error("unable to find dataCenters", zap.Error(err))
		return nil, err
	}
	dataCenterList := []logicalunit.DataCenter{}
	for _, dataCenter := range dataCenters {
		logicalUnit := []logicalunit.UnitIdAndName{}
		if err := luh.caasDB.Model(&caas.LogicalUnit{}).
			Where("data_center_id = ?", dataCenter.Id).
			Find(&logicalUnit).Error; err != nil {
			luh.log.Error("unable to find logical unit", zap.Error(err))
			return nil, err
		}
		dataCenterList = append(dataCenterList, logicalunit.DataCenter{
			Id:           dataCenter.Id,
			Name:         dataCenter.Name,
			Remarks:      dataCenter.Remarks,
			Code:         dataCenter.Code,
			LogicalUnits: logicalUnit,
		})
	}
	return dataCenterList, nil
}

func (luh *LogicalUnitHandler) CreateDataCenter(ctx context.Context, dataCenter *logicalunit.DataCenter) (error, *int64) {
	// 名称和标识非空判断
	if len(dataCenter.Name) == 0 {
		return errors.NewFromCodeWithMessage(errors.Var.DataCenterNameIsNull, "dataCenterName is empty"), nil
	}
	if len(dataCenter.Code) == 0 {
		return errors.NewFromCodeWithMessage(errors.Var.DataCenterCodeIsNull, "dataCenterCode is empty"), nil
	}
	// 名称和标识查重
	dtCenter := []caas.DataCenter{}
	if err := luh.caasDB.Model(&caas.DataCenter{}).Where("name = ?", dataCenter.Name).Find(&dtCenter).Error; err != nil {
		luh.log.Error("unable to find dataCenter", zap.Error(err))
		return err, nil
	}
	if len(dtCenter) != 0 {
		return errors.NewFromCodeWithMessage(errors.Var.DataCenterNameExisted, "DataCenterName is alredy existed"), nil
	}
	if err := luh.caasDB.Model(&caas.DataCenter{}).Where("code = ?", dataCenter.Code).Find(&dtCenter).Error; err != nil {
		luh.log.Error("unable to find dataCenter", zap.Error(err))
		return err, nil
	}
	if len(dtCenter) != 0 {
		return errors.NewFromCodeWithMessage(errors.Var.DataCenterCodeExisted, "DataCenterCode is alredy existed"), nil
	}

	data := &caas.DataCenter{
		Id:      luh.snowflake.GenerateID(),
		Name:    dataCenter.Name,
		Remarks: dataCenter.Remarks,
		Code:    dataCenter.Code,
	}
	// 查重结束后即可创建
	if err := luh.caasDB.Model(&caas.DataCenter{}).Create(data).Error; err != nil {
		luh.log.Error("unable to create dataCenter", zap.Error(err))
		return err, nil
	}
	return nil, &data.Id
}

func (luh *LogicalUnitHandler) DeleteDataCenter(ctx context.Context, id string) error {
	// 根据id进行删除表中数据
	var count int64
	if err := luh.caasDB.Model(&caas.LogicalUnit{}).Where("data_center_id = ?", id).Count(&count).Error; err != nil {
		luh.log.Error("unable to delete dataCenter", zap.Error(err))
		return err
	}
	if count > 0 {
		return errors.NewFromCodeWithMessage(errors.Var.DataCenterHaveUnit, "DataCenter Have Unit")
	}
	if err := luh.caasDB.Model(&caas.DataCenter{}).Delete(&caas.DataCenter{}, id).Error; err != nil {
		luh.log.Error("unable to delete dataCenter", zap.Error(err))
		return err
	}
	return nil
}

func (luh *LogicalUnitHandler) UpdateDataCenter(ctx context.Context, dataCenter *logicalunit.DataCenter) error {
	// 判断名字是否为空
	if len(dataCenter.Name) == 0 {
		return errors.NewFromCodeWithMessage(errors.Var.DataCenterNameIsNull, "dataCenterName is empty")
	}
	// 判断除了当前id，是否还有相同名字
	data := &caas.DataCenter{}
	luh.caasDB.Model(&caas.DataCenter{}).Where("name = ?", dataCenter.Name).First(data)
	if data.Name == dataCenter.Name && data.Id != dataCenter.Id {
		return errors.NewFromCodeWithMessage(errors.Var.DataCenterNameExisted, "dataCenter name is already existed")
	}
	// 根据id进行修改
	newData := map[string]interface{}{
		"Name":    dataCenter.Name,
		"Remarks": dataCenter.Remarks,
	}
	if err := luh.caasDB.Model(&caas.DataCenter{}).Where("id = ?", dataCenter.Id).Updates(newData).Error; err != nil {
		luh.log.Error("unable to update dataCenter", zap.Error(err))
		return err
	}
	return nil
}

func (luh *LogicalUnitHandler) GetDataCenterInfo(ctx context.Context, dataCenterId string) (*logicalunit.DataCenterInfo, error) {
	var dataCenter caas.DataCenter
	if err := luh.caasDB.Model(&caas.DataCenter{}).First(&dataCenter, dataCenterId).Error; err != nil {
		return nil, err
	}

	// 查询出当前数据中心下的所有逻辑单元
	logicalunits := []caas.LogicalUnit{}
	if err := luh.caasDB.Model(&caas.LogicalUnit{}).
		Where("data_center_id = ?", dataCenterId).
		Find(&logicalunits).Error; err != nil {
		luh.log.Error("unable to find logicalUnit", zap.Error(err))
		return nil, err
	}
	logicalunitRoughs := []logicalunit.LogicalUnitRough{}
	for _, v := range logicalunits {
		unitInfos := []caas.LogicalInfo{}
		var registryNum, backupServerNum int64
		var onLineNum, notOnLineNum, clusterNum int
		// 找出类型是制品服务得数据数量
		if err := luh.caasDB.Model(&caas.LogicalInfo{}).
			Where("logical_unit_id = ?", v.Id).
			Where("info_type = ?", "harbor").
			Count(&registryNum).Error; err != nil {
			luh.log.Error("unable to find info", zap.Error(err))
			return nil, err
		}
		// 找出类型是备份服务器得数据数量
		if err := luh.caasDB.Model(&caas.LogicalInfo{}).
			Where("logical_unit_id = ?", v.Id).
			Where("info_type = ?", "backup").
			Count(&backupServerNum).Error; err != nil {
			luh.log.Error("unable to find info", zap.Error(err))
			return nil, err
		}
		// 获取类型是集群得数据
		if err := luh.caasDB.Model(&caas.LogicalInfo{}).
			Where("logical_unit_id = ?", v.Id).
			Where("info_type = ?", "cluster").
			Find(&unitInfos).Error; err != nil {
			luh.log.Error("unable to find logicalUnitInfo", zap.Error(err))
			return nil, err
		}
		clusterNum = len(unitInfos)
		// 遍历集群得数据，根据名称获取到集群状态，并对在线集群数和不在线集群数进行增加
		for _, rough := range unitInfos {
			cluster, err := clientmgr.GetCluster(rough.Info)
			if err != nil {
				return nil, errors.NewFromError(ctx, err)
			}
			status := cluster.GetStatus()
			if status == stellarisv1alhpha1.OnlineStatus {
				onLineNum += 1
			} else if status == stellarisv1alhpha1.OfflineStatus {
				notOnLineNum += 1
			}
		}

		logicalunitRoughs = append(logicalunitRoughs, logicalunit.LogicalUnitRough{
			Id:              v.Id,
			DataCenterId:    v.DataCenterId,
			Code:            v.Code,
			Type:            v.Type,
			Name:            v.Name,
			Remarks:         v.Remarks,
			Territory:       v.Territory,
			ClusterNum:      clusterNum,
			OnLineNum:       onLineNum,
			NotOnLineNum:    notOnLineNum,
			RegistryNum:     int(registryNum),
			BackupServerNum: int(backupServerNum),
		})
	}
	dataCenterInfo := logicalunit.DataCenterInfo{
		Id:           dataCenter.Id,
		Name:         dataCenter.Name,
		Remarks:      dataCenter.Remarks,
		Code:         dataCenter.Code,
		LogicalUnits: logicalunitRoughs,
	}
	return &dataCenterInfo, nil
}

func (luh *LogicalUnitHandler) ListCenterAndUnit(ctx context.Context) ([]logicalunit.DataCenterAndUnit, error) {
	centerAndUnits := []logicalunit.DataCenterAndUnit{}
	//获取全部可绑定集群以及集群状态
	clusterStatusList := make([]logicalunit.LogicalUnitClusteStatus, 0)
	clusterList, clusterErr := cluster.NewHandler().ListClusters(ctx)
	if clusterErr != nil {
		return nil, clusterErr
	}
	for _, clu := range clusterList {
		if clu.Status != clustermodel.StatusTypeOfflineStatus && clu.Status != clustermodel.StatusTypeOnlineStatus {
			continue
		}
		clusterStatusList = append(clusterStatusList, logicalunit.LogicalUnitClusteStatus{
			ClusterName:   clu.Name,
			ClusterStatus: string(clu.Status),
		})
	}

	// 从数据库中获取所有的数据中心数据
	dataCenterList := []caas.DataCenter{}
	if err := luh.caasDB.Model(&caas.DataCenter{}).Find(&dataCenterList).Error; err != nil {
		luh.log.Error("unable to find dataCenter", zap.Error(err))
		return nil, err
	}

	LogicalInfoList := []caas.LogicalInfo{}
	if err := luh.caasDB.Model(&caas.LogicalInfo{}).
		Where("info_type = ?", "cluster").
		Find(&LogicalInfoList).Error; err != nil {
		luh.log.Error("unable to find dataCenter", zap.Error(err))
		return nil, err
	}
	//全部集群未绑定的情况
	if len(LogicalInfoList) == 0 {
		centerAndUnits = append(centerAndUnits, logicalunit.DataCenterAndUnit{
			Id:       -1,
			Clusters: clusterStatusList,
		})
		return centerAndUnits, nil
	}

	//获取已有绑定集群的逻辑单元
	logicalUnitIds := lo.Map(LogicalInfoList, func(item caas.LogicalInfo, index int) int64 {
		return item.LogicalUnitId
	})

	for _, dataCenter := range dataCenterList {
		// 获取到当前数据中心下得逻辑单元
		logicalunits := []caas.LogicalUnit{}
		if err := luh.caasDB.Model(&caas.LogicalUnit{}).
			Where("data_center_id = ?", dataCenter.Id).
			Where("id in (?)", logicalUnitIds).
			Find(&logicalunits).Error; err != nil {
			luh.log.Error("unable to find logicalUnit", zap.Error(err))
			return nil, err
		}
		if len(logicalunits) == 0 {
			continue
		}
		logicalunitList := []logicalunit.LogicalUnit{}
		for _, v := range logicalunits {
			logicalunitList = append(logicalunitList, logicalunit.LogicalUnit{
				Id:           v.Id,
				DataCenterId: v.DataCenterId,
				Name:         v.Name,
				Code:         v.Code,
				Type:         v.Type,
				Territory:    v.Territory,
				Remarks:      v.Remarks,
			})
		}
		centerAndUnits = append(centerAndUnits, logicalunit.DataCenterAndUnit{
			Id:              dataCenter.Id,
			Name:            dataCenter.Name,
			Remarks:         dataCenter.Remarks,
			Code:            dataCenter.Code,
			LogicalUnitList: logicalunitList,
		})
	}

	//获取已有绑定逻辑单元的集群
	//bindedClusters := lo.Map(LogicalInfoList, func(item caas.LogicalInfo, index int) string {
	//	return item.Info
	//})
	unBindingClusterList := make([]logicalunit.LogicalUnitClusteStatus, 0)
	bindedClustersMap := lo.SliceToMap(LogicalInfoList, func(item caas.LogicalInfo) (string, caas.LogicalInfo) {
		return item.Info, item
	})
	for _, clusterStatus := range clusterStatusList {
		_, exist := bindedClustersMap[clusterStatus.ClusterName]
		if !exist {
			unBindingClusterList = append(unBindingClusterList, clusterStatus)
		}
	}
	if len(unBindingClusterList) > 0 {
		centerAndUnits = append(centerAndUnits, logicalunit.DataCenterAndUnit{
			Id:       -1,
			Clusters: unBindingClusterList,
		})
	}
	return centerAndUnits, nil
}

func (luh *LogicalUnitHandler) CreateLogicalUnit(ctx context.Context, logicalUnit *logicalunit.LogicalUnit) (*caas.LogicalUnit, error) {
	// 名称、所处地域、标识和类型非空判断
	if len(logicalUnit.Name) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.LogicalUnitNameIsNull, "logicalUnitName is empty")
	}
	if len(logicalUnit.Code) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.LogicalUnitCodeIsNull, "logicalUnitCode is empty")
	}
	if len(logicalUnit.Type) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.LogicalUnitTypeIsNull, "logicalUnitType is empty")
	}
	//if len(logicalUnit.Territory) == 0 {
	//	return nil, errors.NewFromCodeWithMessage(errors.Var.LogicalUnitTerritoryIsNull, "logicalUnitTerritory is empty")
	//}

	lgunit := []caas.LogicalUnit{}
	if err := luh.caasDB.Model(&caas.LogicalUnit{}).
		Where("name = ?", logicalUnit.Name).
		Where("data_center_id = ?", logicalUnit.DataCenterId).
		Find(&lgunit).Error; err != nil {
		luh.log.Error("unable to find logicalUnit", zap.Error(err))
		return nil, err
	}
	if len(lgunit) != 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.LogicalUnitNameExisted, "logicalUnitName is already existed")
	}
	if err := luh.caasDB.Model(&caas.LogicalUnit{}).
		Where("code = ?", logicalUnit.Code).
		Find(&lgunit).Error; err != nil {
		luh.log.Error("unable to find logicalUnit", zap.Error(err))
		return nil, err
	}
	if len(lgunit) != 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.LogicalUnitCodeExisted, "logicalUnitCode is already existed")
	}

	unit := &caas.LogicalUnit{
		Id:           luh.snowflake.GenerateID(),
		DataCenterId: logicalUnit.DataCenterId,
		Name:         logicalUnit.Name,
		Code:         logicalUnit.Code,
		Type:         logicalUnit.Type,
		Territory:    logicalUnit.Territory,
		Remarks:      logicalUnit.Remarks,
	}
	if err := luh.caasDB.Model(&caas.LogicalUnit{}).Create(unit).Error; err != nil {
		luh.log.Error("unable to create logicalUnit", zap.Error(err))
		return nil, err
	}
	return unit, nil
}

func (luh *LogicalUnitHandler) DeleteLogicalUnit(ctx context.Context, logicalUnitId string) error {
	if len(logicalUnitId) == 0 {
		return errors.NewFromCodeWithMessage(errors.Var.LogicalUnitIdIsNull, "logicalUnitId is empty")
	}
	var count int64
	if err := luh.caasDB.Model(&caas.LogicalInfo{}).Where("logical_unit_id = ?", logicalUnitId).Count(&count).Error; err != nil {
		luh.log.Error("unable to delete dataCenter", zap.Error(err))
		return err
	}
	if count > 0 {
		return errors.NewFromCodeWithMessage(errors.Var.LogicalUnitHaveData, "LogicalUnit Have Data")
	}
	if err := luh.caasDB.Model(&caas.LogicalUnit{}).Delete(&caas.LogicalUnit{}, logicalUnitId).Error; err != nil {
		luh.log.Error("unable to delete logicalUnit", zap.Error(err))
		return err
	}
	return nil
}

func (luh *LogicalUnitHandler) UpdateLogicalUnit(ctx context.Context, logicalUnit *logicalunit.LogicalUnit) error {
	if len(logicalUnit.Name) == 0 {
		return errors.NewFromCodeWithMessage(errors.Var.LogicalUnitNameIsNull, "logicalUnitName is empty")
	}
	if len(logicalUnit.Type) == 0 {
		return errors.NewFromCodeWithMessage(errors.Var.LogicalUnitTypeIsNull, "logicalUnitType is empty")
	}
	//if len(logicalUnit.Territory) == 0 {
	//	return errors.NewFromCodeWithMessage(errors.Var.LogicalUnitTerritoryIsNull, "logicalUnitTerritory is empty")
	//}

	lgunit := []caas.LogicalUnit{}
	if err := luh.caasDB.Model(&caas.LogicalUnit{}).
		Where("name = ?", logicalUnit.Name).
		Where("id != ?", logicalUnit.Id).
		Find(&lgunit).Error; err != nil {
		luh.log.Error("unable to find logicalUnit", zap.Error(err))
		return err
	}
	if len(lgunit) != 0 {
		return errors.NewFromCodeWithMessage(errors.Var.LogicalUnitNameExisted, "logicalUnitName is already existed")
	}

	unit := map[string]interface{}{
		"Name":      logicalUnit.Name,
		"Type":      logicalUnit.Type,
		"Territory": logicalUnit.Territory,
		"Remarks":   logicalUnit.Remarks,
	}
	if err := luh.caasDB.Debug().Model(&caas.LogicalUnit{}).Where("id = ?", logicalUnit.Id).Updates(unit).Error; err != nil {
		luh.log.Error("unable to update logicalUnit", zap.Error(err))
		return err
	}
	return nil
}

func (luh *LogicalUnitHandler) GetLogicalUnitClusters(ctx context.Context, logicalUnitId string, dataCenterId string) ([]logicalunit.LogicalUnitClusteStatus, error) {
	infoList := make([]caas.LogicalInfo, 0)
	unitIds := make([]int64, 0)
	//clusterNames := make([]string, 0)
	var ClusterStatusSet = sets.New[logicalunit.LogicalUnitClusteStatus]()
	ClusterStatusList := make([]logicalunit.LogicalUnitClusteStatus, 0)

	//var clusterList stellarisv1alhpha1.ClusterList
	//if err := clientmgr.GetLocalCluster().GetClient().GetCtrlClient().List(ctx, &clusterList); err != nil {
	//	return nil, err
	//}
	clusterList, clusterErr := cluster.NewHandler().ListClusters(ctx)
	if clusterErr != nil {
		return nil, clusterErr
	}

	// 获取未绑定
	if dataCenterId == "" {
		logicalInfoList := make([]caas.LogicalInfo, 0)
		if err := luh.caasDB.Model(&caas.LogicalInfo{}).
			Where("info_type = ?", "cluster").
			Find(&logicalInfoList).Error; err != nil {
			return nil, err
		}
		for _, clu := range clusterList {
			inInfos := false
			if clu.Status != clustermodel.StatusTypeOfflineStatus && clu.Status != clustermodel.StatusTypeOnlineStatus {
				continue
			}
			for _, v := range logicalInfoList {
				if clu.Name == v.Info {
					inInfos = true
					break
				}
			}
			if !inInfos {
				ClusterStatusList = append(ClusterStatusList, logicalunit.LogicalUnitClusteStatus{
					ClusterName:   clu.Name,
					ClusterStatus: string(clu.Status),
					CreateTime:    clu.CreateTime,
				})
			}
		}
		return ClusterStatusList, nil
	}

	//和数据库里比对
	if logicalUnitId == "" {
		logicalUnitList := make([]caas.LogicalUnit, 0)
		if err := luh.caasDB.Model(&caas.LogicalUnit{}).
			Where("data_center_id = ?", dataCenterId).
			Find(&logicalUnitList).Error; err != nil {
			return nil, err
		}
		for _, unit := range logicalUnitList {
			unitIds = append(unitIds, unit.Id)
		}
	} else {
		id, _ := strconv.ParseInt(logicalUnitId, 10, 64)
		unitIds = append(unitIds, id)
	}
	luh.caasDB.Model(&caas.LogicalInfo{}).
		Where("info_type = ?", "cluster").
		Where("logical_unit_id in ?", unitIds).
		Find(&infoList).DB()
	//获取环境上所有集群的状态
	for _, v := range infoList {
		inCluster := false
		for _, clu := range clusterList {
			if clu.Status != clustermodel.StatusTypeOfflineStatus && clu.Status != clustermodel.StatusTypeOnlineStatus {
				continue
			}
			// 数据库中的集群名字在集群列表中存在
			if v.Info == clu.Name {
				ClusterStatusSet.Insert(logicalunit.LogicalUnitClusteStatus{
					ClusterName:   clu.Name,
					ClusterStatus: string(clu.Status),
					CreateTime:    clu.CreateTime,
				})

				inCluster = true
				break
			}
		}
		// 不在集群列表中则删除该数据
		if !inCluster {
			if err := luh.caasDB.Model(&caas.LogicalInfo{}).
				Where("info = ?", v.Info).Delete(&caas.LogicalInfo{}).Error; err != nil {
				luh.log.Error("unable to delete info", zap.Error(err))
				return nil, err
			}
		}
	}
	resList := ClusterStatusSet.UnsortedList()
	sort.Slice(resList, func(i, j int) bool {
		return resList[i].CreateTime.Before(resList[j].CreateTime)
	})
	return resList, nil
}

func (luh *LogicalUnitHandler) LogicalUnitInfoBind(ctx context.Context, infoBinding logicalunit.InfoBinding) error {
	for _, info := range infoBinding.Infos {
		logicalInfo := &caas.LogicalInfo{
			Id:            luh.snowflake.GenerateID(),
			LogicalUnitId: infoBinding.LogicalUnitId,
			InfoType:      infoBinding.InfoType,
			Info:          info,
		}
		if err := luh.caasDB.Model(&caas.LogicalInfo{}).Create(logicalInfo).Error; err != nil {
			luh.log.Error("unable to bind info", zap.Error(err))
			return err
		}
	}

	return nil
}

func (luh *LogicalUnitHandler) GetLogicalUnitInfo(ctx context.Context, logicalUnitId string) (*logicalunit.UnitInfoDetails, error) {
	if len(logicalUnitId) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.LogicalUnitIdIsNull, "logicalUnitId is empty")
	}
	var InfoDetail logicalunit.UnitInfoDetails
	var clusterList stellarisv1alhpha1.ClusterList
	LogicalUnitInfoList := make([]caas.LogicalInfo, 0)
	BackupIds := make([]logicalunit.InfoBindingIds, 0)
	RegistryIds := make([]logicalunit.InfoBindingIds, 0)
	Clusters := make([]logicalunit.InfoBindingCluster, 0)
	// 从数据库中获取逻辑单元详情表信息
	if err := luh.caasDB.Model(&caas.LogicalInfo{}).
		Where("logical_unit_id = ?", logicalUnitId).
		Find(&LogicalUnitInfoList).Error; err != nil {
		luh.log.Error("unable to get logical unit info", zap.Error(err))
		return nil, err
	}
	// 获取到集群列表
	if err := clientmgr.GetLocalCluster().GetClient().GetCtrlClient().List(ctx, &clusterList); err != nil {
		return nil, errors.NewFromError(ctx, err)
	}
	for _, info := range LogicalUnitInfoList {
		// 通过info.InfoType进行判断是什么类型数据
		if info.InfoType == "cluster" {
			// 判断集群信息是否已经过期
			notInClusterList := true
			for _, v := range clusterList.Items {
				if v.Name == info.Info {
					notInClusterList = false
					break
				}
			}
			if notInClusterList {
				luh.caasDB.Model(&caas.LogicalInfo{}).
					Where("logical_unit_id = ?", logicalUnitId).
					Where("info = ?", info.Info).
					Delete(&caas.LogicalInfo{})
				continue
			}
			// 根据集群名称获取到集群对象和返回参数
			//getCluster, err := clientmgr.GetCluster(info.Info)
			response, err := luh.handler.WithClusterName(ctx, info.Info)
			if err != nil {
				return nil, errors.NewFromError(ctx, err)
			}

			/*// 通过集群名称获取到当前集群下的存储信息，并进行累加
			clusterStore := 0
			var storageClassList storagev1.StorageClassList
			getCluster.GetClient().GetCtrlClient().List(ctx, &storageClassList)
			for _, storageClass := range storageClassList.Items {
				annotations := storageClass.Annotations
				v := annotations["storageservice.harmonycloud.cn/capacity"]
				if len(v) == 0 {
					continue
				}
				num, err := strconv.ParseInt(v, 10, 0)
				if err != nil {
					return nil, errors.NewFromError(ctx, err)
				}
				clusterStore += int(num)
			}

			// 根据集群对象获取到当前集群下的节点，并根据类型进行数据展示
			nodeNum := make(map[string]int)
			var nodeList v1.NodeList
			getCluster.GetClient().GetCtrlClient().List(ctx, &nodeList)
			for _, node := range nodeList.Items {
				nodeNum[node.Status.NodeInfo.Architecture] += 1
			}

			// 根据集群对象获取到当前集群下的网络信息
			netWork := make(map[string]string)
			var netList v1alpha1.NetworkDetailList
			getCluster.GetClient().GetCtrlClient().List(ctx, &netList)
			for _, net := range netList.Items {
				netWork[net.Name] = net.Spec.StackMode
			}

			// 根据集群对象获取到当前集群下的负载均衡信息
			ingressMap := make(map[string]int)
			var ingressList ingress.IngressClassList
			getCluster.GetClient().GetCtrlClient().List(ctx, &ingressList)
			for _, v := range ingressList.Items {
				split := strings.Split(string(v.Spec.Type), "-")
				ingressMap[split[0]] += 1
			}*/

			// 将数据写入集群信息列表
			Clusters = append(Clusters, logicalunit.InfoBindingCluster{
				ClusterName:   response.Name,
				ClusterStatus: string(response.Status),
				ClusterLabel:  response.Labels,
				K8sVersion:    response.K8sVersion,
				Prole:         getProle(*response.Labels),
				//ClusterNetwork:  netWork,
				NodeCount: response.NodeCount,
				//ClusterStore:    clusterStore,
				//NodeNum:         nodeNum,
				//IngressClassNum: ingressMap,
			})
		} else if info.InfoType == "harbor" {
			// 查询制品服务是否过期，过期则删除且不展示
			registryList := []caas.Registry{}
			if err := luh.caasDB.Model(&caas.Registry{}).
				Where("registry_id = ?", info.Info).
				Find(&registryList).Error; err != nil {
				luh.log.Error("unable to get registry info", zap.Error(err))
				return nil, err
			}
			if len(registryList) == 0 {
				luh.caasDB.Model(&caas.LogicalInfo{}).
					Where("logical_unit_id = ?", logicalUnitId).
					Where("info = ?", info.Info).
					Delete(&caas.LogicalInfo{})
				continue
			}
			// 数据未过期，加入制品服务id列表中
			RegistryIds = append(RegistryIds, logicalunit.InfoBindingIds{
				Id: info.Info,
			})
		} else {
			// 查询数据是否过期，过期则删除且不展示
			backupList := []caas.BackupServer{}
			if err := luh.caasDB.Model(&caas.BackupServer{}).
				Where("id = ?", info.Info).Find(&backupList).Error; err != nil {
				luh.log.Error("unable to get backup server info", zap.Error(err))
				return nil, err
			}
			if len(backupList) == 0 {
				luh.caasDB.Model(&caas.LogicalInfo{}).
					Where("info = ?", info.Info).
					Where("logical_unit_id = ?", logicalUnitId).
					Delete(&caas.LogicalInfo{})
				continue
			}
			// 未过期，加入备份服务器id列表中
			BackupIds = append(BackupIds, logicalunit.InfoBindingIds{
				Id: info.Info,
			})
		}
	}
	// 获取当前逻辑单元信息
	if err := luh.caasDB.Model(&caas.LogicalUnit{}).
		Where("id = ?", logicalUnitId).
		Find(&InfoDetail.LogicalUnit).Error; err != nil {
		luh.log.Error("unable to get logical unit", zap.Error(err))
		return nil, err
	}
	InfoDetail.RegistryIds = RegistryIds
	InfoDetail.BackupServerIds = BackupIds
	InfoDetail.BindingClusters = Clusters
	return &InfoDetail, nil
}

func (luh *LogicalUnitHandler) GetClusterIngress(ctx context.Context, clusterName string) ([]logicalunit.ClusterIngressClass, error) {
	if len(clusterName) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ClusterNameIsNull, "clusterName is empty")
	}
	getCluster, err := clientmgr.GetCluster(clusterName)
	if err != nil {
		return nil, errors.NewFromError(ctx, err)
	}
	// 根据集群对象获取到当前集群下的负载均衡信息
	ingressMap := make(map[string]int)
	var ingressList ingress.IngressClassList
	getCluster.GetClient().GetCtrlClient().List(ctx, &ingressList)
	for _, v := range ingressList.Items {
		split := strings.Split(string(v.Spec.Type), "-")
		ingressMap[split[0]] += 1
	}
	clusterIngress := []logicalunit.ClusterIngressClass{}
	for key, value := range ingressMap {
		clusterIngress = append(clusterIngress, logicalunit.ClusterIngressClass{
			IngressName: key,
			Count:       value,
		})
	}
	return clusterIngress, nil
}

func (luh *LogicalUnitHandler) GetClusterNetwork(ctx context.Context, clusterName string) ([]logicalunit.ClusterNetwork, error) {
	if len(clusterName) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ClusterNameIsNull, "clusterName is empty")
	}
	localCluster := clientmgr.GetLocalCluster()
	// 根据集群对象获取到当前集群下的网络信息
	var clusterAddon stellarisv1alhpha1.ClusterAddon

	// get addon
	if err := localCluster.GetClient().GetCtrlClient().
		Get(ctx, client.ObjectKey{Namespace: addon.GetAddonNamespace(clusterName), Name: "heimdallr"}, &clusterAddon); err != nil {
		return nil, err
	}
	if clusterAddon.Status.Configurations == nil || clusterAddon.Status.Configurations.ConfigurationSchemaData == nil {
		return nil, nil
	}
	schemeData := clusterAddon.Status.Configurations.ConfigurationSchemaData
	test, err := schemeData.MarshalJSON()
	if err != nil {
		return nil, err
	}
	obj := struct {
		Heimdallr struct {
			HeimdallrCueRender []struct {
				Name      string `json:"name"`
				StackMode string `json:"stackMode"`
			} `json:"heimdallrCueRender"`
		} `json:"heimdallr"`
	}{}

	if err := json.Unmarshal(test, &obj); err != nil {
		return nil, err
	}
	clusterNets := []logicalunit.ClusterNetwork{}
	for _, render := range obj.Heimdallr.HeimdallrCueRender {
		clusterNets = append(clusterNets, logicalunit.ClusterNetwork{
			NetworkName: render.Name,
			StackMode:   render.StackMode,
		})
	}
	return clusterNets, nil
}

func (luh *LogicalUnitHandler) GetClusterNode(ctx context.Context, clusterName string) ([]logicalunit.ClusterNode, error) {
	if len(clusterName) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ClusterNameIsNull, "clusterName is empty")
	}
	getCluster, err := clientmgr.GetCluster(clusterName)
	if err != nil {
		return nil, errors.NewFromError(ctx, err)
	}
	// 根据集群对象获取到当前集群下的节点，并根据类型进行数据展示
	nodeMap := make(map[string]int)
	var nodeList v1.NodeList
	getCluster.GetClient().GetCtrlClient().List(ctx, &nodeList)
	for _, node := range nodeList.Items {
		nodeMap[node.Status.NodeInfo.Architecture] += 1
	}
	clusterNodes := []logicalunit.ClusterNode{}
	for key, value := range nodeMap {
		clusterNodes = append(clusterNodes, logicalunit.ClusterNode{
			NodeName: key,
			Count:    value,
		})
	}
	return clusterNodes, nil
}

func (luh *LogicalUnitHandler) GetClusterStorage(ctx context.Context, clusterName string) (*logicalunit.ClusterStorageClass, error) {
	if len(clusterName) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ClusterNameIsNull, "clusterName is empty")
	}
	getCluster, err := clientmgr.GetCluster(clusterName)
	if err != nil {
		return nil, errors.NewFromError(ctx, err)
	}
	// 通过集群名称获取到当前集群下的存储信息，并进行累加
	clusterStorage := logicalunit.ClusterStorageClass{}
	var storageClassList storagev1.StorageClassList
	getCluster.GetClient().GetCtrlClient().List(ctx, &storageClassList)
	for _, storageClass := range storageClassList.Items {
		annotations := storageClass.Annotations
		v := annotations["storageservice.harmonycloud.cn/capacity"]
		if len(v) == 0 {
			continue
		}
		num, err := strconv.ParseInt(v, 10, 0)
		if err != nil {
			return nil, errors.NewFromError(ctx, err)
		}
		clusterStorage.Count += int(num)
	}
	return &clusterStorage, nil
}

func (luh *LogicalUnitHandler) DeleteLogicalInfo(ctx context.Context, logicalUnitId string, info string, infoType string) error {
	if len(logicalUnitId) == 0 {
		return errors.NewFromCodeWithMessage(errors.Var.LogicalUnitIdIsNull, "logiclaUnitId is empty")
	}
	if len(info) == 0 {
		return errors.NewFromCodeWithMessage(errors.Var.LogicalInfoInfoIsNull, "logicalInfo info is null")
	}
	if len(infoType) == 0 {
		return errors.NewFromCodeWithMessage(errors.Var.LogicalInfoTypeIsNull, "logicalInfo type is null")
	}
	if err := luh.caasDB.Model(&caas.LogicalInfo{}).
		Where("logical_unit_id = ?", logicalUnitId).
		Where("info_type = ?", infoType).
		Where("info = ?", info).
		Delete(&caas.LogicalInfo{}).Error; err != nil {
		luh.log.Error("unable to delete logical info", zap.Error(err))
		return err
	}
	return nil
}

func getProle(labels string) []clustermodel.ProleType {
	if strings.Contains(labels, "unified-platform.harmonycloud.cn/hub-cluster=true") {
		return []clustermodel.ProleType{clustermodel.ProleTypeHub, clustermodel.ProleTypeBusiness}
	} else {
		return []clustermodel.ProleType{clustermodel.ProleTypeBusiness}
	}
}
