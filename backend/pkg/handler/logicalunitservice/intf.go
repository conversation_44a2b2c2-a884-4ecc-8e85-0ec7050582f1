package logicalunitservice

import (
	"context"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/logicalunit"
)

type LogicalUnitIntf interface {
	ListDataCenter(ctx context.Context) ([]logicalunit.DataCenter, error)
	CreateDataCenter(ctx context.Context, dataCenter *logicalunit.DataCenter) (error, *int64)
	DeleteDataCenter(ctx context.Context, id string) error
	UpdateDataCenter(ctx context.Context, dataCenter *logicalunit.DataCenter) error
	ListCenterAndUnit(ctx context.Context) ([]logicalunit.DataCenterAndUnit, error)
	CreateLogicalUnit(ctx context.Context, logicalUnit *logicalunit.LogicalUnit) (*caas.LogicalUnit, error)
	DeleteLogicalUnit(ctx context.Context, logicalUnitId string) error
	UpdateLogicalUnit(ctx context.Context, logicalUnit *logicalunit.LogicalUnit) error
	LogicalUnitInfoBind(ctx context.Context, infoBinding logicalunit.InfoBinding) error
	GetLogicalUnitClusters(ctx context.Context, logicalUnitId string, dataCenterId string) ([]logicalunit.LogicalUnitClusteStatus, error)
	GetOrganLogicalUnitClusters(ctx context.Context, logicalUnitId string, dataCenterId string, organId string) ([]logicalunit.LogicalUnitClusteStatus, error)
	GetProjectLogicalUnitClusters(ctx context.Context, logicalUnitId string, dataCenterId string, organId string, projectId string) ([]logicalunit.LogicalUnitClusteStatus, error)
	GetDataCenterInfo(ctx context.Context, dataCenterId string) (*logicalunit.DataCenterInfo, error)
	GetLogicalUnitInfo(ctx context.Context, logicalUnitId string) (*logicalunit.UnitInfoDetails, error)
	GetClusterIngress(ctx context.Context, clusterName string) ([]logicalunit.ClusterIngressClass, error)
	GetClusterNetwork(ctx context.Context, clusterName string) ([]logicalunit.ClusterNetwork, error)
	GetClusterNode(ctx context.Context, clusterName string) ([]logicalunit.ClusterNode, error)
	GetClusterStorage(ctx context.Context, clusterName string) (*logicalunit.ClusterStorageClass, error)
	DeleteLogicalInfo(ctx context.Context, logicalUnitId string, info string, infoType string) error
}

type LogicalAndOrganIntf interface {
	GetLogicalUnitByOrgan(ctx context.Context, organId string) ([]logicalunit.DataCenterAndUnit, error)
}

type LogicalAndProjectIntf interface {
	GetLogicalUnitByProject(ctx context.Context, organId string, projectId string) ([]logicalunit.DataCenterAndUnit, error)
}
