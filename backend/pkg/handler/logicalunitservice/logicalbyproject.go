package logicalunitservice

import (
	"context"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/database"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/logicalunit"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/snowflake"
	"k8s.io/apimachinery/pkg/util/sets"
)

func NewLogicalAndProjectHandler() LogicalAndProjectIntf {
	return &LogicalAndProjectHandler{
		caasDB:    database.CaasDB,
		log:       logger.GetLogger(),
		snowflake: snowflake.NewSnowflakeIntf(),
		handler:   cluster.NewHandler(),
	}
}

type LogicalAndProjectHandler struct {
	caasDB    *gorm.DB
	log       *zap.Logger
	snowflake snowflake.SnowflakeIntf
	handler   cluster.Handler
}

func (laoh *LogicalAndProjectHandler) GetLogicalUnitByProject(ctx context.Context, organId string, projectId string) ([]logicalunit.DataCenterAndUnit, error) {
	dataCenterAndUnits := []logicalunit.DataCenterAndUnit{}
	projects := []caas.ProjectQuota{}
	if err := laoh.caasDB.Model(&caas.ProjectQuota{}).
		Where("organ_id = ?", organId).
		Where("project_id = ?", projectId).
		Where("cluster_name is not null").Group("cluster_name").
		Find(&projects).Error; err != nil {
		laoh.log.Error("unable to find projectQuota", zap.Error(err))
		return nil, err
	}
	clusterNames := sets.New[string]()
	for _, project := range projects {
		clusterNames.Insert(project.ClusterName)
	}

	projectIcs := []caas.ProjectIC{}
	if err := laoh.caasDB.Model(&caas.ProjectIC{}).
		Where("organ_id = ?", organId).
		Where("project_id = ?", projectId).Group("cluster_name").
		Find(&projectIcs).Error; err != nil {
		laoh.log.Error("unable to find projectQuota", zap.Error(err))
		return nil, err
	}

	for _, projectIc := range projectIcs {
		clusterNames.Insert(projectIc.ClusterName)
	}

	unitByCenterMap := make(map[int64]sets.Set[logicalunit.LogicalUnit], 0)
	dataCenterIds := make([]int64, 0)
	dataCenters := []caas.DataCenter{}
	unbindingFlag := false
	for _, clusterName := range clusterNames.UnsortedList() {
		logicalInfos := []caas.LogicalInfo{}
		logicalUnits := []caas.LogicalUnit{}
		logicalUnitIds := make([]int64, 0)
		// 根据集群名字获取到逻辑单元详情
		if err := laoh.caasDB.
			Model(&caas.LogicalInfo{}).
			Where("info = ?", clusterName).
			Find(&logicalInfos).Error; err != nil {
			laoh.log.Error("unable to find logicalInfo", zap.Error(err))
			return nil, err
		}
		if len(logicalInfos) == 0 {
			unbindingFlag = true
			continue
		}
		for _, info := range logicalInfos {
			logicalUnitIds = append(logicalUnitIds, info.LogicalUnitId)
		}
		// 根据逻辑单元id获取到逻辑单元数据
		if err := laoh.caasDB.Model(&caas.LogicalUnit{}).
			Where("id in ?", logicalUnitIds).
			Find(&logicalUnits).Error; err != nil {
			laoh.log.Error("unable to find logicalUnit", zap.Error(err))
			return nil, err
		}

		for _, logicalUnit := range logicalUnits {
			_, exist := unitByCenterMap[logicalUnit.DataCenterId]
			if exist {
				unitByCenterMap[logicalUnit.DataCenterId].Insert(logicalunit.LogicalUnit{
					Id:   logicalUnit.Id,
					Name: logicalUnit.Name,
				})
			} else {
				unitByCenterMap[logicalUnit.DataCenterId] = sets.New[logicalunit.LogicalUnit](logicalunit.LogicalUnit{
					Id:   logicalUnit.Id,
					Name: logicalUnit.Name,
				})
			}
			dataCenterIds = append(dataCenterIds, logicalUnit.DataCenterId)
		}
	}
	// 根据数据中心id获取到数据中心数据
	if err := laoh.caasDB.Model(&caas.DataCenter{}).
		Where("id in ?", dataCenterIds).
		Find(&dataCenters).Error; err != nil {
		laoh.log.Error("unable to find dataCenter", zap.Error(err))
		return nil, err
	}
	// 根据数据中心列表获取到它们下面有该集群的逻辑单元列表
	for _, dataCenter := range dataCenters {
		dataCenterAndUnits = append(dataCenterAndUnits, logicalunit.DataCenterAndUnit{
			Id:              dataCenter.Id,
			Name:            dataCenter.Name,
			LogicalUnitList: unitByCenterMap[dataCenter.Id].UnsortedList(),
		})
	}
	if unbindingFlag {
		dataCenterAndUnits = append(dataCenterAndUnits, logicalunit.DataCenterAndUnit{
			Id: -1,
		})
	}
	return dataCenterAndUnits, nil
}
