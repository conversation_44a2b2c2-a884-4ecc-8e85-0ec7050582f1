package logicalunitservice

import (
	"context"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/database"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/logicalunit"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/snowflake"
	"k8s.io/apimachinery/pkg/util/sets"
)

func NewLogicalAndOrganHandler() LogicalAndOrganIntf {
	return &LogicalAndOrganHandler{
		caasDB:    database.CaasDB,
		log:       logger.GetLogger(),
		snowflake: snowflake.NewSnowflakeIntf(),
		handler:   cluster.NewHandler(),
	}
}

type LogicalAndOrganHandler struct {
	caasDB    *gorm.DB
	log       *zap.Logger
	snowflake snowflake.SnowflakeIntf
	handler   cluster.Handler
}

func (laoh *LogicalAndOrganHandler) GetLogicalUnitByOrgan(ctx context.Context, organId string) ([]logicalunit.DataCenterAndUnit, error) {
	dataCenterAndUnits := []logicalunit.DataCenterAndUnit{}
	organs := []caas.OrganQuota{}
	if err := laoh.caasDB.Model(&caas.OrganQuota{}).
		Where("organ_id = ?", organId).
		Where("cluster_name is not null").Group("cluster_name").
		Find(&organs).Error; err != nil {
		laoh.log.Error("unable to find organQuota", zap.Error(err))
		return nil, err
	}
	clusterNames := sets.New[string]()
	for _, organ := range organs {
		clusterNames.Insert(organ.ClusterName)
	}

	organIcs := []caas.OrganIC{}
	if err := laoh.caasDB.Model(&caas.OrganIC{}).
		Where("organ_id = ?", organId).Group("cluster_name").
		Find(&organIcs).Error; err != nil {
		laoh.log.Error("unable to find projectQuota", zap.Error(err))
		return nil, err
	}

	for _, organIc := range organIcs {
		clusterNames.Insert(organIc.ClusterName)
	}

	dataCenterIds := make([]int64, 0)
	unitByCenterMap := make(map[int64]sets.Set[logicalunit.LogicalUnit], 0)
	dataCenters := []caas.DataCenter{}
	unbindingFlag := false
	for _, clusterName := range clusterNames.UnsortedList() {
		logicalInfos := []caas.LogicalInfo{}
		logicalUnits := []caas.LogicalUnit{}
		logicalUnitIds := make([]int64, 0)

		// 根据集群名字获取到逻辑单元详情
		if err := laoh.caasDB.
			Model(&caas.LogicalInfo{}).
			Where("info = ?", clusterName).
			Find(&logicalInfos).Error; err != nil {
			laoh.log.Error("unable to find logicalInfo", zap.Error(err))
			return nil, err
		}
		if len(logicalInfos) == 0 {
			unbindingFlag = true
			continue
		}
		for _, info := range logicalInfos {
			logicalUnitIds = append(logicalUnitIds, info.LogicalUnitId)
		}
		// 根据逻辑单元id获取到逻辑单元数据
		if err := laoh.caasDB.Model(&caas.LogicalUnit{}).
			Where("id in ?", logicalUnitIds).
			Find(&logicalUnits).Error; err != nil {
			laoh.log.Error("unable to find logicalUnit", zap.Error(err))
			return nil, err
		}

		for _, logicalUnit := range logicalUnits {
			_, exist := unitByCenterMap[logicalUnit.DataCenterId]
			if exist {
				unitByCenterMap[logicalUnit.DataCenterId].Insert(logicalunit.LogicalUnit{
					Id:   logicalUnit.Id,
					Name: logicalUnit.Name,
				})
			} else {
				unitByCenterMap[logicalUnit.DataCenterId] = sets.New[logicalunit.LogicalUnit](logicalunit.LogicalUnit{
					Id:   logicalUnit.Id,
					Name: logicalUnit.Name,
				})
			}
			dataCenterIds = append(dataCenterIds, logicalUnit.DataCenterId)
		}
	}

	// 根据数据中心id获取到数据中心数据
	if err := laoh.caasDB.Model(&caas.DataCenter{}).
		Where("id in ?", dataCenterIds).
		Find(&dataCenters).Error; err != nil {
		laoh.log.Error("unable to find dataCenter", zap.Error(err))
		return nil, err
	}
	// 根据数据中心列表获取到它们下面有该集群的逻辑单元列表
	for _, dataCenter := range dataCenters {
		dataCenterAndUnits = append(dataCenterAndUnits, logicalunit.DataCenterAndUnit{
			Id:              dataCenter.Id,
			Name:            dataCenter.Name,
			LogicalUnitList: unitByCenterMap[dataCenter.Id].UnsortedList(),
		})
	}
	if unbindingFlag {
		dataCenterAndUnits = append(dataCenterAndUnits, logicalunit.DataCenterAndUnit{
			Id: -1,
		})
	}
	return dataCenterAndUnits, nil
}
