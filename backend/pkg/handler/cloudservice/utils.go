package cloudservice

import (
	"fmt"

	cloudservice_v1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/v1alpha1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
)

func getComponentNamespaceByCloudService(cloudServiceName string) string {
	return fmt.Sprintf("cloudservice-%s", cloudServiceName)
}

type cloudComponentArray []cloudservice_v1alpha1.CloudComponent

func (arr *cloudComponentArray) reservePolicy(policy models.CloudServiceComponentPolicy) {
	if arr == nil {
		return
	}
	cc := make([]cloudservice_v1alpha1.CloudComponent, 0, len(*arr))
	for _, item := range *arr {
		if models.CloudServiceComponentPolicy(item.Spec.ClusterPolicy) == policy {
			cc = append(cc, item)
		}
	}
	*arr = cc
}
