package cloudservice

import "testing"

func Test_cloudserviceHandler_ListCloudService(t *testing.T) {
	// todo implements me
}

func Test_cloudserviceHandler_DescribeCloudService(t *testing.T) {
	// todo implements me
}

func Test_cloudserviceHandler_ListCloudServiceManagedComponent(t *testing.T) {
	// todo implements me
}

func Test_cloudserviceHandler_ListCloudServiceWorkComponent(t *testing.T) {
	// todo implements me
}

func Test_cloudserviceHandler_CloudComponentDetail(t *testing.T) {
	// todo implements me
}

func Test_cloudserviceHandler_listCloudServiceComponent(t *testing.T) {
	// todo implements me
}
