package internal

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	appsv1 "k8s.io/api/apps/v1"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/sets"
	runtimeclient "sigs.k8s.io/controller-runtime/pkg/client"
)

func GetPodGetter(wt workloadType, client runtimeclient.Client) (PodGetter, error) {
	switch wt {
	case Deployment:
		return &deploymentPodGetter{
			client: client,
		}, nil

	case DaemonSet:
		return &daemonSetPodGetter{
			client: client,
		}, nil
	case Statefulset:
		return &statefulsetPodGetter{
			client: client,
		}, nil
	case Job:
		return &jobPodGetter{
			client: client,
		}, nil
	case CronJob:
		return &cronjobPodGetter{
			client: client,
		}, nil
	default:
		return nil, fmt.Errorf("unsupport workload code %d", wt)
	}
}

type PodGetter interface {
	ListByWorkload(ctx context.Context, namespace string, name string) ([]corev1.Pod, error)
}

type deploymentPodGetter struct {
	client runtimeclient.Client
}

func (g *deploymentPodGetter) ListByWorkload(ctx context.Context, namespace string, name string) ([]corev1.Pod, error) {
	// find deployment first
	deployment := appsv1.Deployment{}
	if err := g.client.Get(ctx, runtimeclient.ObjectKey{Namespace: namespace, Name: name}, &deployment); err != nil {
		return nil, err
	}
	var matchLabels runtimeclient.MatchingLabels
	matchLabels = deployment.Spec.Selector.MatchLabels

	// get replicas second
	replicasSetList := appsv1.ReplicaSetList{}
	if err := g.client.List(ctx, &replicasSetList, runtimeclient.InNamespace(namespace), matchLabels); err != nil {
		return nil, err
	}
	// filter replicas
	var replicas = make([]appsv1.ReplicaSet, 0, len(replicasSetList.Items)>>1<<1)
	for _, replicasSet := range replicasSetList.Items {
		if isOwner(replicasSet.ObjectMeta, string(deployment.UID)) {
			replicas = append(replicas, replicasSet)
		}
	}

	// list pod after
	pods := corev1.PodList{}
	if err := g.client.List(ctx, &pods, runtimeclient.InNamespace(namespace), matchLabels); err != nil {
		return nil, err
	}

	var result = make([]corev1.Pod, 0, len(pods.Items)>>1<<1)
	keySet := sets.NewString()
	for _, replicasSet := range replicas {
		for _, pod := range pods.Items {
			key := objKeyFunc(pod.ObjectMeta)
			if !keySet.Has(key) && objMatchLabels(pod.ObjectMeta, replicasSet.Labels) {
				result = append(result, pod)
				keySet.Insert(key)
			}
		}
	}
	return result, nil
}

type daemonSetPodGetter struct {
	client runtimeclient.Client
}

func (g *daemonSetPodGetter) ListByWorkload(ctx context.Context, namespace string, name string) ([]corev1.Pod, error) {

	// find daemon set first
	daemonSet := appsv1.DaemonSet{}
	if err := g.client.Get(ctx, runtimeclient.ObjectKey{Namespace: namespace, Name: name}, &daemonSet); err != nil {
		return nil, err
	}
	var matchLabels runtimeclient.MatchingLabels
	matchLabels = daemonSet.Spec.Selector.MatchLabels

	// find pod second
	pods := corev1.PodList{}
	if err := g.client.List(ctx, &pods, runtimeclient.InNamespace(namespace), matchLabels); err != nil {
		return nil, err
	}
	var result = make([]corev1.Pod, 0, len(pods.Items)>>1<<1)
	for _, pod := range pods.Items {
		if isOwner(pod.ObjectMeta, string(daemonSet.UID)) {
			result = append(result, pod)
		}
	}
	return result, nil

}

type statefulsetPodGetter struct {
	client runtimeclient.Client
}

func (g *statefulsetPodGetter) ListByWorkload(ctx context.Context, namespace string, name string) ([]corev1.Pod, error) {
	// find statefulSet set first
	statefulSet := appsv1.StatefulSet{}
	if err := g.client.Get(ctx, runtimeclient.ObjectKey{Namespace: namespace, Name: name}, &statefulSet); err != nil {
		return nil, err
	}
	var matchLabels runtimeclient.MatchingLabels
	matchLabels = statefulSet.Spec.Selector.MatchLabels

	// find pod second
	pods := corev1.PodList{}
	if err := g.client.List(ctx, &pods, runtimeclient.InNamespace(namespace), matchLabels); err != nil {
		return nil, err
	}
	var result = make([]corev1.Pod, 0, len(pods.Items)>>1<<1)
	for _, pod := range pods.Items {
		if isOwner(pod.ObjectMeta, string(statefulSet.UID)) {
			result = append(result, pod)
		}
	}
	return result, nil
}

type jobPodGetter struct {
	client runtimeclient.Client
}

func (g *jobPodGetter) ListByWorkload(ctx context.Context, namespace string, name string) ([]corev1.Pod, error) {
	// find job set first
	job := batchv1.Job{}
	if err := g.client.Get(ctx, runtimeclient.ObjectKey{Namespace: namespace, Name: name}, &job); err != nil {
		return nil, err
	}
	var matchLabels runtimeclient.MatchingLabels
	matchLabels = job.Spec.Selector.MatchLabels

	// find pod second
	pods := corev1.PodList{}
	if err := g.client.List(ctx, &pods, runtimeclient.InNamespace(namespace), matchLabels); err != nil {
		return nil, err
	}
	var result = make([]corev1.Pod, 0, len(pods.Items)>>1<<1)
	for _, pod := range pods.Items {
		if isOwner(pod.ObjectMeta, string(job.UID)) {
			result = append(result, pod)
		}
	}
	return result, nil
}

type cronjobPodGetter struct {
	client runtimeclient.Client
}

func (g *cronjobPodGetter) ListByWorkload(ctx context.Context, namespace string, name string) ([]corev1.Pod, error) {
	// find cronjob first
	cronJob := batchv1.CronJob{}
	if err := g.client.Get(ctx, runtimeclient.ObjectKey{Namespace: namespace, Name: name}, &cronJob); err != nil {
		return nil, err
	}

	// list all namespace job
	jobs := batchv1.JobList{}
	if err := g.client.List(ctx, &jobs, runtimeclient.InNamespace(namespace)); err != nil {
		return nil, err
	}
	var cronJobJobs = make([]batchv1.Job, 0, len(jobs.Items)>>1<<1)

	for _, job := range jobs.Items {
		if isOwner(job.ObjectMeta, string(cronJob.UID)) {
			cronJobJobs = append(cronJobJobs, job)
		}
	}
	podListMap := sync.Map{}
	wait := &sync.WaitGroup{}
	wait.Add(len(cronJobJobs))
	for index, job := range cronJobJobs {
		job := job
		go func() {
			defer wait.Done()
			jpg := jobPodGetter{client: g.client}
			podList, err := jpg.ListByWorkload(ctx, job.Namespace, job.Name)
			if err != nil {
				logger.GetSugared().Errorf("[cronjobPodGetter] ListByWorkload, get pod by job error: %s", err.Error())
				return
			}
			podListMap.Store(index, podList)
		}()
	}

	var result = make([]corev1.Pod, 0)
	podListMap.Range(func(key, value any) bool {
		podList := value.([]corev1.Pod)
		result = append(result, podList...)
		return true
	})

	return result, nil
}

func objMatchLabels(metadata metav1.ObjectMeta, match map[string]string) bool {
	if len(match) == 0 || len(metadata.Labels) == 0 {
		return false
	}

	for key, value := range match {
		v, exist := metadata.Labels[key]
		if !exist || !strings.EqualFold(value, v) {
			return false
		}
	}
	return true
}

func objKeyFunc(metadata metav1.ObjectMeta) string {
	return metadata.Namespace + "/" + metadata.Name
}

func isOwner(metadata metav1.ObjectMeta, uid string) bool {
	if len(metadata.OwnerReferences) == 0 {
		return false
	}
	for _, owner := range metadata.OwnerReferences {
		if strings.EqualFold(string(owner.UID), uid) {
			return true
		}
	}
	return false

}
