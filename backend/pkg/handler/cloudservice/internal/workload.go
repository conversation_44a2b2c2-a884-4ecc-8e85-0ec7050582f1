package internal

import (
	"context"
	"fmt"
	"strings"

	cloudservice_v1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/v1alpha1"
	"harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	runtimeclient "sigs.k8s.io/controller-runtime/pkg/client"
)

func NewWorkloadGetter(options ...WorkloadGetterBuilderOption) WorkloadGetter {
	builder := &workloadGetter{}
	if len(options) != 0 {
		for _, option := range options {
			option.apply(builder)
		}
	}
	return builder
}

type WorkloadGetter interface {
	ListWorkload(ctx context.Context, cluster string) ([]runtimeclient.Object, error)
}

type workloadGetter struct {
	inNamespaces       []string
	matchLabels        map[string]string
	workloadTypes      []workloadType
	workloadReferences []cloudservice_v1alpha1.ObjectReference
}

func (g *workloadGetter) ListWorkload(ctx context.Context, clusterName string) ([]runtimeclient.Object, error) {
	// Get cluster client
	cluster, err := client.OnlineClusterAssert(client.GetCluster(clusterName))
	if err != nil {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ClusterStatusNotOnline, err.Error())
	}

	//workloadReferences为空
	if len(g.workloadReferences) == 0 {
		// check filter
		if len(g.inNamespaces) == 0 {
			return nil, fmt.Errorf("workload namespace is not appoint")
		}

		if len(g.matchLabels) == 0 {
			return nil, fmt.Errorf("workload match label is not appoint")
		}

		if len(g.workloadTypes) == 0 {
			return nil, fmt.Errorf("workload workload type is not appoint")
		}
		workloadCliList := make([]specialWorkloadGetter, 0, len(g.workloadTypes))
		for _, workloadType := range g.workloadTypes {
			workloadCli, err := newFromWorkloadType(workloadType, cluster.GetClient().GetCtrlClient())
			if err != nil {
				logger.GetSugared().Warnf("[ListWorkload] Get Workload cli error,err is '%s'", err.Error())
			} else {
				workloadCliList = append(workloadCliList, workloadCli)
			}
		}

		chList := make([]<-chan []runtimeclient.Object, 0, len(workloadCliList))
		for _, cli := range workloadCliList {
			for _, ns := range g.inNamespaces {
				workloadsChans := cli.getAsync(ctx, ns, g.matchLabels)
				chList = append(chList, workloadsChans)
			}
		}
		readFromChanFunc := func(ch <-chan []runtimeclient.Object) []runtimeclient.Object {
			objs := make([]runtimeclient.Object, 0)
			if ch != nil {
				for obj := range ch {
					objs = append(objs, obj...)
				}
			}
			return objs
		}

		workloads := make([]runtimeclient.Object, 0)
		for _, ch := range chList {
			workloads = append(workloads, readFromChanFunc(ch)...)
		}
		return workloads, nil
	} else {
		chList := make([]<-chan []runtimeclient.Object, 0, len(g.workloadReferences))
		for _, workloadReference := range g.workloadReferences {
			for _, namesapce := range g.inNamespaces {
				if namesapce != workloadReference.Namespace {
					continue
				}
			}

			workloadCli, err := newFromWorkloadType(ParseStringKind(workloadReference.Kind), cluster.GetClient().GetCtrlClient())
			if err != nil {
				logger.GetSugared().Warnf("[ListWorkload] Get Workload cli error,err is '%s'", err.Error())
				break
			}
			workloadsChans := workloadCli.getAsyncByName(ctx, workloadReference.Namespace, workloadReference.Name)
			chList = append(chList, workloadsChans)
		}

		readFromChanFunc := func(ch <-chan []runtimeclient.Object) []runtimeclient.Object {
			objs := make([]runtimeclient.Object, 0)
			if ch != nil {
				for obj := range ch {
					objs = append(objs, obj...)
				}
			}
			return objs
		}

		workloads := make([]runtimeclient.Object, 0)
		for _, ch := range chList {
			workloads = append(workloads, readFromChanFunc(ch)...)
		}
		return workloads, nil
		return nil, nil
	}
}

type WorkloadGetterBuilderOption interface {
	apply(builder *workloadGetter)
}

// 限定查找的Namespace访问
type scopeNamespaceOption []string

func (op scopeNamespaceOption) apply(builder *workloadGetter) {
	builder.inNamespaces = op
}

func NewNamespaceOption(namespaces ...string) WorkloadGetterBuilderOption {
	// 去重
	namespaceSet := make(map[string]struct{})
	for _, namespace := range namespaces {
		_, exist := namespaceSet[namespace]
		if !exist {
			namespaceSet[namespace] = struct{}{}
		}
	}

	// 写入
	var arr = make([]string, 0, (len(namespaceSet)>>1)<<1)
	for namespace, _ := range namespaceSet {
		arr = append(arr, namespace)
	}

	var op scopeNamespaceOption
	op = arr
	return op
}

type scopeWorkloadReferenceOption []cloudservice_v1alpha1.ObjectReference

func (option scopeWorkloadReferenceOption) apply(builder *workloadGetter) {
	builder.workloadReferences = option
}
func NewWorkloadReferenceOption(workloadReferences ...*cloudservice_v1alpha1.ObjectReference) WorkloadGetterBuilderOption {
	var results []cloudservice_v1alpha1.ObjectReference
	if nil == workloadReferences {
		results = make([]cloudservice_v1alpha1.ObjectReference, 0)
	}

	for _, v := range workloadReferences {
		results = append(results, *v)
	}

	var op scopeWorkloadReferenceOption
	op = results
	return op
}

// 限定查找的Namespace访问 限定查找的标签
type scopeMatchLabelsOption map[string]string

func (option scopeMatchLabelsOption) apply(builder *workloadGetter) {
	builder.matchLabels = option
}
func NewMatchLabelsOption(match map[string]string) WorkloadGetterBuilderOption {
	if match == nil {
		match = make(map[string]string, 0)
	}
	var op scopeMatchLabelsOption
	op = match
	return op
}

// 限定要查找的workload

type workloadType int

const (
	Deployment workloadType = iota
	DaemonSet
	Statefulset
	Job
	CronJob
	AllWorkload
)

func ParseWorkloadType(t string) (workloadType, error) {
	if t == "" {
		return AllWorkload, nil
	}
	if equalsIgnoreCase(t, "Deployment") {
		return Deployment, nil
	} else if equalsIgnoreCase(t, "DaemonSet") {
		return DaemonSet, nil
	} else if equalsIgnoreCase(t, "StatefulSet") {
		return Statefulset, nil
	} else if equalsIgnoreCase(t, "Job") {
		return Job, nil
	} else if equalsIgnoreCase(t, "CronJob") {
		return CronJob, nil
	}
	return -1, fmt.Errorf("unknow workload type of %s", t)

}

func equalsIgnoreCase(t1, t2 string) bool {
	t1 = strings.ToLower(t1)
	t2 = strings.ToLower(t2)
	return strings.EqualFold(t1, t2)
}

type scopeMatchWorkloadOption []workloadType

func (option scopeMatchWorkloadOption) apply(builder *workloadGetter) {
	builder.workloadTypes = option
}

func NewMatchWorkloadOption(workloadTypes ...workloadType) WorkloadGetterBuilderOption {
	// 去重
	var workloadMap = make(map[workloadType]struct{})
	for _, wType := range workloadTypes {
		_, exist := workloadMap[wType]
		if !exist {
			workloadMap[wType] = struct{}{}
		}
	}
	// 写入
	_, existAll := workloadMap[AllWorkload]
	if existAll {
		workloadMap[Deployment] = struct{}{}
		workloadMap[DaemonSet] = struct{}{}
		workloadMap[Statefulset] = struct{}{}
		workloadMap[Job] = struct{}{}
		workloadMap[CronJob] = struct{}{}
		delete(workloadMap, AllWorkload)
	}

	var arr = make([]workloadType, 0, (len(workloadMap)>>1)<<1)
	for wType, _ := range workloadMap {
		arr = append(arr, wType)
	}

	var op scopeMatchWorkloadOption
	op = arr
	return op

}
