package internal

import (
	"context"
	"fmt"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	appsv1 "k8s.io/api/apps/v1"
	batchv1 "k8s.io/api/batch/v1"
	runtimeclient "sigs.k8s.io/controller-runtime/pkg/client"
)

func ParseStringKind(kind string) workloadType {
	switch kind {
	case "Deployment":
		return Deployment
	case "StatefulSet":
		return Statefulset
	case "DaemonSet":
		return DaemonSet
	case "CronJob":
		return CronJob
	case "Job":
		return Job
	}
	return -1
}

func newFromWorkloadType(workloadType workloadType, client runtimeclient.Client) (specialWorkloadGetter, error) {
	switch workloadType {
	case Deployment:
		return &deploymentGetter{
			client: client,
		}, nil

	case DaemonSet:
		return &daemonSetGetter{
			client: client,
		}, nil
	case Statefulset:
		return &statefulsetGetter{
			client: client,
		}, nil
	case Job:
		return &jobGetter{
			client: client,
		}, nil
	case CronJob:
		return &cronjobGetter{
			client: client,
		}, nil
	default:
		return nil, fmt.Errorf("unknnow workload type code of '%d'", workloadType)

	}
}

type specialWorkloadGetter interface {
	getAsync(ctx context.Context, namespace string, matchLabels map[string]string) <-chan []runtimeclient.Object

	getAsyncByName(ctx context.Context, namespace, name string) <-chan []runtimeclient.Object
}

type deploymentGetter struct {
	client runtimeclient.Client
}

func (g *deploymentGetter) getAsync(ctx context.Context, namespace string, matchLabels map[string]string) <-chan []runtimeclient.Object {
	ch := make(chan []runtimeclient.Object)
	go func() {
		workloadList := appsv1.DeploymentList{}
		var matchLabelsObject runtimeclient.MatchingLabels
		matchLabelsObject = matchLabels
		err := g.client.List(ctx, &workloadList, runtimeclient.InNamespace(namespace), matchLabelsObject)
		if err != nil {
			logger.GetSugared().Errorf("[deploymentGetter getAsync] list worklod error,err = '%s'", err.Error())
			close(ch)
			return
		}
		var result []runtimeclient.Object = make([]runtimeclient.Object, 0, len(workloadList.Items))
		for _, workload := range workloadList.Items {
			workload := workload
			result = append(result, &workload)
		}
		ch <- result
		close(ch)
		return
	}()
	return ch
}

type daemonSetGetter struct {
	client runtimeclient.Client
}

func (g *daemonSetGetter) getAsync(ctx context.Context, namespace string, matchLabels map[string]string) <-chan []runtimeclient.Object {
	ch := make(chan []runtimeclient.Object)
	go func() {
		workloadList := appsv1.DaemonSetList{}
		var matchLabelsObject runtimeclient.MatchingLabels
		matchLabelsObject = matchLabels
		err := g.client.List(ctx, &workloadList, runtimeclient.InNamespace(namespace), matchLabelsObject)
		if err != nil {
			logger.GetSugared().Errorf("[deploymentGetter getAsync] list worklod error,err = '%s'", err.Error())
			close(ch)
			return
		}
		var result []runtimeclient.Object = make([]runtimeclient.Object, 0, len(workloadList.Items))
		for _, workload := range workloadList.Items {
			workload := workload
			result = append(result, &workload)
		}
		ch <- result
		close(ch)
		return
	}()
	return ch
}

type statefulsetGetter struct {
	client runtimeclient.Client
}

func (g *statefulsetGetter) getAsync(ctx context.Context, namespace string, matchLabels map[string]string) <-chan []runtimeclient.Object {
	ch := make(chan []runtimeclient.Object)
	go func() {
		workloadList := appsv1.StatefulSetList{}
		var matchLabelsObject runtimeclient.MatchingLabels
		matchLabelsObject = matchLabels
		err := g.client.List(ctx, &workloadList, runtimeclient.InNamespace(namespace), matchLabelsObject)
		if err != nil {
			logger.GetSugared().Errorf("[deploymentGetter getAsync] list worklod error,err = '%s'", err.Error())
			close(ch)
			return
		}
		var result []runtimeclient.Object = make([]runtimeclient.Object, 0, len(workloadList.Items))
		for _, workload := range workloadList.Items {
			workload := workload
			result = append(result, &workload)
		}
		ch <- result
		close(ch)
		return
	}()
	return ch
}

type jobGetter struct {
	client runtimeclient.Client
}

func (g *jobGetter) getAsync(ctx context.Context, namespace string, matchLabels map[string]string) <-chan []runtimeclient.Object {
	ch := make(chan []runtimeclient.Object)
	go func() {
		workloadList := batchv1.JobList{}
		var matchLabelsObject runtimeclient.MatchingLabels
		matchLabelsObject = matchLabels
		err := g.client.List(ctx, &workloadList, runtimeclient.InNamespace(namespace), matchLabelsObject)
		if err != nil {
			logger.GetSugared().Errorf("[deploymentGetter getAsync] list worklod error,err = '%s'", err.Error())
			close(ch)
			return
		}
		var result []runtimeclient.Object = make([]runtimeclient.Object, 0, len(workloadList.Items))
		for _, workload := range workloadList.Items {
			workload := workload
			result = append(result, &workload)
		}
		ch <- result
		close(ch)
		return
	}()
	return ch
}

type cronjobGetter struct {
	client runtimeclient.Client
}

func (g *cronjobGetter) getAsync(ctx context.Context, namespace string, matchLabels map[string]string) <-chan []runtimeclient.Object {
	ch := make(chan []runtimeclient.Object)
	go func() {
		workloadList := batchv1.CronJobList{}
		var matchLabelsObject runtimeclient.MatchingLabels
		matchLabelsObject = matchLabels
		err := g.client.List(ctx, &workloadList, runtimeclient.InNamespace(namespace), matchLabelsObject)
		if err != nil {
			logger.GetSugared().Errorf("[deploymentGetter getAsync] list worklod error,err = '%s'", err.Error())
			close(ch)
			return
		}
		var result []runtimeclient.Object = make([]runtimeclient.Object, 0, len(workloadList.Items))
		for _, workload := range workloadList.Items {
			workload := workload
			result = append(result, &workload)
		}
		ch <- result
		close(ch)
		return
	}()
	return ch
}

func (g *cronjobGetter) getAsyncByName(ctx context.Context, namespace, name string) <-chan []runtimeclient.Object {
	ch := make(chan []runtimeclient.Object)
	go func() {
		cronJob := batchv1.CronJob{}
		objectKey := runtimeclient.ObjectKey{
			Namespace: namespace,
			Name:      name,
		}
		if err := g.client.Get(ctx, objectKey, &cronJob); err != nil {
			logger.GetSugared().Errorf("[deploymentGetter getAsync] list worklod error,err = '%s'", err.Error())
			close(ch)
			return
		}

		var result []runtimeclient.Object = make([]runtimeclient.Object, 0, 1)
		result = append(result, &cronJob)

		ch <- result
		close(ch)
		return
	}()
	return ch
}

func (g *jobGetter) getAsyncByName(ctx context.Context, namespace, name string) <-chan []runtimeclient.Object {
	ch := make(chan []runtimeclient.Object)
	go func() {
		job := batchv1.Job{}
		objectKey := runtimeclient.ObjectKey{
			Namespace: namespace,
			Name:      name,
		}
		if err := g.client.Get(ctx, objectKey, &job); err != nil {
			logger.GetSugared().Errorf("[deploymentGetter getAsync] list worklod error,err = '%s'", err.Error())
			close(ch)
			return
		}

		var result []runtimeclient.Object = make([]runtimeclient.Object, 0, 1)
		result = append(result, &job)

		ch <- result
		close(ch)
		return
	}()
	return ch
}

func (g *deploymentGetter) getAsyncByName(ctx context.Context, namespace, name string) <-chan []runtimeclient.Object {
	ch := make(chan []runtimeclient.Object)
	go func() {
		deploy := appsv1.Deployment{}
		objectKey := runtimeclient.ObjectKey{
			Namespace: namespace,
			Name:      name,
		}
		if err := g.client.Get(ctx, objectKey, &deploy); err != nil {
			logger.GetSugared().Errorf("[deploymentGetter getAsync] list worklod error,err = '%s'", err.Error())
			close(ch)
			return
		}

		var result []runtimeclient.Object = make([]runtimeclient.Object, 0, 1)
		result = append(result, &deploy)

		ch <- result
		close(ch)
		return
	}()
	return ch
}

func (g *daemonSetGetter) getAsyncByName(ctx context.Context, namespace, name string) <-chan []runtimeclient.Object {
	ch := make(chan []runtimeclient.Object)
	go func() {
		ds := appsv1.DaemonSet{}
		objectKey := runtimeclient.ObjectKey{
			Namespace: namespace,
			Name:      name,
		}
		if err := g.client.Get(ctx, objectKey, &ds); err != nil {
			logger.GetSugared().Errorf("[deploymentGetter getAsync] list worklod error,err = '%s'", err.Error())
			close(ch)
			return
		}

		var result []runtimeclient.Object = make([]runtimeclient.Object, 0, 1)
		result = append(result, &ds)

		ch <- result
		close(ch)
		return
	}()
	return ch
}

func (g *statefulsetGetter) getAsyncByName(ctx context.Context, namespace, name string) <-chan []runtimeclient.Object {
	ch := make(chan []runtimeclient.Object)
	go func() {
		sts := appsv1.StatefulSet{}
		objectKey := runtimeclient.ObjectKey{
			Namespace: namespace,
			Name:      name,
		}
		if err := g.client.Get(ctx, objectKey, &sts); err != nil {
			logger.GetSugared().Errorf("[deploymentGetter getAsync] list worklod error,err = '%s'", err.Error())
			close(ch)
			return
		}

		var result []runtimeclient.Object = make([]runtimeclient.Object, 0, 1)
		result = append(result, &sts)

		ch <- result
		close(ch)
		return
	}()
	return ch
}
