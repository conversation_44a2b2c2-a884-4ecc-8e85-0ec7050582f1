package cloudservice

import (
	"context"
	"sort"
	"strings"
	"sync"

	"github.com/gin-gonic/gin/binding"
	cloudservice_v1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/v1alpha1"
	"harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/cloudservice/internal"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/resources"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	"k8s.io/apimachinery/pkg/util/sets"
	runtimeclient "sigs.k8s.io/controller-runtime/pkg/client"
)

type cloudserviceHandler struct {
}

func (c *cloudserviceHandler) ListCloudService(ctx context.Context, filter resources.Filter[models.CloudService]) (*models.PageableResponse[models.CloudService], error) {

	cloudservices := &cloudservice_v1alpha1.CloudServiceList{}

	if err := client.GetLocalCluster().GetClient().GetCtrlClient().List(ctx, cloudservices, &runtimeclient.ListOptions{}); err != nil {
		return nil, err
	}

	var modelCloudServices = make(models.CloudServiceList, 0)
	for _, cloudservice := range cloudservices.Items {
		modelCloudServices = append(modelCloudServices, *utils.ConvertCloudService(cloudservice))
	}
	sort.Sort(modelCloudServices)

	return filter.FilterResult(modelCloudServices)
}

func (c *cloudserviceHandler) DescribeCloudService(ctx context.Context, cloudServiceName string) (*models.CloudService, error) {
	cloudservice := &cloudservice_v1alpha1.CloudService{}
	if err := client.GetLocalCluster().GetClient().GetCtrlClient().Get(ctx, runtimeclient.ObjectKey{Name: cloudServiceName}, cloudservice); err != nil {
		return nil, err
	}

	return utils.ConvertCloudService(*cloudservice), nil
}

// ListCloudServiceComponent 获取云服务的云组件列表
func (c *cloudserviceHandler) ListCloudServiceComponent(ctx context.Context, cloudServiceName string, componentType, cluster string) (models.CloudServiceComponentSortByClusterList, error) {
	// filter cluster
	var matchLabels map[string]string = make(map[string]string, 4)
	if cluster != "" {
		matchLabels[utils.CloudComponentBelongingClusterLabelKey] = cluster
	}
	components, err := c.listCloudServiceComponent(ctx, cloudServiceName, matchLabels)
	if err != nil {
		return nil, err
	}
	var arr cloudComponentArray = components
	// filter component policy
	if componentType != "" {
		arr.reservePolicy(models.CloudServiceComponentPolicy(componentType))
	}
	mids := make([]*models.CloudServiceComponent, 0, len(arr))
	for _, item := range arr {
		conv := utils.Convert2CloudServiceComponent(item)
		mids = append(mids, &conv)
	}

	midMapByCluster := make(map[string][]models.CloudServiceComponent)
	for _, mid := range mids {
		cluster := mid.Cluster
		arr := midMapByCluster[cluster]
		if arr == nil {
			arr = make([]models.CloudServiceComponent, 0)
		}
		arr = append(arr, *mid)
		midMapByCluster[cluster] = arr
	}

	result := make([]models.CloudServiceComponentSortByCluster, 0, len(midMapByCluster))
	for cluster, clusterComponent := range midMapByCluster {
		result = append(result, models.CloudServiceComponentSortByCluster{
			Cluster:    cluster,
			Components: clusterComponent,
		})
	}
	var resultSort models.CloudServiceComponentSortByClusterList
	resultSort = result
	sort.Sort(resultSort)
	return resultSort, nil

}

func (c *cloudserviceHandler) CloudComponentDetail(ctx context.Context, cloudServiceName string, cloudComponentName string, cluster string) (*models.CloudServiceComponent, error) {
	component, err := c.cloudServiceComponentDetail(ctx, cloudServiceName, cloudComponentName, cluster)
	if err != nil {
		return nil, err
	}
	result := utils.Convert2CloudServiceComponent(*component)
	return &result, nil

}

func (c *cloudserviceHandler) CloudComponentWorkloads(ctx context.Context, param models.CloudComponentWorkloadReadParam) ([]*models.CloudComponentWorkload, error) {
	// 基本参数校验
	if err := binding.Validator.ValidateStruct(param); err != nil {
		return nil, err
	}

	component, err := c.cloudServiceComponentDetail(ctx, param.CloudServiceName, param.CloudComponentName, param.Cluster)
	if err != nil {
		return nil, err
	}
	var workloadlabels map[string]string
	if component.Spec.LabelSelector != nil {
		workloadlabels = component.Spec.MatchLabels
	}

	workloadReferences := component.Spec.WorkloadReferences

	// do for namespace
	var workloadInNamespaces []string

	if workloadReferences == nil {
		if param.Namespace == "" {
			workloadInNamespaces = component.Spec.Namespaces
		} else {
			namespaceSet := sets.NewString(component.Spec.Namespaces...)
			if !namespaceSet.Has(param.Namespace) {
				return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, "namespace '"+param.Namespace+"' is illegal")
			}
			workloadInNamespaces = make([]string, 0, 1)
			workloadInNamespaces = append(workloadInNamespaces, param.Namespace)
		}
	} else {
		workloadInNamespaces = make([]string, 0, 1)
		workloadInNamespaces = append(workloadInNamespaces, param.Namespace)
	}

	// do for workload type
	wt, err := internal.ParseWorkloadType(string(param.WorkloadType))
	if err != nil {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, "un support workload type of "+string(param.WorkloadType))
	}

	builder := internal.NewWorkloadGetter(internal.NewMatchWorkloadOption(wt), internal.NewNamespaceOption(workloadInNamespaces...), internal.NewMatchLabelsOption(workloadlabels), internal.NewWorkloadReferenceOption(workloadReferences...))
	workloads, err := builder.ListWorkload(ctx, param.Cluster)
	if err != nil {
		return nil, err
	}

	var result []*models.CloudComponentWorkload
	for _, workload := range workloads {
		r := utils.Convert2CloudComponentWorkload(workload)
		result = append(result, &r)
	}
	var resultSort models.CloudServiceWorkloadList = result
	sort.Sort(resultSort)
	return result, nil

}

// CloudComponentPodInstance
// 查看云组件的的Workload的Pod列表
func (c *cloudserviceHandler) CloudComponentPodInstance(ctx context.Context, param models.CloudComponentWorkloadPodInstanceReadParam) ([]*models.CloudComponentWorkloadPodGrouper, error) {
	// 基本参数校验
	if err := binding.Validator.ValidateStruct(param); err != nil {
		return nil, err
	}

	// get cluster cli
	cluster, err := client.OnlineClusterAssert(client.GetCluster(param.Cluster))
	if err != nil {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ClusterStatusNotOnline, err.Error())
	}

	// get workloads forst
	workloads, err := c.CloudComponentWorkloads(ctx, models.CloudComponentWorkloadReadParam{
		CloudServiceName:   param.CloudServiceName,
		CloudComponentName: param.CloudComponentName,
		WorkloadType:       param.WorkloadType,
		Namespace:          param.Namespace,
		Cluster:            param.Cluster,
	})
	if err != nil {
		return nil, err
	}

	// deal with workload name is not empty
	if !utils.HasEmpty(string(param.WorkloadType), param.Namespace, param.WorkloadName) {
		var workload *models.CloudComponentWorkload
		for _, wl := range workloads {
			if strings.EqualFold(wl.Name, param.WorkloadName) {
				workload = wl
			}
		}
		if workload == nil {
			return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, "workload "+param.WorkloadName+" is not exist")
		}
		workloads = []*models.CloudComponentWorkload{workload}
	}

	var results = make([]*models.CloudComponentWorkloadPodGrouper, 0, len(workloads))
	for _, workload := range workloads {
		results = append(results, &models.CloudComponentWorkloadPodGrouper{
			CloudComponentWorkload: *workload,
		})
	}

	wg := &sync.WaitGroup{}
	wg.Add(len(results))
	for _, result := range results {
		result := result
		go func() {
			defer wg.Done()
			wt, err := internal.ParseWorkloadType(string(result.WorkloadType))
			if err != nil {
				logger.GetSugared().Warnf("un support workload type,workload is %+v,err is %s", result, err)
				return
			}
			podGetter, err := internal.GetPodGetter(wt, cluster.GetClient().GetCtrlClient())
			if err != nil {
				logger.GetSugared().Warnf("cloud not get pod getter,workload is %+v,err is %s", result, err)
				return
			}

			pods, err := podGetter.ListByWorkload(ctx, result.Namespace, result.Name)
			if err != nil {
				logger.GetSugared().Warnf("list workload pod error,workload is %+v,err is %s", result, err)
				return
			}
			var podInstances = make(models.CloudComponentWorkloadPodInstanceList, 0, len(pods))
			for _, pod := range pods {
				p := utils.Convert2CloudComponentWorkloadPodInstance(pod)
				podInstances = append(podInstances, p)
			}
			sort.Sort(podInstances)
			result.Pods = podInstances
		}()
	}
	wg.Wait()
	return results, nil

}

// cloudServiceComponentDetail
// 获取云组件详情
func (c *cloudserviceHandler) cloudServiceComponentDetail(ctx context.Context, cloudServiceName string, cloudComponentName string, cluster string) (*cloudservice_v1alpha1.CloudComponent, error) {
	matchLabels := make(map[string]string, 4)
	// 限制云组件名称
	matchLabels[utils.CloudComponentNameLabelKey] = cloudComponentName
	// 限制集群名称
	matchLabels[utils.CloudComponentBelongingClusterLabelKey] = cluster

	components, err := c.listCloudServiceComponent(ctx, cloudServiceName, matchLabels)
	if err != nil {
		return nil, err
	}
	if len(components) == 0 {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ResourceNotFount, "cloud component is not exist")
	}
	if len(components) > 1 {
		logger.GetSugared().Warnf("[CloudComponentDetail] Get CloudComponent Size > 1,ServiceName:'%s',ComponentName:'%s',Cluster:'%s'", cloudServiceName, cloudComponentName, cluster)
	}
	return &components[0], err
}

// listCloudServiceComponent
// 查找云服务的云组件
func (c *cloudserviceHandler) listCloudServiceComponent(ctx context.Context, cloudServiceName string, matchLabels map[string]string) ([]cloudservice_v1alpha1.CloudComponent, error) {
	// check cloud service exist
	cloudService := cloudservice_v1alpha1.CloudService{}
	if err := client.GetLocalCluster().GetClient().GetCtrlClient().Get(ctx, runtimeclient.ObjectKey{Name: cloudServiceName}, &cloudService); err != nil {
		return nil, err
	}

	// get Component namespace
	cloudComponentNamespace := getComponentNamespaceByCloudService(cloudServiceName)

	// deal with match labels
	if matchLabels == nil {
		matchLabels = make(map[string]string, 2)
	}
	matchLabels[utils.CloudComponentBelongingCloudServiceLabelKey] = cloudServiceName

	// cloud component
	cloudComponentList := cloudservice_v1alpha1.CloudComponentList{}
	if err := client.GetLocalCluster().GetClient().GetCtrlClient().List(ctx, &cloudComponentList,
		runtimeclient.InNamespace(cloudComponentNamespace),
		runtimeclient.MatchingLabels(matchLabels),
	); err != nil {
		return nil, err
	}

	var pointerSlice []*cloudservice_v1alpha1.CloudComponent = utils.StructConvert2Pointer[cloudservice_v1alpha1.CloudComponent](cloudComponentList.Items)
	utils.SortByCreateTime(pointerSlice)
	var result []cloudservice_v1alpha1.CloudComponent = utils.PointerConvert2Struct(pointerSlice)
	return result, nil
}
