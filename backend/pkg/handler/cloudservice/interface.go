package cloudservice

import (
	"context"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/resources"
	database_aop "harmonycloud.cn/unifiedportal/translate-sdk-golang/database-aop"
)

func NewCloudServiceHandler() HandlerIntf {
	th := &translateHandler{}
	th.handler = &cloudserviceHandler{}
	return th
}

type HandlerIntf interface {
	// ListCloudService 获取云服务列表
	ListCloudService(ctx context.Context, filter resources.Filter[models.CloudService]) (*models.PageableResponse[models.CloudService], error)

	// DescribeCloudService 获取云服务的描述
	DescribeCloudService(ctx context.Context, cloudServiceName string) (*models.CloudService, error)

	// ListCloudServiceComponent 获取云服务的云组件列表
	ListCloudServiceComponent(ctx context.Context, cloudServiceName string, componentType, cluster string) (models.CloudServiceComponentSortByClusterList, error)

	// CloudComponentDetail
	// 查看云组件的详情
	CloudComponentDetail(ctx context.Context, cloudServiceName string, cloudComponentName string, cluster string) (*models.CloudServiceComponent, error)

	// CloudComponentWorkloads
	// 查看云组件的的Workload
	CloudComponentWorkloads(ctx context.Context, param models.CloudComponentWorkloadReadParam) ([]*models.CloudComponentWorkload, error)

	// CloudComponentPodInstance
	// 查看云组件的的Workload的Pod列表
	CloudComponentPodInstance(ctx context.Context, param models.CloudComponentWorkloadPodInstanceReadParam) ([]*models.CloudComponentWorkloadPodGrouper, error)
}

type translateHandler struct {
	handler HandlerIntf
}

// ListCloudService 获取云服务列表
func (t *translateHandler) ListCloudService(ctx context.Context, filter resources.Filter[models.CloudService]) (*models.PageableResponse[models.CloudService], error) {
	result, err := t.handler.ListCloudService(ctx, filter)
	database_aop.DoTranslate(ctx, result, err)
	return result, err
}

// DescribeCloudService 获取云服务的描述
func (t *translateHandler) DescribeCloudService(ctx context.Context, cloudServiceName string) (*models.CloudService, error) {
	result, err := t.handler.DescribeCloudService(ctx, cloudServiceName)
	database_aop.DoTranslate(ctx, result, err)
	return result, err
}

// ListCloudServiceComponent 获取云服务的云组件列表
func (t *translateHandler) ListCloudServiceComponent(ctx context.Context, cloudServiceName string, componentType, cluster string) (models.CloudServiceComponentSortByClusterList, error) {
	result, err := t.handler.ListCloudServiceComponent(ctx, cloudServiceName, componentType, cluster)
	database_aop.DoTranslate(ctx, result, err)
	return result, err
}

// CloudComponentDetail
// 查看云组件的详情
func (t *translateHandler) CloudComponentDetail(ctx context.Context, cloudServiceName string, cloudComponentName string, cluster string) (*models.CloudServiceComponent, error) {
	result, err := t.handler.CloudComponentDetail(ctx, cloudServiceName, cloudComponentName, cluster)
	database_aop.DoTranslate(ctx, result, err)
	return result, err
}

// CloudComponentWorkloads
// 查看云组件的的Workload
func (t *translateHandler) CloudComponentWorkloads(ctx context.Context, param models.CloudComponentWorkloadReadParam) ([]*models.CloudComponentWorkload, error) {
	result, err := t.handler.CloudComponentWorkloads(ctx, param)
	database_aop.DoTranslate(ctx, result, err)
	return result, err
}

// CloudComponentPodInstance
// 查看云组件的的Workload的Pod列表
func (t *translateHandler) CloudComponentPodInstance(ctx context.Context, param models.CloudComponentWorkloadPodInstanceReadParam) ([]*models.CloudComponentWorkloadPodGrouper, error) {
	result, err := t.handler.CloudComponentPodInstance(ctx, param)
	database_aop.DoTranslate(ctx, result, err)
	return result, err
}
