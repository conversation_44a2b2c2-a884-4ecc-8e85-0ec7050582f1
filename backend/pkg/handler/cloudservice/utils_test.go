package cloudservice

import (
	"strconv"
	"testing"

	cloudservice_v1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/v1alpha1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
)

func Test_cloudComponentArray_filterManaged(t *testing.T) {
	tests := []struct {
		input        cloudComponentArray
		isEmpty      bool
		filterPolicy models.CloudServiceComponentPolicy
	}{
		{
			input:        nil,
			isEmpty:      true,
			filterPolicy: models.CloudServiceComponentPolicyManaged,
		},
		{
			input: cloudComponentArray{
				cloudservice_v1alpha1.CloudComponent{
					Spec: cloudservice_v1alpha1.CloudComponentSpec{
						ClusterPolicy: cloudservice_v1alpha1.ClusterManagedPolicy,
					},
				},
				cloudservice_v1alpha1.CloudComponent{
					Spec: cloudservice_v1alpha1.CloudComponentSpec{
						ClusterPolicy: cloudservice_v1alpha1.ClusterManagedPolicy,
					},
				},
			},
			isEmpty:      false,
			filterPolicy: models.CloudServiceComponentPolicyManaged,
		},
		{
			input: cloudComponentArray{
				cloudservice_v1alpha1.CloudComponent{
					Spec: cloudservice_v1alpha1.CloudComponentSpec{
						ClusterPolicy: cloudservice_v1alpha1.ClusterManagedPolicy,
					},
				},
				cloudservice_v1alpha1.CloudComponent{
					Spec: cloudservice_v1alpha1.CloudComponentSpec{
						ClusterPolicy: cloudservice_v1alpha1.ClusterWorkPolicy,
					},
				},
			},
			isEmpty:      false,
			filterPolicy: models.CloudServiceComponentPolicyManaged,
		},
		{
			input: cloudComponentArray{
				cloudservice_v1alpha1.CloudComponent{
					Spec: cloudservice_v1alpha1.CloudComponentSpec{
						ClusterPolicy: cloudservice_v1alpha1.ClusterWorkPolicy,
					},
				},
				cloudservice_v1alpha1.CloudComponent{
					Spec: cloudservice_v1alpha1.CloudComponentSpec{
						ClusterPolicy: cloudservice_v1alpha1.ClusterWorkPolicy,
					},
				},
			},
			isEmpty:      true,
			filterPolicy: models.CloudServiceComponentPolicyManaged,
		},
	}

	for index, test := range tests {
		t.Run("Test_cloudComponentArray_filterManaged-"+strconv.Itoa(index), func(t *testing.T) {
			test.input.reservePolicy(test.filterPolicy)
			if test.isEmpty && len(test.input) != 0 {
				t.Errorf("want result list is empty,current len is %d", len(test.input))
				return
			}
			if !allIn(test.input, test.filterPolicy) {
				t.Errorf("want result list type all of '%v',result is %+v", test.filterPolicy, test.input)
				return
			}
		})

	}
}

func allIn(arr cloudComponentArray, policy models.CloudServiceComponentPolicy) bool {
	if len(arr) == 0 {
		return true
	}
	for _, item := range arr {
		if models.CloudServiceComponentPolicy(item.Spec.ClusterPolicy) != policy {
			return false
		}
	}
	return true
}
