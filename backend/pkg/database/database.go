package database

import (
	"fmt"
	"os"
	"strings"

	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
)

var AmpDB *gorm.DB
var CaasDB *gorm.DB

func Init() error {
	var databaseDriver driver
	var err error
	databaseDriver, err = getDriver(config.DatabaseDriver.Value)
	if err != nil {
		return err
	}

	AmpDB, err = getGormDb(getAMPDsnFromConfig, databaseDriver)
	if err != nil {
		return err
	}
	CaasDB, err = getGormDb(getCaasDsnFromConfig, databaseDriver)
	if err != nil {
		return err
	}
	return nil

}

func getAMPDsnFromConfig() string {
	var username, password, host, port, scheme, args string
	username = config.AMPDatabaseUsername.Value
	password = config.AMPDatabasePassword.Value
	host = config.AMPDatabaseHost.Value
	port = config.AMPDatabasePort.Value
	scheme = config.AMPDatabaseScheme.Value
	args = config.AMPDatabaseArgs.Value
	dsn := parseToDsn(username, password, host, port, scheme, args)
	return dsn
}
func getCaasDsnFromConfig() string {
	var username, password, host, port, scheme, args string
	username = config.CaasDatabaseUsername.Value
	password = config.CaasDatabasePassword.Value
	host = config.CaasDatabaseHost.Value
	port = config.CaasDatabasePort.Value
	scheme = config.CaasDatabaseScheme.Value
	args = config.CaasDatabaseArgs.Value
	dsn := parseToDsn(username, password, host, port, scheme, args)
	return dsn
}

func getGormDb(getDsnFunc func() string, databaseDriver driver) (*gorm.DB, error) {
	dsn := getDsnFunc()
	db, err := gorm.Open(databaseDriver.openFunc()(dsn), &gorm.Config{
		QueryFields: true,
	})
	if err != nil {
		return nil, err
	}
	if os.Getenv("GORM_DEBUG") == "true" {
		db.Logger = db.Logger.LogMode(logger.Info)
	}
	return db, nil
}

func parseToDsn(username, password, host, port, scheme, args string) string {
	dns := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s", username, password, host, port, scheme)
	if strings.EqualFold(args, "") {
		return dns
	}
	return fmt.Sprintf("%s?%s", dns, args)
}
