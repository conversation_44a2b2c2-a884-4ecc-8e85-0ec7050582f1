package database

import (
	"fmt"
	"strings"
	"testing"
)

func Test_parseToDsn(t *testing.T) {
	params := []struct {
		username string
		password string
		host     string
		port     string
		scheme   string
		args     string
		want     string
	}{
		{
			username: "root",
			password: "Hc@Cloud01",
			host:     "mysql-svc",
			port:     "3306",
			scheme:   "amp",
			args:     "",
			want:     "root:Hc@Cloud01@tcp(mysql-svc:3306)/amp",
		},
		{
			username: "root",
			password: "Hc@Cloud01",
			host:     "mysql-svc",
			port:     "3306",
			scheme:   "amp",
			args:     "charset=utf8mb4&loc=local&parseTime=true",
			want:     "root:Hc@Cloud01@tcp(mysql-svc:3306)/amp?charset=utf8mb4&loc=local&parseTime=true",
		},
	}
	for index, param := range params {
		name := fmt.Sprintf("Test_parseToDsn_%d", index)
		t.Run(name, func(t *testing.T) {
			dns := parseToDsn(param.username, param.password, param.host, param.port, param.scheme, param.args)
			if !strings.EqualFold(dns, param.want) {
				t.Errorf("want is \n %s \n,result is \n %s \n", param.want, dns)
			}
		})
	}
}
