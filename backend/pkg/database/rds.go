package database

import (
	"context"
	"strconv"

	rds "github.com/redis/go-redis/v9"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
)

var RDS *rds.Client

func InitRDS() error {
	db, err := strconv.Atoi(config.RedisSelectDB.Value)
	if err != nil {
		return err
	}
	rdsCli := rds.NewClient(&rds.Options{
		Addr:     config.RedisAddress.Value,
		Password: config.RedisPassword.Value,
		DB:       db,
	})
	if err := rdsCli.Ping(context.TODO()).Err(); err != nil {
		return err
	}
	RDS = rdsCli
	return nil
}
