package utils

import (
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/velero"
)

func SolveCronString(cron *velero.Cron) {
	if cron.CycleType == "custom" {
		return
	}

	cronSlice := []string{"*", "*", "*", "*", "*"}

	solve := func(sli []string, min, max int, cronSlice *string) bool {
		flag := true
		for _, s := range sli {
			flag = flag && velero.IsRange(s, min, max)
		}
		if flag {
			*cronSlice = JoinString(sli, constants.Comma, constants.Start)
		}
		return flag
	}

	switch cron.CycleType {
	case constants.CronTypeMonth, constants.CronTypeWeek:
		if cron.CycleType == constants.CronTypeMonth {
			if !solve(cron.Days, 1, 31, &cronSlice[2]) {
				cron.CycleType = constants.CronTypeCustom
				break
			}
		} else if cron.CycleType == constants.CronTypeWeek {
			if !solve(cron.Weeks, 0, 6, &cronSlice[4]) {
				cron.CycleType = constants.CronTypeCustom
				break
			}
		}
		fallthrough
	case constants.CronTypeDate:
		if !solve(cron.Hours, 0, 23, &cronSlice[1]) {
			cron.CycleType = constants.CronTypeCustom
			break
		}
		fallthrough
	case constants.CronTypeHour:
		if !solve(cron.Minutes, 0, 59, &cronSlice[0]) {
			cron.CycleType = constants.CronTypeCustom
			break
		}
	}

	cron.CronString = JoinString(cronSlice, constants.Blank, "")
}
