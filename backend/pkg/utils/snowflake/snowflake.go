package snowflake

import (
	"time"

	"github.com/sony/sonyflake"
)

// SnowflakeIntf
// 雪花算法 ID 生成工具接口，在测试用例中需要使用 mock 实现方案
type SnowflakeIntf interface {
	// GenerateID 生成雪花算法 ID
	GenerateID() int64
}

func NewSnowflakeIntf() SnowflakeIntf {
	startTime := time.Date(2020, 1, 1, 0, 0, 0, 0, time.UTC)
	return &snowflakeHelper{
		flake: sonyflake.NewSonyflake(sonyflake.Settings{
			StartTime: startTime,
		}),
	}
}

// snowflakeHelper
// 雪花算法 ID 生成工具的具体实现
type snowflakeHelper struct {
	flake *sonyflake.Sonyflake
}

// GenerateID
// 生成唯一的雪花算法 ID
func (s *snowflakeHelper) GenerateID() int64 {
	id, _ := s.flake.NextID()
	//前端最高只能接收16位的number，超过会精度丢失
	return int64(id % 1e15)
}
