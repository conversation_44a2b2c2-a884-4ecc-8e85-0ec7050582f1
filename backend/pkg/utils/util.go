package utils

import (
	"encoding/json"
	"strings"

	"gopkg.in/yaml.v3"
)

// HasEmpty
// 判断传入可变长string 是否包含空字符串
func HasEmpty(strList ...string) bool {
	if len(strList) == 0 {
		return false
	}

	for _, str := range strList {
		if strings.EqualFold(str, "") {
			return true
		}
	}

	return false
}

func BeanCopy(src, target interface{}) error {
	jsonBytes, err := json.Marshal(src)
	if err != nil {
		return err
	}
	return json.Unmarshal(jsonBytes, target)
}

// IsValidJson 判断是否是合法的json
func IsValidJson(str string) bool {
	var js map[string]any
	return json.Unmarshal([]byte(str), &js) == nil
}

// IsValidYaml 判断是否是合法的yaml
func IsValidYaml(str string) bool {
	var js map[string]any
	return yaml.Unmarshal([]byte(str), &js) == nil
}
