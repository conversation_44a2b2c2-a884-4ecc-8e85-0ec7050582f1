package mr

import (
	"context"
	"errors"
	"reflect"
	"testing"
)

// 示例的 GenerateFunc，生成整数切片
func sampleGenerate(source chan<- int) {
	for i := 1; i <= 5; i++ {
		source <- i
	}
}

// 示例的 MapperFunc，将整数映射为其平方
func sampleMapper(item int, writer Writer[int], cancel func(error)) {
	writer.Write(item * item)
}

// 示例的 ReducerFunc，计算整数切片的总和
func sampleReducer(pipe <-chan int, writer Writer[int], cancel func(error)) {
	sum := 0
	for num := range pipe {
		sum += num
	}
	writer.Write(sum)
}

// 示例的 GenerateFunc，返回错误
func errorGenerate(source chan<- int) {
	close(source)
}

// 示例的 MapperFunc，处理负数时返回错误
func errorMapper(item int, writer Writer[int], cancel func(error)) {
	if item < 0 {
		cancel(errors.New("negative number"))
		return
	}
	writer.Write(item * item)
}

// 示例的 ReducerFunc，处理空管道时返回错误
func errorReducer(pipe <-chan int, writer Writer[int], cancel func(error)) {
	sum := 0
	for num := range pipe {
		sum += num
	}
	if sum == 0 {
		cancel(errors.New("sum is zero"))
		return
	}
	writer.Write(sum)
}

func TestMapReduce(t *testing.T) {
	type args[T any, U any, V any] struct {
		generate GenerateFunc[T]
		mapper   MapperFunc[T, U]
		reducer  ReducerFunc[U, V]
		opts     []Option
	}
	type testCase[T any, U any, V any] struct {
		name    string
		args    args[T, U, V]
		want    V
		wantErr bool
	}
	tests := []testCase[int, int, int]{
		{
			name: "正常情况",
			args: args[int, int, int]{
				generate: sampleGenerate,
				mapper:   sampleMapper,
				reducer:  sampleReducer,
				opts:     nil,
			},
			want:    55, // 1^2 + 2^2 + 3^2 + 4^2 + 5^2 = 55
			wantErr: false,
		},
		{
			name: "带有上下文取消的情况",
			args: args[int, int, int]{
				generate: sampleGenerate,
				mapper:   sampleMapper,
				reducer:  sampleReducer,
				opts:     []Option{WithContext(context.Background())},
			},
			want:    55,
			wantErr: false,
		},
		{
			name: "带有超时上下文的情况",
			args: args[int, int, int]{
				generate: sampleGenerate,
				mapper:   sampleMapper,
				reducer:  sampleReducer,
				opts:     []Option{WithContext(context.Background()), WithWorkers(2)},
			},
			want:    55,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := MapReduce(tt.args.generate, tt.args.mapper, tt.args.reducer, tt.args.opts...)
			if (err != nil) != tt.wantErr {
				t.Errorf("MapReduce() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("MapReduce() got = %v, want %v", got, tt.want)
			}
		})
	}
}
