package sisyphus_solutionparam

type commonOption struct {
	JoinMaster            bool `json:"joinMaster"`
	JoinNode              bool `json:"joinNode"`
	PreCheck              bool `json:"precheck"`
	AutoEditHostname      bool `json:"autoEditHostname"`
	CustomSandboxRegistry bool `json:"customSandboxRegistry"`
}

type extraOption struct {
	DefaultDeployChrony        bool  `json:"defaultDeployChrony"`
	AddDefaultRegistry         bool  `json:"addDefaultRegistry"`
	NeedAuthForDefaultRegistry *bool `json:"needAuthForDefaultRegistry,omitempty"`
}

type removeNodeOptionsOption struct {
	Common removeNodeOptionsOptionCommon `json:"common"`
}

type removeNodeOptionsOptionCommon struct {
	ContainerRuntime string `json:"containerRuntime"`
	NeedReset        bool   `json:"needReset"`
}

type addNodeOptions struct {
	Common     addNodeOptionsCommon     `json:"common"`
	Containerd addNodeOptionsContainerd `json:"containerd"`
	Docker     addNodeOptionsDocker     `json:"docker"`
	Extra      addNodeOptionsExtra      `json:"extra"`
}
type addNodeOptionsCommon struct {
	AutoEditHostname bool   `json:"autoEditHostname"`
	ContainerRuntime string `json:"containerRuntime"`
	JoinMaster       bool   `json:"joinMaster"`
	JoinNode         bool   `json:"joinNode"`
	Precheck         bool   `json:"precheck"`
}
type addNodeOptionsContainerd struct {
	AddDefaultRegistry         bool `json:"addDefaultRegistry"`
	CustomSandboxRegistry      bool `json:"customSandboxRegistry"`
	NeedAuthForDefaultRegistry bool `json:"needAuthForDefaultRegistry"`
}
type addNodeOptionsDocker struct {
	AddDefaultRegistry bool `json:"addDefaultRegistry"`
}
type addNodeOptionsExtra struct {
	DefaultDeployChrony bool `json:"defaultDeployChrony"`
}
