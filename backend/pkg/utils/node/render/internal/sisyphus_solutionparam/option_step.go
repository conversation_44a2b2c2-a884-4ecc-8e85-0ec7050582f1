package sisyphus_solutionparam

import (
	"fmt"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
)

const (
	optionNameAction            = "action"
	optionNameCommon            = "common"
	optionNameExtra             = "extra"
	optionNameNeedReset         = "needReset"
	optionNameRemoveNodeOptions = "removeNodeOptions"
	optionNameAddNodeOptions    = "addNodeOptions"
)

type actionOptionStep struct {
	upAction   string
	downAction string
}

func newActionOptionStep() optionStep {
	return actionOptionStep{
		upAction:   "节点上线",
		downAction: "节点下线",
	}
}
func (actionOptionStep) name() string {
	return optionNameAction
}

func (actionOptionStep) should(createRequest node.NodeUpDownCreateRequest) (bool, error) {
	// 节点上线｜节点下线都会有
	return true, nil
}
func (step actionOptionStep) option(createRequest node.NodeUpDownCreateRequest) (interface{}, error) {
	switch createRequest.Type {
	case node.NodeUpDownTypeNodeUp:
		return step.upAction, nil
	case node.NodeUpDownTypeNodeDown:
		return step.downAction, nil
	default:
		return nil, fmt.Errorf("un support node up down type of %s", createRequest.Type)
	}
}

func (step actionOptionStep) valueRenderToResponse(value any, response *node.NodeUpDownResponse) error {
	t, err := step.getType(value)
	if err != nil {
		return err
	}
	response.Type = t
	return nil
}
func (step actionOptionStep) valueRenderToCreateResponse(value any, response *node.NodeUpDownCreateResponse) error {
	t, err := step.getType(value)
	if err != nil {
		return err
	}
	response.Type = t
	return nil
}

func (step actionOptionStep) getType(value any) (node.NodeUpDownType, error) {
	switch value.(type) {
	case string:
		val := value.(string)
		switch val {
		case step.upAction:
			return node.NodeUpDownTypeNodeUp, nil
		case step.downAction:
			return node.NodeUpDownTypeNodeDown, nil
		default:
			return "", fmt.Errorf("unknow action value of %v", value)
		}
	default:
		return "", fmt.Errorf("unknow action value of %v", value)
	}
}

type commonOptionStep struct {
}

func newCommonOptionStep() optionStep {
	return commonOptionStep{}
}
func (commonOptionStep) name() string {
	return optionNameCommon
}

// 暂时用CRI判断 未来需要修改
func (commonOptionStep) should(createRequest node.NodeUpDownCreateRequest) (bool, error) {
	// 只有节点上线才会有 并且cri类型为Containerd
	return createRequest.Type == node.NodeUpDownTypeNodeUp && createRequest.CRI == cluster.CRITypeContainerd, nil
}
func (commonOptionStep) option(createRequest node.NodeUpDownCreateRequest) (interface{}, error) {
	return commonOption{
		JoinMaster:            false,
		JoinNode:              true,
		PreCheck:              true,
		AutoEditHostname:      true,
		CustomSandboxRegistry: false,
	}, nil
}

func (commonOptionStep) valueRenderToResponse(value any, response *node.NodeUpDownResponse) error {
	return nil
}
func (step commonOptionStep) valueRenderToCreateResponse(value any, response *node.NodeUpDownCreateResponse) error {
	return nil
}

type extraOptionStep struct {
}

func newExtraOptionStep() optionStep {
	return extraOptionStep{}
}
func (extraOptionStep) name() string {
	return optionNameExtra
}

// 暂时用CRI判断 未来需要修改
func (extraOptionStep) should(createRequest node.NodeUpDownCreateRequest) (bool, error) {
	// 只有节点上线才会有 并且cri类型为Containerd
	return createRequest.Type == node.NodeUpDownTypeNodeUp && createRequest.CRI == cluster.CRITypeContainerd, nil
}
func (extraOptionStep) option(createRequest node.NodeUpDownCreateRequest) (interface{}, error) {
	var needAuthForDefaultRegistry *bool
	if createRequest.Registry != nil {
		boolFalse := false
		needAuthForDefaultRegistry = &boolFalse
	}
	return extraOption{
		DefaultDeployChrony:        true,
		AddDefaultRegistry:         createRequest.Registry != nil,
		NeedAuthForDefaultRegistry: needAuthForDefaultRegistry,
	}, nil
}
func (extraOptionStep) valueRenderToResponse(value any, response *node.NodeUpDownResponse) error {
	return nil
}
func (step extraOptionStep) valueRenderToCreateResponse(value any, response *node.NodeUpDownCreateResponse) error {
	return nil
}

type needResetStep struct {
}

func newNeedResetOptionStep() optionStep {
	return needResetStep{}
}
func (needResetStep) name() string {
	return optionNameNeedReset
}

// 这里暂时用cri 来判断 未来接的时候要该
func (needResetStep) should(createRequest node.NodeUpDownCreateRequest) (bool, error) {
	// 只有节点下线才会有 并且CRI 为 Containerd
	return createRequest.Type == node.NodeUpDownTypeNodeDown && createRequest.CRI == cluster.CRITypeContainerd, nil
}
func (needResetStep) option(createRequest node.NodeUpDownCreateRequest) (interface{}, error) {
	return true, nil
}

func (needResetStep) valueRenderToResponse(value any, response *node.NodeUpDownResponse) error {
	return nil
}

func (step needResetStep) valueRenderToCreateResponse(value any, response *node.NodeUpDownCreateResponse) error {
	return nil
}

type removeNodeOptionsStep struct {
}

func newRemoveNodeOptionsStep() optionStep {
	return removeNodeOptionsStep{}
}
func (removeNodeOptionsStep) name() string {
	return optionNameRemoveNodeOptions
}

// 这里暂时用cri 来判断 未来接的时候要该
func (removeNodeOptionsStep) should(createRequest node.NodeUpDownCreateRequest) (bool, error) {
	// 只有节点下线才会有 并且CRI 为 Docker
	return createRequest.Type == node.NodeUpDownTypeNodeDown && createRequest.CRI == cluster.CRITypeDocker, nil
}

func (removeNodeOptionsStep) option(createRequest node.NodeUpDownCreateRequest) (interface{}, error) {
	// 暂时只接docker
	return removeNodeOptionsOption{
		Common: removeNodeOptionsOptionCommon{
			ContainerRuntime: string(createRequest.CRI),
			NeedReset:        true,
		},
	}, nil
}

func (removeNodeOptionsStep) valueRenderToResponse(value any, response *node.NodeUpDownResponse) error {
	return nil
}

func (step removeNodeOptionsStep) valueRenderToCreateResponse(value any, response *node.NodeUpDownCreateResponse) error {
	return nil
}

type addNodeOptionsStep struct {
}

func newAddNodeOptionsStep() optionStep {
	return addNodeOptionsStep{}
}
func (addNodeOptionsStep) name() string {
	return optionNameAddNodeOptions
}

// 这里暂时用cri 来判断 未来接的时候要该
func (addNodeOptionsStep) should(createRequest node.NodeUpDownCreateRequest) (bool, error) {
	// 只有节点上线才会有 并且CRI 为 Docker
	return createRequest.Type == node.NodeUpDownTypeNodeUp && createRequest.CRI == cluster.CRITypeDocker, nil
}

func (addNodeOptionsStep) option(createRequest node.NodeUpDownCreateRequest) (interface{}, error) {

	return addNodeOptions{
		Common: addNodeOptionsCommon{
			AutoEditHostname: true,
			ContainerRuntime: string(createRequest.CRI),
			JoinMaster:       false,
			JoinNode:         true,
			Precheck:         true,
		},
		Containerd: addNodeOptionsContainerd{
			AddDefaultRegistry:         createRequest.Registry != nil,
			CustomSandboxRegistry:      false,
			NeedAuthForDefaultRegistry: false,
		},
		Docker: addNodeOptionsDocker{
			AddDefaultRegistry: createRequest.Registry != nil,
		}, Extra: addNodeOptionsExtra{
			DefaultDeployChrony: true,
		},
	}, nil
}

func (addNodeOptionsStep) valueRenderToResponse(value any, response *node.NodeUpDownResponse) error {
	return nil
}

func (step addNodeOptionsStep) valueRenderToCreateResponse(value any, response *node.NodeUpDownCreateResponse) error {
	return nil
}
