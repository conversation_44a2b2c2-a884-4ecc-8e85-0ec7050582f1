package sisyphus_solutionparam

import (
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
)

// ParamMgr
// 处理西西弗斯的 solution param 序列化与反序列化管理器
type ParamMgr interface {
	CreateRequestAsParam(createRequest node.NodeUpDownCreateRequest, solutionName string) (string, error)
	ParamRenderToResponse(response *node.NodeUpDownResponse, param string) error
	ParamRenderToCreateResponse(response *node.NodeUpDownCreateResponse, param string) error
	ParamRenderToCreateStatusResponse(response *node.NodeUpDownStatusResponse, param string) error
}

// jobParamStep
// 生成西西弗斯 solution param 每个步骤的内容
type jobParamStep interface {
	// 获取步骤名称
	name() string
	// 判断步骤是否需要转化 如没有macvlan 就不需要转化macvlan 的请求参数
	shouldConvert(createRequest node.NodeUpDownCreateRequest) (bool, error)
	// 转化请求参数
	convert(createRequest node.NodeUpDownCreateRequest) (map[string]interface{}, error)
	// 将property 的内容渲染到 create response
	renderCreateResponse(property map[string]interface{}, createResponse *node.NodeUpDownCreateResponse) error
	// 将property 的内容渲染到 create response
	renderResponse(property map[string]interface{}, response *node.NodeUpDownResponse) error
	// 将property 的内容渲染到 create status response
	renderCreateStatusResponse(property map[string]interface{}, createResponse *clustermodel.CreateStatusResponse) error
}

// optionStep
// 生成西西弗斯 solution option 每个步骤的内容
type optionStep interface {
	name() string
	should(createRequest node.NodeUpDownCreateRequest) (bool, error)
	option(createRequest node.NodeUpDownCreateRequest) (interface{}, error)
	valueRenderToResponse(value any, response *node.NodeUpDownResponse) error
	valueRenderToCreateResponse(value any, response *node.NodeUpDownCreateResponse) error
}

type jobParamStepProperty interface {
	asProperty() (map[string]interface{}, error)
}
