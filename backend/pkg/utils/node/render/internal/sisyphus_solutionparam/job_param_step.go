package sisyphus_solutionparam

import (
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

const (
	jobParamNameNodeUpCommon = "A001-节点上线通用参数"
	jobParamNameNodeUpCRI    = "A002-容器编排及运行时参数"
)

type nodeUpCommonJobParamStep struct {
}

func newNodeUpCommonJobParamStep() jobParamStep {
	return nodeUpCommonJobParamStep{}
}

// 获取步骤名称
func (nodeUpCommonJobParamStep) name() string {
	return jobParamNameNodeUpCommon
}

// 判断步骤是否需要转化 如没有macvlan 就不需要转化macvlan 的请求参数
func (step nodeUpCommonJobParamStep) shouldConvert(createRequest node.NodeUpDownCreateRequest) (bool, error) {
	return createRequest.Type == node.NodeUpDownTypeNodeUp, nil
}

// 转化请求参数
func (step nodeUpCommonJobParamStep) convert(createRequest node.NodeUpDownCreateRequest) (map[string]interface{}, error) {
	return newNodeUpCommonModel(createRequest.ClusterName, createRequest.Registry, *createRequest.TimeServer).asProperty()
}

// 将property 的内容渲染到 create response
func (step nodeUpCommonJobParamStep) renderCreateResponse(property map[string]interface{}, createResponse *node.NodeUpDownCreateResponse) error {
	var model nodeUpCommonModel
	if err := utils.BeanCopy(property, &model); err != nil {
		return err
	}
	createResponse.RegistryType = append(createResponse.RegistryType, clustermodel.RegistryTypeDefault)
	if model.Common.Registry != nil {
		createResponse.RegistryType = append(createResponse.RegistryType, clustermodel.RegistryTypeCustom)
		createResponse.Registry = new(clustermodel.CreateRegistryConfigResponse)
		createResponse.Registry.Protocol = model.Common.Registry.Scheme
		createResponse.Registry.Address = model.Common.Registry.Addr
		createResponse.Registry.Port = model.Common.Registry.Port
	}
	createResponse.TimeServer = &model.Common.TimeServer
	return nil
}

// 将property 的内容渲染到 create status response
func (step nodeUpCommonJobParamStep) renderCreateStatusResponse(property map[string]interface{}, createResponse *clustermodel.CreateStatusResponse) error {
	// nothing to do
	return nil
}

// 将property 的内容渲染到 create response
func (step nodeUpCommonJobParamStep) renderResponse(property map[string]interface{}, response *node.NodeUpDownResponse) error {
	var model nodeUpCommonModel
	if err := utils.BeanCopy(property, &model); err != nil {
		return err
	}
	response.ClusterName = model.Common.ClusterName
	return nil
}

type nodeUpCRIJobParamStep struct {
}

func newNodeUpCRIJobParamStep() jobParamStep {
	return nodeUpCRIJobParamStep{}
}

// 获取步骤名称
func (nodeUpCRIJobParamStep) name() string {
	return jobParamNameNodeUpCRI
}

// 判断步骤是否需要转化 如没有macvlan 就不需要转化macvlan 的请求参数
func (step nodeUpCRIJobParamStep) shouldConvert(createRequest node.NodeUpDownCreateRequest) (bool, error) {
	return createRequest.Type == node.NodeUpDownTypeNodeUp && createRequest.CRI == clustermodel.CRITypeDocker, nil
}

// 转化请求参数
func (step nodeUpCRIJobParamStep) convert(createRequest node.NodeUpDownCreateRequest) (map[string]interface{}, error) {
	return newCriParamModel().asProperty()
}

// 将property 的内容渲染到 create response
func (step nodeUpCRIJobParamStep) renderCreateResponse(property map[string]interface{}, createResponse *node.NodeUpDownCreateResponse) error {
	return nil
}

// 将property 的内容渲染到 create status response
func (step nodeUpCRIJobParamStep) renderCreateStatusResponse(property map[string]interface{}, createResponse *clustermodel.CreateStatusResponse) error {
	// nothing to do
	return nil
}

// 将property 的内容渲染到 create response
func (step nodeUpCRIJobParamStep) renderResponse(property map[string]interface{}, response *node.NodeUpDownResponse) error {
	return nil
}
