package sisyphus_solutionparam

import (
	"context"
	"encoding/json"
	"fmt"

	"harmonycloud.cn/unifiedportal/cloudservice-operator/pkg/handler/installer/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/addon"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/cluster/config"
	nodeconfig "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/node/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/sisyphustype"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/sisyphusutil"
	"k8s.io/apimachinery/pkg/util/sets"
)

type paramMgr struct {
	// 读取下层集群的西西弗斯地址
	sisyphusConfigHandler addon.SisyphusConfigHandler
	sisyphusclient        client.Sisyphus
	jobParams             []jobParamStep
	options               []optionStep
}

func NewParamMgr() ParamMgr {
	return paramMgr{
		sisyphusConfigHandler: addon.NewSisyphusConfigHandler(),
		sisyphusclient:        client.NewSisyphus(),
		jobParams: []jobParamStep{
			newNodeUpCommonJobParamStep(),
			newNodeUpCRIJobParamStep(),
		},
		options: []optionStep{
			newActionOptionStep(),
			newCommonOptionStep(),
			newExtraOptionStep(),
			newNeedResetOptionStep(),
			newRemoveNodeOptionsStep(),
			newAddNodeOptionsStep(),
		},
	}
}
func (mgr paramMgr) CreateRequestAsParam(createRequest node.NodeUpDownCreateRequest, solutionName string) (string, error) {
	options, err := mgr.parseSisyphusOption(createRequest)
	if err != nil {
		return "", err
	}
	jobParam, err := mgr.parseSisyphusJobParams(createRequest)
	if err != nil {
		return "", err
	}
	hosts, err := parseSisyphusHostGroups(createRequest)
	if err != nil {
		return "", err
	}
	sisyphusConfig, err := mgr.sisyphusConfigHandler.GetSisyphusURL(context.Background(), createRequest.ClusterName)
	if err != nil {
		return "", err
	}
	editContext, err := sisyphusutil.GetEditContext(mgr.sisyphusclient, sisyphusConfig)
	if err != nil {
		return "", err
	}
	// 根据基线版本获取solution info
	solutionInfo, err := nodeconfig.NodeUPDownConfig.SolutionInfos.GetSolutionInfoByBaselineVersion(createRequest.ClusterBaselineVersion)
	if err != nil {
		return "", err
	}
	// 处理NodeStorage
	disks, err := sisyphusutil.GetNodeDisks(convert2NodeDiskMap(createRequest))
	if err != nil {
		return "", err
	}

	sisyphusParam := sisyphustype.EditDeployRequest{
		Name: solutionName,
		Solution: sisyphustype.SolutionMetadata{
			Name:    solutionInfo.SolutionName,
			Version: solutionInfo.SolutionVersion,
		},
		Contexts: editContext,
		Hosts:    hosts,
		Options:  options,
		Params:   jobParam,
		Disks:    disks,
	}
	jsonBytes, err := json.Marshal(sisyphusParam)
	return string(jsonBytes), err
}

func convert2NodeDiskMap(createRequest node.NodeUpDownCreateRequest) map[string]clustermodel.NodeStorageRequest {
	var res = make(map[string]clustermodel.NodeStorageRequest)
	if createRequest.NodeConfig.NodeStorageRequest != nil {
		res[createRequest.NodeConfig.Ip] = *createRequest.NodeConfig.NodeStorageRequest
	}
	return res
}

func (mgr paramMgr) ParamRenderToResponse(response *node.NodeUpDownResponse, param string) error {
	var sisyphusParam sisyphustype.EditDeployRequest
	if err := json.Unmarshal([]byte(param), &sisyphusParam); err != nil {
		return err
	}
	// 处理option
	if err := mgr.optionRenderToResponse(response, sisyphusParam.Options); err != nil {
		return err
	}
	// 处理节点分组
	if err := mgr.hostsRenderToResponse(response, sisyphusParam.Hosts); err != nil {
		return err
	}
	// 处理磁盘自动挂载
	if len(sisyphusParam.Disks) != 0 {
		response.HasAutoStorageNode = true
	}
	// 处理JobParam
	for _, jobParam := range sisyphusParam.Params {
		name := jobParam.Name
		property := jobParam.Properties
		// 获取处理的interface
		var jobParamStep jobParamStep
		for _, step := range mgr.jobParams {
			if step.name() == name {
				jobParamStep = step
				break
			}
		}
		if jobParamStep == nil {
			continue
		}
		if err := jobParamStep.renderResponse(property, response); err != nil {
			return err
		}
	}
	return nil
}

func (mgr paramMgr) ParamRenderToCreateResponse(response *node.NodeUpDownCreateResponse, param string) error {
	var sisyphusParam sisyphustype.EditDeployRequest
	if err := json.Unmarshal([]byte(param), &sisyphusParam); err != nil {
		return err
	}
	// 处理option
	if err := mgr.optionRenderToCreateResponse(response, sisyphusParam.Options); err != nil {
		return err
	}
	// 处理节点分组
	if err := mgr.hostsRenderToCreateResponse(response, sisyphusParam.Hosts); err != nil {
		return err
	}

	// 处理JobParam
	for _, jobParam := range sisyphusParam.Params {
		name := jobParam.Name
		property := jobParam.Properties
		// 获取处理的interface
		var jobParamStep jobParamStep
		for _, step := range mgr.jobParams {
			if step.name() == name {
				jobParamStep = step
				break
			}
		}
		if jobParamStep == nil {
			continue
		}
		if err := jobParamStep.renderCreateResponse(property, response); err != nil {
			return err
		}
	}
	// 处理disk
	if response.Type == node.NodeUpDownTypeNodeUp {
		response.NodeConfig.RenderNodeDiskConfigs(sisyphusutil.ReverseNodeDisks(sisyphusParam.Disks))
	}
	return nil

}

func (mgr paramMgr) ParamRenderToCreateStatusResponse(response *node.NodeUpDownStatusResponse, param string) error {
	var sisyphusParam sisyphustype.EditDeployRequest
	if err := json.Unmarshal([]byte(param), &sisyphusParam); err != nil {
		return err
	}

	// 处理磁盘自动挂载
	if len(sisyphusParam.Disks) != 0 {
		response.HasAutoStorageNode = true
	}

	return nil

}

func getK8sVersionAndCriVersion(sisyphusParam sisyphustype.EditDeployRequest) (k8sVersion, criVersion string) {
	solutionName := sisyphusParam.Solution.Name
	solutionVersion := sisyphusParam.Solution.Version
	solution, exist := config.CreateClusterConfig.SolutionInfos.FindBySolutionNameAndSolutionVersion(solutionName, solutionVersion)
	if exist {
		k8sVersion = solution.KubernetesVersion
		criVersion = solution.CRIVersion
	} else {
		k8sVersion = constants.UnKnowKubernetesVersion
		criVersion = constants.UnKnowCRIVersion
	}
	return
}

func parseSisyphusHostGroups(request node.NodeUpDownCreateRequest) ([]sisyphustype.HostGroups, error) {
	controllerNode := sisyphustype.HostGroups{
		IP:     request.ControlNode.Ip,
		Groups: []string{nodeconfig.NodeUPDownConfig.Group.ControlNode},
	}
	workerNode := sisyphustype.HostGroups{
		IP: request.NodeConfig.Ip,
	}

	// 添加被控制节点分组
	switch request.Type {
	case node.NodeUpDownTypeNodeUp:
		workerNode.Groups = append(workerNode.Groups, nodeconfig.NodeUPDownConfig.Group.NodeUpWorker)
		if request.Type == node.NodeUpDownTypeNodeUp && request.NodeConfig.SupportGpu != nil && *request.NodeConfig.SupportGpu {
			workerNode.Groups = append(workerNode.Groups, nodeconfig.NodeUPDownConfig.Group.NodeUpGpu)
		}
	case node.NodeUpDownTypeNodeDown:
		workerNode.Groups = append(workerNode.Groups, nodeconfig.NodeUPDownConfig.Group.NodeDown)
	default:
		return nil, fmt.Errorf("un support node up down type of %s", request.Type)
	}
	return []sisyphustype.HostGroups{controllerNode, workerNode}, nil
}

func (mgr paramMgr) optionRenderToResponse(response *node.NodeUpDownResponse, options map[string]interface{}) error {
	if options == nil {
		return fmt.Errorf("options is empty")
	}
	for _, option := range mgr.options {
		nameOfValue := options[option.name()]
		if err := option.valueRenderToResponse(nameOfValue, response); err != nil {
			return err
		}
	}
	return nil
}

func (mgr paramMgr) optionRenderToCreateResponse(createResponse *node.NodeUpDownCreateResponse, options map[string]interface{}) error {
	if options == nil {
		return fmt.Errorf("options is empty")
	}
	for _, option := range mgr.options {
		nameOfValue := options[option.name()]
		if err := option.valueRenderToCreateResponse(nameOfValue, createResponse); err != nil {
			return err
		}
	}
	return nil
}

func (mgr paramMgr) hostsRenderToResponse(response *node.NodeUpDownResponse, hosts []sisyphustype.HostGroups) error {
	for _, host := range hosts {
		hostGroupSet := sets.New[string](host.Groups...)
		if hostGroupSet.HasAny(nodeconfig.NodeUPDownConfig.Group.NodeDown, nodeconfig.NodeUPDownConfig.Group.NodeUpWorker) {
			response.Ip = host.IP
			break
		}
	}
	return nil
}

func (mgr paramMgr) hostsRenderToCreateResponse(createResponse *node.NodeUpDownCreateResponse, hosts []sisyphustype.HostGroups) error {
	for _, host := range hosts {
		hostGroupSet := sets.New[string](host.Groups...)
		if hostGroupSet.HasAny(nodeconfig.NodeUPDownConfig.Group.NodeDown, nodeconfig.NodeUPDownConfig.Group.NodeUpWorker) {
			// 工作节点
			createResponse.CacheNodeToWorker(host.IP)
		}
		if hostGroupSet.HasAny(nodeconfig.NodeUPDownConfig.Group.ControlNode) {
			// 主控节点
			createResponse.CacheNodeToControl(host.IP)
		}
		if createResponse.Type == node.NodeUpDownTypeNodeUp {
			bol := false
			if hostGroupSet.HasAny(nodeconfig.NodeUPDownConfig.Group.NodeUpGpu) {
				// GPU节点
				bol = true
			}
			createResponse.NodeConfig.SupportGpu = &bol
		}

	}
	return nil
}

func (mgr paramMgr) parseSisyphusOption(request node.NodeUpDownCreateRequest) (map[string]interface{}, error) {
	var optionMap = make(map[string]interface{}, len(mgr.options)<<1)
	for _, option := range mgr.options {
		option := option
		should, err := option.should(request)
		if err != nil {
			return nil, err
		}
		if should {
			optionIntf, err := option.option(request)
			if err != nil {
				return nil, err
			}
			optionMap[option.name()] = optionIntf
		}
	}
	return optionMap, nil
}

func (mgr paramMgr) parseSisyphusJobParams(request node.NodeUpDownCreateRequest) ([]sisyphustype.JobParam, error) {
	var jobParams = make([]sisyphustype.JobParam, 0)
	for _, jp := range mgr.jobParams {
		shouldConvert, err := jp.shouldConvert(request)
		if err != nil {
			return nil, err
		}
		if !shouldConvert {
			continue
		}
		name := jp.name()
		cvt, err := jp.convert(request)
		if err != nil {
			return nil, err
		}
		jobParams = append(jobParams, sisyphustype.JobParam{
			Name:       name,
			Properties: cvt,
		})
	}
	return jobParams, nil
}
