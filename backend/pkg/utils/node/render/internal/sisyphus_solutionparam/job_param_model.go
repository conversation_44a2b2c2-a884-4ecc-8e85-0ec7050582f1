package sisyphus_solutionparam

import (
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

func newNodeUpCommonModel(clusterName string, registry *cluster.CreateRegistryConfigRequest, timeServer string) jobParamStepProperty {
	var registryModel *nodeUpCommonRegistry
	if registry != nil {
		registryModel = &nodeUpCommonRegistry{
			Scheme:               registry.Protocol,
			Addr:                 registry.Address,
			Port:                 registry.Port,
			PauseImageRepository: "k8s-deploy",
		}
	}
	return nodeUpCommonModel{
		Common: nodeUpCommon{
			ClusterName: clusterName,
			Registry:    registryModel,
			TimeServer:  timeServer,
		},
	}
}

type nodeUpCommonModel struct {
	Common nodeUpCommon `json:"common"`
}

func (obj nodeUpCommonModel) asProperty() (map[string]interface{}, error) {
	var result = make(map[string]interface{})
	var err = utils.BeanCopy(obj, &result)
	return result, err
}

type nodeUpCommon struct {
	ClusterName string                `json:"clusterName"`
	Registry    *nodeUpCommonRegistry `json:"registry,omitempty"`
	TimeServer  string                `json:"timeServer"`
}

type nodeUpCommonRegistry struct {
	Scheme               string `json:"scheme,omitempty"`
	Addr                 string `json:"addr,omitempty"`
	Port                 int    `json:"port,omitempty"`
	PauseImageRepository string `json:"pauseImageRepository,omitempty"`
}

func newCriParamModel() jobParamStepProperty {
	return criParamModel{
		Docker: criParamModelDocker{
			Bip:          "**********/26",
			CgroupDriver: "systemd",
			Log: criParamModelDockerLog{
				MaxFile: "10",
				MaxSize: "100m",
			},
		},
	}
}

type criParamModel struct {
	Docker criParamModelDocker `json:"docker"`
}

type criParamModelDocker struct {
	Bip          string                 `json:"bip"`
	CgroupDriver string                 `json:"cgroupdriver"`
	Log          criParamModelDockerLog `json:"log"`
}

type criParamModelDockerLog struct {
	MaxFile string `json:"max_file"`
	MaxSize string `json:"max_size"`
}

func (obj criParamModel) asProperty() (map[string]interface{}, error) {
	var result = make(map[string]interface{})
	var err = utils.BeanCopy(obj, &result)
	return result, err
}
