package internal

import (
	"sort"
	"strings"

	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/installerutil"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/iputil"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/node/render/internal/task"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/uuid"
)

// metaNameRRTIL
// 处理metadata.name 的 RRTIL
type metaNameRRTIL struct {
	uuid uuid.UUIDIntf
}

func NewMetaNameRRTIL(uuid uuid.UUIDIntf) RequestRender {
	return metaNameRRTIL{
		uuid: uuid,
	}
}

// Installer
// 将create request 的内容渲染到 installer
func (rrtil metaNameRRTIL) Installer(installer *installerv1alpha1.Installer, request node.NodeUpDownCreateRequest) error {
	if installer.Name == "" {
		// 节点上下线类型 + 集群 + UUID
		installer.Name = strings.ToLower(string(request.Type)) + "-" + request.ClusterName + "-" + rrtil.uuid.UUID()
	}
	return nil
}

// BatchInstaller
// 将batch create request 的内容渲染到 installer 这里只会渲染元信息而不会渲染task信息
func (rrtil metaNameRRTIL) BatchInstaller(installer *installerv1alpha1.Installer, request node.NodeUpDownBatchCreateRequest) error {
	if installer.Name == "" {
		// 节点上下线类型 + 集群 + UUID
		installer.Name = strings.ToLower(string(node.NodeUpDownTypeBatch)) + "-" + request.ClusterName + "-" + rrtil.uuid.UUID()
	}
	return nil
}

// Response
// 将installer 的内容渲染到 response
func (rrtil metaNameRRTIL) Response(response *node.NodeUpDownResponse, installer installerv1alpha1.Installer) error {
	// nothing to do
	return nil
}

// CreateStatusResponse
// 将installer 的内容渲染到 create status response
func (rrtil metaNameRRTIL) CreateStatusResponse(statusResponse *node.NodeUpDownStatusResponse, installer installerv1alpha1.Installer) error {
	// nothing to do
	return nil
}

// CreateResponse
// 将installer 的内容渲染到 create response
func (rrtil metaNameRRTIL) CreateResponse(createResponse *node.NodeUpDownCreateResponse, installer installerv1alpha1.Installer) error {
	// nothing to do
	return nil
}

// metaLabelsRRTIL
// 处理metadata.labels 的 RRTIL
type metaLabelsRRTIL struct {
}

func NewMetaLabelsRRTIL() RequestRender {
	return metaLabelsRRTIL{}
}

// Installer
// 将create request 的内容渲染到 installer
func (rrtil metaLabelsRRTIL) Installer(installer *installerv1alpha1.Installer, request node.NodeUpDownCreateRequest) error {
	if installer.Labels == nil {
		installer.Labels = make(map[string]string)
	}
	// 标记类型 节点上下线
	installer.Labels[constants.InstallTypeLabelKey] = constants.NodeUpDownInstallerTypeLabelValue
	// 标记集群名称
	installer.Labels[constants.NodeUpDownClusterNameLabelKey] = request.ClusterName
	// 标记类型详情 属于节点上线还是节点下线
	installer.Labels[constants.NodeUpDownTypeLabelKey] = string(request.Type)
	// 记录节点上下线的IP
	installer.Labels[constants.NodeUpDownIPLabelKey] = iputil.FormatLabelValueForIP(request.NodeConfig.Ip)
	return nil
}

// BatchInstaller
// 将batch create request 的内容渲染到 installer 这里只会渲染元信息而不会渲染task信息
func (rrtil metaLabelsRRTIL) BatchInstaller(installer *installerv1alpha1.Installer, request node.NodeUpDownBatchCreateRequest) error {
	if installer.Labels == nil {
		installer.Labels = make(map[string]string)
	}
	// 标记类型 节点上下线
	installer.Labels[constants.InstallTypeLabelKey] = constants.NodeUpDownInstallerTypeLabelValue
	// 标记集群名称
	installer.Labels[constants.NodeUpDownClusterNameLabelKey] = request.ClusterName
	// 标记类型详情 这里记录批量类型
	installer.Labels[constants.NodeUpDownTypeLabelKey] = string(node.NodeUpDownTypeBatch)
	return nil
}

// Response
// 将installer 的内容渲染到 response
func (rrtil metaLabelsRRTIL) Response(response *node.NodeUpDownResponse, installer installerv1alpha1.Installer) error {
	// nothing to do
	return nil
}

func (rrtil metaLabelsRRTIL) CreateStatusResponse(statusResponse *node.NodeUpDownStatusResponse, installer installerv1alpha1.Installer) error {
	return nil
}

// CreateResponse
// 将installer 的内容渲染到 create response
func (rrtil metaLabelsRRTIL) CreateResponse(createResponse *node.NodeUpDownCreateResponse, installer installerv1alpha1.Installer) error {
	// nothing to do
	return nil
}

// metaAnnotationsRRTIL
// 处理metadata.annotations 的 RRTIL
type metaAnnotationsRRTIL struct {
}

func NewMetaAnnotationsRRTIL() RequestRender {
	return metaAnnotationsRRTIL{}
}

// Installer
// 将create request 的内容渲染到 installer
func (rrtil metaAnnotationsRRTIL) Installer(installer *installerv1alpha1.Installer, request node.NodeUpDownCreateRequest) error {
	if installer.Annotations == nil {
		installer.Annotations = make(map[string]string)
	}
	// 执行完成后删除自己
	installer.Annotations["on-success-remove-self"] = "true"
	installer.Annotations[constants.InstallerCRITypeAnnotationKey] = string(request.CRI)
	return nil
}

// BatchInstaller
// 将batch create request 的内容渲染到 installer 这里只会渲染元信息而不会渲染task信息
func (rrtil metaAnnotationsRRTIL) BatchInstaller(installer *installerv1alpha1.Installer, request node.NodeUpDownBatchCreateRequest) error {
	if installer.Annotations == nil {
		installer.Annotations = make(map[string]string)
	}
	// 执行完成后删除自己
	installer.Annotations["on-success-remove-self"] = "true"
	return nil
}

// Response
// 将installer 的内容渲染到 response
func (rrtil metaAnnotationsRRTIL) Response(response *node.NodeUpDownResponse, installer installerv1alpha1.Installer) error {
	if installer.Annotations == nil {
		installer.Annotations = make(map[string]string)
	}
	criKey, exist := installer.Annotations[constants.InstallerCRITypeAnnotationKey]
	if !exist || criKey == "" {
		criKey = string(clustermodel.CRITypeContainerd)
	}
	response.CRI = clustermodel.CRIType(criKey)

	// nothing to do
	return nil
}

func (rrtil metaAnnotationsRRTIL) CreateStatusResponse(statusResponse *node.NodeUpDownStatusResponse, installer installerv1alpha1.Installer) error {
	if installer.Annotations == nil {
		installer.Annotations = make(map[string]string)
	}
	criKey, exist := installer.Annotations[constants.InstallerCRITypeAnnotationKey]
	if !exist || criKey == "" {
		criKey = string(clustermodel.CRITypeContainerd)
	}
	statusResponse.CRI = clustermodel.CRIType(criKey)
	return nil
}

// CreateResponse
// 将installer 的内容渲染到 create response
func (rrtil metaAnnotationsRRTIL) CreateResponse(createResponse *node.NodeUpDownCreateResponse, installer installerv1alpha1.Installer) error {
	if installer.Annotations == nil {
		installer.Annotations = make(map[string]string)
	}
	criKey, exist := installer.Annotations[constants.InstallerCRITypeAnnotationKey]
	if !exist || criKey == "" {
		criKey = string(clustermodel.CRITypeContainerd)
	}
	createResponse.CRI = clustermodel.CRIType(criKey)
	return nil
}

// metaTaskRRTIL
// 处理metadata.task 的 RRTIL
type metaTaskRRTIL struct {
	// 处理task 的顺序
	taskIntfList []task.Intf
}

func NewMetaTaskRRTIL(uuid uuid.UUIDIntf) RequestRender {
	return metaTaskRRTIL{
		taskIntfList: []task.Intf{
			task.NewNodeInitialTask(),                        // 第一步进行信息提交
			task.NewSisyphusNodeResetSolutionApplyTask(uuid), // (Option) 提交节点重置西西弗斯表单
			task.NewSisyphusSolutionApplyTask(uuid),          // 提交节点上下线西西弗斯表单
			task.NewSisyphusNodeResetSolutionExecTask(),      // (Option) 执行节点重置行为
			task.NewSisyphusSolutionExecTask(),               // 执行节点上下线行为
		},
	}
}

// Installer
// 将create request 的内容渲染到 installer
func (rrtil metaTaskRRTIL) Installer(installer *installerv1alpha1.Installer, request node.NodeUpDownCreateRequest) error {
	if installer.Spec.Tasks == nil {
		installer.Spec.Tasks = make(installerv1alpha1.InstallerTaskSpecList, 0, 16)
	}
	for _, taskIntf := range rrtil.taskIntfList {
		if err := rrtil.dealForCreateRequestRenderToInstallerTask(taskIntf, installer, request); err != nil {
			return err
		}
	}

	sortIndex := []installerutil.Intf{}
	for _, intf := range rrtil.taskIntfList {
		intf := intf
		sortIndex = append(sortIndex, intf)
	}
	sort.Sort(installerutil.NewTaskSortUtil(sortIndex, installer.Spec.Tasks))

	return nil
}

// BatchInstaller
// 将batch create request 的内容渲染到 installer 这里只会渲染元信息而不会渲染task信息
func (rrtil metaTaskRRTIL) BatchInstaller(installer *installerv1alpha1.Installer, request node.NodeUpDownBatchCreateRequest) error {
	// task 及template 内容在上层进行处理
	return nil
}

// Response
// 将installer 的内容渲染到 response
func (rrtil metaTaskRRTIL) Response(response *node.NodeUpDownResponse, installer installerv1alpha1.Installer) error {
	// 状态获取逻辑
	for _, taskIntf := range rrtil.taskIntfList {
		if err := rrtil.dealForInstallerTaskRenderToResponse(taskIntf, response, installer); err != nil {
			return err
		}
	}
	return nil
}

func (rrtil metaTaskRRTIL) CreateStatusResponse(statusResponse *node.NodeUpDownStatusResponse, installer installerv1alpha1.Installer) error {
	// 写入步骤与状态
	for _, taskIntf := range rrtil.taskIntfList {
		if err := rrtil.dealForInstallerTaskRenderToCreateStateResponse(taskIntf, statusResponse, installer); err != nil {
			return err
		}
	}
	return nil
}

// CreateResponse
// 将installer 的内容渲染到 create response
func (rrtil metaTaskRRTIL) CreateResponse(createResponse *node.NodeUpDownCreateResponse, installer installerv1alpha1.Installer) error {
	// 写入表单信息
	for _, taskIntf := range rrtil.taskIntfList {
		if err := rrtil.dealForInstallerTaskRenderToCreateResponse(taskIntf, createResponse, installer); err != nil {
			return err
		}
	}
	return nil
}

func (rrtil metaTaskRRTIL) dealForCreateRequestRenderToInstallerTask(taskIntf task.Intf, installer *installerv1alpha1.Installer, request node.NodeUpDownCreateRequest) error {

	// 先查找到spec
	// 若specTask 存在   targetIndex != -1
	// 若specTask 不存在 targetIndex  = -1
	targetIndex := installerutil.GetSpecTaskIndexByKindAndName(installer.Spec.Tasks, taskIntf.Kind(), taskIntf.Name())

	// 查看是否需要提供该task
	if taskIntf.Should(installer, request) {
		// 若specTask不存在 则初始化一个
		var targetTask installerv1alpha1.InstallerTaskSpec
		if targetIndex == -1 {
			installer.Spec.Tasks = append(installer.Spec.Tasks, targetTask)
			targetIndex = len(installer.Spec.Tasks) - 1
			targetTask.TaskName = taskIntf.Name()
			targetTask.Description = taskIntf.Description()
			kind := taskIntf.Kind()
			targetTask.Kind = &kind
		} else {
			targetTask = installer.Spec.Tasks[targetIndex]
		}

		// 从 installer 中查找task status
		var targetStatus = installerutil.GetStatusTaskByKindAndName(installer.Status.Tasks, *(targetTask.Kind), targetTask.TaskName)
		if err := taskIntf.CreateRequestRenderToInstallerTask(installer.Spec.Tasks, &targetTask, targetStatus, request); err != nil {
			return err
		}
		installer.Spec.Tasks[targetIndex] = targetTask
	} else {
		// 若已存在 则移除
		if targetIndex != -1 {
			installer.Spec.Tasks = append(installer.Spec.Tasks[0:targetIndex], installer.Spec.Tasks[targetIndex+1:]...)[0 : len(installer.Spec.Tasks)-1]
		}
	}
	return nil
}

func (rrtil metaTaskRRTIL) dealForInstallerTaskRenderToResponse(taskIntf task.Intf, response *node.NodeUpDownResponse, installer installerv1alpha1.Installer) error {
	// 从installer 中查找task spec
	taskSpec := installerutil.GetSpecTaskByKindAndName(installer.Spec.Tasks, taskIntf.Kind(), taskIntf.Name())
	// 没有找到该类型的task不做处理
	if taskSpec == nil {
		return nil
	}
	// 从 installer 中查找task status
	var targetStatus = installerutil.GetStatusTaskByKindAndName(installer.Status.Tasks, *(taskSpec.Kind), taskSpec.TaskName)

	return taskIntf.InstallerTaskRenderToResponse(response, *taskSpec, targetStatus)
}

func (rrtil metaTaskRRTIL) dealForInstallerTaskRenderToCreateStateResponse(taskIntf task.Intf, statusResponse *node.NodeUpDownStatusResponse, installer installerv1alpha1.Installer) error {
	// 从installer 中查找task spec 和 task status
	taskSpec := installerutil.GetSpecTaskByKindAndName(installer.Spec.Tasks, taskIntf.Kind(), taskIntf.Name())
	// 没有找到该类型的task不做处理
	if taskSpec == nil {
		return nil
	}
	var taskStatus = installerutil.GetStatusTaskByKindAndName(installer.Status.Tasks, *(taskSpec.Kind), taskSpec.TaskName)
	return taskIntf.InstallerTaskRenderToCreateStateResponse(statusResponse, *taskSpec, taskStatus)
}

func (rrtil metaTaskRRTIL) dealForInstallerTaskRenderToCreateResponse(taskIntf task.Intf, createResponse *node.NodeUpDownCreateResponse, installer installerv1alpha1.Installer) error {
	// 从installer 中查找task spec
	// 从installer 中查找task spec
	taskSpec := installerutil.GetSpecTaskByKindAndName(installer.Spec.Tasks, taskIntf.Kind(), taskIntf.Name())
	// 没有找到该类型的task不做处理
	if taskSpec == nil {
		return nil
	}

	return taskIntf.InstallerTaskRenderToCreateResponse(createResponse, *taskSpec)
}
