package task

import (
	"context"
	"fmt"
	"strconv"

	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/addon"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/installerutil"
	nodeconfig "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/node/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/node/render/internal/sisyphus_solutionparam"
	noderesettask "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/nodereset/render/task"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/uuid"
	"k8s.io/apimachinery/pkg/util/sets"
)

// nodeInitialTask
// 处理节点初始化的task
type nodeInitialTask struct {
	// 读取下层集群的西西弗斯地址
	sisyphusConfigHandler addon.SisyphusConfigHandler
}

func NewNodeInitialTask() Intf {
	return nodeInitialTask{
		sisyphusConfigHandler: addon.NewSisyphusConfigHandler(),
	}
}

// Name
// 获取task 名称
func (nodeInitialTask) Name() string {
	return constants.TaskNameForInitialNode
}

// Description
// 获取task 描述
func (nodeInitialTask) Description() string {
	return constants.TaskDescriptionForInitialNode
}

// Kind
// 获取当前task 处理器的类型
func (nodeInitialTask) Kind() installerv1alpha1.InstallerTaskKind {
	return installerv1alpha1.InstallerTaskKindSisyphusNodeInitial
}
func (nodeInitialTask) Should(installer *installerv1alpha1.Installer, request node.NodeUpDownCreateRequest) bool {
	// 节点初始化永远需要做
	return true
}

// CreateRequestRenderToInstallerTask
// 将 request 内容渲染到task
func (handler nodeInitialTask) CreateRequestRenderToInstallerTask(existTasks []installerv1alpha1.InstallerTaskSpec, task *installerv1alpha1.InstallerTaskSpec, status *installerv1alpha1.InstallerTaskStatus, request node.NodeUpDownCreateRequest) error {
	var nodeRequests = []node.NodeRequest{
		// 处理主控节点
		{
			Ip:   request.ControlNode.Ip,
			Port: request.ControlNode.Port,
			Auth: request.ControlNode.Auth,
		},
		// 处理待上线 ｜ 下线节点
		{
			Ip:   request.NodeConfig.Ip,
			Port: request.NodeConfig.Port,
			Auth: request.NodeConfig.Auth,
		},
	}
	sisyphusConfig, err := handler.sisyphusConfigHandler.GetSisyphusURL(context.Background(), request.ClusterName)
	if err != nil {
		return err
	}
	return installerutil.RenderTaskForNodeInitial(task, sisyphusConfig, nodeRequests)
}

// InstallerTaskRenderToResponse
// 将 installer task 的内容渲染到response
func (nodeInitialTask) InstallerTaskRenderToResponse(response *node.NodeUpDownResponse, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	// 总步骤设置
	response.Total = response.Total + 1

	// 大步骤 成功 ｜ 失败 ｜ 运行中 ===》 step + 1
	if taskStatus != nil && shouldStepAdd(taskStatus.Phase) {
		response.Step = response.Step + 1
	}

	// 成功 ｜ 失败 ｜ 运行中
	if response.Status == "" {
		if taskStatus != nil {
			switch taskStatus.Phase {
			case installerv1alpha1.StatusPhasePending:
				fallthrough
			case installerv1alpha1.StatusPhaseRunning:
				response.Status = node.NodeUpDownStatusCreateInitial
			case installerv1alpha1.StatusPhaseSuccess:
				response.Status = "" // 如果该步骤success 则设置状态为 "" 表示该步骤已完成
			case installerv1alpha1.StatusPhaseFailed:
				response.Status = node.NodeUpDownStatusCreateInitialFailed
			default:
				response.Status = node.NodeUpDownStatusUnknow
			}
		} else {
			response.Status = node.NodeUpDownStatusCreateInitial
		}
	}

	// and nothing to do
	return nil
}

// InstallerTaskRenderToCreateStateResponse
// 将 installer task 的内容渲染到create status response
func (nodeInitialTask) InstallerTaskRenderToCreateStateResponse(statusResponse *node.NodeUpDownStatusResponse, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	// 查找信息提交的processing
	group, exist := statusResponse.Processing.FindByCode(nodeconfig.NodeUPDownConfig.Initial.GroupCode)
	if !exist {
		group = &clustermodel.CreateProcessResponse{
			Code:   nodeconfig.NodeUPDownConfig.Initial.GroupCode,
			Name:   nodeconfig.NodeUPDownConfig.Initial.GroupAlias,
			Status: clustermodel.ProcessStatusWaiting,
		}
		statusResponse.Processing = append(statusResponse.Processing, *group)
		group, _ = statusResponse.Processing.FindByCode(nodeconfig.NodeUPDownConfig.Initial.GroupCode)
	}

	step := clustermodel.CreateProcessStepResponse{
		Code:   nodeconfig.NodeUPDownConfig.Initial.NodeInitialCode,
		Name:   nodeconfig.NodeUPDownConfig.Initial.NodeInitialAlias,
		Status: clustermodel.ProcessStatusWaiting,
	}
	if taskStatus != nil {
		step.Status = clustermodel.MustParseByInstallerStatus(taskStatus.Phase)
		if taskStatus.Phase == installerv1alpha1.StatusPhaseFailed {
			step.ErrorType = &taskStatus.Reason
			step.ErrorMessage = &taskStatus.Message
		}
	}
	group.Steps = append(group.Steps, step)
	group.Status = installerutil.GetStepsStatus(group.Steps)
	return nil
}

// InstallerTaskRenderToCreateResponse
// 将 installer task 的内容渲染到create response
func (nodeInitialTask) InstallerTaskRenderToCreateResponse(createResponse *node.NodeUpDownCreateResponse, taskSpec installerv1alpha1.InstallerTaskSpec) error {
	if taskSpec.SisyphusNodeInitial == nil || taskSpec.SisyphusNodeInitial.Param == nil {
		return nil
	}
	for _, initialNode := range taskSpec.SisyphusNodeInitial.Param {
		port, _ := strconv.Atoi(initialNode.Port)
		authParamStruct := clustermodel.NodeAuthUsernameAndPasswordParamRequest{
			Username:              initialNode.Username,
			Password:              initialNode.Password,
			AuthorizationPassword: initialNode.SudoPassword,
		}
		var authParam map[string]interface{}
		_ = utils.BeanCopy(authParamStruct, &authParam)
		authResponse := clustermodel.NodeAuthResponse{
			AuthType: clustermodel.UserNameAndPasswordAuthType,
			Param:    authParam,
		}

		node := node.NodeResponse{
			Ip:   initialNode.IP,
			Port: port,
			Auth: &authResponse,
		}
		createResponse.CacheNodeInfo(node)
	}
	return nil
}

type sisyphusNodeResetSolutionApplyTask struct {
	nodeResetIntf noderesettask.Intf
	// 读取下层集群的西西弗斯地址
	sisyphusConfigHandler addon.SisyphusConfigHandler
}

func NewSisyphusNodeResetSolutionApplyTask(uuid uuid.UUIDIntf) Intf {
	return sisyphusNodeResetSolutionApplyTask{
		nodeResetIntf:         noderesettask.NewSisyphusNodeResetSolutionApplyTask(uuid),
		sisyphusConfigHandler: addon.NewSisyphusConfigHandler(),
	}
}

func (task sisyphusNodeResetSolutionApplyTask) Name() string {
	return task.nodeResetIntf.Name()
}

func (task sisyphusNodeResetSolutionApplyTask) Description() string {
	return task.nodeResetIntf.Description()
}

func (task sisyphusNodeResetSolutionApplyTask) Kind() installerv1alpha1.InstallerTaskKind {
	return task.nodeResetIntf.Kind()
}

func (task sisyphusNodeResetSolutionApplyTask) Should(installer *installerv1alpha1.Installer, request node.NodeUpDownCreateRequest) bool {
	// 如果不是节点上线类型的任务，则直接返回false
	if request.Type != node.NodeUpDownTypeNodeUp {
		return false
	}
	// 节点重置 - 表单提交 需要进行判断
	return task.nodeResetIntf.Should(installer, request.StartFromFailed, request.Reset)
}

func (h sisyphusNodeResetSolutionApplyTask) CreateRequestRenderToInstallerTask(existTasks []installerv1alpha1.InstallerTaskSpec, task *installerv1alpha1.InstallerTaskSpec, status *installerv1alpha1.InstallerTaskStatus, request node.NodeUpDownCreateRequest) error {
	sisyphusConfig, err := h.sisyphusConfigHandler.GetSisyphusURL(context.Background(), request.ClusterName)
	if err != nil {
		return err
	}
	return h.nodeResetIntf.CreateRequestRenderToInstallerTask(existTasks, task, status, node.NewNodeResetRequest([]node.NodeRequest{
		{
			Ip:   request.NodeConfig.Ip,
			Port: request.NodeConfig.Port,
			Auth: request.NodeConfig.Auth,
		},
	}, request.StartFromFailed, request.Reset, request.ClusterBaselineVersion, request.CRI), sisyphusConfig)

}

func (sisyphusNodeResetSolutionApplyTask) InstallerTaskRenderToResponse(response *node.NodeUpDownResponse, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	// 总步骤设置
	response.Total = response.Total + 1

	// 大步骤 成功 ｜ 失败 ｜ 运行中 ===》 step + 1
	if taskStatus != nil && shouldStepAdd(taskStatus.Phase) {
		response.Step = response.Step + 1
	}

	// 设置初始状态为节点初始化中
	if response.Status == "" {
		if taskStatus != nil {
			switch taskStatus.Phase {
			case installerv1alpha1.StatusPhasePending:
				fallthrough
			case installerv1alpha1.StatusPhaseRunning:
				response.Status = node.NodeUpDownStatusCreateInitial
			case installerv1alpha1.StatusPhaseSuccess:
				response.Status = "" // 如果该步骤success 则设置状态为 "" 表示该步骤已完成
			case installerv1alpha1.StatusPhaseFailed:
				response.Status = node.NodeUpDownStatusCreateInitialFailed
			default:
				response.Status = node.NodeUpDownStatusUnknow
			}
		} else {
			response.Status = node.NodeUpDownStatusCreateInitial
		}
	}

	return nil
}

func (h sisyphusNodeResetSolutionApplyTask) InstallerTaskRenderToCreateStateResponse(statusResponse *node.NodeUpDownStatusResponse, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	return h.nodeResetIntf.InstallerTaskRenderToCreateStateResponse(&statusResponse.Processing, nodeconfig.NodeUPDownConfig.Initial.GroupCode, nodeconfig.NodeUPDownConfig.Initial.GroupAlias, statusResponse.CRI, taskSpec, taskStatus)
}

func (sisyphusNodeResetSolutionApplyTask) InstallerTaskRenderToCreateResponse(createResponse *node.NodeUpDownCreateResponse, taskSpec installerv1alpha1.InstallerTaskSpec) error {
	return nil
}

type sisyphusNodeResetSolutionExecTask struct {
	nodeResetIntf noderesettask.Intf
	// 读取下层集群的西西弗斯地址
	sisyphusConfigHandler addon.SisyphusConfigHandler
}

func NewSisyphusNodeResetSolutionExecTask() Intf {
	return sisyphusNodeResetSolutionExecTask{
		nodeResetIntf:         noderesettask.NewSisyphusNodeResetSolutionExecTask(),
		sisyphusConfigHandler: addon.NewSisyphusConfigHandler(),
	}
}

func (task sisyphusNodeResetSolutionExecTask) Name() string {
	return task.nodeResetIntf.Name()
}

func (task sisyphusNodeResetSolutionExecTask) Description() string {
	return task.nodeResetIntf.Description()
}

func (task sisyphusNodeResetSolutionExecTask) Kind() installerv1alpha1.InstallerTaskKind {
	return task.nodeResetIntf.Kind()
}

func (task sisyphusNodeResetSolutionExecTask) Should(installer *installerv1alpha1.Installer, request node.NodeUpDownCreateRequest) bool {
	// 如果不是节点上线类型的任务，则直接返回false
	if request.Type != node.NodeUpDownTypeNodeUp {
		return false
	}
	return task.nodeResetIntf.Should(installer, request.StartFromFailed, request.Reset)

}

func (h sisyphusNodeResetSolutionExecTask) CreateRequestRenderToInstallerTask(existTasks []installerv1alpha1.InstallerTaskSpec, task *installerv1alpha1.InstallerTaskSpec, status *installerv1alpha1.InstallerTaskStatus, request node.NodeUpDownCreateRequest) error {
	sisyphusConfig, err := h.sisyphusConfigHandler.GetSisyphusURL(context.Background(), request.ClusterName)
	if err != nil {
		return err
	}

	return h.nodeResetIntf.CreateRequestRenderToInstallerTask(existTasks, task, status, node.NewNodeResetRequest([]node.NodeRequest{
		{
			Ip:   request.NodeConfig.Ip,
			Port: request.NodeConfig.Port,
			Auth: request.NodeConfig.Auth,
		},
	}, request.StartFromFailed, request.Reset, request.ClusterBaselineVersion, request.CRI), sisyphusConfig)

}

func (sisyphusNodeResetSolutionExecTask) InstallerTaskRenderToResponse(response *node.NodeUpDownResponse, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	// 设置初始状态为节点初始化中
	if response.Status == "" {
		if taskStatus != nil {
			switch taskStatus.Phase {
			case installerv1alpha1.StatusPhasePending:
				fallthrough
			case installerv1alpha1.StatusPhaseRunning:
				response.Status = node.NodeUpDownStatusCreateInitial
			case installerv1alpha1.StatusPhaseSuccess:
				response.Status = "" // 如果该步骤success 则设置状态为 "" 表示该步骤已完成
			case installerv1alpha1.StatusPhaseFailed:
				response.Status = node.NodeUpDownStatusCreateInitialFailed
			default:
				response.Status = node.NodeUpDownStatusUnknow
			}
		} else {
			response.Status = node.NodeUpDownStatusCreateInitial
		}
	}
	// 处理total & step
	// 获取重置的期望执行步骤有序列表
	sortSteps := noderesettask.ListNodeResetExpectStepCodes(response.CRI)
	handleExecTotalAndStep(response, sortSteps, taskStatus)
	return nil
}

func (h sisyphusNodeResetSolutionExecTask) InstallerTaskRenderToCreateStateResponse(statusResponse *node.NodeUpDownStatusResponse, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	return h.nodeResetIntf.InstallerTaskRenderToCreateStateResponse(&statusResponse.Processing, "", "", statusResponse.CRI, taskSpec, taskStatus)
}

func (sisyphusNodeResetSolutionExecTask) InstallerTaskRenderToCreateResponse(createResponse *node.NodeUpDownCreateResponse, taskSpec installerv1alpha1.InstallerTaskSpec) error {
	return nil
}

// sisyphusSolutionApplyTask
// 处理西西弗斯的 任务创建的task
type sisyphusSolutionApplyTask struct {
	uuid     uuid.UUIDIntf
	paramMgr sisyphus_solutionparam.ParamMgr
	// 读取下层集群的西西弗斯地址
	sisyphusConfigHandler addon.SisyphusConfigHandler
}

func NewSisyphusSolutionApplyTask(uuid uuid.UUIDIntf) Intf {
	return sisyphusSolutionApplyTask{
		uuid:                  uuid,
		paramMgr:              sisyphus_solutionparam.NewParamMgr(),
		sisyphusConfigHandler: addon.NewSisyphusConfigHandler(),
	}
}

// Name
// 获取task 名称
func (sisyphusSolutionApplyTask) Name() string {
	return constants.TaskNameForSisyphusSolutionApply
}

// Description
// 获取task 描述
func (sisyphusSolutionApplyTask) Description() string {
	return constants.TaskDescriptionForSisyphusSolutionApply
}

// Kind
// 获取当前task 处理器的类型
func (sisyphusSolutionApplyTask) Kind() installerv1alpha1.InstallerTaskKind {
	return installerv1alpha1.InstallerTaskKindSisyphusSolutionApply
}

func (sisyphusSolutionApplyTask) Should(installer *installerv1alpha1.Installer, request node.NodeUpDownCreateRequest) bool {
	// 节点上下｜下线必须执行
	return true
}

// CreateRequestRenderToInstallerTask
// 将 request 内容渲染到task
func (applyTask sisyphusSolutionApplyTask) CreateRequestRenderToInstallerTask(existTasks []installerv1alpha1.InstallerTaskSpec, task *installerv1alpha1.InstallerTaskSpec, status *installerv1alpha1.InstallerTaskStatus, request node.NodeUpDownCreateRequest) error {
	sisyphusConfig, err := applyTask.sisyphusConfigHandler.GetSisyphusURL(context.TODO(), request.ClusterName)
	if err != nil {
		return err
	}

	return installerutil.RenderTaskForSolutionApply(applyTask.uuid, sisyphusConfig, task, func(solutionName string) (string, error) {
		return applyTask.paramMgr.CreateRequestAsParam(request, solutionName)
	})
}

// InstallerTaskRenderToResponse
// 将 installer task 的内容渲染到response
func (applyTask sisyphusSolutionApplyTask) InstallerTaskRenderToResponse(response *node.NodeUpDownResponse, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	// 总步骤设置
	response.Total = response.Total + 1

	// 大步骤 成功 ｜ 失败 ｜ 运行中 ===》 step + 1
	if taskStatus != nil && shouldStepAdd(taskStatus.Phase) {
		response.Step = response.Step + 1
	}

	// 设置初始状态为节点初始化中
	if response.Status == "" {
		if taskStatus != nil {
			switch taskStatus.Phase {
			case installerv1alpha1.StatusPhasePending:
				fallthrough
			case installerv1alpha1.StatusPhaseRunning:
				response.Status = node.NodeUpDownStatusCreateInitial
			case installerv1alpha1.StatusPhaseSuccess:
				response.Status = "" // 如果该步骤success 则设置状态为 "" 表示该步骤已完成
			case installerv1alpha1.StatusPhaseFailed:
				response.Status = node.NodeUpDownStatusCreateInitialFailed
			default:
				response.Status = node.NodeUpDownStatusUnknow
			}
		} else {
			response.Status = node.NodeUpDownStatusCreateInitial
		}
	}

	if taskSpec.SisyphusSolutionApply == nil || taskSpec.SisyphusSolutionApply.Param == "" {
		return fmt.Errorf("request sisyphus param is empty")
	}
	if err := applyTask.paramMgr.ParamRenderToResponse(response, taskSpec.SisyphusSolutionApply.Param); err != nil {
		return err
	}

	// nothing to do
	return nil
}

// InstallerTaskRenderToCreateStateResponse
// 将 installer task 的内容渲染到create status response
func (applyTask sisyphusSolutionApplyTask) InstallerTaskRenderToCreateStateResponse(statusResponse *node.NodeUpDownStatusResponse, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	// 查找信息提交的processing
	group, exist := statusResponse.Processing.FindByCode(nodeconfig.NodeUPDownConfig.Initial.GroupCode)
	if !exist {
		group = &clustermodel.CreateProcessResponse{
			Code:   nodeconfig.NodeUPDownConfig.Initial.GroupCode,
			Name:   nodeconfig.NodeUPDownConfig.Initial.GroupAlias,
			Status: clustermodel.ProcessStatusWaiting,
		}
		statusResponse.Processing = append(statusResponse.Processing, *group)
		group, _ = statusResponse.Processing.FindByCode(nodeconfig.NodeUPDownConfig.Initial.GroupCode)
	}
	var step = clustermodel.CreateProcessStepResponse{
		Status: clustermodel.ProcessStatusWaiting,
	}
	switch statusResponse.Type {
	case node.NodeUpDownTypeNodeUp:
		step.Code = nodeconfig.NodeUPDownConfig.Initial.NodeUpSisyphusSolutionApplyCode
		step.Name = nodeconfig.NodeUPDownConfig.Initial.NodeUpSisyphusSolutionApplyAlias
	case node.NodeUpDownTypeNodeDown:
		step.Code = nodeconfig.NodeUPDownConfig.Initial.NodeDownSisyphusSolutionApplyCode
		step.Name = nodeconfig.NodeUPDownConfig.Initial.NodeDownSisyphusSolutionApplyAlias
	default:
		return fmt.Errorf("unknow node updown type %s", statusResponse.Type)
	}

	if taskStatus != nil {
		step.Status = clustermodel.MustParseByInstallerStatus(taskStatus.Phase)
		if taskStatus.Phase == installerv1alpha1.StatusPhaseFailed {
			step.ErrorType = &taskStatus.Reason
			step.ErrorMessage = &taskStatus.Message
		}
	}
	group.Steps = append(group.Steps, step)
	group.Status = installerutil.GetStepsStatus(group.Steps)

	if taskSpec.SisyphusSolutionApply == nil || taskSpec.SisyphusSolutionApply.Param == "" {
		return fmt.Errorf("request sisyphus param is empty")
	}
	if err := applyTask.paramMgr.ParamRenderToCreateStatusResponse(statusResponse, taskSpec.SisyphusSolutionApply.Param); err != nil {
		return err
	}

	return nil
}

// InstallerTaskRenderToCreateResponse
// 将 installer task 的内容渲染到create response
func (applyTask sisyphusSolutionApplyTask) InstallerTaskRenderToCreateResponse(createResponse *node.NodeUpDownCreateResponse, taskSpec installerv1alpha1.InstallerTaskSpec) error {
	if taskSpec.SisyphusSolutionApply == nil || taskSpec.SisyphusSolutionApply.Param == "" {
		return nil
	}
	if err := applyTask.paramMgr.ParamRenderToCreateResponse(createResponse, taskSpec.SisyphusSolutionApply.Param); err != nil {
		return err
	}
	return nil
}

// sisyphusSolutionApplyTask
// 处理西西弗斯的 任务执行的task
type sisyphusSolutionExecTask struct {
	// 读取下层集群的西西弗斯地址
	sisyphusConfigHandler addon.SisyphusConfigHandler
}

func NewSisyphusSolutionExecTask() Intf {
	return sisyphusSolutionExecTask{
		sisyphusConfigHandler: addon.NewSisyphusConfigHandler(),
	}
}

// Name
// 获取task 名称
func (sisyphusSolutionExecTask) Name() string {
	return constants.TaskNameForSisyphusSolutionExec
}

// Description
// 获取task 描述
func (sisyphusSolutionExecTask) Description() string {
	return constants.TaskDescriptionForSisyphusSolutionExec
}

// Kind
// 获取当前task 处理器的类型
func (sisyphusSolutionExecTask) Kind() installerv1alpha1.InstallerTaskKind {
	return installerv1alpha1.InstallerTaskKindSisyphusSolutionExec
}

func (sisyphusSolutionExecTask) Should(installer *installerv1alpha1.Installer, request node.NodeUpDownCreateRequest) bool {
	// 节点上下｜下线必须执行
	return true
}

// CreateRequestRenderToInstallerTask
// 将 request 内容渲染到task
func (handler sisyphusSolutionExecTask) CreateRequestRenderToInstallerTask(existTasks []installerv1alpha1.InstallerTaskSpec, task *installerv1alpha1.InstallerTaskSpec, status *installerv1alpha1.InstallerTaskStatus, request node.NodeUpDownCreateRequest) error {
	// 获取exec 对应的solution 的名称
	solutionName, err := installerutil.GetSisyphusSolutionApplyTaskSolutionName(existTasks, constants.TaskNameForSisyphusSolutionApply)
	if err != nil {
		return err
	}

	// 获取节点上线的期望步骤
	var expectSteps = listNodeUPDownExpectStepCodes(request.Type, request.HasAutoStorageNode(), request.CRI)

	sisyphusConfig, err := handler.sisyphusConfigHandler.GetSisyphusURL(context.Background(), request.ClusterName)
	if err != nil {
		return err
	}

	return installerutil.RenderTaskForSolutionExec(task, solutionName, expectSteps, request.StartFromFailed, request.Reset, status, sisyphusConfig)
}

// InstallerTaskRenderToResponse
// 将 installer task 的内容渲染到response
func (sisyphusSolutionExecTask) InstallerTaskRenderToResponse(response *node.NodeUpDownResponse, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	// 如果前面步骤未设置状态 则设置状态
	if response.Status == "" {
		response.Status = getClusterStatusFromInstallerTaskStatus(response.Type, response.HasAutoStorageNode, taskStatus, response.CRI)
	}

	// 获取节点上下线的期望执行步骤有序列表
	sortSteps := listNodeUPDownExpectStepCodes(response.Type, response.HasAutoStorageNode, response.CRI)

	// 处理total & step
	handleExecTotalAndStep(response, sortSteps, taskStatus)

	return nil
}

// InstallerTaskRenderToCreateStateResponse
// 将 installer task 的内容渲染到create status response
func (sisyphusSolutionExecTask) InstallerTaskRenderToCreateStateResponse(statusResponse *node.NodeUpDownStatusResponse, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	// 定义一个集合 存储spec中的所有 step
	specParamsSet := sets.New[string]()
	if len(taskSpec.SisyphusSolutionExec.Param) != 0 {
		specParamsSet.Insert(taskSpec.SisyphusSolutionExec.Param...)
	}

	// 获取期望的步骤列表
	var expectProcessingList = make(clustermodel.CreateProcessListResponse, 0)
	var steps = listNodeUPDownExpectStepCodes(statusResponse.Type, statusResponse.HasAutoStorageNode, statusResponse.CRI)
	// 构建期望的组列表
	var expectGroupSet = sets.New[string]()
	switch statusResponse.Type {
	case node.NodeUpDownTypeNodeUp:
		// 节点上线包括(初始化[节点磁盘挂载]、预检、节点上线步骤)
		if statusResponse.HasAutoStorageNode {
			expectGroupSet.Insert(nodeconfig.NodeUPDownConfig.NodeUpDownGroupInfo.Initialing...)
		}
		expectGroupSet.Insert(nodeconfig.NodeUPDownConfig.NodeUpDownGroupInfo.Prefligting...)
		expectGroupSet.Insert(nodeconfig.NodeUPDownConfig.NodeUpDownGroupInfo.NodeUPInstalling...)
	case node.NodeUpDownTypeNodeDown:
		// 节点下线包括(节点下线步骤)
		expectGroupSet.Insert(nodeconfig.NodeUPDownConfig.NodeUpDownGroupInfo.NodeDownInstalling...)
	default:
		return fmt.Errorf("unknow node updown type %s", statusResponse.Type)
	}

	for _, group := range nodeconfig.NodeUPDownConfig.NodeUPDownStepGroups {
		// 先判断这个组是否需要
		if !expectGroupSet.Has(group.Code) {
			continue
		}
		group := group
		processing := clustermodel.CreateProcessResponse{
			Code:        group.Code,
			Name:        group.Alias,
			Description: group.Description,
			Status:      clustermodel.ProcessStatusWaiting,
		}
		var processingSteps clustermodel.CreateProcessStepListResponse
		for _, step := range group.Steps.ListStepsMatchCodes(steps...) {
			step := step
			var status = clustermodel.ProcessStatusWaiting
			if !specParamsSet.Has(step.Code) {
				status = clustermodel.ProcessStatusSuccess
			}
			processingSteps = append(processingSteps, clustermodel.CreateProcessStepResponse{
				Code:        step.Code,
				Name:        step.Alias,
				Description: step.Description,
				Status:      status,
			})
		}
		processing.Steps = processingSteps
		expectProcessingList = append(expectProcessingList, processing)
	}

	// 设置step状态
	if taskStatus != nil && len(taskStatus.Steps) != 0 {
		// 构建status step map 便于查询
		statusStepMap := make(map[string]installerv1alpha1.InstallerStatusInfo, len(taskStatus.Steps))
		// 设置step 状态
		for _, step := range taskStatus.Steps {
			name := step.StepName
			statusInfo := step.InstallerStatusInfo
			statusStepMap[name] = statusInfo
		}
		// 将状态写入 expectProcessingList
		for index, _ := range expectProcessingList {
			expectProcessing := &expectProcessingList[index]
			// 先设置每个step 的状态
			for index1, _ := range expectProcessing.Steps {
				expectStep := &(expectProcessing.Steps[index1])
				statusStepInfo, exist := statusStepMap[expectStep.Code]
				if !exist {
					continue
				}
				expectStep.Status = clustermodel.MustParseByInstallerStatus(statusStepInfo.Phase)
				if statusStepInfo.Reason != "" {
					expectStep.ErrorType = &statusStepInfo.Reason
				}
				if statusStepInfo.Message != "" {
					expectStep.ErrorMessage = &statusStepInfo.Message
				}
			}
		}
	}

	// step 的状态设置完成后 设置组状态
	for index, _ := range expectProcessingList {
		expectProcessing := &expectProcessingList[index]
		expectProcessing.Status = installerutil.GetStepsStatus(expectProcessing.Steps)
	}

	statusResponse.Processing = append(statusResponse.Processing, expectProcessingList...)
	return nil
}

// InstallerTaskRenderToCreateResponse
// 将 installer task 的内容渲染到create response
func (sisyphusSolutionExecTask) InstallerTaskRenderToCreateResponse(createResponse *node.NodeUpDownCreateResponse, taskSpec installerv1alpha1.InstallerTaskSpec) error {
	// nothing  to do
	return nil
}

// getClusterStatusFromInstallerTaskStatus
// 从task status 中获取当前集群的状态
func getClusterStatusFromInstallerTaskStatus(nodeUpDownType node.NodeUpDownType, hasAutoStorage bool, taskStatus *installerv1alpha1.InstallerTaskStatus, cri clustermodel.CRIType) node.NodeUpDownStatus {

	// 根据参数检查返回状态
	if taskStatus == nil {
		return node.NodeUpDownStatusCreateInitial
	}

	if len(taskStatus.Steps) == 0 {
		return ""
	}

	// 处理step未开始但标记未失败的情况
	if taskStatus.Steps[0].Phase == installerv1alpha1.StatusPhasePending && taskStatus.Phase == installerv1alpha1.StatusPhaseFailed {
		return node.NodeUpDownStatusCreateInitialFailed
	}

	// stepFunc 为内置函数 表示输入组信息 返回该组下的所有步骤
	stepFunc := func(groupCodes ...string) sets.Set[string] {
		steps := nodeconfig.NodeUPDownConfig.NodeUPDownStepGroups.ListStepCodeByGroupCodes(groupCodes...)
		return sets.New[string](steps...)
	}
	// 查看步骤状态 根据步骤状态设置集群状态所属生命周期
	// 获取节点初始化的步骤集合
	initialSteps := stepFunc(nodeconfig.NodeUPDownConfig.NodeUpDownGroupInfo.Initialing...)
	// 获取预检的步骤集合
	preflitingSteps := stepFunc(nodeconfig.NodeUPDownConfig.NodeUpDownGroupInfo.Prefligting...)

	// 获取执行中的步骤集合
	//installingSteps := stepFunc(nodeconfig.NodeUPDownConfig.NodeUpDownGroupInfo.Installing...)

	// 将status 中的step 转化为通过名称进行查找的map
	stepStatusMap := make(map[string]installerv1alpha1.SisyphusSolutionExecStepResult, len(taskStatus.Steps))
	for _, step := range taskStatus.Steps {
		step := step
		stepName := step.StepName
		stepStatusMap[stepName] = step
	}

	// 获取节点上下线的期望执行步骤有序列表
	sortSteps := listNodeUPDownExpectStepCodes(nodeUpDownType, hasAutoStorage, cri)
	for _, step := range sortSteps {
		stepResult, exist := stepStatusMap[step]
		if !exist {
			// stepStatusMap 中不存在 表示该步骤之前已执行过且已执行成功
			continue
		}
		switch stepResult.Phase {
		case installerv1alpha1.StatusPhasePending:
			fallthrough
		case installerv1alpha1.StatusPhaseRunning:
			if initialSteps.Has(step) {
				// 处理初始化中
				return node.NodeUpDownStatusCreateInitial
			} else if preflitingSteps.Has(step) && nodeUpDownType == node.NodeUpDownTypeNodeUp { // 只有上线有预检
				// 处理预检中
				return node.NodeUpDownStatusPreflighting
			} else {
				// 处理节点上下线中
				return node.NodeUpDownStatusInstalling
			}
		case installerv1alpha1.StatusPhaseSuccess:
			continue
		case installerv1alpha1.StatusPhaseFailed:
			if initialSteps.Has(step) {
				// 处理初始化中
				return node.NodeUpDownStatusCreateInitialFailed
			} else if preflitingSteps.Has(step) && nodeUpDownType == node.NodeUpDownTypeNodeUp { // 只有上线有预检
				// 处理预检中
				return node.NodeUpDownStatusPreflightFailed
			} else {
				// 处理节点上下线中
				return node.NodeUpDownStatusInstallFailed
			}
		default:
			return node.NodeUpDownStatusUnknow
		}
	}
	// 如果全部都是成功 则表示无步骤处理 在纳管中进行查看
	return node.NodeUpDownStatusSuccess
}

func shouldStepAdd(phase installerv1alpha1.StatusPhase) bool {
	//  成功 ｜ 失败 ｜ 运行中 ===》 step + 1
	switch phase {
	case installerv1alpha1.StatusPhaseSuccess:
		fallthrough
	case installerv1alpha1.StatusPhaseFailed:
		fallthrough
	case installerv1alpha1.StatusPhaseRunning:
		return true
	}
	return false
}

func handleExecTotalAndStep(response *node.NodeUpDownResponse, sortSteps []string, taskStatus *installerv1alpha1.InstallerTaskStatus) {
	response.Total = response.Total + int64(len(sortSteps))
	if taskStatus == nil {
		return
	}
	// 将status 中的step 转化为通过名称进行查找的map
	stepStatusMap := make(map[string]installerv1alpha1.SisyphusSolutionExecStepResult, len(taskStatus.Steps))
	for _, step := range taskStatus.Steps {
		step := step
		stepName := step.StepName
		stepStatusMap[stepName] = step
	}
	for _, step := range sortSteps {
		res, exist := stepStatusMap[step]
		if !exist {
			// 不存在表示已成功 ， step + 1
			response.Step = response.Step + 1
		}
		if shouldStepAdd(res.Phase) {
			response.Step = response.Step + 1
		}
	}
}

// listNodeUPDownExpectStepCodes 获取节点上下线的期望步骤编码
func listNodeUPDownExpectStepCodes(nodeType node.NodeUpDownType, hasAutoStorage bool, criType clustermodel.CRIType) []string {
	// 基础label nodeType
	var stepOptions = []string{string(nodeType)}
	// 如果有磁盘自动挂载，则需要跑磁盘自动挂载的步骤
	if hasAutoStorage {
		stepOptions = append(stepOptions, string(nodeType)+"/"+string(clustermodel.NodeStorageTypeAuto))
	}

	// 补充CRI - TYPE
	stepOptions = append(stepOptions, string(nodeType)+"/"+string(criType))

	// 获取节点上下线的期望执行步骤有序列表
	sortSteps := nodeconfig.NodeUPDownConfig.NodeUPDownStepGroups.ListStepCodeAnyMatchLabels(stepOptions...)

	return sortSteps
}
