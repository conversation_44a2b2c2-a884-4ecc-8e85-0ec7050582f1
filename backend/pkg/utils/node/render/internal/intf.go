package internal

import (
	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
)

// RequestRender
// 将request 进行渲染
type RequestRender interface {
	// Installer
	// 将create request 的内容渲染到 installer
	Installer(installer *installerv1alpha1.Installer, request node.NodeUpDownCreateRequest) error

	// BatchInstaller
	// 将batch create request 的内容渲染到 installer 这里只会渲染元信息而不会渲染task信息
	BatchInstaller(installer *installerv1alpha1.Installer, request node.NodeUpDownBatchCreateRequest) error

	// Response
	// 将installer 的内容渲染到 response
	Response(response *node.NodeUpDownResponse, installer installerv1alpha1.Installer) error
	// CreateStatusResponse
	// 将installer 的内容渲染到 create status response
	CreateStatusResponse(statusResponse *node.NodeUpDownStatusResponse, installer installerv1alpha1.Installer) error

	// CreateResponse
	// 将installer 的内容渲染到 create response
	CreateResponse(createResponse *node.NodeUpDownCreateResponse, installer installerv1alpha1.Installer) error
}
