package render

import (
	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	nodemodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/node/render/internal"
	util2 "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/uuid"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func NewIntf() Intf {
	uuidIntf := util2.NewUUIDIntf()
	return NewIntfWithParam(uuidIntf)
}
func NewIntfWithParam(uuidIntf util2.UUIDIntf) Intf {
	return &intfMgr{
		requestRenderToInstallerIntfList: []internal.RequestRender{
			internal.NewMetaNameRRTIL(uuidIntf),
			internal.NewMetaLabelsRRTIL(),
			internal.NewMetaAnnotationsRRTIL(),
			internal.NewMetaTaskRRTIL(uuidIntf),
		},
	}
}

type Intf interface {
	// CreateRequestRenderToInstaller
	// 将请求表单中的信息 apply 到 Installer
	CreateRequestRenderToInstaller(request nodemodel.NodeUpDownCreateRequest, installer *installerv1alpha1.Installer) error

	// BatchCreateRequestRenderToInstaller
	// 将批量请求表单中的信息 apply 到 Installer
	BatchCreateRequestRenderToInstaller(request nodemodel.NodeUpDownBatchCreateRequest, installer *installerv1alpha1.Installer) error

	// InstallerRenderToCreateResponse
	// 将installer 中的内容 apply 到 Response
	InstallerRenderToCreateResponse(installer installerv1alpha1.Installer, response *nodemodel.NodeUpDownCreateResponse) error

	// InstallerToResponse
	// 将installer 中的内容 apply 到 Response
	InstallerToResponse(installer installerv1alpha1.Installer, response *nodemodel.NodeUpDownResponse) error

	// BatchInstallerToBatchResponse
	// 将批量installer 中的内容 apply 到 Response列表
	BatchInstallerToBatchResponse(installer installerv1alpha1.Installer, responses *nodemodel.NodeUpDownResponseList) error

	// InstallerToStatusResponse
	// 将installer 中的内容 apply 到 StatusResponse
	InstallerToStatusResponse(installer installerv1alpha1.Installer, response *nodemodel.NodeUpDownStatusResponse) error
}

type intfMgr struct {
	// requestRenderToInstallerIntfList
	// 将request 中的内容渲染到 installer 中
	requestRenderToInstallerIntfList []internal.RequestRender
}

// CreateRequestRenderToInstaller
// 将请求表单中的信息 apply 到 Installer
func (mgr *intfMgr) CreateRequestRenderToInstaller(request nodemodel.NodeUpDownCreateRequest, installer *installerv1alpha1.Installer) error {
	for _, r := range mgr.requestRenderToInstallerIntfList {
		if err := r.Installer(installer, request); err != nil {
			return err
		}
	}
	return nil
}

// BatchCreateRequestRenderToInstaller
// 将批量请求表单中的信息 apply 到 Installer
func (mgr *intfMgr) BatchCreateRequestRenderToInstaller(request nodemodel.NodeUpDownBatchCreateRequest, installer *installerv1alpha1.Installer) error {
	// 处理元信息
	for _, r := range mgr.requestRenderToInstallerIntfList {
		if err := r.BatchInstaller(installer, request); err != nil {
			return err
		}
	}

	// 处理 template
	requests := request.ConvertToNodeUpDownCreateRequest()
	var installers = make([]installerv1alpha1.Installer, 0, len(requests))
	for _, req := range requests {
		var ins installerv1alpha1.Installer
		if err := mgr.CreateRequestRenderToInstaller(req, &ins); err != nil {
			return err
		}
		installers = append(installers, ins)
	}
	var installerTaskSpec []installerv1alpha1.InstallerTaskSpec
	// 将installer 转化为task spec
	for _, ins := range installers {
		// 先转化该installer 的task 转化为template
		var tasks []installerv1alpha1.InstallerTaskSpecTemplate
		for _, insTask := range ins.Spec.Tasks {
			var template installerv1alpha1.InstallerTaskSpecTemplate
			insTask.RejectToTemplate(&template)
			tasks = append(tasks, template)
		}
		// 写入spec
		installerTaskSpec = append(installerTaskSpec, installerv1alpha1.InstallerTaskSpec{
			TaskName:    ins.Name,
			Description: constants.TaskDescriptionForInstallerTemplate,
			Kind:        &installerv1alpha1.InstallerTaskKindCreateInstallerByTemplate,
			CreateInstallerByTemplate: &installerv1alpha1.CreateInstallerByTemplateSpec{
				IgnoreError: true,
				Template: installerv1alpha1.InstallerTemplate{
					ObjectMeta: installerv1alpha1.ObjectMetaTemplate{
						Namespace:   ins.ObjectMeta.Namespace,
						Labels:      ins.ObjectMeta.Labels,
						Annotations: ins.ObjectMeta.Annotations,
					},
					Spec: installerv1alpha1.InstallerTemplateSpec{
						Tasks: tasks,
					},
				},
			},
		})
	}
	installer.Spec.Tasks = installerTaskSpec
	return nil
}

// InstallerRenderToCreateResponse
// 将installer 中的内容 apply 到 Response
func (mgr *intfMgr) InstallerRenderToCreateResponse(installer installerv1alpha1.Installer, response *nodemodel.NodeUpDownCreateResponse) error {
	for _, r := range mgr.requestRenderToInstallerIntfList {
		if err := r.CreateResponse(response, installer); err != nil {
			return err
		}
	}
	return nil
}

// InstallerToResponse
// 将installer 中的内容 apply 到 Response
func (mgr *intfMgr) InstallerToResponse(installer installerv1alpha1.Installer, response *nodemodel.NodeUpDownResponse) error {
	if installer.Spec.ReApply {
		installer.Status = installerv1alpha1.InstallerStatus{}
	}
	for _, r := range mgr.requestRenderToInstallerIntfList {
		if err := r.Response(response, installer); err != nil {
			return err
		}
	}
	// 如果installer 在删除中 标记状态为删除中
	if installer.DeletionTimestamp != nil {
		response.Status = nodemodel.NodeUpDownStatusDeleting
	}
	response.CreateTime = installer.CreationTimestamp.UTC()
	return nil
}

// BatchInstallerToBatchResponse
// 将批量installer 中的内容 apply 到 Response列表
func (mgr *intfMgr) BatchInstallerToBatchResponse(installer installerv1alpha1.Installer, responses *nodemodel.NodeUpDownResponseList) error {
	mockInstallers := make([]installerv1alpha1.Installer, 0, len(installer.Spec.Tasks))
	for _, task := range installer.Spec.Tasks {
		mockInstaller, err := TaskAsMockInstaller(task)
		if err != nil {
			return err
		}
		mockInstallers = append(mockInstallers, mockInstaller)
	}
	// 将mock installer 转化为 response列表
	for _, installer := range mockInstallers {
		var response nodemodel.NodeUpDownResponse
		if err := mgr.InstallerToResponse(installer, &response); err != nil {
			return err
		}
		*responses = append(*responses, response)
	}
	return nil
}

func TaskAsMockInstaller(task installerv1alpha1.InstallerTaskSpec) (installerv1alpha1.Installer, error) {
	if task.CreateInstallerByTemplate == nil {
		return installerv1alpha1.Installer{}, errors.NewFromCode(errors.Var.BatchInstallerError)
	}

	var tasks = make(installerv1alpha1.InstallerTaskSpecList, 0, len(task.CreateInstallerByTemplate.Template.Spec.Tasks))
	for _, taskTemp := range task.CreateInstallerByTemplate.Template.Spec.Tasks {
		tasks = append(tasks, taskTemp.ConvertToInstallerTaskSpec())
	}

	return installerv1alpha1.Installer{
		ObjectMeta: metav1.ObjectMeta{
			Namespace:   task.CreateInstallerByTemplate.Template.ObjectMeta.Namespace,
			Labels:      task.CreateInstallerByTemplate.Template.ObjectMeta.Labels,
			Annotations: task.CreateInstallerByTemplate.Template.ObjectMeta.Annotations,
		},
		Spec: installerv1alpha1.InstallerSpec{
			Tasks: tasks,
		},
	}, nil
}

// InstallerToStatusResponse
// 将installer 中的内容 apply 到 StatusResponse
func (mgr *intfMgr) InstallerToStatusResponse(installer installerv1alpha1.Installer, statusResponse *nodemodel.NodeUpDownStatusResponse) error {
	if installer.Spec.ReApply {
		installer.Status = installerv1alpha1.InstallerStatus{}
	}
	var response = new(nodemodel.NodeUpDownResponse)
	if err := mgr.InstallerToResponse(installer, response); err != nil {
		return err
	}
	statusResponse.ClusterName = response.ClusterName
	statusResponse.Ip = response.Ip
	statusResponse.Type = response.Type
	statusResponse.Status = response.Status

	for _, r := range mgr.requestRenderToInstallerIntfList {
		if err := r.CreateStatusResponse(statusResponse, installer); err != nil {
			return err
		}
	}
	return nil
}
