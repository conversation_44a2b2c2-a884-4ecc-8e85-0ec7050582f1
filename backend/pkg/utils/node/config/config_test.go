package config

import (
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"testing"

	"gopkg.in/yaml.v2"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
	clusterconfig "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/cluster/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/sisyphustype"
	"k8s.io/apimachinery/pkg/util/sets"
)

var (
	nodeDownSteps = `
[
    {
        "name": "sisyphus-system-step-prepare-execution",
        "nickname": "执行部署平台初始化步骤",
        "orchestration": {
            "name": "sisyphus",
            "type": "component",
            "version": "v1.4.0-1.0.0-universal"
        },
        "actions": [
            "install"
        ]
    },
    {
        "name": "remove-from-kubernetes",
        "nickname": "从集群中移除节点",
        "orchestration": {
            "name": "kubernetes",
            "type": "component",
            "version": "v1.27.10-1.0.1-universal"
        },
        "actions": [
            "uninstall_node"
        ]
    },
    {
        "name": "uninstall-containerd",
        "nickname": "卸载容器运行时并清除用户数据",
        "orchestration": {
            "name": "containerd",
            "type": "component",
            "version": "v1.6.28-1.0.1-universal"
        },
        "actions": [
            "uninstall"
        ]
    },
    {
        "name": "uninstall-docker",
        "nickname": "卸载容器运行时并清除用户数据",
        "orchestration": {
            "name": "containerd",
            "type": "component",
            "version": "v1.6.28-1.0.1-universal"
        },
        "actions": [
            "uninstall"
        ]
    },
    {
        "name": "uninstall-sisyphus-flag",
        "nickname": "卸载sisyphus初始化步骤标记"
    }
]
`
	nodeUPSteps = `
[
    {
        "name": "sisyphus-system-step-prepare-execution",
        "nickname": "执行部署平台初始化步骤",
        "orchestration": {
            "name": "sisyphus",
            "type": "component",
            "version": "v1.4.0-1.0.0-universal"
        },
        "actions": [
            "install"
        ],
        "status": "running"
    },
    {
        "name": "precheck-check-os",
        "nickname": "预检-检查CPU架构/操作系统",
        "orchestration": {
            "name": "precheck",
            "type": "component",
            "version": "v1.0.3-1.0.0-universal"
        },
        "actions": [
            "check-os"
        ],
        "status": "pending"
    },
    {
        "name": "precheck-check-linux-packages",
        "nickname": "预检-检查是否存在已知的冲突软件包",
        "orchestration": {
            "name": "precheck",
            "type": "component",
            "version": "v1.0.3-1.0.0-universal"
        },
        "actions": [
            "check-linux-packages"
        ],
        "status": "pending"
    },
    {
        "name": "precheck-check-master-resources",
        "nickname": "预检-检查主控节点资源是否符合要求",
        "orchestration": {
            "name": "precheck",
            "type": "component",
            "version": "v1.0.3-1.0.0-universal"
        },
        "actions": [
            "check-resources"
        ],
        "status": "pending"
    },
    {
        "name": "install-chrony-client",
        "nickname": "安装时间同步服务(客户端)",
        "orchestration": {
            "name": "chrony",
            "type": "component",
            "version": "multiversion-1.2.4-universal"
        },
        "actions": [
            "sync-outer-server"
        ],
        "status": "pending"
    },
    {
        "name": "install-containerd",
        "nickname": "安装containerd容器运行时",
        "orchestration": {
            "name": "containerd",
            "type": "component",
            "version": "v1.6.28-1.0.1-universal"
        },
        "actions": [
            "install"
        ],
        "status": "pending"
    },
    {
        "name": "install-docker",
        "nickname": "安装docker容器运行时"
    },
    {
        "name": "join-kubernetes-node",
        "nickname": "添加kubernetes其它节点",
        "orchestration": {
            "name": "kubernetes",
            "type": "component",
            "version": "v1.27.10-1.0.1-universal"
        },
        "actions": [
            "join_node"
        ],
        "status": "pending"
    },
    {
        "name": "label-gpu-nodes",
        "nickname": "添加GPU标签",
        "orchestration": {
            "name": "common-tools",
            "type": "component",
            "version": "v0.0.2-1.0.0-universal"
        },
        "actions": [
            "mark-nodes"
        ],
        "status": "pending"
    }
]
`
)

func Test_ShouldReadConfig(t *testing.T) {
	config.NodeUpDownStepConfigPath.Value = "../../../../../node-up-down-config.yaml"
	t.Run("tt", func(t *testing.T) {
		if err := ShouldReadConfig(); err != nil {
			t.Fatal(ShouldReadConfig())
		}
		bs, _ := json.Marshal(NodeUPDownConfig)
		fmt.Println(string(bs))
	})
}

func Test_NodeUPDown_Config_Sync_NodeDownSteps(t *testing.T) {
	// 先读取到配置
	config.NodeUpDownStepConfigPath.Value = "../../../../../node-up-down-config.yaml"
	if err := ShouldReadConfig(); err != nil {
		t.Errorf(err.Error())
		return
	}
	// 设置需要写入配置的信息
	var sisyphusStep []sisyphustype.SisyphusStep
	if err := json.Unmarshal([]byte(nodeDownSteps), &sisyphusStep); err != nil {
		t.Errorf("format error,e:%v", err)
	}
	// 写入配置
	ndStep := groupNodeDown(sisyphusStep)

	SyncConfig("node-down-group", string(node.NodeUpDownTypeNodeDown), ndStep)
	ya, _ := yaml.Marshal(NodeUPDownConfig)
	os.WriteFile(config.NodeUpDownStepConfigPath.Value, ya, os.ModePerm)

}

func Test_NodeUPDown_Config_Sync_NodeUPSteps(t *testing.T) {
	// 先读取到配置
	config.NodeUpDownStepConfigPath.Value = "../../../../../node-up-down-config.yaml"
	if err := ShouldReadConfig(); err != nil {
		t.Errorf(err.Error())
		return
	}
	// 设置需要写入配置的信息
	var sisyphusStep []sisyphustype.SisyphusStep
	if err := json.Unmarshal([]byte(nodeUPSteps), &sisyphusStep); err != nil {
		t.Errorf("format error,e:%v", err)
	}
	// 写入配置
	ndStep, npStep, nuStep := groupNodeUp(sisyphusStep)

	SyncConfig("node-disk-mount-group", string(node.NodeUpDownTypeNodeUp)+"/auto", ndStep)
	SyncConfig("node-up-prefix-group", string(node.NodeUpDownTypeNodeUp), npStep)
	SyncConfig("node-up-group", string(node.NodeUpDownTypeNodeUp), nuStep)
	ya, _ := yaml.Marshal(NodeUPDownConfig)
	os.WriteFile(config.NodeUpDownStepConfigPath.Value, ya, os.ModePerm)
}

// 对节点下线进行分组
func groupNodeDown(sisyphusSteps []sisyphustype.SisyphusStep) (nodeDownSteps []sisyphustype.SisyphusStep) {
	for _, step := range sisyphusSteps {
		step := step
		nodeDownSteps = append(nodeDownSteps, step)
	}
	return
}

// 对节点上线进行分组
func groupNodeUp(sisyphusSteps []sisyphustype.SisyphusStep) (diskMountSteps []sisyphustype.SisyphusStep, preflightSteps []sisyphustype.SisyphusStep, nodeUpSteps []sisyphustype.SisyphusStep) {
	for _, step := range sisyphusSteps {
		step := step
		if strings.HasPrefix(step.Name, "precheck") || step.Name == "sisyphus-system-step-prepare-execution" {
			preflightSteps = append(preflightSteps, step)
		} else {
			nodeUpSteps = append(nodeUpSteps, step)
		}
	}
	diskMountSteps = append(diskMountSteps, sisyphustype.SisyphusStep{Name: "sisyphus-disk-execution", Nickname: "执行部署平台磁盘管理步骤"})
	return
}

func SyncConfig(groupName string, nodeTypeLabel string, sisyphusSteps []sisyphustype.SisyphusStep) {
	stepGroupHandleFunc := func(groupName string, nodeTypeLabel string, sisyphusSteps []sisyphustype.SisyphusStep, groups clusterconfig.StepGroups) {
		// 先查找到group的index
		groupIndex := -1
		for index, g := range groups {
			if g.Code == groupName {
				groupIndex = index
			}
		}
		if groupIndex == -1 {
			return
		}
		groupItem := groups[groupIndex]
		removeNumber := 0
		// 先移除所有 和 nodeType 有关的Step
		for index, _ := range groupItem.Steps {
			groupStep := groupItem.Steps[index-removeNumber]
			stepSupportNodeTypes := sets.New[string](groupStep.Labels...)
			if stepSupportNodeTypes.Has(string(nodeTypeLabel)) {
				stepSupportNodeTypes.Delete(string(nodeTypeLabel))
			}
			if stepSupportNodeTypes.Has(string(nodeTypeLabel) + "/" + string(clustermodel.CRITypeContainerd)) {
				stepSupportNodeTypes.Delete(string(nodeTypeLabel) + "/" + string(clustermodel.CRITypeContainerd))
			}

			if stepSupportNodeTypes.Has(string(nodeTypeLabel) + "/" + string(clustermodel.CRITypeDocker)) {
				stepSupportNodeTypes.Delete(string(nodeTypeLabel) + "/" + string(clustermodel.CRITypeDocker))
			}

			resultNodeConfigTypes := stepSupportNodeTypes.UnsortedList()
			if len(resultNodeConfigTypes) == 0 {
				groupItem.Steps = append(groupItem.Steps[0:index-removeNumber], groupItem.Steps[index-removeNumber+1:]...)
				removeNumber++
			} else {
				groupItem.Steps[index-removeNumber].Labels = resultNodeConfigTypes
			}
		}
		// 向最后扩展
		for _, sisyphusStep := range sisyphusSteps {
			var labels []string
			if sisyphusStep.Name == "uninstall-containerd" {
				labels = append(labels, string(nodeTypeLabel)+"/"+string(clustermodel.CRITypeContainerd))
			} else if sisyphusStep.Name == "uninstall-docker" {
				labels = append(labels, string(nodeTypeLabel)+"/"+string(clustermodel.CRITypeDocker))
			} else if sisyphusStep.Name == "install-containerd" {
				labels = append(labels, string(nodeTypeLabel)+"/"+string(clustermodel.CRITypeContainerd))
			} else if sisyphusStep.Name == "install-docker" {
				labels = append(labels, string(nodeTypeLabel)+"/"+string(clustermodel.CRITypeDocker))
			} else {
				labels = append(labels, string(nodeTypeLabel))
			}

			groupItem.Steps = append(groupItem.Steps, clusterconfig.Step{
				Code:   sisyphusStep.Name,
				Alias:  sisyphusStep.Nickname,
				Labels: labels,
			})
		}

		groups[groupIndex] = groupItem
	}
	// 处理节点上下线的
	stepGroupHandleFunc(groupName, nodeTypeLabel, sisyphusSteps, NodeUPDownConfig.NodeUPDownStepGroups)
}
