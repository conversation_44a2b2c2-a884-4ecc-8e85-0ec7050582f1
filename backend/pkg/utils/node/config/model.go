package config

import (
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/cluster/config"
)

type Config struct {
	SolutionInfos       NodeUpDownSolutionInfoList `json:"solutionInfos" yaml:"solutionInfos"`             // 基线对应的解决方案信息列表
	MasterNodeLabelKeys []string                   `json:"masterNodeLabelKeys" yaml:"masterNodeLabelKeys"` // master 节点包含的label key
	MasterNodeLabelMaps map[string]string          `json:"masterNodeLabelMaps" yaml:"masterNodeLabelMaps"` // master 节点包含的label map
	Group               NodeGroup                  `json:"group" yaml:"group"`                             // 节点分组信息
	Action              SolutionAction             `json:"action" yaml:"action"`                           // 行为选项参数列表
	// 记录step 分组信息 描述哪些步骤在预检 哪些步骤在创建中
	NodeUpDownGroupInfo  GroupInfo         `json:"groupInfo" yaml:"groupInfo"`
	NodeUPDownStepGroups config.StepGroups `json:"nodeUpDownStepGroups" yaml:"nodeUpDownStepGroups"` // 节点上下线的步骤分组
	Initial              InitialGroup      `json:"initial" yaml:"initial"`                           // 初始化分组信息
}

type NodeUpDownSolutionInfoList []NodeUpDownSolutionInfo

func (arr NodeUpDownSolutionInfoList) GetSolutionInfoByBaselineVersion(baselineVersion string) (NodeUpDownSolutionInfo, error) {
	for _, item := range arr {
		if item.BaselineVersion == baselineVersion {
			return item, nil
		}
	}
	return NodeUpDownSolutionInfo{}, errors.NewFromCodeWithMessage(errors.Var.UnKnowBaselineVersion, baselineVersion)
}

type NodeUpDownSolutionInfo struct {
	BaselineVersion string `json:"baselineVersion" yaml:"baselineVersion"` // 底座基线版本
	SolutionName    string `json:"solutionName" yaml:"solutionName"`       // 节点上下线解决方案名称
	SolutionVersion string `json:"solutionVersion" yaml:"solutionVersion"` // 底座版本
}

type GroupInfo struct {
	Initialing         []string `json:"initialing" yaml:"initialing"`
	Prefligting        []string `json:"prefligting" yaml:"prefligting"`
	NodeUPInstalling   []string `json:"nodeUpInstalling" yaml:"nodeUpInstalling"`
	NodeDownInstalling []string `json:"nodeDownInstalling" yaml:"nodeDownInstalling"`
}

// NodeGroup 节点分组信息
type NodeGroup struct {
	ControlNode  string `json:"controlNode" yaml:"controlNode"`
	NodeUpWorker string `json:"nodeUpWorker" yaml:"nodeUpWorker"`
	NodeUpGpu    string `json:"nodeUpGpu" yaml:"nodeUpGpu"`
	NodeDown     string `json:"nodeDown" yaml:"nodeDown"`
}

// SolutionAction 行为选项参数列表
type SolutionAction struct {
	NodeUP   string `json:"nodeUP" yaml:"nodeUp"`
	NodeDown string `json:"nodeDown" yaml:"nodeDown"`
}

// InitialGroup 初始化分组信息
type InitialGroup struct {
	GroupCode                          string `json:"groupCode" yaml:"groupCode"`
	GroupAlias                         string `json:"groupAlias" yaml:"groupAlias"`
	NodeInitialCode                    string `json:"nodeInitialCode" yaml:"nodeInitialCode"`
	NodeInitialAlias                   string `json:"nodeInitialAlias" yaml:"nodeInitialAlias"`
	NodeDownSisyphusSolutionApplyCode  string `json:"nodeDownSisyphusSolutionApplyCode" yaml:"nodeDownSisyphusSolutionApplyCode"`
	NodeDownSisyphusSolutionApplyAlias string `json:"nodeDownSisyphusSolutionApplyAlias" yaml:"nodeDownSisyphusSolutionApplyAlias"`
	NodeUpSisyphusSolutionApplyCode    string `json:"nodeUpSisyphusSolutionApplyCode" yaml:"nodeUpSisyphusSolutionApplyCode"`
	NodeUpSisyphusSolutionApplyAlias   string `json:"nodeUpSisyphusSolutionApplyAlias" yaml:"nodeUpSisyphusSolutionApplyAlias"`
}
