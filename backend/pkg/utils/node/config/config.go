package config

import (
	"fmt"
	"os"
	"sync"

	"gopkg.in/yaml.v3"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
)

var NodeUPDownConfig *Config
var stepGroupConfigLock sync.Mutex

// ShouldReadConfig
// 启动任务是读取配置文件
func ShouldReadConfig() error {
	stepGroupConfigLock.Lock()
	defer stepGroupConfigLock.Unlock()
	if NodeUPDownConfig != nil {
		return nil
	}
	bytes, err := os.ReadFile(config.NodeUpDownStepConfigPath.Value)
	if err != nil {
		return fmt.Errorf("read file %s,appear error:%v", config.NodeUpDownStepConfigPath.Value, err)
	}
	NodeUPDownConfig = new(Config)
	err = yaml.Unmarshal(bytes, &NodeUPDownConfig)
	if err != nil {
		return fmt.Errorf("unmarshal file %s,appear error:%v", config.NodeUpDownStepConfigPath.Value, err)
	}
	return nil
}
