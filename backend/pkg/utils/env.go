package utils

import (
	"os"
	"strconv"
	"strings"
	"time"
)

// GetEnvStringWithDefault 获取环境变量字符串值，如果环境变量不存在，则返回默认值
func GetEnvStringWithDefault(key, defaultValue string) string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return value
}

// GetEnvInt64WithDefault 获取环境变量整数值，如果环境变量不存在，则返回默认值
func GetEnvInt64WithDefault(key string, defaultValue int64) int64 {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	intValue, err := strconv.ParseInt(value, 10, 64)
	if err != nil {
		return defaultValue
	}
	return intValue
}

// GetEnvFloat64WithDefault 获取环境变量浮点数值，如果环境变量不存在，则返回默认值
func GetEnvFloat64WithDefault(key string, defaultValue float64) float64 {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	floatValue, err := strconv.ParseFloat(value, 64)
	if err != nil {
		return defaultValue
	}
	return floatValue
}

// GetEnvDurationWithDefault 获取环境变量时间值，如果环境变量不存在，则返回默认值
func GetEnvDurationWithDefault(key string, defaultValue time.Duration) time.Duration {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	duration, err := time.ParseDuration(value)
	if err != nil {
		return defaultValue
	}
	return duration
}

// GetEnvBoolWithDefault 获取环境变量布尔值，如果环境变量不存在，则返回默认值
func GetEnvBoolWithDefault(key string, defaultValue bool) bool {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	boolValue, err := strconv.ParseBool(value)
	if err != nil {
		return defaultValue
	}
	return boolValue
}

// GetEnvStringSliceWithDefault 获取环境变量字符串切片值，如果环境变量不存在，则返回默认值
func GetEnvStringSliceWithDefault(key string, defaultValue []string) []string {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	return strings.Split(value, ",")
}

// GetEnvIntSliceWithDefault 获取环境变量整数切片值，如果环境变量不存在，则返回默认值
func GetEnvIntSliceWithDefault(key string, defaultValue []int64) []int64 {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	intSlice := []int64{}
	for _, v := range strings.Split(value, ",") {
		intValue, err := strconv.ParseInt(v, 10, 64)
		if err != nil {
			return defaultValue
		}
		intSlice = append(intSlice, intValue)
	}
	return intSlice
}

// GetEnvFloatSliceWithDefault 获取环境变量浮点数切片值，如果环境变量不存在，则返回默认值
func GetEnvFloatSliceWithDefault(key string, defaultValue []float64) []float64 {
	value := os.Getenv(key)
	if value == "" {
		return defaultValue
	}
	floatSlice := []float64{}
	for _, v := range strings.Split(value, ",") {
		floatValue, err := strconv.ParseFloat(v, 64)
		if err != nil {
			return defaultValue
		}
		floatSlice = append(floatSlice, floatValue)
	}
	return floatSlice
}
