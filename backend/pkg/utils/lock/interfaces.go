package lock

import (
	"context"
	"errors"
	"time"
)

type DistributeLock interface {
	// Lock lock and wait
	//	@param	ctx		上下文
	//	@param	lockKey	锁定的key
	Lock(ctx context.Context, lockKey string) (Distribute<PERSON>ockHandler, error)

	// TryLock try lock without wait
	//	@param	ctx		上下文
	//	@param	lockKey	锁定的key
	TryLock(ctx context.Context, lockKey string) (DistributeLockH<PERSON>ler, error)

	// TryLockAndWait try lock with wait time out
	//	@param	ctx		上下文
	//	@param	lockKey	锁定的key
	//	@param	timeout	超时时间
	TryLockAndWait(ctx context.Context, lockKey string, timeout time.Duration) (Distribute<PERSON><PERSON><PERSON><PERSON><PERSON>, error)
}

type DistributeLockHandler interface {
	UnLock(ctx context.Context) error
}

var (
	getLockTimeout = errors.New("get lock timeout")
	alreadyLocked  = errors.New("already locked")
	timeOutIllegal = errors.New("timeout illegal,value must bigger than zero")
	contextIsDone  = errors.New("context is done")
)
