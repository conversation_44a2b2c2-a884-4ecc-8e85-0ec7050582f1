package lock

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/google/uuid"
	rds "github.com/redis/go-redis/v9"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
)

const (
	defaultRedisKeyMaxDuration = 10 * time.Minute
)

func NewRDSDistributeLock(client *rds.Client) DistributeLock {
	return &rdsDistributeLock{
		rdsClient:      client,
		keyMaxDuration: defaultRedisKeyMaxDuration,
	}
}

type rdsDistributeLock struct {
	rdsClient      *rds.Client   // redis 客户端
	keyMaxDuration time.Duration // redis key 最大过期时间
}

// Lock lock and wait
//
//	ctx 上下文
//	lockKey 锁定的key
func (lock *rdsDistributeLock) Lock(ctx context.Context, lockKey string) (DistributeLockHandler, error) {
	return lock.TryLockAndWait(ctx, lockKey, lock.keyMaxDuration)
}

// TryLock try lock without wait
//
//	ctx 上下文
//	lockKey 锁定的key
func (lock *rdsDistributeLock) TryLock(ctx context.Context, lockKey string) (DistributeLockHandler, error) {
	lockValue := uuid.New().String()
	logger.GetSugared().Debugf("rdsDistributeLock[TryLock] lockkey is %s lock value is %s", lockKey, lockValue)
	success, err := lock.rdsClient.SetNX(ctx, lockKey, lockValue, lock.keyMaxDuration).Result()
	if err != nil {
		return nil, err
	}
	if success {
		return newRDSDistributeLockHandler(lockKey, lockValue, lock.rdsClient), nil
	}
	return nil, alreadyLocked
}

// TryLockAndWait try lock with wait time out
//
//	ctx 上下文
//	lockKey 锁定的key
func (lock *rdsDistributeLock) TryLockAndWait(ctx context.Context, lockKey string, timeout time.Duration) (DistributeLockHandler, error) {
	lockValue := uuid.New().String()
	logger.GetSugared().Debugf("rdsDistributeLock[TryLockAndWait] lockkey is %s lock value is %s", lockKey, lockValue)
	success, err := lock.rdsClient.SetNX(ctx, lockKey, lockValue, lock.keyMaxDuration).Result()
	if err != nil {
		return nil, err
	}
	if success {
		return newRDSDistributeLockHandler(lockKey, lockValue, lock.rdsClient), nil
	}
	if timeout.Milliseconds() <= 0 {
		return nil, timeOutIllegal
	}

	// 等待重试
	taf, err := newRetryTicker(timeout)
	if err != nil {
		return nil, err
	}
	defer taf.close()
	for {
		select {
		case <-taf.ticker.C:
			// 定时任务
			success, err := lock.rdsClient.SetNX(ctx, lockKey, lockValue, lock.keyMaxDuration).Result()
			if err != nil {
				return nil, err
			}
			if !success {
				continue
			}
			return newRDSDistributeLockHandler(lockKey, lockValue, lock.rdsClient), nil
		case <-taf.after:
			// 超时
			return nil, getLockTimeout
		case <-ctx.Done():
			// IO 关闭
			return nil, contextIsDone
		}
	}
}

func newRDSDistributeLockHandler(lockKey string, lockValue string, redisCli *rds.Client) DistributeLockHandler {
	return &rdsDistributeLockHandler{
		lockKey:   lockKey,
		lockValue: lockValue,
		redisCli:  redisCli,
	}
}

type rdsDistributeLockHandler struct {
	lockKey   string
	lockValue string
	redisCli  *rds.Client
}

func (handler *rdsDistributeLockHandler) UnLock(ctx context.Context) error {
	value, err := handler.redisCli.Get(ctx, handler.lockKey).Result()
	if err != nil {
		return err
	}
	if strings.EqualFold(value, handler.lockValue) {
		return handler.redisCli.Del(ctx, handler.lockKey).Err()
	}
	return fmt.Errorf("redis lock unlock error,key is %s,value is %s", handler.lockKey, handler.lockValue)
}
