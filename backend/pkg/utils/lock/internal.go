package lock

import (
	"fmt"
	"time"
)

func newRetryTicker(timeout time.Duration) (*tickerAndAfter, error) {
	wait := timeout.Milliseconds()
	if wait <= 0 {
		return nil, fmt.Errorf("error of newRetryTicker,waitTime is %d", wait)
	}
	after := time.After(timeout)
	ticker := time.NewTicker(convertTimeSplit(timeout))
	return &tickerAndAfter{
		ticker: ticker,
		after:  after,
	}, nil

}

type tickerAndAfter struct {
	ticker *time.Ticker
	after  <-chan time.Time
}

func (taf *tickerAndAfter) close() {
	_ = taf.ticker.Stop
}

func convertTimeSplit(waitTime time.Duration) time.Duration {
	million := waitTime.Milliseconds()
	if million <= 1000 {
		return time.Duration(million) * time.Millisecond / 2
	} else {
		return 1000 * time.Millisecond
	}
}
