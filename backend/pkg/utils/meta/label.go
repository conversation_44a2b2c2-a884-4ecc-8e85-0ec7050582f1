package meta

import (
	"fmt"
	"strings"
)

func ToLabelStr(labels map[string]string) string {
	var result string
	if len(labels) == 0 {
		return result
	}
	kvStrList := make([]string, 0, len(labels))
	for k, v := range labels {
		kvStrList = append(kvStrList, fmt.Sprintf("%s=%s", k, v))
	}
	return strings.Join(kvStrList, ",")
}

func GetFromMap(mapObj map[string]string, key string) (string, bool) {
	var result string
	var exist bool
	if len(mapObj) == 0 {
		return result, exist
	}
	result, exist = mapObj[key]
	return result, exist
}

func MergeMap(src map[string]string, dst map[string]string) map[string]string {
	var result = dst
	if result == nil {
		result = make(map[string]string)
	}
	if src != nil {
		for k, v := range src {
			result[k] = v
		}
	}
	return result

}
