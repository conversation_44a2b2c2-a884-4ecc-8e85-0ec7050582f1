package utils

import (
	"sort"

	"sigs.k8s.io/controller-runtime/pkg/client"
)

func StructConvert2Pointer[T any](list []T) []*T {
	var newList []*T
	for _, item := range list {
		item := item
		var itemP *T = &item
		newList = append(newList, itemP)
	}
	return newList
}

func PointerConvert2Struct[T any](list []*T) []T {
	var newList []T
	for _, item := range list {
		item := item
		newList = append(newList, *item)
	}
	return newList
}

func SortByCreateTime[T client.Object](objects []T) {
	sortUtil := clientObjectSortByCreateTime[T]{
		list: objects,
	}
	sort.Sort(sortUtil)
}

type clientObjectSortByCreateTime[T client.Object] struct {
	list []T
}

func (sortUtil clientObjectSortByCreateTime[T]) Len() int {
	var list []T = sortUtil.list
	return len(list)
}
func (sortUtil clientObjectSortByCreateTime[T]) Less(i, j int) bool {
	var list []T = sortUtil.list
	createTimeI := list[i].GetCreationTimestamp()
	createTimeJ := list[j].GetCreationTimestamp()
	return createTimeJ.After(createTimeI.Time)
}
func (sortUtil clientObjectSortByCreateTime[T]) Swap(i, j int) {
	var list []T = sortUtil.list
	list[i], list[j] = list[j], list[i]
}
