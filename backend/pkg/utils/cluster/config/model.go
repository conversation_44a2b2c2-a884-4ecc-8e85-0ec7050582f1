package config

import (
	"fmt"
	"strings"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"k8s.io/apimachinery/pkg/util/sets"
)

// Config
// 创建集群配置文件
type Config struct {
	// SolutionInfos 记录k8s 版本、cri版本同solution name 和 version 的关系
	SolutionInfos SolutionInfoList `json:"solutionInfos" yaml:"solutionInfos"`
	// 记录step 分组信息 描述哪些步骤在预检 哪些步骤在创建中
	ClusterCreatingGroupInfo GroupInfo `json:"groupInfo" yaml:"groupInfo"`
	// NodeHasGpuCode 当节点有gpu 时 需要添加该分组
	NodeHasGpuCode string `json:"nodeHasGpuCode" yaml:"nodeHasGpuCode"`
	// NodeRoleMasterCodes 记录master 节点的分组
	NodeRoleMasterCodes []string `json:"nodeRoleMasterCodes" yaml:"nodeRoleMasterCodes"`
	// 集群创建配置文件分组相关
	StepGroups StepGroups `json:"stepGroups" yaml:"stepGroups"`
	// 集群纳管步骤信息
	ClusterManageGroup StepGroup `json:"clusterManageGroup" yaml:"clusterManageGroup"`
	// 非高可用需要选择第一台默认负载均衡节点的分组
	GroupKeyDefaultLb string `json:"groupKeyDefaultLb" yaml:"groupKeyDefaultLb"`
	// 不同集群类型的集群分组
	NodeTypeGroup NodeTypeGroupConfig `json:"nodeTypeGroup,omitempty" yaml:"nodeTypeGroup,omitempty"`
	// 对应初始化的步骤与分组
	InitialGroupInfo InitialGroup `json:"initial" yaml:"initial"`
	// 集群纳管后置步骤
	AfterAddCluster AfterAddCluster `json:"afterAddCluster" yaml:"afterAddCluster"`
}

type AfterAddCluster struct {
	Code        string `json:"code,omitempty" yaml:"code,omitempty"`
	Alias       string `json:"alias,omitempty" yaml:"alias,omitempty"`
	Description string `json:"description,omitempty" yaml:"description,omitempty"`
	Steps       Steps  `json:"steps,omitempty" yaml:"steps,omitempty"`
}

type InitialGroup struct {
	GroupCode                  string `json:"groupCode" yaml:"groupCode"`
	GroupAlias                 string `json:"groupAlias" yaml:"groupAlias"`
	NodeInitialCode            string `json:"nodeInitialCode" yaml:"nodeInitialCode"`
	NodeInitialAlias           string `json:"nodeInitialAlias" yaml:"nodeInitialAlias"`
	SisyphusSolutionApplyCode  string `json:"sisyphusSolutionApplyCode" yaml:"sisyphusSolutionApplyCode"`
	SisyphusSolutionApplyAlias string `json:"sisyphusSolutionApplyAlias" yaml:"sisyphusSolutionApplyAlias"`
}
type NodeTypeGroupConfig struct {
	AllInOne       NodeGroupConfig       `json:"allInOne,omitempty" yaml:"allInOne,omitempty"`             // all In one
	StandardNoneHA NodeGroupConfig       `json:"standardNoneHA,omitempty" yaml:"standardNoneHA,omitempty"` // 标准非高可用
	StandardHA     NodeGroupConfig       `json:"standardHA,omitempty" yaml:"standardHA,omitempty"`         // 标准高可用
	MinimizeHA     NodeGroupConfig       `json:"minimizeHA,omitempty" yaml:"minimizeHA,omitempty"`         // 最小化高可用
	KubeOVNPlugin  PluginNodeGroupConfig `json:"kubeOvnPlugin,omitempty" yaml:"kubeOvnPlugin,omitempty"`   // 最小化高可用
}

type NodeGroupConfig struct {
	First   []string `json:"first,omitempty" yaml:"first,omitempty"`     // 第1台节点分组
	Second  []string `json:"second,omitempty" yaml:"second,omitempty"`   // 第2台节点分组
	Third   []string `json:"third,omitempty" yaml:"third,omitempty"`     // 第3台节点分组
	Fourth  []string `json:"fourth,omitempty" yaml:"fourth,omitempty"`   // 第4台节点分组
	Fifth   []string `json:"fifth,omitempty" yaml:"fifth,omitempty"`     // 第5台节点分组
	Sixth   []string `json:"sixth,omitempty" yaml:"sixth,omitempty"`     // 第6台节点分组
	Seventh []string `json:"seventh,omitempty" yaml:"seventh,omitempty"` // 第7台节点分组
	Other   []string `json:"other,omitempty" yaml:"other,omitempty"`     // 其他节点分组
}

type PluginNodeGroupConfig struct {
	AddInFirstConfig *AddInFirstConfig `json:"addInFirst,omitempty" yaml:"addInFirst,omitempty"`
}

func (plugin PluginNodeGroupConfig) Plugin(group ...*[]string) {
	if plugin.AddInFirstConfig != nil {
		plugin.AddInFirstConfig.Plugin(group...)
	}
}

// AddInFirstConfig
// 当第一台节点复合 MatchProxy、Match 时，为 第一台节点添加 Add 的 族
type AddInFirstConfig struct {
	MatchProxy string   `json:"matchProxy,omitempty" yaml:"matchProxy,omitempty"`
	Match      []string `json:"match,omitempty" yaml:"match,omitempty"`
	Add        []string `json:"add,omitempty" yaml:"add,omitempty"`
}

func (plugin AddInFirstConfig) Plugin(group ...*[]string) {
	switch plugin.MatchProxy {
	case "anyMatch":
		plugin.anyMatch(group...)
	}
}

func (plugin AddInFirstConfig) anyMatch(group ...*[]string) {
	if len(group) == 0 {
		return
	}
	var target *[]string
	// 查找到第一个复合条件的group指针
	for index, item := range group {
		sets := sets.New[string](*item...)
		if sets.HasAny(plugin.Match...) {
			target = group[index]
			break
		}
	}
	if target != nil {
		*target = append(*target, plugin.Add...)
	}
}

type GroupInfo struct {
	Initialing  []string `json:"initialing" yaml:"initialing"`
	Prefligting []string `json:"prefligting" yaml:"prefligting"`
	Installing  []string `json:"installing" yaml:"installing"`
}

// SolutionInfoList 记录k8s 版本、cri版本同solution name 和 version 的关系
type SolutionInfoList []SolutionInfo

func (list SolutionInfoList) FindBaselineVersion(kubernetesVersion, criVersion string) (string, error) {
	solution, exist := list.FindByKubernetesAndCRIVersion(kubernetesVersion, criVersion)
	if !exist {
		return "", errors.NewFromCodeWithMessage(errors.Var.ParamError, fmt.Sprintf("can not find solution by kubernetesVersion:%s,kubernetesCRIVersion:%s", kubernetesVersion, criVersion))
	}
	if solution.MergeLabels == nil {
		return "", fmt.Errorf("could not fount baseline version in k8sversion:%s,criVersion %v,merge label is empty", kubernetesVersion, criVersion)
	}
	version, exist := solution.MergeLabels["caas-infra-baseline"]
	if !exist || strings.TrimSpace(version) == "" {
		return "", fmt.Errorf("could not fount baseline version in k8sversion:%s,criVersion %v", kubernetesVersion, criVersion)
	}
	return version, nil
}

func (list SolutionInfoList) FindByKubernetesAndCRIVersion(kubernetesVersion, criVersion string) (*SolutionInfo, bool) {
	var result *SolutionInfo
	for _, item := range list {
		if item.KubernetesVersion == kubernetesVersion && item.CRIVersion == criVersion {
			result = &item
			break
		}
	}
	return result, result != nil
}

func (list SolutionInfoList) FindBySolutionNameAndSolutionVersion(solutionName, solutionVersion string) (*SolutionInfo, bool) {
	var result *SolutionInfo
	for _, item := range list {
		if item.SolutionName == solutionName && item.SolutionVersion == solutionVersion {
			result = &item
			break
		}
	}
	return result, result != nil
}

type SolutionInfo struct {
	KubernetesVersion string            `json:"kubernetesVersion" yaml:"kubernetesVersion"`
	CRIVersion        string            `json:"criVersion" yaml:"criVersion"`
	SolutionName      string            `json:"solutionName" yaml:"solutionName"`
	SolutionVersion   string            `json:"solutionVersion" yaml:"solutionVersion"`
	MergeLabels       map[string]string `json:"mergeLabels" yaml:"mergeLabels"`
	MergeAnnotations  map[string]string `json:"mergeAnnotations" yaml:"mergeAnnotations"`
	DataImport        DataImportVersion `json:"dataImportVersion" yaml:"dataImportVersion"`
}

type DataImportVersion struct {
	NodeUpDown string `json:"nodeUpDown" yaml:"nodeUpDown"`
	ResetNode  string `json:"resetNode" yaml:"resetNode"`
}

// StepGroups
// 集群创建配置文件分组相关
type StepGroups []StepGroup

func (groups StepGroups) ListStepCodeByGroupCodes(groupCodes ...string) []string {
	steps := groups.ListStepByGroupCodes(groupCodes...)
	var codes = make([]string, 0, len(steps))
	for _, step := range steps {
		step := step
		codes = append(codes, step.Code)
	}
	return codes
}

func (groups StepGroups) ListStepByGroupCodes(groupCodes ...string) Steps {
	var steps Steps
	selectGroupSet := sets.New[string](groupCodes...)
	for _, g := range groups {
		g := g
		if selectGroupSet.Has(g.Code) {
			steps = append(steps, g.Steps...)
		}
	}
	return steps
}

func (groups StepGroups) ListStepsAnyMatchLabels(labels ...string) Steps {
	var list Steps
	for _, g := range groups {
		g := g
		for _, s := range g.Steps {
			s := s
			supportOptions := sets.New[string](s.Labels...)
			if supportOptions.HasAny(labels...) {
				list = append(list, s)
			}
		}
	}
	return list
}
func (groups StepGroups) ListStepCodeAnyMatchLabels(labels ...string) []string {
	steps := groups.ListStepsAnyMatchLabels(labels...)
	var codes []string
	for _, s := range steps {
		s := s
		codes = append(codes, s.Code)
	}
	return codes
}

// StepGroup
// 集群创建配置文件分组相关

type StepGroup struct {
	Code        string `json:"code,omitempty" yaml:"code,omitempty"`
	Alias       string `json:"alias,omitempty" yaml:"alias,omitempty"`
	Description string `json:"description,omitempty" yaml:"description,omitempty"`
	Steps       Steps  `json:"steps,omitempty" yaml:"steps,omitempty"`
}

// Steps
// 集群创建配置文件分组相关
type Steps []Step

func (arr Steps) ListStepsMatchCodes(code ...string) Steps {
	codeSet := sets.New[string](code...)
	var result []Step
	for _, item := range arr {
		item := item
		if codeSet.Has(item.Code) {
			result = append(result, item)
		}
	}
	return result
}
func (arr Steps) LikeLabel(likeLabel string) Steps {
	var result []Step
	for _, item := range arr {
		item := item
		var flag bool
		for _, label := range item.Labels {
			if strings.Contains(label, likeLabel) {
				flag = true
				break
			}
		}
		if flag {
			result = append(result, item)
		}
	}
	return result
}

func (arr Steps) ListStepsAnyMatchLabels(labels ...string) Steps {
	var newArr Steps
	for _, step := range arr {
		step := step
		supportOptions := sets.New[string](step.Labels...)
		if supportOptions.HasAny(labels...) {
			newArr = append(newArr, step)
		}
	}
	return newArr
}

// Step
// 集群创建配置文件分组相关
type Step struct {
	Code        string   `json:"code,omitempty" yaml:"code,omitempty"`
	Alias       string   `json:"alias,omitempty" yaml:"alias,omitempty"`
	Description string   `json:"description,omitempty" yaml:"description,omitempty"`
	Labels      []string `json:"labels,omitempty" yaml:"labels,omitempty"`
}
