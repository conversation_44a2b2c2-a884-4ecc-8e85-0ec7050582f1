package config

import (
	"fmt"
	"os"
	"sync"

	"gopkg.in/yaml.v3"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
	"k8s.io/apimachinery/pkg/util/sets"
)

// BackGroundStepSuffix
// 当只有某些场景需要额外跑某些步骤时候，步骤标签会添加如下结尾
// 目前只有单macvlan模式需要（macvlan+kubeovn也是单macvlan模式）
var BackGroundStepSuffix = "-only"
var CreateClusterConfig *Config
var stepGroupConfigLock sync.Mutex

// ShouldReadConfig
// 启动任务是读取配置文件
func ShouldReadConfig() error {
	stepGroupConfigLock.Lock()
	defer stepGroupConfigLock.Unlock()
	if CreateClusterConfig != nil {
		return nil
	}
	bytes, err := os.ReadFile(config.ClusterCreateStepConfigPath.Value)
	if err != nil {
		return fmt.Errorf("read file %s,appear error:%v", config.ClusterCreateStepConfigPath.Value, err)
	}
	CreateClusterConfig = new(Config)
	err = yaml.Unmarshal(bytes, &CreateClusterConfig)
	if err != nil {
		return fmt.Errorf("unmarshal file %s,appear error:%v", config.ClusterCreateStepConfigPath.Value, err)
	}
	// group 检查
	groupSets := sets.New[string]()
	for _, stepGroupConfig := range CreateClusterConfig.StepGroups {
		groupName := stepGroupConfig.Code
		if groupSets.Has(groupName) {
			return fmt.Errorf("renderer ShouldReadConfig,group name %s repeate at file %s", groupName, config.ClusterCreateStepConfigPath.Value)
		} else {
			groupSets.Insert(groupName)
		}
	}
	stepOptionSets := sets.New[string]()
	for _, stepGroupConfig := range CreateClusterConfig.StepGroups {
		for _, stepConfig := range stepGroupConfig.Steps {
			for _, option := range stepConfig.Labels {
				stepOption := stepConfig.Code + "$" + string(option)
				if stepOptionSets.Has(stepOption) {
					return fmt.Errorf("renderer ShouldReadConfig,step name %s option %s repeate at file %s", stepConfig.Code, option, config.ClusterCreateStepConfigPath.Value)
				} else {
					stepOptionSets.Insert(stepOption)
				}
			}

		}

	}
	// groupInfo 检查
	// Prefligting 、 Installing 不允许为 empty

	if len(CreateClusterConfig.ClusterCreatingGroupInfo.Prefligting) == 0 {
		return fmt.Errorf("groupInfo.prefligting is not config")
	}
	if len(CreateClusterConfig.ClusterCreatingGroupInfo.Installing) == 0 {
		return fmt.Errorf("groupInfo.installing is not config")
	}
	// Prefligting 、 Installing 中的group不允许重复
	prefligtingGroups := sets.New[string](CreateClusterConfig.ClusterCreatingGroupInfo.Prefligting...)
	installingGroups := sets.New[string](CreateClusterConfig.ClusterCreatingGroupInfo.Installing...)
	for _, group := range prefligtingGroups.UnsortedList() {
		if installingGroups.Has(group) {
			return fmt.Errorf("groupInfo,group %s repeated", group)
		}
	}

	// Prefligting 、 Installing 中的信息在 step group 中必须存在
	prefligtingGroups.Insert(installingGroups.UnsortedList()...)
	for _, group := range prefligtingGroups.UnsortedList() {
		if !groupSets.Has(group) {
			return fmt.Errorf("groupInfo,group %s not exist in step group", group)
		}
	}

	return nil
}
