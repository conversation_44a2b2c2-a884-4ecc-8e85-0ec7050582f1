package config

import (
	"encoding/json"
	"fmt"
	"os"
	"sort"
	"strings"
	"testing"

	"gopkg.in/yaml.v2"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/sisyphustype"
	"k8s.io/apimachinery/pkg/util/sets"
)

var (
	allInOneSisyphusStepsJsonStr = `
[
  {
    "name": "sisyphus-system-step-prepare-execution",
    "nickname": "执行部署平台初始化步骤"
  },
  {
    "name": "precheck-check-os",
    "nickname": "预检-检查CPU架构/操作系统"
  },
  {
    "name": "precheck-check-linux-packages",
    "nickname": "预检-检查是否存在已知的冲突软件包"
  },
  {
    "name": "precheck-check-master-resources",
    "nickname": "预检-检查主控节点资源是否符合要求"
  },
  {
    "name": "precheck-check-system-nodes-resources",
    "nickname": "预检-检查系统节点资源是否符合要求"
  },
  {
    "name": "precheck-check-system-nodepool-resources",
    "nickname": "预检-检查系统资源池是否符合要求"
  },
  {
    "name": "precheck-check-system-node-vgs",
    "nickname": "预检-检查系统节点VG是否创建"
  },
  {
    "name": "edit-hostname",
    "nickname": "修改主机名"
  },
  {
    "name": "install-chrony-server",
    "nickname": "安装时间同步服务(服务端)"
  },
  {
    "name": "install-chrony-client",
    "nickname": "安装时间同步服务(客户端)"
  },
  {
    "name": "install-containerd",
    "nickname": "安装containerd容器运行时"
  },
  {
    "name": "install-docker",
    "nickname": "安装docker容器运行时"
  },
  {
    "name": "init-kubernetes-ansible-module",
    "nickname": "初始化kubernetes主控节点依赖"
  },
  {
    "name": "create-kubernetes-cluster",
    "nickname": "创建kubernetes集群"
  },
  {
    "name": "create-etcd-backup",
    "nickname": "创建etcd备份任务"
  },
  {
    "name": "join-kubernetes-node",
    "nickname": "添加kubernetes其它节点"
  },
  {
    "name": "allow-master-schedulable",
    "nickname": "允许主控节点可调度"
  },
  {
    "name": "label-chrony-server",
    "nickname": "添加内部时间同步服务器节点标签"
  },
  {
    "name": "label-sisyphus-initialized-master",
    "nickname": "添加主控节点角色标签(初始化后)"
  },
  {
    "name": "label-system-node-pool-nodes",
    "nickname": "系统资源池节点打标签"
  },
  {
    "name": "install-node-pool",
    "nickname": "安装资源池"
  },
  {
    "name": "init-system-nodepool",
    "nickname": "初始化系统资源池"
  },
  {
    "name": "install-calico",
    "nickname": "安装统一网络模型-安装calico(CNI)"
  },
  {
    "name": "install-bifrost",
    "nickname": "安装统一网络模型-安装bifrost(CNI)"
  },
  {
    "name": "install-heimdallr",
    "nickname": "安装统一网络模型-安装heimdallr"
  },
  {
    "name": "install-multus",
    "nickname": "安装统一网络模型-安装多网卡"
  },
  {
    "name": "restart-coredns-when-single-bifrost",
    "nickname": "重启CoreDNS组件"
  },
  {
    "name": "install-route-override-controller",
    "nickname": "安装统一网络模型-安装路由覆写组件"
  },
  {
    "name": "install-mystra",
    "nickname": "安装统一网络模型-安装网络隔离"
  },
  {
    "name": "install-metrics-server",
    "nickname": "安装基础监控服务"
  },
  {
    "name": "install-lvm-csi-plugin",
    "nickname": "安装LVM(本地存储管理服务)"
  },
  {
    "name": "ingress-expose-helper",
    "nickname": "安装负载均衡四层对外服务辅助"
  },
  {
    "name": "install-nginx-ingress-controller",
    "nickname": "安装默认负载均衡(NginxIngressController)"
  },
  {
    "name": "install-prometheus",
    "nickname": "安装Prometheus监控"
  },
  {
    "name": "install-monitor-components",
    "nickname": "安装监控探针套件"
  },
  {
    "name": "install-grafana",
    "nickname": "安装Grafana监控面板"
  },
  {
	 "name": "install-log-controller",
     "nickname": "安装日志采集器(log-controller)"
  },
  {
    "name": "install-elk",
    "nickname": "安装ELK日志"
  },
  {
    "name": "install-kube-eventer",
    "nickname": "安装Kubernetes事件收集器"
  },
  {
    "name": "install-pgsql-operator",
    "nickname": "安装postgresql运维控制器"
  },
  {
    "name": "install-baseline-checker",
     "nickname": "安装基线检查组件"
  },
  {
    "name": "install-gpu-manager",
    "nickname": "安装GPU控制器"
  },
  {
    "name": "install-gpu-exporter",
    "nickname": "安装GPU监控"
  },
  {
    "name": "install-stellaris-proxy",
    "nickname": "安装多集群框架代理"
  },
  {
    "name": "install-resources-aggregated-controller",
    "nickname": "安装资源关联控制器"
  },
  {
    "name": "install-application-model",
    "nickname": "安装应用模型"
  },
  {
    "name": "install-problem-isolation",
    "nickname": "安装故障隔离"
  },
  {
    "name": "install-harmonycloud-hpa",
    "nickname": "安装水平扩缩容(HPA)"
  },
  {
    "name": "install-velero",
    "nickname": "安装备份工具(velero)"
  },
  {
    "name": "install-egress",
    "nickname": "安装出口网关"
  },
  {
    "name": "install-iris",
    "nickname": "安装容器网络流量管理组件"
  },
  {
    "name": "create-kata-runtime-class",
    "nickname": "创建kata运行时"
  },
  {
    "name": "install-kata-container",
    "nickname": "安装kata-container运行时"
  },
  {
    "name": "install-mongodb",
    "nickname": "安装mongodb"
  },
  {
    "name": "install-sisyphus-cluster-manager",
    "nickname": "安装集群生命周期管理组件"
  },
  {
    "name": "install-kube-ovn-cni",
    "nickname": "安装KubeOVN网络插件"
  },
  {
    "name": "install-kubevirt",
    "nickname": "安装KubeVirt虚拟机组件"
  },
  {
    "name": "install-cdi",
    "nickname": "安装CDI组件"
  },
  {
    "name": "configure-log-collect-rules",
     "nickname": "配置日志采集规则"
  },
  {
    "name": "configure-baseline-checker-rules",
     "nickname": "配置基线检查规则"
  },
  {
    "name": "import-data-from-sisyphus",
    "nickname": "从部署平台中导入数据至目标集群"
  }
]
`
	standardNoneHASisyphusStepsJsonStr = `
[
  {
    "name": "sisyphus-system-step-prepare-execution",
    "nickname": "执行部署平台初始化步骤"
  },
  {
    "name": "precheck-check-os",
    "nickname": "预检-检查CPU架构/操作系统"
  },
  {
    "name": "precheck-check-linux-packages",
    "nickname": "预检-检查是否存在已知的冲突软件包"
  },
  {
    "name": "precheck-check-master-resources",
    "nickname": "预检-检查主控节点资源是否符合要求"
  },
  {
    "name": "precheck-check-system-nodes-resources",
    "nickname": "预检-检查系统节点资源是否符合要求"
  },
  {
    "name": "precheck-check-system-nodepool-resources",
    "nickname": "预检-检查系统资源池是否符合要求"
  },
  {
    "name": "precheck-check-system-node-vgs",
    "nickname": "预检-检查系统节点VG是否创建"
  },
  {
    "name": "edit-hostname",
    "nickname": "修改主机名"
  },
  {
    "name": "install-chrony-server",
    "nickname": "安装时间同步服务(服务端)"
  },
  {
    "name": "install-chrony-client",
    "nickname": "安装时间同步服务(客户端)"
  },
  {
    "name": "install-containerd",
    "nickname": "安装containerd容器运行时"
  },
  {
    "name": "install-docker",
    "nickname": "安装docker容器运行时"
  },
  {
    "name": "init-kubernetes-ansible-module",
    "nickname": "初始化kubernetes主控节点依赖"
  },
  {
    "name": "create-kubernetes-cluster",
    "nickname": "创建kubernetes集群"
  },
  {
    "name": "create-etcd-backup",
    "nickname": "创建etcd备份任务"
  },
  {
    "name": "join-kubernetes-node",
    "nickname": "添加kubernetes其它节点"
  },
  {
    "name": "label-chrony-server",
    "nickname": "添加内部时间同步服务器节点标签"
  },
  {
    "name": "label-sisyphus-initialized-master",
    "nickname": "添加主控节点角色标签(初始化后)"
  },
  {
    "name": "label-system-node-pool-nodes",
    "nickname": "系统资源池节点打标签"
  },
  {
    "name": "install-node-pool",
    "nickname": "安装资源池"
  },
  {
    "name": "init-system-nodepool",
    "nickname": "初始化系统资源池"
  },
  {
    "name": "install-calico",
    "nickname": "安装统一网络模型-安装calico(CNI)"
  },
  {
    "name": "install-bifrost",
    "nickname": "安装统一网络模型-安装bifrost(CNI)"
  },
  {
    "name": "install-heimdallr",
    "nickname": "安装统一网络模型-安装heimdallr"
  },
  {
    "name": "install-multus",
    "nickname": "安装统一网络模型-安装多网卡"
  },
  {
    "name": "restart-coredns-when-single-bifrost",
    "nickname": "重启CoreDNS组件"
  },
  {
    "name": "install-route-override-controller",
    "nickname": "安装统一网络模型-安装路由覆写组件"
  },
  {
    "name": "install-mystra",
    "nickname": "安装统一网络模型-安装网络隔离"
  },
  {
    "name": "install-metrics-server",
    "nickname": "安装基础监控服务"
  },
  {
    "name": "install-lvm-csi-plugin",
    "nickname": "安装LVM(本地存储管理服务)"
  },
  {
    "name": "ingress-expose-helper",
    "nickname": "安装负载均衡四层对外服务辅助"
  },
  {
    "name": "install-nginx-ingress-controller",
    "nickname": "安装默认负载均衡(NginxIngressController)"
  },
  {
    "name": "install-prometheus",
    "nickname": "安装Prometheus监控"
  },
  {
    "name": "install-monitor-components",
    "nickname": "安装监控探针套件"
  },
  {
    "name": "install-grafana",
    "nickname": "安装Grafana监控面板"
  },
  {
	 "name": "install-log-controller",
     "nickname": "安装日志采集器(log-controller)"
  },
  {
    "name": "install-elk",
    "nickname": "安装ELK日志"
  },
  {
    "name": "install-kube-eventer",
    "nickname": "安装Kubernetes事件收集器"
  },
  {
    "name": "install-pgsql-operator",
    "nickname": "安装postgresql运维控制器"
  },
  {
    "name": "install-baseline-checker",
     "nickname": "安装基线检查组件"
  },
  {
    "name": "install-gpu-manager",
    "nickname": "安装GPU控制器"
  },
  {
    "name": "install-gpu-exporter",
    "nickname": "安装GPU监控"
  },
  {
    "name": "install-stellaris-proxy",
    "nickname": "安装多集群框架代理"
  },
  {
    "name": "install-resources-aggregated-controller",
    "nickname": "安装资源关联控制器"
  },
  {
    "name": "install-application-model",
    "nickname": "安装应用模型"
  },
  {
    "name": "install-problem-isolation",
    "nickname": "安装故障隔离"
  },
  {
    "name": "install-harmonycloud-hpa",
    "nickname": "安装水平扩缩容(HPA)"
  },
  {
    "name": "install-velero",
    "nickname": "安装备份工具(velero)"
  },
  {
    "name": "install-egress",
    "nickname": "安装出口网关"
  },
  {
    "name": "install-iris",
    "nickname": "安装容器网络流量管理组件"
  },
  {
    "name": "create-kata-runtime-class",
    "nickname": "创建kata运行时"
  },
  {
    "name": "install-kata-container",
    "nickname": "安装kata-container运行时"
  },
  {
    "name": "install-mongodb",
    "nickname": "安装mongodb"
  },
  {
    "name": "install-sisyphus-cluster-manager",
    "nickname": "安装集群生命周期管理组件"
  },
  {
    "name": "install-kube-ovn-cni",
    "nickname": "安装KubeOVN网络插件"
  },
  {
    "name": "install-kubevirt",
    "nickname": "安装KubeVirt虚拟机组件"
  },
  {
    "name": "install-cdi",
    "nickname": "安装CDI组件"
  },
  {
    "name": "configure-log-collect-rules",
     "nickname": "配置日志采集规则"
  },
  {
    "name": "configure-baseline-checker-rules",
     "nickname": "配置基线检查规则"
  },
  {
    "name": "import-data-from-sisyphus",
    "nickname": "从部署平台中导入数据至目标集群"
  }
]
`
	standardHASisyphusStepsJsonStr = `
[
  {
    "name": "sisyphus-system-step-prepare-execution",
    "nickname": "执行部署平台初始化步骤"
  },
  {
    "name": "precheck-check-os",
    "nickname": "预检-检查CPU架构/操作系统"
  },
  {
    "name": "precheck-check-linux-packages",
    "nickname": "预检-检查是否存在已知的冲突软件包"
  },
  {
    "name": "precheck-check-master-resources",
    "nickname": "预检-检查主控节点资源是否符合要求"
  },
  {
    "name": "precheck-check-system-nodes-resources",
    "nickname": "预检-检查系统节点资源是否符合要求"
  },
  {
    "name": "precheck-check-system-nodepool-resources",
    "nickname": "预检-检查系统资源池是否符合要求"
  },
  {
    "name": "precheck-check-system-node-vgs",
    "nickname": "预检-检查系统节点VG是否创建"
  },
  {
    "name": "edit-hostname",
    "nickname": "修改主机名"
  },
  {
    "name": "install-chrony-server",
    "nickname": "安装时间同步服务(服务端)"
  },
  {
    "name": "install-chrony-client",
    "nickname": "安装时间同步服务(客户端)"
  },
  {
    "name": "install-haproxy",
    "nickname": "安装APIserver负载均衡(HAProxy)"
  },
  {
    "name": "install-haproxy-keepalived",
    "nickname": "安装APIserver负载均衡VIP"
  },
  {
    "name": "install-containerd",
    "nickname": "安装containerd容器运行时"
  },
  {
    "name": "install-docker",
    "nickname": "安装docker容器运行时"
  },
  {
    "name": "init-kubernetes-ansible-module",
    "nickname": "初始化kubernetes主控节点依赖"
  },
  {
    "name": "create-kubernetes-cluster",
    "nickname": "创建kubernetes集群"
  },
  {
    "name": "join-kubernetes-master",
    "nickname": "添加kubernetes主控节点"
  },
  {
    "name": "create-etcd-backup",
    "nickname": "创建etcd备份任务"
  },
  {
    "name": "join-kubernetes-node",
    "nickname": "添加kubernetes其它节点"
  },
  {
    "name": "label-chrony-server",
    "nickname": "添加内部时间同步服务器节点标签"
  },
  {
    "name": "label-sisyphus-initialized-master",
    "nickname": "添加主控节点角色标签(初始化后)"
  },
  {
    "name": "label-system-node-pool-nodes",
    "nickname": "系统资源池节点打标签"
  },
  {
    "name": "install-node-pool",
    "nickname": "安装资源池"
  },
  {
    "name": "init-system-nodepool",
    "nickname": "初始化系统资源池"
  },
  {
    "name": "install-calico",
    "nickname": "安装统一网络模型-安装calico(CNI)"
  },
  {
    "name": "install-bifrost",
    "nickname": "安装统一网络模型-安装bifrost(CNI)"
  },
  {
    "name": "install-heimdallr",
    "nickname": "安装统一网络模型-安装heimdallr"
  },
  {
    "name": "install-multus",
    "nickname": "安装统一网络模型-安装多网卡"
  },
  {
    "name": "restart-coredns-when-single-bifrost",
    "nickname": "重启CoreDNS组件"
  },
  {
    "name": "install-route-override-controller",
    "nickname": "安装统一网络模型-安装路由覆写组件"
  },
  {
    "name": "install-mystra",
    "nickname": "安装统一网络模型-安装网络隔离"
  },
  {
    "name": "install-metrics-server",
    "nickname": "安装基础监控服务"
  },
  {
    "name": "install-lvm-csi-plugin",
    "nickname": "安装LVM(本地存储管理服务)"
  },
  {
    "name": "install-ingress-keepalived",
    "nickname": "安装ingressVip"
  },
  {
    "name": "ingress-expose-helper",
    "nickname": "安装负载均衡四层对外服务辅助"
  },
  {
    "name": "install-nginx-ingress-controller",
    "nickname": "安装默认负载均衡(NginxIngressController)"
  },
  {
    "name": "install-prometheus",
    "nickname": "安装Prometheus监控"
  },
  {
    "name": "install-monitor-components",
    "nickname": "安装监控探针套件"
  },
  {
    "name": "install-grafana",
    "nickname": "安装Grafana监控面板"
  },
  {
	 "name": "install-log-controller",
     "nickname": "安装日志采集器(log-controller)"
  },
  {
    "name": "install-elk",
    "nickname": "安装ELK日志"
  },
  {
    "name": "install-kube-eventer",
    "nickname": "安装Kubernetes事件收集器"
  },
  {
    "name": "install-pgsql-operator",
    "nickname": "安装postgresql运维控制器"
  },
  {
    "name": "install-baseline-checker",
     "nickname": "安装基线检查组件"
  },
  {
    "name": "install-gpu-manager",
    "nickname": "安装GPU控制器"
  },
  {
    "name": "install-gpu-exporter",
    "nickname": "安装GPU监控"
  },
  {
    "name": "install-stellaris-proxy",
    "nickname": "安装多集群框架代理"
  },
  {
    "name": "install-resources-aggregated-controller",
    "nickname": "安装资源关联控制器"
  },
  {
    "name": "install-application-model",
    "nickname": "安装应用模型"
  },
  {
    "name": "install-problem-isolation",
    "nickname": "安装故障隔离"
  },
  {
    "name": "install-harmonycloud-hpa",
    "nickname": "安装水平扩缩容(HPA)"
  },
  {
    "name": "install-velero",
    "nickname": "安装备份工具(velero)"
  },
  {
    "name": "install-egress",
    "nickname": "安装出口网关"
  },
  {
    "name": "install-iris",
    "nickname": "安装容器网络流量管理组件"
  },
  {
    "name": "create-kata-runtime-class",
    "nickname": "创建kata运行时"
  },
  {
    "name": "install-kata-container",
    "nickname": "安装kata-container运行时"
  },
  {
    "name": "install-mongodb",
    "nickname": "安装mongodb"
  },
  {
    "name": "install-sisyphus-cluster-manager",
    "nickname": "安装集群生命周期管理组件"
  },
  {
    "name": "install-kube-ovn-cni",
    "nickname": "安装KubeOVN网络插件"
  },
  {
    "name": "install-kubevirt",
    "nickname": "安装KubeVirt虚拟机组件"
  },
  {
    "name": "install-cdi",
    "nickname": "安装CDI组件"
  },
  {
    "name": "configure-log-collect-rules",
     "nickname": "配置日志采集规则"
  },
  {
    "name": "configure-baseline-checker-rules",
     "nickname": "配置基线检查规则"
  },
  {
    "name": "import-data-from-sisyphus",
    "nickname": "从部署平台中导入数据至目标集群"
  }
]
`
	minimizeHASisyphusStepsJsonStr = `
[
  {
    "name": "sisyphus-system-step-prepare-execution",
    "nickname": "执行部署平台初始化步骤"
  },
  {
    "name": "precheck-check-os",
    "nickname": "预检-检查CPU架构/操作系统"
  },
  {
    "name": "precheck-check-linux-packages",
    "nickname": "预检-检查是否存在已知的冲突软件包"
  },
  {
    "name": "precheck-check-master-resources",
    "nickname": "预检-检查主控节点资源是否符合要求"
  },
  {
    "name": "precheck-check-system-nodes-resources",
    "nickname": "预检-检查系统节点资源是否符合要求"
  },
  {
    "name": "precheck-check-system-nodepool-resources",
    "nickname": "预检-检查系统资源池是否符合要求"
  },
  {
    "name": "precheck-check-system-node-vgs",
    "nickname": "预检-检查系统节点VG是否创建"
  },
  {
    "name": "edit-hostname",
    "nickname": "修改主机名"
  },
  {
    "name": "install-chrony-server",
    "nickname": "安装时间同步服务(服务端)"
  },
  {
    "name": "install-chrony-client",
    "nickname": "安装时间同步服务(客户端)"
  },
  {
    "name": "install-haproxy",
    "nickname": "安装APIserver负载均衡(HAProxy)"
  },
  {
    "name": "install-mix-keepalived",
    "nickname": "安装混合负载均衡"
  },
  {
    "name": "install-containerd",
    "nickname": "安装containerd容器运行时"
  },
  {
    "name": "install-docker",
    "nickname": "安装docker容器运行时"
  },
  {
    "name": "init-kubernetes-ansible-module",
    "nickname": "初始化kubernetes主控节点依赖"
  },
  {
    "name": "create-kubernetes-cluster",
    "nickname": "创建kubernetes集群"
  },
  {
    "name": "join-kubernetes-master",
    "nickname": "添加kubernetes主控节点"
  },
  {
    "name": "create-etcd-backup",
    "nickname": "创建etcd备份任务"
  },
  {
    "name": "join-kubernetes-node",
    "nickname": "添加kubernetes其它节点"
  },
  {
    "name": "allow-master-schedulable",
    "nickname": "允许主控节点可调度"
  },
  {
    "name": "label-chrony-server",
    "nickname": "添加内部时间同步服务器节点标签"
  },
  {
    "name": "label-sisyphus-initialized-master",
    "nickname": "添加主控节点角色标签(初始化后)"
  },
  {
    "name": "label-system-node-pool-nodes",
    "nickname": "系统资源池节点打标签"
  },
  {
    "name": "install-node-pool",
    "nickname": "安装资源池"
  },
  {
    "name": "init-system-nodepool",
    "nickname": "初始化系统资源池"
  },
  {
    "name": "install-calico",
    "nickname": "安装统一网络模型-安装calico(CNI)"
  },
  {
    "name": "install-bifrost",
    "nickname": "安装统一网络模型-安装bifrost(CNI)"
  },
  {
    "name": "install-heimdallr",
    "nickname": "安装统一网络模型-安装heimdallr"
  },
  {
    "name": "install-multus",
    "nickname": "安装统一网络模型-安装多网卡"
  },
  {
    "name": "restart-coredns-when-single-bifrost",
    "nickname": "重启CoreDNS组件"
  },
  {
    "name": "install-route-override-controller",
    "nickname": "安装统一网络模型-安装路由覆写组件"
  },
  {
    "name": "install-mystra",
    "nickname": "安装统一网络模型-安装网络隔离"
  },
  {
    "name": "install-metrics-server",
    "nickname": "安装基础监控服务"
  },
  {
    "name": "install-lvm-csi-plugin",
    "nickname": "安装LVM(本地存储管理服务)"
  },
  {
    "name": "ingress-expose-helper",
    "nickname": "安装负载均衡四层对外服务辅助"
  },
  {
    "name": "install-nginx-ingress-controller",
    "nickname": "安装默认负载均衡(NginxIngressController)"
  },
  {
    "name": "install-prometheus",
    "nickname": "安装Prometheus监控"
  },
  {
    "name": "install-monitor-components",
    "nickname": "安装监控探针套件"
  },
  {
    "name": "install-grafana",
    "nickname": "安装Grafana监控面板"
  },
  {
	 "name": "install-log-controller",
     "nickname": "安装日志采集器(log-controller)"
  },
  {
    "name": "install-elk",
    "nickname": "安装ELK日志"
  },
  {
    "name": "install-kube-eventer",
    "nickname": "安装Kubernetes事件收集器"
  },
  {
    "name": "install-pgsql-operator",
    "nickname": "安装postgresql运维控制器"
  },
  {
    "name": "install-baseline-checker",
     "nickname": "安装基线检查组件"
  },
  {
    "name": "install-gpu-manager",
    "nickname": "安装GPU控制器"
  },
  {
    "name": "install-gpu-exporter",
    "nickname": "安装GPU监控"
  },
  {
    "name": "install-stellaris-proxy",
    "nickname": "安装多集群框架代理"
  },
  {
    "name": "install-resources-aggregated-controller",
    "nickname": "安装资源关联控制器"
  },
  {
    "name": "install-application-model",
    "nickname": "安装应用模型"
  },
  {
    "name": "install-problem-isolation",
    "nickname": "安装故障隔离"
  },
  {
    "name": "install-harmonycloud-hpa",
    "nickname": "安装水平扩缩容(HPA)"
  },
  {
    "name": "install-velero",
    "nickname": "安装备份工具(velero)"
  },
  {
    "name": "install-egress",
    "nickname": "安装出口网关"
  },
  {
    "name": "install-iris",
    "nickname": "安装容器网络流量管理组件"
  },
  {
    "name": "create-kata-runtime-class",
    "nickname": "创建kata运行时"
  },
  {
    "name": "install-kata-container",
    "nickname": "安装kata-container运行时"
  },
  {
    "name": "install-mongodb",
    "nickname": "安装mongodb"
  },
  {
    "name": "install-sisyphus-cluster-manager",
    "nickname": "安装集群生命周期管理组件"
  },
  {
    "name": "install-kube-ovn-cni",
    "nickname": "安装KubeOVN网络插件"
  },
  {
    "name": "install-kubevirt",
    "nickname": "安装KubeVirt虚拟机组件"
  },
  {
    "name": "install-cdi",
    "nickname": "安装CDI组件"
  },
  {
    "name": "configure-log-collect-rules",
     "nickname": "配置日志采集规则"
  },
  {
    "name": "configure-baseline-checker-rules",
     "nickname": "配置基线检查规则"
  },
  {
    "name": "import-data-from-sisyphus",
    "nickname": "从部署平台中导入数据至目标集群"
  }
]
`
)

func Test_PrintSimple(t *testing.T) {

	writeFunc := func(originStr string, fileName string, varName string) error {
		var sisyphusStep []sisyphustype.SisyphusStep
		if err := json.Unmarshal([]byte(originStr), &sisyphusStep); err != nil {
			return err
		}
		filePath := fmt.Sprintf("../../../../../cluster-steps/%s", fileName)
		data, err := json.MarshalIndent(sisyphusStep, "", "  ")
		if err != nil {
			return err
		}
		varName = varName + " = `\n" + string(data) + "\n`"
		if err := os.WriteFile(filePath, []byte(varName), os.ModePerm); err != nil {
			return err
		}
		return nil
	}

	t.Run("PrintSimple", func(t *testing.T) {
		if err := writeFunc(allInOneSisyphusStepsJsonStr, "all-in-one.txt", "allInOneSisyphusStepsJsonStr"); err != nil {
			t.Fatal(err)
		}

		if err := writeFunc(standardNoneHASisyphusStepsJsonStr, "standard-none-ha.txt", "standardNoneHASisyphusStepsJsonStr"); err != nil {
			t.Fatal(err)
		}

		if err := writeFunc(standardHASisyphusStepsJsonStr, "standard-ha.txt", "standardHASisyphusStepsJsonStr"); err != nil {
			t.Fatal(err)
		}

		if err := writeFunc(minimizeHASisyphusStepsJsonStr, "min-ha.txt", "minimizeHASisyphusStepsJsonStr"); err != nil {
			t.Fatal(err)
		}
	})

}

func Test_ShouldReadConfig(t *testing.T) {
	config.ClusterCreateStepConfigPath.Value = "../../../../../cluster-create-config.yaml"
	t.Run("Test_ShouldReadConfig", func(t *testing.T) {
		if err := ShouldReadConfig(); err != nil {
			t.Errorf(err.Error())
		}
	})
	bs, _ := json.Marshal(CreateClusterConfig)
	fmt.Println(string(bs))
}

func Test_Cluster_Create_Config_Sync_Cluster_Install_AllInOne(t *testing.T) {
	// 先读取到配置
	config.ClusterCreateStepConfigPath.Value = "../../../../../cluster-create-config.yaml"
	if err := ShouldReadConfig(); err != nil {
		t.Errorf(err.Error())
		return
	}
	// 设置需要写入配置的信息
	nodeType := clustermodel.NodeConfigTypeAllInOne

	var sisyphusStep []sisyphustype.SisyphusStep
	if err := json.Unmarshal([]byte(allInOneSisyphusStepsJsonStr), &sisyphusStep); err != nil {
		t.Errorf("format error,e:%v", err)
	}
	// 写入配置
	initStep, pStep, iStep := group(sisyphusStep)
	SyncConfig("node-disk-mount-group", nodeType, initStep)
	SyncConfig("cluster-preflight-group", nodeType, pStep)
	SyncConfig("cluster-create-group", nodeType, iStep)
	ya, _ := yaml.Marshal(CreateClusterConfig)
	os.WriteFile(config.ClusterCreateStepConfigPath.Value, ya, os.ModePerm)

}

func Test_Cluster_Create_Config_Sync_Cluster_Install_StandardNoneHA(t *testing.T) {
	// 先读取到配置
	config.ClusterCreateStepConfigPath.Value = "../../../../../cluster-create-config.yaml"
	if err := ShouldReadConfig(); err != nil {
		t.Errorf(err.Error())
		return
	}
	// 设置需要写入配置的信息
	nodeType := clustermodel.NodeConfigTypeStandardNoneHA

	var sisyphusStep []sisyphustype.SisyphusStep
	if err := json.Unmarshal([]byte(standardNoneHASisyphusStepsJsonStr), &sisyphusStep); err != nil {
		t.Errorf("format error,e:%v", err)
	}
	// 写入配置
	initStep, pStep, iStep := group(sisyphusStep)
	SyncConfig("node-disk-mount-group", nodeType, initStep)
	SyncConfig("cluster-preflight-group", nodeType, pStep)
	SyncConfig("cluster-create-group", nodeType, iStep)
	ya, _ := yaml.Marshal(CreateClusterConfig)
	os.WriteFile(config.ClusterCreateStepConfigPath.Value, ya, os.ModePerm)

}

func Test_Cluster_Create_Config_Sync_Cluster_Install_StandardHA(t *testing.T) {
	// 先读取到配置
	config.ClusterCreateStepConfigPath.Value = "../../../../../cluster-create-config.yaml"
	if err := ShouldReadConfig(); err != nil {
		t.Errorf(err.Error())
		return
	}
	// 设置需要写入配置的信息
	nodeType := clustermodel.NodeConfigTypeStandardHA

	var sisyphusStep []sisyphustype.SisyphusStep
	if err := json.Unmarshal([]byte(standardHASisyphusStepsJsonStr), &sisyphusStep); err != nil {
		t.Errorf("format error,e:%v", err)
	}
	// 写入配置
	initStep, pStep, iStep := group(sisyphusStep)
	SyncConfig("node-disk-mount-group", nodeType, initStep)
	SyncConfig("cluster-preflight-group", nodeType, pStep)
	SyncConfig("cluster-create-group", nodeType, iStep)
	ya, _ := yaml.Marshal(CreateClusterConfig)
	os.WriteFile(config.ClusterCreateStepConfigPath.Value, ya, os.ModePerm)

}

func Test_Cluster_Create_Config_Sync_Cluster_Install_MinimizeHA(t *testing.T) {
	// 先读取到配置
	config.ClusterCreateStepConfigPath.Value = "../../../../../cluster-create-config.yaml"
	if err := ShouldReadConfig(); err != nil {
		t.Errorf(err.Error())
		return
	}
	// 设置需要写入配置的信息
	nodeType := clustermodel.NodeConfigTypeMinimizeHA

	var sisyphusStep []sisyphustype.SisyphusStep
	if err := json.Unmarshal([]byte(minimizeHASisyphusStepsJsonStr), &sisyphusStep); err != nil {
		t.Errorf("format error,e:%v", err)
	}
	// 写入配置
	initStep, pStep, iStep := group(sisyphusStep)
	SyncConfig("node-disk-mount-group", nodeType, initStep)
	SyncConfig("cluster-preflight-group", nodeType, pStep)
	SyncConfig("cluster-create-group", nodeType, iStep)
	ya, _ := yaml.Marshal(CreateClusterConfig)
	os.WriteFile(config.ClusterCreateStepConfigPath.Value, ya, os.ModePerm)

}

func group(sisyphusSteps []sisyphustype.SisyphusStep) (diskMountSteps, preflight, installing []sisyphustype.SisyphusStep) {
	for _, step := range sisyphusSteps {
		step := step
		if strings.HasPrefix(step.Name, "precheck") || step.Name == "sisyphus-system-step-prepare-execution" {
			preflight = append(preflight, step)
		} else if step.Name == "install-stellaris-proxy" {
			continue
		} else {
			installing = append(installing, step)
		}
	}
	diskMountSteps = append(diskMountSteps, sisyphustype.SisyphusStep{Name: "sisyphus-disk-execution", Nickname: "执行部署平台磁盘管理步骤"})
	return
}

func SyncConfig(groupName string, nodeType clustermodel.NodeConfigType, sisyphusSteps []sisyphustype.SisyphusStep) {
	// 先查找到group的index
	groupIndex := -1
	for index, g := range CreateClusterConfig.StepGroups {
		if g.Code == groupName {
			groupIndex = index
		}
	}
	if groupIndex == -1 {
		return
	}
	groupItem := CreateClusterConfig.StepGroups[groupIndex]
	removeNumber := 0
	// 先移除所有 和 nodeType 有关的Step
	for index, _ := range groupItem.Steps {
		groupStep := groupItem.Steps[index-removeNumber]
		stepSupportNodeTypes := sets.New[string](groupStep.Labels...)
		if stepSupportNodeTypes.Has(string(nodeType)) {
			stepSupportNodeTypes.Delete(string(nodeType))
		}
		if stepSupportNodeTypes.Has(string(nodeType) + "/" + string(clustermodel.NodeStorageTypeAuto)) {
			stepSupportNodeTypes.Delete(string(nodeType) + "/" + string(clustermodel.NodeStorageTypeAuto))
		}
		// 处理网络 只有选择某一网络才会跑这些步骤
		if stepSupportNodeTypes.Has(string(nodeType) + "/" + string(clustermodel.CNITypeCalico)) {
			stepSupportNodeTypes.Delete(string(nodeType) + "/" + string(clustermodel.CNITypeCalico))
		}
		if stepSupportNodeTypes.Has(string(nodeType) + "/" + string(clustermodel.CNITypeMacvlan)) {
			stepSupportNodeTypes.Delete(string(nodeType) + "/" + string(clustermodel.CNITypeMacvlan))
		}
		if stepSupportNodeTypes.Has(string(nodeType) + "/" + string(clustermodel.CNITypeKubeOVM)) {
			stepSupportNodeTypes.Delete(string(nodeType) + "/" + string(clustermodel.CNITypeKubeOVM))
		}
		// 处理磁盘自动挂载
		if stepSupportNodeTypes.Has(string(nodeType) + "/" + string(clustermodel.NodeStorageTypeAuto)) {
			stepSupportNodeTypes.Has(string(nodeType) + "/" + string(clustermodel.NodeStorageTypeAuto))
		}

		// 特殊处理 单跑macvlan 需要额外跑一个重新启动CoreDNS的步骤
		if stepSupportNodeTypes.Has(string(nodeType) + "/" + string(clustermodel.CNITypeMacvlan) + "/" + BackGroundStepSuffix) {
			stepSupportNodeTypes.Delete(string(nodeType) + "/" + string(clustermodel.CNITypeMacvlan) + "/" + BackGroundStepSuffix)
		}

		// 处理容器运行时
		if stepSupportNodeTypes.Has(string(nodeType) + "/" + string(clustermodel.CRITypeDocker)) {
			stepSupportNodeTypes.Delete(string(nodeType) + "/" + string(clustermodel.CRITypeDocker))
		}
		if stepSupportNodeTypes.Has(string(nodeType) + "/" + string(clustermodel.CRITypeContainerd)) {
			stepSupportNodeTypes.Delete(string(nodeType) + "/" + string(clustermodel.CRITypeContainerd))
		}

		resultNodeConfigTypes := stepSupportNodeTypes.UnsortedList()
		if len(resultNodeConfigTypes) == 0 {
			groupItem.Steps = append(groupItem.Steps[0:index-removeNumber], groupItem.Steps[index-removeNumber+1:]...)
			removeNumber++
		} else {
			groupItem.Steps[index-removeNumber].Labels = resultNodeConfigTypes
		}
	}
	// 向最后扩展
	for _, sisyphusStep := range sisyphusSteps {
		var labels []string
		if sisyphusStep.Name == "sisyphus-disk-execution" {
			labels = append(labels, string(nodeType)+"/"+string(clustermodel.NodeStorageTypeAuto))
		} else if sisyphusStep.Name == "install-calico" {
			labels = append(labels, string(nodeType)+"/"+string(clustermodel.CNITypeCalico))
		} else if sisyphusStep.Name == "install-bifrost" {
			labels = append(labels, string(nodeType)+"/"+string(clustermodel.CNITypeMacvlan))
		} else if sisyphusStep.Name == "install-kubeovm" {
			labels = append(labels, string(nodeType)+"/"+string(clustermodel.CNITypeKubeOVM))
		} else if sisyphusStep.Name == "install-kube-ovn-cni" {
			labels = append(labels, string(nodeType)+"/"+string(clustermodel.CNITypeKubeOVM))
		} else if sisyphusStep.Name == "install-kubevirt" {
			labels = append(labels, string(nodeType)+"/"+string(clustermodel.CNITypeKubeOVM))
		} else if sisyphusStep.Name == "install-cdi" {
			labels = append(labels, string(nodeType)+"/"+string(clustermodel.CNITypeKubeOVM))
		} else if sisyphusStep.Name == "install-containerd" {
			labels = append(labels, string(nodeType)+"/"+string(clustermodel.CRITypeContainerd))
		} else if sisyphusStep.Name == "install-docker" {
			labels = append(labels, string(nodeType)+"/"+string(clustermodel.CRITypeDocker))
		} else {
			labels = append(labels, string(nodeType))
		}
		groupItem.Steps = append(groupItem.Steps, Step{
			Code:   sisyphusStep.Name,
			Alias:  sisyphusStep.Nickname,
			Labels: labels,
		})
	}

	CreateClusterConfig.StepGroups[groupIndex] = groupItem

}

var (
	stepEn = `Import data from deployment platform to target cluster
Modify Host Name
Allow the main control node to be schedulable
Create etcd backup task
Create Kata runtime
Create Kubernetes cluster
Initialize Kubernetes master node dependencies
Initialize system resource pool
Install API Server Load Balancing (HAProxy)
Install API Server Load Balancing VIP
Install CDI components
Install ELK logs
Install GPU controller
Install GPU monitoring
Install Grafana monitoring panel
Install KubeOVN network plugin
Install KubeVirt virtual machine components
Install Kubernetes Event Collector
Install LVM (Local Storage Management Service)
Install Prometheus monitoring
Install containerd container runtime
Install docker container runtime
Install ingressVip
When installing kata container runtime
Install MongoDB
Install PostgreSQL Operations Controller
Install export gateway
Install basic monitoring services
Install baseline check component
Install backup tool (velero)
Install multi cluster framework proxy
Install container network traffic management component
Install application model
Install fault isolation
Install log controller
Install time synchronization service (client)
Install time synchronization service (server)
Install Horizontal Expansion Capacity (HPA)
Install hybrid load balancing
Install monitoring probe kit
Install Unified Network Model - Install Bifrost (CNI)
Install Unified Network Model - Install Calico (CNI)
Install Unified Network Model - Install Heimdallr
Install Unified Network Model - Install Multiple Network Cards
Install Unified Network Model - Install Network Isolation
Install Unified Network Model - Install Routing Overwrite Component
Install load balancing layer 4 external service assistance
Install resource association controller
Install resource pool
Install the cluster lifecycle management component
Install default load balancing (Nginx IngressController)
Perform deployment platform initialization steps
Add Kubernetes master node
Add other Kubernetes nodes
Add the main control node role label (after initialization)
Add internal time synchronization server node labels
Label system resource pool nodes
Configure baseline check rules
Configure log collection rules
Restart CoreDNS component
Pre check - Check CPU architecture/operating system
Pre check - Check if the resources of the main control node meet the requirements
Pre check - Check for known conflicting software packages
Pre check - Check if system node VG has been created
Pre check - Check if the system node resources meet the requirements
Pre check - Check if the system resource pool meets the requirements`
	stepHk = `從部署平臺中導入數據至目標集羣
修改主機名
允許主控節點可調度
建立etcd備份任務
創建kata運行時
創建kubernetes集羣
初始化kubernetes主控節點依賴
初始化系統資源池
安裝APIserver負載均衡（HAProxy）
安裝APIserver負載均衡VIP
安裝CDI組件
安裝ELK日誌
安裝GPU控制器
安裝GPU監控
安裝Grafana監控面板
安裝KubeOVN網絡挿件
安裝KubeVirt虛擬機器組件
安裝Kubernetes事件收集器
安裝LVM（本地存儲管理服務）
安裝Prometheus監控
安裝containerd容器運行時
安裝docker容器運行時
安裝ingressVip
安裝kata-container運行時
安裝mongodb
安裝postgresql運維控制器
安裝出口閘道
安裝基礎監控服務
安裝基線檢查組件
安裝備份工具（velero）
安裝多集羣框架代理
安裝容器網路流量管理組件
安裝應用模型
安裝故障隔離
安裝日誌採集器（log-controller）
安裝時間同步服務（用戶端）
安裝時間同步服務（服務端）
安裝水准擴縮容（HPA）
安裝混合負載均衡
安裝監控探針套件
安裝統一網絡模型-安裝bifrost（CNI）
安裝統一網絡模型-安裝calico（CNI）
安裝統一網絡模型-安裝heimdallr
安裝統一網絡模型-安裝多網卡
安裝統一網絡模型-安裝網絡隔離
安裝統一網絡模型-安裝路由覆寫組件
安裝負載均衡四層對外服務輔助
安裝資源關聯控制器
安裝資源池
安裝集羣生命週期管理組件
安裝默認負載均衡（NginxIngressController）
執行部署平臺初始化步驟
添加kubernetes主控節點
添加kubernetes其它節點
添加主控節點角色標籤（初始化後）
添加內部時間同步服務器節點標籤
系統資源池節點打標籤
配置基线检查规则
配寘日誌採集規則
重啓CoreDNS組件
預檢-檢查CPU架構/作業系統
預檢-檢查主控節點資源是否符合要求
預檢-檢查是否存在已知的衝突套裝軟體
預檢-檢查系統節點VG是否創建
預檢-檢查系統節點資源是否符合要求
預檢-檢查系統資源池是否符合要求`
)

func Test_Sort_Name_And_NickName(t *testing.T) {

	sisyphusStepUnMarshalFunc := func(str string) ([]sisyphustype.SisyphusStep, error) {
		var sisyphusStep []sisyphustype.SisyphusStep
		err := json.Unmarshal([]byte(str), &sisyphusStep)
		return sisyphusStep, err
	}
	var keySet = map[string]string{}
	if steps, err := sisyphusStepUnMarshalFunc(allInOneSisyphusStepsJsonStr); err != nil {
		t.Error(err)
		return
	} else {
		for _, step := range steps {
			keySet[step.Name] = step.Nickname
		}
	}
	if steps, err := sisyphusStepUnMarshalFunc(standardNoneHASisyphusStepsJsonStr); err != nil {
		t.Error(err)
		return
	} else {
		for _, step := range steps {
			keySet[step.Name] = step.Nickname
		}
	}
	if steps, err := sisyphusStepUnMarshalFunc(standardHASisyphusStepsJsonStr); err != nil {
		t.Error(err)
		return
	} else {
		for _, step := range steps {
			keySet[step.Name] = step.Nickname
		}
	}
	if steps, err := sisyphusStepUnMarshalFunc(minimizeHASisyphusStepsJsonStr); err != nil {
		t.Error(err)
		return
	} else {
		for _, step := range steps {
			keySet[step.Name] = step.Nickname
		}
	}
	keys := []string{}
	values := []string{}
	for k, v := range keySet {
		keys = append(keys, k)
		values = append(values, v)
	}
	sort.Strings(keys)
	sort.Strings(values)
	for _, v := range values {
		fmt.Println(v)
	}
	printStepSql(keys, values, keySet, "en-US", stepEn)
	printStepSql(keys, values, keySet, "zh-HK", stepHk)
}

func printStepSql(keys, values []string, keySetMap map[string]string, language, translateStr string) {
	fmt.Println("-- ", language)
	translate := strings.Split(translateStr, "\n")
	if len(keys) != len(values) || len(keys) != len(translate) {
		panic("length not eq")
	}
	valueIndexMap := map[string]int{}
	for index, value := range values {
		valueIndexMap[value] = index
	}

	for _, key := range keys {
		keyValue := keySetMap[key]
		valueIndex := valueIndexMap[keyValue]
		tVal := translate[valueIndex]
		fmt.Println(fmt.Sprintf("insert into sys_resource_translate_config(`group_name`,`unique_value`,`language_code`,`property`,`translation`) values('olympus-cluster-create-step','%s','%s','name','%s') ON DUPLICATE KEY update `translation`='%s';", key, language, tVal, tVal))
	}
}
