package task

import (
	"context"
	"fmt"
	"strconv"
	"strings"

	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/addon"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	clusterconfig "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/cluster/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/cluster/render/internal/sisyphus_solutionparam"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/installerutil"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/meta"
	noderesettask "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/nodereset/render/task"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/uuid"
	"k8s.io/apimachinery/pkg/util/sets"
)

// nodeInitialTask
// 处理节点初始化的task
type nodeInitialTask struct {
	// 读取下层集群的西西弗斯地址
	sisyphusConfigHandler addon.SisyphusConfigHandler
}

func NewNodeInitialTask() Intf {
	return nodeInitialTask{
		sisyphusConfigHandler: addon.NewSisyphusConfigHandler(),
	}
}

// Name
// 获取task 名称
func (nodeInitialTask) Name() string {
	return constants.TaskNameForInitialNode
}

// Description
// 获取task 描述
func (nodeInitialTask) Description() string {
	return constants.TaskDescriptionForInitialNode
}

// Kind
// 获取当前task 处理器的类型
func (nodeInitialTask) Kind() installerv1alpha1.InstallerTaskKind {
	return installerv1alpha1.InstallerTaskKindSisyphusNodeInitial
}

// should
// 用于判断渲染taskSpec时是否需要提供该task
// if 需要提供
//
//	if 以存在 -> 修改
//	else     -> 新增
//
// else
//
//	if 以存在 -> 删除
//	else     -> nothing to do
func (nodeInitialTask) Should(installer *installerv1alpha1.Installer, request clustermodel.CreateRequest) bool {
	return true
}

// CreateRequestRenderToInstallerTask
// 将 request 内容渲染到task
func (handler nodeInitialTask) CreateRequestRenderToInstallerTask(existTasks []installerv1alpha1.InstallerTaskSpec, task *installerv1alpha1.InstallerTaskSpec, status *installerv1alpha1.InstallerTaskStatus, request clustermodel.CreateRequest) error {
	sisyphusConfig, err := handler.sisyphusConfigHandler.GetSisyphusURL(context.Background(), "")
	if err != nil {
		return err
	}
	return installerutil.RenderTaskForNodeInitial(task, sisyphusConfig, createRequestGetNodeRequest(request))
}

// InstallerTaskRenderToResponse
// 将 installer task 的内容渲染到response
func (nodeInitialTask) InstallerTaskRenderToResponse(response *clustermodel.Response, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	// 设置初始状态为预检中
	if response.Status == "" {
		if taskStatus != nil {
			switch taskStatus.Phase {
			case installerv1alpha1.StatusPhasePending:
				fallthrough
			case installerv1alpha1.StatusPhaseRunning:
				response.Status = clustermodel.StatusTypeCreateInitialing
			case installerv1alpha1.StatusPhaseSuccess:
				response.Status = "" // 如果该步骤success 则设置状态为 "" 表示该步骤已完成
			case installerv1alpha1.StatusPhaseFailed:
				response.Status = clustermodel.StatusTypeCreateInitialFailed
			default:
				response.Status = clustermodel.StatusTypeUnKnow
			}
		} else {
			response.Status = clustermodel.StatusTypeCreateInitialing
		}
	}

	// and nothing to do
	return nil
}

// InstallerTaskRenderToCreateStateResponse
// 将 installer task 的内容渲染到create status response
func (nodeInitialTask) InstallerTaskRenderToCreateStateResponse(statusResponse *clustermodel.CreateStatusResponse, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	// 查找节点初始化的Processing
	group, exist := statusResponse.Processing.FindByCode(clusterconfig.CreateClusterConfig.InitialGroupInfo.GroupCode)
	if !exist {
		group = &clustermodel.CreateProcessResponse{
			Code:   clusterconfig.CreateClusterConfig.InitialGroupInfo.GroupCode,
			Name:   clusterconfig.CreateClusterConfig.InitialGroupInfo.GroupAlias,
			Status: clustermodel.ProcessStatusWaiting,
		}
		statusResponse.Processing = append(statusResponse.Processing, *group)
		group, _ = statusResponse.Processing.FindByCode(clusterconfig.CreateClusterConfig.InitialGroupInfo.GroupCode)
	}

	step := clustermodel.CreateProcessStepResponse{
		Code:   clusterconfig.CreateClusterConfig.InitialGroupInfo.NodeInitialCode,
		Name:   clusterconfig.CreateClusterConfig.InitialGroupInfo.NodeInitialAlias,
		Status: clustermodel.ProcessStatusWaiting,
	}
	if taskStatus != nil {
		step.Status = clustermodel.MustParseByInstallerStatus(taskStatus.Phase)
		if taskStatus.Phase == installerv1alpha1.StatusPhaseFailed {
			step.ErrorType = &taskStatus.Reason
			step.ErrorMessage = &taskStatus.Message
		}
	}
	group.Steps = append(group.Steps, step)
	group.Status = installerutil.GetStepsStatus(group.Steps)
	return nil
}

// InstallerTaskRenderToCreateResponse
// 将 installer task 的内容渲染到create response
func (nodeInitialTask) InstallerTaskRenderToCreateResponse(createResponse *clustermodel.CreateResponse, taskSpec installerv1alpha1.InstallerTaskSpec) error {
	if taskSpec.SisyphusNodeInitial == nil || taskSpec.SisyphusNodeInitial.Param == nil {
		return nil
	}
	// 目前response 中存储的node 全部都为default node 参考在annotation 写入步骤
	defaultNodeIp := sets.New[string]()
	if createResponse.NodeConfigs.Nodes != nil {
		for _, node := range createResponse.NodeConfigs.Nodes {
			ip := node.Ip
			defaultNodeIp.Insert(ip)
		}
	}

	// 写入节点信息
	var nodeList clustermodel.CreateNodeConfigListResponse
	for _, initialNode := range taskSpec.SisyphusNodeInitial.Param {
		port, _ := strconv.Atoi(initialNode.Port)
		authParamStruct := clustermodel.NodeAuthUsernameAndPasswordParamRequest{
			Username:              initialNode.Username,
			Password:              initialNode.Password,
			AuthorizationPassword: initialNode.SudoPassword,
		}
		var authParam map[string]interface{}
		_ = utils.BeanCopy(authParamStruct, &authParam)
		authResponse := clustermodel.NodeAuthResponse{
			AuthType: clustermodel.UserNameAndPasswordAuthType,
			Param:    authParam,
		}

		node := clustermodel.CreateNodeConfigResponse{
			// Role SystemNode SupportGpu 在分组中进行设置
			Ip:        initialNode.IP,
			Port:      port,
			Auth:      &authResponse,
			IsDefault: defaultNodeIp.Has(initialNode.IP),
		}
		nodeList = append(nodeList, node)
	}

	// 设置平台意义上的系统节点
	var systemNodeList clustermodel.CreateNodeConfigListResponse
	switch createResponse.NodeConfigs.NodeConfigType {
	case clustermodel.NodeConfigTypeAllInOne:
		systemNodeList = nodeList[0:1]
	case clustermodel.NodeConfigTypeMinimizeHA:
		systemNodeList = nodeList[0:3]
	case clustermodel.NodeConfigTypeStandardNoneHA:
		systemNodeList = nodeList[1:4]
	case clustermodel.NodeConfigTypeStandardHA:
		systemNodeList = nodeList[3:7]
	}

	for index, _ := range systemNodeList {
		systemNodeList[index].SystemNode = true
	}
	// 写回 response
	createResponse.NodeConfigs.Nodes = nodeList
	return nil
}

// sisyphusNodeResetSolutionApplyTask
// 处理节点重置表单信息提交
type sisyphusNodeResetSolutionApplyTask struct {
	nodeResetIntf noderesettask.Intf
	// 读取下层集群的西西弗斯地址
	sisyphusConfigHandler addon.SisyphusConfigHandler
}

func NewSisyphusNodeResetSolutionApplyTask(uuid uuid.UUIDIntf) Intf {
	return sisyphusNodeResetSolutionApplyTask{
		nodeResetIntf:         noderesettask.NewSisyphusNodeResetSolutionApplyTask(uuid),
		sisyphusConfigHandler: addon.NewSisyphusConfigHandler(),
	}
}

func (task sisyphusNodeResetSolutionApplyTask) Name() string {
	return task.nodeResetIntf.Name()
}

func (task sisyphusNodeResetSolutionApplyTask) Description() string {
	return task.nodeResetIntf.Description()
}

func (task sisyphusNodeResetSolutionApplyTask) Kind() installerv1alpha1.InstallerTaskKind {
	return task.nodeResetIntf.Kind()
}

// should
// 用于判断渲染taskSpec时是否需要提供该task
// if 需要提供
//
//	if 以存在 -> 修改
//	else     -> 新增
//
// else
//
//	if 以存在 -> 删除
//	else     -> nothing to do
func (task sisyphusNodeResetSolutionApplyTask) Should(installer *installerv1alpha1.Installer, request clustermodel.CreateRequest) bool {
	// 节点重置 - 表单提交 需要进行判断
	return task.nodeResetIntf.Should(installer, request.StartFromFailed, request.Reset)
}

// CreateRequestRenderToInstallerTask
// 将 create request 内容渲染到task
func (h sisyphusNodeResetSolutionApplyTask) CreateRequestRenderToInstallerTask(existTasks []installerv1alpha1.InstallerTaskSpec, task *installerv1alpha1.InstallerTaskSpec, status *installerv1alpha1.InstallerTaskStatus, request clustermodel.CreateRequest) error {
	baseLindVersion, err := clusterconfig.CreateClusterConfig.SolutionInfos.FindBaselineVersion(request.KubernetesVersion, request.KubernetesCRIVersion)
	if err != nil {
		return err
	}
	sisyphusConfig, err := h.sisyphusConfigHandler.GetSisyphusURL(context.Background(), "")
	if err != nil {
		return err
	}
	return h.nodeResetIntf.CreateRequestRenderToInstallerTask(existTasks, task, status, node.NewNodeResetRequest(createRequestGetNodeRequest(request), request.StartFromFailed, request.Reset, baseLindVersion, request.CRI), sisyphusConfig)
}

// InstallerTaskRenderToResponse
// 将 installer task 的内容渲染到response
func (sisyphusNodeResetSolutionApplyTask) InstallerTaskRenderToResponse(response *clustermodel.Response, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	// 设置初始状态为节点初始化中
	if response.Status == "" {
		if taskStatus != nil {
			switch taskStatus.Phase {
			case installerv1alpha1.StatusPhasePending:
				fallthrough
			case installerv1alpha1.StatusPhaseRunning:
				response.Status = clustermodel.StatusTypeCreateInitialing
			case installerv1alpha1.StatusPhaseSuccess:
				response.Status = "" // 如果该步骤success 则设置状态为 "" 表示该步骤已完成
			case installerv1alpha1.StatusPhaseFailed:
				response.Status = clustermodel.StatusTypeCreateInitialFailed
			default:
				response.Status = clustermodel.StatusTypeUnKnow
			}
		} else {
			response.Status = clustermodel.StatusTypeCreateInitialing
		}
	}
	return nil
}

// InstallerTaskRenderToCreateStateResponse
// 将 installer task 的内容渲染到create status response
func (h sisyphusNodeResetSolutionApplyTask) InstallerTaskRenderToCreateStateResponse(statusResponse *clustermodel.CreateStatusResponse, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	return h.nodeResetIntf.InstallerTaskRenderToCreateStateResponse(&statusResponse.Processing, clusterconfig.CreateClusterConfig.InitialGroupInfo.GroupCode, clusterconfig.CreateClusterConfig.InitialGroupInfo.GroupAlias, statusResponse.CRI, taskSpec, taskStatus)
}

// InstallerTaskRenderToCreateResponse
// 将 installer task 的内容渲染到create response
func (sisyphusNodeResetSolutionApplyTask) InstallerTaskRenderToCreateResponse(createResponse *clustermodel.CreateResponse, taskSpec installerv1alpha1.InstallerTaskSpec) error {
	return nil
}

// sisyphusNodeResetSolutionExecTask
// 处理节点重置执行步骤
type sisyphusNodeResetSolutionExecTask struct {
	nodeResetIntf noderesettask.Intf
	// 读取下层集群的西西弗斯地址
	sisyphusConfigHandler addon.SisyphusConfigHandler
}

func NewSisyphusNodeResetSolutionExecTask() Intf {
	return sisyphusNodeResetSolutionExecTask{
		nodeResetIntf:         noderesettask.NewSisyphusNodeResetSolutionExecTask(),
		sisyphusConfigHandler: addon.NewSisyphusConfigHandler(),
	}
}

func (task sisyphusNodeResetSolutionExecTask) Name() string {
	return task.nodeResetIntf.Name()
}

func (task sisyphusNodeResetSolutionExecTask) Description() string {
	return task.nodeResetIntf.Description()
}

func (task sisyphusNodeResetSolutionExecTask) Kind() installerv1alpha1.InstallerTaskKind {
	return task.nodeResetIntf.Kind()
}

// should
// 用于判断渲染taskSpec时是否需要提供该task
// if 需要提供
//
//	if 以存在 -> 修改
//	else     -> 新增
//
// else
//
//	if 以存在 -> 删除
//	else     -> nothing to do
func (task sisyphusNodeResetSolutionExecTask) Should(installer *installerv1alpha1.Installer, request clustermodel.CreateRequest) bool {
	// 节点重置 - 表单提交 需要进行判断
	return task.nodeResetIntf.Should(installer, request.StartFromFailed, request.Reset)
}

// CreateRequestRenderToInstallerTask
// 将 create request 内容渲染到task
func (h sisyphusNodeResetSolutionExecTask) CreateRequestRenderToInstallerTask(existTasks []installerv1alpha1.InstallerTaskSpec, task *installerv1alpha1.InstallerTaskSpec, status *installerv1alpha1.InstallerTaskStatus, request clustermodel.CreateRequest) error {
	baseLindVersion, err := clusterconfig.CreateClusterConfig.SolutionInfos.FindBaselineVersion(request.KubernetesVersion, request.KubernetesCRIVersion)
	if err != nil {
		return err
	}
	sisyphusConfig, err := h.sisyphusConfigHandler.GetSisyphusURL(context.Background(), "")
	if err != nil {
		return err
	}

	return h.nodeResetIntf.CreateRequestRenderToInstallerTask(existTasks, task, status, node.NewNodeResetRequest(createRequestGetNodeRequest(request), request.StartFromFailed, request.Reset, baseLindVersion, request.CRI), sisyphusConfig)
}

// InstallerTaskRenderToResponse
// 将 installer task 的内容渲染到response
func (sisyphusNodeResetSolutionExecTask) InstallerTaskRenderToResponse(response *clustermodel.Response, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	// 设置初始状态为节点初始化中
	if response.Status == "" {
		if taskStatus != nil {
			switch taskStatus.Phase {
			case installerv1alpha1.StatusPhasePending:
				fallthrough
			case installerv1alpha1.StatusPhaseRunning:
				response.Status = clustermodel.StatusTypeCreateInitialing
			case installerv1alpha1.StatusPhaseSuccess:
				response.Status = "" // 如果该步骤success 则设置状态为 "" 表示该步骤已完成
			case installerv1alpha1.StatusPhaseFailed:
				response.Status = clustermodel.StatusTypeCreateInitialFailed
			default:
				response.Status = clustermodel.StatusTypeUnKnow
			}
		} else {
			response.Status = clustermodel.StatusTypeCreateInitialing
		}
	}
	return nil
}

// InstallerTaskRenderToCreateStateResponse
// 将 installer task 的内容渲染到create status response
func (h sisyphusNodeResetSolutionExecTask) InstallerTaskRenderToCreateStateResponse(statusResponse *clustermodel.CreateStatusResponse, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	return h.nodeResetIntf.InstallerTaskRenderToCreateStateResponse(&statusResponse.Processing, clusterconfig.CreateClusterConfig.InitialGroupInfo.GroupCode, clusterconfig.CreateClusterConfig.InitialGroupInfo.GroupAlias, statusResponse.CRI, taskSpec, taskStatus)
}

// InstallerTaskRenderToCreateResponse
// 将 installer task 的内容渲染到create response
func (sisyphusNodeResetSolutionExecTask) InstallerTaskRenderToCreateResponse(createResponse *clustermodel.CreateResponse, taskSpec installerv1alpha1.InstallerTaskSpec) error {
	return nil
}

// sisyphusSolutionApplyTask
// 处理西西弗斯的 任务创建的task
type sisyphusSolutionApplyTask struct {
	uuid     uuid.UUIDIntf
	paramMgr sisyphus_solutionparam.ParamMgr
	// 读取下层集群的西西弗斯地址
	sisyphusConfigHandler addon.SisyphusConfigHandler
}

func NewSisyphusSolutionApplyTask(uuid uuid.UUIDIntf) Intf {
	return sisyphusSolutionApplyTask{
		uuid:                  uuid,
		paramMgr:              sisyphus_solutionparam.NewParamMgr(),
		sisyphusConfigHandler: addon.NewSisyphusConfigHandler(),
	}
}

// Name
// 获取task 名称
func (sisyphusSolutionApplyTask) Name() string {
	return constants.TaskNameForSisyphusSolutionApply
}

// Description
// 获取task 描述
func (sisyphusSolutionApplyTask) Description() string {
	return constants.TaskDescriptionForSisyphusSolutionApply
}

// Kind
// 获取当前task 处理器的类型
func (sisyphusSolutionApplyTask) Kind() installerv1alpha1.InstallerTaskKind {
	return installerv1alpha1.InstallerTaskKindSisyphusSolutionApply
}

// should
// 用于判断渲染taskSpec时是否需要提供该task
// if 需要提供
//
//	if 以存在 -> 修改
//	else     -> 新增
//
// else
//
//	if 以存在 -> 删除
//	else     -> nothing to do
func (sisyphusSolutionApplyTask) Should(installer *installerv1alpha1.Installer, request clustermodel.CreateRequest) bool {
	return true
}

// CreateRequestRenderToInstallerTask
// 将 request 内容渲染到task
func (applyTask sisyphusSolutionApplyTask) CreateRequestRenderToInstallerTask(existTasks []installerv1alpha1.InstallerTaskSpec, task *installerv1alpha1.InstallerTaskSpec, status *installerv1alpha1.InstallerTaskStatus, request clustermodel.CreateRequest) error {
	sisyphusConfig, err := applyTask.sisyphusConfigHandler.GetSisyphusURL(context.Background(), "")
	if err != nil {
		return err
	}

	return installerutil.RenderTaskForSolutionApply(applyTask.uuid, sisyphusConfig, task, func(solutionName string) (string, error) {
		return applyTask.paramMgr.CreateRequestAsParam(request, solutionName)
	})
}

// InstallerTaskRenderToResponse
// 将 installer task 的内容渲染到response
func (applyTask sisyphusSolutionApplyTask) InstallerTaskRenderToResponse(response *clustermodel.Response, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	// 如果上一个步骤没有失败（即状态还是预检中 则设置本步骤状态）
	// 设置初始状态为节点初始化中
	if response.Status == "" {
		if taskStatus != nil {
			switch taskStatus.Phase {
			case installerv1alpha1.StatusPhasePending:
				fallthrough
			case installerv1alpha1.StatusPhaseRunning:
				response.Status = clustermodel.StatusTypeCreateInitialing
			case installerv1alpha1.StatusPhaseSuccess:
				response.Status = "" // 如果该步骤success 则设置状态为 "" 表示该步骤已完成
			case installerv1alpha1.StatusPhaseFailed:
				response.Status = clustermodel.StatusTypeCreateInitialFailed
			default:
				response.Status = clustermodel.StatusTypeUnKnow
			}
		} else {
			response.Status = clustermodel.StatusTypeCreateInitialing
		}
	}

	// 读取k8s 版本
	if taskSpec.SisyphusSolutionApply == nil || taskSpec.SisyphusSolutionApply.Param == "" {
		return nil
	}
	if err := applyTask.paramMgr.ParamRenderToResponse(response, taskSpec.SisyphusSolutionApply.Param); err != nil {
		return err
	}
	if taskSpec.SisyphusSolutionApply == nil || taskSpec.SisyphusSolutionApply.Param == "" {
		return nil
	}

	// nothing to do
	return nil
}

// InstallerTaskRenderToCreateStateResponse
// 将 installer task 的内容渲染到create status response
func (applyTask sisyphusSolutionApplyTask) InstallerTaskRenderToCreateStateResponse(statusResponse *clustermodel.CreateStatusResponse, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	if taskSpec.SisyphusSolutionApply == nil || taskSpec.SisyphusSolutionApply.Param == "" {
		return nil
	}
	if err := applyTask.paramMgr.ParamRenderToCreateStatusResponse(statusResponse, taskSpec.SisyphusSolutionApply.Param); err != nil {
		return err
	}
	// 查找节点初始化的Processing
	group, exist := statusResponse.Processing.FindByCode(clusterconfig.CreateClusterConfig.InitialGroupInfo.GroupCode)
	if !exist {
		group = &clustermodel.CreateProcessResponse{
			Code:   clusterconfig.CreateClusterConfig.InitialGroupInfo.GroupCode,
			Name:   clusterconfig.CreateClusterConfig.InitialGroupInfo.GroupAlias,
			Status: clustermodel.ProcessStatusWaiting,
		}
		statusResponse.Processing = append(statusResponse.Processing, *group)
		group, _ = statusResponse.Processing.FindByCode(clusterconfig.CreateClusterConfig.InitialGroupInfo.GroupCode)
	}

	step := clustermodel.CreateProcessStepResponse{
		Code:   clusterconfig.CreateClusterConfig.InitialGroupInfo.SisyphusSolutionApplyCode,
		Name:   clusterconfig.CreateClusterConfig.InitialGroupInfo.SisyphusSolutionApplyAlias,
		Status: clustermodel.ProcessStatusWaiting,
	}
	if taskStatus != nil {
		step.Status = clustermodel.MustParseByInstallerStatus(taskStatus.Phase)
		if taskStatus.Phase == installerv1alpha1.StatusPhaseFailed {
			step.ErrorType = &taskStatus.Reason
			step.ErrorMessage = &taskStatus.Message
		}
	}
	group.Steps = append(group.Steps, step)
	group.Status = installerutil.GetStepsStatus(group.Steps)
	return nil
}

// InstallerTaskRenderToCreateResponse
// 将 installer task 的内容渲染到create response
func (applyTask sisyphusSolutionApplyTask) InstallerTaskRenderToCreateResponse(createResponse *clustermodel.CreateResponse, taskSpec installerv1alpha1.InstallerTaskSpec) error {
	if taskSpec.SisyphusSolutionApply == nil || taskSpec.SisyphusSolutionApply.Param == "" {
		return nil
	}
	if err := applyTask.paramMgr.ParamRenderToCreateResponse(createResponse, taskSpec.SisyphusSolutionApply.Param); err != nil {
		return err
	}
	return nil
}

// sisyphusSolutionApplyTask
// 处理西西弗斯的 任务执行的task
type sisyphusSolutionExecTask struct {
	// 读取下层集群的西西弗斯地址
	sisyphusConfigHandler addon.SisyphusConfigHandler
}

func NewSisyphusSolutionExecTask() Intf {
	return sisyphusSolutionExecTask{
		sisyphusConfigHandler: addon.NewSisyphusConfigHandler(),
	}
}

// Name
// 获取task 名称
func (sisyphusSolutionExecTask) Name() string {
	return constants.TaskNameForSisyphusSolutionExec
}

// Description
// 获取task 描述
func (sisyphusSolutionExecTask) Description() string {
	return constants.TaskDescriptionForSisyphusSolutionExec
}

// Kind
// 获取当前task 处理器的类型
func (sisyphusSolutionExecTask) Kind() installerv1alpha1.InstallerTaskKind {
	return installerv1alpha1.InstallerTaskKindSisyphusSolutionExec
}

// should
// 用于判断渲染taskSpec时是否需要提供该task
// if 需要提供
//
//	if 以存在 -> 修改
//	else     -> 新增
//
// else
//
//	if 以存在 -> 删除
//	else     -> nothing to do
func (sisyphusSolutionExecTask) Should(installer *installerv1alpha1.Installer, request clustermodel.CreateRequest) bool {
	return true
}

// CreateRequestRenderToInstallerTask
// 将 request 内容渲染到task
func (handler sisyphusSolutionExecTask) CreateRequestRenderToInstallerTask(existTasks []installerv1alpha1.InstallerTaskSpec, task *installerv1alpha1.InstallerTaskSpec, status *installerv1alpha1.InstallerTaskStatus, request clustermodel.CreateRequest) error {
	// 获取exec 对应的solution 的名称
	solutionName, err := installerutil.GetSisyphusSolutionApplyTaskSolutionName(existTasks, constants.TaskNameForSisyphusSolutionApply)
	if err != nil {
		return err
	}

	// 获取节点上线的期望步骤
	var expectSteps = listCreateClusterExpectSteps(request.NodeConfigs.NodeConfigType, request.NetworkConfigs.CNITypeList(), request.NodeConfigs.Nodes.HasAutoStorageNode(), request.CRI)

	sisyphusConfig, err := handler.sisyphusConfigHandler.GetSisyphusURL(context.Background(), "")
	if err != nil {
		return err
	}

	return installerutil.RenderTaskForSolutionExec(task, solutionName, expectSteps, request.StartFromFailed, request.Reset, status, sisyphusConfig)
}

// InstallerTaskRenderToResponse
// 将 installer task 的内容渲染到response
func (sisyphusSolutionExecTask) InstallerTaskRenderToResponse(response *clustermodel.Response, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	// 如果前面步骤未设置状态 则设置状态
	if response.Status == "" {
		response.Status = getClusterStatusFromInstallerTaskStatus(response.NodeConfigType, response.CNITypes, response.HasAutoStorageNode, taskStatus, response.CRI)
	}
	return nil
}

// InstallerTaskRenderToCreateStateResponse
// 将 installer task 的内容渲染到create status response
func (sisyphusSolutionExecTask) InstallerTaskRenderToCreateStateResponse(statusResponse *clustermodel.CreateStatusResponse, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {

	// 定义一个集合 存储spec中的所有 step
	specParamsSet := sets.New[string]()
	if len(taskSpec.SisyphusSolutionExec.Param) != 0 {
		specParamsSet.Insert(taskSpec.SisyphusSolutionExec.Param...)
	}

	// 获取期望的步骤列表
	var expectProcessingList = make(clustermodel.CreateProcessListResponse, 0)
	var steps = listCreateClusterExpectSteps(statusResponse.NodeConfigType, statusResponse.CNITypes, statusResponse.HasAutoStorageNode, statusResponse.CRI)

	// 构建期望的组列表
	var expectGroupSet = sets.New[string]()
	if statusResponse.HasAutoStorageNode {
		expectGroupSet.Insert(clusterconfig.CreateClusterConfig.ClusterCreatingGroupInfo.Initialing...)
	}
	expectGroupSet.Insert(clusterconfig.CreateClusterConfig.ClusterCreatingGroupInfo.Prefligting...)
	expectGroupSet.Insert(clusterconfig.CreateClusterConfig.ClusterCreatingGroupInfo.Installing...)

	for _, group := range clusterconfig.CreateClusterConfig.StepGroups {
		// 先判断这个组是否需要
		if !expectGroupSet.Has(group.Code) {
			continue
		}

		group := group
		processing := clustermodel.CreateProcessResponse{
			Code:        group.Code,
			Name:        group.Alias,
			Description: group.Description,
			Status:      clustermodel.ProcessStatusWaiting,
		}
		var processingSteps clustermodel.CreateProcessStepListResponse
		for _, step := range group.Steps.LikeLabel(string(statusResponse.NodeConfigType)).ListStepsMatchCodes(steps...) {
			step := step
			var status = clustermodel.ProcessStatusWaiting
			if !specParamsSet.Has(step.Code) {
				status = clustermodel.ProcessStatusSuccess
			}
			processingSteps = append(processingSteps, clustermodel.CreateProcessStepResponse{
				Code:        step.Code,
				Name:        step.Alias,
				Description: step.Description,
				Status:      status,
			})
		}
		processing.Steps = processingSteps
		expectProcessingList = append(expectProcessingList, processing)
	}

	// 设置step状态
	if taskStatus != nil && len(taskStatus.Steps) != 0 {
		// 构建status step map 便于查询
		statusStepMap := make(map[string]installerv1alpha1.InstallerStatusInfo, len(taskStatus.Steps))
		// 设置step 状态
		for _, step := range taskStatus.Steps {
			name := step.StepName
			statusInfo := step.InstallerStatusInfo
			statusStepMap[name] = statusInfo
		}
		// 将状态写入 expectProcessingList
		for index, _ := range expectProcessingList {
			expectProcessing := &expectProcessingList[index]
			// 先设置每个step 的状态
			for index1, _ := range expectProcessing.Steps {
				expectStep := &(expectProcessing.Steps[index1])
				statusStepInfo, exist := statusStepMap[expectStep.Code]
				if !exist {
					continue
				}
				expectStep.Status = clustermodel.MustParseByInstallerStatus(statusStepInfo.Phase)
				if statusStepInfo.Reason != "" {
					expectStep.ErrorType = &statusStepInfo.Reason
				}
				if statusStepInfo.Message != "" {
					expectStep.ErrorMessage = &statusStepInfo.Message
				}

			}

		}
	}

	// step 的状态设置完成后 设置组状态
	for index, _ := range expectProcessingList {
		expectProcessing := &expectProcessingList[index]
		expectProcessing.Status = installerutil.GetStepsStatus(expectProcessing.Steps)
	}

	statusResponse.Processing = append(statusResponse.Processing, expectProcessingList...)
	return nil
}

// InstallerTaskRenderToCreateResponse
// 将 installer task 的内容渲染到create response
func (sisyphusSolutionExecTask) InstallerTaskRenderToCreateResponse(createResponse *clustermodel.CreateResponse, taskSpec installerv1alpha1.InstallerTaskSpec) error {
	// nothing  to do
	return nil
}

// managedClusterTask
// 处理集群纳管理的task
type managedClusterTask struct {
	// 读取下层集群的西西弗斯地址
	sisyphusConfigHandler addon.SisyphusConfigHandler
}

func NewManagedClusterTask() Intf {
	return managedClusterTask{
		sisyphusConfigHandler: addon.NewSisyphusConfigHandler(),
	}
}

// Name
// 获取task 名称
func (managedClusterTask) Name() string {
	return constants.TaskNameForManagedCluster
}

// Description
// 获取task 描述
func (managedClusterTask) Description() string {
	return constants.TaskDescriptionForManagedCluster
}

// Kind
// 获取当前task 处理器的类型
func (managedClusterTask) Kind() installerv1alpha1.InstallerTaskKind {
	return installerv1alpha1.InstallerTaskSisyphusKindAddCluster
}

// should
// 用于判断渲染taskSpec时是否需要提供该task
// if 需要提供
//
//	if 以存在 -> 修改
//	else     -> 新增
//
// else
//
//	if 以存在 -> 删除
//	else     -> nothing to do
func (managedClusterTask) Should(installer *installerv1alpha1.Installer, request clustermodel.CreateRequest) bool {
	return true
}

// CreateRequestRenderToInstallerTask
// 将 create request 内容渲染到task
func (handler managedClusterTask) CreateRequestRenderToInstallerTask(existTasks []installerv1alpha1.InstallerTaskSpec, task *installerv1alpha1.InstallerTaskSpec, status *installerv1alpha1.InstallerTaskStatus, request clustermodel.CreateRequest) error {
	if task.SisyphusAddCluster == nil {
		task.SisyphusAddCluster = new(installerv1alpha1.SisyphusAddClusterTaskSpec)
	}

	sisyphusConfig, err := handler.sisyphusConfigHandler.GetSisyphusURL(context.Background(), "")
	if err != nil {
		return err
	}

	task.SisyphusAddCluster.URL = sisyphusConfig.Address
	task.SisyphusAddCluster.Username = sisyphusConfig.Username
	task.SisyphusAddCluster.Password = sisyphusConfig.Password
	// 获取exec 对应的solution 的名称
	solutionName, err := installerutil.GetSisyphusSolutionApplyTaskSolutionName(existTasks, constants.TaskNameForSisyphusSolutionApply)
	if err != nil {
		return err
	}
	task.SisyphusAddCluster.SolutionName = solutionName
	// set step name
	task.SisyphusAddCluster.StepName = clusterconfig.CreateClusterConfig.ClusterManageGroup.Steps[0].Code
	// set cluster name
	task.SisyphusAddCluster.ClusterName = request.ClusterName
	// set timeout
	task.SisyphusAddCluster.TimeoutSeconds = 60 * 10
	return nil
}

// InstallerTaskRenderToResponse
// 将 installer task 的内容渲染到response
func (managedClusterTask) InstallerTaskRenderToResponse(response *clustermodel.Response, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	// 设置初始化状态为纳管中
	if response.Status == "" {
		if taskStatus != nil {
			switch taskStatus.Phase {
			case installerv1alpha1.StatusPhasePending:
				fallthrough
			case installerv1alpha1.StatusPhaseRunning:
				response.Status = clustermodel.StatusTypeJoining
			case installerv1alpha1.StatusPhaseSuccess:
				// 步骤执行成功 查看下一次的状态
				response.Status = ""
			case installerv1alpha1.StatusPhaseFailed:
				response.Status = clustermodel.StatusTypeJoinFail
			default:
				response.Status = clustermodel.StatusTypeUnKnow
			}
		} else {
			response.Status = clustermodel.StatusTypeJoining
		}
	}
	return nil
}

// InstallerTaskRenderToCreateStateResponse
// 将 installer task 的内容渲染到create status response
func (managedClusterTask) InstallerTaskRenderToCreateStateResponse(statusResponse *clustermodel.CreateStatusResponse, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	var status clustermodel.ProcessStatus
	var reason, message *string
	if taskStatus == nil {
		status = clustermodel.ProcessStatusWaiting
	} else {
		switch taskStatus.Phase {
		case installerv1alpha1.StatusPhaseSuccess:
			status = clustermodel.ProcessStatusSuccess
		case installerv1alpha1.StatusPhaseFailed:
			status = clustermodel.ProcessStatusFail
		case installerv1alpha1.StatusPhaseRunning:
			status = clustermodel.ProcessStatusProcessing
		case installerv1alpha1.StatusPhasePending:
			status = clustermodel.ProcessStatusWaiting
		default:
			status = clustermodel.ProcessStatusWaiting
		}
		if taskStatus.Reason != "" {
			reason = &taskStatus.Reason
		}
		if taskStatus.Message != "" {
			message = &taskStatus.Message
		}
	}

	// 写入集群纳管的步骤
	clusterAddProcess := clustermodel.CreateProcessResponse{
		Code:        clusterconfig.CreateClusterConfig.ClusterManageGroup.Code,
		Name:        clusterconfig.CreateClusterConfig.ClusterManageGroup.Alias,
		Description: clusterconfig.CreateClusterConfig.ClusterManageGroup.Description,
		Status:      status,
		Steps: clustermodel.CreateProcessStepListResponse{
			{
				Code:         clusterconfig.CreateClusterConfig.ClusterManageGroup.Steps[0].Code,
				Name:         clusterconfig.CreateClusterConfig.ClusterManageGroup.Steps[0].Alias,
				Description:  clusterconfig.CreateClusterConfig.ClusterManageGroup.Steps[0].Description,
				Status:       status,
				ErrorType:    reason,
				ErrorMessage: message,
			},
		},
	}

	statusResponse.Processing = append(statusResponse.Processing, clusterAddProcess)
	return nil
}

// InstallerTaskRenderToCreateResponse
// 将 installer task 的内容渲染到create response
func (managedClusterTask) InstallerTaskRenderToCreateResponse(createResponse *clustermodel.CreateResponse, taskSpec installerv1alpha1.InstallerTaskSpec) error {
	// nothing to do
	return nil
}

// mergeStcTask
// 未集群STC打标签和注解的task的task
type mergeStcTask struct {
}

func NewMergeStcTask() Intf {
	return mergeStcTask{}
}

// Name
// 获取task 名称
func (mergeStcTask) Name() string {
	return constants.TaskNameForMergeStcMeta
}

// Description
// 获取task 描述
func (mergeStcTask) Description() string {
	return constants.TaskDescriptionForMergeStcMeta
}

// Kind
// 获取当前task 处理器的类型
func (mergeStcTask) Kind() installerv1alpha1.InstallerTaskKind {
	return installerv1alpha1.InstallerTaskKindMergeStcMeta
}

// should
// 用于判断渲染taskSpec时是否需要提供该task
// if 需要提供
//
//	if 以存在 -> 修改
//	else     -> 新增
//
// else
//
//	if 以存在 -> 删除
//	else     -> nothing to do
func (mergeStcTask) Should(installer *installerv1alpha1.Installer, request clustermodel.CreateRequest) bool {
	return true
}

// CreateRequestRenderToInstallerTask
// 将 request 内容渲染到task
func (mergeStcTask) CreateRequestRenderToInstallerTask(existTasks []installerv1alpha1.InstallerTaskSpec, task *installerv1alpha1.InstallerTaskSpec, status *installerv1alpha1.InstallerTaskStatus, request clustermodel.CreateRequest) error {
	// nil 值初始化判断
	if task.MergeStcMeta == nil {
		task.MergeStcMeta = new(installerv1alpha1.MergeStcMetaTaskSpec)
	}
	task.MergeStcMeta.ClusterName = request.ClusterName
	task.MergeStcMeta.Param.MergeLabels = mapMerge[string](nil, request.Labels)
	task.MergeStcMeta.Param.MergeAnnotations = mapMerge[string](nil, map[string]string{constants.StcDescriptionAnnotationKey: request.Description})

	// 处理版本特定的label 和 标签信息
	solutionInfo, exist := clusterconfig.CreateClusterConfig.SolutionInfos.FindByKubernetesAndCRIVersion(request.KubernetesVersion, request.KubernetesCRIVersion)
	if !exist {
		return errors.NewFromCodeWithMessage(errors.Var.ParamError, fmt.Sprintf("can not find solution by kubernetesVersion:%s,kubernetesCRIVersion:%s", request.KubernetesVersion, request.KubernetesCRIVersion))
	}
	task.MergeStcMeta.Param.MergeLabels = mapMerge[string](task.MergeStcMeta.Param.MergeLabels, solutionInfo.MergeLabels)
	task.MergeStcMeta.Param.MergeAnnotations = mapMerge[string](task.MergeStcMeta.Param.MergeAnnotations, solutionInfo.MergeAnnotations)

	return nil
}

func mapMerge[v any](src, merge map[string]v) map[string]v {
	if src == nil {
		src = make(map[string]v, 16)
	}
	if len(merge) != 0 {
		for k, v := range merge {
			src[k] = v
		}
	}
	return src
}

// InstallerTaskRenderToResponse
// 将 installer task 的内容渲染到response
func (mergeStcTask) InstallerTaskRenderToResponse(response *clustermodel.Response, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	// 该步骤无需设置集群状态

	// 设置annotation
	if taskSpec.MergeStcMeta == nil {
		return nil
	}
	// 处理标签
	labelStr := meta.ToLabelStr(taskSpec.MergeStcMeta.Param.MergeLabels)
	response.Labels = &labelStr

	// 处理描述
	description, _ := meta.GetFromMap(taskSpec.MergeStcMeta.Param.MergeAnnotations, constants.StcDescriptionAnnotationKey)
	response.Description = &description
	// 此步骤不需要设置状态
	return nil
}

// InstallerTaskRenderToCreateStateResponse
// 将 installer task 的内容渲染到create status response
func (mergeStcTask) InstallerTaskRenderToCreateStateResponse(statusResponse *clustermodel.CreateStatusResponse, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	// nothing to do
	return nil
}

// InstallerTaskRenderToCreateResponse
// 将 installer task 的内容渲染到create response
func (mergeStcTask) InstallerTaskRenderToCreateResponse(createResponse *clustermodel.CreateResponse, taskSpec installerv1alpha1.InstallerTaskSpec) error {
	if taskSpec.MergeStcMeta == nil {
		return nil
	}
	// 处理标签
	labels := meta.MergeMap(createResponse.Labels, taskSpec.MergeStcMeta.Param.MergeLabels)
	createResponse.Labels = labels
	// 处理描述
	var description string
	if taskSpec.MergeStcMeta.Param.MergeAnnotations != nil {
		description = taskSpec.MergeStcMeta.Param.MergeAnnotations[constants.StcDescriptionAnnotationKey]
	}
	createResponse.Description = description
	return nil
}

// getClusterStatusFromInstallerTaskStatus
// 从task status 中获取当前集群的状态
func getClusterStatusFromInstallerTaskStatus(nodeType clustermodel.NodeConfigType, cniTypes []clustermodel.CNIType, hasAutoStorageNode bool, taskStatus *installerv1alpha1.InstallerTaskStatus, cri clustermodel.CRIType) clustermodel.StatusType {

	// 根据参数检查返回状态
	if taskStatus == nil {
		return clustermodel.StatusTypeCreateInitialing
	}

	if len(taskStatus.Steps) == 0 {
		return ""
	}

	// 处理step未开始但标记未失败的情况
	if taskStatus.Steps[0].Phase == installerv1alpha1.StatusPhasePending && taskStatus.Phase == installerv1alpha1.StatusPhaseFailed {
		return clustermodel.StatusTypeCreateInitialFailed
	}

	// stepFunc 为内置函数 表示输入组信息 返回该组下的所有步骤
	stepFunc := func(groupCodes ...string) sets.Set[string] {
		steps := clusterconfig.CreateClusterConfig.StepGroups.ListStepCodeByGroupCodes(groupCodes...)
		return sets.New[string](steps...)
	}
	// 查看步骤状态 根据步骤状态设置集群状态所属生命周期
	// 获取节点初始化的步骤集合
	initialSteps := stepFunc(clusterconfig.CreateClusterConfig.ClusterCreatingGroupInfo.Initialing...)

	// 获取预检的步骤集合
	preflitingSteps := stepFunc(clusterconfig.CreateClusterConfig.ClusterCreatingGroupInfo.Prefligting...)

	//// 获取执行中的步骤集
	//installingSteps := stepFunc(clusterconfig.CreateClusterConfig.ClusterCreatingGroupInfo.Installing...)

	// 将status 中的step 转化为通过名称进行查找的map
	stepStatusMap := make(map[string]installerv1alpha1.SisyphusSolutionExecStepResult, len(taskStatus.Steps))
	for _, step := range taskStatus.Steps {
		step := step
		stepName := step.StepName
		stepStatusMap[stepName] = step
	}
	// 获取创建集群的期望执行步骤有序列表
	sortSteps := listCreateClusterExpectSteps(nodeType, cniTypes, hasAutoStorageNode, cri)
	for _, step := range sortSteps {
		stepResult, exist := stepStatusMap[step]
		if !exist {
			// stepStatusMap 中不存在 表示该步骤之前已执行过且已执行成功
			continue
		}
		switch stepResult.Phase {
		case installerv1alpha1.StatusPhasePending:
			fallthrough
		case installerv1alpha1.StatusPhaseRunning:
			if initialSteps.Has(step) {
				// 处理初始化中
				if taskStatus.Phase == installerv1alpha1.StatusPhaseFailed {
					return clustermodel.StatusTypeCreateInitialFailed
				}
				return clustermodel.StatusTypeCreateInitialing
			} else if preflitingSteps.Has(step) {
				// 处理预检中
				if taskStatus.Phase == installerv1alpha1.StatusPhaseFailed {
					return clustermodel.StatusTypePreflightFailed
				}
				return clustermodel.StatusTypePreflighting
			} else {
				// 处理集群创建中
				if taskStatus.Phase == installerv1alpha1.StatusPhaseFailed {
					return clustermodel.StatusTypeInstallFailed
				}
				return clustermodel.StatusTypeInstalling
			}
		case installerv1alpha1.StatusPhaseSuccess:
			continue
		case installerv1alpha1.StatusPhaseFailed:
			if initialSteps.Has(step) {
				// 处理初始化中
				return clustermodel.StatusTypeCreateInitialFailed
			} else if preflitingSteps.Has(step) {
				// 处理预检中
				return clustermodel.StatusTypePreflightFailed
			} else {
				// 处理集群创建中
				return clustermodel.StatusTypeInstallFailed
			}
		default:
			return clustermodel.StatusTypeUnKnow
		}
	}
	// 如果全部都是成功 则表示无步骤处理 在纳管中进行查看
	return ""
}

func createRequestGetNodeRequest(request clustermodel.CreateRequest) []node.NodeRequest {
	nodeRequest := make([]node.NodeRequest, 0, len(request.NodeConfigs.Nodes))
	for _, n := range request.NodeConfigs.Nodes {
		nodeRequest = append(nodeRequest, node.NodeRequest{
			Ip:   n.Ip,
			Port: n.Port,
			Auth: n.Auth,
		})
	}
	return nodeRequest
}

func listCreateClusterExpectSteps(nodeConfigType clustermodel.NodeConfigType,
	cniTypes []clustermodel.CNIType, hasAutoStorage bool, cri clustermodel.CRIType) []string {
	var stepOptions = make([]string, 0)
	// 选择集群部署模式 AllInOne、MinimizeHA、StandardNoneHA、StandardHA
	stepOptions = append(stepOptions, string(nodeConfigType))
	// 选择对应网络模式 选择网络模式时需要带上集群部署模式
	for _, cniType := range cniTypes {
		stepOptions = append(stepOptions, strings.Join([]string{string(nodeConfigType), string(cniType)}, "/"))
	}
	// 单macvlan网络模式下 需要额外运行 "重启coreDNS组件的步骤"
	mainNetworkTypes := sets.New[clustermodel.CNIType](cniTypes...).Delete(clustermodel.CNITypeKubeOVM)
	if mainNetworkTypes.Len() == 1 && mainNetworkTypes.Has(clustermodel.CNITypeMacvlan) {
		stepOptions = append(stepOptions, strings.Join([]string{string(nodeConfigType), string(clustermodel.CNITypeMacvlan), clusterconfig.BackGroundStepSuffix}, "/"))
	}
	// 如果有磁盘自动挂载，则需要跑磁盘自动挂载的步骤
	if hasAutoStorage {
		stepOptions = append(stepOptions, string(nodeConfigType)+"/"+string(clustermodel.NodeStorageTypeAuto))
	}
	stepOptions = append(stepOptions, string(nodeConfigType)+"/"+string(cri))

	return clusterconfig.CreateClusterConfig.StepGroups.ListStepCodeAnyMatchLabels(stepOptions...)
}

// afterClusterTask
// 处理集群纳管后的task
type afterClusterTask struct {
	// 读取下层集群的西西弗斯地址
	sisyphusConfigHandler addon.SisyphusConfigHandler
}

func NewAfterClusterTask() Intf {
	return afterClusterTask{
		sisyphusConfigHandler: addon.NewSisyphusConfigHandler(),
	}
}

// Name
// 获取task 名称
func (act afterClusterTask) Name() string {
	return constants.TaskNameAfterSisyphusManagedClusterSolutionExec
}

// Description
// 获取task 描述
func (act afterClusterTask) Description() string {
	return constants.TaskDescriptionAfterSisyphusManagedClusterSolutionExec
}

// Kind
// 获取当前task 处理器的类型
func (act afterClusterTask) Kind() installerv1alpha1.InstallerTaskKind {
	return installerv1alpha1.InstallerTaskKindSisyphusSolutionExec
}

// should
// 用于判断渲染taskSpec时是否需要提供该task
// if 需要提供
//
//	if 以存在 -> 修改
//	else     -> 新增
//
// else
//
//	if 以存在 -> 删除
//	else     -> nothing to do
func (act afterClusterTask) Should(installer *installerv1alpha1.Installer, request clustermodel.CreateRequest) bool {
	return len(listAfterManagedClusterExpectSteps()) != 0
}

// CreateRequestRenderToInstallerTask
// 将 create request 内容渲染到task
func (act afterClusterTask) CreateRequestRenderToInstallerTask(existTasks []installerv1alpha1.InstallerTaskSpec, task *installerv1alpha1.InstallerTaskSpec, status *installerv1alpha1.InstallerTaskStatus, request clustermodel.CreateRequest) error {
	// 获取exec 对应的solution 的名称
	solutionName, err := installerutil.GetSisyphusSolutionApplyTaskSolutionName(existTasks, constants.TaskNameForSisyphusSolutionApply)
	if err != nil {
		return err
	}
	// 获取节点上线的期望步骤
	var expectSteps = listAfterManagedClusterExpectSteps()

	sisyphusConfig, err := act.sisyphusConfigHandler.GetSisyphusURL(context.Background(), "")
	if err != nil {
		return err
	}
	return installerutil.RenderTaskForSolutionExec(task, solutionName, expectSteps, request.StartFromFailed, request.Reset, status, sisyphusConfig)
}

// InstallerTaskRenderToResponse
// 将 installer task 的内容渲染到response
func (act afterClusterTask) InstallerTaskRenderToResponse(response *clustermodel.Response, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	// 如果前面步骤未设置状态 则设置状态
	if response.Status == "" {
		response.Status = getClusterStatusFromInstallerTaskStatusForAfterAddCluster(taskStatus)
	}
	return nil
}

// InstallerTaskRenderToCreateStateResponse
// 将 installer task 的内容渲染到create status response
func (act afterClusterTask) InstallerTaskRenderToCreateStateResponse(statusResponse *clustermodel.CreateStatusResponse, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {

	// 定义一个集合 存储spec中的所有 step
	specParamsSet := sets.New[string]()
	if len(taskSpec.SisyphusSolutionExec.Param) != 0 {
		specParamsSet.Insert(taskSpec.SisyphusSolutionExec.Param...)
	}

	processing := clustermodel.CreateProcessResponse{
		Code:        clusterconfig.CreateClusterConfig.AfterAddCluster.Code,
		Name:        clusterconfig.CreateClusterConfig.AfterAddCluster.Alias,
		Description: clusterconfig.CreateClusterConfig.AfterAddCluster.Description,
		Status:      clustermodel.ProcessStatusWaiting,
	}
	var processingSteps clustermodel.CreateProcessStepListResponse
	for _, step := range clusterconfig.CreateClusterConfig.AfterAddCluster.Steps {
		step := step
		var status = clustermodel.ProcessStatusWaiting
		if !specParamsSet.Has(step.Code) {
			status = clustermodel.ProcessStatusSuccess
		}
		processingSteps = append(processingSteps, clustermodel.CreateProcessStepResponse{
			Code:        step.Code,
			Name:        step.Alias,
			Description: step.Description,
			Status:      status,
		})
	}
	processing.Steps = processingSteps

	// 设置step状态
	if taskStatus != nil && len(taskStatus.Steps) != 0 {
		// 构建status step map 便于查询
		statusStepMap := make(map[string]installerv1alpha1.InstallerStatusInfo, len(taskStatus.Steps))
		// 设置step 状态
		for _, step := range taskStatus.Steps {
			name := step.StepName
			statusInfo := step.InstallerStatusInfo
			statusStepMap[name] = statusInfo
		}
		// 先设置每个step 的状态
		for index1, _ := range processing.Steps {
			expectStep := &(processing.Steps[index1])
			statusStepInfo, exist := statusStepMap[expectStep.Code]
			if !exist {
				continue
			}
			expectStep.Status = clustermodel.MustParseByInstallerStatus(statusStepInfo.Phase)
			if statusStepInfo.Reason != "" {
				expectStep.ErrorType = &statusStepInfo.Reason
			}
			if statusStepInfo.Message != "" {
				expectStep.ErrorMessage = &statusStepInfo.Message
			}
		}
	}

	processing.Status = installerutil.GetStepsStatus(processing.Steps)
	statusResponse.Processing = append(statusResponse.Processing, processing)
	return nil
}

// InstallerTaskRenderToCreateResponse
// 将 installer task 的内容渲染到create response
func (act afterClusterTask) InstallerTaskRenderToCreateResponse(createResponse *clustermodel.CreateResponse, taskSpec installerv1alpha1.InstallerTaskSpec) error {
	// nothing  to do
	return nil
}

// 获取期望步骤
func listAfterManagedClusterExpectSteps() []string {
	var codes = make([]string, 0, len(clusterconfig.CreateClusterConfig.AfterAddCluster.Steps))
	for _, step := range clusterconfig.CreateClusterConfig.AfterAddCluster.Steps {
		codes = append(codes, step.Code)
	}
	return codes
}

// getClusterStatusFromInstallerTaskStatusForAfterAddCluster
// 从task status 中获取当前集群的状态（纳管集群后置步骤专用）
func getClusterStatusFromInstallerTaskStatusForAfterAddCluster(taskStatus *installerv1alpha1.InstallerTaskStatus) clustermodel.StatusType {
	// 根据参数检查返回状态
	if taskStatus == nil {
		return clustermodel.StatusTypeComponentInstalling
	}

	if len(taskStatus.Steps) == 0 {
		return clustermodel.StatusTypeInitializingStatus
	}

	// 处理step未开始但标记未失败的情况
	if taskStatus.Steps[0].Phase == installerv1alpha1.StatusPhasePending && taskStatus.Phase == installerv1alpha1.StatusPhaseFailed {
		return clustermodel.StatusTypeComponentInstallFailed
	}

	// 将status 中的step 转化为通过名称进行查找的map
	stepStatusMap := make(map[string]installerv1alpha1.SisyphusSolutionExecStepResult, len(taskStatus.Steps))
	for _, step := range taskStatus.Steps {
		step := step
		stepName := step.StepName
		stepStatusMap[stepName] = step
	}
	// 获取后置步骤的期望执行步骤有序列表
	sortSteps := listAfterManagedClusterExpectSteps()
	for _, step := range sortSteps {
		stepResult, exist := stepStatusMap[step]
		if !exist {
			// stepStatusMap 中不存在 表示该步骤之前已执行过且已执行成功
			continue
		}
		switch stepResult.Phase {
		case installerv1alpha1.StatusPhasePending:
			fallthrough
		case installerv1alpha1.StatusPhaseRunning:
			return clustermodel.StatusTypeComponentInstalling
		case installerv1alpha1.StatusPhaseSuccess:
			continue
		case installerv1alpha1.StatusPhaseFailed:
			return clustermodel.StatusTypeComponentInstallFailed
		default:
			return clustermodel.StatusTypeUnKnow
		}
	}
	// 如果全部都是成功
	return clustermodel.StatusTypeInitializingStatus
}
