package task

import (
	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
)

type Intf interface {
	// Name
	// 获取task 名称
	Name() string
	// Description
	// 获取task 描述
	Description() string

	// Kind
	// 获取当前task 处理器的类型
	Kind() installerv1alpha1.InstallerTaskKind

	// should
	// 用于判断渲染taskSpec时是否需要提供该task
	// if 需要提供
	//        if 以存在 -> 修改
	//        else     -> 新增
	// else
	//        if 以存在 -> 删除
	//        else     -> nothing to do
	Should(installer *installerv1alpha1.Installer, request clustermodel.CreateRequest) bool

	// CreateRequestRenderToInstallerTask
	// 将 create request 内容渲染到task
	CreateRequestRenderToInstallerTask(existTasks []installerv1alpha1.InstallerTaskSpec, task *installerv1alpha1.InstallerTaskSpec, status *installerv1alpha1.InstallerTaskStatus, request clustermodel.CreateRequest) error

	// InstallerTaskRenderToResponse
	// 将 installer task 的内容渲染到response
	InstallerTaskRenderToResponse(response *clustermodel.Response, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error

	// InstallerTaskRenderToCreateStateResponse
	// 将 installer task 的内容渲染到create status response
	InstallerTaskRenderToCreateStateResponse(statusResponse *clustermodel.CreateStatusResponse, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error

	// InstallerTaskRenderToCreateResponse
	// 将 installer task 的内容渲染到create response
	InstallerTaskRenderToCreateResponse(createResponse *clustermodel.CreateResponse, taskSpec installerv1alpha1.InstallerTaskSpec) error
}
