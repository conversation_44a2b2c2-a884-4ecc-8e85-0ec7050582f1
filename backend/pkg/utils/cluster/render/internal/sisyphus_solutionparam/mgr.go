package sisyphus_solutionparam

import (
	"context"
	"encoding/json"
	"fmt"

	"harmonycloud.cn/unifiedportal/cloudservice-operator/pkg/handler/installer/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/addon"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/cluster/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/sisyphustype"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/sisyphusutil"
	"k8s.io/apimachinery/pkg/util/sets"
)

type paramMgr struct {
	// 读取下层集群的西西弗斯地址
	sisyphusConfigHandler addon.SisyphusConfigHandler
	sisyphusclient        client.Sisyphus
	jobParams             []jobParamStep
	options               []optionStep
}

func NewParamMgr() ParamMgr {
	return paramMgr{

		sisyphusConfigHandler: addon.NewSisyphusConfigHandler(),
		sisyphusclient:        client.NewSisyphus(),
		jobParams: []jobParamStep{
			newCommonParamConfigJobParamStep(),
			newCalicoNetworkConfigJobParamStep(),
			newMacvlanNetworkConfigJobParamStep(),
			newKubeOVNNetworkConfigJobParamStep(),
			newDefaultLbParentLbConfigJobParamStep(),
			newApiServerLbConfigJobParamStep(),
			newContainerArrangementAndRuntimeParamConfigJobParamStep(),
			newStanderComponentConfigJobParamStep(),
			NewStorageClassConfigJobParamStep(),
			NewDataImportJobParamStepJobParamStep(),
		},
		options: []optionStep{
			newCommonOptionStep(),
			newExtraOptionStep(),
			newNetworkOptionStep(),
			newLoggingOptionStep(),
			newMonitorOptionStep(),
			newLabsOptionStep(),
		},
	}
}
func (mgr paramMgr) CreateRequestAsParam(createRequest clustermodel.CreateRequest, solutionName string) (string, error) {
	options, err := mgr.parseSisyphusOption(createRequest)
	if err != nil {
		return "", err
	}
	jobParam, err := mgr.parseSisyphusJobParams(createRequest)
	if err != nil {
		return "", err
	}
	hosts, err := parseSisyphusHostGroups(createRequest)
	if err != nil {
		return "", err
	}

	sisyphusConfig, err := mgr.sisyphusConfigHandler.GetSisyphusURL(context.Background(), "")
	if err != nil {
		return "", err
	}

	editContext, err := sisyphusutil.GetEditContext(mgr.sisyphusclient, sisyphusConfig)
	if err != nil {
		return "", err
	}

	solutionInfo, exist := config.CreateClusterConfig.SolutionInfos.FindByKubernetesAndCRIVersion(createRequest.KubernetesVersion, createRequest.KubernetesCRIVersion)
	if !exist {
		return "", errors.NewFromCodeWithMessage(errors.Var.ParamError, fmt.Sprintf("can not find solution by kubernetesVersion:%s,kubernetesCRIVersion:%s", createRequest.KubernetesVersion, createRequest.KubernetesCRIVersion))
	}
	// 处理NodeStorage
	disks, err := sisyphusutil.GetNodeDisks(convert2NodeDiskMap(createRequest))
	if err != nil {
		return "", err
	}

	sisyphusParam := sisyphustype.EditDeployRequest{
		Name: solutionName,
		Solution: sisyphustype.SolutionMetadata{
			Name:    solutionInfo.SolutionName,
			Version: solutionInfo.SolutionVersion,
		}, Contexts: editContext,
		Hosts:   hosts,
		Options: options,
		Params:  jobParam,
		Disks:   disks,
	}
	jsonBytes, err := json.Marshal(sisyphusParam)
	return string(jsonBytes), err
}

func convert2NodeDiskMap(createRequest clustermodel.CreateRequest) map[string]clustermodel.NodeStorageRequest {
	var res = make(map[string]clustermodel.NodeStorageRequest)
	for _, node := range createRequest.NodeConfigs.Nodes {
		res[node.Ip] = node.Storage
	}
	return res
}

func (mgr paramMgr) ParamRenderToResponse(response *clustermodel.Response, param string) error {
	var sisyphusParam sisyphustype.EditDeployRequest
	_ = json.Unmarshal([]byte(param), &sisyphusParam)
	k8sVersion, _ := getK8sVersionAndCriVersion(sisyphusParam)
	response.K8sVersion = &k8sVersion
	// 处理JobParam
	for _, jobParam := range sisyphusParam.Params {
		name := jobParam.Name
		property := jobParam.Properties
		// 获取处理的interface
		var jobParamStep jobParamStep
		for _, step := range mgr.jobParams {
			if step.name() == name {
				jobParamStep = step
				break
			}
		}
		if jobParamStep == nil {
			continue
		}
		if err := jobParamStep.renderResponse(property, response); err != nil {
			return err
		}
	}
	// 处理storage
	if len(sisyphusParam.Disks) != 0 {
		response.HasAutoStorageNode = true
	}
	return nil
}

func (mgr paramMgr) ParamRenderToCreateResponse(response *clustermodel.CreateResponse, param string) error {
	var sisyphusParam sisyphustype.EditDeployRequest
	_ = json.Unmarshal([]byte(param), &sisyphusParam)
	k8sVersion, criVersion := getK8sVersionAndCriVersion(sisyphusParam)
	response.KubernetesVersion = k8sVersion
	response.KubernetesCRIVersion = criVersion

	// 处理节点角色 | 是否包含GPU
	masterNodeIps := sets.New[string]()
	gpuNodeIps := sets.New[string]()
	for _, node := range sisyphusParam.Hosts {
		node := node
		groupSets := sets.New[string](node.Groups...)
		// 分组选择master
		for _, masterCode := range config.CreateClusterConfig.NodeRoleMasterCodes {
			if groupSets.Has(masterCode) {
				masterNodeIps.Insert(node.IP)
				break
			}
		}
		// 分组选择支持gpu的
		if groupSets.Has(config.CreateClusterConfig.NodeHasGpuCode) {
			gpuNodeIps.Insert(node.IP)
		}
	}
	for index, node := range response.NodeConfigs.Nodes {
		if masterNodeIps.Has(node.Ip) {
			response.NodeConfigs.Nodes[index].Role = clustermodel.Master
		} else {
			response.NodeConfigs.Nodes[index].Role = clustermodel.Worker
		}
		response.NodeConfigs.Nodes[index].SupportGpu = gpuNodeIps.Has(node.Ip)

	}

	// 处理JobParam
	for _, jobParam := range sisyphusParam.Params {
		name := jobParam.Name
		property := jobParam.Properties
		// 获取处理的interface
		var jobParamStep jobParamStep
		for _, step := range mgr.jobParams {
			if step.name() == name {
				jobParamStep = step
				break
			}
		}
		if jobParamStep == nil {
			continue
		}
		if err := jobParamStep.renderCreateResponse(property, response); err != nil {
			return err
		}
	}

	// 处理disk
	response.NodeConfigs.Nodes.RenderNodeDiskConfigs(sisyphusutil.ReverseNodeDisks(sisyphusParam.Disks))
	return nil

}

func (mgr paramMgr) ParamRenderToCreateStatusResponse(response *clustermodel.CreateStatusResponse, param string) error {
	// 处理JobParam
	var sisyphusParam sisyphustype.EditDeployRequest
	_ = json.Unmarshal([]byte(param), &sisyphusParam)
	// 处理JobParam
	for _, jobParam := range sisyphusParam.Params {
		name := jobParam.Name
		property := jobParam.Properties
		// 获取处理的interface
		var jobParamStep jobParamStep
		for _, step := range mgr.jobParams {
			if step.name() == name {
				jobParamStep = step
				break
			}
		}
		if jobParamStep == nil {
			continue
		}
		if err := jobParamStep.renderCreateStatusResponse(property, response); err != nil {
			return err
		}
	}
	// 处理storage
	if len(sisyphusParam.Disks) != 0 {
		response.HasAutoStorageNode = true
	}
	return nil

}

func getK8sVersionAndCriVersion(sisyphusParam sisyphustype.EditDeployRequest) (k8sVersion, criVersion string) {
	solutionName := sisyphusParam.Solution.Name
	solutionVersion := sisyphusParam.Solution.Version
	solution, exist := config.CreateClusterConfig.SolutionInfos.FindBySolutionNameAndSolutionVersion(solutionName, solutionVersion)
	if exist {
		k8sVersion = solution.KubernetesVersion
		criVersion = solution.CRIVersion
	} else {
		k8sVersion = constants.UnKnowKubernetesVersion
		criVersion = constants.UnKnowCRIVersion
	}
	return
}

func parseSisyphusHostGroups(request clustermodel.CreateRequest) ([]sisyphustype.HostGroups, error) {
	var result []sisyphustype.HostGroups
	for _, node := range request.NodeConfigs.Nodes {
		hostGroup := sisyphustype.HostGroups{
			IP: node.Ip,
		}
		if node.SupportGpu {
			hostGroup.Groups = []string{config.CreateClusterConfig.NodeHasGpuCode}
		}
		result = append(result, hostGroup)
	}
	var first, second, third, fourth, fifth, sixth, seventh, other []sisyphustype.HostGroups
	var nodeConfig config.NodeGroupConfig
	switch request.NodeConfigs.NodeConfigType {
	case clustermodel.NodeConfigTypeAllInOne:
		first = result[0:1]
		other = result[1:]
		nodeConfig = config.CreateClusterConfig.NodeTypeGroup.AllInOne
	case clustermodel.NodeConfigTypeStandardNoneHA:
		first = result[0:1]
		second = result[1:2]
		third = result[2:3]
		fourth = result[3:4]
		other = result[4:]
		nodeConfig = config.CreateClusterConfig.NodeTypeGroup.StandardNoneHA
	case clustermodel.NodeConfigTypeMinimizeHA:
		first = result[0:1]
		second = result[1:2]
		third = result[2:3]
		other = result[3:]
		nodeConfig = config.CreateClusterConfig.NodeTypeGroup.MinimizeHA
	case clustermodel.NodeConfigTypeStandardHA:
		first = result[0:1]
		second = result[1:2]
		third = result[2:3]
		fourth = result[3:4]
		fifth = result[4:5]
		sixth = result[5:6]
		seventh = result[6:7]
		other = result[7:]
		nodeConfig = config.CreateClusterConfig.NodeTypeGroup.StandardHA
	default:
		return result, errors.NewFromCode(errors.Var.NodeConfigTypeIllegal)
	}

	setNodeGroupFunc := func(nodes []sisyphustype.HostGroups, groups []string) {
		for index, _ := range nodes {
			if len(nodes[index].Groups) == 0 {
				nodes[index].Groups = groups
			} else {
				nodes[index].Groups = append(nodes[index].Groups, groups...)
			}
		}
	}
	setNodeGroupFunc(first, nodeConfig.First)
	setNodeGroupFunc(second, nodeConfig.Second)
	setNodeGroupFunc(third, nodeConfig.Third)
	setNodeGroupFunc(fourth, nodeConfig.Fourth)
	setNodeGroupFunc(fifth, nodeConfig.Fifth)
	setNodeGroupFunc(sixth, nodeConfig.Sixth)
	setNodeGroupFunc(seventh, nodeConfig.Seventh)
	setNodeGroupFunc(other, nodeConfig.Other)
	// 处理 plugin
	var dealGroup []*[]string
	for index, _ := range result {
		dealGroup = append(dealGroup, &result[index].Groups)
	}
	if _, exist := request.NetworkConfigs.CNIs[clustermodel.CNITypeKubeOVM]; exist {
		config.CreateClusterConfig.NodeTypeGroup.KubeOVNPlugin.Plugin(dealGroup...)
	}
	return result, nil
}

func (mgr paramMgr) parseSisyphusOption(request clustermodel.CreateRequest) (map[string]interface{}, error) {
	var optionMap = make(map[string]interface{}, len(mgr.options)<<1)
	for _, option := range mgr.options {
		option := option
		should, err := option.should(request)
		if err != nil {
			return nil, err
		}
		if should {
			optionIntf, err := option.option(request)
			if err != nil {
				return nil, err
			}
			optionMap[option.name()] = optionIntf
		}
	}
	return optionMap, nil
}

func (mgr paramMgr) parseSisyphusJobParams(request clustermodel.CreateRequest) ([]sisyphustype.JobParam, error) {
	var jobParams = make([]sisyphustype.JobParam, 0)
	for _, jp := range mgr.jobParams {
		shouldConvert, err := jp.shouldConvert(request)
		if err != nil {
			return nil, err
		}
		if !shouldConvert {
			continue
		}
		name := jp.name()
		cvt, err := jp.convert(request)
		if err != nil {
			return nil, err
		}
		jobParams = append(jobParams, sisyphustype.JobParam{
			Name:       name,
			Properties: cvt,
		})
	}
	return jobParams, nil
}
