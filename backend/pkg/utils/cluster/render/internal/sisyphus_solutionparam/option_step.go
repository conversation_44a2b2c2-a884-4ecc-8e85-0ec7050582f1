package sisyphus_solutionparam

import (
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
)

const (
	optionNameCommon  = "common"
	optionNameExtra   = "extra"
	optionNameNetwork = "network"
	optionNameLogging = "logging"
	optionNameMonitor = "monitor"
	optionNameLabs    = "labs"
)

type commonOptionStep struct {
}

func newCommonOptionStep() optionStep {
	return commonOptionStep{}
}
func (commonOptionStep) name() string {
	return optionNameCommon
}
func (commonOptionStep) option(createRequest clustermodel.CreateRequest) (interface{}, error) {
	// 处理解决方案是否为高可用
	var arch CaasInfraDeployArchType
	var defaultKubernetesMasterScheduler bool
	var defaultDeployApiserverLB, defaultDeployIngressVIP bool
	var defaultDeployMixLB *bool

	switch createRequest.NodeConfigs.NodeConfigType {
	case clustermodel.NodeConfigTypeAllInOne:
		// All-in-One
		arch = CaasInfraDeployArchTypeNoneHA
		defaultKubernetesMasterScheduler = true
		defaultDeployApiserverLB, defaultDeployIngressVIP = false, false
		defaultDeployMixLB = nil
	case clustermodel.NodeConfigTypeMinimizeHA:
		// 最小化高可用
		arch = CaasInfraDeployArchTypeHA
		defaultKubernetesMasterScheduler = true
		defaultDeployApiserverLB, defaultDeployIngressVIP = false, false
		flag := true
		defaultDeployMixLB = &flag
	case clustermodel.NodeConfigTypeStandardNoneHA:
		// 标准非高可用
		arch = CaasInfraDeployArchTypeNoneHA
		defaultKubernetesMasterScheduler = false
		defaultDeployApiserverLB, defaultDeployIngressVIP = false, false
		defaultDeployMixLB = nil
	case clustermodel.NodeConfigTypeStandardHA:
		// 标准高可用
		arch = CaasInfraDeployArchTypeHA
		defaultKubernetesMasterScheduler = false
		defaultDeployApiserverLB, defaultDeployIngressVIP = true, true
		flag := false
		defaultDeployMixLB = &flag
	default:
		return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, "un support node type"+string(createRequest.NodeConfigs.NodeConfigType))
	}

	return commonOption{
		AutoCalVolumeSizeForAllComponents: true,
		CaasInfraDeployArch:               arch,
		// CaasInfraVersion 写死 专业版
		CaasInfraVersion:            "专业版",
		DefaultCreateSystemNodePool: true,
		ContainerRuntime:            string(createRequest.CRI),
		// DefaultDeployApiserverLB
		// DefaultDeployIngressVIP 只有高可用才需要填写true 否则填写false
		DefaultDeployApiserverLB:         defaultDeployApiserverLB,
		DefaultDeployIngressVIP:          defaultDeployIngressVIP,
		DefaultDeployMixLB:               defaultDeployMixLB,
		DefaultKubernetesMasterScheduler: defaultKubernetesMasterScheduler,
		// IsControlPlane 写死false
		IsControlPlane: false,
		// 预检写死true
		PreCheck:                true,
		SkyviewTokenAuthWebhook: true,
		CustomSandboxRegistry:   false,
		AutoEditHostname:        true,
		CustomPassword:          false,
	}, nil
}
func (commonOptionStep) should(createRequest clustermodel.CreateRequest) (bool, error) {
	return true, nil
}

type extraOptionStep struct {
}

func newExtraOptionStep() optionStep {
	return extraOptionStep{}
}

func (extraOptionStep) name() string {
	return optionNameExtra
}
func (extraOptionStep) option(createRequest clustermodel.CreateRequest) (interface{}, error) {
	// defaultDeployChrony、defaultDeployGlusterFS、defaultDeployHarbor、defaultDeployHarborVIP -> true、false、false、false
	obj := extraOption{
		DefaultDeployApisixLB:   false, // 写死false
		DefaultDeployApisixVIP:  false, // 写死false
		DefaultDeployTraefikLB:  false, // 写死false
		DefaultDeployTraefikVIP: false, // 写死false
		ExternalEtcdCluster:     false, // 写死false
		// DefaultSupportArchOS 已废弃 这里随便写死一个值即可
		DefaultSupportArchOS:                "centos7-amd64",
		DefaultDeployChrony:                 true,
		DefaultDeployGlusterFS:              false,
		DefaultDeployHarbor:                 false,
		DefaultDeployMinio:                  false,
		UseExternalShareStorage:             false,
		UseExternalRegistry:                 createRequest.Registry != nil, // 是否使用自定义仓库 true 表示 使用自定义仓库 false 表示使用平台默认仓库
		DefaultDeploySisyphusClusterManager: false,
	}

	// 高可用需要显式声明  DefaultDeployHarborVIP 为 false
	switch createRequest.NodeConfigs.NodeConfigType {
	case clustermodel.NodeConfigTypeMinimizeHA:
		fallthrough
	case clustermodel.NodeConfigTypeStandardHA:
		flag := false
		obj.DefaultDeployHarborVIP = &flag
	}
	return obj, nil
}
func (extraOptionStep) should(createRequest clustermodel.CreateRequest) (bool, error) {
	return true, nil
}

type networkOptionStep struct {
}

func newNetworkOptionStep() optionStep {
	return networkOptionStep{}
}
func (networkOptionStep) name() string {
	return optionNameNetwork
}
func (networkOptionStep) option(createRequest clustermodel.CreateRequest) (interface{}, error) {
	option := &networkOption{}
	// 处理 calico
	if config, exist := createRequest.NetworkConfigs.CNIs[clustermodel.CNITypeCalico]; exist {
		stack, err := getNetworkStackValue(len(config.Ipv4Param) != 0, len(config.Ipv6Param) != 0)
		if err != nil {
			return nil, err
		}
		option.Calico.Install = true
		option.Calico.Stack = stack
		option.Calico.Property = map[string]interface{}{}
		switch stack {
		case networkStackIpv4:
			option.Calico.Property["v4Mode"] = "ipip"
		case networkStackIpv6:
			option.Calico.Property["v6Mode"] = "bgp"
		case networkStackDual:
			option.Calico.Property["v4Mode"] = "ipip"
			option.Calico.Property["v6Mode"] = "bgp"
		}
	} else {
		option.Calico.Install = false
	}
	// 处理 macvlan
	if config, exist := createRequest.NetworkConfigs.CNIs[clustermodel.CNITypeMacvlan]; exist {
		stack, err := getNetworkStackValue(len(config.Ipv4Param) != 0, len(config.Ipv6Param) != 0)
		if err != nil {
			return nil, err
		}
		option.Bifrost.Install = true
		option.Bifrost.Stack = stack
	} else {
		option.Bifrost.Install = false
	}

	// todo 处理 kube-ovm
	if _, exist := createRequest.NetworkConfigs.CNIs[clustermodel.CNITypeKubeOVM]; exist {

	} else {

	}

	return option, nil
}
func (networkOptionStep) should(createRequest clustermodel.CreateRequest) (bool, error) {
	return true, nil
}

type loggingOptionStep struct {
}

func newLoggingOptionStep() optionStep {
	return loggingOptionStep{}
}
func (loggingOptionStep) name() string {
	return optionNameLogging
}
func (loggingOptionStep) option(createRequest clustermodel.CreateRequest) (interface{}, error) {
	option := &loggingOption{
		CreateBackupStorage: false,
		OutputToKafka:       false,
	}
	return option, nil
}
func (loggingOptionStep) should(createRequest clustermodel.CreateRequest) (bool, error) {
	return true, nil
}

type monitorOptionStep struct {
}

func newMonitorOptionStep() optionStep {
	return monitorOptionStep{}
}
func (monitorOptionStep) name() string {
	return optionNameMonitor
}
func (monitorOptionStep) option(createRequest clustermodel.CreateRequest) (interface{}, error) {
	option := &monitorOption{
		EnableSkyviewAlertWebhook: true,
		EnableZeusAlertWebhook:    false,
	}
	return option, nil
}
func (monitorOptionStep) should(createRequest clustermodel.CreateRequest) (bool, error) {
	return true, nil
}

type labsOptionStep struct {
}

func newLabsOptionStep() optionStep {
	return labsOptionStep{}
}

func (labsOptionStep) name() string {
	return optionNameLabs
}
func (labsOptionStep) option(createRequest clustermodel.CreateRequest) (interface{}, error) {
	_, deployKubeVirt := createRequest.NetworkConfigs.CNIs[clustermodel.CNITypeKubeOVM]
	return &labsOption{
		DeployKubevirt: deployKubeVirt,
		DeployLonghorn: false,
	}, nil
}
func (labsOptionStep) should(createRequest clustermodel.CreateRequest) (bool, error) {
	return true, nil
}
