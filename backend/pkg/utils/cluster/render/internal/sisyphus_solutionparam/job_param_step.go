package sisyphus_solutionparam

import (
	"fmt"
	"strconv"
	"strings"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/cluster/config"
	"k8s.io/apimachinery/pkg/util/sets"
)

const (
	jobParamNameCommonParamConfig                   = "A001-通用参数"
	jobParamNameCalicoNetworkConfig                 = "A021-网络插件(calico)配置"
	jobParamNameMacvlanNetworkConfig                = "A022-网络插件(bifrost)配置"
	jobParamNameKubeOVNNetworkConfig                = "C001-实验性组件-Kubevirt配置"
	jobParamNameDefaultLbParentLbConfig             = "A012-默认负载均衡(nginx-ingress-controller)上层负载均衡配置"
	jobParamNameApiServerLbConfig                   = "A011-资源管理接口(apiserver)上层负载均衡配置"
	jobParamNameContainerArrangementAndRuntimeParam = "A002-容器编排及运行时参数"
	jobParamNameStanderComponentConfig              = "A031-标准版组件配置"
	jobParamNameStorageClassConfig                  = "A041-存储配置"
	jobParamNameDateImport                          = "A051-数据导入配置"
)

type apiServerLbConfigJobParamStep struct {
	shouldConvertNodeConfigType sets.Set[clustermodel.NodeConfigType]
}

func newApiServerLbConfigJobParamStep() jobParamStep {
	return apiServerLbConfigJobParamStep{
		// 只有最小化高可用 和标准高可用 才需要转化
		shouldConvertNodeConfigType: sets.New[clustermodel.NodeConfigType](clustermodel.NodeConfigTypeMinimizeHA, clustermodel.NodeConfigTypeStandardHA),
	}
}

// 获取步骤名称
func (apiServerLbConfigJobParamStep) name() string {
	return jobParamNameApiServerLbConfig
}

// 判断步骤是否需要转化 如没有macvlan 就不需要转化macvlan 的请求参数
func (step apiServerLbConfigJobParamStep) shouldConvert(createRequest clustermodel.CreateRequest) (bool, error) {
	flag := step.shouldConvertNodeConfigType.Has(createRequest.NodeConfigs.NodeConfigType)
	return flag, nil
}

// 转化请求参数
func (step apiServerLbConfigJobParamStep) convert(createRequest clustermodel.CreateRequest) (map[string]interface{}, error) {
	// vrid 已确认写死 110
	return newApiServerLbModel(createRequest.NetworkConfigs.ApiServerVIP.AsVIP(), "110", createRequest.NetworkConfigs.ApiServerVIP.NetworkCard).asProperty()
}

// 将property 的内容渲染到 create response
func (step apiServerLbConfigJobParamStep) renderCreateResponse(property map[string]interface{}, createResponse *clustermodel.CreateResponse) error {
	apiServerLbModel := apiServerLbModel{}
	if err := utils.BeanCopy(property, &apiServerLbModel); err != nil {
		return err
	}
	vipCidr := apiServerLbModel.VIP.ApiServer.VIPs[0].Vips[0]
	networkCard := apiServerLbModel.VIP.ApiServer.VIPs[0].NetworkInterfaces
	vipCidrParse := strings.Split(vipCidr, "/")
	vip, mast := vipCidrParse[0], vipCidrParse[1]
	maskInt, err := strconv.Atoi(mast)
	if err != nil {
		return err
	}
	if createResponse.NetworkConfigs.ApiServerVIP == nil {
		createResponse.NetworkConfigs.ApiServerVIP = new(clustermodel.CreateApiServerVipConfigResponse)
	}
	createResponse.NetworkConfigs.ApiServerVIP.Address = vip
	createResponse.NetworkConfigs.ApiServerVIP.Mask = maskInt
	createResponse.NetworkConfigs.ApiServerVIP.NetworkCard = networkCard
	return nil
}

// 将property 的内容渲染到 create status response
func (step apiServerLbConfigJobParamStep) renderCreateStatusResponse(property map[string]interface{}, createResponse *clustermodel.CreateStatusResponse) error {
	// nothing to do
	return nil
}

// 将property 的内容渲染到 create response
func (step apiServerLbConfigJobParamStep) renderResponse(property map[string]interface{}, response *clustermodel.Response) error {
	// nothing to do
	return nil
}

type calicoNetworkConfigJobParamStep struct {
}

func newCalicoNetworkConfigJobParamStep() jobParamStep {
	return calicoNetworkConfigJobParamStep{}
}

// 获取步骤名称
func (calicoNetworkConfigJobParamStep) name() string {
	return jobParamNameCalicoNetworkConfig
}

// 判断步骤是否需要转化 如没有macvlan 就不需要转化macvlan 的请求参数
func (calicoNetworkConfigJobParamStep) shouldConvert(createRequest clustermodel.CreateRequest) (bool, error) {
	_, exist := createRequest.NetworkConfigs.CNIs[clustermodel.CNITypeCalico]
	return exist, nil
}

// 转化请求参数
func (calicoNetworkConfigJobParamStep) convert(createRequest clustermodel.CreateRequest) (map[string]interface{}, error) {
	cniConfig := createRequest.NetworkConfigs.CNIs[clustermodel.CNITypeCalico]
	ipRequestFunc := func(src map[string]interface{}) (*clustermodel.CreateCNIConfigCalicoParamRequest, error) {
		var target *clustermodel.CreateCNIConfigCalicoParamRequest
		if src != nil {
			target = new(clustermodel.CreateCNIConfigCalicoParamRequest)
			if err := utils.BeanCopy(src, target); err != nil {
				return nil, err
			}
			if err := target.Validator(); err != nil {
				return nil, err
			}
		}
		return target, nil
	}
	var ipv4Request *clustermodel.CreateCNIConfigCalicoParamRequest
	var ipv6Request *clustermodel.CreateCNIConfigCalicoParamRequest
	var requestErr error
	if ipv4Request, requestErr = ipRequestFunc(cniConfig.Ipv4Param); requestErr != nil {
		return nil, requestErr
	}
	if ipv6Request, requestErr = ipRequestFunc(cniConfig.Ipv6Param); requestErr != nil {
		return nil, requestErr
	}
	var v4Cidr, v6Cidr string
	if ipv4Request != nil {
		v4Cidr = strings.ToLower(ipv4Request.CIDR)
	}
	if ipv6Request != nil {
		v6Cidr = strings.ToLower(ipv6Request.CIDR)
	}
	return newCalicoNetworkMode(v4Cidr, v6Cidr).asProperty()
}

// 将property 的内容渲染到 create response
func (calicoNetworkConfigJobParamStep) renderCreateResponse(property map[string]interface{}, createResponse *clustermodel.CreateResponse) error {
	// 初始化必要数据
	createResponse.NetworkConfigs.PlatformCNITypes = append(createResponse.NetworkConfigs.PlatformCNITypes, clustermodel.CNITypeCalico)
	if createResponse.NetworkConfigs.CNIs == nil {
		createResponse.NetworkConfigs.CNIs = make(map[clustermodel.CNIType]clustermodel.CreateCNIConfigResponse)
	}
	calicoNetworkModel := networkModel{}
	if err := utils.BeanCopy(property, &calicoNetworkModel); err != nil {
		return err
	}
	v4Cidr := calicoNetworkModel.Network.Calico.IPv4CIDR
	v6Cidr := calicoNetworkModel.Network.Calico.IPv6CIDR
	var dualStack bool
	if v4Cidr != "" && v6Cidr != "" {
		dualStack = true
	}
	asParamFunc := func(cidr string) map[string]interface{} {
		var result map[string]interface{}
		if cidr == "" {
			return result
		}
		var model clustermodel.CreateCNIConfigCalicoParamRequest
		model.CIDR = cidr
		_ = utils.BeanCopy(model, &result)
		return result
	}

	createResponse.NetworkConfigs.CNIs[clustermodel.CNITypeCalico] = clustermodel.CreateCNIConfigResponse{
		DualStack: dualStack,
		Ipv4Param: asParamFunc(v4Cidr),
		Ipv6Param: asParamFunc(v6Cidr),
	}
	return nil
}

// 将property 的内容渲染到 create status response
func (calicoNetworkConfigJobParamStep) renderCreateStatusResponse(property map[string]interface{}, createsStatusResponse *clustermodel.CreateStatusResponse) error {
	createsStatusResponse.CNITypes = append(createsStatusResponse.CNITypes, clustermodel.CNITypeCalico)
	return nil
}

// 将property 的内容渲染到 create response
func (calicoNetworkConfigJobParamStep) renderResponse(property map[string]interface{}, response *clustermodel.Response) error {
	response.CNITypes = append(response.CNITypes, clustermodel.CNITypeCalico)
	return nil
}

type macvlanNetworkConfigJobParamStep struct {
}

func newMacvlanNetworkConfigJobParamStep() jobParamStep {
	return macvlanNetworkConfigJobParamStep{}
}

// 获取步骤名称
func (macvlanNetworkConfigJobParamStep) name() string {
	return jobParamNameMacvlanNetworkConfig
}

// 判断步骤是否需要转化 如没有macvlan 就不需要转化macvlan 的请求参数
func (macvlanNetworkConfigJobParamStep) shouldConvert(createRequest clustermodel.CreateRequest) (bool, error) {
	_, exist := createRequest.NetworkConfigs.CNIs[clustermodel.CNITypeMacvlan]
	return exist, nil
}

// 转化请求参数
func (macvlanNetworkConfigJobParamStep) convert(createRequest clustermodel.CreateRequest) (map[string]interface{}, error) {
	cniConfig := createRequest.NetworkConfigs.CNIs[clustermodel.CNITypeMacvlan]
	ipRequestFunc := func(src map[string]interface{}) (*clustermodel.CreateCNIConfigMacvlanParamRequest, error) {
		var target *clustermodel.CreateCNIConfigMacvlanParamRequest
		if src != nil {
			target = new(clustermodel.CreateCNIConfigMacvlanParamRequest)
			if err := utils.BeanCopy(src, target); err != nil {
				return nil, err
			}
			if err := target.Validator(); err != nil {
				return nil, err
			}
		}
		return target, nil
	}
	var ipv4Request *clustermodel.CreateCNIConfigMacvlanParamRequest
	var ipv6Request *clustermodel.CreateCNIConfigMacvlanParamRequest
	var requestErr error
	if ipv4Request, requestErr = ipRequestFunc(cniConfig.Ipv4Param); requestErr != nil {
		return nil, requestErr
	}
	if ipv6Request, requestErr = ipRequestFunc(cniConfig.Ipv6Param); requestErr != nil {
		return nil, requestErr
	}
	subnetConvertFunc := func(request *clustermodel.CreateCNIConfigMacvlanParamRequest) *MacvlanSubnet {
		var target *MacvlanSubnet
		if request != nil {
			target = new(MacvlanSubnet)
			target.StartIp = strings.ToLower(request.StartIP)
			target.EndIp = strings.ToLower(request.EndIP)
			target.Gateway = strings.ToLower(request.Gateway)
			target.Prefix = request.Mask
			// 校验在上一步完成
			reservedIpList, _ := request.ReservedIpList()
			if len(reservedIpList) > 0 {
				for _, ipSec := range reservedIpList {
					target.ExcludeIPs = append(target.ExcludeIPs, MacvlanExcludeIP{
						StartIp: strings.ToLower(ipSec[0]),
						EndIp:   strings.ToLower(ipSec[1]),
					})
				}
			}
		}
		return target
	}
	return newMacvlanNetworkConfig(ipv4Request.NetworkCard, ipv4Request.VlanId, subnetConvertFunc(ipv4Request), subnetConvertFunc(ipv6Request)).asProperty()
}

// 将property 的内容渲染到 create response
func (macvlanNetworkConfigJobParamStep) renderCreateResponse(property map[string]interface{}, createResponse *clustermodel.CreateResponse) error {
	// 初始化必要数据
	createResponse.NetworkConfigs.PlatformCNITypes = append(createResponse.NetworkConfigs.PlatformCNITypes, clustermodel.CNITypeMacvlan)
	if createResponse.NetworkConfigs.CNIs == nil {
		createResponse.NetworkConfigs.CNIs = make(map[clustermodel.CNIType]clustermodel.CreateCNIConfigResponse)
	}

	macvlanNetworkModel := networkModel{}
	if err := utils.BeanCopy(property, &macvlanNetworkModel); err != nil {
		return err
	}
	dualStack := macvlanNetworkModel.Network.Macvlan.SubnetInfo.Ipv4 != nil && macvlanNetworkModel.Network.Macvlan.SubnetInfo.Ipv6 != nil
	asParamFunc := func(subnet *MacvlanSubnet, networkCard string, vlanId int) map[string]interface{} {
		var result map[string]interface{}
		if subnet == nil {
			return result
		}
		var param clustermodel.CreateCNIConfigMacvlanParamRequest
		param.StartIP = subnet.StartIp
		param.EndIP = subnet.EndIp
		param.Gateway = subnet.Gateway
		param.Mask = subnet.Prefix
		param.VlanId = vlanId
		param.NetworkCard = networkCard
		if len(subnet.ExcludeIPs) != 0 {
			var ipSecList []string
			for index, _ := range subnet.ExcludeIPs {
				ipSecList = append(ipSecList, strings.Join([]string{subnet.ExcludeIPs[index].StartIp, subnet.ExcludeIPs[index].EndIp}, "~"))
			}
			param.ReservedIp = strings.Join(ipSecList, ", ")
		}
		_ = utils.BeanCopy(param, &result)
		return result
	}

	createResponse.NetworkConfigs.CNIs[clustermodel.CNITypeMacvlan] = clustermodel.CreateCNIConfigResponse{
		DualStack: dualStack,
		Ipv4Param: asParamFunc(macvlanNetworkModel.Network.Macvlan.SubnetInfo.Ipv4, macvlanNetworkModel.Network.Macvlan.NetworkCard, macvlanNetworkModel.Network.Macvlan.Vlanid),
		Ipv6Param: asParamFunc(macvlanNetworkModel.Network.Macvlan.SubnetInfo.Ipv6, macvlanNetworkModel.Network.Macvlan.NetworkCard, macvlanNetworkModel.Network.Macvlan.Vlanid),
	}
	return nil
}

// 将property 的内容渲染到 create status response
func (macvlanNetworkConfigJobParamStep) renderCreateStatusResponse(property map[string]interface{}, createsStatusResponse *clustermodel.CreateStatusResponse) error {
	createsStatusResponse.CNITypes = append(createsStatusResponse.CNITypes, clustermodel.CNITypeMacvlan)
	return nil
}

// 将property 的内容渲染到 create response
func (macvlanNetworkConfigJobParamStep) renderResponse(property map[string]interface{}, response *clustermodel.Response) error {
	response.CNITypes = append(response.CNITypes, clustermodel.CNITypeMacvlan)
	return nil
}

type kubeOVNNetworkConfigJobParamStep struct {
}

func newKubeOVNNetworkConfigJobParamStep() jobParamStep {
	return kubeOVNNetworkConfigJobParamStep{}
}

// 获取步骤名称
func (kubeOVNNetworkConfigJobParamStep) name() string {
	return jobParamNameKubeOVNNetworkConfig
}

// 判断步骤是否需要转化 如没有macvlan 就不需要转化macvlan 的请求参数
func (kubeOVNNetworkConfigJobParamStep) shouldConvert(createRequest clustermodel.CreateRequest) (bool, error) {
	_, exist := createRequest.NetworkConfigs.CNIs[clustermodel.CNITypeKubeOVM]
	return exist, nil
}

// 转化请求参数
func (kubeOVNNetworkConfigJobParamStep) convert(createRequest clustermodel.CreateRequest) (map[string]interface{}, error) {
	var obj clustermodel.CreateCNIConfigKubeOVNParamRequest
	if err := utils.BeanCopy(createRequest.NetworkConfigs.CNIs[clustermodel.CNITypeKubeOVM].Ipv4Param, &obj); err != nil {
		return nil, err
	}
	if err := obj.Validator(); err != nil {
		return nil, err
	}
	return newKubeOvnNetworkConfig(obj.NetworkCard).asProperty()
}

// 将property 的内容渲染到 create response
func (kubeOVNNetworkConfigJobParamStep) renderCreateResponse(property map[string]interface{}, createResponse *clustermodel.CreateResponse) error {
	// 初始化必要数据
	createResponse.NetworkConfigs.PlatformCNITypes = append(createResponse.NetworkConfigs.PlatformCNITypes, clustermodel.CNITypeKubeOVM)
	if createResponse.NetworkConfigs.CNIs == nil {
		createResponse.NetworkConfigs.CNIs = make(map[clustermodel.CNIType]clustermodel.CreateCNIConfigResponse)
	}
	kubeOvnModel := kubeOvnModel{}
	if err := utils.BeanCopy(property, &kubeOvnModel); err != nil {
		return err
	}

	var ipV4Param map[string]interface{}
	var ipV4ParamStrct = clustermodel.CreateCNIConfigKubeOVNParamRequest{
		NetworkCard: kubeOvnModel.Kubevirt.Ovn.NetworkCard,
	}
	if err := utils.BeanCopy(ipV4ParamStrct, &ipV4Param); err != nil {
		return err
	}

	createResponse.NetworkConfigs.CNIs[clustermodel.CNITypeKubeOVM] = clustermodel.CreateCNIConfigResponse{
		DualStack: false,
		Ipv4Param: ipV4Param,
	}
	return nil
}

// 将property 的内容渲染到 create response
func (kubeOVNNetworkConfigJobParamStep) renderResponse(property map[string]interface{}, response *clustermodel.Response) error {
	response.CNITypes = append(response.CNITypes, clustermodel.CNITypeKubeOVM)
	return nil
}

// 将property 的内容渲染到 create status response
func (kubeOVNNetworkConfigJobParamStep) renderCreateStatusResponse(property map[string]interface{}, createsStatusResponse *clustermodel.CreateStatusResponse) error {
	createsStatusResponse.CNITypes = append(createsStatusResponse.CNITypes, clustermodel.CNITypeKubeOVM)
	return nil
}

type standerComponentConfigJobParamStep struct {
}

func newStanderComponentConfigJobParamStep() jobParamStep {
	return standerComponentConfigJobParamStep{}
}

// 获取步骤名称
func (standerComponentConfigJobParamStep) name() string {
	return jobParamNameStanderComponentConfig
}

// 判断步骤是否需要转化 如没有macvlan 就不需要转化macvlan 的请求参数
func (standerComponentConfigJobParamStep) shouldConvert(createRequest clustermodel.CreateRequest) (bool, error) {
	return true, nil
}

// 转化请求参数
func (standerComponentConfigJobParamStep) convert(createRequest clustermodel.CreateRequest) (map[string]interface{}, error) {
	return newStanderComponentConfigModel().asProperty()
}

// 将property 的内容渲染到 create response
func (standerComponentConfigJobParamStep) renderCreateResponse(property map[string]interface{}, createResponse *clustermodel.CreateResponse) error {
	// 标准版组件配置 不需要处理
	return nil
}

// 将property 的内容渲染到 create status response
func (standerComponentConfigJobParamStep) renderCreateStatusResponse(property map[string]interface{}, createResponse *clustermodel.CreateStatusResponse) error {
	// nothing to do
	return nil
}

// 将property 的内容渲染到 create response
func (standerComponentConfigJobParamStep) renderResponse(property map[string]interface{}, response *clustermodel.Response) error {
	// nothing to do
	return nil
}

type commonParamConfigJobParamStep struct {
}

func newCommonParamConfigJobParamStep() jobParamStep {
	return commonParamConfigJobParamStep{}
}

// 获取步骤名称
func (commonParamConfigJobParamStep) name() string {
	return jobParamNameCommonParamConfig
}

// 判断步骤是否需要转化 如没有macvlan 就不需要转化macvlan 的请求参数
func (commonParamConfigJobParamStep) shouldConvert(createRequest clustermodel.CreateRequest) (bool, error) {
	return true, nil
}

// 转化请求参数
func (commonParamConfigJobParamStep) convert(createRequest clustermodel.CreateRequest) (map[string]interface{}, error) {
	// 处理 param api-server
	var apiServer commonApiServer
	switch {
	case createRequest.NodeConfigs.NodeConfigType == clustermodel.NodeConfigTypeStandardHA:
		fallthrough
	case createRequest.NodeConfigs.NodeConfigType == clustermodel.NodeConfigTypeMinimizeHA:
		// 如果是高可用 则填写api-server VIP
		scheme, err := parseWebScheme(createRequest.NetworkConfigs.ApiServerVIP.Protocol)
		if err != nil {
			return nil, err
		}
		apiServer.Scheme = scheme
		apiServer.Address = createRequest.NetworkConfigs.ApiServerVIP.Address
		apiServer.Port = createRequest.NetworkConfigs.ApiServerVIP.Port

	default:
		// 如果非高可用 从节点列表选择一台master
		masterNode := createRequest.NodeConfigs.Nodes.Master()
		if len(masterNode) == 0 {
			return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, "master node size is 0")
		}
		apiServer.Scheme = commonApiServerSchemeHttps
		apiServer.Address = masterNode[0].Ip
		apiServer.Port = 6443
	}

	// 处理 ingressIP
	var ingressIP string
	switch {
	case createRequest.NodeConfigs.NodeConfigType == clustermodel.NodeConfigTypeStandardHA:
		fallthrough
	case createRequest.NodeConfigs.NodeConfigType == clustermodel.NodeConfigTypeMinimizeHA:
		// 如果是高可用 则使用 ingress-vip
		ingressIP = createRequest.NetworkConfigs.LoadBalance.Address
	default:
		// 如果非高可用 用第一台 默认负载均衡节点
		hostGroups, err := parseSisyphusHostGroups(createRequest)
		if err != nil {
			return nil, err
		}
		for _, host := range hostGroups {
			host := host
			if len(host.Groups) == 0 {
				continue
			}
			groups := sets.New[string](host.Groups...)
			if groups.Has(config.CreateClusterConfig.GroupKeyDefaultLb) {
				ingressIP = host.IP
				break
			}
		}
		if ingressIP == "" {
			return nil, errors.NewFromCode(errors.Var.DefaultLbNotExist)
		}

	}
	// 处理registry
	var registry *commonRegistry
	if createRequest.Registry != nil {
		registry = new(commonRegistry)
		scheme, err := parseWebScheme(createRequest.Registry.Protocol)
		if err != nil {
			return nil, err
		}
		registry.Scheme = scheme
		registry.Address = createRequest.Registry.Address
		registry.Port = createRequest.Registry.Port
	}
	// 处理平台ingress 访问地址
	var platformIngressIp string
	platformIngressIp = createRequest.HubClusterIngressAddress
	// 处理集群名称
	var clusterName string
	clusterName = createRequest.ClusterName
	// 处理stellaris
	var stellariesAddress []string
	stellariesAddress = append(stellariesAddress, fmt.Sprintf("%s:%d", createRequest.StellarisComponent.Address, createRequest.StellarisComponent.Port))
	if createRequest.StellarisComponent.StandbyAddress != nil && createRequest.StellarisComponent.StandbyPort != nil {
		stellariesAddress = append(stellariesAddress, fmt.Sprintf("%s:%d", *createRequest.StellarisComponent.StandbyAddress, *createRequest.StellarisComponent.StandbyPort))
	}
	return newCommonParamModel(apiServer, ingressIP, registry, platformIngressIp, clusterName, stellariesAddress).asProperty()
}

// 将property 的内容渲染到 create response
func (commonParamConfigJobParamStep) renderCreateResponse(property map[string]interface{}, createResponse *clustermodel.CreateResponse) error {
	var commonParamModel commonParamModel
	if err := utils.BeanCopy(property, &commonParamModel); err != nil {
		return err
	}
	// 处理 param api-server
	if createResponse.NetworkConfigs.ApiServerVIP == nil {
		createResponse.NetworkConfigs.ApiServerVIP = new(clustermodel.CreateApiServerVipConfigResponse)
	}
	apiServerVipPtr := createResponse.NetworkConfigs.ApiServerVIP
	apiServerVipPtr.Protocol = string(commonParamModel.Common.ApiServer.Scheme)
	apiServerVipPtr.Port = commonParamModel.Common.ApiServer.Port

	// ingress 这里不做处理 在 默认负载均衡的上层负载均衡配置

	// 处理registry
	createResponse.RegistryType = append(createResponse.RegistryType, clustermodel.RegistryTypeDefault)
	if commonParamModel.Common.Registry != nil {
		createResponse.RegistryType = append(createResponse.RegistryType, clustermodel.RegistryTypeCustom)
		createResponse.Registry = &clustermodel.CreateRegistryConfigResponse{
			Protocol: string(commonParamModel.Common.Registry.Scheme),
			Address:  commonParamModel.Common.Registry.Address,
			Port:     commonParamModel.Common.Registry.Port,
		}
	}
	// 处理平台ingress 访问地址
	createResponse.HubClusterIngressAddress = commonParamModel.Common.Skyview.Ip
	// 处理 stellarisComponent
	coreAddress := commonParamModel.Common.Stellaris.CoreAddresses
	addrFunction := func(addr string) (string, int) {
		arr := strings.Split(addr, ":")
		switch len(arr) {
		case 0:
			return "", 0
		case 1:
			arr = append(arr, "80")
		}
		hostArr := arr[0 : len(arr)-1]
		hostStr := strings.Join(hostArr, ":")
		portStr := arr[len(arr)-1]
		port, _ := strconv.Atoi(portStr)
		return hostStr, port
	}
	switch {
	case len(coreAddress) > 1:
		standbyAddr := coreAddress[1]
		host, port := addrFunction(standbyAddr)
		createResponse.StellarisComponent.StandbyAddress = &host
		createResponse.StellarisComponent.StandbyPort = &port
		fallthrough
	case len(coreAddress) == 1:
		addr := coreAddress[0]
		host, port := addrFunction(addr)
		createResponse.StellarisComponent.Address = host
		createResponse.StellarisComponent.Port = port
	}
	return nil
}

// 将property 的内容渲染到 create status response
func (commonParamConfigJobParamStep) renderCreateStatusResponse(property map[string]interface{}, createResponse *clustermodel.CreateStatusResponse) error {
	// nothing to do
	return nil
}

// 将property 的内容渲染到 create response
func (commonParamConfigJobParamStep) renderResponse(property map[string]interface{}, response *clustermodel.Response) error {
	// nothing to do
	return nil
}

type containerArrangementAndRuntimeParamConfigJobParamStep struct {
}

func newContainerArrangementAndRuntimeParamConfigJobParamStep() jobParamStep {
	return containerArrangementAndRuntimeParamConfigJobParamStep{}
}

// 获取步骤名称
func (containerArrangementAndRuntimeParamConfigJobParamStep) name() string {
	return jobParamNameContainerArrangementAndRuntimeParam
}

// 判断步骤是否需要转化 如没有macvlan 就不需要转化macvlan 的请求参数
func (containerArrangementAndRuntimeParamConfigJobParamStep) shouldConvert(createRequest clustermodel.CreateRequest) (bool, error) {
	return true, nil
}

// 转化请求参数
func (containerArrangementAndRuntimeParamConfigJobParamStep) convert(createRequest clustermodel.CreateRequest) (map[string]interface{}, error) {
	return NewContainerArrangementAndRuntimeParam().asProperty()
}

// 将property 的内容渲染到 create response
func (containerArrangementAndRuntimeParamConfigJobParamStep) renderCreateResponse(property map[string]interface{}, createResponse *clustermodel.CreateResponse) error {
	// nothing to do
	return nil
}

// 将property 的内容渲染到 create status response
func (containerArrangementAndRuntimeParamConfigJobParamStep) renderCreateStatusResponse(property map[string]interface{}, createResponse *clustermodel.CreateStatusResponse) error {
	// nothing to do
	return nil
}

// 将property 的内容渲染到 create response
func (containerArrangementAndRuntimeParamConfigJobParamStep) renderResponse(property map[string]interface{}, response *clustermodel.Response) error {
	// nothing to do
	return nil
}

type defaultLbParentLbConfigJobParamStep struct {
	shouldConvertNodeConfigType sets.Set[clustermodel.NodeConfigType]
}

func newDefaultLbParentLbConfigJobParamStep() jobParamStep {
	return defaultLbParentLbConfigJobParamStep{
		// 只有最小化高可用 和标准高可用 才需要转化
		shouldConvertNodeConfigType: sets.New[clustermodel.NodeConfigType](clustermodel.NodeConfigTypeMinimizeHA, clustermodel.NodeConfigTypeStandardHA),
	}
}

// 获取步骤名称
func (defaultLbParentLbConfigJobParamStep) name() string {
	return jobParamNameDefaultLbParentLbConfig
}

// 判断步骤是否需要转化 如没有macvlan 就不需要转化macvlan 的请求参数
func (step defaultLbParentLbConfigJobParamStep) shouldConvert(createRequest clustermodel.CreateRequest) (bool, error) {
	flag := step.shouldConvertNodeConfigType.Has(createRequest.NodeConfigs.NodeConfigType)
	return flag, nil
}

// 转化请求参数
func (defaultLbParentLbConfigJobParamStep) convert(createRequest clustermodel.CreateRequest) (map[string]interface{}, error) {
	return newDefaultLbParentLbModel(createRequest.NetworkConfigs.LoadBalance.AsVIP(), "120", createRequest.NetworkConfigs.LoadBalance.NetworkCard).asProperty()
}

// 将property 的内容渲染到 create response
func (defaultLbParentLbConfigJobParamStep) renderCreateResponse(property map[string]interface{}, createResponse *clustermodel.CreateResponse) error {
	var lbModel defaultLbParentLbModel
	if err := utils.BeanCopy(property, &lbModel); err != nil {
		return err
	}
	networkCard := lbModel.VIP.Ingress.VIPs[0].NetworkInterfaces
	cidr := lbModel.VIP.Ingress.VIPs[0].Vips[0]
	cidrSplit := strings.Split(cidr, "/")
	ip, maskStr := cidrSplit[0], cidrSplit[1]
	maskInt, _ := strconv.Atoi(maskStr)
	createResponse.NetworkConfigs.LoadBalance = &clustermodel.CreateLbConfigResponse{
		Address:     ip,
		Mask:        maskInt,
		NetworkCard: networkCard,
	}
	return nil
}

// 将property 的内容渲染到 create status response
func (defaultLbParentLbConfigJobParamStep) renderCreateStatusResponse(property map[string]interface{}, createResponse *clustermodel.CreateStatusResponse) error {
	// nothing to do
	return nil
}

// 将property 的内容渲染到 create response
func (defaultLbParentLbConfigJobParamStep) renderResponse(property map[string]interface{}, response *clustermodel.Response) error {
	// nothing to do
	return nil
}

func NewStorageClassConfigJobParamStep() jobParamStep {
	return StorageClassConfigJobParamStep{}
}

type StorageClassConfigJobParamStep struct {
}

// 获取步骤名称
func (StorageClassConfigJobParamStep) name() string {
	return jobParamNameStorageClassConfig
}

// 判断步骤是否需要转化 如没有macvlan 就不需要转化macvlan 的请求参数
func (StorageClassConfigJobParamStep) shouldConvert(createRequest clustermodel.CreateRequest) (bool, error) {
	return true, nil
}

// 转化请求参数
func (StorageClassConfigJobParamStep) convert(createRequest clustermodel.CreateRequest) (map[string]interface{}, error) {
	return NewStorageProperty("caas-lvm").asProperty()
}

// 将property 的内容渲染到 create response
func (StorageClassConfigJobParamStep) renderCreateResponse(property map[string]interface{}, createResponse *clustermodel.CreateResponse) error {
	return nil
}

// 将property 的内容渲染到 create response
func (StorageClassConfigJobParamStep) renderResponse(property map[string]interface{}, response *clustermodel.Response) error {
	return nil
}

// 将property 的内容渲染到 create status response
func (StorageClassConfigJobParamStep) renderCreateStatusResponse(property map[string]interface{}, createResponse *clustermodel.CreateStatusResponse) error {
	return nil
}

func NewDataImportJobParamStepJobParamStep() jobParamStep {
	return DataImportJobParamStep{}
}

type DataImportJobParamStep struct {
}

// 获取步骤名称
func (DataImportJobParamStep) name() string {
	return jobParamNameDateImport
}

// 判断步骤是否需要转化 如没有macvlan 就不需要转化macvlan 的请求参数
func (DataImportJobParamStep) shouldConvert(createRequest clustermodel.CreateRequest) (bool, error) {
	return true, nil
}

// 转化请求参数
func (DataImportJobParamStep) convert(createRequest clustermodel.CreateRequest) (map[string]interface{}, error) {
	solutionInfo, exist := config.CreateClusterConfig.SolutionInfos.FindByKubernetesAndCRIVersion(createRequest.KubernetesVersion, createRequest.KubernetesCRIVersion)
	if !exist {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, fmt.Sprintf("can not find solution by kubernetesVersion:%s,kubernetesCRIVersion:%s", createRequest.KubernetesVersion, createRequest.KubernetesCRIVersion))
	}
	return NewDataImportModel(solutionInfo.DataImport.NodeUpDown, solutionInfo.DataImport.ResetNode).asProperty()
}

// 将property 的内容渲染到 create response
func (DataImportJobParamStep) renderCreateResponse(property map[string]interface{}, createResponse *clustermodel.CreateResponse) error {
	return nil
}

// 将property 的内容渲染到 create response
func (DataImportJobParamStep) renderResponse(property map[string]interface{}, response *clustermodel.Response) error {
	return nil
}

// 将property 的内容渲染到 create status response
func (DataImportJobParamStep) renderCreateStatusResponse(property map[string]interface{}, createResponse *clustermodel.CreateStatusResponse) error {
	return nil
}
