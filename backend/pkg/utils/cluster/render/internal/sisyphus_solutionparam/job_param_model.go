package sisyphus_solutionparam

import (
	"fmt"

	sysconfig "harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

func newApiServerLbModel(vip string, vrid string, networkCard string) jobParamStepProperty {
	return apiServerLbModel{
		VIP: apiServerLbVIP{
			ApiServer: apiServerLbApiServer{
				VIPs: []apiServerLbVIPInfo{
					{
						Vips:              []string{vip},
						Vrid:              vrid,
						NetworkInterfaces: networkCard,
					},
				},
			},
		},
	}
}

type apiServerLbModel struct {
	VIP apiServerLbVIP `json:"vip"`
}

func (obj apiServerLbModel) asProperty() (map[string]interface{}, error) {
	var result = make(map[string]interface{})
	var err = utils.BeanCopy(obj, &result)
	return result, err
}

type apiServerLbVIP struct {
	ApiServer apiServerLbApiServer `json:"apiserver"`
}

type apiServerLbApiServer struct {
	VIPs []apiServerLbVIPInfo `json:"vips"`
}
type apiServerLbVIPInfo struct {
	Vips              []string `json:"vips"`
	Vrid              string   `json:"vrid"`
	NetworkInterfaces string   `json:"interface"`
}

func newCalicoNetworkMode(v4Cidr, v6Cidr string) jobParamStepProperty {
	var v4Method, v6Method string
	if v4Cidr != "" {
		v4Method = "can-reach=*******"
	}
	if v6Cidr != "" {
		v6Method = "first-found"
	}
	return networkModel{
		Network: NetworkNetwork{
			Calico: &CalicoParam{
				IPv4CIDR:                v4Cidr,
				IPv6CIDR:                v6Cidr,
				Ipv4AutodetectionMethod: v4Method,
				Ipv6AutodetectionMethod: v6Method,
			},
		},
	}
}
func newKubeOvnNetworkConfig(networkCard string) jobParamStepProperty {
	return kubeOvnModel{
		Kubevirt: kubeVirt{
			Ovn: kubeOvnNetwork{
				NetworkCard: networkCard,
			},
		},
	}
}

type kubeOvnModel struct {
	Kubevirt kubeVirt `json:"kubevirt"`
}

func (obj kubeOvnModel) asProperty() (map[string]interface{}, error) {
	var result = make(map[string]interface{})
	var err = utils.BeanCopy(obj, &result)
	return result, err
}

type kubeVirt struct {
	Ovn kubeOvnNetwork `json:"ovn"`
}
type kubeOvnNetwork struct {
	NetworkCard string `json:"interface"`
}

func newMacvlanNetworkConfig(networkCard string, vlanId int, v4, v6 *MacvlanSubnet) jobParamStepProperty {
	return networkModel{
		Network: NetworkNetwork{
			Macvlan: &MacvlanParam{
				Vlanid:      vlanId,
				NetworkCard: networkCard,
				SubnetInfo: MacvlanSubnetInfo{
					Ipv4: v4,
					Ipv6: v6,
				},
			},
		},
	}
}

type networkModel struct {
	Network NetworkNetwork `json:"network"`
}

func (obj networkModel) asProperty() (map[string]interface{}, error) {
	var result = make(map[string]interface{})
	var err = utils.BeanCopy(obj, &result)
	return result, err
}

type NetworkNetwork struct {
	Calico  *CalicoParam  `json:"calico,omitempty"`
	Macvlan *MacvlanParam `json:"bifrost,omitempty"`
}
type MacvlanParam struct {
	Vlanid      int               `json:"vlanid"`
	NetworkCard string            `json:"interface"`
	SubnetInfo  MacvlanSubnetInfo `json:"subnet"`
}
type MacvlanSubnetInfo struct {
	Ipv4 *MacvlanSubnet `json:"v4,omitempty"`
	Ipv6 *MacvlanSubnet `json:"v6,omitempty"`
}
type MacvlanSubnet struct {
	StartIp    string             `json:"start"`
	EndIp      string             `json:"end"`
	Gateway    string             `json:"gateway"`
	Prefix     int                `json:"prefix"`
	ExcludeIPs []MacvlanExcludeIP `json:"excludeIPs,omitempty"`
}
type MacvlanExcludeIP struct {
	StartIp string `json:"start"`
	EndIp   string `json:"end"`
}
type CalicoParam struct {
	IPv4CIDR                string `json:"IPv4CIDR,omitempty"`
	IPv6CIDR                string `json:"IPv6CIDR,omitempty"`
	Ipv4AutodetectionMethod string `json:"ipv4AutodetectionMethod,omitempty"`
	Ipv6AutodetectionMethod string `json:"ipv6AutodetectionMethod,omitempty"`
}

type standerComponentConfigModel struct {
	KubeEventer standerComponentConfigModelKubeEventer `json:"kubeEventer"`
	Prometheus  standerComponentConfigModelPrometheus  `json:"prometheus"`
}
type standerComponentConfigModelKubeEventer struct {
	Retention int `json:"retention"`
}

func newStanderComponentConfigModel() jobParamStepProperty {
	return standerComponentConfigModel{
		KubeEventer: standerComponentConfigModelKubeEventer{
			Retention: 15,
		},
		Prometheus: standerComponentConfigModelPrometheus{
			Prometheus: prometheusPrometheus{
				Retention: "120h",
			},
			AlertManager: prometheusAlertManager{
				Webhook: alertWebhook{
					SkyviewWebhook: fmt.Sprintf("http://%s/olympus-oam/apis/v1/alertCenter/webhook", sysconfig.TopClusterIngressAddress.Value),
				},
			},
		},
	}
}

func (obj standerComponentConfigModel) asProperty() (map[string]interface{}, error) {
	var result = make(map[string]interface{})
	var err = utils.BeanCopy(obj, &result)
	return result, err
}

type standerComponentConfigModelPrometheus struct {
	Prometheus   prometheusPrometheus   `json:"prometheus"`
	AlertManager prometheusAlertManager `json:"alertmanager"`
}
type prometheusPrometheus struct {
	Retention string `json:"retention"`
}
type prometheusAlertManager struct {
	Webhook alertWebhook `json:"webhook"`
}
type alertWebhook struct {
	SkyviewWebhook string `json:"skyviewWebhook"`
}

func NewContainerArrangementAndRuntimeParam() jobParamStepProperty {
	return containerArrangementAndRuntimeParam{
		Docker: docker{
			Bip:          "**********/26",
			CGroupDriver: "systemd",
			Log: dockerLog{
				MaxFile: "10",
				MaxSize: "100m",
			},
		},
		Kubernetes: kubernetes{
			ApiserverAuditMaxage:    5,
			ApiserverAuditMaxbackup: 10,
			ApiserverAuditMaxsize:   1024,

			ClusterDNS:            "**********",
			MaxPods:               110,
			ClusterServiceIpRange: "*********/12",
		},
	}
}

type containerArrangementAndRuntimeParam struct {
	Kubernetes kubernetes `json:"kubernetes"`
	Docker     docker     `json:"docker"`
}

func (obj containerArrangementAndRuntimeParam) asProperty() (map[string]interface{}, error) {
	var result = make(map[string]interface{})
	var err = utils.BeanCopy(obj, &result)
	return result, err
}

func newCommonParamModel(apiServer commonApiServer, ingressIp string, registry *commonRegistry, platformIngressIp string, clusterName string, stellarisAddress []string) jobParamStepProperty {
	return commonParamModel{
		Common: commonCommon{
			ApiServer: apiServer,
			Ingress: commonIngress{
				IngressIP: ingressIp,
			},
			Registry: registry,
			Skyview: commonSkyview{
				Ip: platformIngressIp,
			},
			Stellaris: commonStellaries{
				CoreAddresses: stellarisAddress,
			},
			ClusterName:      clusterName,
			TotalCaasLVMSize: 150,
		},
	}
}

type commonParamModel struct {
	Common commonCommon `json:"common"`
}

func (obj commonParamModel) asProperty() (map[string]interface{}, error) {
	var result = make(map[string]interface{})
	var err = utils.BeanCopy(obj, &result)
	return result, err
}

type kubernetes struct {
	ApiserverAuditMaxage    int    `json:"apiserver_audit_maxage"`
	ApiserverAuditMaxbackup int    `json:"apiserver_audit_maxbackup"`
	ApiserverAuditMaxsize   int    `json:"apiserver_audit_maxsize"`
	ClusterDNS              string `json:"cluster_dns"`
	MaxPods                 int    `json:"max_pods"`
	ClusterServiceIpRange   string `json:"service_cluster_ip_range"`
}

type docker struct {
	Bip          string    `json:"bip"`
	CGroupDriver string    `json:"cgroupdriver"`
	Log          dockerLog `json:"log"`
}

type dockerLog struct {
	MaxFile string `json:"max_file"`
	MaxSize string `json:"max_size"`
}

type commonCommon struct {
	ApiServer        commonApiServer  `json:"apiserver"`
	Ingress          commonIngress    `json:"ingress"`
	Registry         *commonRegistry  `json:"registry,omitempty"`
	Skyview          commonSkyview    `json:"skyview"`
	Stellaris        commonStellaries `json:"stellaris"`
	ClusterName      string           `json:"clusterName"`
	TotalCaasLVMSize int              `json:"totalCaasLVMSize"`
}

type commonStellaries struct {
	CoreAddresses []string `json:"coreAddrs"`
}
type webScheme string

func parseWebScheme(str string) (webScheme, error) {
	switch str {
	case string(commonApiServerSchemeHttp):
		return commonApiServerSchemeHttp, nil
	case string(commonApiServerSchemeHttps):
		return commonApiServerSchemeHttps, nil
	default:
		return "", errors.NewFromCodeWithMessage(errors.Var.ParamError, "un support "+str)

	}
}

var (
	commonApiServerSchemeHttp  webScheme = "http"
	commonApiServerSchemeHttps webScheme = "https"
)

type commonApiServer struct {
	Scheme  webScheme `json:"scheme"`
	Address string    `json:"addr"`
	Port    int       `json:"port"`
}

type commonIngress struct {
	IngressIP string `json:"ingressIP"`
}

type commonRegistry struct {
	Scheme  webScheme `json:"scheme"`
	Address string    `json:"addr"`
	Port    int       `json:"port"`
}

type commonSkyview struct {
	Ip string `json:"addr"`
}

func newDefaultLbParentLbModel(vip string, vrid string, networkCard string) jobParamStepProperty {
	return defaultLbParentLbModel{
		VIP: ingressVIP{
			Ingress: ingressVIPIngress{
				VIPs: []ingressVipIngressInfo{
					{
						Vips:              []string{vip},
						Vrid:              vrid,
						NetworkInterfaces: networkCard,
					},
				},
			},
		},
	}
}

type defaultLbParentLbModel struct {
	VIP ingressVIP `json:"vip"`
}

func (obj defaultLbParentLbModel) asProperty() (map[string]interface{}, error) {
	var result = make(map[string]interface{})
	var err = utils.BeanCopy(obj, &result)
	return result, err
}

type ingressVIP struct {
	Ingress ingressVIPIngress `json:"ingress"`
}

type ingressVIPIngress struct {
	VIPs []ingressVipIngressInfo `json:"vips"`
}

type ingressVipIngressInfo struct {
	Vips              []string `json:"vips"`
	Vrid              string   `json:"vrid"`
	NetworkInterfaces string   `json:"interface"`
}

func NewStorageProperty(sc string) jobParamStepProperty {
	return StorageProperty{
		Storage: StorageParam{
			ClusterManager: storageClusterManager{
				ClusterManager:     map[string]string{},
				LinuxPackageServer: map[string]string{},
				Pypiserver:         map[string]string{},
			},
			Sisyphus: StorageSisyphusParam{
				Backend: StorageSCInfoParam{
					StorageClass: sc,
				},
				Mongodb: StorageSCInfoParam{
					StorageClass: sc,
				},
			},
		},
	}
}

type storageClusterManager struct {
	ClusterManager     map[string]string `json:"clusterManager"`
	LinuxPackageServer map[string]string `json:"linuxPackageServer"`
	Pypiserver         map[string]string `json:"pypiserver"`
}

type StorageProperty struct {
	Storage StorageParam `json:"storage"`
}

func (obj StorageProperty) asProperty() (map[string]interface{}, error) {
	var result = make(map[string]interface{})
	var err = utils.BeanCopy(obj, &result)
	return result, err
}

type StorageParam struct {
	ClusterManager storageClusterManager `json:"clusterManager"`
	Sisyphus       StorageSisyphusParam  `json:"sisyphus"`
}
type StorageSisyphusParam struct {
	Backend StorageSCInfoParam `json:"backend"`
	Mongodb StorageSCInfoParam `json:"mongodb"`
}

type StorageSCInfoParam struct {
	StorageClass string `json:"storageClass,omitempty"`
}

func NewDataImportModel(nodeUpDown, reset string) dataImportModel {
	return dataImportModel{
		Orchs: dataImportOrchs{
			NodeUpDownSolutionVersion: nodeUpDown,
			ResetNodeSolutionVersion:  reset,
		},
	}
}

type dataImportModel struct {
	Orchs dataImportOrchs `json:"orchs"`
}

func (obj dataImportModel) asProperty() (map[string]interface{}, error) {
	var result = make(map[string]interface{})
	var err = utils.BeanCopy(obj, &result)
	return result, err
}

type dataImportOrchs struct {
	NodeUpDownSolutionVersion string `json:"nodeUpDownSolutionVersion"`
	ResetNodeSolutionVersion  string `json:"resetNodeSolutionVersion"`
}
