package internal

import (
	"encoding/json"
	"sort"

	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/cluster/render/internal/task"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/installerutil"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/meta"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/uuid"
)

// metaNameRRTIL
// 处理metadata.name 的 RRTIL
type metaNameRRTIL struct {
	uuid uuid.UUIDIntf
}

func NewMetaNameRRTIL(uuid uuid.UUIDIntf) RequestRender {
	return metaNameRRTIL{
		uuid: uuid,
	}
}

// Installer
// 将create request 的内容渲染到 installer
func (rrtil metaNameRRTIL) Installer(installer *installerv1alpha1.Installer, request clustermodel.CreateRequest) error {
	if installer.Name == "" {
		installer.Name = request.ClusterName + "-" + rrtil.uuid.UUID()
	}
	return nil
}

// Response
// 将installer 的内容渲染到 response
func (rrtil metaNameRRTIL) Response(response *clustermodel.Response, installer installerv1alpha1.Installer) error {
	// nothing to do
	return nil
}

// CreateStatusResponse
// 将installer 的内容渲染到 create status response
func (rrtil metaNameRRTIL) CreateStatusResponse(statusResponse *clustermodel.CreateStatusResponse, installer installerv1alpha1.Installer) error {
	// nothing to do
	return nil
}

// CreateResponse
// 将installer 的内容渲染到 create response
func (rrtil metaNameRRTIL) CreateResponse(createResponse *clustermodel.CreateResponse, installer installerv1alpha1.Installer) error {
	// nothing to do
	return nil
}

// metaLabelsRRTIL
// 处理metadata.labels 的 RRTIL
type metaLabelsRRTIL struct {
}

func NewMetaLabelsRRTIL() RequestRender {
	return metaLabelsRRTIL{}
}

// Installer
// 将create request 的内容渲染到 installer
func (rrtil metaLabelsRRTIL) Installer(installer *installerv1alpha1.Installer, request clustermodel.CreateRequest) error {
	if installer.Labels == nil {
		installer.Labels = make(map[string]string)
	}
	// 标记类型 创建集群
	installer.Labels[constants.InstallTypeLabelKey] = constants.CreateClusterInstallerTypeLabelValue
	// 标记集群名称
	installer.Labels[constants.CreateClusterClusterNameLabelKey] = request.ClusterName

	return nil
}

// Response
// 将installer 的内容渲染到 response
func (rrtil metaLabelsRRTIL) Response(response *clustermodel.Response, installer installerv1alpha1.Installer) error {
	clusterName, _ := meta.GetFromMap(installer.Labels, constants.CreateClusterClusterNameLabelKey)
	if clusterName == "" {
		return errors.NewFromCodeWithMessage(errors.Var.InstallerLabelKeyEmpty, constants.CreateClusterClusterNameLabelKey)
	}
	// 与response 中记录集群名称
	response.Name = clusterName
	return nil
}

func (rrtil metaLabelsRRTIL) CreateStatusResponse(statusResponse *clustermodel.CreateStatusResponse, installer installerv1alpha1.Installer) error {
	clusterName, _ := meta.GetFromMap(installer.Labels, constants.CreateClusterClusterNameLabelKey)
	if clusterName == "" {
		return errors.NewFromCodeWithMessage(errors.Var.InstallerLabelKeyEmpty, constants.CreateClusterClusterNameLabelKey)
	}
	// 与response 中记录集群名称
	statusResponse.Name = clusterName
	return nil
}

// CreateResponse
// 将installer 的内容渲染到 create response
func (rrtil metaLabelsRRTIL) CreateResponse(createResponse *clustermodel.CreateResponse, installer installerv1alpha1.Installer) error {
	clusterName, _ := meta.GetFromMap(installer.Labels, constants.CreateClusterClusterNameLabelKey)
	if clusterName == "" {
		return errors.NewFromCodeWithMessage(errors.Var.InstallerLabelKeyEmpty, constants.CreateClusterClusterNameLabelKey)
	}
	// 与response 中记录集群名称
	createResponse.Name = clusterName
	return nil
}

// metaAnnotationsRRTIL
// 处理metadata.annotations 的 RRTIL
type metaAnnotationsRRTIL struct {
}

func NewMetaAnnotationsRRTIL() RequestRender {
	return metaAnnotationsRRTIL{}
}

// Installer
// 将create request 的内容渲染到 installer
func (rrtil metaAnnotationsRRTIL) Installer(installer *installerv1alpha1.Installer, request clustermodel.CreateRequest) error {
	if installer.Annotations == nil {
		installer.Annotations = make(map[string]string)
	}
	installer.Annotations["on-success-remove-self"] = "true"
	// 记录是默认节点的节点IP
	defaultNodeIps := make([]string, 0, len(request.NodeConfigs.Nodes))
	for _, node := range request.NodeConfigs.Nodes {
		node := node
		if node.IsDefault {
			defaultNodeIps = append(defaultNodeIps, node.Ip)
		}
	}
	var defaultNodeIpsBytes []byte
	var defaultNodeIpsMarshalErr error
	if defaultNodeIpsBytes, defaultNodeIpsMarshalErr = json.Marshal(defaultNodeIps); defaultNodeIpsMarshalErr != nil {
		return defaultNodeIpsMarshalErr
	} else {
		installer.Annotations[constants.CreateClusterDefaultNodeAnnotationKey] = string(defaultNodeIpsBytes)
	}

	// 标记节点模式
	installer.Annotations[constants.CreateClusterNodeTypeAnnotationKey] = string(request.NodeConfigs.NodeConfigType)
	// 标记CRI
	installer.Annotations[constants.InstallerCRITypeAnnotationKey] = string(request.CRI)
	return nil
}

// Response
// 将installer 的内容渲染到 response
func (rrtil metaAnnotationsRRTIL) Response(response *clustermodel.Response, installer installerv1alpha1.Installer) error {
	if installer.Annotations == nil {
		installer.Annotations = make(map[string]string)
	}

	// 读取节点配置类型
	nodeConfigType, err := clustermodel.ParseNodeConfigType(installer.Annotations[constants.CreateClusterNodeTypeAnnotationKey])
	if err != nil {
		return err
	}
	response.NodeConfigType = nodeConfigType

	criKey, exist := installer.Annotations[constants.InstallerCRITypeAnnotationKey]
	if !exist || criKey == "" {
		criKey = string(clustermodel.CRITypeContainerd)
	}
	response.CRI = clustermodel.CRIType(criKey)
	return nil
}

func (rrtil metaAnnotationsRRTIL) CreateStatusResponse(statusResponse *clustermodel.CreateStatusResponse, installer installerv1alpha1.Installer) error {
	if installer.Annotations == nil {
		installer.Annotations = make(map[string]string)
	}
	// 读取节点配置类型
	nodeConfigType, err := clustermodel.ParseNodeConfigType(installer.Annotations[constants.CreateClusterNodeTypeAnnotationKey])
	if err != nil {
		return err
	}
	statusResponse.NodeConfigType = nodeConfigType
	// 读取节点架构
	arch := installer.Annotations[constants.CreateClusterNodeArchAnnotationKey]
	statusResponse.Arch = arch

	criKey, exist := installer.Annotations[constants.InstallerCRITypeAnnotationKey]
	if !exist || criKey == "" {
		criKey = string(clustermodel.CRITypeContainerd)
	}
	statusResponse.CRI = clustermodel.CRIType(criKey)
	return nil
}

// CreateResponse
// 将installer 的内容渲染到 create response
func (rrtil metaAnnotationsRRTIL) CreateResponse(createResponse *clustermodel.CreateResponse, installer installerv1alpha1.Installer) error {
	if installer.Annotations == nil {
		installer.Annotations = make(map[string]string)
	}
	// 设置节点模式
	nodeConfigType, exist := meta.GetFromMap(installer.Annotations, constants.CreateClusterNodeTypeAnnotationKey)
	if !exist {
		return errors.NewFromCode(errors.Var.NodeConfigTypeLost)
	}
	parsedNodeConfigType, err := clustermodel.ParseNodeConfigType(nodeConfigType)
	if err != nil {
		return err
	}
	createResponse.NodeConfigs.NodeConfigType = parsedNodeConfigType

	// 设置默认节点IP
	if defaultNodeIps, exist := meta.GetFromMap(installer.Annotations, constants.CreateClusterDefaultNodeAnnotationKey); exist {
		var defaultNodeIpList []string
		_ = json.Unmarshal([]byte(defaultNodeIps), &defaultNodeIpList)
		if len(defaultNodeIpList) != 0 {
			for _, nodeIp := range defaultNodeIpList {
				createResponse.NodeConfigs.Nodes = append(createResponse.NodeConfigs.Nodes, clustermodel.CreateNodeConfigResponse{
					Ip:        nodeIp,
					IsDefault: true,
				})
			}
		}
	}
	criKey, exist := installer.Annotations[constants.InstallerCRITypeAnnotationKey]
	if !exist || criKey == "" {
		criKey = string(clustermodel.CRITypeContainerd)
	}
	createResponse.CRI = clustermodel.CRIType(criKey)
	return nil
}

// metaTaskRRTIL
// 处理metadata.task 的 RRTIL
type metaTaskRRTIL struct {
	// 处理task 的顺序
	taskIntfList []task.Intf
}

func NewMetaTaskRRTIL(uuid uuid.UUIDIntf) RequestRender {
	return metaTaskRRTIL{
		taskIntfList: []task.Intf{
			task.NewNodeInitialTask(),                        // 节点初始化
			task.NewSisyphusNodeResetSolutionApplyTask(uuid), // (Option) 提交节点重置西西弗斯表单
			task.NewSisyphusSolutionApplyTask(uuid),          // 提交集群创建西西弗斯表单
			task.NewSisyphusNodeResetSolutionExecTask(),      // (Option) 执行节点重置行为
			task.NewSisyphusSolutionExecTask(),               // 执行集群创建行为
			task.NewManagedClusterTask(),                     // 执行集群纳管行为
			task.NewMergeStcTask(),                           // 合并元数据(这一步无论成功｜失败都会标记成功)
			task.NewAfterClusterTask(),                       // 执行集群纳管后置行为
		},
	}
}

// Installer
// 将create request 的内容渲染到 installer
func (rrtil metaTaskRRTIL) Installer(installer *installerv1alpha1.Installer, request clustermodel.CreateRequest) error {
	if installer.Spec.Tasks == nil {
		installer.Spec.Tasks = make(installerv1alpha1.InstallerTaskSpecList, 0, 16)
	}
	for _, taskIntf := range rrtil.taskIntfList {
		if err := rrtil.dealForCreateRequestRenderToInstallerTask(taskIntf, installer, request); err != nil {
			return err
		}
	}
	// 排序
	sortIndex := []installerutil.Intf{}
	for _, intf := range rrtil.taskIntfList {
		intf := intf
		sortIndex = append(sortIndex, intf)
	}
	sort.Sort(installerutil.NewTaskSortUtil(sortIndex, installer.Spec.Tasks))
	return nil
}

// Response
// 将installer 的内容渲染到 response
func (rrtil metaTaskRRTIL) Response(response *clustermodel.Response, installer installerv1alpha1.Installer) error {
	// 于response 中记录集群状态为未纳管理
	response.State = clustermodel.StateTypeUnControlled
	// 于response 中记录api-server状态未初始化中
	response.ApiServerStatus = clustermodel.ApiServerStatusTypeInitialize
	// 状态获取逻辑
	for _, taskIntf := range rrtil.taskIntfList {
		if err := rrtil.dealForInstallerTaskRenderToResponse(taskIntf, response, installer); err != nil {
			return err
		}
	}

	return nil
}

func (rrtil metaTaskRRTIL) CreateStatusResponse(statusResponse *clustermodel.CreateStatusResponse, installer installerv1alpha1.Installer) error {
	// 写入步骤与状态
	for _, taskIntf := range rrtil.taskIntfList {
		if err := rrtil.dealForInstallerTaskRenderToCreateStateResponse(taskIntf, statusResponse, installer); err != nil {
			return err
		}
	}
	return nil
}

// CreateResponse
// 将installer 的内容渲染到 create response
func (rrtil metaTaskRRTIL) CreateResponse(createResponse *clustermodel.CreateResponse, installer installerv1alpha1.Installer) error {
	// 写入表单信息
	for _, taskIntf := range rrtil.taskIntfList {
		if err := rrtil.dealForInstallerTaskRenderToCreateResponse(taskIntf, createResponse, installer); err != nil {
			return err
		}
	}
	return nil
}

func (rrtil metaTaskRRTIL) dealForCreateRequestRenderToInstallerTask(taskIntf task.Intf, installer *installerv1alpha1.Installer, request clustermodel.CreateRequest) error {
	// 先查找到spec
	// 若specTask 存在   targetIndex != -1
	// 若specTask 不存在 targetIndex  = -1
	targetIndex := installerutil.GetSpecTaskIndexByKindAndName(installer.Spec.Tasks, taskIntf.Kind(), taskIntf.Name())

	// 查看是否需要提供该task
	if taskIntf.Should(installer, request) {
		// 若specTask不存在 则初始化一个
		var targetTask installerv1alpha1.InstallerTaskSpec
		if targetIndex == -1 {
			installer.Spec.Tasks = append(installer.Spec.Tasks, targetTask)
			targetIndex = len(installer.Spec.Tasks) - 1
			targetTask.TaskName = taskIntf.Name()
			targetTask.Description = taskIntf.Description()
			kind := taskIntf.Kind()
			targetTask.Kind = &kind
		} else {
			targetTask = installer.Spec.Tasks[targetIndex]
		}

		// 从 installer 中查找task status
		var targetStatus = installerutil.GetStatusTaskByKindAndName(installer.Status.Tasks, *(targetTask.Kind), targetTask.TaskName)
		if err := taskIntf.CreateRequestRenderToInstallerTask(installer.Spec.Tasks, &targetTask, targetStatus, request); err != nil {
			return err
		}
		installer.Spec.Tasks[targetIndex] = targetTask
	} else {
		// 若已存在 则移除
		if targetIndex != -1 {
			installer.Spec.Tasks = append(installer.Spec.Tasks[0:targetIndex], installer.Spec.Tasks[targetIndex+1:]...)[0 : len(installer.Spec.Tasks)-1]
		}
	}
	return nil
}

func (rrtil metaTaskRRTIL) dealForInstallerTaskRenderToResponse(taskIntf task.Intf, response *clustermodel.Response, installer installerv1alpha1.Installer) error {
	// 从installer 中查找task spec
	taskSpec := installerutil.GetSpecTaskByKindAndName(installer.Spec.Tasks, taskIntf.Kind(), taskIntf.Name())
	// 没有找到该类型的task不做处理
	if taskSpec == nil {
		return nil
	}
	// 从 installer 中查找task status
	var targetStatus = installerutil.GetStatusTaskByKindAndName(installer.Status.Tasks, *(taskSpec.Kind), taskSpec.TaskName)

	return taskIntf.InstallerTaskRenderToResponse(response, *taskSpec, targetStatus)
}

func (rrtil metaTaskRRTIL) dealForInstallerTaskRenderToCreateStateResponse(taskIntf task.Intf, statusResponse *clustermodel.CreateStatusResponse, installer installerv1alpha1.Installer) error {
	// 从installer 中查找task spec 和 task status
	taskSpec := installerutil.GetSpecTaskByKindAndName(installer.Spec.Tasks, taskIntf.Kind(), taskIntf.Name())
	// 没有找到该类型的task不做处理
	if taskSpec == nil {
		return nil
	}
	var taskStatus = installerutil.GetStatusTaskByKindAndName(installer.Status.Tasks, *(taskSpec.Kind), taskSpec.TaskName)
	return taskIntf.InstallerTaskRenderToCreateStateResponse(statusResponse, *taskSpec, taskStatus)
}

func (rrtil metaTaskRRTIL) dealForInstallerTaskRenderToCreateResponse(taskIntf task.Intf, createResponse *clustermodel.CreateResponse, installer installerv1alpha1.Installer) error {
	// 从installer 中查找task spec
	// 从installer 中查找task spec
	taskSpec := installerutil.GetSpecTaskByKindAndName(installer.Spec.Tasks, taskIntf.Kind(), taskIntf.Name())
	// 没有找到该类型的task不做处理
	if taskSpec == nil {
		return nil
	}

	return taskIntf.InstallerTaskRenderToCreateResponse(createResponse, *taskSpec)
}
