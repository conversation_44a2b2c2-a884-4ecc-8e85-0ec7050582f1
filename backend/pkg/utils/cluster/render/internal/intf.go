package internal

import (
	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
)

// RequestRender
// 将request 进行渲染
type RequestRender interface {
	// Installer
	// 将create request 的内容渲染到 installer
	Installer(installer *installerv1alpha1.Installer, request clustermodel.CreateRequest) error
	// Response
	// 将installer 的内容渲染到 response
	Response(response *clustermodel.Response, installer installerv1alpha1.Installer) error
	// CreateStatusResponse
	// 将installer 的内容渲染到 create status response
	CreateStatusResponse(statusResponse *clustermodel.CreateStatusResponse, installer installerv1alpha1.Installer) error

	// CreateResponse
	// 将installer 的内容渲染到 create response
	CreateResponse(createResponse *clustermodel.CreateResponse, installer installerv1alpha1.Installer) error
}
