package render

import (
	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/cluster/render/internal"
	util2 "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/uuid"
)

func NewIntf() Intf {
	uuidIntf := util2.NewUUIDIntf()
	return NewIntfWithParam(uuidIntf)
}
func NewIntfWithParam(uuidIntf util2.UUIDIntf) Intf {
	return &intfMgr{
		requestRenderToInstallerIntfList: []internal.RequestRender{
			internal.NewMetaNameRRTIL(uuidIntf),
			internal.NewMetaLabelsRRTIL(),
			internal.NewMetaAnnotationsRRTIL(),
			internal.NewMetaTaskRRTIL(uuidIntf),
		},
	}
}

type Intf interface {
	// CreateRequestRenderToInstaller
	// 将请求表单中的信息 apply 到 Installer
	CreateRequestRenderToInstaller(request clustermodel.CreateRequest, installer *installerv1alpha1.Installer) error

	// InstallerRenderToCreateResponse
	// 将installer 中的内容 apply 到 Response
	InstallerRenderToCreateResponse(installer installerv1alpha1.Installer, response *clustermodel.CreateResponse) error

	// InstallerToResponse
	// 将installer 中的内容 apply 到 Response
	InstallerToResponse(installer installerv1alpha1.Installer, response *clustermodel.Response) error

	// InstallerToStatusResponse
	// 将installer 中的内容 apply 到 StatusResponse
	InstallerToStatusResponse(installer installerv1alpha1.Installer, response *clustermodel.CreateStatusResponse) error
}

type intfMgr struct {
	// requestRenderToInstallerIntfList
	// 将request 中的内容渲染到 installer 中
	requestRenderToInstallerIntfList []internal.RequestRender
}

// CreateRequestRenderToInstaller
// 将请求表单中的信息 apply 到 Installer
func (mgr *intfMgr) CreateRequestRenderToInstaller(request clustermodel.CreateRequest, installer *installerv1alpha1.Installer) error {
	for _, r := range mgr.requestRenderToInstallerIntfList {
		if err := r.Installer(installer, request); err != nil {
			return err
		}
	}
	return nil
}

// InstallerRenderToCreateResponse
// 将installer 中的内容 apply 到 Response
func (mgr *intfMgr) InstallerRenderToCreateResponse(installer installerv1alpha1.Installer, response *clustermodel.CreateResponse) error {
	for _, r := range mgr.requestRenderToInstallerIntfList {
		if err := r.CreateResponse(response, installer); err != nil {
			return err
		}
	}
	return nil
}

// InstallerToResponse
// 将installer 中的内容 apply 到 Response
func (mgr *intfMgr) InstallerToResponse(installer installerv1alpha1.Installer, response *clustermodel.Response) error {
	if installer.Spec.ReApply {
		installer.Status = installerv1alpha1.InstallerStatus{}
	}
	for _, r := range mgr.requestRenderToInstallerIntfList {
		if err := r.Response(response, installer); err != nil {
			return err
		}
	}
	// 如果installer 在删除中 标记状态为删除中
	if installer.DeletionTimestamp != nil {
		response.Status = clustermodel.StatusTypeDeleting
	}
	response.CreateTime = installer.CreationTimestamp.Time
	return nil
}

// InstallerToStatusResponse
// 将installer 中的内容 apply 到 StatusResponse
func (mgr *intfMgr) InstallerToStatusResponse(installer installerv1alpha1.Installer, statusResponse *clustermodel.CreateStatusResponse) error {
	if installer.Spec.ReApply {
		installer.Status = installerv1alpha1.InstallerStatus{}
	}
	var response = new(clustermodel.Response)
	if err := mgr.InstallerToResponse(installer, response); err != nil {
		return err
	}
	statusResponse.Status = response.Status
	for _, r := range mgr.requestRenderToInstallerIntfList {
		if err := r.CreateStatusResponse(statusResponse, installer); err != nil {
			return err
		}
	}
	return nil

}
