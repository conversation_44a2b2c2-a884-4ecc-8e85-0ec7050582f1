package installerutil

import (
	"slices"
	"testing"

	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
)

func Test_WithStartFromFailedStepHandler(t *testing.T) {
	params := []struct {
		description string
		ars         struct {
			total             []string
			isStartFromFailed bool
			status            *installerv1alpha1.InstallerTaskStatus
		}
		want []string
	}{
		{
			description: "测试不从失败处开始,且status 为 nil",
			ars: struct {
				total             []string
				isStartFromFailed bool
				status            *installerv1alpha1.InstallerTaskStatus
			}{
				total:             []string{"A", "B", "C", "D", "E"},
				isStartFromFailed: false,
				status:            nil,
			},
			want: []string{"A", "B", "C", "D", "E"},
		},
		{
			description: "测试不从失败处开始,且status 不为 nil",
			ars: struct {
				total             []string
				isStartFromFailed bool
				status            *installerv1alpha1.InstallerTaskStatus
			}{
				total:             []string{"A", "B", "C", "D", "E"},
				isStartFromFailed: false,
				status: &installerv1alpha1.InstallerTaskStatus{
					Steps: installerv1alpha1.SisyphusSolutionExecStepResults{
						{
							StepName: "A",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "B",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "C",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "D",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "E",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
					},
				},
			},
			want: []string{"A", "B", "C", "D", "E"},
		},
		{
			description: "测试从失败处开始,且status 为 nil",
			ars: struct {
				total             []string
				isStartFromFailed bool
				status            *installerv1alpha1.InstallerTaskStatus
			}{
				total:             []string{"A", "B", "C", "D", "E"},
				isStartFromFailed: true,
				status:            nil,
			},
			want: []string{"A", "B", "C", "D", "E"},
		},
		{
			description: "测试从失败处开始,且不存在新增项 - 从未开始",
			ars: struct {
				total             []string
				isStartFromFailed bool
				status            *installerv1alpha1.InstallerTaskStatus
			}{
				total:             []string{"A", "B", "C", "D", "E"},
				isStartFromFailed: true,
				status: &installerv1alpha1.InstallerTaskStatus{
					Steps: installerv1alpha1.SisyphusSolutionExecStepResults{
						{
							StepName: "A",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "B",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "C",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "D",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "E",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
					},
				},
			},
			want: []string{"A", "B", "C", "D", "E"},
		},
		{
			description: "测试从失败处开始,且不存在新增项 - 头部失败",
			ars: struct {
				total             []string
				isStartFromFailed bool
				status            *installerv1alpha1.InstallerTaskStatus
			}{
				total:             []string{"A", "B", "C", "D", "E"},
				isStartFromFailed: true,
				status: &installerv1alpha1.InstallerTaskStatus{
					Steps: installerv1alpha1.SisyphusSolutionExecStepResults{
						{
							StepName: "A",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseFailed,
							},
						},
						{
							StepName: "B",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "C",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "D",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "E",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
					},
				},
			},
			want: []string{"A", "B", "C", "D", "E"},
		},
		{
			description: "测试从失败处开始,且不存在新增项 - 中间失败",
			ars: struct {
				total             []string
				isStartFromFailed bool
				status            *installerv1alpha1.InstallerTaskStatus
			}{
				total:             []string{"A", "B", "C", "D", "E"},
				isStartFromFailed: true,
				status: &installerv1alpha1.InstallerTaskStatus{
					Steps: installerv1alpha1.SisyphusSolutionExecStepResults{
						{
							StepName: "A",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "B",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "C",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "D",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseFailed,
							},
						},
						{
							StepName: "E",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
					},
				},
			},
			want: []string{"D", "E"},
		},
		{
			description: "测试从失败处开始,且不存在新增项 - 尾部失败",
			ars: struct {
				total             []string
				isStartFromFailed bool
				status            *installerv1alpha1.InstallerTaskStatus
			}{
				total:             []string{"A", "B", "C", "D", "E"},
				isStartFromFailed: true,
				status: &installerv1alpha1.InstallerTaskStatus{
					Steps: installerv1alpha1.SisyphusSolutionExecStepResults{
						{
							StepName: "A",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "B",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "C",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "D",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "E",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseFailed,
							},
						},
					},
				},
			},
			want: []string{"E"},
		},
		{
			description: "测试从失败处开始,且不存在新增项 - 全部成功",
			ars: struct {
				total             []string
				isStartFromFailed bool
				status            *installerv1alpha1.InstallerTaskStatus
			}{
				total:             []string{"A", "B", "C", "D", "E"},
				isStartFromFailed: true,
				status: &installerv1alpha1.InstallerTaskStatus{
					Steps: installerv1alpha1.SisyphusSolutionExecStepResults{
						{
							StepName: "A",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "B",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "C",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "D",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "E",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
					},
				},
			},
			want: []string{},
		},
		{
			description: "测试从失败处开始,且不存在新增项 - 没有老的Step",
			ars: struct {
				total             []string
				isStartFromFailed bool
				status            *installerv1alpha1.InstallerTaskStatus
			}{
				total:             []string{"A", "B", "C", "D", "E"},
				isStartFromFailed: true,
				status: &installerv1alpha1.InstallerTaskStatus{
					Steps: installerv1alpha1.SisyphusSolutionExecStepResults{},
				},
			},
			want: []string{},
		},
		{
			description: "测试从失败处开始 头部新增 - 从未开始",
			ars: struct {
				total             []string
				isStartFromFailed bool
				status            *installerv1alpha1.InstallerTaskStatus
			}{
				total:             []string{"a", "b", "A", "B", "C", "D", "E"},
				isStartFromFailed: true,
				status: &installerv1alpha1.InstallerTaskStatus{
					Steps: installerv1alpha1.SisyphusSolutionExecStepResults{
						{
							StepName: "A",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "B",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "C",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "D",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "E",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
					},
				},
			},
			want: []string{"a", "b", "A", "B", "C", "D", "E"},
		},
		{
			description: "测试从失败处开始 头部新增 - 头部失败",
			ars: struct {
				total             []string
				isStartFromFailed bool
				status            *installerv1alpha1.InstallerTaskStatus
			}{
				total:             []string{"a", "b", "A", "B", "C", "D", "E"},
				isStartFromFailed: true,
				status: &installerv1alpha1.InstallerTaskStatus{
					Steps: installerv1alpha1.SisyphusSolutionExecStepResults{
						{
							StepName: "A",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseFailed,
							},
						},
						{
							StepName: "B",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "C",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "D",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "E",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
					},
				},
			},
			want: []string{"A", "B", "C", "D", "E"},
		},
		{
			description: "测试从失败处开始 头部新增 - 中间失败",
			ars: struct {
				total             []string
				isStartFromFailed bool
				status            *installerv1alpha1.InstallerTaskStatus
			}{
				total:             []string{"a", "b", "A", "B", "C", "D", "E"},
				isStartFromFailed: true,
				status: &installerv1alpha1.InstallerTaskStatus{
					Steps: installerv1alpha1.SisyphusSolutionExecStepResults{
						{
							StepName: "A",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "B",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseFailed,
							},
						},
						{
							StepName: "C",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "D",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "E",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
					},
				},
			},
			want: []string{"B", "C", "D", "E"},
		},
		{
			description: "测试从失败处开始 头部新增 - 尾部失败",
			ars: struct {
				total             []string
				isStartFromFailed bool
				status            *installerv1alpha1.InstallerTaskStatus
			}{
				total:             []string{"a", "b", "A", "B", "C", "D", "E"},
				isStartFromFailed: true,
				status: &installerv1alpha1.InstallerTaskStatus{
					Steps: installerv1alpha1.SisyphusSolutionExecStepResults{
						{
							StepName: "A",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "B",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "C",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "D",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "E",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseFailed,
							},
						},
					},
				},
			},
			want: []string{"E"},
		},
		{
			description: "测试从失败处开始 非头部新增 - 从未开始",
			ars: struct {
				total             []string
				isStartFromFailed bool
				status            *installerv1alpha1.InstallerTaskStatus
			}{
				total:             []string{"A", "B", "a", "b", "C", "D", "E"},
				isStartFromFailed: true,
				status: &installerv1alpha1.InstallerTaskStatus{
					Steps: installerv1alpha1.SisyphusSolutionExecStepResults{
						{
							StepName: "A",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "B",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "C",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "D",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "E",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
					},
				},
			},
			want: []string{"A", "B", "a", "b", "C", "D", "E"},
		},
		{
			description: "测试从失败处开始 非头部新增 - 头部失败",
			ars: struct {
				total             []string
				isStartFromFailed bool
				status            *installerv1alpha1.InstallerTaskStatus
			}{
				total:             []string{"A", "B", "a", "b", "C", "D", "E"},
				isStartFromFailed: true,
				status: &installerv1alpha1.InstallerTaskStatus{
					Steps: installerv1alpha1.SisyphusSolutionExecStepResults{
						{
							StepName: "A",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseFailed,
							},
						},
						{
							StepName: "B",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "C",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "D",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "E",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
					},
				},
			},
			want: []string{"A", "B", "a", "b", "C", "D", "E"},
		},
		{
			description: "测试从失败处开始 非头部新增 - 中间失败",
			ars: struct {
				total             []string
				isStartFromFailed bool
				status            *installerv1alpha1.InstallerTaskStatus
			}{
				total:             []string{"A", "B", "a", "b", "C", "D", "E"},
				isStartFromFailed: true,
				status: &installerv1alpha1.InstallerTaskStatus{
					Steps: installerv1alpha1.SisyphusSolutionExecStepResults{
						{
							StepName: "A",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "B",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseFailed,
							},
						},
						{
							StepName: "C",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "D",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "E",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
					},
				},
			},
			want: []string{"B", "a", "b", "C", "D", "E"},
		},
		{
			description: "测试从失败处开始 非头部新增 - 尾部失败",
			ars: struct {
				total             []string
				isStartFromFailed bool
				status            *installerv1alpha1.InstallerTaskStatus
			}{
				total:             []string{"A", "B", "a", "b", "C", "D", "E"},
				isStartFromFailed: true,
				status: &installerv1alpha1.InstallerTaskStatus{
					Steps: installerv1alpha1.SisyphusSolutionExecStepResults{
						{
							StepName: "A",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "B",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "C",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "D",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "E",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseFailed,
							},
						},
					},
				},
			},
			want: []string{"E"},
		},
		// 重复的从失败出开始
		{
			description: "重复从失败处开始 - 存在失败",
			ars: struct {
				total             []string
				isStartFromFailed bool
				status            *installerv1alpha1.InstallerTaskStatus
			}{
				total:             []string{"A", "B", "C", "D", "E"},
				isStartFromFailed: true,
				status: &installerv1alpha1.InstallerTaskStatus{
					Steps: installerv1alpha1.SisyphusSolutionExecStepResults{
						{
							StepName: "C",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseFailed,
							},
						},
						{
							StepName: "D",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "E",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
					},
				},
			},
			want: []string{"C", "D", "E"},
		},
		{
			description: "重复从失败处开始 - 全部成功",
			ars: struct {
				total             []string
				isStartFromFailed bool
				status            *installerv1alpha1.InstallerTaskStatus
			}{
				total:             []string{"A", "B", "C", "D", "E"},
				isStartFromFailed: true,
				status: &installerv1alpha1.InstallerTaskStatus{
					Steps: installerv1alpha1.SisyphusSolutionExecStepResults{
						{
							StepName: "D",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "E",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
					},
				},
			},
			want: []string{},
		},
		{
			description: "重复从失败处开始 - 原本全部成功",
			ars: struct {
				total             []string
				isStartFromFailed bool
				status            *installerv1alpha1.InstallerTaskStatus
			}{
				total:             []string{"A", "B", "C", "D", "E"},
				isStartFromFailed: true,
				status: &installerv1alpha1.InstallerTaskStatus{
					Steps: installerv1alpha1.SisyphusSolutionExecStepResults{},
				},
			},
			want: []string{},
		},
		// 重复从失败处开始，节点联通性失败
		{
			description: "重复从失败处开始 - 原本全部成功",
			ars: struct {
				total             []string
				isStartFromFailed bool
				status            *installerv1alpha1.InstallerTaskStatus
			}{
				total:             []string{"A", "B", "C", "D", "E"},
				isStartFromFailed: true,
				status: &installerv1alpha1.InstallerTaskStatus{
					Steps: installerv1alpha1.SisyphusSolutionExecStepResults{
						{
							StepName: "E",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
					},
				},
			},
			want: []string{"A", "B", "C", "D", "E"},
		},
		// 步骤变少
		{
			description: "测试从失败处开始 - 步骤变少 - 消失的失败",
			ars: struct {
				total             []string
				isStartFromFailed bool
				status            *installerv1alpha1.InstallerTaskStatus
			}{
				total:             []string{"C", "D", "E"},
				isStartFromFailed: true,
				status: &installerv1alpha1.InstallerTaskStatus{
					Steps: installerv1alpha1.SisyphusSolutionExecStepResults{
						{
							StepName: "A",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseFailed,
							},
						},
						{
							StepName: "B",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "C",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "D",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "E",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
					},
				},
			},
			want: []string{"C", "D", "E"},
		},
		{
			description: "测试从失败处开始 - 步骤变少 - 头部失败",
			ars: struct {
				total             []string
				isStartFromFailed bool
				status            *installerv1alpha1.InstallerTaskStatus
			}{
				total:             []string{"C", "D", "E"},
				isStartFromFailed: true,
				status: &installerv1alpha1.InstallerTaskStatus{
					Steps: installerv1alpha1.SisyphusSolutionExecStepResults{
						{
							StepName: "A",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "B",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "C",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseFailed,
							},
						},
						{
							StepName: "D",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
						{
							StepName: "E",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
					},
				},
			},
			want: []string{"C", "D", "E"},
		},
		{
			description: "测试从失败处开始 - 步骤变少 - 中间失败",
			ars: struct {
				total             []string
				isStartFromFailed bool
				status            *installerv1alpha1.InstallerTaskStatus
			}{
				total:             []string{"C", "D", "E"},
				isStartFromFailed: true,
				status: &installerv1alpha1.InstallerTaskStatus{
					Steps: installerv1alpha1.SisyphusSolutionExecStepResults{
						{
							StepName: "A",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "B",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "C",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseSuccess,
							},
						},
						{
							StepName: "D",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhaseFailed,
							},
						},
						{
							StepName: "E",
							InstallerStatusInfo: installerv1alpha1.InstallerStatusInfo{
								Phase: installerv1alpha1.StatusPhasePending,
							},
						},
					},
				},
			},
			want: []string{"D", "E"},
		},
	}
	for _, param := range params {
		t.Run(param.description, func(t *testing.T) {
			result := WithStartFromFailedStepHandler(param.ars.total, param.ars.isStartFromFailed, param.ars.status)
			if !slices.Equal(result, param.want) {
				t.Errorf("error of %s,result %v,want %v", param.description, result, param.want)
			}
		})
	}
}
