package installerutil

import (
	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
)

func GetSpecTaskByKindAndName(tasks installerv1alpha1.InstallerTaskSpecList,
	kind installerv1alpha1.InstallerTaskKind,
	name string) *installerv1alpha1.InstallerTaskSpec {
	idx := GetSpecTaskIndexByKindAndName(tasks, kind, name)
	if idx == -1 {
		return nil
	}
	return &tasks[idx]
}

func GetSpecTaskIndexByKindAndName(tasks installerv1alpha1.InstallerTaskSpecList,
	kind installerv1alpha1.InstallerTaskKind,
	name string) int {
	var targetIndex = -1
	for index, task := range tasks {
		task := task
		if task.Kind != nil && *task.Kind == kind && name == task.TaskName {
			targetIndex = index
			break
		}
	}
	return targetIndex
}

func GetStatusTaskIndexByKindAndName(tasksStatus installerv1alpha1.InstallerTaskStatusList,
	kind installerv1alpha1.InstallerTaskKind,
	name string) int {
	var targetIndex = -1
	for index, tasksStatu := range tasksStatus {
		tasksStatu := tasksStatu
		if tasksStatu.Kind != nil && *tasksStatu.Kind == kind && name == tasksStatu.TaskName {
			targetIndex = index
			break
		}
	}
	return targetIndex
}

func GetStatusTaskByKindAndName(tasksStatus installerv1alpha1.InstallerTaskStatusList,
	kind installerv1alpha1.InstallerTaskKind,
	name string) *installerv1alpha1.InstallerTaskStatus {
	var targetIndex = GetStatusTaskIndexByKindAndName(tasksStatus, kind, name)
	switch targetIndex {
	case -1:
		return nil
	default:
		return &tasksStatus[targetIndex]
	}
}

// GetSisyphusSolutionApplyTaskSolutionName
// 获取solution apply task 的solution name
func GetSisyphusSolutionApplyTaskSolutionName(existTasks []installerv1alpha1.InstallerTaskSpec, taskName string) (string, error) {
	var solutionName string
	for _, task := range existTasks {
		task := task
		if task.Kind != nil && *task.Kind == installerv1alpha1.InstallerTaskKindSisyphusSolutionApply && task.TaskName == taskName {
			if task.SisyphusSolutionApply != nil {
				solutionName = task.SisyphusSolutionApply.SolutionName
				break
			}
		}
	}
	if solutionName == "" {
		return solutionName, errors.NewFromCode(errors.Var.SolutionNameIsNotSet)
	}
	return solutionName, nil
}

const (
	// StartFromFailedIgnoreAppend 忽略新增项 从失败处开始执行，新增项目当做已执行成功
	StartFromFailedIgnoreAppend = iota
	// StartFromFailedAtAppend 从新增处开始执行
	StartFromFailedAtAppend
)

// WithStartFromFailedStepHandler
// 处理从失败处开始
func WithStartFromFailedStepHandler(totalSteps []string, isStartFromFailed bool, status *installerv1alpha1.InstallerTaskStatus) []string {
	// 在status中移除
	if status != nil && isStartFromFailed {
		// 表示上一次没有执行任何步骤，则本次从失败处开始也不执行任务步骤
		if len(status.Steps) == 0 {
			totalSteps = []string{}
		} else {
			// 使用老的statusStep 构建Map以便快速查询
			oldStepStatusMap := map[string]installerv1alpha1.SisyphusSolutionExecStepResult{}
			for _, stepStatus := range status.Steps {
				stepStatus := stepStatus
				oldStepStatusMap[stepStatus.StepName] = stepStatus
			}
			// 判断第一个任务是否运行过
			// =》 若第一个任务运行过，则忽略新增项
			// =》 若第一个项目没有运行过，则从新增项目开始
			var isFirstpending = status.Steps[0].Phase == installerv1alpha1.StatusPhasePending

			var statusSteps = make(installerv1alpha1.SisyphusSolutionExecStepResults, 0, len(status.Steps))
			for _, step := range totalSteps {
				stepStatus, exist := oldStepStatusMap[step]
				if !exist {
					stepStatus = installerv1alpha1.SisyphusSolutionExecStepResult{
						StepName: step,
					}
					if isFirstpending {
						stepStatus.Phase = installerv1alpha1.StatusPhasePending
					} else {
						stepStatus.Phase = installerv1alpha1.StatusPhaseSuccess
					}

				}
				statusSteps = append(statusSteps, stepStatus)

			}

			// 从status step 中找到第一个失败的 或 未执行的
			var firstFailedName string
			for _, step := range statusSteps {
				// 西西弗斯意外重新启动会造成任务是running 但任务实际已经failed
				if step.Phase == installerv1alpha1.StatusPhaseFailed || step.Phase == installerv1alpha1.StatusPhasePending || step.Phase == installerv1alpha1.StatusPhaseRunning {
					firstFailedName = step.StepName
					break
				}
			}
			// 找到 firstFailedName 在 paramSteps 中的位置
			var targetIndex = -1
			for i, item := range totalSteps {
				if item == firstFailedName {
					targetIndex = i
					break
				}
			}
			// 从失败处开始
			if targetIndex == -1 {
				totalSteps = []string{}
			} else {
				totalSteps = totalSteps[targetIndex:]
			}
		}
	}

	return totalSteps
}

func GetStepsStatus(steps clustermodel.CreateProcessStepListResponse) clustermodel.ProcessStatus {
	var status = clustermodel.ProcessStatusWaiting
	for index, step := range steps {
		switch step.Status {
		case clustermodel.ProcessStatusSuccess:
			// 最后一步是 成功才会标记成成功
			if index == len(steps)-1 {
				status = clustermodel.ProcessStatusSuccess
			}
		case clustermodel.ProcessStatusFail:
			status = clustermodel.ProcessStatusFail
			return status
		case clustermodel.ProcessStatusProcessing:
			status = clustermodel.ProcessStatusProcessing
			return status
		case clustermodel.ProcessStatusWaiting:
			// 第一个pending标记为pending 否则标记为running
			if index == 0 {
				status = clustermodel.ProcessStatusWaiting
			} else {
				status = clustermodel.ProcessStatusProcessing
			}
			return status
		default:
			status = clustermodel.ProcessStatusWaiting
		}
	}
	return status
}
