package installerutil

import (
	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	addonmodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/addon"
)

func RenderTaskForSolutionExec(task *installerv1alpha1.InstallerTaskSpec,
	solutionName string,
	expectSteps []string,
	startFromFailed bool,
	reset bool,
	status *installerv1alpha1.InstallerTaskStatus, config addonmodel.SisyphusAddressConfig) error {

	// nil 值初始化判断
	if task.SisyphusSolutionExec == nil {
		task.SisyphusSolutionExec = new(installerv1alpha1.SisyphusSolutionExecTaskSpec)
	}
	task.SisyphusSolutionExec.URL = config.Address
	task.SisyphusSolutionExec.Username = config.Username
	task.SisyphusSolutionExec.Password = config.Password

	task.SisyphusSolutionExec.SolutionName = solutionName
	// 如果重置，肯定不从失败处开始
	// 如果非重置、可能从失败处开始
	if !reset {
		expectSteps = WithStartFromFailedStepHandler(expectSteps, startFromFailed, status)
	}
	task.SisyphusSolutionExec.Param = expectSteps
	return nil
}
