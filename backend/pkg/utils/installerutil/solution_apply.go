package installerutil

import (
	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	addonmodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/addon"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/sisyphusutil"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/uuid"
)

func RenderTaskForSolutionApply(uuidutil uuid.UUIDIntf, config addonmodel.SisyphusAddressConfig, task *installerv1alpha1.InstallerTaskSpec, paramFunc SolutionApplyParamFunc) error {
	// nil 值初始化判断
	if task.SisyphusSolutionApply == nil {
		task.SisyphusSolutionApply = new(installerv1alpha1.SisyphusSolutionApplyTaskSpec)
	}
	// 处理 SisyphusSolutionApply
	task.SisyphusSolutionApply.URL = config.Address
	task.SisyphusSolutionApply.Username = config.Username
	task.SisyphusSolutionApply.Password = config.Password
	if task.SisyphusSolutionApply.SolutionName == "" {
		task.SisyphusSolutionApply.SolutionName = uuidutil.UUID()
	}

	param, err := paramFunc(task.SisyphusSolutionApply.SolutionName)
	if err != nil {
		return err
	}
	// 处理param
	task.SisyphusSolutionApply.Param = param
	// 查找token secret
	bytes, err := sisyphusutil.GetSecretToken()
	if err != nil {
		return err
	}
	task.SisyphusSolutionApply.StellarisToken = bytes
	return nil
}

type SolutionApplyParamFunc func(solutionName string) (string, error)
