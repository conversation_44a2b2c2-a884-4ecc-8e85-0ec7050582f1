package installerutil

import (
	"strconv"

	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	addonmodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/addon"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/sisyphusutil"
)

func RenderTaskForNodeInitial(task *installerv1alpha1.InstallerTaskSpec, config addonmodel.SisyphusAddressConfig, requests []node.NodeRequest) error {
	// nil 值初始化判断
	if task.SisyphusNodeInitial == nil {
		task.SisyphusNodeInitial = new(installerv1alpha1.SisyphusNodeInitialTaskSpec)
	}
	task.SisyphusNodeInitial.URL = config.Address
	task.SisyphusNodeInitial.Username = config.Username
	task.SisyphusNodeInitial.Password = config.Password
	var param installerv1alpha1.SisyphusNodeInitialParams
	for _, node := range requests {
		item := installerv1alpha1.SisyphusNodeInitialParam{
			IP:   node.Ip,
			Port: strconv.Itoa(node.Port),
		}
		if err := sisyphusutil.NodeAuthRequestToNodeInitialParam(node.Auth, &item); err != nil {
			return err
		}
		param = append(param, item)
	}
	task.SisyphusNodeInitial.Param = param
	return nil
}
