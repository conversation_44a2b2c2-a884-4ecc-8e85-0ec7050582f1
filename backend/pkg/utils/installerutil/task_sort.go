package installerutil

import (
	"sort"

	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
)

type Intf interface {
	// Name
	// 获取task 名称
	Name() string
	// Description
	// 获取task 描述
	Description() string

	// Kind
	// 获取当前task 处理器的类型
	Kind() installerv1alpha1.InstallerTaskKind
}

func NewTaskSortUtil(intfList []Intf, tasks installerv1alpha1.InstallerTaskSpecList) sort.Interface {
	nameKindDescriptionIndexMap := make(map[nameKindDescription]int)
	for idx, intf := range intfList {
		nameKindDescriptionIndexMap[newNameKindDescription(intf.Name(), intf.Kind(), intf.Description())] = idx + 1
	}
	return taskSortUtil{
		nameKindDescriptionIndexMap: nameKindDescriptionIndexMap,
		tasks:                       tasks,
	}
}

type taskSortUtil struct {
	nameKindDescriptionIndexMap map[nameKindDescription]int
	tasks                       installerv1alpha1.InstallerTaskSpecList
}

// Len is the number of elements in the collection.
func (util taskSortUtil) Len() int {
	return len(util.tasks)
}

func (util taskSortUtil) Less(i, j int) bool {
	taskI, taskJ := (util.tasks)[i], (util.tasks)[j]
	indexByTaskFunc := func(task installerv1alpha1.InstallerTaskSpec) int {
		val, exist := util.nameKindDescriptionIndexMap[newNameKindDescription(task.TaskName, *task.Kind, task.Description)]
		if !exist {
			val = 0
		}
		return val
	}
	sortI, sortJ := indexByTaskFunc(taskI), indexByTaskFunc(taskJ)
	return sortI < sortJ
}

// Swap swaps the elements with indexes i and j.
func (util taskSortUtil) Swap(i, j int) {
	(util.tasks)[i], (util.tasks)[j] = (util.tasks)[j], (util.tasks)[i]
}

func newNameKindDescription(name string, kind installerv1alpha1.InstallerTaskKind, description string) nameKindDescription {
	return nameKindDescription{
		name:        name,
		kind:        kind,
		description: description,
	}
}

type nameKindDescription struct {
	name        string
	description string
	kind        installerv1alpha1.InstallerTaskKind
}
