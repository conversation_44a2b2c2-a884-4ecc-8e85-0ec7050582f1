package utils

const (
	InternalGroup = "/internal"
	OpenApiGroup  = "/openapis"
	ApiGroup      = "/apis"
	V1ApiGroup    = "/v1"
	System        = "/system"
	Baseline      = "/baselines"
)

const (
	ApiV1Group           = ApiGroup + V1ApiGroup
	InternalOpenApiGroup = InternalGroup + OpenApiGroup + V1ApiGroup
	ApiV1BaselineGroup   = ApiV1Group + Baseline
)

// labels constant
const (
	// CloudComponentBelongingCloudServiceLabelKey
	// label key 标记 云组件归属的云服务
	CloudComponentBelongingCloudServiceLabelKey = "unified-platform.harmonycloud.cn/cloudservice-name"

	// CloudComponentNameLabelKey
	//  label key 标记 云组件的名称
	CloudComponentNameLabelKey = "unified-platform.harmonycloud.cn/cloudcomponent-name"

	// CloudComponentBelongingClusterLabelKey
	// label key 标记 云组件归属的集群
	CloudComponentBelongingClusterLabelKey = "unified-platform.harmonycloud.cn/cluster"

	LabelKeyScopeLevel = "unified-platform.harmonycloud.cn/scope-level"

	LabelKeyApplication = "unified-platform.harmonycloud.cn/cloudservice-name"
)
