package utils

import "time"

func TimeAfter(time1, time2 string) bool {
	t1, t1Error := time.Parse(time.RFC3339Nano, time1)
	t2, t2Error := time.Parse(time.RFC3339Nano, time2)

	if t1Error != nil || t2Error != nil {
		return false
	}

	return t1.After(t2)
}

func TimeBefore(time1, time2 string) bool {
	t1, t1Error := time.Parse(time.RFC3339Nano, time1)
	t2, t2Error := time.Parse(time.RFC3339Nano, time2)

	if t1Error != nil || t2Error != nil {
		return false
	}

	return t1.Before(t2)
}

type CustomTime struct {
	time.Time
}

func (ct *CustomTime) UnmarshalJSON(b []byte) error {
	tStr := string(b)
	tStr = tStr[1 : len(tStr)-1]
	parsedTime, err := time.Parse("2006-01-02 15:04:05", tStr)
	if err != nil {
		return err
	}
	ct.Time = parsedTime
	return nil
}
