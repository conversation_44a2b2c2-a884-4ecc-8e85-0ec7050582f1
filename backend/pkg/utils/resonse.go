package utils

import (
	"net/http"

	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/midware-go/midwares/audit"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
)

type Response struct {
	// 返回编码
	Code int `json:"code"`

	// 是否成功
	Success bool `json:"success"`

	// 错误信息
	Message string `json:"errorMsg,omitempty"`

	// 错误详情
	Detail string `json:"errorDetail,omitempty"`

	// 成功数据
	Data any `json:"data,omitempty"`

	// 分页总页数
	Count int `json:"count,omitempty"`
}

func Succeed(c *gin.Context, obj interface{}) {
	httpCode := http.StatusOK
	response := &Response{
		Success: true,
		Data:    obj,
	}
	audit.WithResponseData(c, true, response)
	c.JSON(httpCode, response)
}

func SucceedWithoutData(c *gin.Context) {
	httpCode := http.StatusOK
	response := &Response{
		Success: true,
	}
	audit.WithResponseData(c, true, response)
	c.JSON(httpCode, response)
}

func Failed(c *gin.Context, err errors.Error) {
	err = errors.NewFromError(c, err)
	response := &Response{
		Success: false,
		Code:    err.ResponseCode,
		Message: err.Message,
		Detail:  err.Detail,
	}
	audit.WithResponseData(c, false, response)
	c.JSON(err.HTTPCode, response)
}
