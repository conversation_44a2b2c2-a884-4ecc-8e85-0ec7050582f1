package k8s

import (
	"k8s.io/client-go/rest"
)

// DeepCopyRestConfig 创建并返回一个 `rest.Config` 的深拷贝。
func DeepCopyRestConfig(config *rest.Config) (*rest.Config, error) {
	if config == nil {
		return nil, nil
	}

	copyConfig := *config

	if config.CAData != nil {
		copyConfig.CAData = make([]byte, len(config.CAData))
		copy(copyConfig.CAData, config.CAData)
	}

	// 深拷贝证书和密钥数据
	if config.CertData != nil {
		copyConfig.CertData = make([]byte, len(config.CertData))
		copy(copyConfig.CertData, config.CertData)
	}
	if config.KeyData != nil {
		copyConfig.KeyData = make([]byte, len(config.KeyData))
		copy(copyConfig.KeyData, config.KeyData)
	}

	copyConfig.TLSClientConfig = rest.TLSClientConfig{
		Insecure:   config.TLSClientConfig.Insecure,
		ServerName: config.TLSClientConfig.ServerName,
	}
	if config.TLSClientConfig.CAFile != "" {
		copyConfig.TLSClientConfig.CAFile = config.TLSClientConfig.CAFile
	}
	if config.TLSClientConfig.CertFile != "" {
		copyConfig.TLSClientConfig.CertFile = config.TLSClientConfig.CertFile
	}
	if config.TLSClientConfig.KeyFile != "" {
		copyConfig.TLSClientConfig.KeyFile = config.TLSClientConfig.KeyFile
	}

	return &copyConfig, nil
}
