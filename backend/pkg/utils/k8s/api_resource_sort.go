package k8s

import (
	"sort"

	"k8s.io/apimachinery/pkg/runtime/schema"
)

var APIResourceGroupOrder = []string{
	"",
	"apps",
	"batch",
	"networking.k8s.io",
	"extensions",
	"autoscaling",
	"storage.k8s.io",
	"policy",
	"rbac.authorization.k8s.io",
	"authentication.k8s.io",
	"authorization.k8s.io",
	"events.k8s.io",
	"admissionregistration.k8s.io",
	"apiextensions.k8s.io",
	"apiregistration.k8s.io",
	"certificates.k8s.io",
	"coordination.k8s.io",
	"discovery.k8s.io",
	"flowcontrol.apiserver.k8s.io",
	"scheduling.k8s.io",
}

// APIResourceGroupOrderMap 将 APIResourceGroupOrder 转换为一个映射，快速查询
var APIResourceGroupOrderMap = make(map[string]int)

func init() {
	// 初始化映射，避免每次查询时都要遍历数组
	for i, group := range APIResourceGroupOrder {
		APIResourceGroupOrderMap[group] = i
	}
}

// GetAPIResourceGroupIndex 获取资源组的索引，返回 -1 表示没有找到
func GetAPIResourceGroupIndex(group string) int {
	if index, exists := APIResourceGroupOrderMap[group]; exists {
		return index
	}
	return -1 // 默认返回超出范围的值，确保排在末尾
}

func SortClusterAPIResourceItem(items []*schema.GroupVersionResource) {
	sort.SliceStable(items, func(i, j int) bool {
		// 获取两个资源组的索引
		iGroupIndex := GetAPIResourceGroupIndex(items[i].Group)
		jGroupIndex := GetAPIResourceGroupIndex(items[j].Group)
		if iGroupIndex == -1 {
			iGroupIndex = len(APIResourceGroupOrder) + i
		}
		if jGroupIndex == -1 {
			jGroupIndex = len(APIResourceGroupOrder) + j
		}
		// 首先按 Group 索引排序
		if iGroupIndex != jGroupIndex {
			return iGroupIndex < jGroupIndex
		}

		// 如果 Group 索引相同，按 Version 排序
		if items[i].Version != items[j].Version {
			return items[i].Version < items[j].Version
		}

		// 最后按 Resource 排序
		return items[i].Resource < items[j].Resource
	})
}
