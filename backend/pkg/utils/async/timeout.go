package async

import (
	"context"
	"strings"
	"sync"
	"time"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
)

func NewTimeoutJobGroupAsync[T any](timeout time.Duration) JobGroupAsync[T] {
	result := &timeoutJobGroupAsync[T]{
		timeout: timeout,
	}
	return result
}

type timeoutJobGroupAsync[T any] struct {
	timeout time.Duration
	funcs   []func(ctx context.Context) (T, error)
}

func (timeout *timeoutJobGroupAsync[T]) AddJob(fn func(ctx context.Context) (T, error)) {
	var funcs []func(ctx context.Context) (T, error) = timeout.funcs
	funcs = append(funcs, fn)
	timeout.funcs = funcs
}
func (timeout *timeoutJobGroupAsync[T]) Result(ctx context.Context) ([]T, error) {
	var funcs []func(ctx context.Context) (T, error) = timeout.funcs
	var resultLock = &sync.Mutex{}
	var results = make([]T, 0, len(funcs))
	var errorList []error

	if len(funcs) == 0 {
		return results, nil
	}
	wg := &sync.WaitGroup{}
	wg.Add(len(funcs))
	sessionContext, cancel := context.WithTimeout(context.Background(), timeout.timeout)
	for _, fn := range funcs {
		fn := fn
		go func() {
			defer wg.Done()
			result, err := fn(sessionContext)
			resultLock.Lock()
			defer resultLock.Unlock()
			if err != nil {
				errorList = append(errorList, err)
				// error policy
				return
			}
			results = append(results, result)
		}()
	}

	wgChan := make(chan struct{})
	go func() {
		wg.Wait()
		wgChan <- struct{}{}
	}()

	select {
	case <-ctx.Done():
		// 先取消正在运行中的任务
		cancel()
		// 获取锁以返回数据
		resultLock.Lock()
		defer resultLock.Unlock()
		return results, handleErrors(errors.Var.AsyncJobForcedStop, errorList)
	case <-sessionContext.Done():
		// 获取锁以返回数据
		resultLock.Lock()
		defer resultLock.Unlock()
		return results, handleErrors(errors.Var.AsyncJobTimeout, errorList)
	case <-wgChan:
		// 获取锁以返回数据
		resultLock.Lock()
		defer resultLock.Unlock()
		if len(errorList) == 0 {
			return results, nil
		}
		return results, handleErrors(errors.Var.AsyncError, errorList)
	}
}

func handleErrors(codes errors.ErrorCode, errs []error) error {
	if len(errs) == 0 {
		return errors.NewFromCode(codes)
	}
	var errStrs = make([]string, 0, len(errs))
	for _, err := range errs {
		errStrs = append(errStrs, err.Error())
	}

	return errors.NewFromCodeWithMessage(codes, strings.Join(errStrs, "|"))
}
