package internal

import (
	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
)

// UpgradeRenderInterface is an interface that defines the methods to render the installer
type UpgradeRenderInterface interface {
	// Installer renders the upgrade request to the installer
	Installer(request clustermodel.UpgradeClusterRequest, installer *installerv1alpha1.Installer) error
}
