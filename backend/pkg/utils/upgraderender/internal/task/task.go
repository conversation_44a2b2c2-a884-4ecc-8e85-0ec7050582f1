package task

import (
	"context"
	"fmt"
	"strconv"

	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	hcclient "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/addon"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	sisyphus_solutionparam2 "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/upgraderender/internal/sisyphus_solutionparam"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/uuid"
	v1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	ctrlclient "sigs.k8s.io/controller-runtime/pkg/client"
)

type nodeInitialTask struct {
	// 读取下层集群的西西弗斯地址
	sisyphusConfigHandler addon.SisyphusConfigHandler
}

func NewNodeInitialTask() InstallerElement {
	return nodeInitialTask{
		sisyphusConfigHandler: addon.NewSisyphusConfigHandler(),
	}
}
func (n nodeInitialTask) Name() string {
	return constants.InstallerTaskNameInitialNode
}

func (n nodeInitialTask) Description() string {
	return constants.InstallerTaskDescriptionInitialNode
}

func (n nodeInitialTask) Kind() installerv1alpha1.InstallerTaskKind {
	return installerv1alpha1.InstallerTaskKindSisyphusNodeInitial
}

func (n nodeInitialTask) CreateRequestRenderToInstallerTask(existTasks []installerv1alpha1.InstallerTaskSpec, task *installerv1alpha1.InstallerTaskSpec, status *installerv1alpha1.InstallerTaskStatus, request clustermodel.UpgradeClusterRequest) error {
	if task.SisyphusNodeInitial == nil {
		task.SisyphusNodeInitial = new(installerv1alpha1.SisyphusNodeInitialTaskSpec)
	}
	sisyphusConfig, err := n.sisyphusConfigHandler.GetSisyphusURL(context.Background(), request.ClusterName)
	if err != nil {
		return err
	}
	task.SisyphusNodeInitial.URL = sisyphusConfig.Address
	task.SisyphusNodeInitial.Username = sisyphusConfig.Username
	task.SisyphusNodeInitial.Password = sisyphusConfig.Password
	var param installerv1alpha1.SisyphusNodeInitialParams
	for _, node := range request.Nodes {
		item := installerv1alpha1.SisyphusNodeInitialParam{
			IP:   node.Ip,
			Port: strconv.Itoa(node.Port),
		}
		if node.Auth == nil {
			return errors.NewFromCodeWithMessage(errors.Var.UnKnowAuthType, "auth type is nil")
		}
		node.Auth.AuthType = clustermodel.UserNameAndPasswordAuthType
		switch node.Auth.AuthType {
		case clustermodel.UserNameAndPasswordAuthType:
			var authInfo clustermodel.NodeAuthUsernameAndPasswordParamRequest
			if err := utils.BeanCopy(node.Auth.Param, &authInfo); err != nil {
				return err
			}
			if err := authInfo.Validate(); err != nil {
				return err
			}
			item.Username = authInfo.Username
			item.Password = authInfo.Password
			item.SudoPassword = authInfo.AuthorizationPassword
		default:
			return errors.NewFromCodeWithMessage(errors.Var.UnKnowAuthType, string(node.Auth.AuthType))
		}
		param = append(param, item)
	}
	task.SisyphusNodeInitial.Param = param
	return nil
}

type sisyphusSolution1xUpgradeApplyTask struct {
	uuid                   uuid.UUIDIntf
	parameterConfiguration sisyphus_solutionparam2.ParameterConfigurationInterface
	code                   string
	// 读取下层集群的西西弗斯地址
	sisyphusConfigHandler addon.SisyphusConfigHandler
}

func NewSisyphusSolutionApplyTask(uuid uuid.UUIDIntf) []InstallerElement {
	//// 组件部署任务
	//componentTask := sisyphusSolution1xUpgradeApplyTask{
	//	uuid:                   uuid,
	//	parameterConfiguration: sisyphus_solutionparam2.NewParamHandle(),
	//	code:                   constants.SolutionCodeComponents,
	//}
	//// 1.22k8s部署任务
	//k8s122Task := sisyphusSolution1xUpgradeApplyTask{
	//	uuid:                   uuid,
	//	parameterConfiguration: sisyphus_solutionparam2.NewParamHandle(),
	//	code:                   constants.SolutionCodeK8s122,
	//}
	//// 1.23k8s部署任务
	//k8s123Task := sisyphusSolution1xUpgradeApplyTask{
	//	uuid:                   uuid,
	//	parameterConfiguration: sisyphus_solutionparam2.NewParamHandle(),
	//	code:                   constants.SolutionCodeK8s123,
	//}
	//return []InstallerElement{componentTask, k8s122Task, k8s123Task}
	return nil
}

func (s sisyphusSolution1xUpgradeApplyTask) Name() string {
	switch s.code {
	case constants.SolutionCodeComponents:
		return constants.InstallerTaskNameSisyphusSolutionComponent
	case constants.SolutionCodeK8s122:
		return constants.InstallerTaskNameSisyphusSolution122k8s
	case constants.SolutionCodeK8s123:
		return constants.InstallerTaskNameSisyphusSolution123k8s
	}
	return ""
}

func (s sisyphusSolution1xUpgradeApplyTask) Description() string {
	switch s.code {
	case constants.SolutionCodeComponents:
		return constants.InstallerTaskDescriptionSisyphusSolutionComponent
	case constants.SolutionCodeK8s122:
		return constants.InstallerTaskDescriptionSisyphusSolution122k8s
	case constants.SolutionCodeK8s123:
		return constants.InstallerTaskDescriptionSisyphusSolution123k8s
	}
	return ""
}

func (s sisyphusSolution1xUpgradeApplyTask) Kind() installerv1alpha1.InstallerTaskKind {
	return installerv1alpha1.InstallerTaskKindSisyphusSolutionApply
}

func (s sisyphusSolution1xUpgradeApplyTask) CreateRequestRenderToInstallerTask(existTasks []installerv1alpha1.InstallerTaskSpec, task *installerv1alpha1.InstallerTaskSpec, status *installerv1alpha1.InstallerTaskStatus, request clustermodel.UpgradeClusterRequest) error {
	if task.SisyphusSolutionApply == nil {
		task.SisyphusSolutionApply = new(installerv1alpha1.SisyphusSolutionApplyTaskSpec)
	}
	sisyphusConfig, err := s.sisyphusConfigHandler.GetSisyphusURL(context.Background(), request.ClusterName)
	if err != nil {
		return err
	}
	task.SisyphusSolutionApply.URL = sisyphusConfig.Address
	task.SisyphusSolutionApply.Username = sisyphusConfig.Username
	task.SisyphusSolutionApply.Password = sisyphusConfig.Password
	if task.SisyphusSolutionApply.SolutionName == "" {
		task.SisyphusSolutionApply.SolutionName = s.uuid.UUID()
	}
	param, err := s.parameterConfiguration.CreateRequestAsParam(request, task.SisyphusSolutionApply.SolutionName)
	if err != nil {
		return err
	}
	//// 处理param
	task.SisyphusSolutionApply.Param = param
	// 查找token secret
	secret := v1.Secret{}
	if err := hcclient.GetLocalCluster().GetClient().GetCtrlClient().Get(context.Background(), ctrlclient.ObjectKey{Namespace: "stellaris-system", Name: "stellaris-core-register-token"}, &secret); err != nil {
		if k8serrors.IsNotFound(err) {
			err = errors.NewFromCodeWithMessage(errors.Var.StellariesSecretLost, fmt.Sprintf("%s/%s", "stellaris-system", "stellaris-core-register-token"))
		}
		return err
	}
	var bytes []byte
	var exist bool
	if secret.Data != nil {
		bytes, exist = secret.Data["token"]
	}
	if !exist {
		return errors.NewFromCodeWithMessage(errors.Var.StellariesSecretKeyLost, fmt.Sprintf("%s/%s,key=%s", "stellaris-system", "stellaris-core-register-token", "token"))
	}
	task.SisyphusSolutionApply.StellarisToken = bytes
	return nil
}

type sisyphusSolution1xUpgradeExecTask struct {
	code string
}

func NewSisyphusSolutionExecTask() []InstallerElement {
	componentFrontExecTask := sisyphusSolution1xUpgradeExecTask{
		code: constants.SolutionCodeComponentsFrontExecTaskCode,
	}
	k8s122ExecTask := sisyphusSolution1xUpgradeExecTask{
		code: constants.SolutionCodeK8s122ExecTaskCode,
	}
	k8s123ExecTask := sisyphusSolution1xUpgradeExecTask{
		code: constants.SolutionCodeK8s123ExecTaskCode,
	}
	componentRearExecTask := sisyphusSolution1xUpgradeExecTask{
		code: constants.SolutionCodeComponentsRearExecTaskCode,
	}
	return []InstallerElement{componentFrontExecTask, k8s122ExecTask, k8s123ExecTask, componentRearExecTask}
}

func (s sisyphusSolution1xUpgradeExecTask) Name() string {
	switch s.code {
	case constants.SolutionCodeComponentsFrontExecTaskCode:
		return constants.InstallerTaskNameSisyphusSolutionComponentFrontExec
	case constants.SolutionCodeK8s122ExecTaskCode:
		return constants.InstallerTaskNameSisyphusSolution122k8sExec
	case constants.SolutionCodeK8s123ExecTaskCode:
		return constants.InstallerTaskNameSisyphusSolution123k8sExec
	case constants.SolutionCodeComponentsRearExecTaskCode:
		return constants.InstallerTaskNameSisyphusSolutionComponentRearExec
	}
	return ""
}

func (s sisyphusSolution1xUpgradeExecTask) Description() string {
	switch s.code {
	case constants.SolutionCodeComponentsFrontExecTaskCode:
		return constants.InstallerTaskDescriptionSisyphusSolutionComponentFrontExec
	case constants.SolutionCodeK8s122ExecTaskCode:
		return constants.InstallerTaskDescriptionSisyphusSolution122k8sExec
	case constants.SolutionCodeK8s123ExecTaskCode:
		return constants.InstallerTaskDescriptionSisyphusSolution123k8sExec
	case constants.SolutionCodeComponentsRearExecTaskCode:
		return constants.InstallerTaskDescriptionSisyphusSolutionComponentRearExec
	}
	return ""
}

func (s sisyphusSolution1xUpgradeExecTask) Kind() installerv1alpha1.InstallerTaskKind {
	return installerv1alpha1.InstallerTaskKindSisyphusSolutionExec
}

func (s sisyphusSolution1xUpgradeExecTask) CreateRequestRenderToInstallerTask(existTasks []installerv1alpha1.InstallerTaskSpec, task *installerv1alpha1.InstallerTaskSpec, status *installerv1alpha1.InstallerTaskStatus, request clustermodel.UpgradeClusterRequest) error {
	return nil
}
