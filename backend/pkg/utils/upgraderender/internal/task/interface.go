package task

import (
	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
)

type InstallerElement interface {
	Name() string
	Description() string
	Kind() installerv1alpha1.InstallerTaskKind
	CreateRequestRenderToInstallerTask(existTasks []installerv1alpha1.InstallerTaskSpec, task *installerv1alpha1.InstallerTaskSpec, status *installerv1alpha1.InstallerTaskStatus, request clustermodel.UpgradeClusterRequest) error
}
