package sisyphus_solutionparam

import (
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"k8s.io/apimachinery/pkg/util/sets"
)

const (
	jobParamNameCommonParamConfig         = "A001-通用参数"
	jobParamNameNginxIngressParamConfig   = "A002-负载均衡参数"
	jobParamNameClusterManagerParamConfig = "A003-集群生命周期管理参数"
)

type apiServerLbConfigJobParamStep struct {
	shouldConvertNodeConfigType sets.Set[clustermodel.NodeConfigType]
}

//
//func newApiServerLbConfigJobParamStep() jobParamStep {
//	return apiServerLbConfigJobParamStep{
//		// 只有最小化高可用 和标准高可用 才需要转化
//		shouldConvertNodeConfigType: sets.New[clustermodel.NodeConfigType](clustermodel.NodeConfigTypeMinimizeHA, clustermodel.NodeConfigTypeStandardHA),
//	}
//}
//
//// 获取步骤名称
//func (apiServerLbConfigJobParamStep) name() string {
//	return jobParamNameApiServerLbConfig
//}
//
//// 判断步骤是否需要转化 如没有macvlan 就不需要转化macvlan 的请求参数
//func (step apiServerLbConfigJobParamStep) shouldConvert(createRequest clustermodel.CreateRequest) (bool, error) {
//	flag := step.shouldConvertNodeConfigType.Has(createRequest.NodeConfigs.NodeConfigType)
//	return flag, nil
//}
//
//// 转化请求参数
//func (step apiServerLbConfigJobParamStep) convert(createRequest clustermodel.CreateRequest) (map[string]interface{}, error) {
//	// vrid 已确认写死 110
//	return newApiServerLbModel(createRequest.NetworkConfigs.ApiServerVIP.AsVIP(), "110", createRequest.NetworkConfigs.ApiServerVIP.NetworkCard).asProperty()
//}
//
//// 将property 的内容渲染到 create response
//func (step apiServerLbConfigJobParamStep) renderCreateResponse(property map[string]interface{}, createResponse *clustermodel.CreateResponse) error {
//	apiServerLbModel := apiServerLbModel{}
//	if err := utils.BeanCopy(property, &apiServerLbModel); err != nil {
//		return err
//	}
//	vipCidr := apiServerLbModel.VIP.ApiServer.VIPs[0].Vips[0]
//	networkCard := apiServerLbModel.VIP.ApiServer.VIPs[0].NetworkInterfaces
//	vipCidrParse := strings.Split(vipCidr, "/")
//	vip, mast := vipCidrParse[0], vipCidrParse[1]
//	maskInt, err := strconv.Atoi(mast)
//	if err != nil {
//		return err
//	}
//	if createResponse.NetworkConfigs.ApiServerVIP == nil {
//		createResponse.NetworkConfigs.ApiServerVIP = new(clustermodel.CreateApiServerVipConfigResponse)
//	}
//	createResponse.NetworkConfigs.ApiServerVIP.Address = vip
//	createResponse.NetworkConfigs.ApiServerVIP.Mask = maskInt
//	createResponse.NetworkConfigs.ApiServerVIP.NetworkCard = networkCard
//	return nil
//}
//
//// 将property 的内容渲染到 create status response
//func (step apiServerLbConfigJobParamStep) renderCreateStatusResponse(property map[string]interface{}, createResponse *clustermodel.CreateStatusResponse) error {
//	// nothing to do
//	return nil
//}
//
//// 将property 的内容渲染到 create response
//func (step apiServerLbConfigJobParamStep) renderResponse(property map[string]interface{}, response *clustermodel.Response) error {
//	// nothing to do
//	return nil
//}
//
//type macvlanNetworkConfigJobParamStep struct {
//}
//
//func newMacvlanNetworkConfigJobParamStep() jobParamStep {
//	return macvlanNetworkConfigJobParamStep{}
//}
//
//// 获取步骤名称
//func (macvlanNetworkConfigJobParamStep) name() string {
//	return jobParamNameMacvlanNetworkConfig
//}
//
//// 判断步骤是否需要转化 如没有macvlan 就不需要转化macvlan 的请求参数
//func (macvlanNetworkConfigJobParamStep) shouldConvert(createRequest clustermodel.CreateRequest) (bool, error) {
//	_, exist := createRequest.NetworkConfigs.CNIs[clustermodel.CNITypeMacvlan]
//	return exist, nil
//}
//
//// 转化请求参数
//func (macvlanNetworkConfigJobParamStep) convert(createRequest clustermodel.CreateRequest) (map[string]interface{}, error) {
//	cniConfig := createRequest.NetworkConfigs.CNIs[clustermodel.CNITypeMacvlan]
//	ipRequestFunc := func(src map[string]interface{}) (*clustermodel.CreateCNIConfigMacvlanParamRequest, error) {
//		var target *clustermodel.CreateCNIConfigMacvlanParamRequest
//		if src != nil {
//			target = new(clustermodel.CreateCNIConfigMacvlanParamRequest)
//			if err := utils.BeanCopy(src, target); err != nil {
//				return nil, err
//			}
//			if err := target.Validator(); err != nil {
//				return nil, err
//			}
//		}
//		return target, nil
//	}
//	var ipv4Request *clustermodel.CreateCNIConfigMacvlanParamRequest
//	var ipv6Request *clustermodel.CreateCNIConfigMacvlanParamRequest
//	var requestErr error
//	if ipv4Request, requestErr = ipRequestFunc(cniConfig.Ipv4Param); requestErr != nil {
//		return nil, requestErr
//	}
//	if ipv6Request, requestErr = ipRequestFunc(cniConfig.Ipv6Param); requestErr != nil {
//		return nil, requestErr
//	}
//	subnetConvertFunc := func(request *clustermodel.CreateCNIConfigMacvlanParamRequest) *MacvlanSubnet {
//		var target *MacvlanSubnet
//		if request != nil {
//			target = new(MacvlanSubnet)
//			target.StartIp = strings.ToLower(request.StartIP)
//			target.EndIp = strings.ToLower(request.EndIP)
//			target.Gateway = strings.ToLower(request.Gateway)
//			target.Prefix = request.Mask
//			// 校验在上一步完成
//			reservedIpList, _ := request.ReservedIpList()
//			if len(reservedIpList) > 0 {
//				for _, ipSec := range reservedIpList {
//					target.ExcludeIPs = append(target.ExcludeIPs, MacvlanExcludeIP{
//						StartIp: strings.ToLower(ipSec[0]),
//						EndIp:   strings.ToLower(ipSec[1]),
//					})
//				}
//			}
//		}
//		return target
//	}
//	return newMacvlanNetworkConfig(ipv4Request.NetworkCard, ipv4Request.VlanId, subnetConvertFunc(ipv4Request), subnetConvertFunc(ipv6Request)).asProperty()
//}
//
//// 将property 的内容渲染到 create response
//func (macvlanNetworkConfigJobParamStep) renderCreateResponse(property map[string]interface{}, createResponse *clustermodel.CreateResponse) error {
//	// 初始化必要数据
//	createResponse.NetworkConfigs.PlatformCNITypes = append(createResponse.NetworkConfigs.PlatformCNITypes, clustermodel.CNITypeMacvlan)
//	if createResponse.NetworkConfigs.CNIs == nil {
//		createResponse.NetworkConfigs.CNIs = make(map[clustermodel.CNIType]clustermodel.CreateCNIConfigResponse)
//	}
//
//	macvlanNetworkModel := networkModel{}
//	if err := utils.BeanCopy(property, &macvlanNetworkModel); err != nil {
//		return err
//	}
//	dualStack := macvlanNetworkModel.Network.Macvlan.SubnetInfo.Ipv4 != nil && macvlanNetworkModel.Network.Macvlan.SubnetInfo.Ipv6 != nil
//	asParamFunc := func(subnet *MacvlanSubnet, networkCard string, vlanId int) map[string]interface{} {
//		var result map[string]interface{}
//		if subnet == nil {
//			return result
//		}
//		var param clustermodel.CreateCNIConfigMacvlanParamRequest
//		param.StartIP = subnet.StartIp
//		param.EndIP = subnet.EndIp
//		param.Gateway = subnet.Gateway
//		param.Mask = subnet.Prefix
//		param.VlanId = vlanId
//		param.NetworkCard = networkCard
//		if len(subnet.ExcludeIPs) != 0 {
//			var ipSecList []string
//			for index, _ := range subnet.ExcludeIPs {
//				ipSecList = append(ipSecList, strings.Join([]string{subnet.ExcludeIPs[index].StartIp, subnet.ExcludeIPs[index].EndIp}, "~"))
//			}
//			param.ReservedIp = strings.Join(ipSecList, ", ")
//		}
//		_ = utils.BeanCopy(param, &result)
//		return result
//	}
//
//	createResponse.NetworkConfigs.CNIs[clustermodel.CNITypeMacvlan] = clustermodel.CreateCNIConfigResponse{
//		DualStack: dualStack,
//		Ipv4Param: asParamFunc(macvlanNetworkModel.Network.Macvlan.SubnetInfo.Ipv4, macvlanNetworkModel.Network.Macvlan.NetworkCard, macvlanNetworkModel.Network.Macvlan.Vlanid),
//		Ipv6Param: asParamFunc(macvlanNetworkModel.Network.Macvlan.SubnetInfo.Ipv6, macvlanNetworkModel.Network.Macvlan.NetworkCard, macvlanNetworkModel.Network.Macvlan.Vlanid),
//	}
//	return nil
//}
//
//// 将property 的内容渲染到 create status response
//func (macvlanNetworkConfigJobParamStep) renderCreateStatusResponse(property map[string]interface{}, createsStatusResponse *clustermodel.CreateStatusResponse) error {
//	createsStatusResponse.CNITypes = append(createsStatusResponse.CNITypes, clustermodel.CNITypeMacvlan)
//	return nil
//}
//
//// 将property 的内容渲染到 create response
//func (macvlanNetworkConfigJobParamStep) renderResponse(property map[string]interface{}, response *clustermodel.Response) error {
//	response.CNITypes = append(response.CNITypes, clustermodel.CNITypeMacvlan)
//	return nil
//}
//
//type kubeOVNNetworkConfigJobParamStep struct {
//}
//
//func newKubeOVNNetworkConfigJobParamStep() jobParamStep {
//	return kubeOVNNetworkConfigJobParamStep{}
//}
//
//// 获取步骤名称
//func (kubeOVNNetworkConfigJobParamStep) name() string {
//	return jobParamNameKubeOVNNetworkConfig
//}
//
//// 判断步骤是否需要转化 如没有macvlan 就不需要转化macvlan 的请求参数
//func (kubeOVNNetworkConfigJobParamStep) shouldConvert(createRequest clustermodel.CreateRequest) (bool, error) {
//	_, exist := createRequest.NetworkConfigs.CNIs[clustermodel.CNITypeKubeOVM]
//	return exist, nil
//}
//
//// 转化请求参数
//func (kubeOVNNetworkConfigJobParamStep) convert(createRequest clustermodel.CreateRequest) (map[string]interface{}, error) {
//	var obj clustermodel.CreateCNIConfigKubeOVNParamRequest
//	if err := utils.BeanCopy(createRequest.NetworkConfigs.CNIs[clustermodel.CNITypeKubeOVM].Ipv4Param, &obj); err != nil {
//		return nil, err
//	}
//	if err := obj.Validator(); err != nil {
//		return nil, err
//	}
//	return newKubeOvnNetworkConfig(obj.NetworkCard).asProperty()
//}
//
//// 将property 的内容渲染到 create response
//func (kubeOVNNetworkConfigJobParamStep) renderCreateResponse(property map[string]interface{}, createResponse *clustermodel.CreateResponse) error {
//	// 初始化必要数据
//	createResponse.NetworkConfigs.PlatformCNITypes = append(createResponse.NetworkConfigs.PlatformCNITypes, clustermodel.CNITypeKubeOVM)
//	if createResponse.NetworkConfigs.CNIs == nil {
//		createResponse.NetworkConfigs.CNIs = make(map[clustermodel.CNIType]clustermodel.CreateCNIConfigResponse)
//	}
//	kubeOvnModel := kubeOvnModel{}
//	if err := utils.BeanCopy(property, &kubeOvnModel); err != nil {
//		return err
//	}
//
//	var ipV4Param map[string]interface{}
//	var ipV4ParamStrct = clustermodel.CreateCNIConfigKubeOVNParamRequest{
//		NetworkCard: kubeOvnModel.Kubevirt.Ovn.NetworkCard,
//	}
//	if err := utils.BeanCopy(ipV4ParamStrct, &ipV4Param); err != nil {
//		return err
//	}
//
//	createResponse.NetworkConfigs.CNIs[clustermodel.CNITypeKubeOVM] = clustermodel.CreateCNIConfigResponse{
//		DualStack: false,
//		Ipv4Param: ipV4Param,
//	}
//	return nil
//}
//
//// 将property 的内容渲染到 create response
//func (kubeOVNNetworkConfigJobParamStep) renderResponse(property map[string]interface{}, response *clustermodel.Response) error {
//	response.CNITypes = append(response.CNITypes, clustermodel.CNITypeKubeOVM)
//	return nil
//}
//
//// 将property 的内容渲染到 create status response
//func (kubeOVNNetworkConfigJobParamStep) renderCreateStatusResponse(property map[string]interface{}, createsStatusResponse *clustermodel.CreateStatusResponse) error {
//	createsStatusResponse.CNITypes = append(createsStatusResponse.CNITypes, clustermodel.CNITypeKubeOVM)
//	return nil
//}
//
//type standerComponentConfigJobParamStep struct {
//}
//
//func newStanderComponentConfigJobParamStep() jobParamStep {
//	return standerComponentConfigJobParamStep{}
//}
//
//// 获取步骤名称
//func (standerComponentConfigJobParamStep) name() string {
//	return jobParamNameStanderComponentConfig
//}
//
//// 判断步骤是否需要转化 如没有macvlan 就不需要转化macvlan 的请求参数
//func (standerComponentConfigJobParamStep) shouldConvert(createRequest clustermodel.CreateRequest) (bool, error) {
//	return true, nil
//}
//
//// 转化请求参数
//func (standerComponentConfigJobParamStep) convert(createRequest clustermodel.CreateRequest) (map[string]interface{}, error) {
//	return newStanderComponentConfigModel().asProperty()
//}
//
//// 将property 的内容渲染到 create response
//func (standerComponentConfigJobParamStep) renderCreateResponse(property map[string]interface{}, createResponse *clustermodel.CreateResponse) error {
//	// 标准版组件配置 不需要处理
//	return nil
//}
//
//type defaultLbParentLbConfigJobParamStep struct {
//	shouldConvertNodeConfigType sets.Set[clustermodel.NodeConfigType]
//}
//
//func newDefaultLbParentLbConfigJobParamStep() jobParamStep {
//	return defaultLbParentLbConfigJobParamStep{
//		// 只有最小化高可用 和标准高可用 才需要转化
//		shouldConvertNodeConfigType: sets.New[clustermodel.NodeConfigType](clustermodel.NodeConfigTypeMinimizeHA, clustermodel.NodeConfigTypeStandardHA),
//	}
//}
//
//// 获取步骤名称
//func (defaultLbParentLbConfigJobParamStep) name() string {
//	return jobParamNameDefaultLbParentLbConfig
//}
//
//// 判断步骤是否需要转化 如没有macvlan 就不需要转化macvlan 的请求参数
//func (step defaultLbParentLbConfigJobParamStep) shouldConvert(createRequest clustermodel.CreateRequest) (bool, error) {
//	flag := step.shouldConvertNodeConfigType.Has(createRequest.NodeConfigs.NodeConfigType)
//	return flag, nil
//}
//
//// 转化请求参数
//func (defaultLbParentLbConfigJobParamStep) convert(createRequest clustermodel.CreateRequest) (map[string]interface{}, error) {
//	return newDefaultLbParentLbModel(createRequest.NetworkConfigs.LoadBalance.AsVIP(), "120", createRequest.NetworkConfigs.LoadBalance.NetworkCard).asProperty()
//}
//
//// 将property 的内容渲染到 create response
//func (defaultLbParentLbConfigJobParamStep) renderCreateResponse(property map[string]interface{}, createResponse *clustermodel.CreateResponse) error {
//	var lbModel defaultLbParentLbModel
//	if err := utils.BeanCopy(property, &lbModel); err != nil {
//		return err
//	}
//	networkCard := lbModel.VIP.Ingress.VIPs[0].NetworkInterfaces
//	cidr := lbModel.VIP.Ingress.VIPs[0].Vips[0]
//	cidrSplit := strings.Split(cidr, "/")
//	ip, maskStr := cidrSplit[0], cidrSplit[1]
//	maskInt, _ := strconv.Atoi(maskStr)
//	createResponse.NetworkConfigs.LoadBalance = &clustermodel.CreateLbConfigResponse{
//		Address:     ip,
//		Mask:        maskInt,
//		NetworkCard: networkCard,
//	}
//	return nil
//}
//
//// 将property 的内容渲染到 create status response
//func (defaultLbParentLbConfigJobParamStep) renderCreateStatusResponse(property map[string]interface{}, createResponse *clustermodel.CreateStatusResponse) error {
//	// nothing to do
//	return nil
//}
//
//// 将property 的内容渲染到 create response
//func (defaultLbParentLbConfigJobParamStep) renderResponse(property map[string]interface{}, response *clustermodel.Response) error {
//	// nothing to do
//	return nil
//}
//
//func NewStorageClassConfigJobParamStep() jobParamStep {
//	return StorageClassConfigJobParamStep{}
//}
//
//type StorageClassConfigJobParamStep struct {
//}
//
//// 获取步骤名称
//func (StorageClassConfigJobParamStep) name() string {
//	return jobParamNameStorageClassConfig
//}
//
//// 判断步骤是否需要转化 如没有macvlan 就不需要转化macvlan 的请求参数
//func (StorageClassConfigJobParamStep) shouldConvert(createRequest clustermodel.CreateRequest) (bool, error) {
//	return true, nil
//}
//
//// 转化请求参数
//func (StorageClassConfigJobParamStep) convert(createRequest clustermodel.CreateRequest) (map[string]interface{}, error) {
//	return NewStorageProperty("caas-lvm").asProperty()
//}
//
//// 将property 的内容渲染到 create response
//func (StorageClassConfigJobParamStep) renderCreateResponse(property map[string]interface{}, createResponse *clustermodel.CreateResponse) error {
//	return nil
//}
//
//// 将property 的内容渲染到 create response
//func (StorageClassConfigJobParamStep) renderResponse(property map[string]interface{}, response *clustermodel.Response) error {
//	return nil
//}
//
//// 将property 的内容渲染到 create status response
//func (StorageClassConfigJobParamStep) renderCreateStatusResponse(property map[string]interface{}, createResponse *clustermodel.CreateStatusResponse) error {
//	return nil
//}
