package sisyphus_solutionparam

import (
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
)

type ParameterConfigurationInterface interface {
	CreateRequestAsParam(createRequest clustermodel.UpgradeClusterRequest, solutionName string) (string, error)
}

// jobParamStep
// 生成西西弗斯 solution param 每个步骤的内容
type jobParamStep interface {
	// 获取步骤名称
	name() string
	shouldConvert(createRequest clustermodel.UpgradeClusterRequest) (bool, error)
	convert(createRequest clustermodel.UpgradeClusterRequest) (map[string]interface{}, error)
}

// optionStep
type optionStep interface {
	name() string
	option(createRequest clustermodel.UpgradeClusterRequest) (interface{}, error)
}

type jobParamStepProperty interface {
	asProperty() (map[string]interface{}, error)
}
