package sisyphus_solutionparam

const (
	optionNameCommon  = "common"
	optionNameExtra   = "extra"
	optionNameNetwork = "network"
	optionNameLogging = "logging"
	optionNameMonitor = "monitor"
	optionNameLabs    = "labs"
)

//type commonOptionStep struct {
//}
//
//func newCommonOptionStep() optionStep {
//	return commonOptionStep{}
//}
//func (commonOptionStep) name() string {
//	return optionNameCommon
//}
//func (commonOptionStep) option(createRequest clustermodel.CreateRequest) (interface{}, error) {
//	// 处理解决方案是否为高可用
//	var arch CaasInfraDeployArchType
//	var defaultKubernetesMasterScheduler bool
//	var defaultDeployApiserverLB, defaultDeployIngressVIP bool
//	var defaultDeployMixLB *bool
//
//	switch createRequest.NodeConfigs.NodeConfigType {
//
//	default:
//		return nil, errors.NewFromCodeWithMessage(errors.Var.ParamError, "un support node type"+string(createRequest.NodeConfigs.NodeConfigType))
//	}
//
//	return commonOption{
//		AutoCalVolumeSizeForAllComponents: true,
//		CaasInfraDeployArch:               arch,
//		// CaasInfraVersion 写死 专业版
//		CaasInfraVersion:            "专业版",
//		DefaultCreateSystemNodePool: true,
//		// DefaultDeployApiserverLB
//		// DefaultDeployIngressVIP 只有高可用才需要填写true 否则填写false
//		DefaultDeployApiserverLB:         defaultDeployApiserverLB,
//		DefaultDeployIngressVIP:          defaultDeployIngressVIP,
//		DefaultDeployMixLB:               defaultDeployMixLB,
//		DefaultKubernetesMasterScheduler: defaultKubernetesMasterScheduler,
//		// IsControlPlane 写死false
//		IsControlPlane: false,
//		// 预检写死true
//		PreCheck:                true,
//		SkyviewTokenAuthWebhook: true,
//		AutoEditHostname:        true,
//	}, nil
//}
//
//type extraOptionStep struct {
//}
//
//func newExtraOptionStep() optionStep {
//	return extraOptionStep{}
//}
//
//func (extraOptionStep) name() string {
//	return optionNameExtra
//}
//func (extraOptionStep) option(request clustermodel.UpgradeClusterRequest) (interface{}, error) {
//	obj := extraOption{
//		DefaultDeployChrony:                 true,
//		DefaultDeployGlusterFS:              false,
//		DefaultDeployHarbor:                 false,
//		DefaultDeployMinio:                  false,
//		UseExternalShareStorage:             false,
//		DefaultDeploySisyphusClusterManager: false,
//	}
//
//	return obj, nil
//}
//
//type networkOptionStep struct {
//}
//
//func newNetworkOptionStep() optionStep {
//	return networkOptionStep{}
//}
//func (networkOptionStep) name() string {
//	return optionNameNetwork
//}
//func (networkOptionStep) option(createRequest clustermodel.CreateRequest) (interface{}, error) {
//	option := &networkOption{}
//	if config, exist := createRequest.NetworkConfigs.CNIs[clustermodel.CNITypeCalico]; exist {
//		stack, err := getNetworkStackValue(len(config.Ipv4Param) != 0, len(config.Ipv6Param) != 0)
//		if err != nil {
//			return nil, err
//		}
//		option.Calico.Install = true
//		option.Calico.Stack = stack
//		option.Calico.Property = map[string]interface{}{}
//		switch stack {
//		case networkStackIpv4:
//			option.Calico.Property["v4Mode"] = "ipip"
//		case networkStackIpv6:
//			option.Calico.Property["v6Mode"] = "bgp"
//		case networkStackDual:
//			option.Calico.Property["v4Mode"] = "ipip"
//			option.Calico.Property["v6Mode"] = "bgp"
//		}
//	} else {
//		option.Calico.Install = false
//	}
//	// 处理 macvlan
//	if config, exist := createRequest.NetworkConfigs.CNIs[clustermodel.CNITypeMacvlan]; exist {
//		stack, err := getNetworkStackValue(len(config.Ipv4Param) != 0, len(config.Ipv6Param) != 0)
//		if err != nil {
//			return nil, err
//		}
//		option.Bifrost.Install = true
//		option.Bifrost.Stack = stack
//	} else {
//		option.Bifrost.Install = false
//	}
//
//	// todo 处理 kube-ovm
//	if _, exist := createRequest.NetworkConfigs.CNIs[clustermodel.CNITypeKubeOVM]; exist {
//
//	} else {
//
//	}
//
//	return option, nil
//}
//
//type loggingOptionStep struct {
//}
//
//func newLoggingOptionStep() optionStep {
//	return loggingOptionStep{}
//}
//func (loggingOptionStep) name() string {
//	return optionNameLogging
//}
//func (loggingOptionStep) option(createRequest clustermodel.CreateRequest) (interface{}, error) {
//	option := &loggingOption{
//		CreateBackupStorage: false,
//	}
//	return option, nil
//}
//
//type monitorOptionStep struct {
//}
//
//func newMonitorOptionStep() optionStep {
//	return monitorOptionStep{}
//}
//func (monitorOptionStep) name() string {
//	return optionNameMonitor
//}
//func (monitorOptionStep) option(createRequest clustermodel.CreateRequest) (interface{}, error) {
//	option := &monitorOption{
//		EnableSkyviewAlertWebhook: true,
//		EnableZeusAlertWebhook:    false,
//	}
//	return option, nil
//}
//
//type labsOptionStep struct {
//}
//
//func newLabsOptionStep() optionStep {
//	return labsOptionStep{}
//}
//
//func (labsOptionStep) name() string {
//	return optionNameLabs
//}
//func (labsOptionStep) option(request clustermodel.UpgradeClusterRequest) (interface{}, error) {
//	_, deployKubeVirt := request.NetworkConfigs.CNIs[clustermodel.CNITypeKubeOVM]
//	return &labsOption{
//		DeployKubevirt: deployKubeVirt,
//	}, nil
//}
