package sisyphus_solutionparam

import "harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"

// CaasInfraDeployArchType
// 部署版本 如果节点是标准高可用｜最小化高可用 则版本为 高可用
// 非以上情况是 非高可用
type CaasInfraDeployArchType string

var (
	CaasInfraDeployArchTypeHA     CaasInfraDeployArchType = "高可用"
	CaasInfraDeployArchTypeNoneHA CaasInfraDeployArchType = "非高可用"
)

type commonOption struct {
	AutoCalVolumeSizeForAllComponents bool                    `json:"autoCalVolumeSizeForAllComponents"`
	CaasInfraDeployArch               CaasInfraDeployArchType `json:"caasInfraDeployArch"`
	CaasInfraVersion                  string                  `json:"caasInfraVersion"`
	DefaultCreateSystemNodePool       bool                    `json:"defaultCreateSystemNodePool"`
	DefaultDeployApiserverLB          bool                    `json:"defaultDeployApiserverLB"`
	DefaultDeployIngressVIP           bool                    `json:"defaultDeployIngressVIP"`
	DefaultDeployMixLB                *bool                   `json:"defaultDeployMixLB,omitempty"` // 高可用时填写该参数为false
	DefaultKubernetesMasterScheduler  bool                    `json:"defaultKubernetesMasterScheduler"`
	IsControlPlane                    bool                    `json:"isControlPlane"`
	PreCheck                          bool                    `json:"precheck"` // 是否预检
	SkyviewTokenAuthWebhook           bool                    `json:"skyviewTokenAuthWebhook"`
	AutoEditHostname                  bool                    `json:"autoEditHostname"`
}

type extraOption struct {
	DefaultSupportArchOS                string `json:"defaultSupportArchOS"`
	DefaultDeployChrony                 bool   `json:"defaultDeployChrony"`
	DefaultDeployGlusterFS              bool   `json:"defaultDeployGlusterFS"`
	DefaultDeployHarbor                 bool   `json:"defaultDeployHarbor"`
	DefaultDeployHarborVIP              *bool  `json:"defaultDeployHarborVIP,omitempty"`
	DefaultDeployMinio                  bool   `json:"defaultDeployMinio"`
	UseExternalShareStorage             bool   `json:"useExternalShareStorage"`
	UseExternalRegistry                 bool   `json:"useExternalRegistry"`
	DefaultDeploySisyphusClusterManager bool   `json:"defaultDeploySisyphusClusterManager"`
}

type networkOption struct {
	Bifrost networkBifrost `json:"bifrost"`
	Calico  networkCalico  `json:"calico"`
}

type networkStack string

// getNetworkStackValue
// 工具网络地址族类型判断是ipv4 还是 ipv6 还是双栈
func getNetworkStackValue(hasIpv4 bool, hasIpv6 bool) (networkStack, error) {
	var ipFamily networkStack
	switch {
	case hasIpv4 && hasIpv6:
		ipFamily = networkStackDual
	case hasIpv4:
		ipFamily = networkStackIpv4
	case hasIpv6:
		ipFamily = networkStackIpv6
	default:
		return ipFamily, errors.NewFromCodeWithMessage(errors.Var.ParamError, "un support all false of hasIpv4 and hasIpv6")
	}
	return ipFamily, nil
}

var (
	networkStackIpv4 networkStack = "ipv4"
	networkStackIpv6 networkStack = "ipv6" // + 当前不支持纯IPv6 未来扩展
	networkStackDual networkStack = "dual"
)

type networkBifrost struct {
	Install bool         `json:"install"`
	Stack   networkStack `json:"stack,omitempty"`
}

type networkCalico struct {
	Install  bool                   `json:"install"`
	Stack    networkStack           `json:"stack,omitempty"`
	Property map[string]interface{} `json:"installProperties,omitempty"`
}

type loggingOption struct {
	CreateBackupStorage bool `json:"createBackupStorage"`
}

type monitorOption struct {
	EnableSkyviewAlertWebhook bool `json:"enableSkyviewAlertWebhook"`
	EnableZeusAlertWebhook    bool `json:"enableZeusAlertWebhook"`
}

type labsOption struct {
	DeployKubevirt bool `json:"deployKubevirt"`
}
