package sisyphus_solutionparam

import (
	"harmonycloud.cn/unifiedportal/cloudservice-operator/pkg/handler/installer/client"
)

type parameter struct {
	sisyphusClient client.Sisyphus
	jobParams      []jobParamStep
	options        []optionStep
}

//func NewParamHandle() ParameterConfigurationInterface {
//	return parameter{
//		sisyphusClient: client.NewSisyphus(),
//		jobParams:      []jobParamStep{},
//		options:        []optionStep{},
//	}
//}

//func (p parameter) CreateRequestAsParam(request clustermodel.UpgradeClusterRequest, solutionName string) (string, error) {
//	options, err := p.parseSisyphusOption(request)
//	if err != nil {
//		return "", err
//	}
//	jobParam, err := p.parseSisyphusJobParams(request)
//	if err != nil {
//		return "", err
//	}
//	solutionInfo, exist := config.UpgradeClusterConfig.SolutionInfos.GetSolutionInfoByGroup(solutionName)
//	if !exist {
//		return "", errors.NewFromCodeWithMessage(errors.Var.ParamError, fmt.Sprintf("can not find solution by kubernetesVersion:%s,kubernetesCRIVersion:%s"))
//	}
//	hosts, err := parseSisyphusHostGroups(request)
//	if err != nil {
//		return "", err
//	}
//	editContext := sisyphustype.EditDeployContext{}
//	if err != nil {
//		return "", err
//	}
//
//	//sisyphusParam := sisyphustype.EditDeployRequest{
//	//	Name: solutionName,
//	//	Solution: sisyphustype.SolutionMetadata{
//	//		Name:    solutionInfo.SolutionName,
//	//		Version: solutionInfo.SolutionVersion,
//	//	}, Contexts: editContext,
//	//	Hosts:   hosts,
//	//	Options: options,
//	//	Params:  jobParam,
//	//}
//	//jsonBytes, err := json.Marshal(sisyphusParam)
//	//return string(jsonBytes), err
//	return "", nil
//}
//
//func (p parameter) parseSisyphusOption(request clustermodel.UpgradeClusterRequest) (map[string]interface{}, error) {
//	var optionMap = make(map[string]interface{}, len(p.options)<<1)
//	for _, option := range p.options {
//		option := option
//		optionIntf, err := option.option(request)
//		if err != nil {
//			return nil, err
//		}
//		optionMap[option.name()] = optionIntf
//	}
//	return optionMap, nil
//}
//
//func (p parameter) parseSisyphusJobParams(request clustermodel.UpgradeClusterRequest) ([]sisyphustype.JobParam, error) {
//	var jobParams []sisyphustype.JobParam
//	for _, jobParam := range p.jobParams {
//		shouldConvert, err := jobParam.shouldConvert(request)
//		if err != nil {
//			return nil, err
//		}
//		if !shouldConvert {
//			continue
//		}
//		name := jobParam.name()
//		cvt, err := jobParam.convert(request)
//		if err != nil {
//			return nil, err
//		}
//		jobParams = append(jobParams, sisyphustype.JobParam{
//			Name:       name,
//			Properties: cvt,
//		})
//	}
//	return jobParams, nil
//}
//func parseSisyphusHostGroups(request clustermodel.UpgradeClusterRequest) ([]sisyphustype.HostGroups, error) {
//	var result []sisyphustype.HostGroups
//	for _, node := range request.Nodes {
//		hostGroup := sisyphustype.HostGroups{
//			IP: node.Ip,
//		}
//		result = append(result, hostGroup)
//	}
//	var first, second, third, fourth, fifth, sixth, seventh, other []sisyphustype.HostGroups
//	var nodeConfig config.NodeGroupConfig
//	switch request.NodeConfigs.NodeConfigType {
//	case clustermodel.NodeConfigTypeAllInOne:
//		first = result[0:1]
//		other = result[1:]
//		nodeConfig = config.CreateClusterConfig.NodeTypeGroup.AllInOne
//	case clustermodel.NodeConfigTypeStandardNoneHA:
//		first = result[0:1]
//		second = result[1:2]
//		third = result[2:3]
//		fourth = result[3:4]
//		other = result[4:]
//		nodeConfig = config.CreateClusterConfig.NodeTypeGroup.StandardNoneHA
//	case clustermodel.NodeConfigTypeMinimizeHA:
//		first = result[0:1]
//		second = result[1:2]
//		third = result[2:3]
//		other = result[3:]
//		nodeConfig = config.CreateClusterConfig.NodeTypeGroup.MinimizeHA
//	case clustermodel.NodeConfigTypeStandardHA:
//		first = result[0:1]
//		second = result[1:2]
//		third = result[2:3]
//		fourth = result[3:4]
//		fifth = result[4:5]
//		sixth = result[5:6]
//		seventh = result[6:7]
//		other = result[7:]
//		nodeConfig = config.CreateClusterConfig.NodeTypeGroup.StandardHA
//	default:
//		return result, errors.NewFromCode(errors.Var.NodeConfigTypeIllegal)
//	}
//
//	setNodeGroupFunc := func(nodes []sisyphustype.HostGroups, groups []string) {
//		for index, _ := range nodes {
//			if len(nodes[index].Groups) == 0 {
//				nodes[index].Groups = groups
//			} else {
//				nodes[index].Groups = append(nodes[index].Groups, groups...)
//			}
//		}
//	}
//	setNodeGroupFunc(first, nodeConfig.First)
//	setNodeGroupFunc(second, nodeConfig.Second)
//	setNodeGroupFunc(third, nodeConfig.Third)
//	setNodeGroupFunc(fourth, nodeConfig.Fourth)
//	setNodeGroupFunc(fifth, nodeConfig.Fifth)
//	setNodeGroupFunc(sixth, nodeConfig.Sixth)
//	setNodeGroupFunc(seventh, nodeConfig.Seventh)
//	setNodeGroupFunc(other, nodeConfig.Other)
//	// 处理 plugin
//	var dealGroup []*[]string
//	for index, _ := range result {
//		dealGroup = append(dealGroup, &result[index].Groups)
//	}
//
//	return result, nil
//}
