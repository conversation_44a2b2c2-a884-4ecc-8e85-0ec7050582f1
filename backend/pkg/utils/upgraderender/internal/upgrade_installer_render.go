package internal

import (
	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	task2 "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/upgraderender/internal/task"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/uuid"
)

type metaName struct {
	prefix string
}

func NewMetaName(prefix string) UpgradeRenderInterface {
	return metaName{
		prefix: prefix,
	}
}

func (m metaName) Installer(request clustermodel.UpgradeClusterRequest, installer *installerv1alpha1.Installer) error {
	if installer.Name == "" {
		installer.Name = m.prefix + "-" + request.ClusterName
	}
	return nil
}

type metaLabels struct {
}

func NewMetaLabels() UpgradeRenderInterface {
	return metaLabels{}
}

func (m metaLabels) Installer(request clustermodel.UpgradeClusterRequest, installer *installerv1alpha1.Installer) error {
	if installer.Labels == nil {
		installer.Labels = make(map[string]string)
	}
	installer.Labels[constants.InstallTypeLabelKey] = constants.UpgradeClusterInstallerTypeLabelValue
	installer.Labels[constants.UpgradeClusterNameLabelKey] = request.ClusterName
	return nil
}

type metaAnnotations struct {
}

func NewMetaAnnotations() UpgradeRenderInterface {
	return metaAnnotations{}
}

func (m metaAnnotations) Installer(request clustermodel.UpgradeClusterRequest, installer *installerv1alpha1.Installer) error {
	if installer.Annotations == nil {
		installer.Annotations = make(map[string]string)
	}
	installer.Annotations[constants.InstallerAnnotationUpgradeClusterNodeUpgradeType] = string(request.UpgradeType)
	return nil
}

type metaTask struct {
	installerElementList []task2.InstallerElement
}

func NewMetaTask(uuid uuid.UUIDIntf) UpgradeRenderInterface {
	var taskList []task2.InstallerElement
	taskList = append(taskList, task2.NewNodeInitialTask())
	taskList = append(taskList, task2.NewSisyphusSolutionApplyTask(uuid)...)
	taskList = append(taskList, task2.NewSisyphusSolutionExecTask()...)
	return metaTask{
		installerElementList: taskList,
	}
}

func (m metaTask) Installer(request clustermodel.UpgradeClusterRequest, installer *installerv1alpha1.Installer) error {
	if installer.Spec.Tasks == nil {
		installer.Spec.Tasks = make(installerv1alpha1.InstallerTaskSpecList, 0, 16)
	}
	for _, installerElement := range m.installerElementList {
		if err := m.buildSpecTask(installerElement, installer, request); err != nil {
			return err
		}
	}
	return nil
}

func (m metaTask) buildSpecTask(element task2.InstallerElement, installer *installerv1alpha1.Installer, request clustermodel.UpgradeClusterRequest) error {
	// 查找到spec
	var targetIndex = -1
	for index, task := range installer.Spec.Tasks {
		task := task
		if task.Kind != nil && *task.Kind == element.Kind() && task.TaskName == element.Name() {
			targetIndex = index
			break
		}
	}
	var targetTask installerv1alpha1.InstallerTaskSpec
	if targetIndex == -1 {
		installer.Spec.Tasks = append(installer.Spec.Tasks, targetTask)
		targetIndex = len(installer.Spec.Tasks) - 1
		targetTask.TaskName = element.Name()
		targetTask.Description = element.Description()
		kind := element.Kind()
		targetTask.Kind = &kind
	} else {
		targetTask = installer.Spec.Tasks[targetIndex]
	}

	var targetStatus *installerv1alpha1.InstallerTaskStatus
	for _, status := range installer.Status.Tasks {
		status := status
		if status.Kind != nil && *(status.Kind) == *(targetTask.Kind) && status.TaskName == targetTask.TaskName {
			targetStatus = &status
			break
		}
	}

	if err := element.CreateRequestRenderToInstallerTask(installer.Spec.Tasks, &targetTask, targetStatus, request); err != nil {
		return err
	}
	installer.Spec.Tasks[targetIndex] = targetTask
	return nil
}
