package upgraderender

import (
	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	internal2 "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/upgraderender/internal"
	util2 "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/uuid"
)

type UpgradeInterface interface {
	UpgradeRequestToInstaller(request cluster.UpgradeClusterRequest, installer *installerv1alpha1.Installer) error
	InstallerToUpgradeResponse(response cluster.UpgradeClusterStepResponse, installer installerv1alpha1.Installer) error
}

func NewUpgradeInterface() UpgradeInterface {
	uuid := util2.NewUUIDIntf()
	return &upgradeRender{
		upgradeInstallerList: []internal2.UpgradeRenderInterface{
			internal2.NewMetaName(constants.UpgradeClusterInstallerPrefix),
			internal2.NewMetaLabels(),
			internal2.NewMetaAnnotations(),
			internal2.NewMetaTask(uuid),
		},
	}
}

type upgradeRender struct {
	upgradeInstallerList []internal2.UpgradeRenderInterface
}

func (ur upgradeRender) UpgradeRequestToInstaller(request cluster.UpgradeClusterRequest, installer *installerv1alpha1.Installer) error {
	for _, r := range ur.upgradeInstallerList {
		if err := r.Installer(request, installer); err != nil {
			return err
		}
	}
	return nil
}

func (ur upgradeRender) InstallerToUpgradeResponse(response cluster.UpgradeClusterStepResponse, installer installerv1alpha1.Installer) error {
	//TODO implement me
	panic("implement me")
}
