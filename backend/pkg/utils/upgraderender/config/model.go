package config

import "k8s.io/apimachinery/pkg/util/sets"

type Config struct {
	InitialGroupInfo InitialGroup     `json:"initial" yaml:"initial"`
	GroupInfos       GroupInfo        `json:"groupInfo" yaml:"groupInfo"`
	SolutionInfos    SolutionInfoList `json:"solutionInfos" yaml:"solutionInfos"`
	NodeCodes        []string         `json:"nodeCodes" yaml:"nodeCodes"`
	StepGroups       StepGroups       `json:"stepGroups" yaml:"stepGroups"`
}

type InitialGroup struct {
	GroupCode       string `json:"groupCode" yaml:"groupCode"`
	GroupAlias      string `json:"groupAlias" yaml:"groupAlias"`
	StepFirstCode   string `json:"stepFirstCode" yaml:"stepFirstCode"`
	StepFirstAlias  string `json:"stepFirstAlias" yaml:"stepFirstAlias"`
	StepSecondCode  string `json:"stepSecondCode" yaml:"stepSecondCode"`
	StepSecondAlias string `json:"stepSecondAlias" yaml:"stepSecondAlias"`
	StepThirdCode   string `json:"stepThirdCode" yaml:"stepThirdCode"`
	StepThirdAlias  string `json:"stepThirdAlias" yaml:"stepThirdAlias"`
	StepFourthCode  string `json:"stepFourthCode" yaml:"stepFourthCode"`
	StepFourthAlias string `json:"stepFourthAlias" yaml:"stepFourthAlias"`
}

type GroupInfo struct {
	ComponentFront []string `json:"componentFront" yaml:"component-front"`
	Kubernetes122  []string `json:"kubernetes122" yaml:"kubernetes-1.22"`
	Kubernetes123  []string `json:"kubernetes123" yaml:"kubernetes-1.23"`
	ComponentRear  []string `json:"componentRear" yaml:"component-rear"`
}

type SolutionInfoList []SolutionInfo

type SolutionInfo struct {
	Group   string `json:"group" yaml:"group"`
	Name    string `json:"name" yaml:"name"`
	Version string `json:"version" yaml:"version"`
	ID      string `json:"id" yaml:"id"`
}

func (list SolutionInfoList) GetSolutionInfoByGroup(groupName string) (*SolutionInfo, bool) {
	var result *SolutionInfo
	for _, item := range list {
		if item.Group == groupName {
			result = &item
			break
		}
	}
	return result, result != nil
}

type StepGroups []StepGroup

type StepGroup struct {
	Code  string `json:"code,omitempty" yaml:"code,omitempty"`
	Alias string `json:"alias,omitempty" yaml:"alias,omitempty"`
	Steps Steps  `json:"steps,omitempty" yaml:"steps,omitempty"`
}

type Steps []Step

type Step struct {
	Code   string   `json:"code,omitempty" yaml:"code,omitempty"`
	Alias  string   `json:"alias,omitempty" yaml:"alias,omitempty"`
	Group  string   `json:"group,omitempty" yaml:"group,omitempty"`
	Labels []string `json:"labels,omitempty" yaml:"labels,omitempty"`
}

func (groups StepGroups) ListStepCodeByGroupCodes(groupCodes ...string) []string {
	steps := groups.ListStepByGroupCodes(groupCodes...)
	var codes = make([]string, 0, len(steps))
	for _, step := range steps {
		step := step
		codes = append(codes, step.Code)
	}
	return codes
}

func (groups StepGroups) ListStepByGroupCodes(groupCodes ...string) Steps {
	var steps Steps
	selectGroupSet := sets.New[string](groupCodes...)
	for _, g := range groups {
		g := g
		if selectGroupSet.Has(g.Code) {
			steps = append(steps, g.Steps...)
		}
	}
	return steps
}
