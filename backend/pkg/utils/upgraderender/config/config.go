package config

import (
	"fmt"
	"os"
	"sync"

	"gopkg.in/yaml.v3"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
	"k8s.io/apimachinery/pkg/util/sets"
)

var UpgradeClusterConfig *Config
var stepGroupConfigLock sync.Mutex

func ReadConfig() error {
	stepGroupConfigLock.Lock()
	defer stepGroupConfigLock.Unlock()
	if UpgradeClusterConfig != nil {
		return nil
	}
	bytes, err := os.ReadFile(config.ClusterUpgradeStepConfigPath.Value)
	if err != nil {
		return fmt.Errorf("read file %s,appear error:%v", config.ClusterUpgradeStepConfigPath.Value, err)
	}
	UpgradeClusterConfig = new(Config)
	err = yaml.Unmarshal(bytes, &UpgradeClusterConfig)
	if err != nil {
		return fmt.Errorf("unmarshal file %s,appear error:%v", config.ClusterUpgradeStepConfigPath.Value, err)
	}
	groupSets := sets.New[string]()
	for _, stepGroupConfig := range UpgradeClusterConfig.StepGroups {
		groupName := stepGroupConfig.Code
		if groupSets.Has(groupName) {
			return fmt.Errorf("renderer ShouldReadConfig,group name %s repeate at file %s", groupName, config.ClusterUpgradeStepConfigPath.Value)
		} else {
			groupSets.Insert(groupName)
		}
	}
	stepOptionSets := sets.New[string]()
	for _, stepGroupConfig := range UpgradeClusterConfig.StepGroups {
		for _, stepConfig := range stepGroupConfig.Steps {
			for _, option := range stepConfig.Labels {
				stepOption := stepConfig.Code + "$" + string(option)
				if stepOptionSets.Has(stepOption) {
					return fmt.Errorf("renderer ShouldReadConfig,step name %s option %s repeate at file %s", stepConfig.Code, option, config.ClusterUpgradeStepConfigPath.Value)
				} else {
					stepOptionSets.Insert(stepOption)
				}
			}

		}

	}
	return nil
}
