package utils

import (
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/resources"
)

//// product match/sort/page to other function
//func Filter(c *gin.Context, result []byte) (*models.PageableResponse, error) {
//	resources := ParseQueryParams(c)
//	return resources.FilterResult(result)
//}

//// product match/sort/page to other function
//func FilterToMap(c *gin.Context, result []byte) resources.K8sJson {
//	resources := ParseQueryParams(c)
//	return resources.FilterResultToMap(result)
//}

// parse request params, include selector, sort and page
func ParseQueryParams[T any](c *gin.Context) resources.Filter[T] {
	exact, fuzzy, gt, ge, notEqual := parseSelector(c.Query("selector"))
	limit, offset := parsePage(c.<PERSON>("page_size"), c.Query("page_num"))
	sortName, sortOrder, sortFunc := parseSort(c.Query("sort_name"), c.<PERSON><PERSON>("sort_order"), c.Query("sort_func"))

	filter := resources.Filter[T]{
		Exact:       exact,
		Fuzzy:       fuzzy,
		NotEqual:    notEqual,
		GreaterThan: gt,
		Ge:          ge,
		Limit:       limit,
		Offset:      offset,
		SortName:    sortName,
		SortOrder:   sortOrder,
		SortFunc:    sortFunc,
	}
	return filter
}

// filter selector
// exact query: selector=key1=value1,key2=value2,key3=value3
// fuzzy query: selector=key1~value1,key2~value2,key3~value3
// greater-than query: selector=key1>value1,key2>value2,key3>value3
// greater-or-equal query: selector=key1>=value1,key2>=value2,key3>=value3
// less-than query: selector=key1<value1,key2<value2,key3<value3
// less-or-equal query selector=key1<=value1,key2<=value2,key3<=value3
// not-equal query: selector=key1!=value1,key2!=value2,key3!=value3
// support mixed query: selector=key1~value1,key2=value2,key3!=value3
func parseSelector(selectorStr string) (exact, fuzzy, gt, ge map[string]string, notEqual []string) {
	if selectorStr == "" {
		return nil, nil, nil, nil, nil
	}

	exact = make(map[string]string, 0)
	fuzzy = make(map[string]string, 0)
	gt = make(map[string]string, 0)
	ge = make(map[string]string, 0)
	lt := make(map[string]string, 0)
	le := make(map[string]string, 0)
	notEqual = make([]string, 0)

	labels := strings.Split(selectorStr, ",")
	for _, label := range labels {
		if strings.Contains(label, "!=") {
			notEqual = append(notEqual, label)

			continue
		}
		if strings.Contains(label, ">=") {
			tmpKV := strings.Split(label, ">=")
			if len(tmpKV) != 2 {
				continue
			}
			ge[tmpKV[0]] = tmpKV[1]
			continue
		}
		if strings.Contains(label, "<=") {
			tmpKV := strings.Split(label, "<=")
			if len(tmpKV) != 2 {
				continue
			}
			le[tmpKV[0]] = tmpKV[1]
			continue
		}
		if i := strings.IndexAny(label, "~=<>"); i > 0 {
			switch label[i] {
			case '=':
				exact[label[:i]] = label[i+1:]
			case '~':
				fuzzy[label[:i]] = label[i+1:]
			case '>':
				gt[label[:i]] = label[i+1:]
			case '<':
				lt[label[:i]] = label[i+1:]

			}
		}
	}

	return
}

// page=10,1, means limit=10&page=1, default 10,1
// if pageSize and pageNum is empty list all records
// offset=(page-1)*limit
func parsePage(pageSize string, pageNum string) (limit, offset *int) {
	if pageSize == "" && pageNum == "" {
		return nil, nil
	}
	pageSizeInt := 10
	pageNumInt := 1
	if v, err := strconv.Atoi(pageSize); err == nil {
		pageSizeInt = v
	}
	if v, err := strconv.Atoi(pageNum); err == nil {
		pageNumInt = v
	}
	limit = &pageSizeInt
	if pageNumInt < 1 {
		tmpOffset := 0
		offset = &tmpOffset
	} else {
		tmpOffset := (pageNumInt - 1) * pageSizeInt
		offset = &tmpOffset
	}
	return
}

// sortName=creationTimestamp, sortOrder=asc
func parseSort(name string, order string, sFunc string) (sortName, sortOrder, sortFunc string) {
	sortName = "metadata.name"
	sortOrder = "asc"
	sortFunc = "string"

	if name == "" {
		return
	}
	sortName = name

	if strings.EqualFold(order, "desc") {
		sortOrder = "desc"
	}

	if sFunc != "" {
		sortFunc = sFunc
	}

	return
}
