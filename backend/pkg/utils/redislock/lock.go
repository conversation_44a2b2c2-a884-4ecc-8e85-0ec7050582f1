package redislock

import (
	"context"
	"time"

	rds "github.com/redis/go-redis/v9"
)

const (
	// 默认续期时间间隔为过期时间的1/3
	defaultRenewalInterval = 3
)

type LockResult struct {
	UnlockFunc func()
	Acquired   bool
}

// Lock 分布式锁接口
type Lock interface {
	// TryLock 尝试获取锁
	TryLock(ctx context.Context, expiration time.Duration) (bool, error)

	// Release 释放锁
	Release(ctx context.Context) error

	// AutoLock 自动加锁和解锁，返回解锁函数
	AutoLock(ctx context.Context, expiration time.Duration) LockResult

	// IsLocked 返回锁是否被当前进程持有
	IsLocked() bool

	// GetLockValue 返回锁的唯一标识
	GetLockValue() string
}

// LockType 锁类型
type LockType string

const (
	// SimpleLock 简单锁，不支持选主和续期
	SimpleLock LockType = "simple"
	// LeaderLock 支持选主和续期的锁
	LeaderLock LockType = "leader"
)

// NewLock 创建一个新的分布式锁
func NewLock(client *rds.Client, lockKey string, lockType LockType) Lock {
	switch lockType {
	case LeaderLock:
		return NewLeaderLock(client, lockKey)
	default:
		return NewSimpleLock(client, lockKey)
	}
}
