package redislock

import (
	"context"
	"sync"
	"testing"
	"time"

	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/assert"
)

var (
	testRedisClient *redis.Client
	ctx             context.Context
)

func init() {
	testRedisClient = redis.NewClient(&redis.Options{
		Addr:     "10.120.1.58:30379",
		Password: "Hc@Cloud01",
		DB:       0,
	})
	ctx = context.Background()
}

// TestSimpleLock_Basic 测试简单锁的基本功能
func TestSimpleLock_Basic(t *testing.T) {
	lockKey := "test_simple_lock"
	lock := NewLock(testRedisClient, lockKey, SimpleLock)

	// 清理可能存在的锁
	_ = testRedisClient.Del(ctx, lockKey).Err()

	// 测试获取锁
	acquired, err := lock.TryLock(ctx, time.Second*5)
	assert.NoError(t, err)
	assert.True(t, acquired)
	assert.True(t, lock.IsLocked())

	// 测试重复获取锁
	lock2 := NewLock(testRedisClient, lockKey, SimpleLock)
	acquired, err = lock2.TryLock(ctx, time.Second*5)
	assert.NoError(t, err)
	assert.False(t, acquired)

	// 测试释放锁
	err = lock.Release(ctx)
	assert.NoError(t, err)
	assert.False(t, lock.IsLocked())

	// 测试锁释放后可以重新获取
	acquired, err = lock2.TryLock(ctx, time.Second*5)
	assert.NoError(t, err)
	assert.True(t, acquired)
}

// TestSimpleLock_Concurrent 测试简单锁的并发性能
func TestSimpleLock_Concurrent(t *testing.T) {
	lockKey := "test_simple_lock_concurrent"
	counter := 0
	goroutines := 5  // 减少并发数
	iterations := 20 // 减少迭代次数

	var wg sync.WaitGroup
	var mu sync.Mutex // 添加互斥锁保护计数器
	wg.Add(goroutines)

	// 清理可能存在的锁
	_ = testRedisClient.Del(ctx, lockKey).Err()

	for i := 0; i < goroutines; i++ {
		go func() {
			defer wg.Done()
			for j := 0; j < iterations; j++ {
				lock := NewLock(testRedisClient, lockKey, SimpleLock)
				if acquired, err := lock.TryLock(ctx, time.Second*2); err == nil && acquired {
					mu.Lock() // 保护计数器
					counter++
					mu.Unlock()
					time.Sleep(time.Millisecond * 10) // 模拟业务处理
					_ = lock.Release(ctx)
					time.Sleep(time.Millisecond * 10) // 给其他goroutine机会
				}
			}
		}()
	}

	wg.Wait()
	expectedCount := goroutines * iterations
	t.Logf("Counter: %d, Expected: %d", counter, expectedCount)
	// 允许有一定的误差范围
	assert.True(t, counter > 0 && counter <= expectedCount,
		"Counter should be greater than 0 and less than or equal to %d, got %d", expectedCount, counter)
}

// TestLeaderLock_Basic 测试选主锁的基本功能
func TestLeaderLock_Basic(t *testing.T) {
	lockKey := "test_leader_lock"
	lock := NewLock(testRedisClient, lockKey, LeaderLock)

	// 清理可能存在的锁
	_ = testRedisClient.Del(ctx, lockKey).Err()

	// 测试获取锁
	acquired, err := lock.TryLock(ctx, time.Second*5)
	assert.NoError(t, err)
	assert.True(t, acquired)
	assert.True(t, lock.IsLocked())

	// 获取锁的值
	value := lock.GetLockValue()
	assert.NotEmpty(t, value)

	// 测试重复获取锁
	lock2 := NewLock(testRedisClient, lockKey, LeaderLock)
	acquired, err = lock2.TryLock(ctx, time.Second*5)
	assert.NoError(t, err)
	assert.False(t, acquired)

	// 测试释放锁
	err = lock.Release(ctx)
	assert.NoError(t, err)
	assert.False(t, lock.IsLocked())

	// 测试锁释放后可以重新获取
	acquired, err = lock2.TryLock(ctx, time.Second*5)
	assert.NoError(t, err)
	assert.True(t, acquired)
}

// TestLeaderLock_AutoRenewal 测试选主锁的自动续期功能
func TestLeaderLock_AutoRenewal(t *testing.T) {
	lockKey := "test_leader_lock_renewal"
	lock := NewLock(testRedisClient, lockKey, LeaderLock)

	// 清理可能存在的锁
	_ = testRedisClient.Del(ctx, lockKey).Err()

	// 获取锁，设置较短的过期时间
	acquired, err := lock.TryLock(ctx, time.Second*3)
	assert.NoError(t, err)
	assert.True(t, acquired)

	// 等待超过原始过期时间
	time.Sleep(time.Second * 4)

	// 检查锁是否仍然存在（由于自动续期）
	val, err := testRedisClient.Get(ctx, lockKey).Result()
	assert.NoError(t, err)
	assert.Equal(t, lock.GetLockValue(), val)

	// 释放锁
	err = lock.Release(ctx)
	assert.NoError(t, err)
}

// TestLeaderLock_Concurrent 测试选主锁的并发性能
func TestLeaderLock_Concurrent(t *testing.T) {
	lockKey := "test_leader_lock_concurrent"
	leaderChanges := 0
	goroutines := 5
	iterations := 50

	var wg sync.WaitGroup
	wg.Add(goroutines)

	var mu sync.Mutex
	currentLeader := ""

	for i := 0; i < goroutines; i++ {
		go func() {
			defer wg.Done()
			for j := 0; j < iterations; j++ {
				lock := NewLock(testRedisClient, lockKey, LeaderLock)
				if acquired, err := lock.TryLock(ctx, time.Second*2); err == nil && acquired {
					mu.Lock()
					if currentLeader != lock.GetLockValue() {
						leaderChanges++
						currentLeader = lock.GetLockValue()
					}
					mu.Unlock()
					time.Sleep(time.Millisecond * 100) // 模拟工作负载
					_ = lock.Release(ctx)
				}
				time.Sleep(time.Millisecond * 50) // 避免过于频繁的竞争
			}
		}()
	}

	wg.Wait()
	t.Logf("Leader changes: %d", leaderChanges)
	assert.True(t, leaderChanges > 0, "Should have some leader changes")
}

// TestAutoLock 测试自动加锁和解锁功能
func TestAutoLock(t *testing.T) {
	tests := []struct {
		name     string
		lockType LockType
	}{
		{"SimpleLock", SimpleLock},
		{"LeaderLock", LeaderLock},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			lockKey := "test_auto_lock_" + string(tt.lockType)
			lock := NewLock(testRedisClient, lockKey, tt.lockType)

			// 清理可能存在的锁
			_ = testRedisClient.Del(ctx, lockKey).Err()

			// 使用AutoLock
			func() {
				unlock := lock.AutoLock(ctx, time.Second*5)
				defer func() {
					unlock.UnlockFunc()
				}()

				// 验证锁已获取
				assert.True(t, lock.IsLocked())

				// 尝试重复获取锁
				lock2 := NewLock(testRedisClient, lockKey, tt.lockType)
				acquired, err := lock2.TryLock(ctx, time.Second*5)
				assert.NoError(t, err)
				assert.False(t, acquired)
			}()

			// 验证锁已释放
			assert.False(t, lock.IsLocked())

			// 验证可以重新获取锁
			lock2 := NewLock(testRedisClient, lockKey, tt.lockType)
			acquired, err := lock2.TryLock(ctx, time.Second*5)
			assert.NoError(t, err)
			assert.True(t, acquired)
		})
	}
}
