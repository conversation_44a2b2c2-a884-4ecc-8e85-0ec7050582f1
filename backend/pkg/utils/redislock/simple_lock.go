package redislock

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/opentracing/opentracing-go/log"
	rds "github.com/redis/go-redis/v9"
)

// SimpleLockImpl 简单分布式锁实现
type SimpleLockImpl struct {
	client   *rds.Client
	lockKey  string
	value    string
	isLocked bool
	mu       sync.Mutex
}

// NewSimpleLock 创建分布式锁实例
func NewSimpleLock(client *rds.Client, lockKey string) Lock {
	return &SimpleLockImpl{
		client:  client,
		lockKey: lockKey,
		value:   uuid.New().String(),
	}
}

// TryLock 尝试获取锁（修复：仅在 Redis 操作成功时更新 isLocked）
func (l *SimpleLockImpl) TryLock(ctx context.Context, expiration time.Duration) (bool, error) {
	l.mu.Lock()
	defer l.mu.Unlock()

	success, err := l.client.SetNX(ctx, l.lockKey, l.value, expiration).Result()
	if err != nil {
		return false, fmt.Errorf("redis SetNX failed: %w", err)
	}

	l.isLocked = success // 仅在成功时更新状态
	return success, nil
}

// Release 释放锁（修复：使用 Lua 脚本保证原子性）
func (l *SimpleLockImpl) Release(ctx context.Context) error {
	l.mu.Lock()
	defer l.mu.Unlock()

	if !l.isLocked {
		return nil // 未持有锁时直接返回
	}

	// Lua 脚本：只有锁的值匹配时才删除
	script := `
	if redis.call("GET", KEYS[1]) == ARGV[1] then
		return redis.call("DEL", KEYS[1])
	else
		return 0
	end
	`
	result, err := l.client.Eval(ctx, script, []string{l.lockKey}, l.value).Result()
	if err != nil {
		return fmt.Errorf("redis Eval failed: %w", err)
	}

	if result.(int64) == 1 {
		l.isLocked = false // 仅当 Redis 删除成功时更新状态
		return nil
	}

	return fmt.Errorf("lock value mismatch or already released")
}

// AutoLock 自动加锁和解锁（修复：保持原有逻辑，但依赖修复后的 TryLock/Release）
func (l *SimpleLockImpl) AutoLock(ctx context.Context, expiration time.Duration) LockResult {
	acquired, err := l.TryLock(ctx, expiration)
	if err != nil {
		log.Error(fmt.Errorf("lock error: %w", err))
		return LockResult{Acquired: false, UnlockFunc: func() {}}
	}
	return LockResult{
		UnlockFunc: func() {
			if acquired {
				if err := l.Release(ctx); err != nil {
					log.Error(fmt.Errorf("release failed: %w", err))
				}
			}
		},
		Acquired: acquired,
	}
}

// IsLocked 返回锁状态
func (l *SimpleLockImpl) IsLocked() bool {
	l.mu.Lock()
	defer l.mu.Unlock()

	// 如果本地状态为未锁定，直接返回
	if !l.isLocked {
		return false
	}

	// 可选：查询 Redis 验证锁是否存在（根据需求决定是否启用）
	val, err := l.client.Get(context.Background(), l.lockKey).Result()
	if err != nil || val != l.value {
		l.isLocked = false
		return false
	}

	return true
}

// GetLockValue 返回锁的唯一标识（无需修改）
func (l *SimpleLockImpl) GetLockValue() string {
	return l.value
}
