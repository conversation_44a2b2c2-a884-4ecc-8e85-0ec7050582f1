package redislock

import (
	"context"
	"fmt"
	"os"
	"sync"
	"time"

	"github.com/google/uuid"
	"github.com/opentracing/opentracing-go/log"
	rds "github.com/redis/go-redis/v9"
)

// LeaderLockImpl 支持选主和续期的分布式锁实现
type LeaderLockImpl struct {
	client          *rds.Client
	lockKey         string
	value           string // 锁的唯一标识，用于安全释放
	expiration      time.Duration
	renewalInterval time.Duration
	stopRenewal     chan struct{} // 停止续期的信号
	renewalStopped  chan struct{} // 续期已停止的信号
	isLocked        bool
	mu              sync.Mutex
}

// NewLeaderLock 创建一个新的支持选主和续期的分布式锁
func NewLeaderLock(client *rds.Client, lockKey string) Lock {
	// 使用uuid和主机名作为锁的唯一标识
	hostname, _ := os.Hostname()
	value := fmt.Sprintf("%s-%s", hostname, uuid.New().String())

	return &LeaderLockImpl{
		client:         client,
		lockKey:        lockKey,
		value:          value,
		stopRenewal:    make(chan struct{}),
		renewalStopped: make(chan struct{}),
	}
}

// TryLock 尝试获取锁
func (l *LeaderLockImpl) TryLock(ctx context.Context, expiration time.Duration) (bool, error) {
	l.mu.Lock()
	defer l.mu.Unlock()

	// 设置过期时间
	l.expiration = expiration
	l.renewalInterval = expiration / defaultRenewalInterval

	// 使用 SET NX 命令获取锁
	success, err := l.client.SetNX(ctx, l.lockKey, l.value, expiration).Result()
	if err != nil {
		return false, fmt.Errorf("failed to acquire lock: %w", err)
	}

	if success {
		l.isLocked = true
		// 启动自动续期
		go l.startRenewal(ctx)
	}

	return success, nil
}

// Release 释放锁
func (l *LeaderLockImpl) Release(ctx context.Context) error {
	l.mu.Lock()
	defer l.mu.Unlock()

	if !l.isLocked {
		return nil
	}

	// 使用Lua脚本确保只释放自己的锁
	script := `
		if redis.call("get", KEYS[1]) == ARGV[1] then
			return redis.call("del", KEYS[1])
		else
			return 0
		end
	`

	// 停止续期
	if l.stopRenewal != nil {
		close(l.stopRenewal)
		<-l.renewalStopped
	}

	// 执行Lua脚本
	result, err := l.client.Eval(ctx, script, []string{l.lockKey}, l.value).Result()
	if err != nil {
		return fmt.Errorf("failed to release lock: %w", err)
	}

	if result.(int64) == 0 {
		return fmt.Errorf("lock was held by another process")
	}

	l.isLocked = false
	return nil
}

// AutoLock 自动加锁和解锁
func (l *LeaderLockImpl) AutoLock(ctx context.Context, expiration time.Duration) LockResult {
	acquired, err := l.TryLock(ctx, expiration)
	if err != nil {
		log.Error(fmt.Errorf("lock error %w", err))
		return LockResult{Acquired: false, UnlockFunc: func() {}}
	}
	return LockResult{
		UnlockFunc: func() {
			if acquired { // 只有真正获取成功才释放
				if err := l.Release(ctx); err != nil {
					log.Error(fmt.Errorf("release failed %w", err))
				}
			}
		},
		Acquired: acquired,
	}
}

// startRenewal 开始自动续期
func (l *LeaderLockImpl) startRenewal(ctx context.Context) {
	defer close(l.renewalStopped)

	ticker := time.NewTicker(l.renewalInterval)
	defer ticker.Stop()

	for {
		select {
		case <-ticker.C:
			l.mu.Lock()
			if !l.isLocked {
				l.mu.Unlock()
				return
			}

			// 使用Lua脚本进行续期，确保只续期自己的锁
			script := `
				if redis.call("get", KEYS[1]) == ARGV[1] then
					return redis.call("pexpire", KEYS[1], ARGV[2])
				else
					return 0
				end
			`
			result, err := l.client.Eval(ctx, script, []string{l.lockKey}, l.value, l.expiration.Milliseconds()).Result()
			l.mu.Unlock()

			if err != nil {
				// 续期失败，记录错误但继续尝试
				fmt.Printf("Failed to renew lock: %v\n", err)
				continue
			}

			if result.(int64) == 0 {
				// 锁已经不属于我们了，停止续期
				return
			}

		case <-l.stopRenewal:
			return
		case <-ctx.Done():
			return
		}
	}
}

// IsLocked 返回锁是否被当前进程持有
func (l *LeaderLockImpl) IsLocked() bool {
	l.mu.Lock()
	defer l.mu.Unlock()
	return l.isLocked
}

// GetLockValue 返回锁的唯一标识
func (l *LeaderLockImpl) GetLockValue() string {
	return l.value
}
