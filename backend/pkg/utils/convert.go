package utils

import (
	"encoding/base64"
	"encoding/json"
	"fmt"
	"reflect"
	"strconv"
	"strings"
	"time"

	"github.com/shopspring/decimal"
	veleroV1 "github.com/vmware-tanzu/velero/pkg/apis/velero/v1"
	stellarisv1alpha1 "harmonycloud.cn/stellaris/pkg/apis/stellaris/v1alpha1"
	cloudservice_v1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/v1alpha1"
	appsv1 "k8s.io/api/apps/v1"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/apimachinery/pkg/util/sets"
	klog "k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/velero"
)

func ToStruct(data interface{}, into interface{}) error {
	bytes, err := json.Marshal(data)
	if err != nil {
		return err
	}
	return json.Unmarshal(bytes, into)
}

func ConvertCloudService(service cloudservice_v1alpha1.CloudService) *models.CloudService {

	var componentConditions []models.CloudComponentCondition
	for n, c := range service.Status.ComponentConditions {
		componentConditions = append(componentConditions, models.CloudComponentCondition{
			Name:   n,
			Status: models.CloudServiceComponentStatus(c.Phase),
		})
	}

	cloudservice := &models.CloudService{
		CloudServiceName: service.Spec.Name,
		Icon: models.IconInfo{
			Name: service.Spec.Icon.Name,
		},
		CreateTime:               service.CreationTimestamp.Time,
		Description:              service.Spec.Description,
		DisplayName:              service.Spec.DisplayName,
		Version:                  service.Spec.Version,
		Status:                   models.CloudServiceStatus(service.Status.Phase),
		CpuLimit:                 0,
		MemoryLimit:              0,
		CpuRequest:               0,
		MemoryRequest:            0,
		CpuUsage:                 0,
		MemoryUsage:              0,
		PodNum:                   MustInt64(service.Status.Pods),
		ReadyPodNumber:           MustInt64(service.Status.ReadyPods),
		ComponentNumber:          0,
		ExceptionComponentNumber: 0,
		ComponentConditions:      componentConditions,
	}
	if service.Status.Resources.Requests != nil {
		cpuQuota := service.Status.Resources.Requests.Cpu()
		memoryQuota := service.Status.Resources.Requests.Memory()
		cloudservice.CpuRequest = CpuQuotaToCore(cpuQuota)
		cloudservice.MemoryRequest = MemoryQuotaToGi(memoryQuota)
	}

	if service.Status.Resources.Limits != nil {
		cpuQuota := service.Status.Resources.Limits.Cpu()
		memoryQuota := service.Status.Resources.Limits.Memory()
		cloudservice.CpuLimit = CpuQuotaToCore(cpuQuota)
		cloudservice.MemoryLimit = MemoryQuotaToGi(memoryQuota)
	}

	if service.Status.Resources.Usages != nil {
		cpuQuota := service.Status.Resources.Usages.Cpu()
		memoryQuota := service.Status.Resources.Usages.Memory()
		cloudservice.CpuUsage = CpuQuotaToCore(cpuQuota)
		cloudservice.MemoryUsage = MemoryQuotaToGi(memoryQuota)
	}

	var exceptionComponentNumber int64 = 0

	if service.Status.ComponentConditions != nil {
		cloudservice.ComponentNumber = int64(len(service.Status.ComponentConditions))
		for _, componentStatus := range service.Status.ComponentConditions {
			if string(componentStatus.Phase) != string(models.CloudServiceRunning) {
				exceptionComponentNumber++
			}
		}
		cloudservice.ExceptionComponentNumber = exceptionComponentNumber
	}

	return cloudservice
}

func MustString(str *string) string {
	if str == nil {
		return ""
	}
	return *str
}

func MustGetLabel(key string, labels map[string]string) string {
	if labels == nil {
		return ""
	}
	val, ok := labels[key]
	if ok {
		return val
	}
	return ""
}

func MustInt64(a *int64) int64 {
	if a == nil {
		return 0
	}
	return *a
}

func Convert2CloudServiceComponent(component cloudservice_v1alpha1.CloudComponent) models.CloudServiceComponent {
	csc := models.CloudServiceComponent{
		CloudServiceName:   component.Labels["unified-platform.harmonycloud.cn/cloudservice-name"],
		CloudComponentName: component.Spec.Name,
		ClusterPolicy:      models.CloudServiceComponentPolicy(string(component.Spec.ClusterPolicy)),
		Cluster:            MustGetLabel(CloudComponentBelongingClusterLabelKey, component.Labels),
		CreateTime:         component.CreationTimestamp.Time,
		Description:        component.Spec.Description,
		DisplayName:        component.Spec.DisplayName,
		Version:            component.Spec.Version,
		Status:             models.CloudServiceComponentStatus(string(component.Status.Phase)),
		PodNum:             MustInt64(component.Status.Pods),
		ReadyPodNumber:     MustInt64(component.Status.ReadyPods),
	}
	if component.Status.Resources.Requests != nil {
		cpuQuota := component.Status.Resources.Requests.Cpu()
		memoryQuota := component.Status.Resources.Requests.Memory()
		csc.CpuRequest = CpuQuotaToCore(cpuQuota)
		csc.MemoryRequest = MemoryQuotaToGi(memoryQuota)
	}

	if component.Status.Resources.Limits != nil {
		cpuQuota := component.Status.Resources.Limits.Cpu()
		memoryQuota := component.Status.Resources.Limits.Memory()
		csc.CpuLimit = CpuQuotaToCore(cpuQuota)
		csc.MemoryLimit = MemoryQuotaToGi(memoryQuota)
	}

	if component.Status.Resources.Usages != nil {
		cpuQuota := component.Status.Resources.Usages.Cpu()
		memoryQuota := component.Status.Resources.Usages.Memory()
		csc.CpuUsage = CpuQuotaToCore(cpuQuota)
		csc.MemoryUsage = MemoryQuotaToGi(memoryQuota)
	}
	var workloadCondition *cloudservice_v1alpha1.CloudComponentCondition
	for _, c := range component.Status.Conditions {
		if c.Type == cloudservice_v1alpha1.CloudComponentResourceRunningConditionType && c.Status == corev1.ConditionFalse {
			workloadCondition = &c
			break
		}
	}
	var ccwc models.CloudComponentWorkloadCondition
	if workloadCondition != nil && len(workloadCondition.Workloads) != 0 {
		ccwc.Status = string(workloadCondition.Status)
		ccwc.Reason = workloadCondition.Reason
		ccwc.Message = workloadCondition.Message
		var ws = make([]models.CloudComponentWorkload, 0, len(workloadCondition.Workloads))
		for _, w := range workloadCondition.Workloads {
			ws = append(ws, models.CloudComponentWorkload{
				Namespace:    w.Namespace,
				Name:         w.Name,
				WorkloadType: models.WorkloadType(w.Kind),
			})
		}
		ccwc.Workloads = ws

	}
	csc.UnHealthWorkloadCondition = ccwc

	return csc
}

func Convert2CloudComponentWorkload(workload client.Object) models.CloudComponentWorkload {
	result := models.CloudComponentWorkload{
		Name:       workload.GetName(),
		Namespace:  workload.GetNamespace(),
		CreateTime: workload.GetCreationTimestamp().Time,
	}

	switch workload.(type) {
	case *appsv1.Deployment:
		result.WorkloadType = models.Deployment
		result.State = models.GetDeploymentState(*workload.(*appsv1.Deployment))
	case *appsv1.DaemonSet:
		result.WorkloadType = models.DaemonSet
		result.State = models.GetDaemonSetState(*workload.(*appsv1.DaemonSet))
	case *appsv1.StatefulSet:
		result.WorkloadType = models.Statefulset
		result.State = models.GetStatefulSetState(*workload.(*appsv1.StatefulSet))
	case *batchv1.Job:
		result.WorkloadType = models.Job
		result.State = models.GetJobState(*workload.(*batchv1.Job))
	case *batchv1.CronJob:
		result.WorkloadType = models.CronJob
		result.State = models.GetCronJobState(*workload.(*batchv1.CronJob))
	default:
		logger.GetSugared().Warnf("[Convert2CloudServiceWorkload] worklaod type unknow,type is '%s'", reflect.TypeOf(workload).Kind())
	}
	return result
}

func Convert2CloudComponentWorkloadPodInstance(pod corev1.Pod) models.CloudComponentWorkloadPodInstance {
	var podIps = make([]models.PodIP, 0, len(pod.Status.PodIPs))
	for _, ip := range pod.Status.PodIPs {
		podIps = append(podIps, models.PodIP{
			IP: ip.IP,
		})
	}

	return models.CloudComponentWorkloadPodInstance{
		Name:           pod.Name,
		Namespace:      pod.Namespace,
		Status:         models.PodPhase(pod.Status.Phase),
		PodIps:         podIps,
		ScheduleNode:   pod.Spec.NodeName,
		CreateTime:     pod.ObjectMeta.CreationTimestamp.Time,
		InitContainers: Convert2CloudComponentWorkloadPodInstanceContainers(pod.Spec.InitContainers),
		Containers:     Convert2CloudComponentWorkloadPodInstanceContainers(pod.Spec.Containers),
	}
}

func GetLogPathsFromContainer(container corev1.Container) []models.LogPath {
	var list = make([]models.LogPath, 0)
	pathSets := map[string]sets.Set[string]{}
	for _, env := range container.Env {
		if env.Name == constants.EnvAliyunLogsLogstashName && env.Value != "" {
			idx := strings.LastIndex(env.Value, "/")
			if idx > -1 {
				mountPath := env.Value
				if idx > 0 {
					mountPath = env.Value[:idx]
				}
				if pathSets[mountPath] == nil {
					pathSets[mountPath] = sets.New[string]()
				}
				pathSets[mountPath].Insert(env.Value)
			}

		}
	}
	for _, mount := range container.VolumeMounts {
		if pathSets[mount.MountPath] != nil {
			for _, paths := range pathSets {
				for p := range paths {
					list = append(list, models.LogPath{
						Name: mount.Name + p[strings.LastIndex(p, "/"):],
						Path: p,
					})
				}
			}
		}
	}
	return list
}

func Convert2CloudComponentWorkloadPodInstanceContainers(containers []corev1.Container) []models.CloudComponentWorkloadPodInstanceContainer {
	if len(containers) == 0 {
		return nil
	}

	var cs = make([]models.CloudComponentWorkloadPodInstanceContainer, 0, (len(containers)>>1)<<1)
	for _, container := range containers {
		cs = append(cs, models.CloudComponentWorkloadPodInstanceContainer{
			Name:     container.Name,
			LogPaths: GetLogPathsFromContainer(container),
		})
	}
	return cs
}

func CpuQuotaToCore(quota *resource.Quantity) float64 {
	if quota == nil {
		return 0
	}
	val := quota.AsApproximateFloat64()
	f, _ := decimal.NewFromFloat(val).Round(3).Float64()
	return f
}

func MemoryQuotaToGi(quota *resource.Quantity) float64 {
	if quota == nil {
		return 0
	}
	val := quota.AsApproximateFloat64() / 1024 / 1024 / 1024
	f, _ := decimal.NewFromFloat(val).Round(3).Float64()
	return f
}

func Int64Prt(i int64) *int64 {
	return &i
}

func Ints2Int64s(vals ...int) []int64 {
	res := make([]int64, 0, len(vals))
	if len(vals) == 0 {
		return res
	}
	for _, val := range vals {
		res = append(res, Int2Int64(val))
	}
	return res
}

func Int2Int64(val int) int64 {
	return int64(val)
}

func Base64DecodeString(str string) ([]byte, error) {
	base64Encode := base64.RawURLEncoding
	return base64Encode.DecodeString(str)
}

func Convert2Cluster(cluster stellarisv1alpha1.Cluster) *models.ClusterModel {
	protocol := cluster.Status.ClusterInfo.Protocol
	host := cluster.Status.ClusterInfo.Address
	port := cluster.Status.ClusterInfo.Port
	token := cluster.Status.ClusterInfo.Token
	apiServer := fmt.Sprintf("%s://%s:%d", protocol, host, port)
	cfg := &models.Config{
		Host:        apiServer,
		BearerToken: token,
	}

	ic := &models.ClusterModel{
		Hub:           false,
		ClusterId:     cluster.Name,
		ClusterName:   cluster.Name,
		Config:        cfg,
		NodeCount:     cluster.Status.ClusterInfo.NodeCount,
		K8sVersion:    cluster.Status.ClusterInfo.KubeVersion,
		Architectures: cluster.Status.ClusterInfo.Architectures,
		Status:        string(cluster.Status.Status),
	}
	return ic
}

func ParseArchAlias(arch string) (string, error) {
	//switch arch {
	//case "amd64":
	//	return "x86_64", nil
	//case "arm64":
	//	return "arm_64", nil
	//default:
	//	return "", errors.NewFromCodeWithMessage(errors.Var.UnKnowArch, arch)
	//}
	return arch, nil
}

func ConvertModifyConfig(modifies []velero.Modify) velero.ModifyRule {
	modifyRule := velero.ModifyRule{
		Version: "v1",
	}
	resourceModifierRules := make([]velero.ResourceModifierRule, 0)
	for _, modify := range modifies {
		namespaces := make([]string, 0)
		var resourceNameRegex string
		for _, namespace := range modify.Namespaces {
			namespaces = append(namespaces, namespace.Name)
			resourceNameRegex = strings.Join(namespace.ResourceName, "|")
		}
		resourceModifierRule := velero.ResourceModifierRule{
			Conditions: velero.Condition{
				GroupResource:     modify.ResourceType,
				ResourceNameRegex: fmt.Sprintf("^(%s)$", resourceNameRegex),
				Namespaces:        namespaces,
			},
		}
		patches := make([]velero.Patch, 0)
		for _, content := range modify.Contents {
			patch := velero.Patch{
				Operation: content.Type,
				Path:      content.Path,
				Value:     content.Value,
			}
			patches = append(patches, patch)
		}
		resourceModifierRule.Patches = patches
		resourceModifierRules = append(resourceModifierRules, resourceModifierRule)
	}
	modifyRule.ResourceModifierRules = resourceModifierRules
	return modifyRule
}

func Convert2VeleroVolumes(podVolumeBackup velero.PodVolumeBackup) velero.VeleroVolumes {
	return velero.VeleroVolumes{
		Namespace:                 podVolumeBackup.PodNamespace,
		PersistentVolumeClaimName: MapGetValue(podVolumeBackup.Annotations, constants.VeleroPVCNameAnnotation, ""),
		PodName:                   podVolumeBackup.PodName,
		VolumeName:                podVolumeBackup.VolumeName,
	}
}

func Convert2Backups(backup veleroV1.Backup) velero.Backups {
	var ownerSchedulesName string
	value, exist := backup.Labels[veleroV1.ScheduleNameLabel]
	if exist {
		ownerSchedulesName = value
	}

	resourcesSize, _ := strconv.Atoi(MapGetValue(backup.Annotations, constants.ResourcesSize, "0"))
	persistentVolumeSize, _ := strconv.Atoi(MapGetValue(backup.Annotations, constants.PersistentVolumeSize, "0"))
	namespaceJson := MapGetValue(backup.Annotations, constants.BackupNamespaces, "[]")
	namespaces := make([]string, 0)
	if err := json.Unmarshal([]byte(namespaceJson), &namespaces); err != nil {
		logger.GetSugared().Errorf("json.Unmarshal failed: %v", err)
	}

	finishTime := ""
	if backup.Status.CompletionTimestamp != nil {
		finishTime = backup.Status.CompletionTimestamp.In(time.UTC).Format("2006-01-02T15:04:05Z")
	}
	status := ""
	if backup.Status.Phase == "" ||
		(backup.Status.Phase == veleroV1.BackupPhaseCompleted && resourcesSize <= 0 && len(namespaces) == 0) ||
		(backup.Status.Phase == veleroV1.BackupPhasePartiallyFailed && resourcesSize <= 0 && len(namespaces) == 0) {
		status = "Waiting"
	} else {
		status = string(backup.Status.Phase)
	}

	return velero.Backups{
		BackupsName:          backup.Name,
		OwnerSchedulesName:   ownerSchedulesName,
		CreateTime:           backup.CreationTimestamp.In(time.UTC).Format("2006-01-02T15:04:05Z"),
		FinishTime:           finishTime,
		Namespaces:           namespaces,
		ExecuteType:          MapGetValue(backup.Annotations, constants.ExecuteType, ""),
		Status:               status,
		ResourcesSize:        resourcesSize,
		PersistentVolumeSize: persistentVolumeSize,
		Notes:                backup.Status.FailureReason,
	}
}

func Convert2Restore(clusterName string, restore veleroV1.Restore) velero.Restore {
	status := restore.Status

	var finishTime string
	if status.CompletionTimestamp != nil {
		finishTime = status.CompletionTimestamp.In(time.UTC).Format("2006-01-02T15:04:05Z")
	}

	resourcesSize, _ := strconv.Atoi(MapGetValue(restore.Annotations, constants.ResourcesSize, "0"))
	persistentVolumeSize, _ := strconv.Atoi(MapGetValue(restore.Annotations, constants.PersistentVolumeSize, "0"))
	namespaceJson := MapGetValue(restore.Annotations, constants.RestoreNamespaces, "[]")
	namespaces := make([]string, 0)
	if err := json.Unmarshal([]byte(namespaceJson), &namespaces); err != nil {
		logger.GetSugared().Errorf("json.Unmarshal failed: %v", err)
	}

	newRestore := velero.Restore{
		TargetCluster:        clusterName,
		RestoreName:          restore.Name,
		RestoreTemplateId:    MapGetValue(restore.Labels, constants.RestoreTemplateId, ""),
		RestoreTemplateName:  MapGetValue(restore.Labels, constants.RestoreTemplateName, restore.Name),
		Description:          MapGetValue(restore.Annotations, constants.Description, ""),
		BackupsName:          restore.Spec.BackupName,
		CreateTime:           restore.CreationTimestamp.In(time.UTC).Format("2006-01-02T15:04:05Z"),
		FinishTime:           finishTime,
		Status:               string(status.Phase),
		Namespaces:           namespaces,
		IncludeResources:     restore.Spec.LabelSelector.MatchLabels,
		ExcludeResources:     LabelSelectorRequirementConvert2Map(restore.Spec.LabelSelector.MatchExpressions),
		ResourcesSize:        resourcesSize,
		PersistentVolumeSize: persistentVolumeSize,
		Notes:                status.FailureReason,
	}
	if string(status.Phase) == "" ||
		(newRestore.Status == string(veleroV1.RestorePhaseCompleted) && resourcesSize <= 0 && len(namespaces) == 0) ||
		(newRestore.Status == string(veleroV1.RestorePhasePartiallyFailed) && resourcesSize < 0 && len(namespaces) == 0) {
		newRestore.Status = "Waiting"
	}
	return newRestore
}

func Convert2Schedules(clusterName string, schedule veleroV1.Schedule) *velero.Schedules {
	metadata := schedule.ObjectMeta
	spec := schedule.Spec
	status := schedule.Status

	schedulesType := velero.SchedulesType(MapGetValue(metadata.Annotations, constants.Type, ""))

	schedulesStatus := string(status.Phase)

	if schedulesType == velero.Time {
		if schedulesStatus == "" {
			schedulesStatus = string(veleroV1.SchedulePhaseNew)
		}

		if spec.Paused {
			schedulesStatus = "Stopped"
		}
	} else if schedulesType == velero.Handle {
		if schedulesStatus == "" {
			schedulesStatus = string(veleroV1.SchedulePhaseEnabled)
		}
	}

	namespace := spec.Template.IncludedNamespaces
	allNamespaces := len(spec.Template.IncludedNamespaces) == 1 && spec.Template.IncludedNamespaces[0] == constants.Start
	if allNamespaces {
		namespace = []string{}
	}

	ans := &velero.Schedules{
		SchedulesName: metadata.Name,
		Status:        schedulesStatus,
		StorageServers: velero.StorageServers{
			StorageServersId: MapGetValue(metadata.Labels, constants.StorageServerId, ""),
		},
		Description:               MapGetValue(metadata.Annotations, constants.Description, ""),
		CreateTime:                metadata.CreationTimestamp.In(time.UTC).Format("2006-01-02T15:04:05Z"),
		ClusterName:               clusterName,
		Namespaces:                namespace,
		AllNamespaces:             allNamespaces,
		DefaultVolumesToFsBackup:  *spec.Template.DefaultVolumesToFsBackup,
		Type:                      schedulesType,
		Cron:                      *velero.NewCron(spec.Schedule, MapGetValue(metadata.Annotations, constants.SchedulesCronType, "custom")),
		Ttl:                       int(spec.Template.TTL.Hours() / 24),
		BackupStorageLocationName: spec.Template.StorageLocation,
	}

	if spec.Template.LabelSelector != nil {
		ans.IncludeResources = spec.Template.LabelSelector.MatchLabels
		ans.ExcludeResources = LabelSelectorRequirementConvert2Map(spec.Template.LabelSelector.MatchExpressions)
	}

	return ans
}

func RevertTemplateFromDb(template caas.RestoreTemplate) *velero.RestoreTemplate {
	restore := &veleroV1.Restore{}
	if err := json.Unmarshal([]byte(template.Restore), restore); err != nil {
		klog.Errorf("JSON 解析失败: %v", err)
	}
	temp := Convert2Restore(template.TargetCluster, *restore)

	restoreTemplate := &velero.RestoreTemplate{
		RestoreTemplateId:   strconv.FormatInt(template.ID, 10),
		RestoreTemplateName: temp.RestoreTemplateName,
		ScheduleName:        template.ScheduleName,
		RestoreType:         template.RestoreType,
		TargetCluster:       template.TargetCluster,
		FromCluster:         template.FromCluster,
		Description:         temp.Description,
		CreateTime:          template.CreateTime.In(time.UTC).Format("2006-01-02T15:04:05Z"),
		BackupsName:         temp.BackupsName,
		Namespaces:          temp.Namespaces,
		IncludeResources:    temp.IncludeResources,
		ExcludeResources:    temp.ExcludeResources,
		IsModify:            template.IsModify,
	}
	if template.IsModify {
		var modify []velero.Modify
		// 将字符串解析回对象
		if err := json.Unmarshal([]byte(template.Modify), &modify); err != nil {
			klog.Errorf("JSON 解析失败: %v", err)
		}
		restoreTemplate.Modifys = modify
	}

	return restoreTemplate
}
