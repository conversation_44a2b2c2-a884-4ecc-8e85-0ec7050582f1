package config

import (
	"encoding/json"
	"fmt"
	"os"
	"sort"
	"strings"
	"testing"

	"gopkg.in/yaml.v2"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	clusterconfig "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/cluster/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/sisyphustype"
	"k8s.io/apimachinery/pkg/util/sets"
)

var (
	nodeResetSteps = `
[
    {
        "name": "sisyphus-system-step-prepare-execution",
        "nickname": "执行部署平台初始化步骤",
        "orchestration": {
            "name": "sisyphus",
            "type": "component",
            "version": "v1.4.0-1.0.0-universal"
        },
        "actions": [
            "install"
        ],
        "status": "pending"
    },
    {
        "name": "reset-nodes",
        "nickname": "重置节点",
        "orchestration": {
            "name": "kubernetes",
            "type": "component",
            "version": "v1.23.17-1.0.0-universal"
        },
        "actions": [
            "install-init-dependencies"
        ],
        "status": "pending"
    },
    {
        "name": "uninstall-containerd",
        "nickname": "卸载容器运行时并清除用户数据",
        "orchestration": {
            "name": "containerd",
            "type": "component",
            "version": "v1.6.28-1.0.0-universal"
        },
        "actions": [
            "uninstall"
        ],
        "status": "pending"
    },
    {
        "name": "uninstall-docker",
        "nickname": "卸载容器运行时并清除用户数据"
    },
    {
        "name": "uninstall-sisyphus-flag",
        "nickname": "卸载sisyphus初始化步骤标记"
    }
]
`
)

func Test_ShouldReadConfig(t *testing.T) {
	config.NodeResetStepConfigPath.Value = "../../../../../node-reset-config.yaml"
	t.Run("tt", func(t *testing.T) {
		if err := ShouldReadConfig(); err != nil {
			t.Fatal(ShouldReadConfig())
		}
		bs, _ := json.Marshal(NodeResetConfig)
		fmt.Println(string(bs))
	})
}

func Test_NodeUPDown_Config_Sync_NodeResetSteps(t *testing.T) {
	// 先读取到配置
	config.NodeResetStepConfigPath.Value = "../../../../../node-reset-config.yaml"
	if err := ShouldReadConfig(); err != nil {
		t.Errorf(err.Error())
		return
	}
	// 设置需要写入配置的信息
	var sisyphusStep []sisyphustype.SisyphusStep
	if err := json.Unmarshal([]byte(nodeResetSteps), &sisyphusStep); err != nil {
		t.Errorf("format error,e:%v", err)
	}
	// 写入配置
	nrstep := groupNodeReset(sisyphusStep)

	SyncConfig("node-reset-group", "nodeResetLabel", nrstep)
	ya, _ := yaml.Marshal(NodeResetConfig)
	fmt.Println(string(ya))
	os.WriteFile(config.NodeResetStepConfigPath.Value, ya, os.ModePerm)
}

// 对节点重置进行分组
func groupNodeReset(sisyphusSteps []sisyphustype.SisyphusStep) (nodeResetSteps []sisyphustype.SisyphusStep) {
	for _, step := range sisyphusSteps {
		step := step
		nodeResetSteps = append(nodeResetSteps, step)
	}
	return
}

func SyncConfig(groupName string, label string, sisyphusSteps []sisyphustype.SisyphusStep) {
	stepGroupHandleFunc := func(groupName string, label string, sisyphusSteps []sisyphustype.SisyphusStep, groups clusterconfig.StepGroups) {
		// 先查找到group的index
		groupIndex := -1
		for index, g := range groups {
			if g.Code == groupName {
				groupIndex = index
			}
		}
		if groupIndex == -1 {
			return
		}
		groupItem := groups[groupIndex]
		removeNumber := 0
		// 先移除所有 和 nodeType 有关的Step
		for index, _ := range groupItem.Steps {
			groupStep := groupItem.Steps[index-removeNumber]
			stepSupportNodeTypes := sets.New[string](groupStep.Labels...)
			if stepSupportNodeTypes.Has(label) {
				stepSupportNodeTypes.Delete(label)
			}
			if stepSupportNodeTypes.Has(label + "/" + string(clustermodel.CRITypeContainerd)) {
				stepSupportNodeTypes.Delete(label + "/" + string(clustermodel.CRITypeContainerd))
			}

			if stepSupportNodeTypes.Has(label + "/" + string(clustermodel.CRITypeDocker)) {
				stepSupportNodeTypes.Delete(label + "/" + string(clustermodel.CRITypeDocker))
			}

			resultNodeConfigTypes := stepSupportNodeTypes.UnsortedList()
			if len(resultNodeConfigTypes) == 0 {
				groupItem.Steps = append(groupItem.Steps[0:index-removeNumber], groupItem.Steps[index-removeNumber+1:]...)
				removeNumber++
			} else {
				groupItem.Steps[index-removeNumber].Labels = resultNodeConfigTypes
			}
		}
		// 向最后扩展
		for _, sisyphusStep := range sisyphusSteps {
			var labels []string
			if sisyphusStep.Name == "uninstall-containerd" {
				labels = append(labels, label+"/"+string(clustermodel.CRITypeContainerd))
			} else if sisyphusStep.Name == "uninstall-docker" {
				labels = append(labels, label+"/"+string(clustermodel.CRITypeDocker))
			} else {
				labels = append(labels, label)
			}

			groupItem.Steps = append(groupItem.Steps, clusterconfig.Step{
				Code:   sisyphusStep.Name,
				Alias:  sisyphusStep.Nickname,
				Labels: labels,
			})
		}

		groups[groupIndex] = groupItem
	}
	// 处理节点重置的
	stepGroupHandleFunc(groupName, label, sisyphusSteps, NodeResetConfig.NodeResetStepGroups)
}

var (
	stepEn = `Uninstalling sisyphus initialization steps marked
Uninstalling container runtime and clearing user data
Uninstalling container runtime and clearing user data
Perform deployment platform initialization steps
Reset Node`
	stepHk = `卸載sisyphus初始化步驟標記
卸載容器運行時並清除用戶數據
卸載容器運行時並清除用戶數據
執行部署平臺初始化步驟
重置節點`
)

func Test_Sort_Name_And_NickName(t *testing.T) {

	sisyphusStepUnMarshalFunc := func(str string) ([]sisyphustype.SisyphusStep, error) {
		var sisyphusStep []sisyphustype.SisyphusStep
		err := json.Unmarshal([]byte(str), &sisyphusStep)
		return sisyphusStep, err
	}
	var keySet = map[string]string{}
	if steps, err := sisyphusStepUnMarshalFunc(nodeResetSteps); err != nil {
		t.Error(err)
		return
	} else {
		for _, step := range steps {
			keySet[step.Name] = step.Nickname
		}
	}

	keys := []string{}
	values := []string{}
	for k, v := range keySet {
		keys = append(keys, k)
		values = append(values, v)
	}
	sort.Strings(keys)
	sort.Strings(values)
	for _, v := range values {
		fmt.Println(v)
	}
	printStepSql(keys, values, keySet, "en-US", stepEn)
	printStepSql(keys, values, keySet, "zh-HK", stepHk)
}

func printStepSql(keys, values []string, keySetMap map[string]string, language, translateStr string) {
	fmt.Println("-- ", language)
	translate := strings.Split(translateStr, "\n")
	if len(keys) != len(values) || len(keys) != len(translate) {
		panic("length not eq")
	}
	valueIndexMap := map[string]int{}
	for index, value := range values {
		valueIndexMap[value] = index
	}

	for _, key := range keys {
		keyValue := keySetMap[key]
		valueIndex := valueIndexMap[keyValue]
		tVal := translate[valueIndex]
		fmt.Println(fmt.Sprintf("insert into sys_resource_translate_config(`group_name`,`unique_value`,`language_code`,`property`,`translation`) values('olympus-cluster-create-step','%s','%s','name','%s') ON DUPLICATE KEY update `translation`='%s';", key, language, tVal, tVal))
	}
}
