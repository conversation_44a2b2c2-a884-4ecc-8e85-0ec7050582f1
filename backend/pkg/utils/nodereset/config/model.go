package config

import (
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/cluster/config"
)

type Config struct {
	SolutionInfos       NodeResetSolutionInfoList `json:"solutionInfos" yaml:"solutionInfos"`             // 基线对应的解决方案信息列表
	Group               NodeResetGroup            `json:"group"`                                          // 对应节点重置的步骤
	NodeResetStepGroups config.StepGroups         `json:"nodeResetStepGroups" yaml:"nodeResetStepGroups"` // 节点重置的步骤分组
	Initial             InitialGroup              `json:"initial" yaml:"initial"`                         // 初始化分组信息
}

type NodeResetSolutionInfoList []NodeResetSolutionInfo

func (arr NodeResetSolutionInfoList) GetSolutionInfoByBaselineVersion(baselineVersion string) (NodeResetSolutionInfo, error) {
	for _, item := range arr {
		if item.BaselineVersion == baselineVersion {
			return item, nil
		}
	}
	return NodeResetSolutionInfo{}, errors.NewFromCodeWithMessage(errors.Var.UnKnowBaselineVersion, baselineVersion)
}

type NodeResetSolutionInfo struct {
	BaselineVersion string `json:"baselineVersion" yaml:"baselineVersion"` // 底座基线版本
	SolutionName    string `json:"solutionName" yaml:"solutionName"`       // 节点重置解决方案名称
	SolutionVersion string `json:"solutionVersion" yaml:"solutionVersion"` // 节点重置解决方案版本
}

// InitialGroup 初始化分组信息
type InitialGroup struct {
	NodeResetSisyphusSolutionApplyCode  string `json:"nodeResetSisyphusSolutionApplyCode" yaml:"nodeResetSisyphusSolutionApplyCode"`
	NodeResetSisyphusSolutionApplyAlias string `json:"nodeResetSisyphusSolutionApplyAlias" yaml:"nodeResetSisyphusSolutionApplyAlias"`
}

type NodeResetGroup struct {
	Reset string `json:"reset"`
}
