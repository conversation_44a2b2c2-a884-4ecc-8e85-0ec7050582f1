package config

import (
	"fmt"
	"os"
	"sync"

	"gopkg.in/yaml.v3"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
)

var NodeResetConfig *Config
var stepGroupConfigLock sync.Mutex

// ShouldReadConfig
// 启动任务是读取配置文件
func ShouldReadConfig() error {
	stepGroupConfigLock.Lock()
	defer stepGroupConfigLock.Unlock()
	if NodeResetConfig != nil {
		return nil
	}
	bytes, err := os.ReadFile(config.NodeResetStepConfigPath.Value)
	if err != nil {
		return fmt.Errorf("read file %s,appear error:%v", config.NodeResetStepConfigPath.Value, err)
	}
	NodeResetConfig = new(Config)
	err = yaml.Unmarshal(bytes, &NodeResetConfig)
	if err != nil {
		return fmt.Errorf("unmarshal file %s,appear error:%v", config.NodeResetStepConfigPath.Value, err)
	}
	return nil
}
