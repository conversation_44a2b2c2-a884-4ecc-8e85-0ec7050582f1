package task

import (
	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	addonmodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/addon"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
	clusterconfig "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/cluster/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/installerutil"
	nodeconfig "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/node/config"
	noderesetconfig "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/nodereset/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/nodereset/render/task/sisyphus_solutionparam"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/uuid"
	"k8s.io/apimachinery/pkg/util/sets"
)

func NewSisyphusNodeResetSolutionApplyTask(uuid uuid.UUIDIntf) Intf {
	return sisyphusNodeResetSolutionApplyTask{
		uuid:     uuid,
		paramMgr: sisyphus_solutionparam.NewParamMgr(),
	}
}

type sisyphusNodeResetSolutionApplyTask struct {
	uuid     uuid.UUIDIntf
	paramMgr sisyphus_solutionparam.ParamMgr
}

func (sisyphusNodeResetSolutionApplyTask) Name() string {
	return constants.TaskNameForNodeResetSisyphusSolutionApply
}

func (sisyphusNodeResetSolutionApplyTask) Description() string {
	return constants.TaskDescriptionForNodeResetSisyphusSolutionApply
}

func (sisyphusNodeResetSolutionApplyTask) Kind() installerv1alpha1.InstallerTaskKind {
	return installerv1alpha1.InstallerTaskKindSisyphusSolutionApply
}

func (sisyphusNodeResetSolutionApplyTask) Should(installer *installerv1alpha1.Installer, startFromFailed, reset bool) bool {
	// 节点重置 - 表单提交 需要进行判断
	return shouldNodeReset(installer, startFromFailed, reset)
}

func (applyTask sisyphusNodeResetSolutionApplyTask) CreateRequestRenderToInstallerTask(existTasks []installerv1alpha1.InstallerTaskSpec, task *installerv1alpha1.InstallerTaskSpec, status *installerv1alpha1.InstallerTaskStatus, request node.NodeResetRequest, config addonmodel.SisyphusAddressConfig) error {
	return installerutil.RenderTaskForSolutionApply(applyTask.uuid, config, task, func(solutionName string) (string, error) {
		return applyTask.paramMgr.CreateRequestAsParam(config, request, solutionName)
	})
}

func (sisyphusNodeResetSolutionApplyTask) InstallerTaskRenderToCreateStateResponse(processing *clustermodel.CreateProcessListResponse, groupCode string, groupAlias string, cri clustermodel.CRIType, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	// 查找信息提交的processing
	group, exist := processing.FindByCode(groupCode)
	if !exist {
		group = &clustermodel.CreateProcessResponse{
			Code:   groupCode,
			Name:   groupAlias,
			Status: clustermodel.ProcessStatusWaiting,
		}
		*processing = append(*processing, *group)
		group, _ = processing.FindByCode(nodeconfig.NodeUPDownConfig.Initial.GroupCode)
	}

	step := clustermodel.CreateProcessStepResponse{
		Code:   noderesetconfig.NodeResetConfig.Initial.NodeResetSisyphusSolutionApplyCode,
		Name:   noderesetconfig.NodeResetConfig.Initial.NodeResetSisyphusSolutionApplyAlias,
		Status: clustermodel.ProcessStatusWaiting,
	}
	if taskStatus != nil {
		step.Status = clustermodel.MustParseByInstallerStatus(taskStatus.Phase)
		if taskStatus.Phase == installerv1alpha1.StatusPhaseFailed {
			step.ErrorType = &taskStatus.Reason
			step.ErrorMessage = &taskStatus.Message
		}
	}
	group.Steps = append(group.Steps, step)
	group.Status = installerutil.GetStepsStatus(group.Steps)
	return nil
}

func shouldNodeReset(installer *installerv1alpha1.Installer, startFromFailed, reset bool) bool {

	// 请求中声明需要进行重置，则可以直接进行重置
	if reset {
		return true
	}
	//  ========= 以下是 reset = false 的逻辑
	// 如果原本有进行reset 则进行判断是否需要
	// 请求声明中不进行重置，但是上一次存在重试行为且没有运行成功
	resetRunningTaskStatus := installerutil.GetStatusTaskByKindAndName(installer.Status.Tasks, installerv1alpha1.InstallerTaskKindSisyphusSolutionExec, constants.TaskNameForNodeResetSisyphusSolutionExec)
	lastStepUnSuccess := resetRunningTaskStatus != nil && resetRunningTaskStatus.InstallerStatusInfo.Phase != installerv1alpha1.StatusPhaseSuccess
	// startFromFailed约定了从失败处开始 还是已完成节点重置，重新创建
	if startFromFailed {
		return lastStepUnSuccess
	}
	return false
}

func NewSisyphusNodeResetSolutionExecTask() Intf {
	return sisyphusNodeResetSolutionExecTask{}
}

type sisyphusNodeResetSolutionExecTask struct {
}

// Name
// 获取task 名称
func (sisyphusNodeResetSolutionExecTask) Name() string {
	return constants.TaskNameForNodeResetSisyphusSolutionExec

}

// Description
// 获取task 描述
func (sisyphusNodeResetSolutionExecTask) Description() string {
	return constants.TaskDescriptionForNodeResetSisyphusSolutionExec

}

// Kind
// 获取当前task 处理器的类型
func (sisyphusNodeResetSolutionExecTask) Kind() installerv1alpha1.InstallerTaskKind {
	return installerv1alpha1.InstallerTaskKindSisyphusSolutionExec

}

// Should
// 用于判断渲染taskSpec时是否需要提供该task
// if 需要提供
//
//	if 以存在 -> 修改
//	else     -> 新增
//
// else
//
//	if 以存在 -> 删除
//	else     -> nothing to do
func (sisyphusNodeResetSolutionExecTask) Should(installer *installerv1alpha1.Installer, startFromFailed, reset bool) bool {
	// 节点重置 - 任务执行 需要进行判断
	return shouldNodeReset(installer, startFromFailed, reset)
}

// CreateRequestRenderToInstallerTask
// 将 create request 内容渲染到task
func (sisyphusNodeResetSolutionExecTask) CreateRequestRenderToInstallerTask(existTasks []installerv1alpha1.InstallerTaskSpec, task *installerv1alpha1.InstallerTaskSpec, status *installerv1alpha1.InstallerTaskStatus, request node.NodeResetRequest, config addonmodel.SisyphusAddressConfig) error {
	// 获取exec 对应的solution 的名称
	solutionName, err := installerutil.GetSisyphusSolutionApplyTaskSolutionName(existTasks, constants.TaskNameForNodeResetSisyphusSolutionApply)
	if err != nil {
		return err
	}

	// 获取节点重置的期望步骤
	var expectSteps = ListNodeResetExpectStepCodes(request.CRI)

	return installerutil.RenderTaskForSolutionExec(task, solutionName, expectSteps, request.StartFromFailed, request.Reset, status, config)

}

// InstallerTaskRenderToCreateStateResponse
// 将步骤写入的Processing
func (sisyphusNodeResetSolutionExecTask) InstallerTaskRenderToCreateStateResponse(processing *clustermodel.CreateProcessListResponse, groupCode string, groupAlias string, cri clustermodel.CRIType, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error {
	// 定义一个集合 存储spec中的所有 step
	specParamsSet := sets.New[string]()
	if len(taskSpec.SisyphusSolutionExec.Param) != 0 {
		specParamsSet.Insert(taskSpec.SisyphusSolutionExec.Param...)
	}

	// 获取期望的步骤列表
	var expectProcessingList = make(clustermodel.CreateProcessListResponse, 0)
	var steps = ListNodeResetExpectStepCodes(cri)

	for _, group := range noderesetconfig.NodeResetConfig.NodeResetStepGroups {
		group := group
		processing := clustermodel.CreateProcessResponse{
			Code:        group.Code,
			Name:        group.Alias,
			Description: group.Description,
			Status:      clustermodel.ProcessStatusWaiting,
		}
		var processingSteps clustermodel.CreateProcessStepListResponse
		for _, step := range group.Steps.ListStepsMatchCodes(steps...) {
			step := step
			var status = clustermodel.ProcessStatusWaiting
			if !specParamsSet.Has(step.Code) {
				status = clustermodel.ProcessStatusSuccess
			}
			processingSteps = append(processingSteps, clustermodel.CreateProcessStepResponse{
				Code:        step.Code,
				Name:        step.Alias,
				Description: step.Description,
				Status:      status,
			})
		}
		processing.Steps = processingSteps
		expectProcessingList = append(expectProcessingList, processing)
	}

	// 设置step状态
	if taskStatus != nil && len(taskStatus.Steps) != 0 {
		// 构建status step map 便于查询
		statusStepMap := make(map[string]installerv1alpha1.InstallerStatusInfo, len(taskStatus.Steps))
		// 设置step 状态
		for _, step := range taskStatus.Steps {
			name := step.StepName
			statusInfo := step.InstallerStatusInfo
			statusStepMap[name] = statusInfo
		}
		// 将状态写入 expectProcessingList
		for index, _ := range expectProcessingList {
			expectProcessing := &expectProcessingList[index]
			// 先设置每个step 的状态
			for index1, _ := range expectProcessing.Steps {
				expectStep := &(expectProcessing.Steps[index1])
				statusStepInfo, exist := statusStepMap[expectStep.Code]
				if !exist {
					continue
				}
				expectStep.Status = clustermodel.MustParseByInstallerStatus(statusStepInfo.Phase)
				if statusStepInfo.Reason != "" {
					expectStep.ErrorType = &statusStepInfo.Reason
				}
				if statusStepInfo.Message != "" {
					expectStep.ErrorMessage = &statusStepInfo.Message
				}
			}
		}
	}

	// step 的状态设置完成后 设置组状态
	for index, _ := range expectProcessingList {
		expectProcessing := &expectProcessingList[index]
		expectProcessing.Status = installerutil.GetStepsStatus(expectProcessing.Steps)
	}

	*processing = append(*processing, expectProcessingList...)
	return nil
}

// listNodeResetExpectSteps 获取节点重置的期望步骤
func listNodeResetExpectSteps() []clusterconfig.Step {
	// 获取节点重置类型的期望步骤 这里可以直接传入类型 nodeUp（只可能节点上线有重置）
	var stepOptions = []string{"nodeResetLabel"}
	// 获取重置的期望执行步骤有序列表
	sortSteps := noderesetconfig.NodeResetConfig.NodeResetStepGroups.ListStepsAnyMatchLabels(stepOptions...)
	return sortSteps
}

// ListNodeResetExpectStepCodes 获取节点重置的期望步骤编码
func ListNodeResetExpectStepCodes(cri clustermodel.CRIType) []string {
	label := "nodeResetLabel"
	// 获取节点重置类型的期望步骤 这里可以直接传入类型 nodeUp（只可能节点上线有重置）
	var stepOptions = []string{label}
	stepOptions = append(stepOptions, label+"/"+string(cri))
	// 获取重置的期望执行步骤有序列表
	sortSteps := noderesetconfig.NodeResetConfig.NodeResetStepGroups.ListStepCodeAnyMatchLabels(stepOptions...)
	return sortSteps
}
