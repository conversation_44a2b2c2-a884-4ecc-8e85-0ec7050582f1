package task

import (
	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	addonmodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/addon"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
)

type Intf interface {
	// Name
	// 获取task 名称
	Name() string
	// Description
	// 获取task 描述
	Description() string

	// Kind
	// 获取当前task 处理器的类型
	Kind() installerv1alpha1.InstallerTaskKind

	// Should
	// 用于判断渲染taskSpec时是否需要提供该task
	Should(installer *installerv1alpha1.Installer, startFromFailed, reset bool) bool

	// CreateRequestRenderToInstallerTask
	// 将 create request 内容渲染到task
	CreateRequestRenderToInstallerTask(existTasks []installerv1alpha1.InstallerTaskSpec, task *installerv1alpha1.InstallerTaskSpec, status *installerv1alpha1.InstallerTaskStatus, request node.NodeResetRequest, config addonmodel.SisyphusAddressConfig) error

	// InstallerTaskRenderToCreateStateResponse
	// 将步骤写入的Processing
	InstallerTaskRenderToCreateStateResponse(processing *clustermodel.CreateProcessListResponse, groupCode string, groupAlias string, cri clustermodel.CRIType, taskSpec installerv1alpha1.InstallerTaskSpec, taskStatus *installerv1alpha1.InstallerTaskStatus) error
}
