package sisyphus_solutionparam

import (
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

func newNodeUpCommonModel(clusterName string, registry *cluster.CreateRegistryConfigRequest) jobParamStepProperty {
	var registryModel nodeUpCommonRegistry
	if registry != nil {
		registryModel = nodeUpCommonRegistry{
			Scheme:               registry.Protocol,
			Addr:                 registry.Address,
			Port:                 registry.Port,
			PauseImageRepository: "k8s-deploy",
		}
	}
	return nodeUpCommonModel{
		Common: nodeUpCommon{
			ClusterName: clusterName,
			Registry:    registryModel,
		},
	}
}

type nodeUpCommonModel struct {
	Common nodeUpCommon `json:"common"`
}

func (obj nodeUpCommonModel) asProperty() (map[string]interface{}, error) {
	var result = make(map[string]interface{})
	var err = utils.BeanCopy(obj, &result)
	return result, err
}

type nodeUpCommon struct {
	ClusterName string               `json:"clusterName"`
	Registry    nodeUpCommonRegistry `json:"registry,omitempty"`
}

type nodeUpCommonRegistry struct {
	Scheme               string `json:"scheme"`
	Addr                 string `json:"addr"`
	Port                 int    `json:"port"`
	PauseImageRepository string `json:"pauseImageRepository"`
}
