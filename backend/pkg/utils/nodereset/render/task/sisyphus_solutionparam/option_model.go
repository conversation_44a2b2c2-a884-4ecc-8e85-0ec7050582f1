package sisyphus_solutionparam

type commonOption struct {
	JoinMaster       bool `json:"joinMaster"`
	JoinNode         bool `json:"joinNode"`
	PreCheck         bool `json:"precheck"`
	AutoEditHostname bool `json:"autoEditHostname"`
}

type extraOption struct {
	DefaultDeployChrony        bool  `json:"defaultDeployChrony"`
	AddDefaultRegistry         bool  `json:"addDefaultRegistry"`
	NeedAuthForDefaultRegistry *bool `json:"needAuthForDefaultRegistry,omitempty"`
}
