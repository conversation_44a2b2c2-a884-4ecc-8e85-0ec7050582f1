package sisyphus_solutionparam

import (
	"encoding/json"

	"harmonycloud.cn/unifiedportal/cloudservice-operator/pkg/handler/installer/client"
	addonmodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/addon"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
	noderesetconfig "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/nodereset/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/sisyphustype"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/sisyphusutil"
)

type paramMgr struct {
	sisyphusclient client.Sisyphus
	jobParams      []jobParamStep
	options        []optionStep
}

func NewParamMgr() ParamMgr {
	return paramMgr{
		sisyphusclient: client.NewSisyphus(),
		jobParams:      []jobParamStep{
			// append job params hear
			//newNodeUpCommonJobParamStep(),
		},
		options: []optionStep{
			newWithCleanUpContainerOptionStep(),
			newWithContainerRuntimeStep(),
		},
	}
}
func (mgr paramMgr) CreateRequestAsParam(config addonmodel.SisyphusAddressConfig, createRequest node.NodeResetRequest, solutionName string) (string, error) {
	options, err := mgr.parseSisyphusOption(createRequest)
	if err != nil {
		return "", err
	}
	jobParam, err := mgr.parseSisyphusJobParams(createRequest)
	if err != nil {
		return "", err
	}
	hosts, err := parseSisyphusHostGroups(createRequest)
	if err != nil {
		return "", err
	}
	editContext, err := sisyphusutil.GetEditContext(mgr.sisyphusclient, config)
	if err != nil {
		return "", err
	}
	// 根据极限版本获取solution info
	solutionInfo, err := noderesetconfig.NodeResetConfig.SolutionInfos.GetSolutionInfoByBaselineVersion(createRequest.ClusterBaselineVersion)
	if err != nil {
		return "", err
	}
	sisyphusParam := sisyphustype.EditDeployRequest{
		Name: solutionName,
		Solution: sisyphustype.SolutionMetadata{
			Name:    solutionInfo.SolutionName,
			Version: solutionInfo.SolutionVersion,
		},
		Contexts: editContext,
		Hosts:    hosts,
		Options:  options,
		Params:   jobParam,
	}
	jsonBytes, err := json.Marshal(sisyphusParam)
	return string(jsonBytes), err
}

func parseSisyphusHostGroups(request node.NodeResetRequest) ([]sisyphustype.HostGroups, error) {
	var result = make([]sisyphustype.HostGroups, 0, len(request.Nodes))
	for _, n := range request.Nodes {
		result = append(result, sisyphustype.HostGroups{
			IP:     n.Ip,
			Groups: []string{noderesetconfig.NodeResetConfig.Group.Reset},
		})
	}
	return result, nil
}

func (mgr paramMgr) parseSisyphusOption(request node.NodeResetRequest) (map[string]interface{}, error) {
	var optionMap = make(map[string]interface{}, len(mgr.options)<<1)
	for _, option := range mgr.options {
		option := option
		should, err := option.should(request)
		if err != nil {
			return nil, err
		}
		if should {
			optionIntf, err := option.option(request)
			if err != nil {
				return nil, err
			}
			optionMap[option.name()] = optionIntf
		}
	}
	return optionMap, nil
}

func (mgr paramMgr) parseSisyphusJobParams(request node.NodeResetRequest) ([]sisyphustype.JobParam, error) {
	var jobParams = make([]sisyphustype.JobParam, 0)
	for _, jp := range mgr.jobParams {
		shouldConvert, err := jp.shouldConvert(request)
		if err != nil {
			return nil, err
		}
		if !shouldConvert {
			continue
		}
		name := jp.name()
		cvt, err := jp.convert(request)
		if err != nil {
			return nil, err
		}
		jobParams = append(jobParams, sisyphustype.JobParam{
			Name:       name,
			Properties: cvt,
		})
	}
	return jobParams, nil
}
