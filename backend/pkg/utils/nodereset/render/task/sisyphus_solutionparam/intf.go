package sisyphus_solutionparam

import (
	addonmodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/addon"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
)

// ParamMgr
// 处理西西弗斯的 solution param 序列化与反序列化管理器
type ParamMgr interface {
	CreateRequestAsParam(config addonmodel.SisyphusAddressConfig, createRequest node.NodeResetRequest, solutionName string) (string, error)
}

// jobParamStep
// 生成西西弗斯 solution param 每个步骤的内容
type jobParamStep interface {
	// 获取步骤名称
	name() string
	// 判断步骤是否需要转化 如没有macvlan 就不需要转化macvlan 的请求参数
	shouldConvert(createRequest node.NodeResetRequest) (bool, error)
	// 转化请求参数
	convert(createRequest node.NodeResetRequest) (map[string]interface{}, error)
}

// optionStep
// 生成西西弗斯 solution option 每个步骤的内容
type optionStep interface {
	name() string
	should(createRequest node.NodeResetRequest) (bool, error)
	option(createRequest node.NodeResetRequest) (interface{}, error)
}

type jobParamStepProperty interface {
	asProperty() (map[string]interface{}, error)
}
