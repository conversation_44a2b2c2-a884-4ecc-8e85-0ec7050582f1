package sisyphus_solutionparam

import (
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
)

const (
	withCleanUpContainerDataAction = "cleanup"
	withContainerRuntimeAction     = "containerRuntime"
)

type withCleanUpContainerOptionStep struct {
}

func newWithCleanUpContainerOptionStep() optionStep {
	return withCleanUpContainerOptionStep{}
}
func (withCleanUpContainerOptionStep) name() string {
	return withCleanUpContainerDataAction
}

func (withCleanUpContainerOptionStep) should(createRequest node.NodeResetRequest) (bool, error) {
	return true, nil
}
func (step withCleanUpContainerOptionStep) option(createRequest node.NodeResetRequest) (interface{}, error) {
	return true, nil
}

type withContainerRuntimeStep struct {
}

func newWithContainerRuntimeStep() optionStep {
	return withContainerRuntimeStep{}
}
func (withContainerRuntimeStep) name() string {
	return withContainerRuntimeAction
}

func (withContainerRuntimeStep) should(createRequest node.NodeResetRequest) (bool, error) {
	return createRequest.CRI == cluster.CRITypeDocker, nil
}

func (step withContainerRuntimeStep) option(createRequest node.NodeResetRequest) (interface{}, error) {
	return string(createRequest.CRI), nil
}
