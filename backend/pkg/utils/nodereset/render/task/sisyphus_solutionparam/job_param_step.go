package sisyphus_solutionparam

//
//const (
//	jobParamNameNodeUpCommon = "A001-节点上线通用参数"
//)
//
//type nodeUpCommonJobParamStep struct {
//}
//
//func newNodeUpCommonJobParamStep() jobParamStep {
//	return nodeUpCommonJobParamStep{}
//}
//
//// 获取步骤名称
//func (nodeUpCommonJobParamStep) name() string {
//	return jobParamNameNodeUpCommon
//}
//
//// 判断步骤是否需要转化 如没有macvlan 就不需要转化macvlan 的请求参数
//func (step nodeUpCommonJobParamStep) shouldConvert(createRequest node.NodeUpDownCreateRequest) (bool, error) {
//	return createRequest.Type == node.NodeUpDownTypeNodeUp, nil
//}
//
//// 转化请求参数
//func (step nodeUpCommonJobParamStep) convert(createRequest node.NodeUpDownCreateRequest) (map[string]interface{}, error) {
//	return newNodeUpCommonModel(createRequest.ClusterName, createRequest.Registry).asProperty()
//}
//
//// 将property 的内容渲染到 create response
//func (step nodeUpCommonJobParamStep) renderCreateResponse(property map[string]interface{}, createResponse *node.NodeUpDownCreateResponse) error {
//	var model nodeUpCommonModel
//	if err := utils.BeanCopy(property, &model); err != nil {
//		return err
//	}
//	createResponse.RegistryType = append(createResponse.RegistryType, clustermodel.RegistryTypeDefault)
//	if model.Common.Registry.Scheme != "" && model.Common.Registry.Port != 0 && model.Common.Registry.Addr != "" {
//		createResponse.RegistryType = append(createResponse.RegistryType, clustermodel.RegistryTypeCustom)
//		createResponse.Registry = new(clustermodel.CreateRegistryConfigResponse)
//		createResponse.Registry.Protocol = model.Common.Registry.Scheme
//		createResponse.Registry.Address = model.Common.Registry.Addr
//		createResponse.Registry.Port = model.Common.Registry.Port
//	}
//
//	// todo 先写死节点存储类型
//	createResponse.NodeConfig.Type = clustermodel.NodeStorageTypeManual
//
//	return nil
//}
//
//// 将property 的内容渲染到 create status response
//func (step nodeUpCommonJobParamStep) renderCreateStatusResponse(property map[string]interface{}, createResponse *clustermodel.CreateStatusResponse) error {
//	// nothing to do
//	return nil
//}
//
//// 将property 的内容渲染到 create response
//func (step nodeUpCommonJobParamStep) renderResponse(property map[string]interface{}, response *node.NodeUpDownResponse) error {
//	var model nodeUpCommonModel
//	if err := utils.BeanCopy(property, &model); err != nil {
//		return err
//	}
//	response.ClusterName = model.Common.ClusterName
//	return nil
//}
