package utils

import (
	"reflect"
	"strconv"
	"testing"
	"time"

	cloudservice_v1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/v1alpha1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	v1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type testStruct struct {
	Id float64
}

func Test_ToStruct(t *testing.T) {
	tests := []struct {
		input map[string]any
		want  interface{}
	}{
		{
			input: map[string]any{
				"Id": 3.14,
			},
			want: testStruct{
				Id: 3.14,
			},
		},
	}
	for index, test := range tests {
		t.Run("Test_ToStruct"+strconv.Itoa(index), func(t *testing.T) {
			obj := testStruct{}
			_ = ToStruct(test.input, &obj)
			if !reflect.DeepEqual(obj, test.want) {
				t.<PERSON>rf("[ERROR] result is not equals want ,result is \n %+v \n,want is \n %+v \n", obj, test.want)
			}
		})

	}

}

func Test_MustGetLabel(t *testing.T) {
	tests := []struct {
		input struct {
			key    string
			labels map[string]string
		}
		want string
	}{
		{
			input: struct {
				key    string
				labels map[string]string
			}{key: "key", labels: nil},
			want: "",
		},
		{
			input: struct {
				key    string
				labels map[string]string
			}{key: "key", labels: map[string]string{}},
			want: "",
		},
		{
			input: struct {
				key    string
				labels map[string]string
			}{key: "key", labels: map[string]string{
				"key": "value",
			}},
			want: "value",
		},
	}
	for index, test := range tests {
		t.Run("Test_MustGetLabel-"+strconv.Itoa(index), func(t *testing.T) {
			val := MustGetLabel(test.input.key, test.input.labels)
			if val != test.want {
				t.Errorf("result not equals want ,resutl is %s, want is %s", val, test.want)
			}
		})
	}
}

func Test_MustInt64(t *testing.T) {
	var number int64 = 10
	tests := []struct {
		input *int64
		want  int64
	}{
		{
			input: &number,
			want:  number,
		},
		{
			input: nil,
			want:  0,
		},
	}
	for index, test := range tests {
		t.Run("Test_MustInt64-"+strconv.Itoa(index), func(t *testing.T) {
			val := MustInt64(test.input)
			if val != test.want {
				t.Errorf("result not equals want ,resutl is %d, want is %d", val, test.want)
			}
		})
	}
}

func Test_Convert2CloudServiceComponent(t *testing.T) {
	now := time.Now()
	tests := []struct {
		component cloudservice_v1alpha1.CloudComponent
		want      models.CloudServiceComponent
	}{
		{
			component: cloudservice_v1alpha1.CloudComponent{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "caas-core",
					Namespace: "cloudservice-skyview",
					Labels: map[string]string{
						"unified-platform.harmonycloud.cn/cloudcomponent-name": "caas-core",
						"unified-platform.harmonycloud.cn/cloudservice-name":   "skyview",
						"unified-platform.harmonycloud.cn/cluster":             "cluster-local",
					},
					CreationTimestamp: metav1.Time{
						Time: now,
					},
				},
				Spec: cloudservice_v1alpha1.CloudComponentSpec{
					ClusterPolicy: cloudservice_v1alpha1.ClusterManagedPolicy,
					Description:   "核心组件",
					DisplayName:   "caas-core",
					LabelSelector: &metav1.LabelSelector{
						MatchLabels: map[string]string{
							"app": "caas-core",
						},
					},

					Name:       "caas-core",
					Namespaces: []string{"caas-system"},
					Version:    "v3.2.0",
				}, Status: cloudservice_v1alpha1.CloudComponentStatus{
					Phase: cloudservice_v1alpha1.CloudComponentRunningPhase,
				},
			},
			want: models.CloudServiceComponent{
				CloudComponentName: "caas-core",
				ClusterPolicy:      models.CloudServiceComponentPolicyManaged,
				Cluster:            "cluster-local",
				CreateTime:         now,
				Description:        "核心组件",
				DisplayName:        "caas-core",
				Version:            "v3.2.0",
				Status:             models.CloudServiceComponentRunning,
			},
		},
		{
			component: cloudservice_v1alpha1.CloudComponent{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "cluster-local-better-autoscaler-controller",
					Namespace: "cloudservice-skyview",
					Labels: map[string]string{
						"unified-platform.harmonycloud.cn/cloudcomponent-name": "better-autoscaler-controller",
						"unified-platform.harmonycloud.cn/cloudservice-name":   "skyview",
						"unified-platform.harmonycloud.cn/cluster":             "cluster-local",
					},
					CreationTimestamp: metav1.Time{
						Time: now,
					},
				},
				Spec: cloudservice_v1alpha1.CloudComponentSpec{
					ClusterPolicy: cloudservice_v1alpha1.ClusterWorkPolicy,
					Description:   "弹性伸缩",
					DisplayName:   "better-autoscaler-controller",
					LabelSelector: &metav1.LabelSelector{
						MatchLabels: map[string]string{
							"app": "better-autoscaler-controller",
						},
					},
					Name:       "better-autoscaler-controller",
					Namespaces: []string{"caas-system"},
					Version:    "v3.2.0",
				}, Status: cloudservice_v1alpha1.CloudComponentStatus{
					Phase:     cloudservice_v1alpha1.CloudComponentRunningPhase,
					Pods:      Int64Prt(10),
					ReadyPods: Int64Prt(5),
					Resources: cloudservice_v1alpha1.CloudStatusResourceList{
						Requests: map[v1.ResourceName]resource.Quantity{
							"cpu":    resource.MustParse("100m"),
							"memory": resource.MustParse("128Mi"),
						},
						Limits: map[v1.ResourceName]resource.Quantity{
							"cpu":    resource.MustParse("500m"),
							"memory": resource.MustParse("512Mi"),
						}, Usages: map[v1.ResourceName]resource.Quantity{
							"cpu":    resource.MustParse("200m"),
							"memory": resource.MustParse("256Mi"),
						},
					},
				},
			},
			want: models.CloudServiceComponent{
				CloudComponentName: "better-autoscaler-controller",
				ClusterPolicy:      models.CloudServiceComponentPolicyWork,
				Cluster:            "cluster-local",
				CreateTime:         now,
				Description:        "弹性伸缩",
				DisplayName:        "better-autoscaler-controller",
				Version:            "v3.2.0",
				Status:             models.CloudServiceComponentRunning,
				CpuRequest:         0.1,
				CpuLimit:           0.5,
				CpuUsage:           0.2,
				MemoryRequest:      0.125,
				MemoryLimit:        0.5,
				MemoryUsage:        0.25,
				PodNum:             10,
				ReadyPodNumber:     5,
			},
		},
	}
	for index, test := range tests {
		t.Run("Test_Convert2CloudServiceComponent"+strconv.Itoa(index), func(t *testing.T) {
			result := Convert2CloudServiceComponent(test.component)
			if !reflect.DeepEqual(result, test.want) {
				t.Errorf("testName error,result not equal want. result \n %+v \n,want \n %+v \n", result, test.want)
			}
		})
	}
}

func Test_CpuQuotaToCore(t *testing.T) {
	tests := []struct {
		input  string
		output float64
	}{
		{
			input:  "0",
			output: 0,
		},
		{
			input:  "1m",
			output: 0.001,
		},
		{
			input:  "10m",
			output: 0.01,
		},
		{
			input:  "100m",
			output: 0.1,
		}, {
			input:  "1000m",
			output: 1,
		}, {
			input:  "1",
			output: 1,
		}, {
			input:  "10",
			output: 10,
		}, {
			input:  "100",
			output: 100,
		}, {
			input:  "1000",
			output: 1000,
		}, {
			input:  "1k",
			output: 1000,
		},
	}

	for index, test := range tests {
		t.Run("Test_CpuQuotaToCore-"+strconv.Itoa(index), func(t *testing.T) {
			quota := resource.MustParse(test.input)
			result := CpuQuotaToCore(&quota)
			if result != test.output {
				t.Errorf("[Test_CpuQuotaToCore Error],index is %d,input is %s,result is %v,output is %v", index, test.input, result, test.output)
			}
		})
	}

}

func Test_MemoryQuotaToGi(t *testing.T) {
	tests := []struct {
		input  string
		output float64
	}{
		{
			input:  "0",
			output: 0,
		}, {
			input:  "128Mi",
			output: 0.125,
		}, {
			input:  "256Mi",
			output: 0.25,
		}, {
			input:  "512Mi",
			output: 0.5,
		}, {
			input:  "1024Mi",
			output: 1,
		}, {
			input:  "1Gi",
			output: 1,
		}, {
			input:  "10Gi",
			output: 10,
		}, {
			input:  "1Ti",
			output: 1024,
		},
	}

	for index, test := range tests {
		t.Run("Test_MemoryQuotaToGi-"+strconv.Itoa(index), func(t *testing.T) {
			quota := resource.MustParse(test.input)
			result := MemoryQuotaToGi(&quota)
			if result != test.output {
				t.Errorf("[Test_CpuQuotaToCore Error],index is %d,input is %s,result is %v,output is %v", index, test.input, result, test.output)
			}
		})
	}
}
