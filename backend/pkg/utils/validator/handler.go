package validator

import (
	"reflect"
	"regexp"
	"strings"

	"github.com/go-playground/validator/v10"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/network"
	"k8s.io/apimachinery/pkg/util/sets"
)

type Handler interface {
	Tag() string
	HandleFunc() validator.Func
	SupportKinds(kind reflect.Kind) bool
	SupportTypes(t reflect.Type) bool
}

// kubernetesInstanceNameHandler
// 名称需要复合k8s的名称规范
type kubernetesInstanceNameHandler struct {
	kubernetesNameRegex *regexp.Regexp
}

// newKubernetesInstanceNameHandler
// kubernetesInstanceNameHandler 构造方法
func newKubernetesInstanceNameHandler() Handler {
	return &kubernetesInstanceNameHandler{
		kubernetesNameRegex: regexp.MustCompile("^[a-z0-9]([-a-z0-9]*[a-z0-9])?$"),
	}
}
func (*kubernetesInstanceNameHandler) Tag() string {
	return "kubernetes_instance_name"
}
func (h *kubernetesInstanceNameHandler) HandleFunc() validator.Func {
	return func(fl validator.FieldLevel) bool {
		// 类型检查
		if !(h.SupportKinds(fl.Field().Kind()) && h.SupportTypes(fl.Field().Type())) {
			return false
		}
		// 0 值不做处理
		if fl.Field().IsZero() {
			return true
		}
		value := fl.Field().String()
		return h.kubernetesNameRegex.MatchString(value)
	}
}
func (*kubernetesInstanceNameHandler) SupportKinds(kind reflect.Kind) bool {
	switch kind {
	case reflect.String:
		return true
	case reflect.Ptr:
		return true
	default:
		return false
	}
}
func (*kubernetesInstanceNameHandler) SupportTypes(t reflect.Type) bool {
	return isTypeIncludePtrEquals(t, reflect.String)
}

// notTrimSpaceEmptyHandler
// 字符串中必须包含值 trimSpace后不能为empty
type notTrimSpaceEmptyHandler struct {
}

// newNotTrimSpaceEmptyHandler
// notTrimSpaceEmptyHandler 构造方法
func newNotTrimSpaceEmptyHandler() Handler {
	return &notTrimSpaceEmptyHandler{}
}
func (*notTrimSpaceEmptyHandler) Tag() string {
	return "not_trim_space_empty"
}
func (h *notTrimSpaceEmptyHandler) HandleFunc() validator.Func {
	return func(fl validator.FieldLevel) bool {
		// 类型检查
		if !(h.SupportKinds(fl.Field().Kind()) && h.SupportTypes(fl.Field().Type())) {
			return false
		}
		// 0 值不做处理
		if fl.Field().IsZero() {
			return true
		}

		fieldValue := fl.Field().String()
		fieldValue = strings.TrimSpace(fieldValue)
		return !strings.EqualFold(fieldValue, "")
	}
}
func (*notTrimSpaceEmptyHandler) SupportKinds(kind reflect.Kind) bool {
	switch kind {
	case reflect.String:
		return true
	case reflect.Ptr:
		return true
	default:
		return false
	}
}
func (*notTrimSpaceEmptyHandler) SupportTypes(t reflect.Type) bool {
	return isTypeIncludePtrEquals(t, reflect.String)
}

// protocolWebHandler
// 必须为web协议 http｜https
type protocolWebHandler struct {
}

// newProtocolWebHandler
// notTrimSpaceEmptyHandler 构造方法
func newProtocolWebHandler() Handler {
	return &protocolWebHandler{}
}
func (*protocolWebHandler) Tag() string {
	return "protocol_web"
}
func (h *protocolWebHandler) HandleFunc() validator.Func {
	return func(fl validator.FieldLevel) bool {
		// 类型检查
		if !(h.SupportKinds(fl.Field().Kind()) && h.SupportTypes(fl.Field().Type())) {
			return false
		}
		// 0 值不做处理
		if fl.Field().IsZero() {
			return true
		}

		fieldValue := fl.Field().String()
		return strings.EqualFold(fieldValue, "http") || strings.EqualFold(fieldValue, "https")
	}
}

func (*protocolWebHandler) SupportKinds(kind reflect.Kind) bool {
	switch kind {
	case reflect.String:
		return true
	case reflect.Ptr:
		return true
	default:
		return false
	}
}
func (*protocolWebHandler) SupportTypes(t reflect.Type) bool {
	return isTypeIncludePtrEquals(t, reflect.String)
}

// addressHandler
// 地址handler 比如为ipv4 ipv6 域名
type addressHandler struct {
}

// newAddressHandler
// notTrimSpaceEmptyHandler 构造方法
func newAddressHandler() Handler {
	return &addressHandler{}
}
func (*addressHandler) Tag() string {
	return "address"
}
func (h *addressHandler) HandleFunc() validator.Func {
	return func(fl validator.FieldLevel) bool {
		// 类型检查
		if !(h.SupportKinds(fl.Field().Kind()) && h.SupportTypes(fl.Field().Type())) {
			return false
		}
		// 0 值不做处理
		if fl.Field().IsZero() {
			return true
		}
		value := fl.Field().String()
		return network.IsIPv4IPv6(value) || network.IsHost(value)
	}
}
func (*addressHandler) SupportKinds(kind reflect.Kind) bool {
	switch kind {
	case reflect.String:
		return true
	case reflect.Ptr:
		return true
	default:
		return false
	}
}
func (*addressHandler) SupportTypes(t reflect.Type) bool {
	return isTypeIncludePtrEquals(t, reflect.String)
}

// ipv4IPv6Handler
// 地址handler 比如为ipv4 ipv6
type ipv4IPv6Handler struct {
}

// newAddressHandler
// notTrimSpaceEmptyHandler 构造方法
func newIPv4IPv6Handler() Handler {
	return &ipv4IPv6Handler{}
}
func (*ipv4IPv6Handler) Tag() string {
	return "ipv4ipv6"
}
func (h *ipv4IPv6Handler) HandleFunc() validator.Func {
	return func(fl validator.FieldLevel) bool {
		// 类型检查
		if !(h.SupportKinds(fl.Field().Kind()) && h.SupportTypes(fl.Field().Type())) {
			return false
		}
		// 0 值不做处理
		if fl.Field().IsZero() {
			return true
		}
		value := fl.Field().String()
		return network.IsIPv4IPv6(value)
	}
}
func (*ipv4IPv6Handler) SupportKinds(kind reflect.Kind) bool {
	switch kind {
	case reflect.String:
		return true
	case reflect.Ptr:
		return true
	default:
		return false
	}
}
func (*ipv4IPv6Handler) SupportTypes(t reflect.Type) bool {
	return isTypeIncludePtrEquals(t, reflect.String)
}

// portHandler
// 必须为合法的端口
type portHandler struct {
}

// newPortHandler
// notTrimSpaceEmptyHandler 构造方法
func newPortHandler() Handler {
	return &portHandler{}
}
func (*portHandler) Tag() string {
	return "port"
}
func (h *portHandler) HandleFunc() validator.Func {
	return func(fl validator.FieldLevel) bool {
		// 类型检查
		if !(h.SupportKinds(fl.Field().Kind()) && h.SupportTypes(fl.Field().Type())) {
			return false
		}
		// 0 值不做处理
		if fl.Field().IsZero() {
			return true
		}
		fieldValue := fl.Field().Int()
		return fieldValue >= 0 && fieldValue <= 65535
	}
}
func (*portHandler) SupportKinds(kind reflect.Kind) bool {
	switch kind {
	case reflect.Int:
		return true
	case reflect.Int8:
		return true
	case reflect.Int16:
		return true
	case reflect.Int32:
		return true
	case reflect.Int64:
		return true
	case reflect.Uint:
		return true
	case reflect.Uint8:
		return true
	case reflect.Uint16:
		return true
	case reflect.Uint32:
		return true
	case reflect.Uint64:
		return true
	case reflect.Ptr:
		return true
	default:
		return false
	}
}
func (*portHandler) SupportTypes(t reflect.Type) bool {
	return isTypeIncludePtrEquals(t, reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64, reflect.Uint, reflect.Uint8, reflect.Uint16, reflect.Uint32, reflect.Uint64)
}

func newNodeUpDownTypeHandler() Handler {
	ss := sets.New[string]()
	ss.Insert(string(node.NodeUpDownTypeNodeUp))
	ss.Insert(string(node.NodeUpDownTypeNodeDown))
	return &nodeUpDownTypeHandler{
		supports: ss,
	}
}

type nodeUpDownTypeHandler struct {
	supports sets.Set[string]
}

func (*nodeUpDownTypeHandler) Tag() string {
	return "node_up_down_type"
}
func (h *nodeUpDownTypeHandler) HandleFunc() validator.Func {
	return func(fl validator.FieldLevel) bool {
		// 类型检查
		if !(h.SupportKinds(fl.Field().Kind()) && h.SupportTypes(fl.Field().Type())) {
			return false
		}
		value := fl.Field().String()
		return h.supports.Has(value)
	}
}
func (*nodeUpDownTypeHandler) SupportKinds(kind reflect.Kind) bool {

	switch kind {
	case reflect.String:
		fallthrough
	case reflect.Ptr:
		return true
	default:
		return false
	}
}
func (*nodeUpDownTypeHandler) SupportTypes(t reflect.Type) bool {
	return isTypeIncludePtrEquals(t, reflect.String)
}

func newNodeStorageTypeHandler() Handler {
	ss := sets.New[string]()
	ss.Insert(string(cluster.NodeStorageTypeAuto))
	ss.Insert(string(cluster.NodeStorageTypeManual))
	return &nodeStorageHandler{
		supports: ss,
	}
}

type nodeStorageHandler struct {
	supports sets.Set[string]
}

func (*nodeStorageHandler) Tag() string {
	return "nodeStorage"
}
func (h *nodeStorageHandler) HandleFunc() validator.Func {
	return func(fl validator.FieldLevel) bool {
		// 类型检查
		if !(h.SupportKinds(fl.Field().Kind()) && h.SupportTypes(fl.Field().Type())) {
			return false
		}
		value := fl.Field().String()
		return h.supports.Has(value)
	}
}
func (*nodeStorageHandler) SupportKinds(kind reflect.Kind) bool {
	switch kind {
	case reflect.String:
		fallthrough
	case reflect.Ptr:
		return true
	default:
		return false
	}
}
func (*nodeStorageHandler) SupportTypes(t reflect.Type) bool {
	return isTypeIncludePtrEquals(t, reflect.String)
}
