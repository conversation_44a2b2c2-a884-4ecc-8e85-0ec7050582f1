package validator

import (
	"github.com/go-playground/validator/v10"
)

var handlers = []Handler{
	newKubernetesInstanceNameHandler(),
	newNotTrimSpaceEmptyHandler(),
	newProtocolWebHandler(),
	newAddressHandler(),
	newIPv4IPv6Handler(),
	newPortHandler(),
	newNodeUpDownTypeHandler(),
	newNodeStorageTypeHandler(),
}
var structHandlers = []StructHandler{}

func AddRegister(v *validator.Validate) {
	for _, h := range handlers {
		v.RegisterValidation(h.Tag(), h.HandleFunc(), true)
	}
	for _, sh := range structHandlers {
		v.RegisterStructValidation(sh.HandlerFunc(), sh.Structs()...)
	}
}
