package validator

import (
	"reflect"
)

// isTypeIncludePtrEquals
// 判断类型是否相等（包含指针）
func isTypeIncludePtrEquals(t reflect.Type, kinds ...reflect.Kind) bool {
	for _, kind := range kinds {
		switch t.String() {
		case kind.String():
			return true
		case "*" + kind.String():
			return true
		}

		switch t.Kind().String() {
		case kind.String():
			return true
		case "*" + kind.String():
			return true
		}
	}
	return false
}
