package validator

import (
	"errors"
	"fmt"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
)

// 常量定义区
const (
	LIMIT_PV_STORAGE = "2Pi"
)

// 资源类型定义区
const (
	PV          = "PersistentVolume"
	PVC         = "PersistentVolumeClaim"
	Pod         = "Pod"
	Deployment  = "Deployment"
	Service     = "Service"
	ReplicaSet  = "ReplicaSet"
	DaemonSet   = "DaemonSet"
	StatefulSet = "StatefulSet"
	Namespace   = "Namespace"
	Job         = "Job"
	CronJob     = "CronJob"
	ConfigMap   = "ConfigMap"
	Secret      = "Secret"
)

// 定义验证函数类型
type validFunc func(obj *unstructured.Unstructured) error

type validFuncList []validFunc

// 验证函数注册表
var validFuncMap = map[schema.GroupVersionKind]validFuncList{
	// 使用 WithKind 来直接生成对应的 GVK
	corev1.SchemeGroupVersion.WithKind(PV): {validPersistentVolumeResourceStorage},
}

// GetValidFuncByGVK 根据 GVK 获取对应的验证函数
func GetValidFuncByGVK(gvk schema.GroupVersionKind) []validFunc {
	if validateFunc, exists := validFuncMap[gvk]; exists {
		return validateFunc
	}
	return []validFunc{validNil}
}

// ValidateResource 根据 GVK 调用对应的验证函数
func ValidateResource(obj *unstructured.Unstructured, gvk schema.GroupVersionKind) error {
	if validateFunc, exists := validFuncMap[gvk]; exists {
		for _, f := range validateFunc {
			if err := f(obj); err != nil {
				return err
			}
		}
	}
	return nil
}

// validNil 默认的验证函数
func validNil(_ *unstructured.Unstructured) error {
	return nil
}

// validPersistentVolume 验证 PersistentVolume
func validPersistentVolumeResourceStorage(obj *unstructured.Unstructured) error {
	var pv corev1.PersistentVolume
	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(obj.Object, &pv); err != nil {
		return err
	}

	storageCapacity, exists := pv.Spec.Capacity[corev1.ResourceStorage]
	if !exists {
		return errors.New("PersistentVolume must have storage capacity")
	}

	threshold := resource.MustParse(LIMIT_PV_STORAGE)
	if storageCapacity.Cmp(threshold) > 0 {
		return errors.New(fmt.Sprintf("PersistentVolume storage capacity must be less than %s", LIMIT_PV_STORAGE))
	}
	return nil
}

func validStatefulSetPodTemplate(obj *unstructured.Unstructured) error {
	var statefulSet appsv1.StatefulSet
	if err := runtime.DefaultUnstructuredConverter.FromUnstructured(obj.Object, &statefulSet); err != nil {
		return err
	}
	// 获取 PodTemplateSpec
	podTemplateSpec := statefulSet.Spec.Template
	// 校验 Pod 模板中必须的字段
	if podTemplateSpec.Spec.Containers == nil || len(podTemplateSpec.Spec.Containers) == 0 {
		return errors.New("StatefulSet must have at least one container in Pod template")
	}
	for _, container := range podTemplateSpec.Spec.Containers {
		// 校验容器名称是否为空
		if container.Name == "" {
			return errors.New("container name cannot be empty in Pod template")
		}
		// 校验镜像是否定义
		if container.Image == "" {
			return fmt.Errorf("container '%s' must have an image specified", container.Name)
		}
	}
	return nil
}
