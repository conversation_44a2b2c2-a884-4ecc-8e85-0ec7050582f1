package sisyphustype

// todo 考虑直接引用西西弗斯作为依赖
type EditDeployRequest struct {
	Name     string                 `json:"name"`
	Solution SolutionMetadata       `json:"solution"`
	Contexts EditDeployContext      `json:"contexts"`
	Hosts    []HostGroups           `json:"hosts"`
	Options  map[string]interface{} `json:"options"`
	Params   []JobParam             `json:"params"`
	Disks    []EditDeployDisk       `json:"disks,omitempty"`
}

type EditDeployDisk struct {
	Name string         `json:"name"`
	VGs  []EditDeployVG `json:"vgs,omitempty"`
}

type EditDeployVG struct {
	VGName  string            `json:"vgName"`
	Devices map[string]string `json:"devices,omitempty"`
	LVs     []EditDeployLV    `json:"lvs,omitempty"`
}

type EditDeployLV struct {
	LVName  string         `json:"lvName"`
	Percent map[string]int `json:"percent"`
}

type SolutionMetadata struct {
	Name    string `json:"name"`
	Version string `json:"version"`
}
type EditDeployContext struct {
	SisyphusComponentVersion string  `bson:"sisyphusComponentVersion" json:"sisyphusComponentVersion" yaml:"sisyphusComponentVersion"`
	ImageRegistryAddr        string  `bson:"imageRegistryAddr" json:"imageRegistryAddr" yaml:"imageRegistryAddr"`
	ImageRegistryUsername    *string `bson:"imageRegistryUsername,omitempty" json:"imageRegistryUsername,omitempty" yaml:"imageRegistryUsername,omitempty"`
	ImageRegistryPassword    *string `bson:"imageRegistryPassword,omitempty" json:"imageRegistryPassword,omitempty" yaml:"imageRegistryPassword,omitempty"`
	PypiAddr                 string  `bson:"pypiAddr" json:"pypiAddr" yaml:"pypiAddr"`
	PypiUsername             *string `bson:"pypiUsername,omitempty" json:"pypiUsername,omitempty" yaml:"pypiUsername,omitempty"`
	PypiPassword             *string `bson:"pypiPassword,omitempty" json:"pypiPassword,omitempty" yaml:"pypiPassword,omitempty"`
	HelmRepoAddr             string  `bson:"helmRepoAddr" json:"helmRepoAddr" yaml:"helmRepoAddr"`
	HelmRepoUsername         *string `bson:"helmRepoUsername,omitempty" json:"helmRepoUsername,omitempty" yaml:"helmRepoUsername,omitempty"`
	HelmRepoPassword         *string `bson:"helmRepoPassword,omitempty" json:"helmRepoPassword,omitempty" yaml:"helmRepoPassword,omitempty"`
	LinuxPackageAddr         string  `bson:"linuxPackageAddr" json:"linuxPackageAddr" yaml:"linuxPackageAddr"`
	LinuxPackageUsername     *string `bson:"linuxPackageUsername,omitempty" json:"linuxPackageUsername,omitempty" yaml:"linuxPackageUsername,omitempty"`
	LinuxPackagePassword     *string `bson:"linuxPackagePassword,omitempty" json:"linuxPackagePassword,omitempty" yaml:"linuxPackagePassword,omitempty"`
}

type HostGroups struct {
	IP     string   `json:"ip"`
	Groups []string `json:"groups"`
}

type JobParam struct {
	Name       string                 `json:"name"`
	Properties map[string]interface{} `json:"properties"`
}

type SisyphusStep struct {
	Name     string `json:"name"`
	Nickname string `json:"nickname"`
}
