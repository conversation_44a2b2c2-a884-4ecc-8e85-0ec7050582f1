package utils

import (
	"context"

	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/midware-go/midwares/auth"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/midware"
)

func RequestContext(c *gin.Context) context.Context {
	return c
}

// GetUserTokenFromRequest ...
func GetUserTokenFromRequest(c *gin.Context) string {
	header := c.Get<PERSON>eader("Authorization")
	return header
}

// GetUserTokenFromContext ...
func GetUserTokenFromContext(ctx context.Context) (token string) {
	if v, ok := ctx.Value(midware.Key(midware.UserTokenContextKey)).(string); ok && v != "" {
		return v
	}
	if c, ok := ctx.(*gin.Context); ok {
		if anyV, existed := c.Get(midware.UserTokenContextKey); existed && anyV != "" {
			if v, ok := anyV.(string); ok && v != "" {
				return v
			}
		}
	}
	return auth.GetToken(ctx)
}

// GetAppCodeFromContext ...
func GetAppCodeFromContext(ctx context.Context) string {
	if v, ok := ctx.Value(midware.Key(midware.AppCodeContextKey)).(string); ok {
		return v
	}
	if c, ok := ctx.Value(midware.Key(midware.AppCodeContextKey)).(string); ok {
		return c
	}
	return ""
}

// GetAppIdFromContext ...
func GetAppIdFromContext(ctx context.Context) string {
	if v, ok := ctx.Value(midware.Key(midware.AppIdContextKey)).(string); ok {
		return v
	}
	if c, ok := ctx.Value(midware.Key(midware.AppIdContextKey)).(string); ok {
		return c
	}
	return ""
}

// GetOrganIdFromContext ...
func GetOrganIdFromContext(ctx context.Context) string {
	if v, ok := ctx.Value(midware.Key(midware.OrganIdContextKey)).(string); ok {
		return v
	}
	if c, ok := ctx.Value(midware.OrganIdContextKey).(string); ok {
		return c
	}
	return ""
}

// GetProjectIdFromContext ...
func GetProjectIdFromContext(ctx context.Context) string {
	if v, ok := ctx.Value(midware.Key(midware.ProjectIdContextKey)).(string); ok {
		return v
	}
	if c, ok := ctx.Value(midware.Key(midware.ProjectIdContextKey)).(string); ok {
		return c
	}
	return ""
}

// InProjectWorkspace 是否在项目工作空间下
func InProjectWorkspace(ctx context.Context) bool {
	return len(GetProjectIdFromContext(ctx)) > 0
}
