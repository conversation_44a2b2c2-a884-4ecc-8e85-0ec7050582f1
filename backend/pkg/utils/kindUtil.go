package utils

import "strings"

func Kind2resources(kind string) string {
	// 将字符串转换为小写
	lowercaseKind := strings.ToLower(kind)

	// 简单的复数化处理
	// 注意：这里使用的是非常简单的规则，只适用于规则变化的单词
	// 对于不规则变化的单词，需要更复杂的处理逻辑
	if strings.HasSuffix(lowercaseKind, "s") || strings.HasSuffix(lowercaseKind, "x") || strings.HasSuffix(lowercaseKind, "z") || strings.HasSuffix(lowercaseKind, "ch") || strings.HasSuffix(lowercaseKind, "sh") {
		lowercaseKind += "es"
	} else if strings.HasSuffix(lowercaseKind, "y") && len(lowercaseKind) > 1 && !isVowel(lowercaseKind[len(lowercaseKind)-2]) {
		lowercaseKind = lowercaseKind[:len(lowercaseKind)-1] + "ies"
	} else {
		lowercaseKind += "s"
	}
	return lowercaseKind
}

// 辅助函数：判断字符是否为元音
func isVowel(c byte) bool {
	switch c {
	case 'a', 'e', 'i', 'o', 'u':
		return true
	}
	return false
}
