package downloadquest

import (
	"bytes"
	"compress/gzip"
	"crypto/tls"
	"crypto/x509"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"testing"
	"time"
)

func Test_download(t *testing.T) {
	defaultTransport := http.DefaultTransport.(*http.Transport)
	// same settings as the default transport
	// aside from timeout and TLSClientConfig
	var caPool *x509.CertPool
	httpClient := new(http.Client)
	httpClient.Transport = &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: true, //nolint:gosec
			RootCAs:            caPool,
		},
		IdleConnTimeout:       time.Second,
		DialContext:           defaultTransport.DialContext,
		ForceAttemptHTTP2:     defaultTransport.ForceAttemptHTTP2,
		MaxIdleConns:          defaultTransport.MaxIdleConns,
		Proxy:                 defaultTransport.Proxy,
		TLSHandshakeTimeout:   defaultTransport.TLSHandshakeTimeout,
		ExpectContinueTimeout: defaultTransport.ExpectContinueTimeout,
	}

	httpReq, err := http.NewRequest(http.MethodGet, "http://************:31909/zx-test/backups/backup-test/backup-test.tar.gz?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=minio%2F20231219%2Fminio%2Fs3%2Faws4_request&X-Amz-Date=20231219T023441Z&X-Amz-Expires=600&X-Amz-SignedHeaders=host&X-Amz-Signature=fcd8712d897ffc4f4b32907ea7ade9ef5493c3bc79494f034da431e9de67b3d3", nil)
	if err != nil {
		return
	}

	resp, err := httpClient.Do(httpReq)
	if err != nil {
		if urlErr, ok := err.(*url.Error); ok {
			if _, ok := urlErr.Err.(x509.UnknownAuthorityError); ok {
				return
			}
		}
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return
	}

	reader := resp.Body
	// need to decompress logs
	gzipReader, err := gzip.NewReader(resp.Body)
	if err != nil {
		return
	}
	defer gzipReader.Close()
	reader = gzipReader

	w := new(bytes.Buffer)

	_, err = io.Copy(w, reader)

	size := resp.ContentLength

	fmt.Println("1")

	fmt.Println(strconv.FormatInt(size, 10))
}
