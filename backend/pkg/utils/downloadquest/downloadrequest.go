package downloadquest

import (
	"archive/tar"
	"compress/gzip"
	"context"
	"crypto/tls"
	"crypto/x509"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	uuid2 "github.com/google/uuid"
	veleroV1 "github.com/vmware-tanzu/velero/pkg/apis/velero/v1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/velero"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/klog/v2"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

func Stream(ctx context.Context, clusterClient client.Client, namespace, name string, kind veleroV1.DownloadTargetKind, timeout time.Duration, insecureSkipTLSVerify bool, caCertFile string) (*http.Response, error) {
	uuid, err := uuid2.NewRandom()
	if err != nil {
		return nil, err
	}

	reqName := fmt.Sprintf("%s-%s", name, uuid.String())
	created := &veleroV1.DownloadRequest{
		ObjectMeta: metav1.ObjectMeta{
			Name:      reqName,
			Namespace: namespace,
		},
		Spec: veleroV1.DownloadRequestSpec{
			Target: veleroV1.DownloadTarget{
				Name: name,
				Kind: kind,
			},
		},
	}

	if err := clusterClient.Create(ctx, created); err != nil {
		return nil, err
	}

	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	key := client.ObjectKey{Name: created.Name, Namespace: namespace}
	timeStreamFirstCheck := time.Now()
	downloadURLTimeout := false
	checkFunc := func() {
		if time.Now().After(timeStreamFirstCheck.Add(timeout)) {
			downloadURLTimeout = true
			cancel()
		}
		updated := &veleroV1.DownloadRequest{}
		if err := clusterClient.Get(ctx, key, updated); err != nil {
			return
		}

		if updated.Name != created.Name {
			return
		}

		if updated.Status.DownloadURL != "" {
			created = updated
			cancel()
		}
	}

	wait.Until(checkFunc, 25*time.Millisecond, ctx.Done())
	if downloadURLTimeout {
		return nil, errors.NewFromCode(errors.Var.DownloadURLTimeout)
	}

	var caPool *x509.CertPool
	if len(caCertFile) > 0 {
		caCert, err := os.ReadFile(caCertFile)
		if err != nil {
			return nil, errors.NewFromCode(errors.Var.DownloadURLError)
		}
		// bundle the passed in cert with the system cert pool
		// if it's available, otherwise create a new pool just
		// for this.
		caPool, err = x509.SystemCertPool()
		if err != nil {
			caPool = x509.NewCertPool()
		}
		caPool.AppendCertsFromPEM(caCert)
	}

	defaultTransport := http.DefaultTransport.(*http.Transport)
	// same settings as the default transport
	// aside from timeout and TLSClientConfig
	httpClient := new(http.Client)
	httpClient.Transport = &http.Transport{
		TLSClientConfig: &tls.Config{
			InsecureSkipVerify: insecureSkipTLSVerify, //nolint:gosec
			RootCAs:            caPool,
		},
		IdleConnTimeout:       timeout,
		DialContext:           defaultTransport.DialContext,
		ForceAttemptHTTP2:     defaultTransport.ForceAttemptHTTP2,
		MaxIdleConns:          defaultTransport.MaxIdleConns,
		Proxy:                 defaultTransport.Proxy,
		TLSHandshakeTimeout:   defaultTransport.TLSHandshakeTimeout,
		ExpectContinueTimeout: defaultTransport.ExpectContinueTimeout,
	}

	httpReq, err := http.NewRequest(http.MethodGet, created.Status.DownloadURL, nil)
	if err != nil {
		return nil, err
	}

	resp, err := httpClient.Do(httpReq)
	if err != nil {
		if urlErr, ok := err.(*url.Error); ok {
			if _, ok := urlErr.Err.(x509.UnknownAuthorityError); ok {
				return nil, fmt.Errorf(err.Error() + "\n\nThe --insecure-skip-tls-verify flag can also be used to accept any TLS certificate for the download, but it is susceptible to man-in-the-middle attacks.")
			}
		}
	}

	return resp, nil
}

func GetZipBody(ctx context.Context, clusterClient client.Client, namespace, name string, kind veleroV1.DownloadTargetKind, timeout time.Duration, insecureSkipTLSVerify bool, caCertFile string) ([]byte, error) {
	resp, streamError := Stream(ctx, clusterClient, namespace, name, kind, timeout, insecureSkipTLSVerify, caCertFile)

	if streamError != nil {
		return nil, streamError
	}

	if resp != nil {
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			return nil, errors.NewFromCode(errors.Var.DownloadURLError)
		}

		gzipReader, err := gzip.NewReader(resp.Body)
		if err != nil {
			return nil, err
		}
		defer gzipReader.Close()

		return io.ReadAll(gzipReader)
	} else {
		return nil, nil
	}
}

func GetZipFolderPaths(fileName string) ([]string, error) {
	file, err := os.Open(fileName)
	if err != nil {
		fmt.Println("Failed to open tar.gz file:", err)
		return nil, err
	}
	defer file.Close()

	// 创建 gzip reader
	gzipReader, err := gzip.NewReader(file)
	if err != nil {
		fmt.Println("Failed to create gzip reader:", err)
		return nil, err
	}
	defer gzipReader.Close()

	// 列出 tar 文件中的所有文件路径
	filePaths, err := listFilePathsInTar(gzipReader)
	if err != nil {
		fmt.Printf("Failed to list file paths in tar: %v\n", err)
		return nil, err
	}

	return filePaths, nil
}

func GetDownloadFileData(downloadFileMap map[string][]*velero.DownloadFile, fileName string) error {
	localFile, err := os.Open(fileName)
	if err != nil {
		fmt.Println("Failed to open tar.gz localFile:", err)
		return err
	}
	defer localFile.Close()

	for _, files := range downloadFileMap {
		for _, file := range files {
			content := extractFileFromTar(localFile, file.Path)
			file.Data = content
		}
	}
	return nil
}

func ListResources(ctx context.Context, clusterClient client.Client, namespace, name string, kind veleroV1.DownloadTargetKind) ([]velero.VeleroResources, error) {
	data, getBodyError := GetZipBody(ctx, clusterClient, namespace, name, kind, time.Second, true, "")
	if getBodyError != nil {
		return nil, getBodyError
	}

	var resourceList map[string][]string
	if err := json.Unmarshal(data, &resourceList); err != nil {
		return nil, err
	}

	var ans []velero.VeleroResources

	for k, v := range resourceList {
		for _, s := range v {
			kSplit := strings.Split(k, constants.Slash)
			sSplit := strings.Split(s, constants.Slash)

			if len(kSplit) > 0 && len(sSplit) == 2 {
				ans = append(ans, velero.VeleroResources{
					ResourceType: kSplit[len(kSplit)-1],
					Namespace:    sSplit[0],
					ResourceName: sSplit[1],
				})
			} else if len(kSplit) > 0 && len(sSplit) == 1 {
				ans = append(ans, velero.VeleroResources{
					ResourceType: kSplit[len(kSplit)-1],
					ResourceName: sSplit[0],
				})
			}
		}
	}

	return ans, nil
}

func Logs(ctx context.Context, clusterClient client.Client, namespace, name string, kind veleroV1.DownloadTargetKind) (string, error) {
	data, getBodyError := GetZipBody(ctx, clusterClient, namespace, name, kind, time.Second, true, "")
	if getBodyError != nil {
		return "", getBodyError
	}

	return string(data), nil
}

// 从 tar 归档文件中提取指定路径的文件内容
func extractFileFromTar(file *os.File, targetPath string) string {
	if _, err := file.Seek(0, 0); err != nil {
		fmt.Println("Failed to reset file pointer:", err)
		return ""
	}
	// 创建 gzip reader
	gzipReader, err := gzip.NewReader(file)
	if err != nil {
		fmt.Println("Failed to create gzip reader:", err)
		return ""
	}
	defer gzipReader.Close()
	tr := tar.NewReader(gzipReader)

	for {
		header, err := tr.Next()
		if err == io.EOF {
			break
		}
		if err != nil {
			return ""
		}

		if header.Typeflag == tar.TypeReg && header.Name == targetPath {
			var sb strings.Builder
			if _, err := io.Copy(&sb, tr); err != nil {
				return ""
			}
			return sb.String()
		}
	}

	return ""
}

func listFilePathsInTar(reader io.Reader) ([]string, error) {
	tr := tar.NewReader(reader)
	var filePaths []string

	for {
		header, err := tr.Next()
		if err == io.EOF {
			break
		}
		if err != nil {
			return nil, err
		}

		filePaths = append(filePaths, header.Name)
	}

	return filePaths, nil
}

func SaveFileInLocal(reader io.Reader, backupName string) (string, error) {
	//path := "/usr/local/velero"
	suffix := ".tar.gz"
	id, _ := uuid2.NewRandom()
	fileName := filepath.Join(config.FilePath.Value, backupName+id.String()+suffix)
	// 检查路径是否存在
	if _, err := os.Stat(config.FilePath.Value); os.IsNotExist(err) {
		// 路径不存在，创建路径
		err := os.MkdirAll(config.FilePath.Value, os.ModePerm)
		if err != nil {
			klog.Infof("创建路径失败 path：%s，err：%v", config.FilePath.Value, err)
			return "", err
		}
	}
	outFile, err := os.Create(fileName)
	if err != nil {
		klog.Infof("保存文件失败 file：%s，err：%v", fileName, err)
		return "", err
	}
	defer outFile.Close()

	// 将响应内容写入本地文件
	_, err = io.Copy(outFile, reader)
	if err != nil {
		klog.Infof("保存文件失败 file：%s，err：%v", fileName, err)
		return "", err
	}
	return fileName, nil
}

func RemoveFile(fileName string) error {
	if err := os.Remove(fileName); err != nil {
		fmt.Println("Failed to delete file:", err)
		return err
	}
	return nil
}
