package utils

import k8sV1 "k8s.io/apimachinery/pkg/apis/meta/v1"

func LabelSelectorRequirementConvert2Map(matchExpressions []k8sV1.LabelSelectorRequirement) map[string]string {
	ans := make(map[string]string)

	if len(matchExpressions) != 0 {
		for _, labelSelectorRequirement := range matchExpressions {
			ans[labelSelectorRequirement.Key] = JoinString(labelSelectorRequirement.Values, ",", "")
		}
	}

	return ans
}

func MapConvert2LabelSelectorRequirement(m map[string]string) []k8sV1.LabelSelectorRequirement {
	if m == nil {
		return nil
	}

	ans := make([]k8sV1.LabelSelectorRequirement, 0, len(m))
	for k, v := range m {
		ans = append(ans,
			k8sV1.LabelSelectorRequirement{
				Key:      k,
				Operator: k8sV1.LabelSelectorOpNotIn,
				Values:   []string{v},
			})
	}

	return ans
}
