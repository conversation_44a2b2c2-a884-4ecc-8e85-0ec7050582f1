package rbac

import (
	"context"
	"fmt"
	"strings"

	clientmgr "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	k8sutils "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/k8s"
	authv1 "k8s.io/api/authorization/v1"
	ctrlclient "sigs.k8s.io/controller-runtime/pkg/client"
)

// NewK8sAuthRBACHandler ...
func NewK8sAuthRBACHandler() *K8sAuthRBACHandler {
	return &K8sAuthRBACHandler{}
}

type K8sAuthRBACHandler struct {
}

func (h *K8sAuthRBACHandler) Handler(ctx context.Context, request *AuthRequest) (*AuthResponse, error) {

	review := request.SelfSubjectAccessReview
	if review == nil {
		// 无需鉴权
		return &AuthResponse{Allowed: true}, nil
	}
	userToken := utils.GetUserTokenFromContext(ctx)
	if len(userToken) == 0 {
		return nil, fmt.Errorf("user token is empty")
	}
	if len(review.Cluster) == 0 {
		return nil, fmt.Errorf("cluster is empty")
	}
	if len(review.Resource) == 0 {
		return nil, fmt.Errorf("resource is empty")
	}
	if len(review.Verb) == 0 {
		return nil, fmt.Errorf("verb is empty")
	}
	cluster, err := clientmgr.GetCluster(review.Cluster)
	if err != nil {
		return nil, err
	}
	cmgr := cluster.GetClient()
	cfg, err := k8sutils.DeepCopyRestConfig(cmgr.GetConfig())
	if err != nil {
		return nil, err
	}
	cfg.BearerToken = userToken
	c, err := ctrlclient.New(cfg, ctrlclient.Options{
		Scheme: clientmgr.Scheme,
	})
	if err != nil {
		return nil, err
	}
	accessReview := &authv1.SelfSubjectAccessReview{
		Spec: authv1.SelfSubjectAccessReviewSpec{
			ResourceAttributes: &authv1.ResourceAttributes{
				Namespace: review.Namespace,
				Verb:      review.Verb,
				Group:     review.Group,
				Version:   review.Version,
				Resource:  review.Resource,
			},
		},
	}
	if err := c.Create(ctx, accessReview); err != nil {
		return nil, err
	}
	var msgBuilder strings.Builder
	if accessReview.Status.Denied || !accessReview.Status.Allowed {
		msgBuilder.WriteString(fmt.Sprintf("the [ %s ] operation for resource [ %s ] is denied",
			review.Verb, review.Group+"/"+review.Version+", Resource: "+review.Resource))
		if accessReview.Namespace != "" {
			msgBuilder.WriteString(fmt.Sprintf(" in namespace [ %s ]", accessReview.Namespace))
		}
		if accessReview.Status.Reason != "" {
			msgBuilder.WriteString(fmt.Sprintf(", reason: [ %s ]", accessReview.Status.Reason))
		}
	}
	return &AuthResponse{
		Allowed: accessReview.Status.Allowed,
		Denied:  accessReview.Status.Denied,
		Reason:  msgBuilder.String(),
	}, nil
}
