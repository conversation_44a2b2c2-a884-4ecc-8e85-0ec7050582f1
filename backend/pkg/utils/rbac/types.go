package rbac

const (
	// read
	VerbList string = "list"
	VerbGet  string = "get"
	// write
	VerbCreate           string = "create"
	VerbUpdate           string = "update"
	VerbPatch            string = "patch"
	VerbDelete           string = "delete"
	VerbDeleteCollection string = "deletecollection"
	VerbWatch            string = "watch"
)

type AuthRequest struct {
	// SelfSubjectAccessReview kubernetes 查看是否有对应权限
	SelfSubjectAccessReview *SelfSubjectAccessReviewRequest
}

type SelfSubjectAccessReviewRequest struct {
	// Token user token create client
	Token string
	// Cluster 集群
	Cluster string
	// Namespace 命名空间
	Namespace string
	// Group 组
	Group string
	// Version 组
	Version string
	// Resource 资源
	Resource string
	// Verb 操作 如 get list create update patch delete
	Verb string
}

type AuthResponse struct {
	// Allowed 是否允许
	Allowed bool `json:"allowed"`
	// Denied 是否禁止
	Denied bool `json:"denied"`
	// Reason 原因
	Reason string `json:"reason"`
}
