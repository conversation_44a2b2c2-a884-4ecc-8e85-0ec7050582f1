package rbac

import (
	"context"
	"encoding/json"
	stellarisv1alhpha1 "harmonycloud.cn/stellaris/pkg/apis/stellaris/v1alpha1"
	hcclient "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"strings"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
)

type AuthHandler interface {
	Handler(ctx context.Context, request *AuthRequest) (*AuthResponse, error)
}

var k8sAuthHandlers = []AuthHandler{
	NewK8sAuthRBACHandler(),
}

// AuthK8sRBAC 验证 k8s 权限
func AuthK8sRBAC(ctx context.Context, request *AuthRequest) error {
	// 自有集群判断 如果为自有集群 则直接走admin的token
	if request != nil &&
		request.SelfSubjectAccessReview != nil &&
		request.SelfSubjectAccessReview.Cluster != "" {
		cluster := request.SelfSubjectAccessReview.Cluster
		var stc stellarisv1alhpha1.Cluster
		if err := hcclient.GetLocalCluster().GetClient().GetCtrlClient().Get(ctx, client.ObjectKey{Name: cluster}, &stc); err != nil {
			return err
		}
		if stc.GetLabels() != nil {
			value, exist := stc.GetLabels()[constants.ClusterLabelKeyOfSelfCluster]
			if exist && strings.TrimSpace(value) == strings.TrimSpace(constants.ClusterLabelValueOfSelfCluster) {
				return nil
			}
		}
	}
	for _, h := range k8sAuthHandlers {
		response, err := h.Handler(ctx, request)
		data, _ := json.Marshal(response)
		if err != nil {
			logger.GetSugared().Debugf("k8s auth handler result: %v error: %v", string(data), err)
			return errors.NewFromCodeWithMessage(errors.Var.K8sForbidden, err.Error())
		}
		if response.Denied || !response.Allowed {
			logger.GetSugared().Debugf("k8s auth handler result: %v error: %v", string(data), err)
			return errors.NewFromCodeWithMessage(errors.Var.K8sForbidden, response.Reason)
		}
	}
	return nil
}
