package sisyphusutil

import (
	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

func NodeAuthRequestToNodeInitialParam(nodeAuthRequest *clustermodel.NodeAuthRequest, param *installerv1alpha1.SisyphusNodeInitialParam) error {
	if nodeAuthRequest == nil {
		return errors.NewFromCodeWithMessage(errors.Var.UnKnowAuthType, "auth type is nil")
	}
	nodeAuthRequest.AuthType = clustermodel.UserNameAndPasswordAuthType
	switch nodeAuthRequest.AuthType {
	case clustermodel.UserNameAndPasswordAuthType:
		var authInfo clustermodel.NodeAuthUsernameAndPasswordParamRequest
		if err := utils.BeanCopy(nodeAuthRequest.Param, &authInfo); err != nil {
			return err
		}
		if err := authInfo.Validate(); err != nil {
			return err
		}
		param.Username = authInfo.Username
		param.Password = authInfo.Password
		param.SudoPassword = authInfo.AuthorizationPassword
	default:
		return errors.NewFromCodeWithMessage(errors.Var.UnKnowAuthType, string(nodeAuthRequest.AuthType))
	}
	return nil
}
