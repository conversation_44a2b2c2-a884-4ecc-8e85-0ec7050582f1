package sisyphusutil

import (
	"context"
	"fmt"
	"strings"
	"time"

	"harmonycloud.cn/unifiedportal/cloudservice-operator/pkg/handler/installer/client"
	hcclient "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	addonmodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/addon"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/sisyphustype"
	v1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	ctrlclient "sigs.k8s.io/controller-runtime/pkg/client"
)

func GetEditContext(sisyphusclient client.Sisyphus, config addonmodel.SisyphusAddressConfig) (sisyphustype.EditDeployContext, error) {
	// declare variables
	var ctx context.Context
	var timeoutSeconds = 10 * time.Second
	var contextResponse *client.SisyphusResponse[client.ContextResponse]
	var prepareVersionResponse *client.SisyphusResponse[client.PrepareVersionResponse]
	var result sisyphustype.EditDeployContext
	var err error
	// exec to sisyphus api for get media manage config
	ctx, _ = context.WithTimeout(context.Background(), timeoutSeconds)
	if contextResponse, err = sisyphusclient.Context(ctx, client.WithURL(config.Address)); err != nil {
		return result, err
	}

	ctx, _ = context.WithTimeout(context.Background(), timeoutSeconds)
	if prepareVersionResponse, err = sisyphusclient.PrepareVersions(ctx, client.WithURL(config.Address)); err != nil {
		return result, err
	}
	result.SisyphusComponentVersion = prepareVersionResponse.Data.DefaultVersion
	result.ImageRegistryAddr = contextResponse.Data.ImageRegistryAddr
	result.ImageRegistryUsername = contextResponse.Data.ImageRegistryUsername
	result.ImageRegistryPassword = contextResponse.Data.ImageRegistryPassword
	result.PypiAddr = contextResponse.Data.PypiAddr
	result.PypiUsername = contextResponse.Data.PypiUsername
	result.PypiPassword = contextResponse.Data.PypiPassword
	result.HelmRepoAddr = contextResponse.Data.HelmRepoAddr
	result.HelmRepoUsername = contextResponse.Data.HelmRepoUsername
	result.HelmRepoPassword = contextResponse.Data.HelmRepoPassword
	result.LinuxPackageAddr = contextResponse.Data.LinuxPackageAddr
	result.LinuxPackageUsername = contextResponse.Data.LinuxPackageUsername
	result.LinuxPackagePassword = contextResponse.Data.LinuxPackagePassword
	return result, nil
}

func GetSecretToken() ([]byte, error) {
	// 查找token secret
	secret := v1.Secret{}
	if err := hcclient.GetLocalCluster().GetClient().GetCtrlClient().Get(context.Background(), ctrlclient.ObjectKey{Namespace: "stellaris-system", Name: "stellaris-core-register-token"}, &secret); err != nil {
		if k8serrors.IsNotFound(err) {
			err = errors.NewFromCodeWithMessage(errors.Var.StellariesSecretLost, fmt.Sprintf("%s/%s", "stellaris-system", "stellaris-core-register-token"))
		}
		return nil, err
	}
	var bytes []byte
	var exist bool
	if secret.Data != nil {
		bytes, exist = secret.Data["token"]
	}
	if !exist {
		return nil, errors.NewFromCodeWithMessage(errors.Var.StellariesSecretKeyLost, fmt.Sprintf("%s/%s,key=%s", "stellaris-system", "stellaris-core-register-token", "token"))
	}
	return bytes, nil
}

const (
	// 系统数据盘
	DiskNameSystem   = "系统组件VG"
	DiskVGNameSystem = "caas-lvm"

	// ETCD
	DiskNameETCD   = "ETCD数据盘"
	DiskVGNameETCD = "etcd"
	DISKLVNameETCD = "etcd-data"

	// KubeletCRI
	DiskNameKubeletCRI           = "容器编排系统及容器运行时数据盘"
	DiskNameKubeletCRI_Kubelet   = "kubelet-runtime"
	DISKLVNameKubeletCRI_Kubelet = "kubelet"
	DiskNameKubeletCRI_CRI       = "container-runtime"
	DISKLVNameKubeletCRI_CRI     = "container-runtime"
)

func GetNodeDisks(nodeInfos map[string]cluster.NodeStorageRequest) ([]sisyphustype.EditDeployDisk, error) {
	var result []sisyphustype.EditDeployDisk
	var systemDataDeviceMap, etcdDeviceMap, kubeletDeviceMap, criDeviceMap = make(map[string]string), make(map[string]string), make(map[string]string), make(map[string]string)
	for nodeIp, storageRequest := range nodeInfos {
		if storageRequest.Type != cluster.NodeStorageTypeAuto || storageRequest.DiskPath == nil {
			continue
		}
		// 系统组件vg
		if !strings.EqualFold(storageRequest.DiskPath.System, "") {
			systemDataDeviceMap[nodeIp] = storageRequest.DiskPath.System
		}

		// etcd
		if !strings.EqualFold(storageRequest.DiskPath.ETCD, "") {
			etcdDeviceMap[nodeIp] = storageRequest.DiskPath.ETCD
		}

		// kubelet
		if !strings.EqualFold(storageRequest.DiskPath.Kubelet, "") {
			kubeletDeviceMap[nodeIp] = storageRequest.DiskPath.Kubelet
		}

		// cri
		if !strings.EqualFold(storageRequest.DiskPath.Docker, "") {
			criDeviceMap[nodeIp] = storageRequest.DiskPath.Docker
		}

	}

	// 处理系统组件VG
	if len(systemDataDeviceMap) != 0 {
		result = append(result, sisyphustype.EditDeployDisk{
			Name: DiskNameSystem,
			VGs: []sisyphustype.EditDeployVG{
				{
					VGName:  DiskVGNameSystem,
					Devices: systemDataDeviceMap,
				},
			},
		})
	}

	// 处理ETCD
	if len(etcdDeviceMap) != 0 {
		result = append(result, sisyphustype.EditDeployDisk{
			Name: DiskNameETCD,
			VGs: []sisyphustype.EditDeployVG{
				{
					VGName:  DiskVGNameETCD,
					Devices: etcdDeviceMap,
					LVs: []sisyphustype.EditDeployLV{
						{
							LVName:  DISKLVNameETCD,
							Percent: mapKeyWithStatusValue1(etcdDeviceMap),
						},
					},
				},
			},
		})
	}
	// 处理容器编排及容器运行时
	if len(kubeletDeviceMap) != 0 || len(criDeviceMap) != 0 {
		disk := sisyphustype.EditDeployDisk{
			Name: DiskNameKubeletCRI,
		}
		if len(kubeletDeviceMap) != 0 {
			disk.VGs = append(disk.VGs, sisyphustype.EditDeployVG{
				VGName:  DiskNameKubeletCRI_Kubelet,
				Devices: kubeletDeviceMap,
				LVs: []sisyphustype.EditDeployLV{
					{
						LVName:  DISKLVNameKubeletCRI_Kubelet,
						Percent: mapKeyWithStatusValue1(kubeletDeviceMap),
					},
				},
			})
		}
		if len(criDeviceMap) != 0 {
			disk.VGs = append(disk.VGs, sisyphustype.EditDeployVG{
				VGName:  DiskNameKubeletCRI_CRI,
				Devices: criDeviceMap,
				LVs: []sisyphustype.EditDeployLV{
					{
						LVName:  DISKLVNameKubeletCRI_CRI,
						Percent: mapKeyWithStatusValue1(criDeviceMap),
					},
				},
			})
		}
		result = append(result, disk)
	}
	return result, nil

}

func ReverseNodeDisks(disks []sisyphustype.EditDeployDisk) map[string]*cluster.NodeStorageResponse {

	var system, etcd, kubelet, cri map[string]string
	var result = make(map[string]*cluster.NodeStorageResponse)
	for _, disk := range disks {
		// 处理系统组件VG
		if disk.Name == DiskNameSystem {
			for _, vg := range disk.VGs {
				if vg.VGName == DiskVGNameSystem {
					system = vg.Devices
				}
			}
		}
		// ETCD数据盘
		if disk.Name == DiskNameETCD {
			for _, vg := range disk.VGs {
				if vg.VGName == DiskVGNameETCD {
					etcd = vg.Devices
				}
			}
		}

		// 容器编排系统及容器运行时数据盘
		if disk.Name == DiskNameKubeletCRI {
			for _, vg := range disk.VGs {
				if vg.VGName == DiskNameKubeletCRI_Kubelet {
					kubelet = vg.Devices
				}
				if vg.VGName == DiskNameKubeletCRI_CRI {
					cri = vg.Devices
				}
			}
		}

	}
	initResultFunc := func(result map[string]*cluster.NodeStorageResponse, ip string) {
		_, exist := result[ip]
		if !exist {
			result[ip] = new(cluster.NodeStorageResponse)
			result[ip].Type = cluster.NodeStorageTypeAuto
			result[ip].DiskPath = new(cluster.NodeDiskPathResponse)
		}

	}

	if len(system) != 0 {
		for ip, vol := range system {
			initResultFunc(result, ip)
			result[ip].DiskPath.System = vol
		}
	}

	if len(etcd) != 0 {
		for ip, vol := range etcd {
			initResultFunc(result, ip)
			result[ip].DiskPath.ETCD = vol
		}
	}

	if len(kubelet) != 0 {
		for ip, vol := range kubelet {
			initResultFunc(result, ip)
			result[ip].DiskPath.Kubelet = vol
		}
	}

	if len(cri) != 0 {
		for ip, vol := range cri {
			initResultFunc(result, ip)
			result[ip].DiskPath.Docker = vol
		}
	}
	return result

}

func mapKeyWithStatusValue1(req map[string]string) map[string]int {
	var result = make(map[string]int)
	for k, _ := range req {
		result[k] = 100
	}
	return result
}
