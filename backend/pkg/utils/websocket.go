package utils

import (
	"context"
	"net/http"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
)

const (
	secWebSocketProtocolHeader = "Sec-WebSocket-Protocol"
)

func WebSocketProxy(ctx *gin.Context, follow bool, logBytesChan <-chan []byte, logContext context.Context, logCancelFunc context.CancelFunc) {
	if follow {
		// 开启websocket
		responseHeader := http.Header{}
		responseHeader.Set("Access-Control-Allow-Origin", "*")
		if v := ctx.GetHeader(secWebSocketProtocolHeader); v != "" {
			responseHeader.Set(secWebSocketProtocolHeader, v)
		}
		var upgrader = websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true
			},
		}
		conn, err := upgrader.Upgrade(ctx.Writer, ctx.Request, responseHeader)
		if err != nil {
			Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ConnectFailure, "建立查看pod日志长连接失败"))
			return
		}
		defer conn.Close()
		for {
			select {
			case <-ctx.Done():
				return
			case <-logContext.Done():
				// web socket closed
				return
			case logBytes := <-logBytesChan:
				// log input
				if err := conn.WriteMessage(websocket.TextMessage, logBytes); err != nil {
					return
				}
			}
		}
	} else {
		// 读取日志返回
		select {
		case logBytes := <-logBytesChan:
			Succeed(ctx, string(logBytes))
		case <-logContext.Done():
			Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.UnKnow, "context done"))
		}
	}
}
