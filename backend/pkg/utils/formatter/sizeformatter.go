package formatter

import (
	"encoding/json"
	"fmt"
)

// FormattedSize 自动格式化存储大小，输出适合的单位（B, KB, MB, GB, TB）
type FormattedSize uint64

// String 实现自定义的字符串格式化，输出符合人类阅读习惯的存储大小
func (fs FormattedSize) String() string {
	size := uint64(fs)
	var result string

	switch {
	case size >= 1<<60:
		result = fmt.Sprintf("%.2f EiB", float64(size)/(1<<60))
	case size >= 1<<50:
		result = fmt.Sprintf("%.2f PiB", float64(size)/(1<<50))
	case size >= 1<<40:
		result = fmt.Sprintf("%.2f TiB", float64(size)/(1<<40))
	case size >= 1<<30:
		result = fmt.Sprintf("%.2f GiB", float64(size)/(1<<30))
	case size >= 1<<20:
		result = fmt.Sprintf("%.2f MiB", float64(size)/(1<<20))
	case size >= 1<<10:
		result = fmt.Sprintf("%.2f KiB", float64(size)/(1<<10))
	default:
		result = fmt.Sprintf("%d B", size)
	}
	return result
}

// MarshalJSON 实现自定义的 JSON 序列化，输出符合人类阅读习惯的存储大小
func (fs FormattedSize) MarshalJSON() ([]byte, error) {
	size := uint64(fs)
	var result string

	switch {
	case size >= 1<<60:
		result = fmt.Sprintf("%.2f EiB", float64(size)/(1<<60))
	case size >= 1<<50:
		result = fmt.Sprintf("%.2f PiB", float64(size)/(1<<50))
	case size >= 1<<40:
		result = fmt.Sprintf("%.2f TiB", float64(size)/(1<<40))
	case size >= 1<<30:
		result = fmt.Sprintf("%.2f GiB", float64(size)/(1<<30))
	case size >= 1<<20:
		result = fmt.Sprintf("%.2f MiB", float64(size)/(1<<20))
	case size >= 1<<10:
		result = fmt.Sprintf("%.2f KiB", float64(size)/(1<<10))
	default:
		result = fmt.Sprintf("%d B", size)
	}

	return json.Marshal(result)
}

// UnmarshalJSON 实现自定义的 JSON 反序列化
func (fs *FormattedSize) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}

	var size float64
	var unit string

	if s == "0 B" || s == "" {
		*fs = 0
		return nil
	}

	n, err := fmt.Sscanf(s, "%f %s", &size, &unit)
	if err != nil || n != 2 {
		return fmt.Errorf("无法解析大小字符串: %s", s)
	}

	var multiplier uint64 = 1
	switch unit {
	case "B":
		// 保持 multiplier = 1
	case "KiB":
		multiplier = 1 << 10
	case "MiB":
		multiplier = 1 << 20
	case "GiB":
		multiplier = 1 << 30
	case "TiB":
		multiplier = 1 << 40
	case "PiB":
		multiplier = 1 << 50
	case "EiB":
		multiplier = 1 << 60
	default:
		return fmt.Errorf("未知的大小单位: %s", unit)
	}

	*fs = FormattedSize(uint64(size * float64(multiplier)))
	return nil
}

type FormattedGiBSize uint64

// String 实现自定义的字符串格式化，输出符合人类阅读习惯的 GiB 存储大小
func (fs FormattedGiBSize) String() string {
	return fmt.Sprintf("%d GiB", fs/(1<<30))
}

// MarshalJSON 实现自定义的 JSON 序列化
func (fs FormattedGiBSize) MarshalJSON() ([]byte, error) {
	return json.Marshal(fs.String())
}

// UnmarshalJSON 实现自定义的 JSON 反序列化
func (fs *FormattedGiBSize) UnmarshalJSON(data []byte) error {
	var s string
	if err := json.Unmarshal(data, &s); err != nil {
		return err
	}

	var size uint64
	_, err := fmt.Sscanf(s, "%d GiB", &size)
	if err != nil {
		return err
	}

	*fs = FormattedGiBSize(size * (1 << 30))
	return nil
}
