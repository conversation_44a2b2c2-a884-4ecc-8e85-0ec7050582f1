package formatter

import (
	"encoding/json"
	"fmt"
	"reflect"
	"testing"
)

func TestFormattedSize_MarshalJSON(t *testing.T) {
	tests := []struct {
		name    string
		fs      FormattedSize
		want    []byte
		wantErr bool
	}{
		{
			name:    "Bytes",
			fs:      512,
			want:    []byte(`"512 B"`),
			wantErr: false,
		},
		{
			name:    "Kilobytes",
			fs:      2048,
			want:    []byte(`"2.00 KiB"`),
			wantErr: false,
		},
		{
			name:    "Megabytes",
			fs:      5 * 1024 * 1024,
			want:    []byte(`"5.00 MiB"`),
			wantErr: false,
		},
		{
			name:    "Gigabytes",
			fs:      3 * 1024 * 1024 * 1024,
			want:    []byte(`"3.00 GiB"`),
			wantErr: false,
		},
		{
			name:    "Terabytes",
			fs:      3 * 1024 * 1024 * 1024 * 1024,
			want:    []byte(`"3.00 TiB"`),
			wantErr: false,
		},
		{
			name:    "Petabytes",
			fs:      3 * 1024 * 1024 * 1024 * 1024 * 1024,
			want:    []byte(`"3.00 PiB"`),
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := json.Marshal(tt.fs)
			if (err != nil) != tt.wantErr {
				t.Errorf("MarshalJSON() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("MarshalJSON() got = %s, want %s", got, tt.want)
			}
		})
	}
}

func TestFormattedSize_String(t *testing.T) {
	tests := []struct {
		name string
		fs   FormattedSize
		want string
	}{
		{
			name: "Bytes",
			fs:   512,
			want: "512 B",
		},
		{
			name: "Kilobytes",
			fs:   2048,
			want: "2.00 KiB",
		},
		{
			name: "Megabytes",
			fs:   5 * 1024 * 1024,
			want: "5.00 MiB",
		},
		{
			name: "Gigabytes",
			fs:   3 * 1024 * 1024 * 1024,
			want: "3.00 GiB",
		},
		{
			name: "Terabytes",
			fs:   3 * 1024 * 1024 * 1024 * 1024,
			want: "3.00 TiB",
		},
		{
			name: "Petabytes",
			fs:   3 * 1024 * 1024 * 1024 * 1024 * 1024,
			want: "3.00 PiB",
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if got := fmt.Sprintf("%s", tt.fs); got != tt.want {
				t.Errorf("String() = %v, want %v", got, tt.want)
			}
		})
	}
}
