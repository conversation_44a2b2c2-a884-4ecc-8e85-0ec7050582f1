package midware

import (
	gocontext "context"
	"encoding/base64"
	"fmt"
	"net"
	"net/http"
	"net/http/httputil"
	"os"
	"runtime/debug"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/pkg/errors"
	"go.uber.org/zap"
	"harmonycloud.cn/unifiedportal/midware-go/configs"
	"harmonycloud.cn/unifiedportal/midware-go/midwares/audit"
	"harmonycloud.cn/unifiedportal/midware-go/midwares/auth"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	bizerrors "harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/translate-sdk-golang/context"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
)

type Key string

const (
	// UserTokenContextKey ...
	UserTokenContextKey string = "user-token"
	// AppCodeContextKey ...
	AppCodeContextKey string = "amp-app-code"
	// AppIdContextKey ...
	AppIdContextKey string = "amp-app-id"
	// OrganIdContextKey 组织ID
	OrganIdContextKey string = "organ-id"
	// ProjectIdContextKey 项目ID
	ProjectIdContextKey string = "project-id"
)

var list = []func() gin.HandlerFunc{
	RecoveryHandler,
	LanguageCodeHandler,
	TokenValidHandler,
	TokenSetHandler,
	AuditHandler,
	PlatformInfoSetHandler,
}

func AddMidWareHandler(engine *gin.Engine) {
	for _, item := range list {
		engine.Use(item())
	}
}

// LanguageCodeHandler
// 处理语言code
func LanguageCodeHandler() gin.HandlerFunc {
	return context.GinTranslateContextHandler
}

func PlatformInfoSetHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		appCode := c.GetHeader(constants.HeaderAppCodeKey)
		appId := c.GetHeader(constants.HeaderAppIdKey)
		organId := c.GetHeader(constants.HeaderOrganizationIdKey)
		projectId := c.GetHeader(constants.HeaderProjectIdKey)
		c.Set(AppCodeContextKey, appCode)
		c.Set(AppIdContextKey, appId)
		c.Set(OrganIdContextKey, organId)
		c.Set(ProjectIdContextKey, projectId)
		ctx := gocontext.WithValue(c.Request.Context(), Key(AppCodeContextKey), appCode)
		ctx = gocontext.WithValue(ctx, Key(AppIdContextKey), appId)
		ctx = gocontext.WithValue(ctx, Key(OrganIdContextKey), organId)
		ctx = gocontext.WithValue(ctx, Key(ProjectIdContextKey), projectId)
		c.Request = c.Request.WithContext(ctx)
		c.Next()
	}

}

// TokenSetHandler ...
func TokenSetHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		token := c.GetHeader("Authorization")
		c.Set(UserTokenContextKey, token)
		ctx := gocontext.WithValue(c.Request.Context(), Key(UserTokenContextKey), token)
		c.Request = c.Request.WithContext(ctx)
		c.Next()
	}
}

// TokenValidHandler
// 处理当前用户
func TokenValidHandler() gin.HandlerFunc {
	base64Encode := base64.RawURLEncoding
	key, err := base64Encode.DecodeString(config.JwtSecret.Value)
	if err != nil {
		logger.GetSugared().Fatalf("[TokenValidHandler] base64 decode jwt secret key error,value is '%s',error is '%s'", config.JwtSecret.Value, err)
	}
	configs.SetJwtSecretValue(key)
	return auth.GinTokenValidHandler
}

// AuditHandler
// 处理操作审记
func AuditHandler() gin.HandlerFunc {
	audit.SetDevopsAmpURL(config.DevopsAmpURL.Value)
	audit.SetComponentName(config.CloudServiceName.Value)
	return audit.WebParamHandlerFunc
}

// RecoveryHandler
// 恢复异常
// stack 是否打印堆栈信息
func RecoveryHandler() gin.HandlerFunc {
	return func(c *gin.Context) {
		defer func() {
			//打印堆栈信息
			var bizErr error
			if err := recover(); err != nil {

				var brokenPipe bool
				if ne, ok := err.(*net.OpError); ok {
					var se *os.SyscallError
					if errors.As(ne.Err, &se) {
						if strings.Contains(strings.ToLower(se.Error()), "broken pipe") || strings.Contains(strings.ToLower(se.Error()), "connection reset by peer") {
							brokenPipe = true
						}
					}
					bizErr = err.(error)
				} else if e, ok := err.(error); ok && e != nil {
					bizErr = e
				}

				httpRequest, _ := httputil.DumpRequest(c.Request, false)
				if brokenPipe {
					logger.GetSugared().Error(c.Request.URL.Path,
						zap.Any("error", err),
						zap.String("request", string(httpRequest)),
					)
					_ = c.Error(err.(error))
					failed(c, fmt.Errorf("%v", err))
					c.Abort()
					return
				}
				if bizErr != nil {
					logger.GetSugared().Error("[Recovery from panic]",
						zap.Any("error", bizErr),
						zap.String("request", string(httpRequest)),
						zap.String("stack", string(debug.Stack())))
					failed(c, bizErr)
				} else {
					logger.GetSugared().Error("[Recovery from panic]",
						zap.Any("error", err),
						zap.String("request", string(httpRequest)),
						zap.String("stack", string(debug.Stack())))
					failed(c, fmt.Errorf("%v", err))
				}
				c.Abort()
				return
			}
		}()
		c.Next()
	}
}

// failed ...
func failed(c *gin.Context, err error) {
	bizErr := bizerrors.NewFromError(c, err)
	resp := struct {
		Success bool   `json:"success"`
		Code    int    `json:"code"`
		Message string `json:"errorMsg"`
		Detail  string `json:"errorDetail"`
	}{
		Success: false,
		Code:    bizErr.ResponseCode,
		Message: bizErr.Message,
		Detail:  err.Error(),
	}
	audit.WithResponseData(c, false, resp)
	c.JSON(http.StatusInternalServerError, resp)

}
