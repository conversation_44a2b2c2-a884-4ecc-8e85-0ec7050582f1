package baseline_master

import "context"

type Service interface {
	// GetReportNow from infra from baseline master
	GetReportNow(ctx context.Context, req *GetReportNowRequest) (*GetReportNowResponse, error)
	// GetReport from infra baseline master
	GetReport(ctx context.Context, req *GetReportRequest) (*GetReportResponse, error)
	// GetCheckersReport from infra baseline master
	GetCheckersReport(ctx context.Context, req *GetCheckersReportRequest) (*GetCheckersReportResponse, error)
}
