package baseline_master

import (
	"context"
	"sync"
)

// FakeService 实现 Service 接口用于测试
type FakeService struct {
	sync.Mutex
	// 存储调用记录
	GetReportCalls     []GetReportRequest
	GetReportResponses map[string]*GetReportResponse
	GetReportErrors    map[string]error

	CheckersReportCalls []GetCheckersReportRequest
	CheckersResponses   map[string]*GetCheckersReportResponse
	CheckersErrors      map[string]error
}

// NewFakeService 创建一个新的 FakeService
func NewFakeService() *FakeService {
	return &FakeService{
		GetReportResponses: make(map[string]*GetReportResponse),
		GetReportErrors:    make(map[string]error),
		CheckersResponses:  make(map[string]*GetCheckersReportResponse),
		CheckersErrors:     make(map[string]error),
	}
}

// SetGetReportResponse 设置 GetReport 的返回值
func (f *FakeService) SetGetReportResponse(baselineName string, response *GetReportResponse, err error) {
	f.Lock()
	defer f.Unlock()
	f.GetReportResponses[baselineName] = response
	f.GetReportErrors[baselineName] = err
}

// SetGetCheckersReportResponse 设置 GetCheckersReport 的返回值
func (f *FakeService) SetGetCheckersReportResponse(checkerName string, response *GetCheckersReportResponse, err error) {
	f.Lock()
	defer f.Unlock()
	f.CheckersResponses[checkerName] = response
	f.CheckersErrors[checkerName] = err
}

// GetReport 实现 Service 接口
func (f *FakeService) GetReport(ctx context.Context, req *GetReportRequest) (*GetReportResponse, error) {
	f.Lock()
	defer f.Unlock()
	f.GetReportCalls = append(f.GetReportCalls, *req)
	return f.GetReportResponses[req.BaselineName], f.GetReportErrors[req.BaselineName]
}

// GetCheckersReport 实现 Service 接口
func (f *FakeService) GetCheckersReport(ctx context.Context, req *GetCheckersReportRequest) (*GetCheckersReportResponse, error) {
	f.Lock()
	defer f.Unlock()
	f.CheckersReportCalls = append(f.CheckersReportCalls, *req)
	return f.CheckersResponses[req.CheckerNames[0]], f.CheckersErrors[req.CheckerNames[0]]
}

// GetReportNow 实现 Service 接口
func (f *FakeService) GetReportNow(ctx context.Context, req *GetReportNowRequest) (*GetReportNowResponse, error) {
	return nil, nil
}
