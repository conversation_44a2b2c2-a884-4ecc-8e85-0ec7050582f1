package baseline_master

import (
	"bytes"
	"context"
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"time"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
	"k8s.io/klog/v2"
)

// Option 定义配置选项函数类型
type Option func(*InfraService)

// WithBaseURL 设置基础URL
func WithBaseURL(url string) Option {
	return func(s *InfraService) {
		s.baseURL = url
	}
}

// WithTimeout 设置超时时间
func WithTimeout(timeout time.Duration) Option {
	return func(s *InfraService) {
		s.httpClient.Timeout = timeout
	}
}

// NewService creates a new InfraService with optional configurations
func NewService(opts ...Option) Service {
	// 默认配置
	du := 30 * time.Second
	if dt, err := time.ParseDuration(os.Getenv("BASELINE_MASTER_SERVICE_TIMEOUT")); err != nil {
		klog.V(2).ErrorS(err, "failed to parse BASELINE_MASTER_SERVICE_TIMEOUT")
	} else {
		du = dt
	}

	s := &InfraService{
		baseURL: config.BaselineCheckerServerURL.Value,
		httpClient: &http.Client{
			Timeout: du,
			Transport: &http.Transport{
				TLSClientConfig: &tls.Config{
					InsecureSkipVerify: true,
				},
			},
		},
	}

	// 应用可选配置
	for _, opt := range opts {
		opt(s)
	}

	return s
}

type InfraService struct {
	baseURL    string
	httpClient *http.Client
}

// GetReportNow ...
func (i *InfraService) GetReportNow(ctx context.Context, req *GetReportNowRequest) (*GetReportNowResponse, error) {
	//reqBytes, err := json.Marshal(req)
	//if err != nil {
	//	return nil, err
	//}
	reqBytes := []byte(req.BaselineName)
	httpReq, err := http.NewRequestWithContext(ctx, http.MethodPost, i.baseURL+"/report", io.NopCloser(bytes.NewBuffer(reqBytes)))
	if err != nil {
		return nil, err
	}
	httpReq.Header.Set("Content-Type", "application/json")

	httpResp, err := i.httpClient.Do(httpReq)
	if err != nil {
		return nil, err
	}

	if httpResp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to get report, status code: %d", httpResp.StatusCode)
	}
	bodyBytes, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, err
	}
	var resp GetReportNowResponse
	if err := json.Unmarshal(bodyBytes, &resp); err != nil {
		return nil, err
	}
	if resp.Data == nil {
		return nil, fmt.Errorf("failed get report data is empty, req: %s", reqBytes)
	}
	return &resp, nil
}

// GetReport implements Service.
func (i *InfraService) GetReport(ctx context.Context, req *GetReportRequest) (*GetReportResponse, error) {
	reqBytes, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	httpReq, err := http.NewRequestWithContext(ctx, http.MethodPost, i.baseURL+"/report", io.NopCloser(bytes.NewBuffer(reqBytes)))
	if err != nil {
		return nil, err
	}
	httpReq.Header.Set("Content-Type", "application/json")

	httpResp, err := i.httpClient.Do(httpReq)
	if err != nil {
		return nil, err
	}

	if httpResp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to get report, status code: %d", httpResp.StatusCode)
	}

	bodyBytes, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, err
	}
	var resp GetReportResponse
	if err := json.Unmarshal(bodyBytes, &resp); err != nil {
		return nil, err
	}
	if resp.Code != 0 {
		return nil, fmt.Errorf("failed get report data is empty, baseline master service return err: %s", resp.Message)
	}

	return &resp, nil
}

// GetCheckersReport from infra baseline master
func (i *InfraService) GetCheckersReport(ctx context.Context, req *GetCheckersReportRequest) (*GetCheckersReportResponse, error) {
	reqBytes, err := json.Marshal(req)
	if err != nil {
		return nil, err
	}
	httpReq, err := http.NewRequestWithContext(ctx, http.MethodPost, i.baseURL+"/report_checkers", io.NopCloser(bytes.NewBuffer(reqBytes)))
	if err != nil {
		return nil, err
	}
	httpReq.Header.Set("Content-Type", "application/json")

	httpResp, err := i.httpClient.Do(httpReq)
	if err != nil {
		return nil, err
	}

	if httpResp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to get checkers report, status code: %d", httpResp.StatusCode)
	}

	bodyBytes, err := io.ReadAll(httpResp.Body)
	if err != nil {
		return nil, err
	}
	var resp GetCheckersReportResponse
	if err := json.Unmarshal(bodyBytes, &resp); err != nil {
		return nil, err
	}
	if resp.Data == nil {
		return nil, fmt.Errorf("failed get report data is empty, req: %s", reqBytes)
	}

	return &resp, nil
}
