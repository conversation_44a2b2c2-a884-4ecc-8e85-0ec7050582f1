package baseline_master

import (
	"encoding/json"
	"time"

	"harmonycloud.cn/baseline-checker/pkg/models/checker"
)

type Response[T any] struct {
	Message string `json:"Message"`
	Code    int    `json:"Code"`
	Data    T      `json:"Report"`
}

// NewGetReportNowRequest ...
func NewGetReportNowRequest(baselineName string) *GetReportNowRequest {
	return &GetReportNowRequest{BaselineName: baselineName}
}

type GetReportNowRequest struct {
	// BaselineName 基线名称
	BaselineName string `json:"baselineName"`
}

func (r *GetReportNowRequest) MarshalJSON() ([]byte, error) {
	return []byte(r.BaselineName), nil
}

type GetReportNowResponse Response[[]*checker.Report]

// NewGetReportRequest ...
func NewGetReportRequest(baselineName string) *GetReportRequest {
	var startTime, endTime time.Time
	//now := time.Now().UTC()
	now := time.Now()
	endTime = now.Add(5 * time.Minute)
	startTime = now.Add(-5 * time.Minute)
	return &GetReportRequest{
		BaselineName: baselineName,
		StartTime:    startTime,
		EndTime:      endTime,
	}
}

// GetReportRequest 获取报告
type GetReportRequest struct {
	// BaselineName 上层集群baseline资源名称
	BaselineName string `json:"baselineName"`
	// StartTime 获取报告的开始时间
	StartTime time.Time `json:"startTime"`
	// EndTime 获取报告的结束时间
	EndTime time.Time `json:"endTime"`
}

// MarshalJSON ...
func (r *GetReportRequest) MarshalJSON() ([]byte, error) {
	type alias GetReportRequest
	aux := &struct {
		StartTime string `json:"startTime"`
		EndTime   string `json:"endTime"`
		*alias
	}{
		alias: (*alias)(r),
	}
	aux.StartTime = r.StartTime.Format(time.DateTime)
	aux.EndTime = r.EndTime.Format(time.DateTime)
	return json.Marshal(aux)
}

// GetReportResponse 获取报告
type GetReportResponse Response[[]*checker.Report]

type GetCheckersReportRequest struct {
	// CheckerNames 检查项名称
	CheckerNames []string `json:"checkerNames"`
	// StartTime 获取报告的开始时间
	StartTime time.Time `json:"startTime"`
	// EndTime 获取报告的结束时间
	EndTime time.Time `json:"endTime"`
}

// NewGetCheckersReportRequest ...
func NewGetCheckersReportRequest(checkerNames []string) *GetCheckersReportRequest {
	var startTime, endTime time.Time
	//now := time.Now().UTC()
	now := time.Now()
	endTime = now.Add(5 * time.Minute)
	startTime = now.Add(-5 * time.Minute)
	return &GetCheckersReportRequest{
		CheckerNames: checkerNames,
		StartTime:    startTime,
		EndTime:      endTime,
	}

}

// GetCheckersReportResponse 获取报告
type GetCheckersReportResponse Response[*checker.Report]

// MarshalJSON ...
func (r *GetCheckersReportRequest) MarshalJSON() ([]byte, error) {
	type alias GetCheckersReportRequest
	aux := &struct {
		StartTime string `json:"startTime"`
		EndTime   string `json:"endTime"`
		*alias
	}{
		alias: (*alias)(r),
	}
	aux.StartTime = r.StartTime.Format(time.DateTime)
	aux.EndTime = r.EndTime.Format(time.DateTime)
	return json.Marshal(aux)
}
