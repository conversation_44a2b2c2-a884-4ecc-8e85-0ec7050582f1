package feign

import "time"

type CustomTime struct {
	time.Time
}

func (ct *CustomTime) UnmarshalJSON(b []byte) error {
	tStr := string(b)
	tStr = tStr[1 : len(tStr)-1]
	parsedTime, err := time.Parse("2006-01-02 15:04:05", tStr)
	if err != nil {
		return err
	}
	ct.Time = parsedTime
	return nil
}

type ProjectResponse struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data []struct {
		Code         string `json:"code"`
		Name         string `json:"name"`
		ResourceList []struct {
			OrganID            string     `json:"organId"`
			ResourceTypeCode   string     `json:"resourceTypeCode"`
			ResourceInstanceID string     `json:"resourceInstanceId"`
			Name               string     `json:"name"`
			CreateTime         CustomTime `json:"createTime"`
		} `json:"resourceList"`
	} `json:"data"`
}

type OrganInfoResponse struct {
	Code int `json:"code"`
	Data struct {
		Name string `json:"name"`
	} `json:"data"`
	Msg string `json:"msg"`
}

type OrganMapResponse struct {
	Code int            `json:"code"`
	Data map[string]any `json:"data,inline"`
	Msg  string         `json:"msg"`
}
