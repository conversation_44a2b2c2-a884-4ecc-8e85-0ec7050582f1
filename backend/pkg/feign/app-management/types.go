package app_management

import "mime/multipart"

type Response[T any] struct {
	Code int    `json:"code"`
	Msg  string `json:"msg"`
	Data T      `json:"data"`
}

type UploadOneFileRequest struct {
	// BucketName 桶
	BucketName string `json:"bucketName"`
	// File 文件
	File *multipart.FileHeader `json:"-"`
	// Content 文件内容（二进制形式）
	Content []byte `json:"content"`
	// FileName 文件名（当使用 Content 时需要）
	FileName string `json:"fileName"`
}

type UploadOneFileResponse struct {
	// Id 文件id
	Id int64 `json:"id,string"`
	// Path 桶名
	Path string `json:"path"`
	// UniqueKey md5加密后的名字
	UniqueKey string `json:"uniqueKey"`
	// Name 文件名称
	Name string `json:"name"`
	// Link 链接地址, 当前浏览器地址拼上该链接访问
	// 比如 *********** 环境, 则拼上 `***********${link}` ***********/minio/applicaiton/aaa.txt
	Link string `json:"link"`
}

// DownloadOneFileRequest 下载文件
type DownloadOneFileRequest struct {
	// Id 文件id
	Id int64 `json:"id,string"`
}

// DownloadOneFileResponse 下载文件
type DownloadOneFileResponse struct {
	// FileName 文件名称
	FileName string `json:"fileName"`
	// FileContent 文件
	FileContent []byte `json:"fileContent"`
}
