package app_management

import (
	"testing"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/feign"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/backupserver"
)

const jwt = "eyJraWQiOiJnZiIsImFsZyI6IkhTNTEyIn0.eyJzdWIiOiJ7XCJpZFwiOjEsXCJuYW1lXCI6XCLotoXnuqfnrqHnkIblkZhcIixcInVzZXJuYW1lXCI6XCJhZG1pblwifSIsIm5hbWUiOiLotoXnuqfnrqHnkIblkZgiLCJleHAiOjE3MzIyNzI0MDMsImlhdCI6MTczMjI1NzQwMywib2x5bXB1c25hbWUiOiJvbHltcHVzIn0.OnXG1WMUiIcTA3aToOWX2Vf0nBRpW8CMVqq6UhrWKsfe9ssQh1_YoxkLgQJGxdD9npctcRevtmBvBN0qLgO_0w"

const organId = "3627386849031282688"

func TestService_GetOrganInfo(t *testing.T) {
	type fields struct {
		HttpCli feign.HttpClientInterface
	}
	type args struct {
		jwt     string
		organId string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    *backupserver.Organ
		wantErr bool
	}{
		{
			name: "test",
			fields: fields{
				HttpCli: feign.NewHttpClientWithBaseURL("http://localhost:9060"),
			},
			args: args{
				jwt:     jwt,
				organId: organId,
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := Service{
				HttpCli: tt.fields.HttpCli,
			}
			got, err := s.GetOrganInfo(tt.args.jwt, tt.args.organId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOrganInfo() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != nil {
				t.Logf("GetOrganInfo() got = %v", got)
			}
		})
	}
}

func TestService_GetOrganMap(t *testing.T) {
	type fields struct {
		HttpCli feign.HttpClientInterface
	}
	type args struct {
		jwt string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[string]any
		wantErr bool
	}{
		{
			name: "test",
			fields: fields{
				HttpCli: feign.NewHttpClientWithBaseURL("http://localhost:9060"),
			},
			args: args{
				jwt: jwt,
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := Service{
				HttpCli: tt.fields.HttpCli,
			}
			got, err := s.GetOrganMap(tt.args.jwt)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOrganMap() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			t.Log(got)
		})
	}
}

func TestService_GetProjects(t *testing.T) {
	type fields struct {
		HttpCli feign.HttpClientInterface
	}
	type args struct {
		jwt     string
		organId string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    []*backupserver.Project
		wantErr bool
	}{
		{
			name: "test",
			fields: fields{
				HttpCli: feign.NewHttpClientWithBaseURL("http://localhost:9060"),
			},
			args: args{
				jwt:     jwt,
				organId: organId,
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := Service{
				HttpCli: tt.fields.HttpCli,
			}
			got, err := s.GetProjects(tt.args.jwt, tt.args.organId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetProjects() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != nil {
				t.Logf("GetProjects() got = %v", got)
			}
		})
	}
}

func TestService_GetOrganIdMap(t *testing.T) {
	type fields struct {
		HttpCli feign.HttpClientInterface
	}
	type args struct {
		jwt string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[string]string
		wantErr bool
	}{
		{
			name: "test",
			fields: fields{
				HttpCli: feign.NewHttpClientWithBaseURL("http://localhost:9060"),
			},
			args: args{
				jwt: jwt,
			},
			want:    nil,
			wantErr: false,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := Service{
				HttpCli: tt.fields.HttpCli,
			}
			got, err := s.GetOrganIdMap(tt.args.jwt)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetOrganIdMap() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != nil {
				t.Logf("GetOrganIdMap() got = %v", got)
			}
		})
	}
}

func TestService_GetProjectMapByOrganId(t *testing.T) {
	type fields struct {
		HttpCli feign.HttpClientInterface
	}
	type args struct {
		jwt     string
		organId string
	}
	tests := []struct {
		name    string
		fields  fields
		args    args
		want    map[string]backupserver.Project
		wantErr bool
	}{
		{
			name: "test",
			fields: fields{
				HttpCli: feign.NewHttpClientWithBaseURL("http://localhost:9060"),
			},
			args: args{
				jwt:     jwt,
				organId: organId,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			s := Service{
				HttpCli: tt.fields.HttpCli,
			}
			got, err := s.GetProjectMapByOrganId(tt.args.jwt, tt.args.organId)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetProjectMapByOrganId() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != nil {
				t.Logf("GetProjectMapByOrganId() got = %v", got)
			}
		})
	}
}
