package app_management

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/http"
	"time"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/feign"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/backupserver"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

const svc = "app-management-svc"

const namespace = "caas-system"

const port = 9060

const projectType = "project"

type Service struct {
	HttpCli feign.HttpClientInterface
}

func NewService() *Service {
	return &Service{HttpCli: feign.NewHttpClientWithBaseURL(config.DevopsAmpURL.Value)}
	//return &Service{HttpCli: feign.NewHttpClient(svc, namespace, port)}
}

// UploadOneFile 上传单个文件
func (s *Service) UploadOneFile(ctx context.Context, request *UploadOneFileRequest) (*UploadOneFileResponse, error) {
	// 创建一个 buffer 用来存放 multipart/form-data 的请求体
	bodyBuff := &bytes.Buffer{}
	bodyWriter := multipart.NewWriter(bodyBuff)

	// 创建文件表单字段
	var err error
	if request.File != nil {
		// 如果是文件上传
		file, err := request.File.Open()
		if err != nil {
			return nil, fmt.Errorf("failed to open file: %v", err)
		}
		defer file.Close()

		fileWriter, err := bodyWriter.CreateFormFile("file", request.File.Filename)
		if err != nil {
			return nil, fmt.Errorf("failed to create form file: %v", err)
		}

		// 将文件内容复制到表单字段中
		if _, err = io.Copy(fileWriter, file); err != nil {
			return nil, fmt.Errorf("failed to copy file content: %v", err)
		}
	} else {
		// 使用 Content 方式上传
		if request.FileName == "" {
			return nil, fmt.Errorf("fileName is required when uploading with content")
		}

		fileWriter, err := bodyWriter.CreateFormFile("file", request.FileName)
		if err != nil {
			return nil, fmt.Errorf("failed to create form file with name %s: %v", request.FileName, err)
		}

		// 写入内容
		written, err := fileWriter.Write(request.Content)
		if err != nil {
			return nil, fmt.Errorf("failed to write content (attempted to write %d bytes): %v", len(request.Content), err)
		}
		if written != len(request.Content) {
			return nil, fmt.Errorf("incomplete write: wrote %d bytes out of %d", written, len(request.Content))
		}
	}

	// 关闭 bodyWriter
	if err := bodyWriter.Close(); err != nil {
		return nil, fmt.Errorf("failed to close body writer: %v", err)
	}

	// 请求 URL
	url := fmt.Sprintf("%s/files/upload/one?bucketName=%s", config.DevopsAmpURL.Value,
		request.BucketName)

	// 创建 HTTP 请求
	req, err := http.NewRequestWithContext(ctx, http.MethodPost, url, bodyBuff)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// 设置 Authorization Header
	req.Header.Set("Authorization", utils.GetUserTokenFromContext(ctx))

	// 设置 Content-Type
	req.Header.Set("Content-Type", bodyWriter.FormDataContentType())

	// 创建一个 HTTP 客户端并设置超时
	client := http.Client{
		Timeout: 60 * time.Second,
	}

	// 执行请求
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	// 检查 HTTP 响应状态
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("upload failed: %s", string(body))
	}

	var response Response[UploadOneFileResponse]
	if err := json.Unmarshal(body, &response); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %v", err)
	}
	if response.Code != 0 {
		return nil, fmt.Errorf("upload file failed: %s", response.Msg)
	}
	return &response.Data, nil
}

func (s Service) DownloadOneFile(ctx context.Context, request *DownloadOneFileRequest) (*DownloadOneFileResponse, error) {
	// 构造外部接口 URL
	url := fmt.Sprintf("%s/files/download?id=%d", config.DevopsAmpURL.Value, request.Id)

	// 创建 HTTP 客户端
	client := &http.Client{
		Timeout: 60 * time.Second,
	}

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %v", err)
	}

	// 发起 GET 请求，获取文件流
	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to get file from external service: %v", err)
	}
	defer resp.Body.Close()

	// 如果响应状态不是 200 OK，则返回错误
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("failed to download file, status: %s", resp.Status)
	}

	// 从响应头中获取文件名
	fileName := resp.Header.Get("Content-Disposition")
	// 这里假设文件名在 Content-Disposition 中被指定了，实际情况中可能需要额外的解析
	if fileName == "" {
		fileName = fmt.Sprintf("%d", request.Id) // 如果没有文件名，则使用 id 作为默认文件名
	}
	// 读取文件内容到内存中
	fileContent, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read file content: %v", err)
	}

	// 返回文件内容和文件名
	return &DownloadOneFileResponse{
		FileName:    fileName,
		FileContent: fileContent,
	}, nil
}

func (s Service) GetOrganInfo(jwt, organId string) (*backupserver.Organ, error) {
	if s.HttpCli == nil {
		return nil, fmt.Errorf("HTTP client is not initialized")
	}
	var organInfo backupserver.Organ
	endpoint := fmt.Sprintf("/v2/organizations/%s", organId)
	// 通过 JWT 添加到请求头
	headers := map[string]string{
		"Authorization": jwt,
		"Amp-Organ-Id":  "1",
	}
	// 发起 GET 请求并传递请求头
	body, err := s.HttpCli.GetWithHeaders(endpoint, headers)
	if err != nil {
		return nil, fmt.Errorf("failed to get organ info: %v", err)
	}
	// 定义用于解析 JSON 响应的结构体
	var organInfoResponse feign.OrganInfoResponse

	// 解析 JSON 响应体
	err = json.Unmarshal(body, &organInfoResponse)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %v", err)
	}
	// 检查响应码是否成功
	if organInfoResponse.Code != 0 {
		return nil, fmt.Errorf("failed to get organ info: %s", organInfoResponse.Msg)
	}
	// 设置组织信息
	organInfo.OrganId = organId
	organInfo.OrganName = organInfoResponse.Data.Name

	return &organInfo, nil
}

func (s Service) GetOrganMap(jwt string) (map[string]any, error) {
	if s.HttpCli == nil {
		return nil, fmt.Errorf("HTTP client is not initialized")
	}

	// 构建 endpoint 和查询参数
	baseEndpoint := "/v2/organizations"
	queryParams := "queryParams="

	// 合并 endpoint 和查询参数
	fullEndpoint := fmt.Sprintf("%s?%s", baseEndpoint, queryParams)

	// 设置请求头
	headers := map[string]string{
		"Amp-Organ-Id":  "1",
		"Amp-App-Code":  "application",
		"Authorization": jwt,
	}

	// 定义结构体以解析所需响应数据
	var resp struct {
		Code int            `json:"code"`
		Data map[string]any `json:"data,inline"`
		Msg  string         `json:"msg"`
	}

	// 发起 GET 请求
	body, err := s.HttpCli.GetWithHeaders(fullEndpoint, headers)
	if err != nil {
		return nil, fmt.Errorf("failed to get organ map: %v", err)
	}

	// 解析响应
	err = json.Unmarshal(body, &resp)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %v", err)
	}

	// 检查响应码
	if resp.Code != 0 {
		return nil, fmt.Errorf("failed to get organ info: %s", resp.Msg)
	}

	// 返回 data 字段的内容
	return resp.Data, nil
}

func (s Service) GetOrganIdMap(jwt string) (map[string]string, error) {
	if s.HttpCli == nil {
		return nil, fmt.Errorf("HTTP client is not initialized")
	}

	// 构建 endpoint 和查询参数
	baseEndpoint := "/v2/organizations"
	queryParams := "queryParams="

	// 合并 endpoint 和查询参数
	fullEndpoint := fmt.Sprintf("%s?%s", baseEndpoint, queryParams)

	// 设置请求头
	headers := map[string]string{
		"Amp-Organ-Id":  "1",
		"Amp-App-Code":  "application",
		"Authorization": jwt,
	}

	// 定义结构体以解析所需响应数据
	var resp struct {
		Code int `json:"code"`
		Data struct {
			Records []struct {
				ID   string `json:"id"`
				Name string `json:"name"`
			} `json:"records"`
		} `json:"data"`
		Msg string `json:"msg"`
	}

	// 发起 GET 请求
	body, err := s.HttpCli.GetWithHeaders(fullEndpoint, headers)
	if err != nil {
		return nil, fmt.Errorf("failed to get organ map: %v", err)
	}

	// 解析响应
	err = json.Unmarshal(body, &resp)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %v", err)
	}

	// 检查响应码
	if resp.Code != 0 {
		return nil, fmt.Errorf("failed to get organ info: %s", resp.Msg)
	}

	// 提取 id 和 name 到 map
	organMap := make(map[string]string)
	for _, record := range resp.Data.Records {
		organMap[record.ID] = record.Name
	}

	return organMap, nil
}

func (s Service) GetProjectMapByOrganId(jwt, organId string) (map[string]backupserver.Project, error) {
	if s.HttpCli == nil {
		return nil, fmt.Errorf("HTTP client is not initialized")
	}
	projects := make(map[string]backupserver.Project)
	endpoint := fmt.Sprintf("/resourceInstances/user/group")
	headers := map[string]string{
		"Authorization": jwt,
		"Amp-Organ-Id":  organId,
		"amp-app-code":  "application",
	}
	body, err := s.HttpCli.GetWithHeaders(endpoint, headers)
	if err != nil {
		return nil, fmt.Errorf("failed to get project map: %v", err)
	}
	var projectResponse feign.ProjectResponse
	err = json.Unmarshal(body, &projectResponse)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %v", err)
	}
	if projectResponse.Code != 0 {
		return projects, fmt.Errorf("get project info failed: %s", projectResponse.Msg)
	}
	for _, Data := range projectResponse.Data {
		for _, project := range Data.ResourceList {
			if project.ResourceTypeCode == projectType {
				projects[project.ResourceInstanceID] = backupserver.Project{
					ID:         project.ResourceInstanceID,
					Name:       project.Name,
					CreateTime: &project.CreateTime.Time,
				}
			}
		}

	}
	return projects, nil
}

func (s Service) GetProjects(jwt string, organId string) ([]*backupserver.Project, error) {
	if s.HttpCli == nil {
		return nil, fmt.Errorf("HTTP client is not initialized")
	}
	projects := []*backupserver.Project{}
	endpoint := fmt.Sprintf("/resourceInstances/user/group")
	headers := map[string]string{
		"Authorization": jwt,
		"Amp-Organ-Id":  organId,
		"amp-app-code":  "application",
	}
	body, err := s.HttpCli.GetWithHeaders(endpoint, headers)
	if err != nil {
		return nil, fmt.Errorf("get project info failed: %w", err)
	}
	var projectRespone feign.ProjectResponse
	err = json.Unmarshal(body, &projectRespone)
	if err != nil {
		return projects, fmt.Errorf("failed to unmarshal response: %w", err)
	}
	if projectRespone.Code != 0 {
		return projects, fmt.Errorf("get project info failed: %s", projectRespone.Msg)
	}
	for _, Data := range projectRespone.Data {
		for _, project := range Data.ResourceList {
			if project.ResourceTypeCode == projectType {
				projects = append(projects, &backupserver.Project{
					ID:         project.ResourceInstanceID,
					Name:       project.Name,
					CreateTime: &project.CreateTime.Time,
				})
			}
		}
	}
	return projects, nil
}
