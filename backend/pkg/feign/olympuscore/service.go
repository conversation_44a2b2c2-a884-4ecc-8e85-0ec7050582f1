package olympuscore

import (
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/feign"
)

const svc = "olympus-core-svc"

const namespace = "caas-system"

const port = 8080

const projectType = "project"

type Service struct {
	HttpCli feign.HttpClientInterface
}

func NewService() *Service {
	return &Service{HttpCli: feign.NewHttpClientWithBaseURL(config.OlympusCoreAddress.Value)}
	//return &Service{HttpCli: feign.NewHttpClient(svc, namespace, port)}
}
