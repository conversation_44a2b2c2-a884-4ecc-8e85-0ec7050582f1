package feign

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"
	"time"
)

var _ HttpClientInterface = (*HttpClient)(nil)

type HttpClientInterface interface {
	Get(endpoint string) ([]byte, error)
	Post(endpoint string, body interface{}) ([]byte, error)
	GetWithHeaders(endpoint string, headers map[string]string) ([]byte, error)
}

type HttpClient struct {
	client  *http.Client
	baseURL string
}

type Options struct {
	Timeout time.Duration
}

type Option func(o *Options)

// WithTimeout 设置超时时间
func WithTimeout(timeout time.Duration) Option {
	return func(o *Options) {
		o.Timeout = timeout
	}
}

// NewHttpClient 通过 Kubernetes 服务名创建 HTTP 客户端
func NewHttpClient(serviceName, namespace string, port int, opts ...Option) HttpClientInterface {
	options := &Options{
		Timeout: 5 * time.Second, // 默认超时时间
	}
	for _, opt := range opts {
		opt(options)
	}
	baseURL := fmt.Sprintf("http://%s.%s.svc.cluster.local:%d", serviceName, namespace, port)

	//baseURL := "http://localhost:" + fmt.Sprintf("%d", port)
	return &HttpClient{
		client: &http.Client{
			Timeout: options.Timeout, // 设置请求超时时间
		},
		baseURL: baseURL,
	}
}

func NewHttpClientWithBaseURL(baseURL string, opts ...Option) HttpClientInterface {
	options := &Options{
		Timeout: 5 * time.Second, // 默认超时时间
	}
	for _, opt := range opts {
		opt(options)
	}
	return &HttpClient{
		client: &http.Client{
			Timeout: options.Timeout, // 设置请求超时时间
		},
		baseURL: baseURL,
	}
}

// Get 请求
func (h *HttpClient) Get(endpoint string) ([]byte, error) {
	// 创建 GET 请求
	req, err := http.NewRequest("GET", h.baseURL+endpoint, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create GET request: %v", err)
	}

	// 执行请求
	resp, err := h.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute GET request: %v", err)
	}
	defer resp.Body.Close()
	// 读取响应体
	bodyBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	// 检查 HTTP 状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("GET request failed with status %s: %s", resp.Status, string(bodyBytes))
	}
	return bodyBytes, nil
}

// Post 请求
func (h *HttpClient) Post(endpoint string, body interface{}) ([]byte, error) {
	// 将 body 序列化为 JSON
	jsonBody, err := json.Marshal(body)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request body: %v", err)
	}

	// 创建 POST 请求
	req, err := http.NewRequest("POST", h.baseURL+endpoint, bytes.NewBuffer(jsonBody))
	if err != nil {
		return nil, fmt.Errorf("failed to create POST request: %v", err)
	}
	req.Header.Set("Content-Type", "application/json")

	// 执行请求
	resp, err := h.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute POST request: %v", err)
	}
	defer resp.Body.Close()

	// 读取响应体
	bodyBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	// 检查 HTTP 状态码
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("POST request failed with status %s: %s", resp.Status, string(bodyBytes))
	}

	return bodyBytes, nil
}

// GetWithHeaders GET 请求，支持请求头
func (h *HttpClient) GetWithHeaders(endpoint string, headers map[string]string) ([]byte, error) {
	req, err := http.NewRequest("GET", h.baseURL+endpoint, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create GET request: %v", err)
	}

	// 设置请求头
	for key, value := range headers {
		req.Header.Set(key, value)
	}

	resp, err := h.client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to execute GET request: %v", err)
	}
	defer resp.Body.Close()

	bodyBytes, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %v", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("GET request failed with status %s: %s", resp.Status, string(bodyBytes))
	}

	return bodyBytes, nil
}
