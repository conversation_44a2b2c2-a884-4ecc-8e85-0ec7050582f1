package initialize

import (
	"context"
	"fmt"
	"strings"

	"github.com/minio/minio-go/v7"
	"github.com/minio/minio-go/v7/pkg/credentials"
	"go.uber.org/zap"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	minioutils "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/minio"
)

// NewMinioInit ...
func NewMinioInit() Init {
	var (
		minioEndpoint  = config.MinioEndpoint.Value
		minioAccessKey = config.MinioAccessKey.Value
		minioSecretKey = config.MinioSecretKey.Value
		minioRegion    = config.MinioRegion.Value
		minioBuckets   = strings.Split(config.MinioInitBuckets.Value, ",")
	)
	m := &minioInit{
		region:  minioRegion,
		buckets: minioBuckets,
	}
	client, err := minio.New(minioEndpoint, &minio.Options{
		Creds:  credentials.NewStaticV4(minioAccessKey, minioSecretKey, ""),
		Secure: false,
	})
	m.log = logger.GetLogger().
		With(zap.String("endpoint", minioEndpoint)).
		With(zap.String("region", minioRegion)).
		Named("init-minio")
	if err != nil {
		m.log.Error("init minio client failed", zap.String("url", minioEndpoint), zap.Error(err))
		return m
	}
	m.client = client
	return m

}

// minioInit ...
type minioInit struct {
	client  *minio.Client
	region  string
	buckets []string
	log     *zap.Logger
}

// Init ...
func (i *minioInit) Init() error {
	i.log.Info("start init minio")
	if i.client == nil {
		i.log.Error("the minio client is empty")
		return nil
	}
	if len(i.buckets) == 0 || i.region == "" {
		i.log.Error("the minio forever bucket or region is empty")
		return nil
	}
	for _, bucket := range i.buckets {
		i.log.With(zap.String("region", i.region)).
			With(zap.String("bucket", bucket))
		ctx := context.Background()
		exists, err := i.client.BucketExists(ctx, bucket)
		if err != nil {
			i.log.Error("check bucket exists failed", zap.Error(err))
			return nil
		}
		if !exists {
			if err := i.client.MakeBucket(ctx, bucket,
				minio.MakeBucketOptions{Region: i.region}); err != nil {
				i.log.Error("create bucket failed", zap.Error(err))
				return err
			}
		}
		if bucket == constants.MinioBaselineBucketName {
			bucketPolicy := fmt.Sprintf(minioutils.PublicAccessPolicy, bucket)
			if err := i.client.SetBucketPolicy(ctx, bucket, bucketPolicy); err != nil {
				return err
			}
		}
	}

	return nil
}
