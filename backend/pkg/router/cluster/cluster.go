package cluster

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/cluster"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

var _ = clustermodel.ListResponse{}

func NewRouter() routerutil.ApiController {
	return &router{
		intf: cluster.NewHandler(),
	}
}

// 集群Router
type router struct {
	intf cluster.Handler
}

func (r *router) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.ApiV1Group + "/clusters")
}

func (r *router) RegisterRouter(group *gin.RouterGroup) {
	// 集群相关
	group.GET("", r.listClusters)
	group.GET("/:clusterName", r.withClusterName)
	group.GET("/:clusterName/exist", r.withClusterNameExist)
	group.GET("/switchClusterList", r.switchClusterList)
}

// listClusters 获取集群列表
//
//	@Tags			集群相关
//	@Tags			集群相关API
//	@Summary		获取集群列表
//	@Description	获取集群列表
//	@Param			state				query	string	false	"集群状态筛选器"			Enums(controlled, un-controlled)																					default()
//	@Param			status				query	string	false	"集群生命周期状态筛选器"		Enums(preflighting,installing,joining,preflightFailed,installFailed,joinFailed,online,offline,initializing,unknow)	default()
//	@Param			api_server_status	query	string	false	"集群API-Server状态"	Enums(success,fail,initialize)																						default()
//	@Router			/apis/v1/clusters [get]
//	@Success		200	{object}	utils.Response{data=clustermodel.ListResponse}
func (r *router) listClusters(ctx *gin.Context) {
	state := ctx.Query("state")
	status := ctx.Query("status")
	apiServerStatus := ctx.Query("api_server_status")
	response, err := r.intf.ListClusters(ctx, cluster.MustListClusterStateOption(state), cluster.MustListClusterStatusOption(status), cluster.MustListClusterApiServerStatusOption(apiServerStatus))
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, response)
}

// withClusterName 根据集群名称返回集群详情
//
//	@Tags
//	@Tags			集
//	@Tags			集群相关API
//	@Summary		查看集群详情
//	@Description	查看集群详情
//	@Param			clusterName	path	string	true	"集群名称"
//	@Router			/apis/v1/clusters/{clusterName} [get]
//	@Success		200	{object}	utils.Response{data=clustermodel.Response}
func (r *router) withClusterName(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	if clusterName == "" {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "clusterName is empty"))
		return
	}
	response, err := r.intf.WithClusterName(ctx, clusterName)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, response)
}

// withClusterNameExist 根据集群名称查看集群是否存在
//
//	@Tags			集群相关API
//	@Summary		查看集群是否存在
//	@Description	根据集群名称查看集群是否存在
//	@Param			clusterName	path	string	true	"集群名称"
//	@Router			/apis/v1/clusters/{clusterName}/exist [get]
//	@Success		200	{object}	utils.Response{data=clustermodel.ExistResponse}
func (r *router) withClusterNameExist(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	if clusterName == "" {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "clusterName is empty"))
		return
	}
	response, err := r.intf.ClusterExist(ctx, clusterName)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, response)

}

func (r *router) switchClusterList(ctx *gin.Context) {
	response, err := r.intf.SwitchClusterList(ctx)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, response)

}
