package cluster

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/midware-go/midwares/audit"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/cluster"
	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

func init() {
	audit.VAR.Register(
		http.MethodPost,
		utils.ApiV1Group+"/clusters-create",
		"clusters-create",
		"集群创建新增-{clusterName}",
		audit.NewRequestParamCfgRaw("clusterName", "$.clusterName"),
		audit.NewRequestParamCfgRaw("requestBody", "$"),
	)

	audit.VAR.Register(
		http.MethodPut,
		utils.ApiV1Group+"/clusters-create"+"/:clusterName",
		"clusters-create",
		"集群创建编辑-{clusterName}",
		audit.NewDefaultRequestParamCfgPath("clusterName"),
		audit.NewRequestParamCfgRaw("requestBody", "$"),
	)

}

func NewCreateRouter() routerutil.ApiController {
	return &createRouter{
		intf: cluster.NewCreateClusterHandler(),
	}
}

// 集群Router
type createRouter struct {
	intf cluster.CreateClustersHandler
}

func (r *createRouter) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.ApiV1Group + "/clusters-create")
}

func (r *createRouter) RegisterRouter(group *gin.RouterGroup) {
	// 集群创建相关
	group.POST("", r.clusterCreate)
	group.GET("/:clusterName/status", r.clusterCreateStatus)
	group.GET("/:clusterName", r.getClusterCreateDescription)
	group.PUT("/:clusterName", r.updateClusterCreate)
	group.DELETE("/:clusterName", r.deleteClusterCreate)
	group.POST("/:clusterName/retry", r.clusterCreateRetry)
	// 集群创建日志相关
	group.GET("/:clusterName/logs", r.clusterCreateLogs)
	group.GET("/:clusterName/steps/:stepName/logs", r.clusterCreateLogs)
}

// getClusterCreateSugarConfig 创建集群
//
//	@Tags			创建集群相关API
//	@Summary		创建集群
//	@Description	创建集群 ipv4Param\ipv6Param\auth.Param 请点开model查看详情
//	@Param			requestBody	body	clustermodel.CreateRequest	true	"创建集群请求JSON"
//	@Router			/apis/v1/clusters-create [post]
//	@Success		200	{object}	utils.Response{data=bool}
func (r *createRouter) clusterCreate(ctx *gin.Context) {
	request := clustermodel.CreateRequest{}
	if err := ctx.ShouldBind(&request); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}

	if err := r.intf.CreateCluster(ctx, request); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, nil)
	return
}

// clusterCreateStatus 创建集群状态查询接口
//
//	@Tags			创建集群相关API
//	@Summary		创建集群状态查询接口
//	@Description	创建集群状态查询接口
//	@Param			clusterName	path	string	true	"集群名称"
//	@Router			/apis/v1/clusters-create/{clusterName}/status [get]
//	@Success		200	{object}	utils.Response{data=clustermodel.CreateStatusResponse}
func (r *createRouter) clusterCreateStatus(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	if clusterName == "" {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "clusterName is empty"))
		return
	}
	response, err := r.intf.CreateClusterStatus(ctx, clusterName)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, response)
}

// getClusterCreateDescription 获取创建集群的回显
//
//	@Tags			创建集群相关API
//	@Summary		获取创建集群的回显
//	@Description	获取创建集群的回显 ipv4Param\ipv6Param\auth.Param 请点开model查看详情
//	@Param			clusterName	path	string	true	"集群名称"
//	@Router			/apis/v1/clusters-create/{clusterName} [get]
//	@Success		200	{object}	utils.Response{data=clustermodel.CreateResponse}
func (r *createRouter) getClusterCreateDescription(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	if clusterName == "" {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "clusterName is empty"))
		return
	}
	response, err := r.intf.CreateResponse(ctx, clusterName)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, response)

}

// updateClusterCreate 更新创建失败的集群 以重新开始创建集群的流程
//
//	@Tags			创建集群相关API
//	@Summary		更新创建失败的集群 以重新开始创建集群的流程
//	@Description	更新创建失败的集群 以重新开始创建集群的流程 ipv4Param\ipv6Param\auth.Param 请点开model查看详情
//	@Param			clusterName	path	string						true	"集群名称"
//	@Param			requestBody	body	clustermodel.CreateRequest	true	"创建集群请求JSON"
//	@Router			/apis/v1/clusters-create/{clusterName} [put]
//	@Success		200	{object}	utils.Response{data=bool}
func (r *createRouter) updateClusterCreate(ctx *gin.Context) {

	clusterName := ctx.Param("clusterName")
	if clusterName == "" {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "clusterName is empty"))
		return
	}

	request := clustermodel.CreateRequest{}
	if err := ctx.ShouldBind(&request); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}

	if err := r.intf.UpdateCluster(ctx, clusterName, request); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, nil)
	return
}

// deleteClusterCreate 删除创建失败的集群
//
//	@Tags			创建集群相关API
//	@Summary		删除创建失败的集群
//	@Description	删除创建失败的集群
//	@Param			clusterName	path	string	true	"集群名称"
//	@Router			/apis/v1/clusters-create/{clusterName} [delete]
//	@Success		200	{object}	utils.Response{data=bool}
func (r *createRouter) deleteClusterCreate(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	if clusterName == "" {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "clusterName is empty"))
		return
	}

	if err := r.intf.DeleteCluster(ctx, clusterName); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, nil)
	return
}

// clusterCreateRetry 创建集群失败重试
//
//	@Tags			创建集群相关API
//	@Summary		创建集群失败重试
//	@Description	创建集群失败重试
//	@Param			clusterName	path	string	true	"集群名称"
//	@Router			/apis/v1/clusters-create/{clusterName}/retry [post]
//	@Success		200	{object}	utils.Response{data=bool}
func (r *createRouter) clusterCreateRetry(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	if clusterName == "" {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "clusterName is empty"))
		return
	}
	if err := r.intf.Retry(ctx, clusterName); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}

	utils.Succeed(ctx, nil)
	return
}

// clusterCreateLogs 获取集群创建过程中的日志
//
//	@Tags			创建集群相关API
//	@Summary		获取集群创建过程中的日志
//	@Description	获取集群创建过程中的日志
//	@Param			clusterName	path	string	true	"集群名称"
//	@Param			stepName	path	string	false	"步骤名称"
//	@Param			follow		query	string	false	"是否持续输出"
//	@Router			/apis/v1/clusters-create/{clusterName}/logs [GET]
//	@Router			/apis/v1/clusters-create/{clusterName}/steps/{stepName}/logs [GET]
//	@Success		200	{object}	utils.Response{data=bool}
func (r *createRouter) clusterCreateLogs(ctx *gin.Context) {
	// 读取参数
	request := clustermodel.CreateLogQueryRequest{}
	if err := ctx.ShouldBindUri(&request); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	// 设置是否为流式输出 如果为流式输出 则使用webSocket
	var follow bool
	followStr := ctx.Query("follow")
	if strings.ToLower(followStr) == "true" {
		follow = true
	}
	// 构建全局context
	// 获取日志
	logBytesChan, logContext, logCancelFunc, err := r.intf.CreateLog(ctx, request)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	defer logCancelFunc()
	utils.WebSocketProxy(ctx, follow, logBytesChan, logContext, logCancelFunc)
}
