package cluster

import (
	"io"
	"mime/multipart"

	clustermodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"

	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

func NewClusterUpgradeAPIsController() routerutil.ApiController {
	return &UpgradeAPIsController{
		upgradeClustersHandler: cluster.NewUpgradeClustersHandler(),
	}
}

type UpgradeAPIsController struct {
	upgradeClustersHandler cluster.UpgradeClustersHandler
}

func (uc *UpgradeAPIsController) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.ApiV1Group + "/clusterupgrades")
}

// RegisterRouter 集群升级Restful API
func (uc *UpgradeAPIsController) RegisterRouter(group *gin.RouterGroup) {
	//集群列表是否可升级
	group.GET("", uc.listClusterUpgrade)
	// 集群升级包列表
	group.GET("/:clusterName/packages", uc.listClusterUpgradePackage)
	// 集群升级包详情
	group.GET("/:clusterName/packages/:packageName", uc.getClusterUpgradePackage)
	// 集群默认负载均衡
	group.GET("/:clusterName/loadbalancer/default", uc.getClusterUpgradeLoadBalanceDefault)
	// 集群升级节点列表
	group.GET("/:clusterName/nodes", uc.listClusterUpgradeNodes)
	// 集群升级批量导入模版下载
	group.GET("/:clusterName/nodes/template/download", uc.downloadClusterUpgradeNodeTemplate)
	// 集群升级读取模版文件数据
	group.POST("/:clusterName/nodes/template/upload", uc.uploadClusterUpgradeNodeTemplate)
	// 创建集群升级
	group.POST("/:clusterName", uc.createClusterUpgrade)
	// 集群升级执行流程
	group.GET("/:clusterName/step", uc.getClusterUpgradeStep)
	// 集群升级重试
	group.PUT("/:clusterName/retry", uc.retryClusterUpgrade)
	// 集群升级信息查询
	group.GET("/:clusterName", uc.getClusterUpgrade)
}

func (uc *UpgradeAPIsController) listClusterUpgrade(ctx *gin.Context) {
	response, err := uc.upgradeClustersHandler.ListClusterUpgrade(ctx)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, response)
}

func (uc *UpgradeAPIsController) listClusterUpgradePackage(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	if clusterName == "" {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "clusterName is empty"))
		return
	}
	response, err := uc.upgradeClustersHandler.ListClusterUpgradePackage(ctx, clusterName)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, response)
}

func (uc *UpgradeAPIsController) getClusterUpgradePackage(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	packageName := ctx.Param("packageName")
	response, err := uc.upgradeClustersHandler.GetClusterUpgradePackage(ctx, clusterName, packageName)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, response)
}

func (uc *UpgradeAPIsController) getClusterUpgradeLoadBalanceDefault(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	if clusterName == "" {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "clusterName is empty"))
		return
	}
	response, err := uc.upgradeClustersHandler.GetClusterUpgradeLoadBalanceDefault(ctx, clusterName)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, response)
}

func (uc *UpgradeAPIsController) listClusterUpgradeNodes(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	includeNative := ctx.Query("includeNative")
	if clusterName == "" {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "clusterName is empty"))
		return
	}
	if includeNative == "" {
		includeNative = "false"
	}
	response, err := uc.upgradeClustersHandler.ListClusterUpgradeNodes(ctx, clusterName, includeNative)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, response)
}
func (uc *UpgradeAPIsController) downloadClusterUpgradeNodeTemplate(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	includeNative := ctx.Query("includeNative")
	if clusterName == "" {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "clusterName is empty"))
		return
	}
	if includeNative == "" {
		includeNative = "false"
	}
	downloadInfo, err := uc.upgradeClustersHandler.DownloadClusterUpgradeNodeTemplate(ctx, clusterName, includeNative)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	ctx.Header("Content-Disposition", "attachment; filename="+downloadInfo.FileName)
	ctx.Header("Content-Type", "text/plain")
	buffer := downloadInfo.Buffer
	ctx.Stream(func(w io.Writer) bool {
		cacheBytes := make([]byte, 1024)
		size, err := buffer.Read(cacheBytes)
		if err != nil || size == 0 {
			return false
		}
		_, err = w.Write(cacheBytes[0:size])
		if err != nil {
			return false
		}
		return true
	})
}

func (uc *UpgradeAPIsController) uploadClusterUpgradeNodeTemplate(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	includeNative := ctx.PostForm("includeNative")
	if clusterName == "" {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "clusterName is empty"))
		return
	}
	if includeNative == "" {
		includeNative = "false"
	}
	multipartFileInfo, err := ctx.MultipartForm()
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	var fhList []*multipart.FileHeader
	for _, fhs := range multipartFileInfo.File {
		for _, fh := range fhs {
			fh := fh
			fhList = append(fhList, fh)
		}
	}
	response, err := uc.upgradeClustersHandler.UploadClusterUpgradeNodeTemplate(ctx, fhList, clusterName, includeNative)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, response)
}

func (uc *UpgradeAPIsController) createClusterUpgrade(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	if clusterName == "" {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "clusterName is empty"))
		return
	}
	request := clustermodel.UpgradeClusterRequest{}
	if err := ctx.ShouldBind(&request); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}

	if err := uc.upgradeClustersHandler.UpgradeCluster(ctx, clusterName, request); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, nil)
	return
}

func (uc *UpgradeAPIsController) getClusterUpgradeStep(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	if clusterName == "" {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "clusterName is empty"))
		return
	}
	if response, err := uc.upgradeClustersHandler.GetClusterUpgradeStep(ctx, clusterName); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	} else {
		utils.Succeed(ctx, response)
	}
}

func (uc *UpgradeAPIsController) retryClusterUpgrade(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	if clusterName == "" {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "clusterName is empty"))
		return
	}
	if err := uc.upgradeClustersHandler.RetryClusterUpgrade(ctx, clusterName); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	} else {
		utils.Succeed(ctx, nil)
		return
	}
}

func (uc *UpgradeAPIsController) getClusterUpgrade(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	if clusterName == "" {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "clusterName is empty"))
		return
	}
	if response, err := uc.upgradeClustersHandler.GetClusterUpgrade(ctx, clusterName); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	} else {
		utils.Succeed(ctx, response)
	}
}
