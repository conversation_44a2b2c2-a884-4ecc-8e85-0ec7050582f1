package auth

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/auth"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

func NewAuthRouter() routerutil.ApiController {
	return &authRouter{
		handler: auth.NewHandler(),
	}
}

type authRouter struct {
	handler auth.Handler
}

func (*authRouter) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.ApiV1Group + "/auth")
}
func (r *authRouter) RegisterRouter(group *gin.RouterGroup) {
	group.GET("/currentuser", r.CurrentUser)
}

func (r *authRouter) CurrentUser(ctx *gin.Context) {
	// 一下是操作审记的样例代码
	//audit.WithOperator(ctx, "test-get-current-user", "测试审记-获取当前用户消息:{userName}", map[string]string{
	//	"userName": "xuyunjin",
	//})

	user, err := r.handler.CurrentUser(ctx)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, user)
}
