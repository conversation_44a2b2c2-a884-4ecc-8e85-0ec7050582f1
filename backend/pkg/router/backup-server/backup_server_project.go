package backup_server

import (
	"sort"

	"github.com/gin-gonic/gin"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/backupserver"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

const project = "/project"

func (r BackupServerRouter) appendBackUpServerProject(group *gin.RouterGroup) {
	projectGroup := group.Group(project)
	{
		projectGroup.GET("/:projectId/storageServer", r.getStorageServerListByProjectId)
		projectGroup.GET("/:projectId/storageServer/infos", r.getStorageServerInfoByProjectId)
	}
}

func (r BackupServerRouter) getStorageServerListByProjectId(c *gin.Context) {
	projectId := c.Param("projectId")
	if projectId == "" {
		utils.Failed(c, errors.NewFromCode(errors.Var.ParamError))
		return
	}
	projectStorageServerList, err := r.Project.GetStorageServerListByProjectId(c, projectId)
	if err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.UnKnow, err.Error()))
		return
	}
	sort.Slice(projectStorageServerList, func(i, j int) bool {
		return projectStorageServerList[i].CreatedAt.Before(projectStorageServerList[j].CreatedAt.Time)
	})
	filterList := utils.ParseQueryParams[*backupserver.ProjectStorageServer](c)
	filteredResult, err := filterList.FilterResult(projectStorageServerList)
	utils.Succeed(c, filteredResult)
}

func (r BackupServerRouter) getStorageServerInfoByProjectId(c *gin.Context) {
	projectId := c.Param("projectId")
	if projectId == "" {
		utils.Failed(c, errors.NewFromCode(errors.Var.ParamError))
		return
	}
	storageServerInfos, err := r.Project.GetStorageServerInfoByProjectId(c, projectId)
	if err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.UnKnow, err.Error()))
		return
	}
	utils.Succeed(c, storageServerInfos)
}
