package backup_server

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/backupserver"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

const backupServer = "/backupServer"

type BackupServerRouter struct {
	Resource backupserver.ResourceInterface
	Organ    backupserver.OrganInterface
	Project  backupserver.ProjectInterface
}

func (r BackupServerRouter) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.ApiV1Group + utils.System + backupServer)
}

func (r BackupServerRouter) RegisterRouter(group *gin.RouterGroup) {
	r.appendBackUpServerResource(group)
	r.appendBackUpServerOrgan(group)
	r.appendBackUpServerProject(group)
}

func NewBackupServerRouter() routerutil.ApiController {
	return &BackupServerRouter{
		Resource: backupserver.NewResource(),
		Organ:    backupserver.NewOrgan(),
		Project:  backupserver.NewProject(),
	}
}
