package backup_server

import (
	"sort"

	"github.com/gin-gonic/gin"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/backupserver"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

const organ = "/organ"

func (r BackupServerRouter) appendBackUpServerOrgan(group *gin.RouterGroup) {
	organGroup := group.Group(organ)
	{
		organGroup.GET("/:organId/storageServer", r.getStorageServerListByOrganId)
	}
	{
		organGroup.POST("/:organId/assignProjects", r.assignProjects)
		organGroup.POST("/:organId/unassignProjects", r.unassignProjects)
	}

}

func (r BackupServerRouter) getStorageServerListByOrganId(c *gin.Context) {

	organId := c.<PERSON>("organId")
	organStorageServerList, err := r.Organ.GetStorageServerListByOrganId(c, organId)
	if err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.UnKnow, err.Error()))
		return
	}
	sort.Slice(organStorageServerList, func(i, j int) bool {
		return organStorageServerList[i].CreatedAt.Before(organStorageServerList[j].CreatedAt.Time)
	})
	filterList := utils.ParseQueryParams[*backupserver.OrganStorageServer](c)
	filteredResult, err := filterList.FilterResult(organStorageServerList)
	if err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.UnKnow, "Filter operation failed"))
		return
	}
	utils.Succeed(c, filteredResult)

}

func (r BackupServerRouter) assignProjects(c *gin.Context) {
	organId := c.Param("organId")
	var req backupserver.AssignProjectToBucketReq
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, err.Error()))
		return
	}
	if err := r.Organ.AssignProjectToBucket(c, organId, req); err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.AssignFailed, err.Error()))
		return
	}
	utils.SucceedWithoutData(c)
}

func (r BackupServerRouter) unassignProjects(c *gin.Context) {
	organId := c.Param("organId")
	var req backupserver.UnassignProjectToBucketReq
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, err.Error()))
		return
	}
	if err := r.Organ.UnassignProjectToBucket(c, organId, req); err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.RemoveFailed, err.Error()))
		return
	}
	utils.SucceedWithoutData(c)
}
