package backup_server

import (
	"context"
	"sort"
	"strings"

	"github.com/gin-gonic/gin"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/backupserver"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

const resource = "/resource"

func (r BackupServerRouter) appendBackUpServerResource(group *gin.RouterGroup) {
	resourceGroup := group.Group(resource)
	{
		resourceGroup.GET("/storageServers", r.getStorageServerList)
		resourceGroup.POST("/storageServers", r.createStorageServer)
		resourceGroup.DELETE("/storageServers/:storageServersId", r.deleteStorageServer)
		resourceGroup.PUT("/storageServers/:storageServersId", r.updateStorageServer)
	}
	{
		resourceGroup.GET("/:storageServersId/buckets", r.getBucketList)
		resourceGroup.GET("/getStorageServersByIds", r.getBucketListByIds)
		resourceGroup.POST("/:storageServersId/buckets", r.createBucket)
		resourceGroup.PUT("/:storageServersId/buckets", r.updateBucket)
		resourceGroup.DELETE("/:storageServersId/buckets", r.deleteBucket)
		resourceGroup.GET("/:storageServersId/checkBucket", r.checkBucket)
		resourceGroup.GET("/:storageServersId/bucketQuota", r.getBucketQuota)
	}
	{
		resourceGroup.POST("/:storageServersId/assignBuckets", r.assignBucketToOrgan)
		resourceGroup.POST("/:storageServersId/unassignBuckets", r.unassignBucketToOrgan)
	}
}

func (r BackupServerRouter) updateStorageServer(c *gin.Context) {
	storageServersId := c.Param("storageServersId")
	var req backupserver.StorageServersReq
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, err.Error()))
		return
	}
	if err := r.Resource.UpdateStorageServer(c, storageServersId, req); err != nil {
		switch {
		case strings.Contains(err.Error(), "connection refused"),
			strings.Contains(err.Error(), "S3 API Requests must be made to API port"):
			utils.Failed(c, errors.NewFromCode(errors.Var.CreateBackupServerURLFailed))
		case strings.Contains(err.Error(), "nickname already exists"):
			utils.Failed(c, errors.NewFromCode(errors.Var.BackupServerNameRepeat))
		case strings.Contains(err.Error(), "gave HTTP response"),
			strings.Contains(err.Error(), "gave HTTPs response"):
			utils.Failed(c, errors.NewFromCode(errors.Var.ConnectFailure))
		case strings.Contains(err.Error(), context.DeadlineExceeded.Error()):
			utils.Failed(c, errors.NewFromCode(errors.Var.CreateBackupServerURLFailed))
		case strings.Contains(err.Error(), "Key"),
			strings.Contains(err.Error(), "key"):
			utils.Failed(c, errors.NewFromCode(errors.Var.CreateBackupServerUserOrPasswordFailed))

		default:
			utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.UnKnow, err.Error()))
		}
		return
	}
	utils.Succeed(c, nil)
}

func (r BackupServerRouter) checkBucket(c *gin.Context) {
	storageServersId := c.Param("storageServersId")
	var req backupserver.CheckBucketReq
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, err.Error()))
		return
	}
	if err := r.Resource.CheckBucket(c, storageServersId, req); err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.CheckBucketFailed, err.Error()))
		return
	}
	utils.Succeed(c, nil)
}

func (r BackupServerRouter) getBucketQuota(c *gin.Context) {
	storageServersId := c.Param("storageServersId")
	bucketName := c.Query("bucketName")
	bucketQuota, err := r.Resource.GetBucketQuota(c, storageServersId, bucketName)
	if err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.CheckBucketFailed, err.Error()))
		return
	}
	utils.Succeed(c, *bucketQuota)
}

func (r BackupServerRouter) getOrganList(c *gin.Context) {
	organList, err := r.Resource.GetOrganList(c)
	if err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.UnKnow, err.Error()))
		return
	}
	utils.Succeed(c, organList)
}

func (r BackupServerRouter) getStorageServerList(c *gin.Context) {
	storageList, err := r.Resource.GetStorageServerList(c)
	if err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, err.Error()))
		return
	}
	sort.Slice(storageList, func(i, j int) bool {
		return storageList[i].CreatedAt.After(storageList[j].CreatedAt)
	})
	filter := utils.ParseQueryParams[*backupserver.ResourceStorageServer](c)
	filter.WithFn(func(item *backupserver.ResourceStorageServer) bool {
		nickNames := strings.Split(c.Query("nickName"), ",")
		return len(nickNames) == 0 || func(slice []string, item string) bool {
			for _, v := range slice {
				if strings.Contains(item, v) {
					return true
				}
			}
			return false
		}(nickNames, item.NickName)
	})
	filterList, err := filter.FilterResult(storageList)

	if err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.UnKnow, err.Error()))
		return
	}
	utils.Succeed(c, filterList)

}

func (r BackupServerRouter) createStorageServer(c *gin.Context) {
	var req backupserver.StorageServersReq
	// 解析请求体
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, err.Error()))
		return
	}
	// 校验请求数据
	if err := backupserver.ValidateStorageServerRequest(req); err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, err.Error()))
		return
	}
	// 创建存储服务器
	if err := r.Resource.CreateStorageServer(c, req); err != nil {
		switch {
		case strings.Contains(err.Error(), "connection refused"),
			strings.Contains(err.Error(), "S3 API Requests must be made to API port"):
			utils.Failed(c, errors.NewFromCode(errors.Var.CreateBackupServerURLFailed))
		case strings.Contains(err.Error(), "nickname already exists"):
			utils.Failed(c, errors.NewFromCode(errors.Var.BackupServerNameRepeat))
		case strings.Contains(err.Error(), context.DeadlineExceeded.Error()):
			utils.Failed(c, errors.NewFromCode(errors.Var.CreateBackupServerURLFailed))
		case strings.Contains(err.Error(), "gave HTTP response"),
			strings.Contains(err.Error(), "HTTP"),
			strings.Contains(err.Error(), "HTTPS"),
			strings.Contains(err.Error(), "gave HTTPs response"):
			utils.Failed(c, errors.NewFromCode(errors.Var.CreateBackupServerURLFailed))
		case strings.Contains(err.Error(), "failed to connect to minio"):
			utils.Failed(c, errors.NewFromCode(errors.Var.CreateBackupServerUserOrPasswordFailed))
		case strings.Contains(err.Error(), "Key"),
			strings.Contains(err.Error(), "key"):
			utils.Failed(c, errors.NewFromCode(errors.Var.CreateBackupServerUserOrPasswordFailed))
		case strings.Contains(err.Error(), "already exists"):
			utils.Failed(c, errors.NewFromCode(errors.Var.CreateBackupServerIPAndPortFailed))
		default:
			utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.UnKnow, err.Error()))
		}
		return
	}
	// 成功返回
	utils.SucceedWithoutData(c)
}

func (r BackupServerRouter) deleteStorageServer(c *gin.Context) {
	storageServersId := c.Param("storageServersId")
	flag, err := r.Resource.DeleteStorageServer(c, storageServersId)
	if err != nil {
		switch err.Error() {
		case errors.Var.BackupServerIsUsedByProject.Message:
			utils.Failed(c, errors.NewFromCode(errors.Var.BackupServerIsUsedByProject))
		case errors.Var.BackupServerIsUsedByOrgan.Message:
			utils.Failed(c, errors.NewFromCode(errors.Var.BackupServerIsUsedByOrgan))
		default:
			utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.UnKnow, err.Error()))
		}
		return
	}
	if !flag {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.UnKnow, "删除失败"))
		return
	}
	utils.SucceedWithoutData(c)
}

func (r BackupServerRouter) getBucketList(c *gin.Context) {
	storageServersId := c.Param("storageServersId")
	sortName := c.Query("sort_name")
	sortOrder := c.Query("sort_order")
	storageList, err := r.Resource.GetBucketList(c, storageServersId)
	if err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.UnKnow, err.Error()))
		return
	}

	filter := utils.ParseQueryParams[backupserver.ResourceBucket](c)
	filter.WithFn(func(item backupserver.ResourceBucket) bool {
		names := strings.Split(c.Query("name"), ",")
		return len(names) == 0 || func(slice []string, item string) bool {
			for _, v := range slice {
				if !strings.Contains(item, v) {
					return false
				}
			}
			return true
		}(names, item.Name)
	})
	if sortName == "createTime" {
		if sortOrder == "asc" {
			sort.Slice(storageList.Buckets, func(i, j int) bool {
				return storageList.Buckets[i].CreateTime.Before(storageList.Buckets[j].CreateTime)
			})
		} else if sortOrder == "desc" {
			sort.Slice(storageList.Buckets, func(i, j int) bool {
				return storageList.Buckets[i].CreateTime.After(storageList.Buckets[j].CreateTime)
			})
		}

	}
	filterList, err := filter.FilterResult(storageList.Buckets)
	if err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.UnKnow, err.Error()))
		return
	}
	storageList.Total = len(storageList.Buckets)
	storageList.Buckets = filterList.Items
	storageList.CurrentTotal = len(storageList.Buckets)
	utils.Succeed(c, storageList)
}

func (r BackupServerRouter) getBucketListByIds(c *gin.Context) {
	storageServersIds := c.Query("storageServersIds")
	sortName := c.Query("sort_name")
	sortOrder := c.Query("sort_order")

	ids := strings.Split(storageServersIds, ",")
	res := make([]*backupserver.ResourceBucketList, 0)
	for _, storageServersId := range ids {
		bucketList, err := r.Resource.GetBucketList(c, storageServersId)
		if err != nil {
			utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.UnKnow, err.Error()))
			return
		}

		filter := utils.ParseQueryParams[backupserver.ResourceBucket](c)
		filter.WithFn(func(item backupserver.ResourceBucket) bool {
			names := strings.Split(c.Query("name"), ",")
			return len(names) == 0 || func(slice []string, item string) bool {
				for _, v := range slice {
					if !strings.Contains(item, v) {
						return false
					}
				}
				return true
			}(names, item.Name)
		})
		if sortName == "createTime" {
			if sortOrder == "asc" {
				sort.Slice(bucketList.Buckets, func(i, j int) bool {
					return bucketList.Buckets[i].CreateTime.Before(bucketList.Buckets[j].CreateTime)
				})
			} else if sortOrder == "desc" {
				sort.Slice(bucketList.Buckets, func(i, j int) bool {
					return bucketList.Buckets[i].CreateTime.After(bucketList.Buckets[j].CreateTime)
				})
			}

		}
		filterList, err := filter.FilterResult(bucketList.Buckets)
		if err != nil {
			utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.UnKnow, err.Error()))
			return
		}
		bucketList.Total = len(bucketList.Buckets)
		bucketList.Buckets = filterList.Items
		bucketList.CurrentTotal = len(bucketList.Buckets)
		bucketList.StorageServerId = storageServersId
		res = append(res, bucketList)
	}
	utils.Succeed(c, res)
}

func (r BackupServerRouter) createBucket(c *gin.Context) {
	storageServersId := c.Param("storageServersId")
	var req backupserver.CreateBucketsReq
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, err.Error()))
		return
	}
	err := backupserver.ValidateCreateBucket(req)
	if err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, err.Error()))
		return
	}
	if err := r.Resource.CreateBuckets(c, storageServersId, req); err != nil {
		if err.Error() == errors.Var.BucketNameRepeat.Message {
			utils.Failed(c, errors.NewFromCode(errors.Var.BucketNameRepeat))
			return
		}
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.CreateBucketFailed, err.Error()))
		return
	}
	utils.SucceedWithoutData(c)
}

func (r BackupServerRouter) updateBucket(c *gin.Context) {
	storageServersId := c.Param("storageServersId")
	var req backupserver.UpdateBucketReq
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, err.Error()))
		return
	}
	err := backupserver.ValidateUpdateBucket(req)
	if err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, err.Error()))
		return
	}
	if err := r.Resource.UpdateBucket(c, storageServersId, req); err != nil {
		if strings.Contains(err.Error(), "current limit storage must be greater than") {
			utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.BucketLimiterWithCurrentStorage, err.Error()))
			return
		}
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.UpdateBucketFailed, err.Error()))
		return
	}
	utils.SucceedWithoutData(c)
}

func (r BackupServerRouter) deleteBucket(c *gin.Context) {
	storageServersId := c.Param("storageServersId")
	var req backupserver.DeleteBucketReq
	req.Name = c.Query("name")
	if err := r.Resource.DeleteBucket(c, storageServersId, req); err != nil {
		utils.Failed(c, errors.NewFromCode(errors.Var.DeleteBucketFailed))
		return
	}
	utils.SucceedWithoutData(c)
}

func (r BackupServerRouter) assignBucketToOrgan(c *gin.Context) {
	storageServersId := c.Param("storageServersId")
	var req backupserver.AssignOrganToBucketReq
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, err.Error()))
		return
	}
	err := r.Resource.AssignBucketToOrgan(c, storageServersId, req)
	if err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.AssignFailed, err.Error()))
		return
	}
	utils.SucceedWithoutData(c)
}

func (r BackupServerRouter) unassignBucketToOrgan(c *gin.Context) {
	storageServersId := c.Param("storageServersId")
	var req backupserver.UnassignOrganToBucketReq
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, err.Error()))
		return
	}
	err := r.Resource.UnassignBucketToOrgan(c, storageServersId, req)
	if err != nil {
		if err.Error() == errors.Var.BucketIsUsedByProject.Message {
			utils.Failed(c, errors.NewFromCode(errors.Var.BucketIsUsedByProject))
			return
		}
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.RemoveFailed, err.Error()))
		return
	}
	utils.SucceedWithoutData(c)

}
