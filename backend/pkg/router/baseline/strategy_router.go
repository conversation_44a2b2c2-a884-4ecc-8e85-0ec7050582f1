package baseline

import (
	"fmt"
	"net/http"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	handlers "harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline/helper"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	baselinemodels "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/resources"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

var _ models.PageableResponse[any]

func NewStrategyRouter() routerutil.ApiController {
	if !helper.IsFeatureBaselineEnabled() {
		return &strategyRouter{}
	}
	return &strategyRouter{
		handler:             singletonHandlers.strategyHandler,
		recurringJobHandler: singletonHandlers.recurringDefaultHandler,
		importDataHandler:   singletonHandlers.importDataHandler,
	}
}

type strategyRouter struct {
	handler             handlers.StrategyInterface
	recurringJobHandler handlers.RecurringJobInterface
	importDataHandler   handlers.ImportDataInterface
}

func (s *strategyRouter) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.ApiV1BaselineGroup + "/strategies")
}

func (s *strategyRouter) RegisterRouter(group *gin.RouterGroup) {
	// 查询
	group.GET("", doExecute(s.queryBaselineStrategies))
	// 名称是否已经存在
	group.GET("/name_existed", doExecute(s.getBaselineStrategyNameExisted))
	// 详情
	group.GET("/:id", doExecute(s.getBaselineStrategyDetails))
	// 创建
	group.POST("", doExecute(s.createBaselineStrategy))
	// 更新
	group.PUT("/:id", doExecute(s.updateBaselineStrategy))
	// 更新绑定的基线标准
	group.POST("/:id/standards", doExecute(s.updateBindingBaselineStandards))
	// 删除
	group.DELETE("/:id", doExecute(s.deleteBaselineStrategy))
	// 启用/禁用
	group.POST("/:id/switch", doExecute(s.switchBaselineStrategy))
	// 执行检查任务
	group.POST("/:id/check", doExecute(s.executeCheckJob))
	// 上一次检查任务状态
	group.GET("/:id/last_job_status", doExecute(s.getLastCheckJobStatus))
	// 策略概要
	group.GET("/:id/summary", doExecute(s.getBaselineStrategySummary))
	// 获取集群检查结果
	group.GET("/:id/clusters/check_results", doExecute(s.getClusterCheckResults))
	// 获取标准检查结果
	group.GET("/:id/clusters/:cluster/standards/check_results", doExecute(s.getBaselineStandardCheckResults))
	// 获取标准检查表格报告
	group.GET("/:id/clusters/:cluster/standards/check_results_report", doExecute(s.downloadBaselineStandardCheckResultsReport))
	// 获取单独的检查项检查结果
	group.GET("/:id/clusters/:cluster/standards/:std_id/check_results", doExecute(s.getCheckResults))
	// 获取单独的检查项检查结果报告
	group.GET("/:id/clusters/:cluster/standards/:std_id/check_results_report", doExecute(s.downloadCheckResultReport))
	// 获取定时任务
	group.GET("/recurring_jobs", doExecute(s.queryRecurringJobs))
	// 创建定时任务
	group.POST("/import_builtin_baseline", doExecute(s.importBuiltinBaseline))

}

// queryRecurringJobs 查询定时任务
//
//	@Summary		查询定时任务
//	@Description	查询定时任务
//	@Tags			定时任务
//	@Accept			application/x-www-form-urlencoded
//	@Produce		application/json
//	@Success		200	{object}	utils.Response{code=int,success=bool,data=baselinemodels.ListRecurringJobResponse}
//	@Failure		400	{object}	utils.Response{code=int,success=bool,errorMsg=string}
//	@Failure		500	{object}	utils.Response{code=int,success=bool,errorMsg=string}
//	@Security		jwt
//	@Router			/apis/v1/baselines/strategies/recurring_jobs [get]
func (s *strategyRouter) queryRecurringJobs(c *gin.Context) {
	if resp, err := s.recurringJobHandler.List(c); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	} else {
		utils.Succeed(c, resp)
		return
	}
}

// importBuiltinBaseline 导入内置基线
//
//	@Summary		导入内置基线
//	@Description	从底座获取内置基线，并导入到数据库中
//	@Tags			定时任务
//	@Accept			application/x-www-form-urlencoded
//	@Produce		application/json
//	@Success		200	{object}	utils.Response{code=int,success=bool}
//	@Failure		400	{object}	utils.Response{code=int,success=bool,errorMsg=string}
//	@Failure		500	{object}	utils.Response{code=int,success=bool,errorMsg=string}
//	@Security		jwt
//	@Router			/apis/v1/baselines/strategies/import_builtin_baseline [post]
func (s *strategyRouter) importBuiltinBaseline(c *gin.Context) {
	if err := s.importDataHandler.ImportBuiltinBaseline(c); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	} else {
		utils.Succeed(c, nil)
		return
	}
}

// getBaselineStrategyNameExisted 查询基线策略名称是否存在
//
//	@Summary		查询基线策略名称是否存在
//	@Description	根据名称查询基线策略名称是否存在
//	@Tags			基线策略
//	@Accept			application/x-www-form-urlencoded
//	@Produce		application/json
//	@Param			name	query		string	true	"基线策略名称"
//	@Success		200		{object}	utils.Response{code=int,success=bool,data=baselinemodels.GetBaselineStrategyNameExistedResponse}
//	@Failure		400		{object}	utils.Response{code=int,success=bool,errorMsg=string}
//	@Failure		500		{object}	utils.Response{code=int,success=bool,errorMsg=string}
//	@Security		jwt
//	@Router			/apis/v1/baselines/strategies/name_existed [get]
func (s *strategyRouter) getBaselineStrategyNameExisted(c *gin.Context) {
	var req baselinemodels.GetBaselineStrategyNameExistedRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	if req.Name == "" {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, "name is empty"))
		return
	}
	resp, err := s.handler.GetBaselineStrategyNameExisted(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, resp)
}

// queryBaselineStrategies 查询基线策略列表
//
//	@Summary		查询基线策略
//	@Description	根据名称、是否开启，分页查询基线策略
//	@Tags			基线策略
//	@Accept			application/x-www-form-urlencoded
//	@Produce		application/json
//	@Param			name		query		string																								false	"基线策略"
//	@Param			enable		query		bool																								false	"策略是否开启"
//	@Param			page_num	query		int																									false	"页码, 默认为1"
//	@Param			page_size	query		int																									fasle	"每页大小, 默认为10"
//	@Param			sort_order	query		string																								false	"排序方式 asc 或 desc，默认 desc"
//	@Param			sort_func	query		string																								false	"排序字段比较方式，例如 time, string, number"
//	@Param			sort_name	query		string																								false	"排序字段，通过类似 JSONPath 的方式获取, 默认为 metadata.name"
//	@Param			selector	query		string																								false	"符合查询参数的选择器。例如：精确查询 selector=name=a,namespace=default；模糊查询 selector=name~a,namespace~default；组合查询 selector=name~a,namespace=default"
//	@Success		200			{object}	utils.Response{code=int,success=bool,data=models.PageableResponse[baselinemodels.StrategyItem]{}}	"请求成功，返回基线标准列表"
//	@Failure		400			{object}	utils.Response{code=int,success=bool,errorMsg=string}												"请求参数错误，返回400错误"
//	@Failure		500			{object}	utils.Response{code=int,success=bool,errorMsg=string}												"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/strategies [get]
func (s *strategyRouter) queryBaselineStrategies(c *gin.Context) {
	req := new(baselinemodels.QueryBaselineStrategiesRequest)
	filter := utils.ParseQueryParams[*baselinemodels.StrategyItem](c)
	if err := c.ShouldBindQuery(req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	if c.Query("sort_name") == "" {
		filter.WithSort("updateTime", resources.SortOrderDesc, resources.SortFuncTime)
	}

	req.Filter = filter
	if resp, err := s.handler.QueryBaselineStrategies(c, req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	} else {
		utils.Succeed(c, resp)
		return
	}
}

// getBaselineStrategyDetails 查询基线策略详情
//
//	@Summary		查询基线策略详情
//	@Description	获取指定ID的基线策略详细信息
//	@Tags			基线策略
//	@Accept			application/x-www-form-urlencoded
//	@Produce		application/json
//	@Param			id	path		string																							true	"策略ID"	//	路径参数，基线策略ID
//	@Success		200	{object}	utils.Response{code=int,success=bool,data=baselinemodels.GetBaselineStrategyDetailsResponse}	"请求成功，返回基线策略详情"
//	@Failure		400	{object}	utils.Response{code=int,success=bool,errorMsg=string}											"请求参数错误，返回400错误"
//	@Failure		500	{object}	utils.Response{code=int,success=bool,errorMsg=string}											"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/strategies/{id} [get]
func (s *strategyRouter) getBaselineStrategyDetails(c *gin.Context) {
	req := new(baselinemodels.GetBaselineStrategyDetailsRequest)
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, "id is not int"))
		return
	}
	req.ID = id

	if resp, err := s.handler.GetBaselineStrategyDetails(c, req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	} else {
		utils.Succeed(c, resp)
		return
	}
}

// createBaselineStrategy 创建基线策略
//
//	@Summary		创建新的基线策略
//	@Description	提交数据以创建新的基线策略
//	@Tags			基线策略
//	@Accept			application/json
//	@Produce		application/json
//	@Param			body	body		baselinemodels.CreateBaselineStrategyRequest												true	"基线策略创建请求"	//	请求体，包含创建策略的参数
//	@Success		200		{object}	utils.Response{code=int,success=bool,data=baselinemodels.CreateBaselineStrategyResponse}	"请求成功，返回创建的策略信息"
//	@Failure		400		{object}	utils.Response{code=int,success=bool,errorMsg=string}										"请求参数错误，返回400错误"
//	@Failure		500		{object}	utils.Response{code=int,success=bool,errorMsg=string}										"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/strategies [post]
func (s *strategyRouter) createBaselineStrategy(c *gin.Context) {
	req := new(baselinemodels.CreateBaselineStrategyRequest)
	if err := c.ShouldBindJSON(req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	if resp, err := s.handler.CreateBaselineStrategy(c, req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	} else {
		utils.Succeed(c, resp)
		return
	}
}

// updateBaselineStrategy 更新基线策略
//
//	@Summary		更新已有基线策略
//	@Description	提交数据以更新指定ID的基线策略
//	@Tags			基线策略
//	@Accept			application/json
//	@Produce		application/json
//	@Param			id		path		string																						true	"策略ID"		//	路径参数，基线策略ID
//	@Param			body	body		baselinemodels.UpdateBaselineStrategyRequest												true	"基线策略更新请求"	//	请求体，包含更新策略的参数
//	@Success		200		{object}	utils.Response{code=int,success=bool,data=baselinemodels.UpdateBaselineStrategyResponse}	"请求成功，返回更新后的策略信息"
//	@Failure		400		{object}	utils.Response{code=int,success=bool,errorMsg=string}										"请求参数错误，返回400错误"
//	@Failure		500		{object}	utils.Response{code=int,success=bool,errorMsg=string}										"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/strategies/{id} [put]
func (s *strategyRouter) updateBaselineStrategy(c *gin.Context) {
	req := new(baselinemodels.UpdateBaselineStrategyRequest)
	if err := c.ShouldBindJSON(req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	req.ID = id
	if resp, err := s.handler.UpdateBaselineStrategy(c, req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	} else {
		utils.Succeed(c, resp)
		return
	}
}

// deleteBaselineStrategy 删除基线策略
//
//	@Summary		删除指定ID的基线策略
//	@Description	删除特定ID的基线策略
//	@Tags			基线策略
//	@Accept			application/x-www-form-urlencoded
//	@Produce		application/json
//	@Param			id	path		string																						true	"策略ID"	//	路径参数，基线策略ID
//	@Success		200	{object}	utils.Response{code=int,success=bool,data=baselinemodels.DeleteBaselineStrategyResponse}	"请求成功，返回删除结果"
//	@Failure		400	{object}	utils.Response{code=int,success=bool,errorMsg=string}										"请求参数错误，返回400错误"
//	@Failure		500	{object}	utils.Response{code=int,success=bool,errorMsg=string}										"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/strategies/{id} [delete]
func (s *strategyRouter) deleteBaselineStrategy(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	req := new(baselinemodels.DeleteBaselineStrategyRequest)
	req.ID = id
	if resp, err := s.handler.DeleteBaselineStrategy(c, req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	} else {
		utils.Succeed(c, resp)
		return
	}
}

// updateBindingBaselineStandards 更新基线策略绑定的基线标准
//
//	@Summary		更新基线策略绑定的基线标准
//	@Description	更新基线策略绑定的基线标准
//	@Tags			基线策略
//	@Accept			application/json
//	@Produce		application/json
//	@Param			body	body		baselinemodels.UpdateBindingBaselineStandardsRequest												true	"基线策略更新请求"	//	请求体，包含更新策略的参数
//	@Success		200		{object}	utils.Response{code=int,success=bool,data=baselinemodels.UpdateBindingBaselineStandardsResponse}	"请求成功，返回绑定结果"
//	@Failure		400		{object}	utils.Response{code=int,success=bool,errorMsg=string}												"请求参数错误，返回400错误"
//	@Failure		500		{object}	utils.Response{code=int,success=bool,errorMsg=string}												"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/strategies/{id}/binding_standards [post]
func (s *strategyRouter) updateBindingBaselineStandards(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	req := &baselinemodels.UpdateBindingBaselineStandardsRequest{}
	if err := c.ShouldBindJSON(req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	req.StrategyID = id
	if resp, err := s.handler.UpdateBindingBaselineStandards(c, req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	} else {
		utils.Succeed(c, resp)
		return
	}
}

// switchBaselineStrategy 启用/禁用基线策略
//
//	@Summary		启用或禁用指定ID的基线策略
//	@Description	启用或禁用基线策略
//	@Tags			基线策略
//	@Accept			application/json
//	@Produce		application/json
//	@Param			id		path		string																						true	"策略ID"		//	路径参数，基线策略ID
//	@Param			body	body		baselinemodels.SwitchBaselineStrategyRequest												true	"启用或禁用请求"	//	请求体，包含启用/禁用策略的参数
//	@Success		200		{object}	utils.Response{code=int,success=bool,data=baselinemodels.SwitchBaselineStrategyResponse}	"请求成功，返回策略切换结果"
//	@Failure		400		{object}	utils.Response{code=int,success=bool,errorMsg=string}										"请求参数错误，返回400错误"
//	@Failure		500		{object}	utils.Response{code=int,success=bool,errorMsg=string}										"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/strategies/{id}/switch [post]
func (s *strategyRouter) switchBaselineStrategy(c *gin.Context) {
	req := new(baselinemodels.SwitchBaselineStrategyRequest)
	if err := c.ShouldBindJSON(req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	if resp, err := s.handler.SwitchBaselineStrategy(c, req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	} else {
		utils.Succeed(c, resp)
		return
	}
}

// executeCheckJob 执行策略检查任务
//
//	@Summary		执行基线策略检查任务
//	@Description	执行指定ID的基线策略检查任务
//	@Tags			基线策略
//	@Accept			application/json
//	@Produce		application/json
//	@Param			id		path		string																				true	"策略ID"	//	路径参数，基线策略ID
//	@Param			body	body		baselinemodels.ExecuteCheckJobRequest												true	"执行检查任务请求"
//	@Success		200		{object}	utils.Response{code=int,success=bool,data=baselinemodels.ExecuteCheckJobResponse}	"请求成功，返回检查任务执行结果"
//	@Failure		500		{object}	utils.Response{code=int,success=bool,errorMsg=string}								"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/strategies/{id}/check [post]
func (s *strategyRouter) executeCheckJob(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	req := new(baselinemodels.ExecuteCheckJobRequest)
	if err := c.ShouldBindJSON(req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	req.StrategyID = id
	if resp, err := s.handler.ExecuteCheckJob(c, req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	} else {
		utils.Succeed(c, resp)
		return
	}
}

// getLastJobStatus 获取最新的任务结果
//
//	@Summary		获取最新的任务结果
//	@Description	执行指定ID的基线策略最新的任务结果
//	@Tags			基线策略
//	@Accept			application/json
//	@Produce		application/json
//	@Param			id	path		string																					true	"策略ID"	//	路径参数，基线策略ID
//	@Success		200	{object}	utils.Response{code=int,success=bool,data=baselinemodels.GetLastCheckJobStatusResponse}	"请求成功，返回检查任务执行结果"
//	@Failure		500	{object}	utils.Response{code=int,success=bool,errorMsg=string}									"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/strategies/{id}/last_job_status [get]
func (s *strategyRouter) getLastCheckJobStatus(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	req := new(baselinemodels.GetLastCheckJobStatusRequest)
	req.ID = id
	if resp, err := s.handler.GetLastCheckJobStatus(c, req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	} else {
		utils.Succeed(c, resp)
		return
	}
}

// getBaselineStrategySummary 获取基线策略概要
//
//	@Summary		获取基线策略的概要信息
//	@Description	获取基线策略的总结或概览信息
//	@Param			id	path	string	true	"策略ID"	//	路径参数，基线策略ID
//	@Tags			基线策略
//	@Accept			application/x-www-form-urlencoded
//	@Produce		application/json
//	@Success		200	{object}	utils.Response{code=int,success=bool,data=baselinemodels.GetBaselineStrategySummaryResponse}	"请求成功，返回基线策略概要"
//	@Failure		500	{object}	utils.Response{code=int,success=bool,errorMsg=string}											"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/strategies/{id}/summary [get]
func (s *strategyRouter) getBaselineStrategySummary(c *gin.Context) {
	req := new(baselinemodels.GetBaselineStrategySummaryRequest)
	if err := c.ShouldBindQuery(req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	req.ID = id
	if resp, err := s.handler.GetBaselineStrategySummary(c, req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	} else {
		utils.Succeed(c, resp)
		return
	}
}

// getClusterCheckResults 获取集群检查结果
//
//	@Summary		获取指定集群的检查结果
//	@Description	获取指定ID的基线策略在指定集群上的检查结果
//	@Tags			基线策略
//	@Accept			application/x-www-form-urlencoded
//	@Produce		application/json
//	@Param			id			path		string																										true	"策略ID"	//	路径参数，基线策略ID
//	@Param			clusterName	query		string																										false	"集群名称"
//	@Success		200			{object}	utils.Response{code=int,success=bool,data=models.PageableResponse[baselinemodels.ClusterCheckResultItem]{}}	"请求成功，返回集群检查结果"
//	@Failure		500			{object}	utils.Response{code=int,success=bool,errorMsg=string}														"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/strategies/{id}/clusters/check_results [get]
func (s *strategyRouter) getClusterCheckResults(c *gin.Context) {
	req := new(baselinemodels.GetClusterCheckResultsRequest)
	if err := c.ShouldBindQuery(req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	req.StrategyID = id
	req.Filter.WithFn(func(item *baselinemodels.ClusterCheckResultItem) bool {
		return strings.Contains(item.ClusterName, c.Query("clusterName"))
	})
	if resp, err := s.handler.GetClusterCheckResults(c, req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	} else {
		utils.Succeed(c, resp)
		return
	}
}

func (s *strategyRouter) parseGetBaselineStandardCheckResultsRequest(c *gin.Context) (*baselinemodels.GetBaselineStandardCheckResultsRequest, error) {
	req := new(baselinemodels.GetBaselineStandardCheckResultsRequest)
	if err := c.ShouldBindQuery(req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return nil, err
	}
	req.ClusterName = c.Param("cluster")
	strId, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		return nil, err
	}
	req.StrategyID = strId
	req.Filter = utils.ParseQueryParams[*baselinemodels.StandardCheckResultItem](c)
	req.Filter.WithFn(func(item *baselinemodels.StandardCheckResultItem) bool {
		if req.StandardName != "" && !strings.Contains(item.Name, req.StandardName) {
			return false
		}
		return true
	})
	req.Filter.WithFn(func(item *baselinemodels.StandardCheckResultItem) bool {
		if req.ClusterName != "" && !strings.Contains(item.ClusterName, req.ClusterName) {
			return false
		}
		return true
	})
	return req, nil
}

// getBaselineStandardCheckResults 获取基线标准检查结果
//
//	@Summary		获取指定基线标准的检查结果
//	@Description	获取指定ID的基线策略下特定基线标准的检查结果
//	@Tags			基线策略
//	@Accept			application/x-www-form-urlencoded
//	@Produce		application/json
//	@Param			id				path		string																											true	"策略ID"	//	路径参数，基线策略ID
//	@Param			cluster			path		string																											true	"集群"
//	@Param			standardName	query		string																											false	"基线标准名称"
//	@Param			clusterName		query		string																											false	"集群名称"
//	@Param			page_num		query		int																												false	"页码, 默认为1"
//	@Param			page_size		query		int																												false	"每页大小, 默认为10"
//	@Param			sort_order		query		string																											false	"排序方式 asc 或 desc，默认 desc"
//	@Param			sort_func		query		string																											false	"排序字段比较方式，例如 time, string, number"
//	@Param			sort_name		query		string																											false	"排序字段，通过类似 JSONPath 的方式获取, 默认为 metadata.name"
//	@Param			selector		query		string																											false	"符合查询参数的选择器。例如：精确查询 selector=name=a,namespace=default；模糊查询 selector=name~a,namespace~default；组合查询 selector=name~a,namespace=default"
//	@Success		200				{object}	utils.Response{code=int,success=bool,data=models.PageableResponse[baselinemodels.StandardCheckResultItem]{}}	"请求成功，返回基线标准检查结果"
//	@Failure		500				{object}	utils.Response{code=int,success=bool,errorMsg=string}															"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/strategies/{id}/clusters/{cluster}/standards/check_results [get]
func (s *strategyRouter) getBaselineStandardCheckResults(c *gin.Context) {
	req, err := s.parseGetBaselineStandardCheckResultsRequest(c)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	if resp, err := s.handler.GetBaselineStandardCheckResults(c, req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	} else {
		utils.Succeed(c, resp)
		return
	}

}

// downloadBaselineStandardCheckResultsReport 获取基线标准检查结果报告
//
//	@Summary		获取基线标准检查结果报告
//	@Description	获取指定ID的基线策略下特定基线标准的检查结果报告
//	@Tags			基线策略
//	@Produce		application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
//	@Produce		application/json
//	@Param			id				path		string	true	"策略ID"	//	路径参数，基线策略ID
//	@Param			cluster			path		string	true	"集群"
//	@Param			standardName	query		string	false	"基线标准名称"
//	@Param			clusterName		query		string	false	"集群名称"
//	@Param			page_num		query		int		false	"页码, 默认为1"
//	@Param			page_size		query		int		false	"每页大小, 默认为10"
//	@Param			sort_order		query		string	false	"排序方式 asc 或 desc，默认 desc"
//	@Param			sort_func		query		string	false	"排序字段比较方式，例如 time, string, number"
//	@Param			sort_name		query		string	false	"排序字段，通过类似 JSONPath 的方式获取, 默认为 metadata.name"
//	@Param			selector		query		string	false	"符合查询参数的选择器。例如：精确查询 selector=name=a,namespace=default；模糊查询 selector=name~a,namespace~default；组合查询 selector=name~a,namespace=default"
//	@Success		200				{file}		application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
//	@Failure		500				{object}	utils.Response{code=int,success=bool,errorMsg=string}	"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/strategies/{id}/clusters/{cluster}/standards/check_results_report [get]
func (s *strategyRouter) downloadBaselineStandardCheckResultsReport(c *gin.Context) {
	req, err := s.parseGetBaselineStandardCheckResultsRequest(c)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	downloadReq := baselinemodels.DownloadBaselineStandardCheckResultsReportRequest(*req)
	resp, err := s.handler.DownloadBaselineStandardCheckResultsReport(c, &downloadReq)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", resp.FileName))
	c.Data(http.StatusOK, baselinemodels.ApplicationSheetContentType, resp.FileContent)
}

func (s *strategyRouter) parseGetCheckResultsRequest(c *gin.Context) (*baselinemodels.GetCheckResultsRequest, error) {
	req := new(baselinemodels.GetCheckResultsRequest)
	if err := c.ShouldBind(req); err != nil {
		return nil, err
	}
	req.ClusterName = c.Param("cluster")
	stdId, err := strconv.ParseInt(c.Param("std_id"), 10, 64)
	if err != nil {
		return nil, err
	}
	strId, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		return nil, err
	}
	req.StandardID = stdId
	req.StrategyID = strId
	req.Filter = utils.ParseQueryParams[*baselinemodels.CheckResultItem](c)
	req.Filter.WithFn(func(item *baselinemodels.CheckResultItem) bool {
		if req.ClusterName != "" && !strings.Contains(item.ClusterName, req.ClusterName) {
			return false
		}
		return true
	})
	req.Filter.WithFn(func(item *baselinemodels.CheckResultItem) bool {
		if req.CheckName != "" && !strings.Contains(item.Name, req.CheckName) {
			return false
		}
		return true
	})
	return req, nil
}

// getCheckResults 获取单独检查项检查结果
//
//	@Summary		获取单独检查项的检查结果
//	@Description	获取特定ID的基线策略下单独检查项的检查结果
//	@Tags			基线策略
//	@Accept			application/x-www-form-urlencoded
//	@Produce		application/json
//	@Param			id			path		string																									true	"策略ID"	//	路径参数，基线策略ID
//	@Param			cluster		path		string																									true	"集群"
//	@Param			std_id		path		string																									true	"标准ID"	//	路径参数，标准ID
//	@Param			clusterName	query		string																									false	"集群名称"	//	精确
//	@Param			checkName	query		string																									false	"检查项名称"	//	模糊
//	@Param			page_num	query		int																										false	"页码, 默认为1"
//	@Param			page_size	query		int																										false	"每页大小, 默认为10"
//	@Param			sort_order	query		string																									false	"排序方式 asc 或 desc，默认 desc"
//	@Param			sort_func	query		string																									false	"排序字段比较方式，例如 time, string, number"
//	@Param			sort_name	query		string																									false	"排序字段，通过类似 JSONPath 的方式获取, 默认为 metadata.name"
//	@Param			selector	query		string																									false	"符合查询参数的选择器。例如：精确查询 selector=name=a,namespace=default；模糊查询 selector=name~a,namespace~default；组合查询 selector=name~a,namespace=default"
//	@Success		200			{object}	utils.Response{code=int,success=bool,data=models.PageableResponse[baselinemodels.CheckResultItem]{}}	"请求成功，返回单独检查项的检查结果"
//	@Failure		500			{object}	utils.Response{code=int,success=bool,errorMsg=string}													"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/strategies/{id}/clusters/{cluster}/standards/{std_id}/check_results [get]
func (s *strategyRouter) getCheckResults(c *gin.Context) {
	req, err := s.parseGetCheckResultsRequest(c)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	if resp, err := s.handler.GetCheckResults(c, req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	} else {
		utils.Succeed(c, resp)
		return
	}
}

// downloadCheckResultReport 获取单独检查项检查结果文件
//
//	@Summary		获取单独检查项检查结果文件
//	@Description	获取特定ID的基线策略下单独检查项的检查结果报告文件
//	@Tags			基线策略
//	@Accept			application/x-www-form-urlencoded
//	@Produce		application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
//	@Param			id			path		string	true	"策略ID"	//	路径参数，基线策略ID
//	@Param			cluster		path		string	true	"集群"
//	@Param			std_id		path		string	true	"标准ID"	//	路径参数，标准ID
//	@Param			clusterName	query		string	false	"集群名称"	//	精确
//	@Param			checkName	query		string	false	"检查项名称"	//	模糊
//	@Param			page_num	query		int		false	"页码, 默认为1"
//	@Param			page_size	query		int		false	"每页大小, 默认为10"
//	@Param			sort_order	query		string	false	"排序方式 asc 或 desc，默认 desc"
//	@Param			sort_func	query		string	false	"排序字段比较方式，例如 time, string, number"
//	@Param			sort_name	query		string	false	"排序字段，通过类似 JSONPath 的方式获取, 默认为 metadata.name"
//	@Param			selector	query		string	false	"符合查询参数的选择器。例如：精确查询 selector=name=a,namespace=default；模糊查询 selector=name~a,namespace~default；组合查询 selector=name~a,namespace=default"
//	@Success		200			{file}		application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
//	@Failure		500			{object}	utils.Response{code=int,success=bool,errorMsg=string}	"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/strategies/{id}/clusters/{cluster}/standards/{std_id}/check_results_report [get]
func (s *strategyRouter) downloadCheckResultReport(c *gin.Context) {
	req, err := s.parseGetCheckResultsRequest(c)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	downloadReq := baselinemodels.DownloadCheckResultsReportRequest(*req)
	resp, err := s.handler.DownloadCheckResultsReport(c, &downloadReq)
	if err != nil {
		return
	}
	c.Header("Content-Disposition", fmt.Sprintf("attachment; filename=%s", resp.FileName))
	c.Data(http.StatusOK, baselinemodels.ApplicationSheetContentType, resp.FileContent)
}
