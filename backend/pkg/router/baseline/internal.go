package baseline

import (
	"context"

	"github.com/gin-gonic/gin"
	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/database"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	app_management "harmonycloud.cn/unifiedportal/portal/backend/pkg/feign/app-management"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/addon"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline/helper"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline/infra"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

// doExecute 执行基线检查功能
// 如果基线检查功能开启，则执行真实方法
// 如果基线检查功能未开启，则返回错误
func doExecute(f func(c *gin.Context)) gin.HandlerFunc {
	return func(c *gin.Context) {
		if helper.IsFeatureBaselineEnabled() {
			f(c)
		} else {
			utils.Failed(c, errors.NewFromCode(errors.Var.BaselineFeatureNotEnabled))
		}
	}
}

var (
	singletonHandlers *internalHandlers
)

// internalHandlers 所有baseline相关的handler集合
type internalHandlers struct {
	strategyHandler         baseline.StrategyInterface
	strategyTaskManager     *baseline.StrategyTaskManager
	recurringDefaultHandler baseline.RecurringJobInterface
	strategyJobHandler      baseline.StrategyJobInterface
	standardHandler         baseline.StandardInterface
	checkerHandler          baseline.CheckerInterface
	importDataHandler       baseline.ImportDataInterface
	filerHandler            baseline.CheckerFileInterface
}

// InitHandlers 初始化所有handler的配置项

// GetHandlers 获取所有handlers
func GetHandlers() *internalHandlers {
	return singletonHandlers
}

// InitializeHandlers 初始化所有handlers
func InitializeHandlers() {
	if singletonHandlers != nil {
		return
	}

	h := &internalHandlers{}
	db := database.CaasDB
	rds := database.RDS
	// 初始化FileHandler
	ampService := app_management.NewService()
	h.filerHandler = baseline.NewAmpFileHandler(*ampService)
	//h.filerHandler = baseline.NewMinioFileHandler()

	// 初始化ImportDataHandler

	// 初始化 adapter
	baselineAdapter := infra.NewV1BaselineAdapter()
	checkerAdapter := infra.NewV1CheckerAdapter()
	monitorAdapter := infra.NewV1MonitorAdapter()

	addonHandler := addon.NewHandler()

	cronInstance := cron.New()

	h.importDataHandler = baseline.NewImportDataHandler(cronInstance, db, rds, h.filerHandler)

	// 初始化StrategyJobHandler
	h.strategyJobHandler = baseline.NewStrategyJobHandler(
		cronInstance, db, rds,
		baselineAdapter,
		checkerAdapter,
		monitorAdapter,
		addonHandler)

	recurringCron := cron.New(cron.WithSeconds())

	// 初始化RecurringDefaultHandler
	h.recurringDefaultHandler = baseline.NewDefaultRecurringHandler(recurringCron, db, rds, h.strategyJobHandler)

	// 初始化StrategyTaskManager
	h.strategyTaskManager = baseline.NewTaskManager(db, rds, h.recurringDefaultHandler)

	// 初始化StrategyHandler
	h.strategyHandler = baseline.NewStrategyHandler(
		db,
		baselineAdapter,
		monitorAdapter,
		addonHandler,
		h.strategyJobHandler,
		h.strategyTaskManager,
	)

	go func() {
		if err := h.importDataHandler.Strat(context.Background()); err != nil {
			return
		}
	}()
	go func() {

		cronInstance.Start()
		recurringCron.Start()
	}()

	// 初始化BaselineHandler
	h.standardHandler = baseline.NewStandardHandler(db)

	// 初始化CheckerHandler
	h.checkerHandler = baseline.NewCheckerHandler(db)

	singletonHandlers = h

	// 启动任务管理器
	if err := h.strategyTaskManager.Start(context.Background()); err != nil {
		logger.GetLogger().Error("failed to start strategy task manager", zap.Error(err))
	}
}
