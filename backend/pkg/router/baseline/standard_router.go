package baseline

import (
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	handlers "harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline/helper"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	baselinemodels "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/resources"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

var _ models.PageableResponse[any]

type standardRouter struct {
	standardHandler handlers.StandardInterface
}

func NewStandardRouter() router.ApiController {
	if !helper.IsFeatureBaselineEnabled() {
		return &standardRouter{}
	}
	return &standardRouter{
		standardHandler: singletonHandlers.standardHandler,
	}
}

func (b *standardRouter) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.ApiV1BaselineGroup + "/standards")
}

func (b *standardRouter) RegisterRouter(group *gin.RouterGroup) {
	// 基线标准路由
	// 查询
	group.GET("", doExecute(b.queryBaselines))
	// 名称是否已经存在
	group.GET("/name_existed", doExecute(b.getBaselineNameExisted))
	// 详情
	group.GET("/:id", doExecute(b.getBaselineDetails))
	// 获取当前基线标准下的基线检查项列表
	group.GET("/:id/checkers", doExecute(b.getBaselineCheckers))
	// 创建
	group.POST("", doExecute(b.createBaseline))
	// 更新
	group.PUT("/:id", doExecute(b.editBaseline))
	// 删除
	group.DELETE("/:id", doExecute(b.deleteBaseline))
	// 获取自定义检查项关联的基线标准
	group.GET(":id/strategies", doExecute(b.queryAssociatedStrategies))
	// 更新绑定关系
	group.POST(":id/binding_strategies", doExecute(b.updateBindingStrategies))

}

// getBaselineNameExisted 获取基线标准名称是否已经存在
//
//	@Summary		获取基线标准名称是否已经存在
//	@Description	获取基线标准名称是否已经存在
//	@Tags			基线标准
//	@Accept			application/x-www-form-urlencoded
//	@Produce		application/json
//	@Param			name	query		string	true	"基线标准名称"
//	@Success		200		{object}	utils.Response{code=int,success=bool,data=baselinemodels.GetBaselineNameExistedResponse}
//	@Failure		400		{object}	utils.Response{code=int,success=bool,errorMsg=string}
//	@Failure		500		{object}	utils.Response{code=int,success=bool,errorMsg=string}
//	@Security		jwt
//	@Router			/apis/v1/baselines/standards/name_existed [get]
func (b *standardRouter) getBaselineNameExisted(c *gin.Context) {
	var req baselinemodels.GetBaselineNameExistedRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	if req.Name == "" {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, "name is empty"))
		return
	}
	resp, err := b.standardHandler.GetBaselineNameExisted(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, resp)
}

// queryBaselines 查询基线标准列表
//
//	@Summary		查询基线标准
//	@Description	根据名称、分类名称和分类是否内置，分页查询基线标准
//	@Tags			基线标准
//	@Accept			application/x-www-form-urlencoded
//	@Produce		application/json
//	@Param			name			query		string																								false	"基线标准名称"
//	@Param			categoryId		query		int																									false	"分类ID,精确查询"
//	@Param			categoryName	query		string																								false	"分类名称"
//	@Param			categoryBuiltin	query		bool																								false	"分类是否内置"
//	@Param			builtin			query		bool																								false	"是否内置"
//	@Param			page_num		query		int																									false	"页码, 默认为1"
//	@Param			page_size		query		int																									false	"每页大小, 默认为10"
//	@Param			sort_order		query		string																								false	"排序方式 asc 或 desc，默认 desc"
//	@Param			sort_func		query		string																								false	"排序字段比较方式，例如 time, string, number"
//	@Param			sort_name		query		string																								false	"排序字段，通过类似 JSONPath 的方式获取, 默认为 metadata.name"
//	@Param			selector		query		string																								false	"符合查询参数的选择器。例如：精确查询 selector=name=a,namespace=default；模糊查询 selector=name~a,namespace~default；组合查询 selector=name~a,namespace=default"
//	@Success		200				{object}	utils.Response{code=int,success=bool,data=models.PageableResponse[baselinemodels.StandardItem]{}}	"请求成功，返回基线标准列表"
//	@Failure		400				{object}	utils.Response{code=int,success=bool,errorMsg=string}												"请求参数错误，返回400错误"
//	@Failure		500				{object}	utils.Response{code=int,success=bool,errorMsg=string}												"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/standards [get]
func (b *standardRouter) queryBaselines(c *gin.Context) {
	var req baselinemodels.QueryBaselinesRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	filter := utils.ParseQueryParams[*baselinemodels.StandardItem](c)
	if c.Query("sort_name") == "" {
		filter.WithSort("updateTime", resources.SortOrderDesc, resources.SortFuncTime)
	}
	// 添加模糊查询逻辑
	if req.Name != "" {
		filter.WithFn(func(item *baselinemodels.StandardItem) bool {
			return strings.Contains(item.Name, req.Name) // 判断 `name` 是否包含指定值
		})
	}
	// 添加精准查询逻辑
	if req.CategoryName != "" {
		filter.WithFn(func(item *baselinemodels.StandardItem) bool {
			return item.CategoryName == req.CategoryName // 判断 `categoryName` 是否等于指定值
		})
	}
	req.Filter = filter
	resp, err := b.standardHandler.QueryBaselines(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, resp)
}

// getBaselineDetails 查询基线标准详情
//
//	@Summary		查询基线标准详情
//	@Description	根据基线标准ID获取详情
//	@Tags			基线标准
//	@Accept			application/x-www-form-urlencoded
//	@Produce		application/json
//	@Param			id			path		int																						true	"基线标准ID"
//	@Param			parentId	query		int																						false	"父ID"
//	@Param			riskLevel	query		string																					false	"风险级别"
//	@Param			name		query		string																					false	"检查项名称"
//	@Param			page_num	query		int																						false	"页码, 默认为1"
//	@Param			page_size	query		int																						false	"每页大小, 默认为10"
//	@Success		200			{object}	utils.Response{code=int,success=bool,data=baselinemodels.GetBaselineDetailsResponse}	"请求成功，返回基线标准详情"
//	@Failure		400			{object}	utils.Response{code=int,success=bool,errorMsg=string}									"请求参数错误，返回400错误"
//	@Failure		404			{object}	utils.Response{code=int,success=bool,errorMsg=string}									"未找到基线标准，返回404错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/standards/{id} [get]
func (b *standardRouter) getBaselineDetails(c *gin.Context) {
	var req baselinemodels.GetBaselineDetailsRequest
	idParam := c.Param("id") // 获取路径参数
	id, err := strconv.ParseInt(idParam, 10, 64)
	if err != nil || id <= 0 {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, "id must be a positive integer"))
		return
	}
	if err := c.ShouldBind(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	req.ID = id
	filter := utils.ParseQueryParams[*baselinemodels.StandardCheckItem](c)
	// 添加精准查询逻辑
	if req.RiskLevel != "" {
		filter.WithFn(func(item *baselinemodels.StandardCheckItem) bool {
			return item.RiskLevel == req.RiskLevel
		})
	}
	// 添加模糊查询逻辑
	if req.Name != "" {
		filter.WithFn(func(item *baselinemodels.StandardCheckItem) bool {
			return strings.Contains(item.Name, req.Name)
		})
	}
	req.CheckFilter = filter
	resp, err := b.standardHandler.GetBaselineDetails(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, resp)
}

// getBaselineCheckers 查询基线标准详情
//
//	@Summary		查询基线标准详情
//	@Description	根据基线标准ID获取详情
//	@Tags			基线标准
//	@Accept			application/x-www-form-urlencoded
//	@Produce		application/json
//	@Param			id			path		int																						true	"基线标准ID"
//	@Param			checkIds	query		string																					false	"如果传空表示全部，传 1,2,3 以逗号分隔格式获取对应的1,2,3 三个检查项"
//	@Success		200			{object}	utils.Response{code=int,success=bool,data=baselinemodels.GetBaselineCheckersResponse}	"请求成功，返回基线标准详情"
//	@Failure		400			{object}	utils.Response{code=int,success=bool,errorMsg=string}									"请求参数错误，返回400错误"
//	@Failure		404			{object}	utils.Response{code=int,success=bool,errorMsg=string}									"未找到基线标准，返回404错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/standards/{id}/checkers [get]
func (b *standardRouter) getBaselineCheckers(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, "id is not int"))
		return
	}
	var checkIds []int64
	lo.ForEach(strings.Split(c.Query("checkIds"), ","), func(item string, index int) {
		parseInt, err := strconv.ParseInt(item, 10, 64)
		if err != nil {
			logger.GetSugared().Warnf("Parse checkIds error, %s", item)
			return
		}
		checkIds = append(checkIds, parseInt)
	})
	req := baselinemodels.GetBaselineCheckersRequest{ID: id, CheckIds: checkIds}
	resp, err := b.standardHandler.GetBaselineCheckers(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, resp)

}

// createBaseline 创建基线标准
//
//	@Summary		创建基线标准
//	@Description	创建新的基线标准
//	@Tags			基线标准
//	@Accept			application/json
//	@Produce		application/json
//	@Param			request	body		baselinemodels.CreateBaselineRequest												true	"创建基线标准的请求体"
//	@Success		200		{object}	utils.Response{code=int,success=bool,data=baselinemodels.CreateBaselineResponse}	"请求成功，返回新创建的基线标准信息"
//	@Failure		400		{object}	utils.Response{code=int,success=bool,errorMsg=string}								"请求参数错误，返回400错误"
//	@Failure		500		{object}	utils.Response{code=int,success=bool,errorMsg=string}								"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/standards [post]
func (b *standardRouter) createBaseline(c *gin.Context) {
	var req baselinemodels.CreateBaselineRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	resp, err := b.standardHandler.CreateBaseline(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, resp)
}

// editBaseline 更新基线标准
//
//	@Summary		更新基线标准
//	@Description	根据基线标准ID更新基线标准信息
//	@Tags			基线标准
//	@Accept			application/json
//	@Produce		application/json
//	@Param			id		path		int																					true	"基线标准ID"
//	@Param			request	body		baselinemodels.UpdateBaselineRequest												true	"更新基线标准的请求体"
//	@Success		200		{object}	utils.Response{code=int,success=bool,data=baselinemodels.UpdateBaselineResponse}	"请求成功，返回更新后的基线标准信息"
//	@Failure		400		{object}	utils.Response{code=int,success=bool,errorMsg=string}								"请求参数错误，返回400错误"
//	@Failure		404		{object}	utils.Response{code=int,success=bool,errorMsg=string}								"未找到基线标准，返回404错误"
//	@Failure		500		{object}	utils.Response{code=int,success=bool,errorMsg=string}								"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/standards/{id} [put]
func (b *standardRouter) editBaseline(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, "id is not int"))
		return
	}
	var req baselinemodels.UpdateBaselineRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	req.ID = id
	resp, err := b.standardHandler.UpdateBaseline(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, resp)
}

// deleteBaseline 删除基线标准
//
//	@Summary		删除基线标准
//	@Description	根据基线标准ID删除基线标准
//	@Tags			基线标准
//	@Accept			application/x-www-form-urlencoded
//	@Produce		application/json
//	@Param			id	path		int																					true	"基线标准ID"
//	@Success		200	{object}	utils.Response{code=int,success=bool,data=baselinemodels.DeleteBaselineResponse}	"请求成功，返回删除结果"
//	@Failure		400	{object}	utils.Response{code=int,success=bool,errorMsg=string}								"请求参数错误，返回400错误"
//	@Failure		404	{object}	utils.Response{code=int,success=bool,errorMsg=string}								"未找到基线标准，返回404错误"
//	@Failure		500	{object}	utils.Response{code=int,success=bool,errorMsg=string}								"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/standards/{id} [delete]
func (b *standardRouter) deleteBaseline(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, "id is not int"))
		return
	}
	req := baselinemodels.DeleteBaselineRequest{ID: id}
	resp, err := b.standardHandler.DeleteBaseline(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, resp)
}

// queryAssociatedStrategies 查询关联的基线策略
//
//	@Summary		查询关联的基线策略
//	@Description	获取与指定标准ID相关联的策略列表
//	@Tags			基线标准
//	@Accept			application/json
//	@Produce		application/json
//	@Param			id	path		int																							true	"标准ID"
//	@Success		200	{object}	utils.Response{code=int,success=bool,data=baselinemodels.QueryAssociatedStrategiesResponse}	"请求成功，返回策略列表"
//	@Failure		400	{object}	utils.Response{code=int,success=bool,errorMsg=string}										"请求参数错误，返回400错误"
//	@Failure		500	{object}	utils.Response{code=int,success=bool,errorMsg=string}										"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/standards/{id}/strategies [get]
func (b *standardRouter) queryAssociatedStrategies(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, "id is not int"))
		return
	}
	req := baselinemodels.QueryAssociatedStrategiesRequest{ID: id}
	resp, err := b.standardHandler.QueryAssociatedStrategies(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, resp)
}

// updateBindingStrategies 更新绑定关系
//
//	@Summary		更新绑定关系
//	@Description	更新绑定关系
//	@Tags			基线标准
//	@Accept			application/json
//	@Produce		application/json
//	@Param			id		path		int																							true	"标准ID"
//	@Param			request	body		baselinemodels.UpdateBindingStrategiesResponse												true	"更新基线标准的请求体"
//	@Success		200		{object}	utils.Response{code=int,success=bool,data=baselinemodels.UpdateBindingStrategiesResponse}	"请求成功，返回策略列表"
//	@Failure		400		{object}	utils.Response{code=int,success=bool,errorMsg=string}										"请求参数错误，返回400错误"
//	@Failure		500		{object}	utils.Response{code=int,success=bool,errorMsg=string}										"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/standards/{id}/binding_strategies [post]
func (b *standardRouter) updateBindingStrategies(c *gin.Context) {
	req := baselinemodels.UpdateBindingStrategiesRequest{}
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
	}
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, "id is not int"))
		return
	}
	req.ID = id
	resp, err := b.standardHandler.UpdateBindingStrategies(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, resp)
}
