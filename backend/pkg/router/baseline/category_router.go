package baseline

import (
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	handlers "harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline/helper"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	baselinemodels "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/resources"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

var _ models.PageableResponse[any]

type categoryRouter struct {
	standardHandler handlers.StandardInterface
}

func NewCategoryRouter() router.ApiController {
	if !helper.IsFeatureBaselineEnabled() {
		return &categoryRouter{}
	}
	return &categoryRouter{
		standardHandler: singletonHandlers.standardHandler,
	}
}

func (r *categoryRouter) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.ApiV1BaselineGroup + "/standard_categories")
}

func (r *categoryRouter) RegisterRouter(group *gin.RouterGroup) {
	// 聚和查询基线列表
	group.GET("/standards", doExecute(r.queryCategoryStandards))
	// 名称是否已经存在
	group.GET("/name_existed", doExecute(r.getCategoryNameExisted))
	// 查询
	group.GET("", doExecute(r.queryBaselineCategories))
	// 创建
	group.POST("", doExecute(r.createBaselineCategory))
	// 更新
	group.PUT("/:id", doExecute(r.editBaselineCategory))
	// 删除
	group.DELETE("/:id", doExecute(r.deleteBaselineCategory))
}

// getCategoryNameExisted 查询基线分类名称是否存在
//
//	@Summary		查询基线分类名称是否存在
//	@Description	查询基线分类名称是否存在
//	@Tags			基线标准分类
//	@Accept			application/x-www-form-urlencoded
//	@Produce		application/json
//	@Param			name	query		string	true	"基线分类名称"
//	@Success		200		{object}	utils.Response{code=int,success=bool,data=baselinemodels.GetCategoryNameExistedResponse}
//	@Failure		400		{object}	utils.Response{code=int,success=bool,errorMsg=string}
//	@Failure		500		{object}	utils.Response{code=int,success=bool,errorMsg=string}
//	@Security		jwt
//	@Router			/apis/v1/baselines/standard_categories/name_existed [get]
func (r *categoryRouter) getCategoryNameExisted(c *gin.Context) {
	var req baselinemodels.GetCategoryNameExistedRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	if req.Name == "" {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, "name is empty"))
		return
	}
	resp, err := r.standardHandler.GetCategoryNameExisted(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, resp)
}

// queryCategoryStandards 查询分类聚和的基线标准列表
//
//	@Summary		查询分类聚和的基线标准
//	@Description	查询分类聚和的基线标准列表
//	@Tags			基线标准分类
//	@Accept			application/x-www-form-urlencoded
//	@Produce		application/json
//	@Param			builtin		query		bool																						false	"是否查询内置基线标准"	default(true)
//	@Param			standardId	query		string																						false	"标准id， 如果传入返回各个基线标准所选择的id"
//	@Param			isCopy		query		bool																						false	"是否是复制页面"
//	@Success		200			{object}	utils.Response{code=int,success=bool,data=baselinemodels.QueryCategoryStandardsResponse{}}	"请求成功，返回基线标准列表"
//	@Failure		400			{object}	utils.Response{code=int,success=bool,errorMsg=string}										"请求参数错误，返回400错误"
//	@Failure		500			{object}	utils.Response{code=int,success=bool,errorMsg=string}										"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/standard_categories/standards [get]
func (r *categoryRouter) queryCategoryStandards(c *gin.Context) {
	var req baselinemodels.QueryCategoryStandardsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	filter := resources.Filter[*baselinemodels.CategoryStandardsItem]{}
	filter.WithFn(func(item *baselinemodels.CategoryStandardsItem) bool {
		if req.Builtin == nil {
			return true
		}
		return item.Builtin == *req.Builtin
	})
	req.Filter = filter
	resp, err := r.standardHandler.QueryCategoryStandards(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, resp)
}

// queryBaselineCategories 查询基线分类
//
//	@Summary		查询基线分类
//	@Description	查询基线分类时，支持根据分类名称和是否内置进行筛选。
//	@Tags			基线标准分类
//	@Accept			application/x-www-form-urlencoded
//	@Produce		application/json
//	@Param			name	query		string	false	"基线标准分类名称"	//	选择性筛选的参数，默认值为空
//	@Param			builtin	query		bool	false	"是否内置分类"	//	选择性筛选的参数，默认值为false
//	@Success		200		{object}	utils.Response{data=models.PageableResponse[baselinemodels.CategoryItem]{}}
//	@Failure		400		{object}	utils.Response{errorMsg=string}
//	@Failure		404		{object}	utils.Response{errorMsg=string}
//	@Security		jwt
//	@Router			/apis/v1/baselines/standard_categories [get]
func (r *categoryRouter) queryBaselineCategories(c *gin.Context) {
	var (
		req baselinemodels.QueryBaselineCategoriesRequest
		err error
	)
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	filter := utils.ParseQueryParams[*baselinemodels.CategoryItem](c)
	if req.Name != "" {
		filter.WithFn(func(item *baselinemodels.CategoryItem) bool {
			return strings.Contains(item.Name, req.Name)
		})
	}
	if req.Builtin != nil {
		filter.WithFn(func(item *baselinemodels.CategoryItem) bool {
			return item.Builtin == *req.Builtin
		})
	}
	req.Filter = filter
	resp, err := r.standardHandler.QueryBaselineCategories(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, resp)
}

// createBaselineCategory 创建基线分类
//
//	@Summary		创建基线分类
//	@Description	创建一个新的基线分类
//	@Tags			基线标准分类
//	@Accept			application/json
//	@Produce		application/json
//	@Param			request	body		baselinemodels.CreateBaselineCategoryRequest												true	"创建基线分类请求体"	//	请求体，包含新建分类的数据
//	@Success		200		{object}	utils.Response{code=int,success=bool,data=baselinemodels.CreateBaselineCategoryResponse}	"请求成功，返回新创建的基线分类"
//	@Failure		400		{object}	utils.Response{code=int,success=bool,errorMsg=string}										"请求参数错误，返回400错误"
//	@Failure		500		{object}	utils.Response{code=int,success=bool,errorMsg=string}										"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/standard_categories [post]
func (r *categoryRouter) createBaselineCategory(c *gin.Context) {
	var req baselinemodels.CreateBaselineCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	resp, err := r.standardHandler.CreateBaselineCategory(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, resp)
}

// editBaselineCategory 编辑基线分类
//
//	@Summary		编辑基线分类
//	@Description	根据分类 ID 编辑基线分类
//	@Tags			基线标准分类
//	@Accept			application/json
//	@Produce		application/json
//	@Param			id		path		string																					true	"分类 ID"		//	必填参数，指定要编辑的分类ID
//	@Param			request	body		baselinemodels.EditBaselineCategoryRequest												true	"编辑基线分类请求体"	//	请求体，包含编辑后的分类数据
//	@Success		200		{object}	utils.Response{code=int,success=bool,data=baselinemodels.EditBaselineCategoryResponse}	"请求成功，返回编辑后的基线分类信息"
//	@Failure		400		{object}	utils.Response{code=int,success=bool,errorMsg=string}									"请求参数错误，返回400错误"
//	@Failure		404		{object}	utils.Response{code=int,success=bool,errorMsg=string}									"未找到基线分类，返回404错误"
//	@Failure		500		{object}	utils.Response{code=int,success=bool,errorMsg=string}									"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/standard_categories/{id} [put]
func (r *categoryRouter) editBaselineCategory(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, "id is not int"))
		return
	}
	var req baselinemodels.EditBaselineCategoryRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	req.ID = id
	resp, err := r.standardHandler.EditBaselineCategory(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, resp)
}

// deleteBaselineCategory 删除基线分类
//
//	@Summary		删除基线分类
//	@Description	根据分类 ID 删除基线分类
//	@Tags			基线标准分类
//	@Accept			application/x-www-form-urlencoded
//	@Produce		application/json
//	@Param			id	path		string																						true	"分类 ID"	//	必填参数，指定要删除的分类ID
//	@Success		200	{object}	utils.Response{code=int,success=bool,data=baselinemodels.DeleteBaselineCategoryResponse}	"请求成功，返回删除的基线分类信息"
//	@Failure		400	{object}	utils.Response{code=int,success=bool,errorMsg=string}										"请求参数错误，返回400错误"
//	@Failure		404	{object}	utils.Response{code=int,success=bool,errorMsg=string}										"未找到基线分类，返回404错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/standard_categories/{id} [delete]
func (r *categoryRouter) deleteBaselineCategory(c *gin.Context) {
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, "id is not int"))
		return
	}
	var req baselinemodels.DeleteBaselineCategoryRequest
	req.ID = id
	resp, err := r.standardHandler.DeleteBaselineCategory(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, resp)
}
