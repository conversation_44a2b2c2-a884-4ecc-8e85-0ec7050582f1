package baseline

import (
	"io"
	"path/filepath"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	handlers "harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline/helper"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	baselinemodels "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/resources"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

var _ models.PageableResponse[any]

// checkerRouter handles the routing for baseline checkers.
type checkerRouter struct {
	checkerHandler handlers.CheckerInterface
	fileHandler    handlers.CheckerFileInterface
}

// NewCheckerRouter creates a new instance of checkerRouter.
func NewCheckerRouter() routerutil.ApiController {
	if !helper.IsFeatureBaselineEnabled() {
		return &checkerRouter{}
	}
	return &checkerRouter{
		checkerHandler: singletonHandlers.checkerHandler,
		fileHandler:    singletonHandlers.filerHandler,
	}
}

// GetGroup returns the router group for baseline checkers.
func (r *checkerRouter) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.ApiV1BaselineGroup + "/checkers")
}

// RegisterRouter registers the routes for baseline checkers.
func (r *checkerRouter) RegisterRouter(group *gin.RouterGroup) {
	// 获取自定义检查项
	group.GET("", doExecute(r.getCustomCheckers))
	// 获取自定检查项详情
	group.GET(":id", doExecute(r.getCustomCheckerDetails))
	// 获取标准需要分配自定义检查项列表
	group.GET("/assign_standard_checkers", doExecute(r.getAssignStandardCheckers))
	// 获取名称是否已经存在的结果
	group.GET("/name_existed", doExecute(r.getCustomCheckerNameExisted))
	// 创建自定义检查项
	group.POST("", doExecute(r.createCustomChecker))
	// 编辑自定义检查项
	group.PUT(":id", doExecute(r.editCustomChecker))
	// 删除自定义检查项
	group.DELETE(":id", doExecute(r.deleteCustomChecker))
	group.DELETE("", doExecute(r.deleteCustomCheckers))
	// 获取自定义检查项关联的基线标准
	group.GET(":id/standards", doExecute(r.queryAssociatedBaselineStandards))
	// 更新自定义检查项绑定的标准
	group.POST(":id/binding_standards", doExecute(r.updateCustomCheckerBindingStandards))
	// 上传检查项文件
	group.POST("/upload_files", doExecute(r.uploadCheckerFiles))
}

// getCustomCheckerNameExisted 获取自定义检查项是否存在的结果
//
//	@Summary		获取自定义检查项是否存在的结果
//	@Description	获取自定义检查项是否存在的结果
//	@Tags			自定义检查项
//	@Accept			application/x-www-form-urlencoded
//	@Produce		application/json
//	@Param			name	query		string																							false	"检查项名称"
//	@Success		200		{object}	utils.Response{code=int,success=bool,data=baselinemodels.GetCustomCheckerNameExistedResponse}	"请求成功返回数据"
//	@Failure		400		{object}	utils.Response{code=int,success=bool,errorMsg=string}											"请求参数错误"
//	@Failure		401		{object}	utils.Response{code=int,success=bool,errorMsg=string}											"权限错误"
//	@Failure		500		{object}	utils.Response{code=int,success=bool,errorMsg=string}											"服务器内部错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/checkers/name_existed [get]
func (r *checkerRouter) getCustomCheckerNameExisted(c *gin.Context) {
	var req baselinemodels.GetCustomCheckerNameExistedRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	if req.Name == "" {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, "name is empty"))
		return
	}
	if resp, err := r.checkerHandler.GetCustomCheckerNameExisted(c, &req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	} else {
		utils.Succeed(c, resp)
		return
	}
}

// getCustomCheckers 获取自定义检查项
//
//	@Summary		获取自定义检查项
//	@Description	支持分页，过滤查询
//	@Tags			自定义检查项
//	@Accept			application/x-www-form-urlencoded
//	@Produce		application/json
//	@Param			resourceType	query		string																							false	"资源类型，例如 Kubernetes, Docker, Host"
//	@Param			riskLevel		query		string																							false	"风险级别，例如 High, Medium, Low"
//	@Param			checkMode		query		string																							false	"检查方式，例如 Command, FileYAML, FileJSON"
//	@Param			builtin			query		bool																							true	"是否为内置检查项，true 或 false"	default(false)
//	@Param			page_num		query		int																								false	"页码, 默认为1"
//	@Param			page_size		query		int																								false	"每页大小, 默认为10"
//	@Param			sort_order		query		string																							false	"排序方式 asc 或 desc，默认 desc"
//	@Param			sort_func		query		string																							false	"排序字段比较方式，例如 time, string, number"
//	@Param			sort_name		query		string																							false	"排序字段，通过类似 JSONPath 的方式获取, 默认为 createTime"
//	@Param			selector		query		string																							false	"符合查询参数的选择器。例如：精确查询 selector=name=a,namespace=default；模糊查询 selector=name~a,namespace~default；组合查询 selector=name~a,namespace=default"
//	@Success		200				{object}	utils.Response{code=int,success=bool,data=models.PageableResponse[baselinemodels.CheckItem]{}}	"请求成功返回数据"
//	@Failure		400				{object}	utils.Response{code=int,success=bool,errorMsg=string}											"请求参数错误"
//	@Failure		401				{object}	utils.Response{code=int,success=bool,errorMsg=string}											"权限错误"
//	@Failure		500				{object}	utils.Response{code=int,success=bool,errorMsg=string}											"服务器内部错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/checkers [get]
func (r *checkerRouter) getCustomCheckers(c *gin.Context) {
	var req baselinemodels.QueryCustomCheckersRequest
	req.Filter = utils.ParseQueryParams[*baselinemodels.CheckItem](c)
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	if c.Query("sort_name") == "" {
		req.Filter.WithSort("updateTime", resources.SortOrderDesc, resources.SortFuncTime)
	}
	req.ApplyFieldsToFilter()

	resp, err := r.checkerHandler.QueryCustomCheckers(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, resp)
}

// getCustomCheckerDetails 获取自定义检查项详情
//
//	@Summary		获取自定义检查项详情
//	@Description	Retrieves details of a custom checker by ID.
//	@Tags			自定义检查项
//	@Accept			application/x-www-form-urlencoded
//	@Produce		application/json
//	@Param			id	path		string	true	"Checker ID"
//	@Success		200	{object}	utils.Response{code=int,success=bool,data=baselinemodels.GetCustomCheckerDetailsResponse}
//	@Failure		404	{object}	utils.Response{code=int,message=string}
//	@Security		jwt
//	@Router			/apis/v1/baselines/checkers/{id} [get]
func (r *checkerRouter) getCustomCheckerDetails(c *gin.Context) {
	var req baselinemodels.GetCustomCheckerDetailsRequest
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	req.ID = id
	resp, err := r.checkerHandler.GetCustomCheckerDetails(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, resp)
}

// getAssignStandardCheckers 获取分配给标准的自定义检查项
//
//	@Summary		获取分配给标准自定义检查项
//	@Description	获取分配给标准自定义检查项列表
//	@Tags			自定义检查项
//	@Accept			application/x-www-form-urlencoded
//	@Produce		application/json
//	@Param			standardId	query		string	false	"标准ID"
//	@Success		200			{object}	utils.Response{code=int,success=bool,data=baselinemodels.GetAssignStandardCheckersResponse}
//	@Failure		404			{object}	utils.Response{code=int,message=string}
//	@Security		jwt
//	@Router			/apis/v1/baselines/checkers/assign_standard_checkers [get]
func (r *checkerRouter) getAssignStandardCheckers(c *gin.Context) {
	var req baselinemodels.GetAssignStandardCheckersRequest
	if err := c.ShouldBind(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
	}
	req.Filter = utils.ParseQueryParams[*baselinemodels.StandardCheckItem](c)
	if c.Query("sort_name") == "" {
		req.Filter.WithSort("updateTime", resources.SortOrderDesc, resources.SortFuncTime)
	}
	resp, err := r.checkerHandler.GetAssignStandardCheckers(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, resp)
}

// createCustomChecker 创建一个新的自定义检查项
//
//	@Summary		创建自定义检查项
//	@Description	创建一个新的自定义检查项，包含名称、风险级别和检查模式等配置
//	@Tags			自定义检查项
//	@Accept			application/json
//	@Produce		application/json
//	@Param			checker	body		baselinemodels.CreateCustomCheckerRequest												true	"新建检查项"	//	请求体，包含要创建的自定义检查项的详细信息
//	@Success		200		{object}	utils.Response{code=int,success=bool,data=baselinemodels.CreateCustomCheckerResponse}	"请求成功，返回新创建的自定义检查项的响应"
//	@Failure		401		{object}	utils.Response{code=int,success=bool,errorMsg=string}									"权限错误，返回401错误"
//	@Failure		500		{object}	utils.Response{code=int,success=bool,errorMsg=string}									"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/checkers [post] // 请求路径和方法
func (r *checkerRouter) createCustomChecker(c *gin.Context) {
	var req baselinemodels.CreateCustomCheckerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	if err := req.Validate(); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	resp, err := r.checkerHandler.CreateCustomChecker(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, resp)
}

// editCustomChecker 编辑已有的自定义检查项
//
//	@Summary		编辑自定义检查项
//	@Description	编辑已有的自定义检查项，使用 ID 指定要编辑的检查项
//	@Tags			自定义检查项
//	@Accept			application/json
//	@Produce		application/json
//	@Param			id		path		string																					true	"检查项 ID"	//	路径参数，指定要编辑的检查项	ID
//	@Param			checker	body		baselinemodels.UpdateCustomCheckerRequest												true	"更新后的检查项"	//	请求体，包含更新后的检查项的详细信息
//	@Success		200		{object}	utils.Response{code=int,success=bool,data=baselinemodels.UpdateCustomCheckerResponse}	"请求成功，返回编辑后的自定义检查项响应"
//	@Failure		401		{object}	utils.Response{code=int,success=bool,errorMsg=string}									"权限错误，返回401错误"
//	@Failure		500		{object}	utils.Response{code=int,success=bool,errorMsg=string}									"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/checkers/{id} [put] // 请求路径和方法
func (r *checkerRouter) editCustomChecker(c *gin.Context) {
	var req baselinemodels.UpdateCustomCheckerRequest
	var err error
	// 绑定请求体数据
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	// 获取路径参数 id 并转换为 int64
	req.ID, err = strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	if err := req.Validate(); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	// 调用 handler 层更新检查项
	resp, err := r.checkerHandler.UpdateCustomChecker(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	// 返回成功响应
	utils.Succeed(c, resp)
}

// deleteCustomChecker 删除单个自定义检查项
//
//	@Summary		删除单个自定义检查项
//	@Description	根据检查项 ID 删除指定的自定义检查项
//	@Tags			自定义检查项
//	@Acceptapplication/x-www-form-urlencoded
//	@Produce	application/json
//	@Param		id	path		string													true	"检查项 ID"	//	路径参数，指定要删除的检查项	ID
//	@Success	200	{object}	utils.Response{code=int,success=bool}					"请求成功，删除成功"
//	@Failure	401	{object}	utils.Response{code=int,success=bool,errorMsg=string}	"权限不足，返回401错误"
//	@Failure	500	{object}	utils.Response{code=int,success=bool,errorMsg=string}	"服务器内部错误，返回500错误"
//	@Security	jwt
//	@Router		/apis/v1/baselines/checkers/{id} [delete] // 请求路径和方法
func (r *checkerRouter) deleteCustomChecker(c *gin.Context) {
	var req baselinemodels.DeleteCustomCheckerRequest
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	req.Ids = []int64{id}
	resp, err := r.checkerHandler.DeleteCustomChecker(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, resp)
}

// deleteCustomCheckers 删除多个自定义检查项
//
//	@Summary		删除多个自定义检查项
//	@Description	根据检查项 ID 列表删除多个自定义检查项
//	@Tags			自定义检查项
//	@Accept			application/json
//	@Produce		application/json
//	@Param			checkers	body		baselinemodels.DeleteCustomCheckerRequest				true	"检查项 ID 列表"	//	请求体，包含要删除的检查项	ID	列表
//	@Success		200			{object}	utils.Response{code=int,success=bool}					"请求成功，删除成功"
//	@Failure		401			{object}	utils.Response{code=int,success=bool,errorMsg=string}	"权限不足，返回401错误"
//	@Failure		500			{object}	utils.Response{code=int,success=bool,errorMsg=string}	"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/checkers [delete] // 请求路径和方法
func (r *checkerRouter) deleteCustomCheckers(c *gin.Context) {
	var req baselinemodels.DeleteCustomCheckerRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	resp, err := r.checkerHandler.DeleteCustomChecker(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, resp)
}

// 查询关联自定义检查项的基线标准
//
//	@Summary		查询关联基线标准
//	@Description	查询与指定自定义检查项关联的基线标准
//	@Tags			自定义检查项
//	@Accept			application/x-www-form-urlencoded
//	@Produce		application/json
//	@Param			id	path		string																						true	"检查项 ID"	//	路径参数，指定要查询的检查项	ID
//	@Success		200	{object}	utils.Response{code=int,success=bool,data=baselinemodels.QueryAssociatedBaselinesResponse}	"请求成功，返回关联的基线标准"
//	@Failure		401	{object}	utils.Response{code=int,success=bool,errorMsg=string}										"权限不足，返回401错误"
//	@Failure		404	{object}	utils.Response{code=int,success=bool,errorMsg=string}										"未找到指定的检查项，返回404错误"
//	@Failure		500	{object}	utils.Response{code=int,success=bool,errorMsg=string}										"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/checkers/{id}/standards [get] // 请求路径和方法
func (r *checkerRouter) queryAssociatedBaselineStandards(c *gin.Context) {
	id := c.Param("id") // 获取路径参数 id
	resp, err := r.checkerHandler.QueryAssociatedBaselines(c, &baselinemodels.QueryAssociatedBaselinesRequest{Id: id})
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err)) // 请求出错，返回失败响应
		return
	}
	utils.Succeed(c, resp) // 请求成功，返回数据
}

// updateCustomCheckerBindingStandards 更新自定义检查项关联的基线标准
//
//	@Summary		更新自定义检查项关联的基线标准
//	@Description	更新自定义检查项关联的基线标准并删除
//	@Tags			自定义检查项
//	@Accept			application/json
//	@Produce		application/json
//	@Param			id		path		string																									true	"检查项 ID"	//	路径参数，指定要查询的检查项	ID
//	@Param			body	body		baselinemodels.UpdateCustomCheckerBindingStandardsRequest												true	"检查项 ID 列表"	//	请求体，包含要删除的检查项	ID	列表
//	@Success		200		{object}	utils.Response{code=int,success=bool,data=baselinemodels.UpdateCustomCheckerBindingStandardsResponse}	"请求成功"
//	@Failure		401		{object}	utils.Response{code=int,success=bool,errorMsg=string}													"权限不足，返回401错误"
//	@Failure		404		{object}	utils.Response{code=int,success=bool,errorMsg=string}													"未找到指定的检查项，返回404错误"
//	@Failure		500		{object}	utils.Response{code=int,success=bool,errorMsg=string}													"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/checkers/{id}/binding_standards [post] // 请求路径和方法
func (r *checkerRouter) updateCustomCheckerBindingStandards(c *gin.Context) {
	var req baselinemodels.UpdateCustomCheckerBindingStandardsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	req.ID = id
	resp, err := r.checkerHandler.UpdateCustomCheckerBindingStandardsResponse(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err)) // 请求出错，返回失败响应
		return
	}
	utils.Succeed(c, resp) // 请求成功，返回数据
}

// validateUploadCheckerFile 验证上传的检查项文件
func (r *checkerRouter) validateUploadCheckerFile(req *baselinemodels.UploadCheckerFileRequest) error {
	if req.File == nil {
		return errors.NewFromCodeWithMessage(errors.Var.BaselineCustomCheckerUploadInvalid, "file is required")
	}
	src, err := req.File.Open()
	if err != nil {
		return errors.NewFromCodeWithMessage(errors.Var.BaselineCustomCheckerUploadInvalid, err.Error())
	}
	defer src.Close()
	content, err := io.ReadAll(src)
	if err != nil {
		return errors.NewFromCodeWithMessage(errors.Var.BaselineCustomCheckerUploadInvalid, err.Error())
	}
	ext, _ := strings.CutPrefix(filepath.Ext(req.File.Filename), ".")
	switch strings.ToLower(ext) {
	case "json", "yaml", "yml":
		// max size < 1MiB
		if req.File.Size > 1024*1024 {
			return errors.NewFromCodeWithMessage(errors.Var.BaselineCustomCheckerUploadInvalid, "file size must be less than 1MiB")
		}
		switch ext {
		case "json":
			// check json is valid
			if !utils.IsValidJson(string(content)) {
				return errors.NewFromCodeWithMessage(errors.Var.BaselineCustomCheckerConfigFileInvalidJSON, "file content is not valid json")
			}
		case "yaml", "yml":
			// check yaml is valid
			if !utils.IsValidYaml(string(content)) {
				return errors.NewFromCodeWithMessage(errors.Var.BaselineCustomCheckerConfigFileInvalidYAML, "file content is not valid yaml")
			}
		}
		return nil
	case "env", "toml", "config", "service", "txt", "ini", "conf", "cfg", "repo", "list":
		if req.File.Size > 1024*1024 {
			return errors.NewFromCodeWithMessage(errors.Var.BaselineCustomCheckerUploadInvalid, "file size must be less than 1MiB")
		}

	}
	return errors.NewFromCodeWithMessage(errors.Var.BaselineCustomCheckerUploadInvalid, "file type must be json, yaml or yml")
}

// uploadCheckerFiles 上传检查项相关的文件
//
//	@Summary		上传检查项相关的文件
//	@Description	上传检查项文件，返回对应id，名称，访问路径等
//	@Tags			自定义检查项
//	@Accept			multipart/form-data
//	@Produce		application/json
//	@Param			file	formData	file																				true	"上传的文件对象"
//	@Success		200		{object}	utils.Response{code=int,success=bool,data=baselinemodels.UploadCheckerFileResponse}	"请求成功，返回新创建的自定义检查项的响应"
//	@Failure		401		{object}	utils.Response{code=int,success=bool,errorMsg=string}								"权限错误，返回401错误"
//	@Failure		500		{object}	utils.Response{code=int,success=bool,errorMsg=string}								"服务器内部错误，返回500错误"
//	@Security		jwt
//	@Router			/apis/v1/baselines/checkers/upload_files [post] // 请求路径和方法
func (r *checkerRouter) uploadCheckerFiles(c *gin.Context) {
	file, err := c.FormFile("file")
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	req := &baselinemodels.UploadCheckerFileRequest{File: file}
	if err := r.validateUploadCheckerFile(req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	if resp, err := r.fileHandler.UploadCheckerFile(c, req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	} else {
		utils.Succeed(c, resp)
	}
}
