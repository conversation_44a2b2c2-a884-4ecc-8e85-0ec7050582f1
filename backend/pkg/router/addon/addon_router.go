package addon

import (
	"strconv"

	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/addon"
	addonmodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/addon"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
	_ "harmonycloud.cn/unifiedportal/portal/docs"
)

func NewRouter() routerutil.ApiController {
	return &router{
		intf: addon.NewHandler(),
	}
}

// Addon的 Router
type router struct {
	intf addon.Handler
}

func (r *router) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	// addon 的路由
	//todo： 测试
	return engine.Group(utils.ApiV1Group + "/clusters/:clusterName/components")
}

func (r *router) RegisterRouter(group *gin.RouterGroup) {
	// 组件相关路由
	group.POST("", r.switchComponent)
	group.DELETE("/:componentName", r.cancelComponent)
	group.PUT("/:componentName", r.editComponent)
	group.GET("/:componentName", r.getComponent)
	group.GET("", r.listComponent)
	group.GET("/scan", r.scanComponent)
	group.POST("/switches", r.batchInstall)
}

// switchComponent 接入插件
//
//	@Tags			集群插件相关API
//	@Summary		插件接入
//	@Description	插件的接入
//	@Param			clusterName	path	string						true	"集群名称"
//	@Param			requestBody	body	addonmodel.ComponentRequest	true	"接入插件配置信息"
//	@Router			/apis/v1/clusters/{clusterName}/components [post]
//	@Success		200	{object}	utils.Response{data=bool}
func (r *router) switchComponent(ctx *gin.Context) {
	// 获取参数
	clusterName := ctx.Param("clusterName")
	var request addonmodel.ComponentRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	// 调用接口 handler

	componentDto := addonmodel.ConverComponentRequest2ComponentDto(request)

	err := r.intf.SwitchComponent(ctx, clusterName, componentDto)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, nil)
}

// cancelComponent 插件取消接入
//
//	@Tags			集群插件相关API
//	@Summary		取消接入的插件
//	@Description	取消接入的插件
//	@Param			clusterName		path	string	true	"集群名称"
//	@Param			componentName	path	string	true	"插件名称"
//	@Router			/apis/v1/clusters/{clusterName}/components/{componentName} [delete]
//	@Success		200	{object}	utils.Response{data=bool}
func (r *router) cancelComponent(ctx *gin.Context) {
	// 获取参数
	clusterName := ctx.Param("clusterName")
	componentName := ctx.Param("componentName")
	if err := r.intf.CancelComponent(ctx, clusterName, componentName); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, nil)
}

// editComponent 编辑组件
//
//	@Tags			集群插件相关API
//	@Summary		编辑插件
//	@Description	编辑插件
//	@Param			clusterName		path	string						true	"集群名称"
//	@Param			componentName	path	string						true	"插件名称"
//	@Param			requestBody		body	addonmodel.ComponentRequest	true	"更新的插件配置信息"
//	@Router			/apis/v1/clusters/{clusterName}/components/{componentName} [put]
//	@Success		200	{object}	utils.Response{data=bool}
func (r *router) editComponent(ctx *gin.Context) {
	// 获取参数
	clusterName := ctx.Param("clusterName")
	componentName := ctx.Param("componentName")
	var request addonmodel.ComponentRequest
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "插件配置不合法"))
		return
	}
	componentDto := addonmodel.ConverComponentRequest2ComponentDto(request)

	if err := r.intf.UpdateComponent(ctx, clusterName, componentName, componentDto); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, nil)
}

// getComponent 插件详情
//
//	@Tags			集群插件相关API
//	@Summary		查询插件信息
//	@Description	查询插件信息
//	@Param			clusterName		path	string	true	"集群名称"
//	@Param			componentName	path	string	true	"插件名称"
//	@Param			baseFlag		query	bool	false	"是否获取基础信息"
//	@Router			/apis/v1/clusters/{clusterName}/components/{componentName} [get]
//	@Success		200	{object}	utils.Response{data=addonmodel.ComponentDetailResponse}
func (r *router) getComponent(ctx *gin.Context) {
	// 1.获取参数
	clusterName := ctx.Param("clusterName")
	componentName := ctx.Param("componentName")
	baseFlag := ctx.Query("baseFlag")
	parseBool, err := strconv.ParseBool(baseFlag)
	if err != nil {
		// 返回错误，参数不合法 or 缺少参数
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.BoolParamError, "baseFlag参数不合法"))
		return
	}
	response, err := r.intf.GetComponent(ctx, clusterName, componentName, parseBool)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	componentDetail := addonmodel.ConverComponentDto2ComponentDetailResponse(*response)
	nickName := response.ChName
	if response.EnName != "" {
		nickName += "(" + response.EnName + ")"
	}
	componentDetail.NickName = nickName
	utils.Succeed(ctx, componentDetail)
}

// listComponent 获取插件列表
//
//	@Tags			集群插件相关API
//	@Summary		查询集群所有插件信息
//	@Description	查询集群所有插件信息
//	@Param			clusterName	path	string	true	"集群名称"
//	@Router			/apis/v1/clusters/{clusterName}/components [get]
//	@Success		200	{object}	utils.Response{data=addonmodel.ListResponse}
func (r *router) listComponent(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")

	response, err := r.intf.ListComponent(ctx, clusterName)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	// 转换
	utils.Succeed(ctx, addonmodel.ConverResponseList2ComponentsList(response))
}

// scanComponent 扫描插件
//
//	@Tags			集群插件相关API
//	@Summary		扫描集群插件信息
//	@Description	扫描集群插件信息
//	@Param			clusterName	path	string	true	"集群名称"
//	@Router			/apis/v1/clusters/{clusterName}/components/scan [get]
//	@Success		200	{object}	utils.Response{data=addonmodel.ListComponentScanResponse}
func (r *router) scanComponent(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")

	response, err := r.intf.ScanComponent(ctx, clusterName)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	//
	utils.Succeed(ctx, response)
}

// batchInstall 批量安装插件
//
//	@Tags			集群插件相关API
//	@Summary		插件批量安装
//	@Description	插件批量安装
//	@Param			clusterName	path	string							true	"集群名称"
//	@Param			requestBody	body	addonmodel.ComponentRequestList	true	"插件批量安装配置信息"
//	@Router			/apis/v1/clusters/{clusterName}/components/switches [post]
//	@Success		200	{object}	utils.Response{data=bool}
func (r *router) batchInstall(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")

	var request = []addonmodel.ComponentRequest{}
	if err := ctx.ShouldBindJSON(&request); err != nil {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "request配置不合法"))
		return
	}
	if err := r.intf.BatchInstall(ctx, clusterName, request); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, nil)
}
