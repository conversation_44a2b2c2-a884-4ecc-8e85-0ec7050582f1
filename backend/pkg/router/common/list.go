package common

import (
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/labels"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/resources"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/rbac"
)

func (cc *routessController) listRoutes(routes *gin.RouterGroup) {

	// core clustered
	routes.GET("/clusters/:cluster/api/:version/:ptype", cc.commonList)

	// core namespaced
	routes.GET("/clusters/:cluster/api/:version/:ptype/:pname/:resourcetype", cc.commonList)

	// cluster
	routes.GET("/clusters/:cluster/apis/:group/:version/:ptype", cc.commonList)

	// namespaced
	routes.GET("/clusters/:cluster/apis/:group/:version/ptype/:pname/:resourcetype", cc.commonList)

	routes.GET("/clusters/:cluster/resource_list/apis/:group/:version/:resourcetype", cc.resourceList)

	routes.GET("/clusters/:cluster/resource_list/api/:version/:resourcetype", cc.resourceList)

	routes.GET("/clusters/:cluster/apiResourceList", cc.apiList)

}

// 通用List接口
//
//	@Tags			通用List接口
//	@Summary		通用List接口
//	@Description	可获取一组K8s资源
//	@Param			cluster			path	string	true	"集群"
//	@Param			group			path	string	false	"API组别"
//	@Param			version			path	string	true	"api版本"
//	@Param			namespace		path	string	false	"资源所在Namespace"
//	@Param			resourcetype	path	string	true	"资源类型"
//	@Param			selector		query	string	false	"selector"
//	@Param			page_size		query	string	false	"filter page_size 页长"
//	@Param			page_num		query	string	false	"filter page_num 页码"
//	@Param			sort_name		query	string	false	"sort_name"
//	@Param			sort_order		query	string	false	"sort_order"
//	@Param			sort_func		query	string	false	"sort_func"
//	@Param			label_selector	query	string	false	"标签选择器"
//	@Router			/k8s/clusters/{cluster}/api/{version}/{resourcetype} [get]
//	@Router			/k8s/clusters/{cluster}/api/{version}/namespaces/{namespace}/{resourcetype} [get]
//	@Router			/k8s/clusters/{cluster}/apis/{group}/{version}/{resourcetype} [get]
//	@Router			/k8s/clusters/{cluster}/apis/{group}/{version}/namespaces/{namespace}/{resourcetype} [get]
//	@Success		200	{object}	object
func (cc *routessController) commonList(c *gin.Context) {
	ctx := utils.RequestContext(c)
	p := &common.ReadParam{}
	if err := BindingReadParam(c, p); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	var err error
	filter := utils.ParseQueryParams[unstructured.Unstructured](c)
	p.Filter = filter
	p.Unstructured = parseQueryMapToUnstructured(c)
	p.LabelSelector, err = parseLabelSelector(c)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	p.FieldSelector, err = parseFieldSelector(c)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}

	if err := authK8sRBACWithReadParam(ctx, p, rbac.VerbList); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}

	rsp, err := cc.commonHandler.List(ctx, p)
	if err != nil {
		utils.Failed(c, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(c, rsp)
}

func (cc *routessController) apiList(c *gin.Context) {
	ctx := utils.RequestContext(c)
	p := common.ListAPIResourceParam{Cluster: c.Param("cluster")}
	p.Filter = utils.ParseQueryParams[unstructured.Unstructured](c)
	p.Filter.WithFn(func(item unstructured.Unstructured) bool {
		return true
	})
	resources, err := cc.commonHandler.ApiResourceByGVK(ctx, &p)
	if err != nil {
		utils.Failed(c, errors.NewFromError(ctx, err))
		return
	}
	//resources.Items
	params := utils.ParseQueryParams[*models.ApiResourceByGV](c)
	params.Limit = lo.ToPtr(99999)
	params.WithFn(func(item *models.ApiResourceByGV) bool {
		newResources := make([]*models.APIResource, 0)
		apiResourceNames := strings.Split(c.Query("apiResourceName"), ",")
		for j := range item.Resources {
			if len(apiResourceNames) == 0 || func(slice []string, item string) bool {
				for _, v := range slice {
					if strings.Contains(item, v) {
						return true
					}
				}
				return false
			}(apiResourceNames, item.Resources[j].Name) {
				newResources = append(newResources, item.Resources[j])
			}
		}
		item.Resources = newResources
		return len(item.Resources) > 0
	})
	filterResult, err := params.FilterResult(resources.Items)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	resources.Items = filterResult.Items
	utils.Succeed(c, resources)
}

func (cc *routessController) resourceList(c *gin.Context) {
	ctx := utils.RequestContext(c)
	// 只允许在项目的命名空间进行查询
	organ := c.GetHeader(HandlerOrgan)
	project := c.GetHeader(HandlerProject)
	labelSet := labels.Set{}
	// 如果项目不为空，则添加项目标签
	if project != "" {
		labelSet[LabelOrgan] = organ
		labelSet[LabelProject] = project
	}
	u := &unstructured.Unstructured{
		Object: map[string]interface{}{
			"labels": labelSet,
		},
	}
	p := &common.ReadParam{
		Unstructured: u,
		Cluster:      c.Param("cluster"),
		Group:        c.Param("group"),
		Version:      c.Param("version"),
		ResourceType: c.Param("resourcetype"),
	}
	var err error
	rsp, err := cc.commonHandler.GetResources(ctx, p)
	if err != nil {
		utils.Failed(c, errors.NewFromError(ctx, err))
		return
	}
	params := utils.ParseQueryParams[*models.ResourceInfo](c)
	params.WithFn(func(item *models.ResourceInfo) bool {
		resourceNames := strings.Split(c.Query("resourceName"), ",")
		return len(resourceNames) == 0 || func(slice []string, item string) bool {
			for _, v := range slice {
				if strings.Contains(item, v) {
					return true
				}
			}
			return false
		}(resourceNames, item.Name)
	})
	params.WithFn(func(item *models.ResourceInfo) bool {
		namespaces := strings.Split(c.Query("namespace"), ",")
		return len(namespaces) == 0 || func(namespaces []string, item string) bool {
			for _, v := range namespaces {
				if strings.Contains(item, v) {
					return true
				}
			}
			return false
		}(namespaces, item.Namespace)
	})
	sortBy := resources.SortOrderDesc
	if strings.EqualFold(c.Query("sortBy"), resources.SortOrderAsc) {
		sortBy = resources.SortOrderAsc
	}
	result, err := params.WithSort(".createTime", sortBy, resources.SortFuncTime).FilterResult(rsp.Items)
	if err != nil {
		utils.Failed(c, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(c, result)
}
