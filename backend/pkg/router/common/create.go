package common

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/validator"
	"k8s.io/apimachinery/pkg/api/meta"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/rbac"
)

func (cc *routessController) createRoutes(routes *gin.RouterGroup) {
	// core cluster
	routes.POST("/clusters/:cluster/api/:version/:ptype", cc.commonCreate)

	// core namespaced
	routes.POST("/clusters/:cluster/api/:version/:ptype/:pname/:resourcetype", cc.commonCreate)

	// cluster
	routes.POST("/clusters/:cluster/apis/:group/:version/:ptype", cc.commonCreate)

	// namespaced
	routes.POST("/clusters/:cluster/apis/:group/:version/:ptype/:pname/:resourcetype", cc.commonCreate)

	// k8s yaml
	routes.POST("/clusters/:cluster/api_resources", cc.commonCreateApiResource)
}

// 通用Create接口
//
//	@Summary		通用Creat
//	@Summary		通用Create接口
//	@Description	创建资源
//	@Tags			通用Create接口
//	@Accept			application/json
//	@Produce		application/json
//	@Param			Authorization	header	string	false	"Bearer 用户令牌"
//	@Param			cluster			path	string	true	"集群"
//	@Param			group			path	string	false	"资源Group"
//	@Param			version			path	string	true	"资源版本"
//	@Param			namespace		path	string	false	"命名空间"
//	@Param			resourcetype	path	string	true	"资源类型"	example(pods)
//	@Param			request			body	string	true	"资源JSON"
//	@Security		ApiKeyAuth
//	@Success		200	{object}	object
//	@Router			/k8s/clusters/{cluster}/api/{version}/{resourcetype} [post]
//	@Router			/k8s/clusters/{cluster}/api/{version}/namespaces/{namespace}/{resourcetype} [post]
//	@Router			/k8s/clusters/{cluster}/apis/{group}/{version}/{resourcetype} [post]
//	@Router			/k8s/clusters/{cluster}/apis/{group}/{version}/namespaces/{namespace}/{resourcetype} [post]
func (cc *routessController) commonCreate(c *gin.Context) {
	p := &common.WriteParam{}
	if err := BindingWriteParam(c, p); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	if err := ValidateWriterParamBody(p); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	if p.Namespace != "" {
		if err := AuthProjectPermission(c, p.Cluster, p.Namespace, c.GetHeader(HandlerOrgan), c.GetHeader(HandlerProject)); err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
	}
	if err := authK8sRBACWithWriteParam(c, p, rbac.VerbCreate); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	obj, err := cc.commonHandler.Create(c, p)
	if err != nil {
		logger.GetSugared().Errorf("create k8s error err: %v", err)
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, obj)
}

// commonCreateApiResource 是一个处理创建 Kubernetes 资源的通用接口。
// 它会根据客户端发送的请求内容（YAML 或 JSON 格式），解析并创建相应的 Kubernetes 资源。
// 请求路径中包含了集群名称以及其他资源信息，本方法根据这些信息来进行资源的创建。
//
//	@Summary		创建资源
//	@Description	该接口用于创建 Kubernetes 资源。根据客户端提供的 JSON 或 YAML 数据，创建指定的资源。
//	@Tags			通用Create接口
//	@Accept			application/json, application/yaml
//	@Produce		application/json
//	@Param			Authorization	header	string	false	"Bearer 用户令牌"
//	@Param			cluster			path	string	true	"集群名称"
//	@Param			request			body	string	true	"资源的 JSON 或 YAML 数据"
//	@Security		ApiKeyAuth
//	@Success		200	{object}	object
//	@Router			/k8s/clusters/{cluster}/api_resources [get]
func (cc *routessController) commonCreateApiResource(c *gin.Context) {
	// 1. 从请求中获取原始数据（支持 JSON 或 YAML 格式）
	rawData, u, err := parseRequestData(c)
	if err != nil {
		// 1.1 如果读取数据失败，返回错误响应
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	// 3. 构建写操作参数对象
	p := &common.WriteParam{
		Cluster:     c.Param("cluster"),          // 3.1 从 URL 参数获取集群名称
		DryRun:      c.Query("dryRun") == "true", // 3.2 判断是否为 dry-run 模式
		ContentType: c.ContentType(),             // 3.3 获取请求内容类型
	}
	p.Raw = rawData
	// 4. 验证写操作参数的有效性
	if err = ValidateWriterParamBody(p); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	// 5. 构建 Kubernetes 的 Unstructured 对象，用于操作 Kubernetes 资源
	obj := &unstructured.Unstructured{}
	obj.Object = u
	gvk := obj.GroupVersionKind()
	// 5.2 检验pvc
	err = validator.ValidateResource(obj, gvk)
	if err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.K8sError, err.Error()))
		return
	}
	// 5.2 如果命名空间为空，则设置为默认命名空间
	if err := setDefaultNamespace(obj); err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.K8sError, err.Error()))
		return
	}
	p.Raw, _ = obj.MarshalJSON()
	namespace := obj.GetNamespace()
	// 6. 根据集群名称获取对应的集群配置
	cluster, err := client.GetCluster(c.Param("cluster"))
	if err != nil {
		// 6.1 如果获取集群失败，返回错误响应
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	// 7. 获取资源的 REST 映射信息，用于解析资源的 GVR（Group-Version-Resource）
	mapping, err := cluster.GetClient().GetCtrlClient().RESTMapper().RESTMapping(gvk.GroupKind(), gvk.Version)
	if err != nil {
		// 7.1 如果映射失败，返回错误响应
		utils.Failed(c, errors.HandleK8sError(err))
		return
	}
	gvr := mapping.Resource
	// 8. 设置写操作参数的 GVR 信息和命名空间
	p.SetGroup(gvr.Group)
	p.SetVersion(gvr.Version)
	p.SetResourceType(gvr.Resource)
	p.SetNamespace(namespace)
	// 9. 校验项目的权限信息
	if p.Namespace != "" {
		if err := AuthProjectPermission(c, p.Cluster, p.Namespace, c.GetHeader(HandlerOrgan), c.GetHeader(HandlerProject)); err != nil {
			// 9.1 如果权限校验失败，返回错误响应
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
	}
	// 10. 如果是组织和项目相关资源，但资源的作用域为 Cluster，则禁止操作
	if c.GetHeader(HandlerOrgan) != "" &&
		c.GetHeader(HandlerProject) != "" &&
		mapping.Scope.Name() == meta.RESTScopeNameRoot {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.K8sForbidden, "cluster resource not allowed operation"))
		return
	}

	// 11. 校验 RBAC 权限，确保用户有创建资源的权限
	if err = authK8sRBACWithWriteParam(c, p, rbac.VerbCreate); err != nil {
		// 11.1 如果权限校验失败，返回错误响应
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}

	// 12. 调用通用处理器的 Create 方法，创建 Kubernetes 资源
	crateObj, err := cc.commonHandler.Create(c, p)
	if err != nil {
		// 12.1 如果创建失败，记录错误日志并返回错误响应
		logger.GetSugared().Errorf("create k8s error err: %v", err)
		utils.Failed(c, errors.HandleK8sError(err))
		return
	}

	// 13. 如果创建成功，返回成功响应
	utils.Succeed(c, crateObj)
}
