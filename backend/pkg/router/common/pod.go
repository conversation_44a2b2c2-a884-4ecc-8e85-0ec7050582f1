package common

import "github.com/gin-gonic/gin"

func (cc *routessController) podRoutes(routes *gin.RouterGroup) {

	// get pod logs
	//routes.GET("/clusters/:cluster/api/:version/:ptype/:pname/:resourcetype/:name/:stype", cc.podLogs)
	// get pod
	//routes.GET("/clusters/:cluster/api/v1/namespaces/:namespace/pods/:name", cc.podGet)
	//// update pod
	//routes.PUT("/clusters/:cluster/api/v1/namespaces/:namespace/pods/:name", cc.podUpdate)
	//// delete pod
	//routes.DELETE("/clusters/:cluster/api/v1/namespaces/:namespace/pods/:name", cc.podDelete)
	//// patch pod
	//routes.PATCH("/clusters/:cluster/api/v1/namespaces/:namespace/pods/:name", cc.podPatch)
	//// create pod
	//routes.POST("/clusters/:cluster/api/v1/namespaces/:namespace/pods", cc.podCreate)
	//// list pods
	//routes.GET("/clusters/:cluster/api/v1/namespaces/:namespace/pods", cc.podList)

}
func (cc *routessController) podList(c *gin.Context) {
	c.AddParam("version", "v1")
	c.AddParam("resourcetype", "pods")
	cc.commonList(c)
}

// podCreate ...
func (cc *routessController) podCreate(c *gin.Context) {
	c.AddParam("version", "v1")
	c.AddParam("resourcetype", "pods")
	cc.commonCreate(c)
}

// podPatch ...
func (cc *routessController) podPatch(c *gin.Context) {
	c.AddParam("version", "v1")
	c.AddParam("resourcetype", "pods")
	cc.commonPatch(c)
}

// podDelete ...
func (cc *routessController) podDelete(c *gin.Context) {
	c.AddParam("version", "v1")
	c.AddParam("resourcetype", "pods")
	cc.commonDelete(c)
}

// podUpdate ...
func (cc *routessController) podUpdate(c *gin.Context) {
	c.AddParam("version", "v1")
	c.AddParam("resourcetype", "pods")
	cc.commonUpdate(c)
}

// podGet ...
func (cc *routessController) podGet(c *gin.Context) {
	c.AddParam("version", "v1")
	c.AddParam("resourcetype", "pods")
	cc.commonGet(c)
}
