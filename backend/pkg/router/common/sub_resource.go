package common

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

func (cc *routessController) subResourceRoutes(routes *gin.RouterGroup) {

	routes.GET("/clusters/:cluster/api/:version/:ptype/:pname/:resourcetype/:name/:stype", cc.subGet)

}

func (cc *routessController) subGet(c *gin.Context) {
	p := &common.ReadParam{}
	if err := BindingReadParam(c, p); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	if c.Param("stype") == "log" && c.Param("resourcetype") == "pods" {
		cc.podLogs(c)
		return
	} else {
		utils.Failed(c, errors.NewFromError(c, errors.NewFromCode(errors.Var.K8sNotFound)))
		return
	}
}
