package common

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/rbac"
)

func (cc *routessController) updateRoutes(routes *gin.RouterGroup) {
	// core cluster
	routes.PUT("/clusters/:cluster/api/:version/:ptype/:pname", cc.commonUpdate)

	// core namespaced
	routes.PUT("/clusters/:cluster/api/:version/:ptype/:pname/:resourcetype/:name", cc.commonUpdate)

	// cluster
	routes.PUT("/clusters/:cluster/apis/:group/:version/:ptype/:pname", cc.commonUpdate)

	// namespaced
	routes.PUT("/clusters/:cluster/apis/:group/:version/:ptype/:pname/:resourcetype/:name", cc.commonUpdate)
}

// 通用Update接口
//
//	@Summary		通用Update接口
//	@Description	可更新K8S单个资源
//	@Tags			通用Update接口
//	@Accept			application/json
//	@Produce		application/json
//	@Param			Authorization	header	string	false	"Bearer 用户令牌"
//	@Param			cluster			path	string	true	"集群"
//	@Param			group			path	string	false	"资源Group"
//	@Param			version			path	string	true	"资源版本"
//	@Param			namespace		path	string	false	"命名空间"
//	@Param			resourcetype	path	string	true	"资源类型"	example(pods)
//	@Param			name			path	string	true	"资源名"
//	@Param			request			body	string	true	"资源JSON"
//	@Security		ApiKeyAuth
//	@Success		200	{object}	object
//	@Router			/k8s/clusters/{cluster}/api/{version}/{resourcetype}/{name} [put]
//	@Router			/k8s/clusters/{cluster}/api/{version}/namespaces/{namespace}/{resourcetype}/{name} [put]
//	@Router			/k8s/clusters/{cluster}/apis/{group}/{version}/{resourcetype}/{name} [put]
//	@Router			/k8s/clusters/{cluster}/apis/{group}/{version}/namespaces/{namespace}/{resourcetype}/{name} [put]
func (cc *routessController) commonUpdate(c *gin.Context) {
	ctx := utils.RequestContext(c)
	p := &common.WriteParam{
		DryRun:      c.Query("dryRun") == "true",
		ContentType: c.ContentType(),
	}
	if err := BindingWriteParam(c, p); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	var err error
	if err := ValidateWriterParamBody(p); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	if err := authK8sRBACWithWriteParam(c, p, rbac.VerbUpdate); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	if p.Namespace != "" {
		if err := AuthProjectPermission(c, p.Cluster, p.Namespace, c.GetHeader(HandlerOrgan), c.GetHeader(HandlerProject)); err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
	}
	obj, err := cc.commonHandler.Update(ctx, p)
	if err != nil {
		logger.GetSugared().Errorf("k8s update err: %v", err)
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, obj)
}
