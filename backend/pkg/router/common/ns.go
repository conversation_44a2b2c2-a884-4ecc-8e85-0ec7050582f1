package common

import "github.com/gin-gonic/gin"

func (cc *routessController) nsRoutes(routes *gin.RouterGroup) {
	// ns get ...
	//routes.GET("/clusters/:cluster/api/v1/namespaces/:namespace", cc.nsGet)
	//// ns list ...
	//routes.GET("/clusters/:cluster/api/v1/namespaces", cc.nsList)
	//// ns create ...
	//routes.POST("/clusters/:cluster/api/v1/namespaces", cc.nsCreate)
	//// ns update ...
	//routes.PUT("/clusters/:cluster/api/v1/namespaces/:namespace", cc.nsUpdate)
	//// ns patch ...
	//routes.PATCH("/clusters/:cluster/api/v1/namespaces/:namespace", cc.nsPatch)
	//// ns delete ...
	//routes.DELETE("/clusters/:cluster/api/v1/namespaces/:namespace", cc.nsDelete)
	//// ns delete all ...
	//routes.DELETE("/clusters/:cluster/api/v1/namespaces", cc.nsDeleteAll)
}

// nsGet ...
func (cc *routessController) nsGet(c *gin.Context) {
	c.AddParam("name", c.Param("namespace"))
	c.AddParam("group", "")
	c.AddParam("version", "v1")
	c.AddParam("resourcetype", "namespaces")
	cc.commonGet(c)
}

// nsList ...
func (cc *routessController) nsList(c *gin.Context) {
	c.AddParam("group", "")
	c.AddParam("version", "v1")
	c.AddParam("resourcetype", "namespaces")
	cc.commonList(c)
}

// nsCreate ...
func (cc *routessController) nsCreate(c *gin.Context) {
	c.AddParam("group", "")
	c.AddParam("version", "v1")
	c.AddParam("resourcetype", "namespaces")
	cc.commonCreate(c)
}

// nsUpdate ...
func (cc *routessController) nsUpdate(c *gin.Context) {
	c.AddParam("group", "")
	c.AddParam("name", c.Param("namespace"))
	c.AddParam("version", "v1")
	c.AddParam("resourcetype", "namespaces")
	cc.commonUpdate(c)
}

// nsPatch ...
func (cc *routessController) nsPatch(c *gin.Context) {
	c.AddParam("group", "")
	c.AddParam("name", c.Param("namespace"))
	c.AddParam("version", "v1")
	c.AddParam("resourcetype", "namespaces")
	cc.commonPatch(c)
}

// nsDelete ...
func (cc *routessController) nsDelete(c *gin.Context) {
	c.AddParam("group", "")
	c.AddParam("name", c.Param("namespace"))
	c.AddParam("version", "v1")
	c.AddParam("resourcetype", "namespaces")
	cc.commonDelete(c)
}

// nsDeleteAll ...
func (cc *routessController) nsDeleteAll(c *gin.Context) {
	c.AddParam("group", "")
	c.AddParam("version", "v1")
	c.AddParam("resourcetype", "namespaces")
	cc.commonDeleteAll(c)
}
