package common

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/rbac"
)

func (cc *routessController) describeRoutes(routes *gin.RouterGroup) {
	// core cluster
	routes.GET("/clusters/:cluster/api/:version/:ptype/:pname/describe", cc.describe)

	// core namespaced
	routes.GET("/clusters/:cluster/api/:version/:ptype/:pname/:resourcetype/:name/describe", cc.describe)

	// cluster
	routes.GET("/clusters/:cluster/apis/:group/:version/:ptype/:pname/describe", cc.describe)

	// namespaced
	routes.GET("/clusters/:cluster/apis/:group/:version/:ptype/:pname/:resourcetype/:name/describe", cc.describe)

}

// 通用Get接口
//
//	@Tags			通用Get接口
//	@Summary		通用Get接口
//	@Description	可以获取某一个k8s资源
//	@Param			cluster			path	string	true	"集群"
//	@Param			group			path	string	false	"API组别"
//	@Param			version			path	string	true	"api版本"
//	@Param			namespace		path	string	false	"资源所在Namespace"
//	@Param			resourcetype	path	string	true	"资源类型"
//	@Param			name			path	string	true	"资源名称"
//	@Router			/k8s/clusters/{cluster}/api/{version}/{resourcetype}/{name} [get]
//	@Router			/k8s/clusters/{cluster}/api/{version}/namespaces/{namespace}/{resourcetype}/{name} [get]
//	@Router			/k8s/clusters/{cluster}/apis/{group}/{version}/:resourcetype/{name} [get]
//	@Router			/k8s/clusters/{cluster}/apis/{group}/{version}/namespaces/{namespace}/{resourcetype}/{name} [get]
func (cc *routessController) describe(c *gin.Context) {
	ctx := utils.RequestContext(c)
	p := &common.ReadParam{}
	if err := BindingReadParam(c, p); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}

	if p.Namespace != "" {
		if err := AuthProjectPermission(c, p.Cluster, p.Namespace, c.GetHeader(HandlerOrgan), c.GetHeader(HandlerProject)); err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
	}
	p.Unstructured = parseQueryMapToUnstructured(c)
	//if inReadWhitelist(p) {
	//  logs.V(3).Infof(ctx, "request %v in whitelist", p)
	//} else if err := auth.AuthorizedByManagedCluster(c, p.Namespace, p.Group, p.ResourceType, utils.GetVerb, p.Cluster); err != nil {
	//  utils.Failed(c, err.(errors.Error))
	//  return
	//}
	if err := authK8sRBACWithReadParam(ctx, p, rbac.VerbGet); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	obj, err := cc.commonHandler.Describe(ctx, p)
	if err != nil {
		//logs.Errorf(ctx, "%v", err)
		utils.Failed(c, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(c, obj)
}
