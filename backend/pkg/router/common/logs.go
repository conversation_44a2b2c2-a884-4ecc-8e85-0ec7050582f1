package common

import (
	"bufio"
	"context"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

const (
	secWebSocketProtocolHeader = "Sec-WebSocket-Protocol"
)

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

// 通用Logs接口
//
//	@Tags			通用Logs接口
//	@Summary		通用Logs接口
//	@Description	读取k8s资源
//	@Param			cluster			path		string	true	"集群"
//	@Param			namespace		path		string	true	"资源所在Namespace"
//	@Param			name			path		string	true	"pod名称"
//	@Param			searchText		query		string	false	"模糊过滤搜索字段"
//	@Param			container		query		string	false	"容器名"
//	@Param			follow			query		bool	false	"是否持续输出，如果是true，则需要使用 websocket"
//	@Param			previous		query		bool	false	"是否展示已终止的容器日志"
//	@Param			tailLines		query		int		true	"显示最新日志行数，最多5000"
//	@Param			sinceSeconds	query		int		false	"展示当前时间过去几秒的日志"
//	@Success		200				{object}	object
//	@Router			/k8s/clusters/{cluster}/api/v1/namespaces/{namespace}/pods/{name}/log [get]
func (cc *routessController) podLogs(c *gin.Context) {
	var podLogReq models.PodLogRequest
	if err := c.ShouldBindQuery(&podLogReq); err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, "绑定参数出错"))
		return
	} else {
		if err := c.ShouldBindUri(&podLogReq); err != nil {
			utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, "绑定参数出错"))
			return
		} else {
			if err := podLogReq.Validate(); err != nil {
				utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, err.Error()))
				return
			}
		}
	}
	stream, err := cc.commonHandler.PodLogs(context.TODO(), &podLogReq)
	if err != nil {
		logger.GetSugared().Errorf("read k8s pod logs error: %v", err)
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	defer stream.Close()

	if podLogReq.Follow {
		responseHeader := http.Header{}
		responseHeader.Set("Access-Control-Allow-Origin", "*")
		if v := c.GetHeader(secWebSocketProtocolHeader); v != "" {
			responseHeader.Set(secWebSocketProtocolHeader, v)
		}
		conn, err := upgrader.Upgrade(c.Writer, c.Request, responseHeader)
		if err != nil {
			utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ConnectFailure, "建立查看pod日志长连接失败"))
			return
		}
		defer conn.Close()
		reader := bufio.NewReader(stream)

		for {
			line, err := reader.ReadString('\n')
			if strings.Contains(line, podLogReq.SearchText) {
				if err != nil {
					logger.GetSugared().Errorf("read message error: %v", err)
					return
				}
				if err := conn.WriteMessage(websocket.TextMessage, []byte(line)); err != nil {
					logger.GetSugared().Errorf("write message error: %v", err)
					return
				}
			}
		}
	} else {
		lines, err := io.ReadAll(stream)
		if err != nil {
			msg := fmt.Sprintf("failed to read logs, err=%s", err)
			logger.GetSugared().Errorf(msg)
			utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ConnectFailure, msg))
			return
		}
		utils.Succeed(c, string(lines))
	}

}
