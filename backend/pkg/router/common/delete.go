package common

import (
	"github.com/gin-gonic/gin"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/runtime/schema"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/rbac"
)

func (cc *routessController) deleteRoutes(routes *gin.RouterGroup) {
	// core cluster
	routes.DELETE("/clusters/:cluster/api/:version/:ptype/:pname", cc.commonDelete)

	// core namespaced
	routes.DELETE("/clusters/:cluster/api/:version/:ptype/:pname/:resourcetype/:name", cc.commonDelete)

	// cluster
	routes.DELETE("/clusters/:cluster/apis/:group/:version/:ptype/:pname", cc.commonDelete)

	// namespaced
	routes.DELETE("/clusters/:cluster/apis/:group/:version/:ptype/:pname/:resourcetype/:name", cc.commonDelete)

	// delete all

	// core cluster
	routes.DELETE("/clusters/:cluster/api/:version/:ptype", cc.commonDeleteAll)

	// core namespaced
	routes.DELETE("/clusters/:cluster/api/:version/:ptype/:pname/:resourcetype", cc.commonDeleteAll)

	// cluster
	routes.DELETE("/clusters/:cluster/apis/:group/:version/:ptype", cc.commonDeleteAll)

	// namespaced
	routes.DELETE("/clusters/:cluster/apis/:group/:version/:ptype/:pname/:resourcetype", cc.commonDeleteAll)

	// api-resource
	// core resource
	routes.DELETE("/clusters/:cluster/api/:version/:ptype/api_resources", cc.commonDeleteAPIResource)
	// resource
	routes.DELETE("/clusters/:cluster/apis/:group/:version/:ptype/api_resources", cc.commonDeleteAPIResource)

}

// 通用Delete接口
//
//	@Summary		通用Delete接口
//	@Description	可删除K8S单个资源
//	@Tags			通用Delete接口
//	@Accept			application/json
//	@Produce		application/json
//	@Param			Authorization	header	string	false	"Bearer 用户令牌"
//	@Param			cluster			path	string	true	"集群"
//	@Param			group			path	string	false	"资源Group"
//	@Param			version			path	string	true	"资源版本"
//	@Param			namespace		path	string	false	"命名空间"
//	@Param			resourcetype	path	string	true	"资源类型"	example(pods)
//	@Param			name			path	string	true	"资源名"
//	@Param			request			body	string	false	"delete options"
//	@Security		ApiKeyAuth
//	@Success		200	{object}	object
//	@Router			/k8s/clusters/{cluster}/api/{version}/{resourcetype}/{name} [delete]
//	@Router			/k8s/clusters/{cluster}/api/{version}/namespaces/{namespace}/{resourcetype}/{name} [delete]
//	@Router			/k8s/clusters/{cluster}/apis/{group}/{version}/{resourcetype}/{name} [delete]
//	@Router			/k8s/clusters/{cluster}/apis/{group}/{version}/namespaces/{namespace}/{resourcetype}/{name} [delete]
func (cc *routessController) commonDelete(c *gin.Context) {
	ctx := utils.RequestContext(c)
	p := &common.WriteParam{
		ContentType: c.ContentType(),
		DryRun:      c.Query("dryRun") == "true",
	}
	if err := BindingWriteParam(c, p); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	if p.Namespace != "" {
		if err := AuthProjectPermission(c, p.Cluster, p.Namespace, c.GetHeader(HandlerOrgan), c.GetHeader(HandlerProject)); err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
	}
	if err := authK8sRBACWithWriteParam(c, p, rbac.VerbDelete); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	err := cc.commonHandler.Delete(ctx, p)
	if err != nil {
		logger.GetSugared().Errorf("k8s delete err:%v", err)
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, nil)
}

// 通用Delete All接口
//
//	@Summary		通用Delete All接口
//	@Description	可删除K8S多个资源
//	@Tags			通用Delete All接口
//	@Accept			application/json
//	@Produce		application/json
//	@Param			Authorization	header	string	false	"Bearer 用户令牌"
//	@Param			cluster			path	string	true	"集群"
//	@Param			group			path	string	false	"资源Group"
//	@Param			version			path	string	true	"资源版本"
//	@Param			namespace		path	string	false	"命名空间"
//	@Param			resourcetype	path	string	true	"资源类型"	example(pods)
//	@Param			label_selector	query	string	false	"Label过滤"
//	@Param			request			body	string	false	"delete options"
//	@Security		ApiKeyAuth
//	@Success		200	{object}	object
//	@Router			/k8s/clusters/{cluster}/api/{version}/{resourcetype} [delete]
//	@Router			/k8s/clusters/{cluster}/api/{version}/namespaces/{namespace}/{resourcetype} [delete]
//	@Router			/k8s/clusters/{cluster}/apis/{group}/{version}/{resourcetype} [delete]
//	@Router			/k8s/clusters/{cluster}/apis/{group}/{version}/namespaces/{namespace}/{resourcetype} [delete]
func (cc *routessController) commonDeleteAll(c *gin.Context) {
	ctx := utils.RequestContext(c)
	p := &common.WriteParam{
		ContentType: c.ContentType(),
		DryRun:      c.Query("dryRun") == "true",
	}
	if err := BindingWriteParam(c, p); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	var err error
	if p.Namespace != "" {
		if err := AuthProjectPermission(c, p.Cluster, p.Namespace, c.GetHeader(HandlerOrgan), c.GetHeader(HandlerProject)); err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
	}
	p.LabelSelector, err = parseLabelSelector(c)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	p.FieldSelector, err = parseFieldSelector(c)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	if err := authK8sRBACWithWriteParam(c, p, rbac.VerbPatch); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	p.Raw, err = c.GetRawData()
	if err != nil {
		logger.GetSugared().Errorf("%v", err)
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	err = cc.commonHandler.DeleteAll(ctx, p)
	if err != nil {
		logger.GetSugared().Errorf("k8s delete err:%v", err)
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, nil)
}

func (cc *routessController) commonDeleteAPIResource(c *gin.Context) {

	var req models.K8sResourceDeleteRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}

	// 只允许在项目的命名空间进行删除
	organ := c.GetHeader(HandlerOrgan)
	project := c.GetHeader(HandlerProject)
	labelSet := labels.Set{}
	if project != "" {
		labelSet[LabelOrgan] = organ
		labelSet[LabelProject] = project
	}
	u := &unstructured.Unstructured{
		Object: map[string]interface{}{
			"labels": labelSet,
		},
	}
	p := &common.WriteParam{
		Unstructured: u,
		DryRun:       c.Query("dryRun") == "true",
	}
	if err := BindingWriteParam(c, p); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	gvr := schema.GroupVersionResource{
		Group:    p.Group,
		Version:  p.Version,
		Resource: p.ResourceType,
	}

	var namespaces []string
	for _, resource := range req.Resources {
		if resource.Namespace != "" {
			namespaces = append(namespaces, resource.Namespace)
		}
	}
	if utils.InProjectWorkspace(c) {
		for _, ns := range namespaces {
			if err := AuthProjectPermission(c, p.Cluster, ns, c.GetHeader(HandlerOrgan), c.GetHeader(HandlerProject)); err != nil {
				utils.Failed(c, errors.NewFromError(c, err))
				return
			}
		}
	}

	if err := authK8sRBACWriteParamWithNamespaces(c, p, gvr, namespaces, rbac.VerbDelete); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	err := cc.commonHandler.DeleteApiResources(c, p, req)
	if err != nil {
		logger.GetSugared().Errorf("k8s delete err:%v", err)
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}

	utils.SucceedWithoutData(c)

}
