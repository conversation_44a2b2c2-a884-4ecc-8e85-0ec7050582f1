package common

import (
	"context"
	"fmt"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm/utils"
	clientmgr "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	coreV1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/runtime/schema"
	klog "k8s.io/klog/v2"
	ctrlclient "sigs.k8s.io/controller-runtime/pkg/client"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/rbac"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

const (
	Labels         = "labels"
	LabelOrgan     = "skyview/organ"
	LabelProject   = "skyview/project"
	HandlerOrgan   = "amp-organ-id"
	HandlerProject = "skyview-project-id"
	// AllNamespaceScoped allow all namespace
	AllNamespaceScoped = "*"
)

func NewCommonAPIsController() routerutil.ApiController {
	return &routessController{
		commonHandler: common.NewCommonHandler(),
	}
}

type routessController struct {
	commonHandler common.CommonIntf
}

func (cc *routessController) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group("/k8s")
}

func (cc *routessController) RegisterRouter(routes *gin.RouterGroup) {
	//cc.clusterRoutes(routes)

	cc.apiRoutes(routes)

	cc.nsRoutes(routes)

	// read verbs
	cc.listRoutes(routes)
	cc.getRoutes(routes)
	cc.describeRoutes(routes)

	cc.subResourceRoutes(routes)

	// write verbs
	cc.createRoutes(routes)
	cc.updateRoutes(routes)
	cc.patchRoutes(routes)
	cc.deleteRoutes(routes)

	// cc.podRoutes(routes)
}

func AuthProjectPermission(ctx context.Context, cluster, namespace, organ, project string) error {
	if project == "" {
		klog.V(2).Infof("project is empty")
		return nil
	}
	var (
		allowNamespace []string
		namespaceList  coreV1.NamespaceList
	)
	c, err := clientmgr.OnlineClusterAssert(clientmgr.GetCluster(cluster))
	if err != nil {
		return errors.NewFromError(ctx, err)
	}
	labelSelector := labels.SelectorFromSet(labels.Set{
		LabelOrgan:   organ,
		LabelProject: project,
	})
	err = c.GetClient().GetCtrlClient().List(ctx, &namespaceList, &ctrlclient.ListOptions{
		LabelSelector: labelSelector,
	})
	if err != nil {
		return errors.NewFromError(ctx, err)
	}
	for _, ns := range namespaceList.Items {
		allowNamespace = append(allowNamespace, ns.Name)
	}
	if !utils.Contains(allowNamespace, namespace) {
		return errors.NewFromCodeWithMessage(errors.Var.K8sMethodNotAllowed, fmt.Sprintf("project %s has no permission to access namespace %s", project, namespace))
	}
	return nil
}

func authK8sRBACWithWriteParam(ctx context.Context, w *common.WriteParam, verb string) error {
	authRequest := &rbac.AuthRequest{SelfSubjectAccessReview: &rbac.SelfSubjectAccessReviewRequest{
		Cluster:   w.Cluster,
		Namespace: w.Namespace,
		Group:     w.Group,
		Version:   w.Version,
		Resource:  w.ResourceType,
		Verb:      verb,
	}}
	if err := rbac.AuthK8sRBAC(ctx, authRequest); err != nil {
		return err
	}
	return nil
}

func authK8sRBACWithReadParam(ctx context.Context, r *common.ReadParam, verb string) error {
	authRequest := &rbac.AuthRequest{SelfSubjectAccessReview: &rbac.SelfSubjectAccessReviewRequest{
		Cluster:   r.Cluster,
		Namespace: r.Namespace,
		Group:     r.Group,
		Version:   r.Version,
		Resource:  r.ResourceType,
		Verb:      verb,
	}}
	if err := rbac.AuthK8sRBAC(ctx, authRequest); err != nil {
		return err
	}
	return nil
}

func authK8sRBACReadParamWithNamespace(ctx context.Context, r *common.ReadParam, gvr schema.GroupVersionResource, namespace string, verb string) error {
	return authK8sRBACReadParamWithNamespaces(ctx, r, gvr, []string{namespace}, verb)
}

func authK8sRBACWriteParamWithNamespace(ctx context.Context, w *common.WriteParam, gvr schema.GroupVersionResource, namespace string, verb string) error {
	return authK8sRBACWriteParamWithNamespaces(ctx, w, gvr, []string{namespace}, verb)
}

func authK8sRBACWriteParamWithNamespaces(ctx context.Context, w *common.WriteParam, gvr schema.GroupVersionResource, namespaces []string, verb string) error {

	for _, namespace := range namespaces {
		authRequest := &rbac.AuthRequest{SelfSubjectAccessReview: &rbac.SelfSubjectAccessReviewRequest{
			Cluster:   w.Cluster,
			Namespace: namespace,
			Group:     gvr.Group,
			Version:   gvr.Version,
			Resource:  gvr.Resource,
			Verb:      verb,
		}}
		if err := rbac.AuthK8sRBAC(ctx, authRequest); err != nil {
			return err
		}
	}
	return nil
}

func authK8sRBACReadParamWithNamespaces(ctx context.Context, r *common.ReadParam, gvr schema.GroupVersionResource, namespaces []string, verb string) error {
	for _, namespace := range namespaces {
		authRequest := &rbac.AuthRequest{SelfSubjectAccessReview: &rbac.SelfSubjectAccessReviewRequest{
			Cluster:   r.Cluster,
			Namespace: namespace,
			Group:     gvr.Group,
			Version:   gvr.Version,
			Resource:  gvr.Resource,
			Verb:      verb,
		}}
		if err := rbac.AuthK8sRBAC(ctx, authRequest); err != nil {
			return err
		}
	}
	return nil
}

// parseLabelSelector ...
func parseLabelSelector(c *gin.Context) (labels.Selector, error) {
	// first query labelSelector
	// second query label_selector
	value := c.Query("labelSelector")
	if value != "" {
		selector, err := labels.Parse(value)
		if err != nil {
			return nil, fmt.Errorf("failed to parse url parameter[labelSelector=%s], err=%w", value, err)
		}
		return selector, nil
	}
	value = c.Query("label_selector")
	if value != "" {
		selector, err := labels.Parse(value)
		if err != nil {
			return nil, fmt.Errorf("failed to parse url parameter[label_selector=%s], err=%w", value, err)
		}
		return selector, nil
	}
	return nil, nil
}

// parseFieldSelector ...
func parseFieldSelector(c *gin.Context) (fields.Selector, error) {
	value := c.Query("fieldSelector")
	if value != "" {
		selector, err := fields.ParseSelector(value)
		if err != nil {
			return nil, fmt.Errorf("failed to parse url parameter[fieldSelector=%s], err=%w", value, err)
		}

		return selector, nil
	}
	value = c.Query("field_selector")
	if value != "" {
		selector, err := fields.ParseSelector(value)
		if err != nil {
			return nil, fmt.Errorf("failed to parse url parameter[filed_selector=%s], err=%w", value, err)
		}
		return selector, nil
	}
	return nil, nil
}

// parseQueryMapToUnstructured
func parseQueryMapToUnstructured(c *gin.Context) *unstructured.Unstructured {
	object := map[string]any{}
	queryParams := c.Request.URL.Query() // get all query
	for key, values := range queryParams {
		object[key] = values[0]
	}
	return &unstructured.Unstructured{Object: object}
}
