package common

import (
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	clientmgr "harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
)

func (cc *routessController) apiRoutes(routes *gin.RouterGroup) {

	// versions
	routes.GET("/clusters/:cluster/version", cc.k8sVersion)
	// api resources
	routes.GET("/clusters/:cluster/api", cc.listApiResources)
	routes.GET("/clusters/:cluster/api/:version", cc.listApiResources)
	// apis resources
	routes.GET("/clusters/:cluster/apis", cc.listApiResources)
	routes.GET("/clusters/:cluster/apis/:group", cc.listApiResources)
	routes.GET("/clusters/:cluster/apis/:group/:version", cc.listApiResources)
}

func (cc *routessController) k8sVersion(c *gin.Context) {
	cluster := c.Param("cluster")
	if cli, err := clientmgr.GetCluster(cluster); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	} else {
		if v, err := cli.GetClient().GetClientSet().Discovery().ServerVersion(); err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		} else {
			c.JSON(200, v)
			return
		}
	}
}

func (cc *routessController) listApiResources(c *gin.Context) {
	p := &common.ListAPIResourceParam{
		Cluster: c.Param("cluster"),
	}
	isCoreApi := strings.HasSuffix(c.Request.URL.Path, "api")
	p.Filter = utils.ParseQueryParams[unstructured.Unstructured](c)
	p.Filter.Limit = lo.ToPtr(99999)
	group, version := c.Param("group"), c.Param("version")
	p.Filter.WithFn(func(item unstructured.Unstructured) bool {
		gv, found, err := unstructured.NestedString(item.Object, "groupVersion")
		if err != nil || !found {
			return false
		}
		groupVersion, err := schema.ParseGroupVersion(gv)
		if err != nil {
			return false
		}
		if group == "" && version == "" {
			if isCoreApi {
				if groupVersion.Group == "" {
					return true
				}
			} else {
				if groupVersion.Group != "" {
					return true
				}
			}
		}

		return groupVersion.Group == group && groupVersion.Version == version
	})
	clusterApiResources, err := cc.commonHandler.ListAPIResources(c, p)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	p.Namespaced = true
	clusterNamespacedApiResources, err := cc.commonHandler.ListAPIResources(c, p)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	var apiResourceLists []*metav1.APIResourceList
	for _, item := range append(clusterApiResources.Items, clusterNamespacedApiResources.Items...) {
		newItem := &metav1.APIResourceList{}
		if err := runtime.DefaultUnstructuredConverter.FromUnstructured(item.Object, newItem); err != nil {
			continue
		}
		apiResourceLists = append(apiResourceLists, newItem)
	}
	if group == "" && version == "" {
		apiGroupList := convertAPIResourceListToAPIVersions(apiResourceLists)
		c.JSON(200, apiGroupList)
	} else if group != "" && version == "" {
		apiGroupList := convertAPIResourceListToAPIGroupList(apiResourceLists)
		if len(apiGroupList.Groups) > 0 {
			c.JSON(200, apiGroupList.Groups[0])
		} else {
			c.JSON(200, &metav1.APIGroup{})
		}
	} else if group != "" && version != "" {
		if len(apiResourceLists) > 0 {
			list := apiResourceLists[0]
			list.SetGroupVersionKind(metav1.SchemeGroupVersion.WithKind("APIResourceList"))
			c.JSON(200, list)
		} else {
			c.JSON(200, &metav1.APIResourceList{})
		}
	} else if group == "" && version != "" {
		if len(apiResourceLists) > 0 {
			list := apiResourceLists[0]
			list.SetGroupVersionKind(metav1.SchemeGroupVersion.WithKind("APIResourceList"))
			c.JSON(200, list)
		} else {
			c.JSON(200, &metav1.APIResourceList{})
		}
	}
}

// 将 APIResourceList 转换为 API 版本列表
func convertAPIResourceListToAPIVersions(apiResourceLists []*metav1.APIResourceList) metav1.APIVersions {
	versionSet := make(map[string]struct{}) // 使用 map 去重
	for _, resourceList := range apiResourceLists {
		// 提取版本信息
		groupVersion, err := schema.ParseGroupVersion(resourceList.GroupVersion)
		if err != nil {
			continue
		}
		// 添加到集合
		versionSet[groupVersion.Version] = struct{}{}
	}
	// 将集合转换为切片
	versions := make([]string, 0, len(versionSet))
	for version := range versionSet {
		versions = append(versions, version)
	}

	return metav1.APIVersions{
		TypeMeta: metav1.TypeMeta{
			Kind: "APIVersions",
		},
		Versions:                   versions,
		ServerAddressByClientCIDRs: nil,
	}
}

//func convertAPIResourceListToAPIGroup(resourceList *metav1.APIResourceList) metav1.APIGroup {
//	// 解析 Group 和 Version
//	groupVersion, _ := schema.ParseGroupVersion(resourceList.GroupVersion)
//	var group, version = groupVersion.Group, groupVersion.Version
//
//	// 构造 APIGroup
//	apiGroup := metav1.APIGroup{
//		Name: group,
//		Versions: []metav1.GroupVersionForDiscovery{
//			{
//				GroupVersion: resourceList.GroupVersion,
//				Version:      version,
//			},
//		},
//		PreferredVersion: metav1.GroupVersionForDiscovery{
//			GroupVersion: resourceList.GroupVersion,
//			Version:      version,
//		},
//	}
//
//	return apiGroup
//}

func convertAPIResourceListToAPIGroupList(apiResourceLists []*metav1.APIResourceList) metav1.APIGroupList {
	groupMap := make(map[string]*metav1.APIGroup) // 用于分组和去重
	apiGroupList := metav1.APIGroupList{}

	for _, resourceList := range apiResourceLists {
		// 解析 Group 和 Version
		groupVersion, err := schema.ParseGroupVersion(resourceList.GroupVersion)
		if err != nil {
			continue
		}
		var group, version = groupVersion.Group, groupVersion.Version

		// 如果 Group 不存在于 map 中，创建新的 APIGroup
		if _, exists := groupMap[group]; !exists {
			groupMap[group] = &metav1.APIGroup{
				Name: group,
			}
		}

		// 获取对应 APIGroup 的 reference
		apiGroup := groupMap[group]

		// 检查当前版本是否已经添加到 APIGroup
		versionExists := false
		for _, v := range apiGroup.Versions {
			if v.Version == version {
				versionExists = true
				break
			}
		}

		// 如果版本尚未添加，添加到 Versions 并设置 PreferredVersion
		if !versionExists {
			apiGroup.Versions = append(apiGroup.Versions, metav1.GroupVersionForDiscovery{
				GroupVersion: resourceList.GroupVersion,
				Version:      version,
			})

			// 设置首个版本为 PreferredVersion
			if apiGroup.PreferredVersion.GroupVersion == "" {
				apiGroup.PreferredVersion = metav1.GroupVersionForDiscovery{
					GroupVersion: resourceList.GroupVersion,
					Version:      version,
				}
			}
		}
	}

	// 将 map 转换为 slice
	for _, group := range groupMap {
		apiGroupList.Groups = append(apiGroupList.Groups, *group)
	}

	return apiGroupList
}
