package common

import (
	"encoding/json"
	"fmt"
	"strings"

	"github.com/gin-gonic/gin"
	yaml "gopkg.in/yaml.v2"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/common"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
)

var readWhitelist = []common.ReadParam{
	{
		Group:        "",
		Version:      "v1",
		Namespace:    "kube-public",
		ResourceType: "configmap",
	},
}

func inReadWhitelist(p *common.ReadParam) bool {
	for _, w := range readWhitelist {
		if w.Group == p.Group &&
			w.Version == p.Version &&
			allOrEqual(w.Namespace, p.Namespace) &&
			resourceTypeEqual(w.ResourceType, p.ResourceType) &&
			allOrEqual(w.Name, p.Name) {
			return true
		}
	}
	return false
}

func resourceTypeEqual(a, b string) bool {
	return strings.EqualFold(a, b) || strings.EqualFold(a+"s", b) || strings.EqualFold(a+"es", b)
}

func allOrEqual(a, b string) bool {
	return len(a) == 0 || a == b
}

// getNamespaceFromContext ...
func getNamespaceFromContext(c *gin.Context) string {
	ns := c.Param("namespace")
	if ns == AllNamespaceScoped {
		return ""
	}
	return ns
}

// ValidateWriterParamBody ...
func ValidateWriterParamBody(p common.WriterParam) error {
	u := map[string]any{}
	switch p.GetContentType() {
	case runtime.ContentTypeYAML:
		err := yaml.Unmarshal(p.GetRaw(), &u)
		if err != nil {
			return errors.NewFromCodeWithMessage(errors.Var.K8sError, err.Error())
		}
	default:
		err := json.Unmarshal(p.GetRaw(), &u)
		if err != nil {
			return errors.NewFromCodeWithMessage(errors.Var.K8sError, err.Error())
		}
	}

	if p.GetNamespace() == "" {
		nestedString, _, _ := unstructured.NestedString(u, "metadata", "namespace")
		if nestedString != "" {
			p.SetNamespace(nestedString)
		}
	}

	// check apiVersion and kind is not empty
	apiVersion, _, _ := unstructured.NestedString(u, "apiVersion")
	if apiVersion == "" {
		return errors.NewFromCodeWithMessage(errors.Var.K8sInvalid, "apiVersion is required")
	}
	kind, _, _ := unstructured.NestedString(u, "kind")
	if kind == "" {
		return errors.NewFromCodeWithMessage(errors.Var.K8sInvalid, "kind is required")
	}

	if p.GetName() != "" {
		nestedString, _, _ := unstructured.NestedString(u, "metadata", "name")
		if nestedString != p.GetName() {
			return errors.NewFromCodeWithMessage(errors.Var.K8sInvalid, fmt.Sprintf("metadata.name must be %s", p.GetName()))
		}
	}

	if p.GetNamespace() != "" {
		nestedString, _, _ := unstructured.NestedString(u, "metadata", "namespace")
		if nestedString != p.GetNamespace() {
			return errors.NewFromCodeWithMessage(errors.Var.K8sInvalid, fmt.Sprintf("metadata.namespace must be %s", p.GetNamespace()))
		}
	}

	return nil
}

// BindingWriteParam ...
func BindingWriteParam(c *gin.Context, p *common.WriteParam) error {
	p.Cluster = c.Param("cluster")
	p.Group = c.Param("group")
	p.Version = c.Param("version")
	p.ContentType = c.ContentType()
	var err error
	p.Raw, err = c.GetRawData()
	if err != nil {
		return errors.NewFromCodeWithMessage(errors.Var.ParamError, err.Error())
	}
	p.Unstructured = parseQueryMapToUnstructured(c)
	if c.Param("resourcetype") != "" {
		if c.Param("ptype") == "namespaces" || c.Param("ptype") == "" {
			c.AddParam("namespace", c.Param("pname"))
			p.Namespace = getNamespaceFromContext(c)
		} else {
			return errors.NewFromCodeWithMessage(errors.Var.K8sNotFound, "resource not found")
		}
		p.ResourceType = c.Param("resourcetype")
		p.Name = c.Param("name")
	} else {
		p.ResourceType = c.Param("ptype")
		p.Name = c.Param("pname")
	}
	return nil
}

// BindingReadParam ...
func BindingReadParam(c *gin.Context, p *common.ReadParam) error {
	p.Cluster = c.Param("cluster")
	p.Group = c.Param("group")
	p.Version = c.Param("version")
	if c.Param("resourcetype") != "" {
		if c.Param("ptype") == "namespaces" || c.Param("ptype") == "" {
			c.AddParam("namespace", c.Param("pname"))
			p.Namespace = getNamespaceFromContext(c)
		} else {
			return errors.NewFromCodeWithMessage(errors.Var.K8sNotFound, "resource not found")
		}
		p.ResourceType = c.Param("resourcetype")
		p.Name = c.Param("name")
	} else {
		p.ResourceType = c.Param("ptype")
		p.Name = c.Param("pname")
	}
	return nil
}

func parseRequestData(c *gin.Context) ([]byte, map[string]any, error) {
	rawData, err := c.GetRawData()
	if err != nil {
		return nil, nil, err
	}

	u := map[string]any{}
	switch c.ContentType() {
	case runtime.ContentTypeYAML:
		err = yaml.Unmarshal(rawData, &u)
	default:
		err = json.Unmarshal(rawData, &u)
	}
	if err != nil {
		return nil, nil, errors.NewFromCodeWithMessage(errors.Var.K8sError, err.Error())
	}

	return rawData, u, nil
}

func setDefaultNamespace(obj *unstructured.Unstructured) error {
	v, found, err := unstructured.NestedFieldNoCopy(obj.Object, "metadata")
	if err != nil {
		return err
	}
	if !found || v == nil {
		obj.Object["metadata"] = map[string]any{}
	}
	if obj.GetNamespace() == "" {
		obj.SetNamespace(corev1.NamespaceDefault)
	}
	return nil
}
