package common

import (
	"github.com/gin-gonic/gin"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/rbac"
)

func (cc *routessController) patchRoutes(routes *gin.RouterGroup) {
	// core cluster
	routes.PATCH("/clusters/:cluster/api/:version/:ptype/:pname", cc.commonPatch)

	// core namespaced
	routes.PATCH("/clusters/:cluster/api/:version/:ptype/:pname/:resourcetype/:name", cc.commonPatch)

	// cluster
	routes.PATCH("/clusters/:cluster/apis/:group/:version/:ptype/:pname", cc.commonPatch)

	// namespaced
	routes.PATCH("/clusters/:cluster/apis/:group/:version/:ptype/:pname/:resourcetype/:name", cc.commonPatch)
}

// 通用Patch接口
//
//	@Summary		通用Patch接口
//	@Description	可更新K8S单个资源
//	@Tags			通用Patch接口
//	@Produce		application/json
//	@Param			Authorization	header	string	false	"Bearer 用户令牌"
//	@Param			cluster			path	string	true	"集群"
//	@Param			group			path	string	false	"资源Group"
//	@Param			version			path	string	true	"资源版本"
//	@Param			namespace		path	string	false	"命名空间"
//	@Param			resourcetype	path	string	true	"资源类型"	example(pods)
//	@Param			name			path	string	true	"资源名"
//	@Param			Content-Type	header	string	true	"Patch方式[application/json-patch+json,application/merge-patch+json,application/strategic-merge-patch+json,application/apply-patch+yaml]"
//	@Param			request			body	string	true	"资源Patch JSON"
//	@Security		ApiKeyAuth
//	@Success		200	{object}	object
//	@Router			/k8s/clusters/{cluster}/api/{version}/{resourcetype}/{name} [patch]
//	@Router			/k8s/clusters/{cluster}/api/{version}/namespaces/{namespace}/{resourcetype}/{name} [patch]
//	@Router			/k8s/clusters/{cluster}/apis/{group}/{version}/{resourcetype}/{name} [patch]
//	@Router			/k8s/clusters/{cluster}/apis/{group}/{version}/namespaces/{namespace}/{resourcetype}/{name} [patch]
func (cc *routessController) commonPatch(c *gin.Context) {
	ctx := utils.RequestContext(c)
	p := &common.WriteParam{
		DryRun:      c.Query("dryRun") == "true",
		ContentType: c.ContentType(),
	}
	if err := BindingWriteParam(c, p); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	if err := ValidateWriterParamBody(p); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	if err := authK8sRBACWithWriteParam(c, p, rbac.VerbPatch); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	if p.Namespace != "" {
		if err := AuthProjectPermission(c, p.Cluster, p.Namespace, c.GetHeader(HandlerOrgan), c.GetHeader(HandlerProject)); err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
	}
	obj, err := cc.commonHandler.Patch(ctx, p)
	if err != nil {
		logger.GetSugared().Errorf("patch k8s error err: %v", err)
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, obj)
}
