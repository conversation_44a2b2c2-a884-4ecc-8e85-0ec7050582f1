package router

import (
	"os"
	"strings"

	logical_unit "harmonycloud.cn/unifiedportal/portal/backend/pkg/router/logical-unit"

	"github.com/gin-gonic/gin"
	"github.com/gin-gonic/gin/binding"
	validator "github.com/go-playground/validator/v10"
	swaggerFiles "github.com/swaggo/files"
	ginSwagger "github.com/swaggo/gin-swagger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/config"
	_ "harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/router/addon"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/router/auth"
	devopssystem "harmonycloud.cn/unifiedportal/portal/backend/pkg/router/backup-restore"
	backupserver "harmonycloud.cn/unifiedportal/portal/backend/pkg/router/backup-server"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/router/baseline"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/router/cloudservice"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/router/cluster"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/router/common"
	configRouter "harmonycloud.cn/unifiedportal/portal/backend/pkg/router/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/router/console"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/router/draft"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/router/example"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/router/gateway"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/router/health"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/router/internalopenapi"
	monitoring "harmonycloud.cn/unifiedportal/portal/backend/pkg/router/monitoring"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/router/node"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/router/project/workload"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/router/template"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/router/workspace"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/midware"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
	harmonycloudvalidator "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/validator"
	"harmonycloud.cn/unifiedportal/portal/docs"
)

var controllerFuncs = []func() routerutil.ApiController{
	example.NewPodExampleRouter,
	common.NewCommonAPIsController,
	cloudservice.NewCloudserviceRouter,
	monitoring.NewCloudserviceRouter,
	internalopenapi.NewInternalOpenApiRouter,
	auth.NewAuthRouter,
	workspace.NewWorkspaceController,
	health.NewHealthRouter,
	devopssystem.NewSystemConfigRouter,
	devopssystem.NewVeleroController,
	cluster.NewRouter,
	cluster.NewCreateRouter,
	draft.NewRouter,
	cluster.NewClusterUpgradeAPIsController,
	configRouter.NewRouter,
	template.NewRouter,
	addon.NewRouter,
	node.NewNodeUpDownRouter,
	node.NewControlNodeRouter,
	node.NewNodeVerifyRouter,
	gateway.NewGatewayHealthRouter,
	backupserver.NewBackupServerRouter,
	workload.NewWorkloadController,
	baseline.NewCategoryRouter,
	baseline.NewStrategyRouter,
	baseline.NewStandardRouter,
	baseline.NewCheckerRouter,
	logical_unit.NewLogicalUnitRouter,
	console.NewRouter,
}

// InitGinRouter init gin router
//
//	@basePath					/
//	@title						Olympus Portal API
//	@version					1.0.0
//	@securityDefinitions.apikey	jwt
//	@in							header
//	@name						Authorization
func InitGinRouter() *gin.Engine {
	engine := gin.Default()
	engine.Use()
	// todo logconfig
	// add mid ware handlers
	midware.AddMidWareHandler(engine)

	// register binding
	if v, ok := binding.Validator.Engine().(*validator.Validate); ok {
		harmonycloudvalidator.AddRegister(v)
	}

	// swagger
	applySwagger(engine)

	// init baseline handlers
	baseline.InitializeHandlers()

	// register router
	for _, cf := range controllerFuncs {
		c := cf()
		router := c.GetGroup(engine)
		c.RegisterRouter(router)
	}
	return engine
}

func applySwagger(engine *gin.Engine) {
	if strings.EqualFold(config.EnableSwagger.Value, "true") {
		// swagger
		if os.Getenv("SWAGGER_BASE_PATH") != "" {
			docs.SwaggerInfo.BasePath = os.Getenv("SWAGGER_BASE_PATH")
		}
		engine.GET("/swagger/*any", ginSwagger.WrapHandler(
			swaggerFiles.Handler, ginSwagger.PersistAuthorization(true)))
	}
}
