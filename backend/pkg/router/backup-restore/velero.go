package backup_restore

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/backuprestore"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/storageserver"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

type veleroRouter struct {
	backuprestore.SchedulerInterface
	backuprestore.BackupInterface
	backuprestore.PodVolumeBackupInterface
	backuprestore.PodVolumeRestoreInterface
	backuprestore.RestoreTemplateInterface
	backuprestore.RestoreInterface
	storageserver.VeleroStorageServersInterface
}

func NewVeleroController() routerutil.ApiController {
	return &veleroRouter{
		SchedulerInterface:            backuprestore.NewSchedulerInterface(),
		BackupInterface:               backuprestore.NewBackupInterface(),
		PodVolumeBackupInterface:      backuprestore.NewPodVolumeBackupInterface(),
		PodVolumeRestoreInterface:     backuprestore.NewPodVolumeRestoreInterface(),
		RestoreTemplateInterface:      backuprestore.NewRestoreTemplateInterface(),
		RestoreInterface:              backuprestore.NewRestoreInterface(),
		VeleroStorageServersInterface: storageserver.NewVeleroStorageServerInterface(),
	}
}

func (r veleroRouter) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.ApiV1Group + utils.System + "/backuprestore")
}

func (r veleroRouter) RegisterRouter(group *gin.RouterGroup) {
	// 备份策略router
	r.appendVeleroSchedulesRouter(group)
	// 恢复策略router
	r.appendVeleroRestoreRouter(group)
}
