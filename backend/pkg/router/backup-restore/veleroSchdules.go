package backup_restore

import (
	"net/http"
	"sort"

	"github.com/gin-gonic/gin"
	veleroV1 "github.com/vmware-tanzu/velero/pkg/apis/velero/v1"
	"harmonycloud.cn/unifiedportal/midware-go/midwares/audit"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/velero"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

func init() {
	audit.VAR.Register(
		http.MethodPost,
		utils.ApiV1Group+utils.System+"/backuprestore"+"/schedules",
		"backup-recover",
		"新建数据备份策略-{schedulesName}",
		audit.NewRequestParamCfgRaw("schedulesName", "$.schedulesName"),
		audit.NewRequestParamCfgRaw("requestBody", "$"),
	)

	audit.VAR.Register(
		http.MethodPut,
		utils.ApiV1Group+utils.System+"/backuprestore"+"/schedules/:scheduleName",
		"backup-recover",
		"编辑数据备份策略-{scheduleName}",
		audit.NewDefaultRequestParamCfgPath("scheduleName"),
		audit.NewRequestParamCfgRaw("requestBody", "$"),
	)

	audit.VAR.Register(
		http.MethodPost,
		utils.ApiV1Group+utils.System+"/backuprestore"+"/clusterName/:clusterName/schedules/:scheduleName/execute",
		"backup-recover",
		"执行数据备份策略-{scheduleName}",
		audit.NewDefaultRequestParamCfgPath("scheduleName"),
		audit.NewDefaultRequestParamCfgPath("clusterName"),
	)

	audit.VAR.Register(
		http.MethodPost,
		utils.ApiV1Group+utils.System+"/backuprestore"+"/clusterName/:clusterName/schedules/:scheduleName/start",
		"backup-recover",
		"启动数据备份策略-{scheduleName}",
		audit.NewDefaultRequestParamCfgPath("scheduleName"),
		audit.NewDefaultRequestParamCfgPath("clusterName"),
	)

	audit.VAR.Register(
		http.MethodPost,
		utils.ApiV1Group+utils.System+"/backuprestore"+"/clusterName/:clusterName/schedules/:scheduleName/stop",
		"backup-recover",
		"停止数据备份策略-{scheduleName}",
		audit.NewDefaultRequestParamCfgPath("scheduleName"),
		audit.NewDefaultRequestParamCfgPath("clusterName"),
	)

	audit.VAR.Register(
		http.MethodPost,
		utils.ApiV1Group+utils.System+"/backuprestore"+"/clusterName/:clusterName/schedules/:scheduleName/syncStorageServer",
		"backup-recover",
		"同步数据备份策略-{scheduleName}",
		audit.NewDefaultRequestParamCfgPath("scheduleName"),
		audit.NewDefaultRequestParamCfgPath("clusterName"),
	)

	audit.VAR.Register(
		http.MethodDelete,
		utils.ApiV1Group+utils.System+"/backuprestore"+"/schedules/:scheduleName/backups/:backupName",
		"backup-recover",
		"删除数据备份策略备份记录-{scheduleName}",
		audit.NewDefaultRequestParamCfgPath("scheduleName"),
		audit.NewDefaultRequestParamCfgPath("backupName"),
		audit.NewDefaultRequestParamCfgQuery("clusterName"),
	)

	audit.VAR.Register(
		http.MethodDelete,
		utils.ApiV1Group+utils.System+"/backuprestore"+"/schedules/:scheduleName",
		"backup-recover",
		"删除数据备份策略-{scheduleName}",
		audit.NewDefaultRequestParamCfgPath("scheduleName"),
		audit.NewDefaultRequestParamCfgQuery("clusterName"),
	)

}

func (r *veleroRouter) appendVeleroSchedulesRouter(group *gin.RouterGroup) {
	group.GET("/schedules", r.listSchedules)
	group.GET("/schedules/:scheduleName", r.getSchedules)
	group.GET("/schedules/:scheduleName/backups", r.listSchedulesBackups)
	group.GET("/schedules/:scheduleName/backups/:backupName/resources", r.listBackupsResources)
	group.GET("/schedules/:scheduleName/backups/:backupName/persistentVolumes", r.listBackupsVolumes)
	group.GET("/schedules/:scheduleName/backups/:backupName/logs", r.logsBackups)

	group.POST("/schedules", r.createSchedules)
	group.POST("/schedules/nameCheck", r.schedulesNameCheck)
	group.PUT("/schedules/:scheduleName", r.updateSchedules)
	group.POST("/clusterName/:clusterName/schedules/:scheduleName/execute", r.executeSchedules)
	group.POST("/clusterName/:clusterName/schedules/:scheduleName/start", r.startSchedules)
	group.POST("/clusterName/:clusterName/schedules/:scheduleName/stop", r.stopSchedules)
	group.POST("/clusterName/:clusterName/schedules/:scheduleName/syncStorageServer", r.syncStorageServer)
	group.DELETE("/schedules/:scheduleName/backups/:backupName", r.deleteSchedulesBackups)
	group.DELETE("/schedules/:scheduleName", r.deleteSchedules)

}

// listSchedules
//
//	@Tags		系统运维@数据备份
//	@Summary	获取备份策略列表
//	@Router		/apis/v1/system/backuprestore/schedules [get]
//	@Param		clusterName	query		string	false	"集群名"
//	@Success	200			{object}	utils.Response{data=[]velero.SchedulesItem}
func (r *veleroRouter) listSchedules(ctx *gin.Context) {
	clusterName := ctx.Query("clusterName")

	schedulesList, schedulesListError := r.SchedulerInterface.ListSchedulers(ctx, clusterName)
	if schedulesListError != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, schedulesListError))
	}

	schedulesItemList := make([]velero.SchedulesItem, 0)
	for _, schedule := range schedulesList {
		schedulesItemList = append(schedulesItemList, *schedule.Convert2SchedulesItem())
	}

	sort.Slice(schedulesItemList, func(i, j int) bool {
		return utils.TimeAfter(schedulesItemList[i].CreateTime, schedulesItemList[j].CreateTime)
	})

	utils.Succeed(ctx, schedulesItemList)
}

// getSchedules
//
//	@Tags		系统运维@数据备份
//	@Summary	查询数据备份策略详情
//	@Router		/apis/v1/system/backuprestore/schedules/:scheduleName [get]
//	@Param		scheduleName	path		string	true	"备份策略名称"
//	@Param		clusterName		query		string	true	"集群名"
//	@Success	200				{object}	utils.Response{data=velero.Schedules}
func (r *veleroRouter) getSchedules(ctx *gin.Context) {
	scheduleName := ctx.Param("scheduleName")
	clusterName := ctx.Query("clusterName")

	schedules, schedulesGetError := r.SchedulerInterface.GetSchedulers(ctx, clusterName, scheduleName)
	if schedulesGetError != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, schedulesGetError))
		return
	}

	s, _ := r.GetStorageServers(ctx, schedules.StorageServers.StorageServersId)

	// 隐藏不可展示信息
	s.ClearInformation()

	schedules.StorageServers = *s

	schedules.StorageServersStatus, schedules.StorageServersInfo = r.VeleroStorageServersInterface.GetStorageServerStatus(ctx, clusterName, schedules.StorageServers.StorageServersId)

	utils.Succeed(ctx, schedules)
}

// listSchedulesBackups
//
//	@Tags		系统运维@数据备份
//	@Summary	查询数据备份策略备份记录列表
//	@Router		/apis/v1/system/backuprestore/schedules/:scheduleName/backups [get]
//	@Param		scheduleName	path		string	true	"备份策略名称"
//	@Param		clusterName		query		string	true	"集群名"
//	@Success	200				{object}	utils.Response{data=[]velero.Backups}
func (r *veleroRouter) listSchedulesBackups(ctx *gin.Context) {
	scheduleName := ctx.Param("scheduleName")
	clusterName := ctx.Query("clusterName")
	backupStatus := ctx.Query("backupStatus")

	if backupsList, err := r.BackupInterface.ListBackups(ctx, clusterName, map[string]string{veleroV1.ScheduleNameLabel: scheduleName, "cluster-name": clusterName}, backupStatus); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
	} else {
		sort.Slice(backupsList, func(i, j int) bool {
			return utils.TimeAfter(backupsList[i].CreateTime, backupsList[j].CreateTime)
		})

		utils.Succeed(ctx, backupsList)
	}
}

// listBackupsResources
//
//	@Tags		系统运维@数据备份
//	@Summary	查询数据备份策略备份记录资源列表
//	@Router		/apis/v1/system/backuprestore/schedules/:scheduleName/backups/:backupName/resources [get]
//	@Param		scheduleName	path		string	true	"备份策略名称"
//	@Param		backupName		path		string	true	"备份文件名称"
//	@Param		clusterName		query		string	true	"集群名"
//	@Success	200				{object}	utils.Response{data=[]velero.VeleroResources}
func (r *veleroRouter) listBackupsResources(ctx *gin.Context) {
	scheduleName := ctx.Param("scheduleName")
	backupName := ctx.Param("backupName")
	clusterName := ctx.Query("clusterName")

	if veleroResuorcesList, err := r.BackupInterface.ListBackupsResources(ctx, clusterName, scheduleName, backupName); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
	} else {
		utils.Succeed(ctx, veleroResuorcesList)
	}
}

// listBackupsVolumes
//
//	@Tags		系统运维@数据备份
//	@Summary	查询数据备份策略备份记录资源存储卷
//	@Router		/apis/v1/system/backuprestore/schedules/:scheduleName/backups/:backupName/persistentVolumes [get]
//	@Param		scheduleName	path		string	true	"备份策略名称"
//	@Param		backupName		path		string	true	"备份文件名称"
//	@Param		clusterName		query		string	true	"集群名"
//	@Success	200				{object}	utils.Response{data=[]velero.VeleroVolumes}
func (r *veleroRouter) listBackupsVolumes(ctx *gin.Context) {
	scheduleName := ctx.Param("scheduleName")
	backupName := ctx.Param("backupName")
	clusterName := ctx.Query("clusterName")

	if veleroVolumesList, err := r.PodVolumeBackupInterface.ListBackupVolumes(ctx, clusterName, scheduleName, backupName); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
	} else {
		utils.Succeed(ctx, veleroVolumesList)
	}
}

// logsBackups
//
//	@Tags		系统运维@数据备份
//	@Summary	查询数据备份策略备份记录日志
//	@Router		/apis/v1/system/backuprestore/schedules/:scheduleName/backups/:backupName/logs [get]
//	@Param		scheduleName	path		string	true	"备份策略名称"
//	@Param		backupName		path		string	true	"备份文件名称"
//	@Param		clusterName		query		string	true	"集群名"
//	@Success	200				{object}	utils.Response
func (r *veleroRouter) logsBackups(ctx *gin.Context) {
	scheduleName := ctx.Param("scheduleName")
	backupName := ctx.Param("backupName")
	clusterName := ctx.Query("clusterName")

	if backupsLogs, err := r.BackupInterface.LogsBackups(ctx, clusterName, scheduleName, backupName); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
	} else {
		utils.Succeed(ctx, backupsLogs)
	}
}

// createSchedules
//
//	@Tags		系统运维@数据备份
//	@Summary	新增数据备份策略
//	@Router		/apis/v1/system/backuprestore/schedules [post]
//	@Param		body	body		velero.Schedules	true	"备份策略"
//	@Success	200		{object}	utils.Response
func (r *veleroRouter) createSchedules(ctx *gin.Context) {
	schdules := &velero.Schedules{}
	if err := ctx.ShouldBind(schdules); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}

	if err := r.SchedulerInterface.CreateSchedulers(ctx, schdules.ClusterName, *schdules); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
	} else {
		utils.SucceedWithoutData(ctx)
	}
}

// schedulesNameCheck
//
//	@Tags		系统运维@数据备份
//	@Summary	数据备份策略名称校验(系统级)
//	@Router		/apis/v1/system/backuprestore/schedules/nameCheck [post]
//	@Param		scheduleName	query		string	true	"备份策略名称"
//	@Success	200				{object}	utils.Response
func (r *veleroRouter) schedulesNameCheck(ctx *gin.Context) {
	body := &velero.NameCheck{}
	if err := ctx.ShouldBind(body); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}

	scheduleName := body.Name

	schedulesList, _ := r.SchedulerInterface.ListSchedulers(ctx, "")

	ans := true

	for _, schedules := range schedulesList {
		if schedules.SchedulesName == scheduleName {
			ans = false
			break
		}
	}

	if ans {
		utils.Succeed(ctx, ans)
	} else {
		utils.Failed(ctx, errors.NewFromCode(errors.Var.SchedulesNameRepeat))
	}
}

// updateSchedules
//
//	@Tags		系统运维@数据备份
//	@Summary	编辑数据备份策略
//	@Router		/apis/v1/system/backuprestore/schedules/:scheduleName [put]
//	@Param		scheduleName	path		string				true	"备份策略名称"
//	@Param		body			body		velero.Schedules	true	"备份策略"
//	@Success	200				{object}	utils.Response
func (r *veleroRouter) updateSchedules(ctx *gin.Context) {
	schdules := &velero.Schedules{}
	if err := ctx.ShouldBind(schdules); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}

	if err := r.SchedulerInterface.UpdateSchedulers(ctx, schdules.ClusterName, *schdules); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
	} else {
		utils.SucceedWithoutData(ctx)
	}
}

// executeSchedule
//
//	@Tags		系统运维@数据备份
//	@Summary	立即执行数据备份策略
//	@Router		/apis/v1/system/clusterName/{clusterName}/backuprestore/schedules/:scheduleName/execute [post]
//	@Param		scheduleName	path		string	true	"备份策略名称"
//	@Param		clusterName		query		string	true	"集群名"
//	@Success	200				{object}	utils.Response
func (r *veleroRouter) executeSchedules(ctx *gin.Context) {
	scheduleName := ctx.Param("scheduleName")
	clusterName := ctx.Param("clusterName")

	if err := r.BackupInterface.CreateBackupsBySchedules(ctx, clusterName, scheduleName); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
	} else {
		utils.SucceedWithoutData(ctx)
	}
}

// startSchedule
//
//	@Tags		系统运维@数据备份
//	@Summary	启动数据备份策略
//	@Router		/apis/v1/system/backuprestore/clusterName/{clusterName}/schedules/:scheduleName/start [post]
//	@Param		scheduleName	path		string	true	"备份策略名称"
//	@Param		clusterName		query		string	true	"集群名"
//	@Success	200				{object}	utils.Response
func (r *veleroRouter) startSchedules(ctx *gin.Context) {
	scheduleName := ctx.Param("scheduleName")
	clusterName := ctx.Param("clusterName")

	schedule, getSchedulersErr := r.SchedulerInterface.GetSchedulers(ctx, clusterName, scheduleName)
	if getSchedulersErr != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, getSchedulersErr))
		return
	}

	schedule.Execute = false

	if err := r.SchedulerInterface.UpdateSchedulers(ctx, clusterName, *schedule); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
	} else {
		utils.SucceedWithoutData(ctx)
	}
}

// stopSchedule
//
//	@Tags		系统运维@数据备份
//	@Summary	停止数据备份策略
//	@Router		/apis/v1/system/clusterName/{clusterName}/backuprestore/schedules/:scheduleName/stop [post]
//	@Param		scheduleName	path		string	true	"备份策略名称"
//	@Param		clusterName		query		string	true	"集群名"
//	@Success	200				{object}	utils.Response
func (r *veleroRouter) stopSchedules(ctx *gin.Context) {
	scheduleName := ctx.Param("scheduleName")
	clusterName := ctx.Param("clusterName")

	schedule, getSchedulersErr := r.SchedulerInterface.GetSchedulers(ctx, clusterName, scheduleName)
	if getSchedulersErr != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, getSchedulersErr))
		return
	}

	schedule.Execute = true

	if err := r.SchedulerInterface.UpdateSchedulers(ctx, clusterName, *schedule); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
	} else {
		utils.SucceedWithoutData(ctx)
	}
}

// syncStorageServer
//
//	@Tags		系统运维@数据备份
//	@Summary	同步备份服务器信息
//	@Router		/apis/v1/system/clusterName/{clusterName}/backuprestore/schedules/:scheduleName/syncStorageServer [post]
//	@Param		scheduleName	path		string	true	"备份策略名称"
//	@Param		clusterName		query		string	true	"集群名"
//	@Success	200				{object}	utils.Response
func (r *veleroRouter) syncStorageServer(ctx *gin.Context) {
	scheduleName := ctx.Param("scheduleName")
	clusterName := ctx.Param("clusterName")

	schedules, schedulesGetError := r.SchedulerInterface.GetSchedulers(ctx, clusterName, scheduleName)
	if schedulesGetError != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, schedulesGetError))
		return
	}

	if err := r.VeleroStorageServersInterface.SyncStorageServer(ctx, clusterName, schedules.StorageServers.StorageServersId); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}

	utils.SucceedWithoutData(ctx)
}

// deleteSchedulesBackups
//
//	@Tags		系统运维@数据备份
//	@Summary	删除数据备份策略备份记录
//	@Router		/apis/v1/system/backuprestore/schedules/:scheduleName/backups/:backupName [delete]
//	@Param		scheduleName	path		string	true	"备份策略名称"
//	@Param		backupName		path		string	true	"备份记录名称"
//	@Param		clusterName		query		string	true	"集群名"
//	@Success	200				{object}	utils.Response
func (r *veleroRouter) deleteSchedulesBackups(ctx *gin.Context) {
	backupName := ctx.Param("backupName")
	clusterName := ctx.Query("clusterName")

	if err := r.BackupInterface.DeleteBackups(ctx, clusterName, backupName); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
	} else {
		utils.SucceedWithoutData(ctx)
	}
}

// deleteSchedules
//
//	@Tags		系统运维@数据备份
//	@Summary	删除数据备份策略
//	@Router		/apis/v1/system/backuprestore/schedules/:scheduleName [delete]
//	@Param		scheduleName	path		string	true	"备份策略名称"
//	@Param		clusterName		query		string	true	"集群名"
//	@Success	200				{object}	utils.Response
func (r *veleroRouter) deleteSchedules(ctx *gin.Context) {
	scheduleName := ctx.Param("scheduleName")
	clusterName := ctx.Query("clusterName")

	if err := r.SchedulerInterface.DeleteSchedulers(ctx, clusterName, scheduleName); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
	} else {
		utils.SucceedWithoutData(ctx)
	}
}
