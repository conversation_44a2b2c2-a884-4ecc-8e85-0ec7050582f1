package backup_restore

import (
	"net/http"
	"sort"

	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/midware-go/midwares/audit"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/storageserver"
	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/velero"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

func init() {
	audit.VAR.Register(
		http.MethodPost,
		utils.ApiV1Group+utils.System+"/storageServers",
		"on-configuration",
		"新增备份服务器-{nickname}",
		audit.NewRequestParamCfgRaw("nickname", "$.nickname"),
		audit.NewRequestParamCfgRaw("requestBody", "$"),
	)

	audit.VAR.Register(
		http.MethodPut,
		utils.ApiV1Group+utils.System+"/storageServers"+"/:storageServersId",
		"om-configuration",
		"编辑备份服务器-{nickname}",
		audit.NewRequestParamCfgRaw("nickname", "$.nickname"),
		audit.NewDefaultRequestParamCfgPath("storageServersId"),
		audit.NewRequestParamCfgRaw("requestBody", "$"),
	)

	audit.VAR.Register(
		http.MethodDelete,
		utils.ApiV1Group+utils.System+"/storageServers"+"/:storageServersId",
		"om-configuration",
		"删除备份服务器-{storageServersId}",
		audit.NewDefaultRequestParamCfgPath("storageServersId"),
	)
}

type systemConfigRouter struct {
	storageServersInterface storageserver.StorageServersInterface
}

func NewSystemConfigRouter() routerutil.ApiController {
	return &systemConfigRouter{
		storageServersInterface: storageserver.NewStorageServerInterface(),
	}
}

func (r *systemConfigRouter) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.ApiV1Group + utils.System + "/storageServers")
}

func (r *systemConfigRouter) RegisterRouter(group *gin.RouterGroup) {
	group.GET("", r.ListStorageServers)
	group.POST("", r.CreateStorageServers)
	group.PUT("/:storageServersId", r.UpdateStorageServers)
	group.DELETE("/:storageServersId", r.DeleteStorageServers)
	group.GET("/:storageServersId", r.GetStorageServer)
}

// ListStorageServers 获取存储服务器列表
//
//	@Tags		运维设置@备份服务器
//	@Summary	获取存储服务器列表
//	@Router		/apis/v1/system/storageServers [get]
//	@Success	200	{object}	utils.Response{data=models.StorageServers}
func (r *systemConfigRouter) ListStorageServers(ctx *gin.Context) {
	clusterName := ctx.Query("clusterName")
	backupServerName := ctx.Query("backupServerName")
	strFilter := ctx.Query("queryStr")
	filter := utils.ParseQueryParams[models.StorageServers](ctx)

	// 调用接口获取存储服务器列表
	storageServers, err := r.storageServersInterface.ListStorageServers(ctx, clusterName, backupServerName, strFilter)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	// 过滤 BackupStorageLocationName 为空的存储服务器
	filteredServers := make([]models.StorageServers, 0) // 假设存储服务器的类型是 YourStorageServerType
	for _, server := range storageServers {
		if server.Clusters != nil { // 过滤掉 BackupStorageLocationName 为空的服务器
			filteredServers = append(filteredServers, server)
		}
	}
	// 按创建时间排序，最新的服务器在前
	sort.Slice(filteredServers, func(i, j int) bool {
		return filteredServers[i].CreatedAt.After(filteredServers[j].CreatedAt)
	})
	pageResult, err := filter.FilterResult(filteredServers)
	// 返回过滤和排序后的存储服务器列表
	utils.Succeed(ctx, pageResult)
}

func (r *systemConfigRouter) GetStorageServer(ctx *gin.Context) {
	storageServersId := ctx.Param("storageServersId")
	// 调用接口获取存储服务器列表
	storageServer, err := r.storageServersInterface.GetStorageServers(ctx, storageServersId)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, storageServer)
}

// CreateStorageServers 创建存储服务器
//
//	@Tags		运维设置@备份服务器
//	@Summary	创建存储服务器
//	@Router		/apis/v1/system/storageServers [POST]
//	@Param		queryParam	body		models.StorageServers	true	"参数"
//	@Success	200			{object}	utils.Response
func (r *systemConfigRouter) CreateStorageServers(ctx *gin.Context) {
	storageServers := &models.StorageServers{}
	if err := ctx.ShouldBind(storageServers); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}

	if err := r.storageServersInterface.CreateStorageServers(ctx, *storageServers); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
	} else {
		utils.SucceedWithoutData(ctx)
	}
}

// UpdateStorageServers 更新存储服务器
//
//	@Tags		运维设置@备份服务器
//	@Summary	更新存储服务器
//	@Router		/apis/v1/system/storageServers/:storageServersId [PUT]
//	@Param		storageServersId	query		string					true	"存储服务id"
//	@Param		queryParam			body		models.StorageServers	true	"参数"
//	@Success	200					{object}	utils.Response
func (r *systemConfigRouter) UpdateStorageServers(ctx *gin.Context) {
	storageServers := &models.StorageServers{}
	if err := ctx.ShouldBind(storageServers); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}

	storageServers.StorageServersId = ctx.Param("storageServersId")

	if err := r.storageServersInterface.UpdateStorageServers(ctx, *storageServers); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
	} else {
		utils.SucceedWithoutData(ctx)
	}
}

// DeleteStorageServers 删除存储服务器
//
//	@Tags		运维设置@备份服务器
//	@Summary	删除存储服务器
//	@Router		/apis/v1/system/storageServers/:storageServersId [DELETE]
//	@Param		storageServersId	query		string	true	"存储服务id"
//	@Success	200					{object}	utils.Response
func (r *systemConfigRouter) DeleteStorageServers(ctx *gin.Context) {
	storageServersId := ctx.Param("storageServersId")

	if err := r.storageServersInterface.DeleteStorageServer(ctx, storageServersId); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
	} else {
		utils.SucceedWithoutData(ctx)
	}
}
