package backup_restore

import (
	"net/http"
	"sort"
	"strings"

	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/midware-go/midwares/audit"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/velero"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

func init() {
	audit.VAR.Register(
		http.MethodPost,
		utils.ApiV1Group+utils.System+"/backuprestore"+"/restoresTemplate",
		"backup-recover",
		"新建数据备份恢复策略-{restoreTemplateName}",
		audit.NewRequestParamCfgRaw("restoreTemplateName", "$.restoreTemplateName"),
		audit.NewRequestParamCfgRaw("requestBody", "$"),
	)

	audit.VAR.Register(
		http.MethodPost,
		utils.ApiV1Group+utils.System+"/backuprestore"+"/restoresTemplate/:restoresTemplateId/retry",
		"backup-recover",
		"重试数据备份恢复策略-{restoresTemplateId}",
		audit.NewDefaultRequestParamCfgPath("restoresTemplateId"),
	)

	audit.VAR.Register(
		http.MethodDelete,
		utils.ApiV1Group+utils.System+"/backuprestore"+"/restoresTemplate/:restoresTemplateId",
		"backup-recover",
		"删除数据备份恢复策略-{restoresTemplateId}",
		audit.NewDefaultRequestParamCfgPath("restoresTemplateId"),
	)
}

func (r *veleroRouter) appendVeleroRestoreRouter(group *gin.RouterGroup) {
	group.GET("/restoresTemplate", r.listRestoresTemplates)
	group.GET("/restoresTemplate/:restoresTemplateId", r.getRestoresTemplate)
	group.GET("/restoresTemplate/:restoresTemplateId/restores", r.listRestores)
	group.POST("/restoresTemplate/restore", r.restore)
	group.GET("/restoresTemplate/:restoresTemplateId/restores/:restoresName/resources", r.listRestoresResources)
	group.GET("/restoresTemplate/:restoresTemplateId/restores/:restoresName/persistentVolumes", r.listRestoresPersistentVolumes)
	group.GET("/restoresTemplate/:restoresTemplateId/restores/:restoresName/logs", r.logsRestore)

	group.POST("/restoresTemplate", r.createRestoreTemplate)
	group.POST("/editRestoresTemplate/:restoresTemplateId", r.editRestoreTemplate)
	group.POST("/restoresTemplate/nameCheck", r.restoreTemplateNameCheck)
	group.POST("/restoresTemplate/:restoresTemplateId/retry", r.retryRestoreTemplate)
	group.DELETE("/restoresTemplate/:restoresTemplateId", r.deleteRestoreTemplate)
	group.POST("/restoresTemplate/restoreCheck", r.PreflightRestoreTemplate)
	group.GET("/restoresTemplate/getCheckResult/:restoresTemplateId", r.GetPreflightResult)
	group.GET("/restoresTemplate/listResourceType", r.ListResourceType)
}

// listRestoresTemplates
//
//	@Tags		系统运维@数据恢复
//	@Summary	查询数据恢复列表
//	@Router		/apis/v1/system/backuprestore/restoresTemplate [get]
//	@Param		clusterName	query		string	false	"集群名"
//	@Success	200			{object}	utils.Response{data=[]velero.RestoreTemplate}
func (r *veleroRouter) listRestoresTemplates(ctx *gin.Context) {
	fromCluster := ctx.Query("fromCluster")
	targetCluster := ctx.Query("targetCluster")

	restoreTemplatesList, listRestoreTemplatesErr := r.RestoreTemplateInterface.ListRestoreTemplates(ctx, fromCluster, targetCluster)
	if listRestoreTemplatesErr != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, listRestoreTemplatesErr))
		return
	}

	//restoreTemplateItemList := make([]velero.RestoreTemplateItem, 0)
	//for _, restoreTemplates := range restoreTemplatesList {
	//	restoreTemplateItemList = append(restoreTemplateItemList, *restoreTemplates.Convert2RestoreTemplateItem())
	//}

	sort.Slice(restoreTemplatesList, func(i, j int) bool {
		return utils.TimeAfter(restoreTemplatesList[i].CreateTime, restoreTemplatesList[j].CreateTime)
	})

	utils.Succeed(ctx, restoreTemplatesList)
}

// getRestoresTemplates
//
//	@Tags		系统运维@数据恢复
//	@Summary	查询数据恢复策略详情
//	@Router		/apis/v1/system/backuprestore/restoresTemplate/:restoresTemplateId [get]
//	@Param		restoresTemplateId	query		string	true	"恢复策略id"
//	@Success	200					{object}	utils.Response{data=velero.RestoreTemplate}
func (r *veleroRouter) getRestoresTemplate(ctx *gin.Context) {
	restoresTemplateId := ctx.Param("restoresTemplateId")
	if restoresTemplate, err := r.RestoreTemplateInterface.GetRestoreTemplate(ctx, restoresTemplateId); err != nil {
		utils.Failed(ctx, errors.NewFromCode(errors.Var.RestoreTemplateNotFound))
	} else {
		utils.Succeed(ctx, restoresTemplate)
	}
}

// listRestores
//
//	@Tags		系统运维@数据恢复
//	@Summary	查询数据恢复记录
//	@Router		/apis/v1/system/backuprestore/restoresTemplate/:restoresTemplateId/restores [get]
//	@Param		restoresTemplateId	query		string	true	"恢复策略id"
//	@Success	200					{object}	utils.Response{data=[]velero.Restore}
func (r *veleroRouter) listRestores(ctx *gin.Context) {
	restoresTemplateId := ctx.Param("restoresTemplateId")

	if restoreList, err := r.RestoreTemplateInterface.ListRestore(ctx, "", restoresTemplateId); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
	} else {

		sort.Slice(restoreList, func(i, j int) bool {
			return utils.TimeAfter(restoreList[i].CreateTime, restoreList[j].CreateTime)
		})

		utils.Succeed(ctx, restoreList)
	}
}

// listRestoresResources
//
//	@Tags		系统运维@数据恢复
//	@Summary	查询数据恢复资源列表
//	@Router		/apis/v1/system/backuprestore/restoresTemplate/:restoresTemplateId/restores/:restoresName/resources [get]
//	@Param		restoresTemplateId	query		string	true	"恢复策略id"
//	@Param		restoresName		query		string	true	"恢复记录名称"
//	@Success	200					{object}	utils.Response{data=[]velero.VeleroResources}
func (r *veleroRouter) listRestoresResources(ctx *gin.Context) {
	restoresTemplateId := ctx.Param("restoresTemplateId")
	restoresName := ctx.Param("restoresName")

	restoresTemplate, getRestoreTemplateRrr := r.RestoreTemplateInterface.GetRestoreTemplate(ctx, restoresTemplateId)
	if getRestoreTemplateRrr != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, getRestoreTemplateRrr))
		return
	}

	if restoreResourcesList, err := r.RestoreInterface.ListRestoresResources(ctx, restoresTemplate.TargetCluster, restoresName); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
	} else {
		utils.Succeed(ctx, restoreResourcesList)
	}
}

// listRestoresPersistentVolumes
//
//	@Tags		系统运维@数据恢复
//	@Summary	查询数据恢复存储卷列表
//	@Router		/apis/v1/system/backuprestore/restoresTemplate/:restoresTemplateId/restores/:restoresName/persistentVolumes [get]
//	@Param		restoresTemplateId	query		string	true	"恢复策略id"
//	@Param		restoresName		query		string	true	"恢复记录名称"
//	@Success	200					{object}	utils.Response{data=[]velero.VeleroResources}
func (r *veleroRouter) listRestoresPersistentVolumes(ctx *gin.Context) {
	restoresTemplateId := ctx.Param("restoresTemplateId")
	restoresName := ctx.Param("restoresName")

	restoresTemplate, getRestoreTemplateErr := r.RestoreTemplateInterface.GetRestoreTemplate(ctx, restoresTemplateId)
	if getRestoreTemplateErr != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, getRestoreTemplateErr))
		return
	}

	if veleroVolumesList, err := r.PodVolumeRestoreInterface.ListRestoreVolumes(ctx, restoresTemplate.TargetCluster, restoresTemplateId, restoresName); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
	} else {
		utils.Succeed(ctx, veleroVolumesList)
	}
}

// logsRestore
//
//	@Tags		系统运维@数据恢复
//	@Summary	查询数据恢复记录日志
//	@Router		/apis/v1/system/backuprestore/restoresTemplate/:restoresTemplateId/restores/:restoresName/logs [get]
//	@Param		restoresTemplateId	query		string	true	"恢复策略id"
//	@Param		restoresName		query		string	true	"恢复记录名称"
//	@Success	200					{object}	utils.Response
func (r *veleroRouter) logsRestore(ctx *gin.Context) {
	restoresTemplateId := ctx.Param("restoresTemplateId")
	restoresName := ctx.Param("restoresName")

	restoresTemplate, getRestoreTemplateRrr := r.RestoreTemplateInterface.GetRestoreTemplate(ctx, restoresTemplateId)
	if getRestoreTemplateRrr != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, getRestoreTemplateRrr))
		return
	}

	if restoreLogs, err := r.RestoreInterface.LogsRestore(ctx, restoresTemplate.TargetCluster, restoresName); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
	} else {
		utils.Succeed(ctx, restoreLogs)
	}
}

// createRestoreTemplate
//
//	@Tags		系统运维@数据恢复
//	@Summary	新增数据恢复策略
//	@Router		/apis/v1/system/backuprestore/restoresTemplate [post]
//	@Param		body	body		velero.RestoreTemplate	true	"恢复策略"
//	@Success	200		{object}	utils.Response
func (r *veleroRouter) createRestoreTemplate(ctx *gin.Context) {
	restoreTemplate := &velero.RestoreTemplate{}
	if err := ctx.ShouldBind(restoreTemplate); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}

	restoreTemplateId, createRestoreTemplateError := r.RestoreTemplateInterface.CreateRestoreTemplate(ctx, *restoreTemplate)
	if createRestoreTemplateError != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, createRestoreTemplateError))
		return
	}
	utils.Succeed(ctx, restoreTemplateId)
}

func (r *veleroRouter) editRestoreTemplate(ctx *gin.Context) {
	restoreTemplate := &velero.RestoreTemplate{}
	restoreTemplateId := ctx.Param("restoresTemplateId")
	if err := ctx.ShouldBind(restoreTemplate); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}

	err := r.RestoreTemplateInterface.EditRestoreTemplate(ctx, restoreTemplateId, *restoreTemplate)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, restoreTemplateId)
}

func (r *veleroRouter) restore(ctx *gin.Context) {
	restoreTemplate := &velero.RestoreTemplate{}
	if err := ctx.ShouldBind(restoreTemplate); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	if err := r.RestoreInterface.CreateRestore(ctx, *restoreTemplate); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
	} else {
		utils.SucceedWithoutData(ctx)
	}
}

// restoreTemplateNameCheck
//
//	@Tags		系统运维@数据恢复
//	@Summary	数据恢复策略名称校验
//	@Router		/apis/v1/system/backuprestore/restoresTemplate/nameCheck [post]
//	@Param		restoresTemplateName	query		string	true	"恢复策略名称"
//	@Success	200						{object}	utils.Response
func (r *veleroRouter) restoreTemplateNameCheck(ctx *gin.Context) {
	body := &velero.NameCheck{}
	if err := ctx.ShouldBind(body); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}

	restoresTemplateName := body.Name

	restoreTemplatesList, _ := r.RestoreTemplateInterface.ListRestoreTemplates(ctx, "", "")

	ans := true
	for _, restoreTemplates := range restoreTemplatesList {
		if restoreTemplates.RestoreTemplateName == restoresTemplateName {
			ans = false
			break
		}
	}

	if ans {
		utils.Succeed(ctx, ans)
	} else {
		utils.Failed(ctx, errors.NewFromCode(errors.Var.RestoresTemplateNameRepeat))
	}
}

// retryRestoreTemplate
//
//	@Tags		系统运维@数据恢复
//	@Summary	重试数据恢复记录
//	@Router		/apis/v1/system/backuprestore/restoresTemplate/:restoresTemplateId/retry [post]
//	@Param		restoresTemplateId	query		string	true	"恢复策略id"
//	@Success	200					{object}	utils.Response
func (r *veleroRouter) retryRestoreTemplate(ctx *gin.Context) {
	restoresTemplateId := ctx.Param("restoresTemplateId")

	restoresTemplate, getRestoreTemplateRrr := r.RestoreTemplateInterface.GetRestoreTemplate(ctx, restoresTemplateId)
	if getRestoreTemplateRrr != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, getRestoreTemplateRrr))
		return
	}
	if err := r.RestoreInterface.CreateRestore(ctx, *restoresTemplate); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
	} else {
		utils.SucceedWithoutData(ctx)
	}
}

// deleteRestoreTemplate
//
//	@Tags		系统运维@数据恢复
//	@Summary	删除数据恢复策略
//	@Router		/apis/v1/system/backuprestore/restoresTemplate/:restoresTemplateId [delete]
//	@Param		restoresTemplateId	query		string	true	"恢复策略id"
//	@Success	200					{object}	utils.Response
func (r *veleroRouter) deleteRestoreTemplate(ctx *gin.Context) {
	restoresTemplateId := ctx.Param("restoresTemplateId")

	restoresTemplate, getRestoreTemplateRrr := r.RestoreTemplateInterface.GetRestoreTemplate(ctx, restoresTemplateId)
	if getRestoreTemplateRrr != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, getRestoreTemplateRrr))
		return
	}

	if err := r.DeleteRestore(ctx, restoresTemplate.TargetCluster, restoresTemplateId); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}

	if err := r.DeleteRestoreTemplate(ctx, restoresTemplateId); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
	} else {
		utils.Succeed(ctx, nil)
	}
}

func (r *veleroRouter) PreflightRestoreTemplate(ctx *gin.Context) {
	restoreTemplate := &velero.RestoreTemplate{}
	if err := ctx.ShouldBind(restoreTemplate); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	r.RestoreInterface.PreflightRestoreTemplate(ctx, restoreTemplate)
	utils.SucceedWithoutData(ctx)
}

func (r *veleroRouter) GetPreflightResult(ctx *gin.Context) {
	templateId := ctx.Param("restoresTemplateId")
	res := r.RestoreTemplateInterface.GetPreflightResult(ctx, templateId)
	utils.Succeed(ctx, res)
}

func (r *veleroRouter) ListResourceType(ctx *gin.Context) {
	backupName := ctx.Query("backupName")
	namespace := ctx.Query("namespace")
	cluster := ctx.Query("cluster")
	namespaces := strings.Split(namespace, ",")

	ans, err := r.RestoreTemplateInterface.ListResourceType(ctx, cluster, backupName, namespaces)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, ans)
}
