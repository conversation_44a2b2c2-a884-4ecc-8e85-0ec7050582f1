package example

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	examplehandler "harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/example"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

func NewPodExampleRouter() routerutil.ApiController {
	return &podExampleRouter{
		handler: examplehandler.NewPodPodExampleHandler(),
	}
}

type podExampleRouter struct {
	handler examplehandler.PodExampleIntf
}

func (r *podExampleRouter) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.ApiV1Group + "/example")
}
func (r *podExampleRouter) RegisterRouter(group *gin.RouterGroup) {
	//group.GET("/clusters/:cluster_id/namespaces/:namespace_name/pods", r.ListNamespacesPods)
}

func (r *podExampleRouter) ListNamespacesPods(ctx *gin.Context) {
	clusterId := ctx.Param("cluster_id")
	namespaceName := ctx.Param("namespace_name")
	pods, err := r.handler.ListNamespacePods(ctx, clusterId, namespaceName)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, pods)
}
