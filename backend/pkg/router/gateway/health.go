package gateway

import (
	"github.com/gin-gonic/gin"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

func NewGatewayHealthRouter() routerutil.ApiController {
	return gatewayHealthRouter{}
}

type gatewayHealthRouter struct {
}

func (r gatewayHealthRouter) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group("")
}

func (r gatewayHealthRouter) RegisterRouter(group *gin.RouterGroup) {
	group.GET("/checkpreload", Context)
}

func Context(ctx *gin.Context) {
	ctx.String(200, "success")
}
