package console

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/console"
	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/console"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

func NewRouter() routerutil.ApiController {
	// 确保 models 包被使用，避免 linter 删除导入
	_ = models.ConsoleSessionRequest{}

	return &consoleRouter{
		handler: console.NewConsoleHandler(console.NewConsoleService(nil)),
	}
}

type consoleRouter struct {
	handler *console.ConsoleHandler
}

func (r *consoleRouter) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.ApiV1Group + "/nodeterminals/clusters/:cluster/console")
}

func (r *consoleRouter) RegisterRouter(group *gin.RouterGroup) {
	group.POST("/sessions", r.CreateConsoleSession)
	group.GET("/sessions/:podName", r.GetConsoleSession)
	group.DELETE("/sessions/:podName", r.DeleteConsoleSession)
	group.GET("/sessions/:podName/websocket", r.HandleWebSocket)
	group.GET("/sessions/:podName/sshwebsocket", r.HandleSSHWebSocket)
}

// CreateConsoleSession 创建控制台会话
//
//	@Tags			节点控制台
//	@Summary		创建节点控制台会话
//	@Description	在指定集群的节点上创建调试控制台会话
//	@Param			cluster	path	string							true	"集群名称"
//	@Param			request	body	models.ConsoleSessionRequest	true	"会话请求参数"
//	@Router			/apis/v1/nodeterminals/clusters/{cluster}/console/sessions [post]
//	@Success		200	{object}	utils.Response{data=models.ConsoleSessionResponse}	"成功"
func (r *consoleRouter) CreateConsoleSession(c *gin.Context) {
	r.handler.CreateConsoleSession(c)
}

// GetConsoleSession 获取控制台会话状态
//
//	@Tags			节点控制台
//	@Summary		获取控制台会话状态
//	@Description	获取指定集群中控制台会话的状态信息
//	@Param			cluster	path	string	true	"集群名称"
//	@Param			podName	path	string	true	"Pod名称"
//	@Router			/apis/v1/nodeterminals/clusters/{cluster}/console/sessions/{podName} [get]
//	@Success		200	{object}	utils.Response{data=models.SessionStatusResponse}
func (r *consoleRouter) GetConsoleSession(c *gin.Context) {
	r.handler.GetConsoleSession(c)
}

// DeleteConsoleSession 删除控制台会话
//
//	@Tags			节点控制台
//	@Summary		删除控制台会话
//	@Description	删除指定集群中的控制台会话
//	@Param			cluster	path	string	true	"集群名称"
//	@Param			podName	path	string	true	"Pod名称"
//	@Router			/apis/v1/nodeterminals/clusters/{cluster}/console/sessions/{podName} [delete]
//	@Success		200	{object}	utils.Response{data=map[string]interface{}}
func (r *consoleRouter) DeleteConsoleSession(c *gin.Context) {
	r.handler.DeleteConsoleSession(c)
}

// HandleWebSocket 处理WebSocket连接
//
//	@Tags			节点控制台
//	@Summary		建立WebSocket连接
//	@Description	建立与节点控制台的WebSocket连接
//	@Param			cluster	path	string	true	"集群名称"
//	@Param			podName	path	string	true	"Pod名称"
//	@Router			/apis/v1/nodeterminals/clusters/{cluster}/console/sessions/{podName}/websocket [get]
//	@Success		101	{string}	string	"Switching Protocols"
func (r *consoleRouter) HandleWebSocket(c *gin.Context) {
	r.handler.HandleWebSocket(c)
}

// HandleAttachWebSocket 处理kubectl attach方式的WebSocket连接
//
//	@Tags			节点控制台
//	@Summary		附着到Pod主进程的WebSocket连接
//	@Description	通过WebSocket与Pod主进程直接交互（kubectl attach效果）
//	@Param			cluster	path	string	true	"集群名称"
//	@Param			podName	path	string	true	"Pod名称"
//	@Router			/apis/v1/nodeterminals/clusters/{cluster}/console/sessions/{podName}/attach [get]
//	@Success		101	{string}	string	"Switching Protocols"
func (r *consoleRouter) HandleSSHWebSocket(c *gin.Context) {
	r.handler.HandleSSHWebSocket(c)
}
