package internalopenapi

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"

	permissionv1 "harmonycloud.cn/unifiedportal/api-definition/permission/v1"
)

func (r *internalOpenApiRouter) RegisterPermission(group *gin.RouterGroup) {
	group.POST("/menu", r.Menu)
}

func (r *internalOpenApiRouter) Menu(context *gin.Context) {
	steps := permissionv1.MenuSteps{}
	if err := context.ShouldBind(&steps); err != nil {
		utils.Failed(context, errors.NewFromError(context, err))
		return
	}
	if err := r.handler.HandlePermission(context, steps); err != nil {
		utils.Failed(context, errors.NewFromError(context, err))
		return
	}
	steps.Validator()
	utils.Succeed(context, steps)
}
