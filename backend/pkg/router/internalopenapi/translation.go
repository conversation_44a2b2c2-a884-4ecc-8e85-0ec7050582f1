package internalopenapi

import (
	"strings"

	"github.com/gin-gonic/gin"
	translationv1 "harmonycloud.cn/unifiedportal/api-definition/translation/v1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

func (r *internalOpenApiRouter) RegisterTranslation(group *gin.RouterGroup) {
	group.POST("/config", r.Translation)
}

func (r *internalOpenApiRouter) Translation(context *gin.Context) {
	// bind request
	cloudServiceName := strings.TrimSpace(context.Query("cloudServiceName"))
	if strings.EqualFold("", cloudServiceName) {
		utils.Failed(context, errors.NewFromError(context, errors.NewFromCodeWithMessage(errors.Var.ParamError, "cloudServiceName cloud not be blank")))
		return
	}

	translateConfigSteps := translationv1.TranslateConfigSteps{}
	if err := context.ShouldBind(&translateConfigSteps); err != nil {
		utils.Failed(context, errors.NewFromError(context, err))
		return
	}
	preloadTranslateConfigSteps(translateConfigSteps, cloudServiceName)

	if err := r.handler.HandleTranslation(context, translateConfigSteps); err != nil {
		utils.Failed(context, errors.NewFromError(context, err))
		return
	}
	utils.Succeed(context, translateConfigSteps)
}

func preloadTranslateConfigSteps(translateConfigSteps translationv1.TranslateConfigSteps, cloudServiceName string) {
	if len(translateConfigSteps) == 0 {
		return
	}

	for tcIndex, _ := range translateConfigSteps {
		tc := &translateConfigSteps[tcIndex]
		if len(tc.Configs) == 0 {
			continue
		}
		for cIndex, _ := range tc.Configs {
			c := &tc.Configs[cIndex]
			if len(c.RegexTranslates) != 0 {
				for regexIndex, _ := range c.RegexTranslates {
					regex := &c.RegexTranslates[regexIndex]
					regex.GroupName = strings.Join([]string{cloudServiceName, regex.GroupName}, "/")
				}
			}
			//if len(c.ResourceTranslates) != 0 {
			//	for resourceIndex, _ := range c.ResourceTranslates {
			//		resource := c.ResourceTranslates[resourceIndex]
			//		resource.GroupName = strings.Join([]string{cloudServiceName, resource.GroupName}, "/")
			//	}
			//}
		}
	}
}
