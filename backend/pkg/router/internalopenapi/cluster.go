package internalopenapi

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

func (r *internalOpenApiRouter) RegisterCluster(group *gin.RouterGroup) {
	group.GET(":clusterName", r.DescribeCluster)
	group.GET("", r.ListCluster)
}

func (r *internalOpenApiRouter) DescribeCluster(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	if clusterName == "" {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "param cluster is nil"))
		return
	}

	cluster, err := r.handler.DescribeCluster(ctx, clusterName)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, cluster)
}

func (r *internalOpenApiRouter) ListCluster(ctx *gin.Context) {
	result, err := r.handler.ListCluster(ctx)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, result)
}
