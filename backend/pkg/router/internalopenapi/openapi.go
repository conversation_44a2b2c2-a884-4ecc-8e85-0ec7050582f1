package internalopenapi

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/internalopenapi"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

func NewInternalOpenApiRouter() routerutil.ApiController {
	return &internalOpenApiRouter{
		handler: internalopenapi.NewInternalOpenApiHandler(),
	}
}

type internalOpenApiRouter struct {
	handler internalopenapi.HandlerIntf
}

func (*internalOpenApiRouter) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.InternalOpenApiGroup)
}
func (r *internalOpenApiRouter) RegisterRouter(group *gin.RouterGroup) {
	permissionGroup := group.Group("/permissions")
	r.RegisterPermission(permissionGroup)

	clusterGroup := group.Group("/clusters")
	r.RegisterCluster(clusterGroup)

	translationGroup := group.Group("/translation")
	r.RegisterTranslation(translationGroup)

	//group.GET("/workspace/resources", r.workspaceResources)
}

//func (r *internalOpenApiRouter) workspaceResources(ctx *gin.Context) {
//	cli := client.GetClient().GetHubCtrlClient()
//	_, _, _ = ctx.Query("userId"), ctx.Query("projectId"), ctx.Query("organId")
//	var resources []workspace.Resource
//	csList := cloudservicev1alpha1.CloudServiceList{}
//	if err := cli.List(ctx, &csList); err == nil {
//		resources = append(resources, workspace.Resource{
//			ResourceName: "云服务",
//			ResourceId:   "cloudService",
//			Unit:         "个",
//			Count:        len(csList.Items),
//			Icon:         workspace.Icon{},
//		})
//	}
//	ccList := cloudservicev1alpha1.CloudComponentList{}
//	if err := cli.List(ctx, &ccList); err == nil {
//		resources = append(resources, workspace.Resource{
//			ResourceName: "云组件",
//			ResourceId:   "cloudComponent",
//			Unit:         "个",
//			Count:        len(ccList.Items),
//			Icon:         workspace.Icon{},
//		})
//	}
//	ctx.JSON(200, resources)
//}
