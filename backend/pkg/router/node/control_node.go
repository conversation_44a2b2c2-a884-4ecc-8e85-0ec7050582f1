package node

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/node"
	nodemodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

var _ = nodemodel.SimpleNodeResponseList{}

func NewControlNodeRouter() routerutil.ApiController {
	return &controlNodeRouter{
		intf: node.NewControlNodeHandler(),
	}
}

type controlNodeRouter struct {
	intf node.ControlNodeHandler
}

func (r *controlNodeRouter) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.ApiV1Group + "/clusters/:clusterName/controlNodes")
}

func (r *controlNodeRouter) RegisterRouter(group *gin.RouterGroup) {
	group.GET("", r.nodes) // done
}

// nodes 获取集群主控节点
//
//	@Tags			集群主控节点相关
//	@Summary		获取集群主控节点信息
//	@Description	获取集群主控节点信息
//	@Param			clusterName	path	string	true	"集群名称"
//	@Param			excludes	query	string	false	"过滤节点IP列表"	default()
//	@Router			/apis/v1/clusters/{clusterName}/controlNodes [get]
//	@Success		200	{object}	utils.Response{data=nodemodel.SimpleNodeResponseList}
func (r *controlNodeRouter) nodes(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	excludes := ctx.Query("excludes")
	res, err := r.intf.Nodes(ctx, clusterName, node.NewSimpleNodeExcludeIpFilter(excludes))
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, res)
}
