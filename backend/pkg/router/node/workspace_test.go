package node

import (
	"fmt"
	"testing"

	v1 "k8s.io/api/apps/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func processData(deploy *v1.Deployment) error {
	fmt.Printf("addr=%p\n", deploy)
	// xxx
	deploy.Name = ""
	return nil
}

func processDataWithVal(deploy v1.Deployment) (v1.Deployment, error) {
	fmt.Printf("addr=%p\n", &deploy)
	deploy.Name = ""
	return deploy, nil
}

func Test_a(t *testing.T) {

	deployment := &v1.Deployment{
		TypeMeta: metav1.TypeMeta{},
		ObjectMeta: metav1.ObjectMeta{
			Name: "a",
		},
		Spec:   v1.DeploymentSpec{},
		Status: v1.DeploymentStatus{},
	}
	//deepCopy := deployment.DeepCopy()
	fmt.Println("name=" + deployment.Name)

	processData(deployment)
	fmt.Println(fmt.Sprintf("addr=%p", deployment), "name="+deployment.Name)

	val, _ := processDataWithVal(*deployment)
	fmt.Println(fmt.Sprintf("addr=%p", deployment), "name="+deployment.Name)
	fmt.Println(fmt.Sprintf("addr=%p", &val), "name="+val.Name)
}
