package node

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/node"
	nodemodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

func NewNodeVerifyRouter() routerutil.ApiController {
	return &nodeVerifyRouter{
		handler: node.NewNodeVerifyHandler(),
	}
}

type nodeVerifyRouter struct {
	handler node.NodeVerifyHandler
}

func (r *nodeVerifyRouter) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.ApiV1Group + "/node_verify")
}
func (r *nodeVerifyRouter) RegisterRouter(group *gin.RouterGroup) {
	group.POST("", r.verify) // done
}

// verify 节点校验
//
//	@Tags			节点校验相关
//	@Summary		校验节点联通性
//	@Description	proxy接口，校验节点联通性
//	@Param			clusterName	query	string							true	"使用那个集群的西西弗斯，非必填"
//	@Param			requestBody	body	[]nodemodel.NodeVeriryRequest	true	"需要校验的节点"
//	@Router			/apis/v1/node_verify [post]
//	@Success		200	{object}	utils.Response{data=nodemodel.NodeVerifyResponse}
func (r *nodeVerifyRouter) verify(ctx *gin.Context) {
	var request []nodemodel.NodeVeriryRequest
	if err := ctx.ShouldBind(&request); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}

	res, err := r.handler.Verify(ctx, ctx.Query("clusterName"), request)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, res)
}
