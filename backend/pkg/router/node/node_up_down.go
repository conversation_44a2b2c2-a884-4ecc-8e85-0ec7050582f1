package node

import (
	"strings"

	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/node"
	nodemodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/node"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

func NewNodeUpDownRouter() routerutil.ApiController {
	return &nodeUpDownRouter{
		intf: node.NewNodeUpDownHandler(),
	}
}

type nodeUpDownRouter struct {
	intf node.NodeUpDownHandler
}

func (r *nodeUpDownRouter) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.ApiV1Group + "/clusters/:clusterName/nodeUpDown")
}

func (r *nodeUpDownRouter) RegisterRouter(group *gin.RouterGroup) {
	group.POST("", r.create)            // done
	group.POST("/batch", r.batchCreate) // done
	group.POST("/verify", r.verify)     // done
	group.GET("", r.list)               // done

	group.GET("/count", r.count)             // done
	group.GET("/:node_ip", r.createResponse) // done
	group.PUT("/:node_ip", r.edit)           // done

	group.GET("/:node_ip/status", r.status) // done
	group.GET("/:node_ip/logs", r.logs)     // done
	group.POST("/:node_ip/retry", r.retry)  // done
	group.DELETE("/:node_ip", r.delete)     // done
}

// create 创建节点上下线任务
//
//	@Tags			节点上下线相关
//	@Summary		创建节点上下线任务
//	@Description	创建节点上下线任务
//	@Param			clusterName	path	string								true	"集群名称"
//	@Param			requestBody	body	nodemodel.NodeUpDownCreateRequest	true	"requestBody"
//	@Router			/apis/v1/clusters/{clusterName}/nodeUpDown [post]
//	@Success		200	{object}	utils.Response{}
func (r *nodeUpDownRouter) create(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	var request nodemodel.NodeUpDownCreateRequest
	if err := ctx.ShouldBind(&request); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	err := r.intf.Create(ctx, clusterName, request)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, nil)
}

// count 获取节点上下线任务数量
//
//	@Tags			节点上下线相关
//	@Summary		获取节点上下线任务数量
//	@Description	获取节点上下线任务数量，包括总计数量与数量详情
//	@Param			clusterName	path	string	true	"集群名称"
//	@Param			statuses	query	string	false	"任务状态列表，使用','隔开"							Enums(createInitial,preflighting,installing,createInitialFailed,preflightFailed,installFailed)	default()
//	@Param			types		query	string	false	"任务类型列表，使用','隔开"" Enums(nodeUp,nodeDown)																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																																									default()
//	@Router			/apis/v1/clusters/{clusterName}/nodeUpDown/count [get]
//	@Success		200	{object}	utils.Response{data=nodemodel.NodeUpDownCountResponse}
func (r *nodeUpDownRouter) count(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	statues := ctx.Query("statuses")
	types := ctx.Query("types")

	res, err := r.intf.Count(ctx, clusterName, node.NewNodeUpDownResponseFilterWithStatuses(statues), node.NewNodeUpDownResponseFilterWithTypes(types))
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, res)
}

// verify 创建节点上下线任务 - 节点校验
//
//	@Tags			节点上下线相关
//	@Summary		创建节点上下线任务 - 节点校验
//	@Description	创建节点上下线任务 - 节点校验
//	@Param			clusterName	path	string								true	"集群名称"
//	@Param			requestBody	body	nodemodel.NodeUpDownCreateRequest	true	"requestBody"
//	@Router			/apis/v1/clusters/{clusterName}/nodeUpDown/verify [post]
//	@Success		200	{object}	utils.Response{data=nodemodel.NodeVerifyResponse}
func (r *nodeUpDownRouter) verify(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	var request nodemodel.NodeUpDownCreateRequest
	if err := ctx.ShouldBind(&request); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	resp, err := r.intf.Verify(ctx, clusterName, request)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, resp)
}

// batchCreate 批量创建节点上下线任务
//
//	@Tags			节点上下线相关
//	@Summary		批量创建节点上下线任务
//	@Description	批量创建节点上下线任务
//	@Param			clusterName	path	string									true	"集群名称"
//	@Param			requestBody	body	nodemodel.NodeUpDownBatchCreateRequest	true	"requestBody"
//	@Router			/apis/v1/clusters/{clusterName}/nodeUpDown/batch [post]
//	@Success		200	{object}	utils.Response{}
func (r *nodeUpDownRouter) batchCreate(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	var request nodemodel.NodeUpDownBatchCreateRequest
	if err := ctx.ShouldBind(&request); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	err := r.intf.BatchCreate(ctx, clusterName, request)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, nil)
}

// list 获取节点上下线任务列表
//
//	@Tags			节点上下线相关
//	@Summary		获取节点上下线任务列表
//	@Description	获取节点上下线任务列表
//	@Param			clusterName	path	string	true	"集群名称"
//	@Router			/apis/v1/clusters/{clusterName}/nodeUpDown [get]
//	@Success		200	{object}	utils.Response{data=nodemodel.NodeUpDownResponseList}
func (r *nodeUpDownRouter) list(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	resp, err := r.intf.List(ctx, clusterName)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, resp)
}

// status 获取节点上下线任务执行详情
//
//	@Tags			节点上下线相关
//	@Summary		获取节点上下线任务执行详情
//	@Description	获取节点上下线任务执行详情
//	@Param			clusterName	path	string	true	"集群名称"
//	@Param			nodeIp		path	string	true	"节点IP"
//	@Router			/apis/v1/clusters/{clusterName}/nodeUpDown/{nodeIp}/status [get]
//	@Success		200	{object}	utils.Response{data=nodemodel.NodeUpDownStatusResponse}
func (r *nodeUpDownRouter) status(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	nodeIP := ctx.Param("node_ip")
	resp, err := r.intf.Status(ctx, clusterName, nodeIP)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, resp)
}

// status 获取节点上下线日志
//
//	@Tags			节点上下线相关
//	@Summary		获取节点上下线日志
//	@Description	获取节点上下线日志
//	@Param			clusterName	path	string	true	"集群名称"
//	@Param			nodeIp		path	string	true	"节点IP"
//	@Param			stepName	path	string	false	"步骤名称"
//	@Param			follow		query	string	false	"是否持续输出"
//	@Router			/apis/v1/clusters/{clusterName}/nodeUpDown/{nodeIp}/logs [get]
//	@Success		200	{object}	utils.Response{data=bool}
func (r *nodeUpDownRouter) logs(ctx *gin.Context) {
	// 读取参数
	request := nodemodel.UpDownLogQueryRequest{}
	if err := ctx.ShouldBindUri(&request); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	// 设置是否为流式输出 如果为流式输出 则使用webSocket
	var follow bool
	followStr := ctx.Query("follow")
	if strings.ToLower(followStr) == "true" {
		follow = true
	}
	// 构建全局context
	// 获取日志
	logBytesChan, logContext, logCancelFunc, err := r.intf.UpDownLog(ctx, request)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	defer logCancelFunc()
	utils.WebSocketProxy(ctx, follow, logBytesChan, logContext, logCancelFunc)
}

// createResponse 节点上下线表单回显
//
//	@Tags			节点上下线相关
//	@Summary		节点上下线表单回显
//	@Description	节点上下线表单回显
//	@Param			clusterName	path	string	true	"集群名称"
//	@Param			nodeIp		path	string	true	"节点IP"
//	@Router			/apis/v1/clusters/{clusterName}/nodeUpDown/{nodeIp} [get]
//	@Success		200	{object}	utils.Response{data=nodemodel.NodeUpDownCreateResponse}
func (r *nodeUpDownRouter) createResponse(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	nodeIP := ctx.Param("node_ip")
	resp, err := r.intf.CreateResponse(ctx, clusterName, nodeIP)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, resp)
}

// edit 编辑节点上下线信息
//
//	@Tags			节点上下线相关
//	@Summary		编辑节点上下线信息
//	@Description	编辑节点上下线信息
//	@Param			clusterName	path	string								true	"集群名称"
//	@Param			nodeIp		path	string								true	"节点IP"
//	@Param			requestBody	body	nodemodel.NodeUpDownCreateRequest	true	"requestBody"
//	@Router			/apis/v1/clusters/{clusterName}/nodeUpDown/{nodeIp} [put]
//	@Success		200	{object}	utils.Response{}
func (r *nodeUpDownRouter) edit(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	nodeIP := ctx.Param("node_ip")
	var request nodemodel.NodeUpDownCreateRequest
	if err := ctx.ShouldBind(&request); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	err := r.intf.Edit(ctx, clusterName, nodeIP, request)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, nil)
}

// retry 失败重试
//
//	@Tags			节点上下线相关
//	@Summary		失败重试
//	@Description	失败重试
//	@Param			clusterName	path	string	true	"集群名称"
//	@Param			nodeIp		path	string	true	"节点IP"
//	@Router			/apis/v1/clusters/{clusterName}/nodeUpDown/{nodeIp}/retry [post]
//	@Success		200	{object}	utils.Response{}
func (r *nodeUpDownRouter) retry(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	nodeIP := ctx.Param("node_ip")
	err := r.intf.Retry(ctx, clusterName, nodeIP)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, nil)
}

// delete 删除
//
//	@Tags			节点上下线相关
//	@Summary		删除
//	@Description	删除
//	@Param			clusterName	path	string	true	"集群名称"
//	@Param			nodeIp		path	string	true	"节点IP"
//	@Router			/apis/v1/clusters/{clusterName}/nodeUpDown/{nodeIp} [delete]
//	@Success		200	{object}	utils.Response{}
func (r *nodeUpDownRouter) delete(ctx *gin.Context) {
	clusterName := ctx.Param("clusterName")
	nodeIP := ctx.Param("node_ip")
	err := r.intf.Delete(ctx, clusterName, nodeIP)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, nil)
}
