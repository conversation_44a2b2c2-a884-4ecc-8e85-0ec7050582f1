package config

import (
	"strings"

	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/config"
	configmodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/config"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

var _ = configmodel.ListResponse{}

func NewRouter() routerutil.ApiController {
	return &createRouter{
		handler: config.NewHandler(),
	}
}

// 集群Router
type createRouter struct {
	handler config.Handler
}

func (r *createRouter) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.ApiV1Group + "/sys-configs")
}

func (r *createRouter) RegisterRouter(group *gin.RouterGroup) {
	// 集群创建配置相关
	group.GET("/:group_name", r.listConfigs)
	group.GET("/:group_name/types/:type_name", r.getConfigByType)
	group.GET("/:group_name/info", r.listSugarConfigs)
	group.GET("/:group_name/types/:type_name/info", r.getSugarConfigByType)
}

// listConfigs 获取某一配置组下的全部配置信息
//
//	@Tags			配置相关
//	@Summary		获取某一配置组下的全部配置信息
//	@Description	获取某一配置组下的全部配置信息
//	@Param			group_name	path	string	true	"配置组名称"	Enums(create-cluster,sisyphus-config,system-info,cluster-info)	default(create-cluster)
//	@Router			/apis/v1/sys-configs/{group_name} [get]
//	@Success		200	{object}	utils.Response{data=configmodel.ListResponse}
func (r *createRouter) listConfigs(ctx *gin.Context) {
	groupName := ctx.Param("group_name")
	if strings.EqualFold("", strings.TrimSpace(groupName)) {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "group_name cloud not be empty"))
		return
	}

	for queryKey, queryValue := range ctx.Request.URL.Query() {
		ctx.Set(queryKey, queryValue)
	}

	response, err := r.handler.ListConfig(ctx, groupName)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, response)
}

// getConfigByType 获取某一配置组下的特定配置信息
//
//	@Tags			配置相关
//	@Summary		获取某一配置组下的特定配置信息
//	@Description	获取某一配置组下的特定配置信息
//	@Param			group_name	path	string	true	"配置组名称"	Enums(create-cluster,sisyphus-config,system-info,cluster-info)																										default(create-cluster)
//	@Param			type_name	path	string	true	"配置项名称"	Enums(kubernetes_versions,kubernetes_CRIS,hub_cluster_ingress_address,hub_cluster_stellaries_component,default_calico_cidr,support,default_registry,chrony-info)	default(kubernetes_versions)
//	@Router			/apis/v1/sys-configs/{group_name}/types/{type_name} [get]
//	@Success		200	{object}	utils.Response{data=configmodel.Response}
func (r *createRouter) getConfigByType(ctx *gin.Context) {
	groupName := ctx.Param("group_name")
	if strings.EqualFold("", strings.TrimSpace(groupName)) {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "group_name cloud not be empty"))
		return
	}

	typeName := ctx.Param("type_name")
	if strings.EqualFold("", strings.TrimSpace(typeName)) {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "typeName cloud not be empty"))
		return
	}

	for queryKey, queryValue := range ctx.Request.URL.Query() {
		ctx.Set(queryKey, queryValue)
	}

	response, err := r.handler.GetConfigByType(ctx, groupName, typeName)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, response)
}

// listSugarConfigs 获取某一配置组下的全部配置信息(糖方式)(推荐前端使用该接口)
//
//	@Tags			配置相关
//	@Summary		获取某一配置组下的全部配置信息(糖方式)(推荐前端使用该接口)
//	@Description	获取某一配置组下的全部配置信息(糖方式)(推荐前端使用该接口)
//	@Param			group_name	path	string	true	"配置组名称"	Enums(create-cluster,sisyphus-config,system-info,cluster-info)	default(create-cluster)
//	@Router			/apis/v1/sys-configs/{group_name}/info [get]
//	@Success		200	{object}	utils.Response{data=configmodel.ListSugarResponse}
func (r *createRouter) listSugarConfigs(ctx *gin.Context) {
	groupName := ctx.Param("group_name")
	if strings.EqualFold("", strings.TrimSpace(groupName)) {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "group_name cloud not be empty"))
		return
	}
	for queryKey, queryValue := range ctx.Request.URL.Query() {
		ctx.Set(queryKey, queryValue)
	}
	response, err := r.handler.ListSugarConfig(ctx, groupName)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, response)
}

// getSugarConfigByType 获取某一配置组下的特定配置信息
//
//	@Tags			配置相关
//	@Summary		获取某一配置组下的特定配置信息(糖方式)(推荐前端使用该接口)
//	@Description	获取某一配置组下的特定配置信息(糖方式)(推荐前端使用该接口)
//	@Param			group_name	path	string	true	"配置组名称"	Enums(create-cluster,sisyphus-config,system-info,cluster-info)																										default(create-cluster)
//	@Param			type_name	path	string	true	"配置项名称"	Enums(kubernetes_versions,kubernetes_CRIS,hub_cluster_ingress_address,hub_cluster_stellaries_component,default_calico_cidr,support,default_registry,chrony-info)	default(kubernetes_versions)
//	@Router			/apis/v1/sys-configs/{group_name}/types/{type_name}/info [get]
//	@Success		200	{object}	utils.Response{data=any}
func (r *createRouter) getSugarConfigByType(ctx *gin.Context) {
	groupName := ctx.Param("group_name")
	if strings.EqualFold("", strings.TrimSpace(groupName)) {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "group_name cloud not be empty"))
		return
	}

	typeName := ctx.Param("type_name")
	if strings.EqualFold("", strings.TrimSpace(typeName)) {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "typeName cloud not be empty"))
		return
	}
	for queryKey, queryValue := range ctx.Request.URL.Query() {
		ctx.Set(queryKey, queryValue)
	}

	response, err := r.handler.GetSugarConfigByType(ctx, groupName, typeName)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}

	utils.Succeed(ctx, response.Sugar)
}
