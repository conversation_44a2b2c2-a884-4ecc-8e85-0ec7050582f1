package workload

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/project/workload"
	workloadmodels "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/workload"
	commonRouter "harmonycloud.cn/unifiedportal/portal/backend/pkg/router/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
	appsv1 "k8s.io/api/apps/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/labels"
)

func NewWorkloadController() routerutil.ApiController {
	return &routeWorkloadController{
		DeploymentHandler:  workload.NewDeploymentHandler(),
		StatefulSetHandler: workload.NewStatefulSetHandler(),
	}
}

type routeWorkloadController struct {
	DeploymentHandler  workload.DeploymentHandler
	StatefulSetHandler workload.StatefulSetHandler
}

func (rwc *routeWorkloadController) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group("/workloads")
}

func (rwc *routeWorkloadController) RegisterRouter(routes *gin.RouterGroup) {

	// read

	// get
	routes.GET("/clusters/:cluster/apis/apps/v1/namespaces/:namespace/:workloadtype/:workloadName", rwc.getWorkload)
	// list
	routes.GET("/clusters/:cluster/apis/apps/v1/namespaces/:namespace/:workloadtype", rwc.listWorkloads)

	// write

	// create
	routes.POST("/clusters/:cluster/apis/apps/v1/namespaces/:namespace/:workloadtype", rwc.createWorkload)
	// update
	routes.PUT("/clusters/:cluster/apis/apps/v1/namespaces/:namespace/:workloadtype/:workloadName")
	// patch
	routes.PATCH("/clusters/:cluster/apis/apps/v1/namespaces/:namespace/:workloadtype/:workloadName")
	// delete
	routes.DELETE("/clusters/:cluster/apis/apps/v1/namespaces/:namespace/:workloadtype/:workloadName")
}

func (rwc *routeWorkloadController) getWorkload(c *gin.Context) {
	p := &common.ReadParam{}
	p.Cluster = c.Param("cluster")
	p.Namespace = c.Param("namespace")
	p.ResourceType = c.Param("workloadtype")
	p.Name = c.Param("workloadName")
	p.Filter = utils.ParseQueryParams[unstructured.Unstructured](c)
	switch p.ResourceType {
	case workloadmodels.ResourceTypeDeployments:
		response, err := rwc.DeploymentHandler.Get(c, &workloadmodels.GetDeploymentRequest{ReadParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeStatefulSets:
		response, err := rwc.StatefulSetHandler.Get(c, &workloadmodels.GetStatefulSetRequest{ReadParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	}
}

func (rwc *routeWorkloadController) listWorkloads(c *gin.Context) {
	p := &common.ReadParam{}
	p.Cluster = c.Param("cluster")
	p.Namespace = c.Param("namespace")
	p.ResourceType = c.Param("workloadtype")
	p.Name = c.Param("workloadName")
	parse, err := labels.Parse(c.Param("labelSelector"))
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	p.LabelSelector = parse
	p.Filter = utils.ParseQueryParams[unstructured.Unstructured](c)
	switch p.ResourceType {
	case workloadmodels.ResourceTypeDeployments:
		response, err := rwc.DeploymentHandler.List(c, &workloadmodels.ListDeploymentRequest{ReadParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeStatefulSets:
		response, err := rwc.StatefulSetHandler.List(c, &workloadmodels.ListStatefulSetRequest{ReadParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
		}
		utils.Succeed(c, response)
	}

}

func (rwc *routeWorkloadController) createWorkload(c *gin.Context) {
	p := &common.WriteParam{
		DryRun: c.Query("dryRun") == "true",
	}
	p.Cluster = c.Param("cluster")
	p.Namespace = c.Param("namespace")
	p.ResourceType = c.Param("workloadtype")
	p.Name = c.Param("workloadName")
	p.Group = appsv1.SchemeGroupVersion.Group
	p.Version = appsv1.SchemeGroupVersion.Version
	var err error
	p.Raw, err = c.GetRawData()
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	p.ContentType = c.ContentType()
	if p.Namespace != "" {
		if err := commonRouter.AuthProjectPermission(c, p.Cluster, p.Namespace, c.GetHeader(commonRouter.HandlerOrgan), c.GetHeader(commonRouter.HandlerProject)); err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
	}
	switch p.ResourceType {
	case workloadmodels.ResourceTypeDeployments:
		response, err := rwc.DeploymentHandler.Create(c, &workloadmodels.CreateDeploymentRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeStatefulSets:
		response, err := rwc.StatefulSetHandler.Create(c, &workloadmodels.CreateStatefulSetRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	}
}

func (rwc *routeWorkloadController) updateWorkload(c *gin.Context) {
	p := &common.WriteParam{}
	p.Cluster = c.Param("cluster")
	p.Namespace = c.Param("namespace")
	p.ResourceType = c.Param("workloadtype")
	p.Name = c.Param("workloadName")
	var err error
	p.Raw, err = c.GetRawData()
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	p.ContentType = c.ContentType()
	switch p.ResourceType {
	case workloadmodels.ResourceTypeDeployments:
		response, err := rwc.DeploymentHandler.Update(c, &workloadmodels.UpdateDeploymentRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeStatefulSets:
		response, err := rwc.StatefulSetHandler.Update(c, &workloadmodels.UpdateStatefulSetRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	}
}

func (rwc *routeWorkloadController) patchWorkload(c *gin.Context) {
	p := &common.WriteParam{}
	p.Cluster = c.Param("cluster")
	p.Namespace = c.Param("namespace")
	p.ResourceType = c.Param("workloadtype")
	p.Name = c.Param("workloadName")
	var err error
	p.Raw, err = c.GetRawData()
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	p.ContentType = c.ContentType()
	switch p.ResourceType {
	case workloadmodels.ResourceTypeDeployments:
		response, err := rwc.DeploymentHandler.Patch(c, &workloadmodels.PatchDeploymentRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	case workloadmodels.ResourceTypeStatefulSets:
		response, err := rwc.StatefulSetHandler.Patch(c, &workloadmodels.PatchStatefulSetRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)
	}
}

func (rwc *routeWorkloadController) deleteWorkload(c *gin.Context) {
	p := &common.WriteParam{}
	p.Cluster = c.Param("cluster")
	p.Namespace = c.Param("namespace")
	p.ResourceType = c.Param("workloadtype")
	p.Name = c.Param("workloadName")
	switch p.ResourceType {
	case workloadmodels.ResourceTypeDeployments:
		response, err := rwc.DeploymentHandler.Delete(c, &workloadmodels.DeleteDeploymentRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)

	case workloadmodels.ResourceTypeStatefulSets:
		response, err := rwc.StatefulSetHandler.Delete(c, &workloadmodels.DeleteStatefulSetRequest{WriteParam: *p})
		if err != nil {
			utils.Failed(c, errors.NewFromError(c, err))
			return
		}
		utils.Succeed(c, response)

	}
}
