package cloudservice

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

func (c *cloudserviceRouter) appendServiceRouter(group *gin.RouterGroup) {
	group.GET("", c.ListCloudService)
	group.GET("/:cloudServiceName", c.DescribeCloudService)
}

// ListCloudService 获取云服务列表
//
//	@Tags			云服务
//	@Summary		获取云服务列表
//	@Description	获取平台上安装的云服务列表信息
//	@Router			/apis/v1/cloudservices [get]
//	@Param			selector		query		string	false	"selector"
//	@Param			page_size		query		string	false	"filter page_size 页长"
//	@Param			page_num		query		string	false	"filter page_num 页码"
//	@Param			sort_name		query		string	false	"sort_name"
//	@Param			sort_order		query		string	false	"sort_order"
//	@Param			sort_func		query		string	false	"sort_func"
//	@Param			label_selector	query		string	false	"标签选择器"
//	@Success		200				{object}	utils.Response{}
func (c *cloudserviceRouter) ListCloudService(ctx *gin.Context) {
	filter := utils.ParseQueryParams[models.CloudService](ctx)
	cloudServices, err := c.handler.ListCloudService(ctx, filter)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, cloudServices)
}

// DescribeCloudService 获取云服务详情
//
//	@Tags			云服务
//	@Summary		获取云服务详情
//	@Description	查看平台云服务详情
//	@Param			cloudServiceName	path	string	true	"云服务名称"
//	@Router			/apis/v1/cloudservices/{cloudServiceName} [get]
//	@Success		200	{object}	utils.Response{data=models.CloudService}
func (c *cloudserviceRouter) DescribeCloudService(ctx *gin.Context) {
	cloudServiceName := ctx.Param("cloudServiceName")
	if cloudServiceName == "" {
		utils.Failed(ctx, errors.NewFromCode(errors.Var.ResourceNotFount))
		return
	}
	cloudService, err := c.handler.DescribeCloudService(ctx, cloudServiceName)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, cloudService)
}
