package cloudservice

import (
	"strings"

	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

func (c *cloudserviceRouter) appendComponentRouter(group *gin.RouterGroup) {
	// 获取云服务的管控面云组件
	group.GET("/:cloudServiceName/components", c.ListCloudServiceComponents)

	// 获取云组件详情
	group.GET("/:cloudServiceName/components/:componentName", c.CloudServiceComponentDetail)

	// 获取云组件的workload
	group.GET("/:cloudServiceName/components/:componentName/workloads", c.CloudServiceComponentWorkloads)
	group.GET("/:cloudServiceName/components/:componentName/workloadtypes/:workloadType/workloads", c.CloudServiceComponentWorkloads)
	group.GET("/:cloudServiceName/components/:componentName/workloadtypes/:workloadType/namespaces/:namespace/workloads", c.CloudServiceComponentWorkloads)

	// 获取workload 的pod
	group.GET("/:cloudServiceName/components/:componentName/podinstances", c.CloudServiceComponentPodList)
	group.GET("/:cloudServiceName/components/:componentName/workloadtypes/:workloadType/podinstances", c.CloudServiceComponentPodList)
	group.GET("/:cloudServiceName/components/:componentName/workloadtypes/:workloadType/namespaces/:namespace/podinstances", c.CloudServiceComponentPodList)
	group.GET("/:cloudServiceName/components/:componentName/workloadtypes/:workloadType/namespaces/:namespace/workloads/:workloadName/podinstances", c.CloudServiceComponentPodList)

}

// ListCloudServiceComponents 获取云服务的组件列表
//
//	@Tags			云组件
//	@Summary		获取云服务的组件列表
//	@Description	根据条件获取云服务的组件列表 该接口属于通用接口 获取管控面数据 query='componentType=managed';获取业务面数据 query='componentType=work';日志查询时候 不传 query;
//	@Param			cloudServiceName	path	string	true	"云服务名称"
//	@Param			componentType		query	string	false	"云组件类型"
//	@Param			cluster				query	string	false	"云组件所在集群"
//	@Router			/apis/v1/cloudservices/{cloudServiceName}/components [get]
//	@Success		200	{object}	utils.Response{data=models.CloudServiceComponentSortByCluster}
func (c *cloudserviceRouter) ListCloudServiceComponents(ctx *gin.Context) {
	cloudServiceName := ctx.Param("cloudServiceName")
	if strings.EqualFold(cloudServiceName, "") {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "param cloudServiceName is not exist"))
		return
	}
	// 云组件类型
	componentType := ctx.Query("componentType")

	// 集群名称
	cluster := ctx.Query("cluster")

	pag, err := c.handler.ListCloudServiceComponent(ctx, cloudServiceName, componentType, cluster)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}

	utils.Succeed(ctx, pag)
	return

}

// CloudServiceComponentDetail 查看云组件详情
//
//	@Tags			云组件
//	@Summary		查看云组件详情
//	@Description	根据云服务、云服务下的组件名称与集群，查看管控面或数据面的云组件详情
//	@Param			cloudServiceName	path	string	true	"云服务名称"
//	@Param			componentName		path	string	true	"云组件名称"
//	@Param			cluster				query	string	true	"集群"
//	@Router			/apis/v1/cloudservices/{cloudServiceName}/components/{componentName} [get]
//	@Success		200	{object}	utils.Response{data=models.CloudServiceComponent}
func (c *cloudserviceRouter) CloudServiceComponentDetail(ctx *gin.Context) {
	cloudServiceName := ctx.Param("cloudServiceName")
	if strings.EqualFold(cloudServiceName, "") {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "param cloudServiceName is not exist"))
		return
	}

	cloudComponentName := ctx.Param("componentName")
	if strings.EqualFold(cloudComponentName, "") {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "param componentName is not exist"))
		return
	}

	// 获取集群名称 管控组件可能不存在cluster-id
	clusterName := ctx.Query("cluster")
	if strings.EqualFold(clusterName, "") {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "param cluster is not exist"))
		return
	}

	result, err := c.handler.CloudComponentDetail(ctx, cloudServiceName, cloudComponentName, clusterName)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}

	utils.Succeed(ctx, result)
	return

}

// CloudServiceComponentWorkloads 获取云组件的workload
//
//	@Tags			云组件工作负载
//	@Summary		获取云组件的workload
//	@Description	获取某一集群下的某一云组件的workload列表
//	@Param			cloudServiceName	path	string	true	"云服务名称"
//	@Param			componentName		path	string	true	"云组件名称"
//	@Param			workloadType		path	string	false	"工作负载类型 (Option)"
//	@Param			namespace			path	string	false	"命名空间(Option)"
//	@Param			cluster				query	string	true	"集群"
//	@Router			/apis/v1/cloudservices/{cloudServiceName}/components/{componentName}/workloads [get]
//	@Router			/apis/v1/cloudservices/{cloudServiceName}/components/{componentName}/workloadtypes/{workloadType}/workloads  [get]
//	@Router			/apis/v1/cloudservices/{cloudServiceName}/components/{componentName}/workloadtypes/{workloadType}/namespaces/{namespace}/workloads  [get]
//	@Success		200	{object}	utils.Response{data=[]models.CloudComponentWorkload}
func (c *cloudserviceRouter) CloudServiceComponentWorkloads(ctx *gin.Context) {

	readParam := models.CloudComponentWorkloadReadParam{
		CloudServiceName:   ctx.Param("cloudServiceName"),
		CloudComponentName: ctx.Param("componentName"),
		WorkloadType:       models.WorkloadType(ctx.Param("workloadType")),
		Namespace:          ctx.Param("namespace"),
		Cluster:            ctx.Query("cluster"),
	}

	result, err := c.handler.CloudComponentWorkloads(ctx, readParam)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}

	utils.Succeed(ctx, result)
	return
}

// CloudServiceComponentPodList 获取云组件Workload 的 Pod 列表
//
//	@Tags			云组件工作负载Pod实例
//	@Summary		获取云组件Workload 的 Pod 列表
//	@Description	获取云组件Workload 的 Pod 列表
//	@Param			cloudServiceName	path	string	true	"云服务名称"
//	@Param			componentName		path	string	true	"云组件名称"
//	@Param			workloadType		path	string	false	"工作负载类型"
//	@Param			namespace			path	string	false	"命名空间"
//	@Param			workloadName		path	string	false	"工作负载名称"
//	@Param			cluster				query	string	true	"集群"
//	@Router			/apis/v1/cloudservices/{cloudServiceName}/components/{componentName}/podinstances [get]
//	@Router			/apis/v1/cloudservices/{cloudServiceName}/components/{componentName}/workloadtypes/{workloadType}/podinstances [get]
//	@Router			/apis/v1/cloudservices/{cloudServiceName}/components/{componentName}/workloadtypes/{workloadType}/namespaces/{namespace}/podinstances [get]
//	@Router			/apis/v1/cloudservices/{cloudServiceName}/components/{componentName}/workloadtypes/{workloadType}/namespaces/{namespace}/workloads/{workloadName}/podinstances [get]
//	@Success		200	{object}	utils.Response{data=[]models.CloudComponentWorkloadPodGrouper}
func (c *cloudserviceRouter) CloudServiceComponentPodList(ctx *gin.Context) {

	readParam := models.CloudComponentWorkloadPodInstanceReadParam{
		CloudServiceName:   ctx.Param("cloudServiceName"),
		CloudComponentName: ctx.Param("componentName"),
		WorkloadType:       models.WorkloadType(ctx.Param("workloadType")),
		Namespace:          ctx.Param("namespace"),
		WorkloadName:       ctx.Param("workloadName"),
		Cluster:            ctx.Query("cluster"),
	}

	result, err := c.handler.CloudComponentPodInstance(ctx, readParam)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}

	utils.Succeed(ctx, result)
	return
}
