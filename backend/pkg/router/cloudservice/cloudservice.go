package cloudservice

import (
	"github.com/gin-gonic/gin"
	cloudservicehandler "harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/cloudservice"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

type cloudserviceRouter struct {
	handler cloudservicehandler.HandlerIntf
}

func NewCloudserviceRouter() routerutil.ApiController {
	return &cloudserviceRouter{
		handler: cloudservicehandler.NewCloudServiceHandler(),
	}
}

func (c *cloudserviceRouter) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.ApiV1Group + "/cloudservices")
}

func (c *cloudserviceRouter) RegisterRouter(group *gin.RouterGroup) {
	// 云服务router
	c.appendServiceRouter(group)
	// 云组件router
	c.appendComponentRouter(group)
}
