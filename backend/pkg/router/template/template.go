package template

import (
	"io"
	"mime/multipart"

	"github.com/gin-gonic/gin"
	_ "github.com/xuri/excelize/v2"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/template"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

func NewRouter() routerutil.ApiController {
	return &router{
		handler: template.NewHandler(),
	}
}

type router struct {
	handler template.Handler
}

func (*router) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.ApiV1Group + "/template")
}

func (r *router) RegisterRouter(group *gin.RouterGroup) {
	r.excelRouter(group.Group("/excel/:template_code"))
}

func (r *router) excelRouter(group *gin.RouterGroup) {
	group.GET("/download", r.excelTemplateDownload)
	group.POST("/upload", r.excelTemplateDownloadAnalyzing)
}

// excelTemplateDownload 下载模版文件
//
//	@Tags			模版文件相关
//	@Summary		获取excel模版文件的下载流
//	@Description	获取excel模版文件的下载流
//	@Param			language		query	string	false	"语言"		Enums(zh-CN,zh-HK,en-US)																																																		default()
//	@Param			template_code	path	string	true	"模版文件code"	Enums(cluster_create_nodes_template_AllInOne,cluster_create_nodes_template_MinimizeHA,cluster_create_nodes_template_StandardNoneHA,cluster_create_nodes_template_StandardHA,cluster_node_batch_append,cluster_upgrade_nodes)	default(cluster_create_nodes_template_AllInOne)
//	@Router			/apis/v1/template/excel/{template_code}/download [get]
//	@Success		200	{file}	application/octet-stream;charset=utf-8
func (r *router) excelTemplateDownload(ctx *gin.Context) {
	templateCode := ctx.Param("template_code")
	if templateCode == "" {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "path param template_code is empty"))
		return
	}
	downloadInfo, err := r.handler.ExcelTemplateDownload(ctx, templateCode)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	ctx.Header("Content-Disposition", "attachment; filename="+downloadInfo.FileName)
	ctx.Header("Content-Type", "text/plain")
	buffer := downloadInfo.Buffer
	ctx.Stream(func(w io.Writer) bool {
		cacheBytes := make([]byte, 1024)
		size, err := buffer.Read(cacheBytes)
		if err != nil || size == 0 {
			return false
		}
		_, err = w.Write(cacheBytes[0:size])
		if err != nil {
			return false
		}
		return true
	})
}

// excelTemplateDownload 下载模版文件
//
//	@Tags			模版文件相关
//	@Summary		解析上传excel文件的内容
//	@Description	解析上传excel文件的内容
//	@Param			template_code	path		string	true	"模版文件code"	Enums(cluster_create_nodes_template_AllInOne,cluster_create_nodes_template_MinimizeHA,cluster_create_nodes_template_StandardNoneHA,cluster_create_nodes_template_StandardHA,cluster_node_batch_append,cluster_upgrade_nodes)	default(cluster_create_nodes_template_AllInOne)
//	@Param			file			formData	file	true	"文件"
//	@Router			/apis/v1/template/excel/{template_code}/upload [post]
//	@Success		200	{object}	utils.Response{}
func (r *router) excelTemplateDownloadAnalyzing(ctx *gin.Context) {
	templateCode := ctx.Param("template_code")
	if templateCode == "" {
		utils.Failed(ctx, errors.NewFromCodeWithMessage(errors.Var.ParamError, "path param template_code is empty"))
		return
	}

	multipartFileInfo, err := ctx.MultipartForm()
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	var fileHeaderList []*multipart.FileHeader
	for _, fileHeaders := range multipartFileInfo.File {
		for _, fileHeader := range fileHeaders {
			fileHeader := fileHeader
			fileHeaderList = append(fileHeaderList, fileHeader)
		}
	}

	obj, err := r.handler.ExcelTemplateUpdate(ctx, templateCode, fileHeaderList)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, obj)

}
