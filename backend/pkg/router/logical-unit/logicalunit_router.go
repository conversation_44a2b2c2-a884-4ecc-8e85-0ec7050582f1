package logical_unit

import (
	"github.com/gin-gonic/gin"
	_ "harmonycloud.cn/ingress-expose-helper/pkg/apis/expose.helper/v1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/logicalunit"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	"strconv"
)

func (lur *LogicalUnitRouter) dataCenterRoutes(routes *gin.RouterGroup) {
	routes.GET("/dataCenterList", lur.listDataCenter)
	routes.GET("/dataCenterInfo/:dataCenterId", lur.getDataCenterInfo)
	routes.POST("/createDataCenter", lur.createDataCenter)
	routes.DELETE("/deleteDataCenter/dataCenter/:id", lur.deleteDataCenter)
	routes.PUT("/updateDataCenter/dataCenter/:id", lur.updateDataCenter)
}

func (lur *LogicalUnitRouter) logicalUnitRoutes(routes *gin.RouterGroup) {
	routes.GET("/listCenterAndUnit", lur.listCenterAndUnit)
	routes.POST("/createLogicalUnit/:dataCenterId", lur.createLogicalUnit)
	routes.DELETE("/deleteLogicalUnit/:logicalUnitId", lur.deleteLogicalUnit)
	routes.PUT("/updateLogicalUnit/:logicalUnitId", lur.updateLogicalUnit)
	routes.POST("/logicalUnitBindCluster", lur.logicalUnitBindCluster)
	routes.DELETE("/deleteLogicalInfo/:logicalUnitId", lur.deleteLogicalInfo)
	routes.GET("/getLogicalUnitCluster", lur.getLogicalUnitCluster)
	routes.GET("/getOrganLogicalUnitCluster", lur.getOrganLogicalUnitCluster)
	routes.GET("/getProjectLogicalUnitCluster", lur.getProjectLogicalUnitCluster)
	routes.GET("/logicalUnitInfo/:logicalUnitId", lur.getLogicalUnitInfo)
	routes.GET("/logicalUnitInfo/getClusterIngress/:clusterName", lur.GetClusterIngress)
	routes.GET("/logicalUnitInfo/getClusterNetwork/:clusterName", lur.GetClusterNetwork)
	routes.GET("/logicalUnitInfo/getClusterNode/:clusterName", lur.GetClusterNode)
	routes.GET("/logicalUnitInfo/getClusterStorage/:clusterName", lur.GetClusterStorage)
}

func (lur *LogicalUnitRouter) listDataCenter(c *gin.Context) {
	list, err := lur.LogicalUnitHandler.ListDataCenter(c)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, list)
}

func (lur *LogicalUnitRouter) createDataCenter(c *gin.Context) {
	var req logicalunit.DataCenter
	// 解析请求体
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, err.Error()))
		return
	}
	err, id := lur.LogicalUnitHandler.CreateDataCenter(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, id)
}

func (lur *LogicalUnitRouter) deleteDataCenter(c *gin.Context) {
	err := lur.LogicalUnitHandler.DeleteDataCenter(c, c.Param("id"))
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, nil)
}

func (lur *LogicalUnitRouter) updateDataCenter(c *gin.Context) {
	dataCenterId, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	var req logicalunit.DataCenter
	// 解析请求体
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, err.Error()))
		return
	}
	req.Id = dataCenterId
	err = lur.LogicalUnitHandler.UpdateDataCenter(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, nil)
}

func (lur *LogicalUnitRouter) getDataCenterInfo(c *gin.Context) {
	dataCenterInfo, err := lur.LogicalUnitHandler.GetDataCenterInfo(c, c.Param("dataCenterId"))
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, dataCenterInfo)
}

func (lur *LogicalUnitRouter) createLogicalUnit(c *gin.Context) {
	dataCenterId, err := strconv.ParseInt(c.Param("dataCenterId"), 10, 64)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	var req logicalunit.LogicalUnit
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, err.Error()))
		return
	}
	req.DataCenterId = dataCenterId
	unit, err := lur.LogicalUnitHandler.CreateLogicalUnit(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, unit)
}

func (lur *LogicalUnitRouter) deleteLogicalUnit(c *gin.Context) {
	err := lur.LogicalUnitHandler.DeleteLogicalUnit(c, c.Param("logicalUnitId"))
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, nil)
}

func (lur *LogicalUnitRouter) updateLogicalUnit(c *gin.Context) {
	logicalUnitId, err := strconv.ParseInt(c.Param("logicalUnitId"), 10, 64)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	var req logicalunit.LogicalUnit
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, err.Error()))
		return
	}
	req.Id = logicalUnitId
	err = lur.LogicalUnitHandler.UpdateLogicalUnit(c, &req)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, nil)
}

func (lur *LogicalUnitRouter) logicalUnitBindCluster(c *gin.Context) {
	var logicalUnitInfo logicalunit.InfoBinding
	if err := c.ShouldBindJSON(&logicalUnitInfo); err != nil {
		utils.Failed(c, errors.NewFromCodeWithMessage(errors.Var.ParamError, err.Error()))
		return
	}
	if err := lur.LogicalUnitHandler.LogicalUnitInfoBind(c, logicalUnitInfo); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, nil)
}

func (lur *LogicalUnitRouter) deleteLogicalInfo(c *gin.Context) {
	logicalUnitId := c.Param("logicalUnitId")
	info := c.Query("info")
	infoType := c.Query("infoType")
	if err := lur.LogicalUnitHandler.DeleteLogicalInfo(c, logicalUnitId, info, infoType); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, nil)
}

func (lur *LogicalUnitRouter) getLogicalUnitCluster(c *gin.Context) {
	logicalUnitId := c.Query("logicalUnitId")
	dataCenterId := c.Query("dataCenterId")
	clusters, err := lur.LogicalUnitHandler.GetLogicalUnitClusters(c, logicalUnitId, dataCenterId)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, clusters)
}

func (lur *LogicalUnitRouter) getOrganLogicalUnitCluster(c *gin.Context) {
	logicalUnitId := c.Query("logicalUnitId")
	dataCenterId := c.Query("dataCenterId")
	organId := c.Query("organId")
	clusters, err := lur.LogicalUnitHandler.GetOrganLogicalUnitClusters(c, logicalUnitId, dataCenterId, organId)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, clusters)
}

func (lur *LogicalUnitRouter) getProjectLogicalUnitCluster(c *gin.Context) {
	logicalUnitId := c.Query("logicalUnitId")
	dataCenterId := c.Query("dataCenterId")
	organId := c.Query("organId")
	projectId := c.Query("projectId")

	clusters, err := lur.LogicalUnitHandler.GetProjectLogicalUnitClusters(c, logicalUnitId, dataCenterId, organId, projectId)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, clusters)
}

func (lur *LogicalUnitRouter) listCenterAndUnit(c *gin.Context) {
	units, err := lur.LogicalUnitHandler.ListCenterAndUnit(c)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}

	utils.Succeed(c, units)
}

func (lur *LogicalUnitRouter) getLogicalUnitInfo(c *gin.Context) {
	logicalUnitId := c.Param("logicalUnitId")
	info, err := lur.LogicalUnitHandler.GetLogicalUnitInfo(c, logicalUnitId)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, info)
}

// 获取负载均衡
func (lur *LogicalUnitRouter) GetClusterIngress(c *gin.Context) {
	clusterName := c.Param("clusterName")
	cluster, err := lur.LogicalUnitHandler.GetClusterIngress(c, clusterName)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, cluster)
}

// 获取网络
func (lur *LogicalUnitRouter) GetClusterNetwork(c *gin.Context) {
	clusterName := c.Param("clusterName")
	cluster, err := lur.LogicalUnitHandler.GetClusterNetwork(c, clusterName)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, cluster)
}

// 获取节点
func (lur *LogicalUnitRouter) GetClusterNode(c *gin.Context) {
	clusterName := c.Param("clusterName")
	cluster, err := lur.LogicalUnitHandler.GetClusterNode(c, clusterName)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, cluster)
}

// 获取存储
func (lur *LogicalUnitRouter) GetClusterStorage(c *gin.Context) {
	clusterName := c.Param("clusterName")
	cluster, err := lur.LogicalUnitHandler.GetClusterStorage(c, clusterName)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, cluster)
}
