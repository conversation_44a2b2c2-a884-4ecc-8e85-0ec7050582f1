package logical_unit

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

func (lur *LogicalUnitRouter) LogicalUnitByOrganRoutes(routes *gin.RouterGroup) {
	routes.GET("/unitByOrgan/:organId", lur.getUnitByOrgan)
	routes.GET("/unitByOrgan/:organId/:projectId", lur.getUnitByProject)
}

func (lur *LogicalUnitRouter) getUnitByOrgan(c *gin.Context) {
	organId := c.Param("organId")
	centerAndUnits, err := lur.LogicalAndOrganHandler.GetLogicalUnitByOrgan(c, organId)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, centerAndUnits)
}

func (lur *LogicalUnitRouter) getUnitByProject(c *gin.Context) {
	organId := c.Param("organId")
	projectId := c.Param("projectId")
	centerAndUnits, err := lur.LogicalAndProjectHandler.GetLogicalUnitByProject(c, organId, projectId)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, centerAndUnits)
}
