package logical_unit

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/logicalunitservice"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

func NewLogicalUnitRouter() routerutil.ApiController {
	return &LogicalUnitRouter{
		LogicalUnitHandler:       logicalunitservice.NewLogicalUnitHandler(),
		LogicalAndOrganHandler:   logicalunitservice.NewLogicalAndOrganHandler(),
		LogicalAndProjectHandler: logicalunitservice.NewLogicalAndProjectHandler(),
	}
}

type LogicalUnitRouter struct {
	LogicalUnitHandler       logicalunitservice.LogicalUnitIntf
	LogicalAndOrganHandler   logicalunitservice.LogicalAndOrganIntf
	LogicalAndProjectHandler logicalunitservice.LogicalAndProjectIntf
}

func (l LogicalUnitRouter) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.ApiV1Group)
}

func (l LogicalUnitRouter) RegisterRouter(routers *gin.RouterGroup) {
	l.dataCenterRoutes(routers)
	l.logicalUnitRoutes(routers)
	l.LogicalUnitByOrganRoutes(routers)
}
