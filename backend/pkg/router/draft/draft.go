package draft

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/draft"
	modeldraft "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/draft"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

func NewRouter() routerutil.ApiController {
	return &router{
		code:    "clusters-create",
		handler: draft.NewHandler(),
	}
}

// 集群Router
type router struct {
	code    string
	handler draft.Handler
}

func (r *router) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.ApiV1Group + "/draft/clusters-create")
}

func (r *router) RegisterRouter(group *gin.RouterGroup) {
	group.POST("", r.applyDraft)
	group.DELETE("", r.deleteDraft)
	group.GET("", r.getDraft)
}

// applyDraft 应用草稿
//
//	@Tags			创建集群 - 草稿相关API
//	@Summary		应用草稿
//	@Description	创建或保存草稿
//	@Param			Authorization	header	string	true	"用户令牌"
//	@Param			requestBody		body	any		true	"保存｜创建草稿JSON"
//	@Router			/apis/v1/draft/clusters-create [post]
//	@Success		200	{object}	utils.Response{data=bool}
func (r *router) applyDraft(ctx *gin.Context) {
	var body map[string]interface{}
	if err := ctx.ShouldBind(&body); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	req := modeldraft.Request{
		Type:  r.code,
		Param: body,
	}

	if err := r.handler.SaveDraft(ctx, &req); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, true)
}

// deleteDraft 删除草稿
//
//	@Tags			创建集群 - 草稿相关API
//	@Summary		删除草稿
//	@Description	删除草稿
//	@Param			Authorization	header	string	true	"用户令牌"
//	@Router			/apis/v1/draft/clusters-create [delete]
//	@Success		200	{object}	utils.Response{data=bool}
func (r *router) deleteDraft(ctx *gin.Context) {
	if err := r.handler.DeleteDraft(ctx, r.code); err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, true)

}

// getDraft 获取草稿内容
//
//	@Tags			创建集群 - 草稿相关API
//	@Summary		获取草稿内容
//	@Description	获取草稿内容
//	@Param			Authorization	header	string	true	"用户令牌"
//	@Router			/apis/v1/draft/clusters-create [get]
//	@Success		200	{object}	utils.Response{data=any}
func (r *router) getDraft(ctx *gin.Context) {
	response, err := r.handler.GetDraft(ctx, r.code)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	if response.Status == modeldraft.NotExists {
		utils.Succeed(ctx, nil)
		return
	}
	utils.Succeed(ctx, response.Data.Draft)
}
