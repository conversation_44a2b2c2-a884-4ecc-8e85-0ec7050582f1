package health

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

func NewHealthRouter() routerutil.ApiController {
	return &healthRouter{}
}

type healthRouter struct {
}

func (*healthRouter) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.ApiV1Group + "/health")
}
func (r *healthRouter) RegisterRouter(group *gin.RouterGroup) {
	group.GET("", r.HealthOk)
}

func (*healthRouter) HealthOk(ctx *gin.Context) {
	utils.Succeed(ctx, "ok")
}
