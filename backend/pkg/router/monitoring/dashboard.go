package cloudservice

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	handler "harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/monitoring"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/monitoring"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

type dashboardRouter struct {
	handler handler.DashboardIntf
}

func NewCloudserviceRouter() routerutil.ApiController {
	return &dashboardRouter{
		handler: handler.NewDashboardHandler(),
	}
}

func (c *dashboardRouter) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.ApiV1Group + "/monitoring/dashboards")
}

func (c *dashboardRouter) RegisterRouter(group *gin.RouterGroup) {
	group.GET("", c.ListDashboards)
}

// ListDashboards 获取Grafana Dashboards
//
//	@Tags			Grafana
//	@Summary		获取Grafana Dashboards
//	@Description	获取Grafana Dashboards
//	@Param			key	query	monitoring.ObjectKey	true	"Dashboard查询Request"
//	@Router			/apis/v1/monitoring/dashboards [get]
//	@Success		200	{object}	utils.Response{data=monitoring.Dashboard}
func (d *dashboardRouter) ListDashboards(c *gin.Context) {
	var key monitoring.ObjectKey
	if err := c.ShouldBind(&key); err != nil {
		logger.GetLogger().Error("params is nil")
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	if err := key.Verify(); err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	list, err := d.handler.ListDashboards(c, key)
	if err != nil {
		utils.Failed(c, errors.NewFromError(c, err))
		return
	}
	utils.Succeed(c, list)
}
