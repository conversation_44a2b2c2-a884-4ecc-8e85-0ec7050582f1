package workspace

import (
	"github.com/gin-gonic/gin"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/auth"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/workspace"
	workspacemodel "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/workspace"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
	routerutil "harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/router"
)

var _ = workspacemodel.Resource{}

func NewWorkspaceController() routerutil.ApiController {
	return &controller{
		handler:            workspace.NewWorkspaceHandler(),
		currentUserHandler: auth.NewHandler(),
	}
}

type controller struct {
	handler            workspace.WorkspaceIntf
	currentUserHandler auth.Handler
}

func (c *controller) GetGroup(engine *gin.Engine) *gin.RouterGroup {
	return engine.Group(utils.ApiV1Group + "/workspace")
}

func (c *controller) RegisterRouter(group *gin.RouterGroup) {
	group.GET("/getCloudservicesCard", c.getCloudservicesCard)
	group.GET("/cloudservices/:cloudServiceName/resources", c.ListWorkspaceCloudServiceResources)
	group.GET("/getProjectResources", c.GetProjectResources)

}

func (c *controller) getCloudservicesCard(ctx *gin.Context) {
	cloudServiceList, err := c.handler.GetCloudServicesCard(ctx)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, cloudServiceList)
}

// ListWorkspaceCloudServiceResources lists the cloud service resources for a workspace.
//
//	@Summary		List cloud service resources
//	@Description	List the resources related to a cloud service in a workspace.
//	@Tags			WorkspaceIntf
//	@Accept			json
//	@Produce		json
//	@Param			cloudServiceName	path		string											true	"Name of the cloud service"
//	@Param			projectId			query		string											false	"ID of the project"
//	@Param			organId				query		string											false	"ID of the organization"
//	@Success		200					{object}	utils.Response{data=[]workspacemodel.Resource}	"List of cloud service resources"
//	@Router			/apis/v1/workspace/cloudservices/{cloudServiceName} [get]
func (c *controller) ListWorkspaceCloudServiceResources(ctx *gin.Context) {
	cloudServiceName := ctx.Param("cloudServiceName")
	projectId, organId := ctx.Query("projectId"), ctx.Query("organId")
	resourceType := ctx.Query("resourceType")
	withProjectId := ctx.Query("withProjectId")
	authorization := ctx.GetHeader("Authorization")
	user, err := c.currentUserHandler.CurrentUser(ctx)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	resList, err := c.handler.GetCloudServiceResourceList(ctx, authorization, user.GetUserId(), projectId, organId, cloudServiceName, resourceType, withProjectId)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, resList)
}

func (c *controller) GetProjectResources(ctx *gin.Context) {
	//namespaces := ctx.QueryArray("namespaces")
	projectIds := ctx.QueryArray("projectIds")
	group := ctx.Query("group")
	version := ctx.Query("version")
	resourceType := ctx.Query("resourceType")
	resourceList, err := c.handler.GetProjectResources(ctx, group, version, resourceType, projectIds)
	if err != nil {
		utils.Failed(ctx, errors.NewFromError(ctx, err))
		return
	}
	utils.Succeed(ctx, resourceList)
}
