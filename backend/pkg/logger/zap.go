package logger

import (
	"os"
	"strings"
	"sync"

	"github.com/go-logr/logr"
	"github.com/go-logr/zapr"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

var logger *zap.Logger
var once sync.Once

func Init() {
	once.Do(initLogger)
}
func Sync() {
	logger.Sync()
}

func isRunningInKubernetes() bool {
	_, inCluster := os.LookupEnv("KUBERNETES_SERVICE_HOST")
	return inCluster
}

// todo init log
func initLogger() {
	var err error
	level := getLogLevelFromEnv()
	var cfg zap.Config
	if isRunningInKubernetes() {
		// 生产模式
		cfg = zap.NewProductionConfig()
		cfg.EncoderConfig.EncodeTime = zapcore.RFC3339TimeEncoder
		cfg.EncoderConfig.TimeKey = "time"
	} else {
		// 开发模式
		cfg = zap.NewDevelopmentConfig()
	}

	cfg.Level = zap.NewAtomicLevelAt(level)

	logger, err = cfg.Build()
	if err != nil {
		panic("Failed to initialize logger: " + err.Error())
	}
}

// getLogLevelFromEnv ...
func getLogLevelFromEnv() zapcore.Level {
	switch strings.ToLower(os.Getenv("ZAP_LOG_LEVEL")) {
	case "debug":
		return zapcore.DebugLevel
	case "info":
		return zapcore.InfoLevel
	case "warn":
		return zapcore.WarnLevel
	case "error":
		return zapcore.ErrorLevel
	case "dpanic":
		return zapcore.DPanicLevel
	case "panic":
		return zapcore.PanicLevel
	case "fatal":
		return zapcore.FatalLevel
	default:
		return zapcore.InfoLevel // 默认
	}
}

// GetLogger
// 性能模式日志 例
//
//	logger.Info("failed to fetch URL",
//	// Structured context as strongly typed Field values.
//	zap.String("url", url),
//	zap.Int("attempt", 3),
//	zap.Duration("backoff", time.Second),
//
// )
func GetLogger() *zap.Logger {
	return logger
}

// GetSugared
// printf 风格模式 例
// sugar.Infow("failed to fetch URL",
//
//	// Structured context as loosely typed key-value pairs.
//	"url", url,
//	"attempt", 3,
//	"backoff", time.Second,
//
// )
func GetSugared() *zap.SugaredLogger {
	return logger.Sugar()
}

// GetLogr 获取logr 接口风格的日志
func GetLogr() logr.Logger {
	return zapr.NewLogger(logger)
}
