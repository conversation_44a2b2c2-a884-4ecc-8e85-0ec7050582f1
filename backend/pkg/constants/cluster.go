package constants

const (
	// InstallTypeLabelKey
	// 标记installer 的类型
	InstallTypeLabelKey = "installer.unified-platform.harmonycloud.cn/type"
)

const (
	// ClusterChronyServerLabelKey
	// chrony server 的 label key
	ClusterChronyServerLabelKey = "node-role.kubernetes.io/chrony-server"

	// CreateClusterClusterNameLabelKey
	// 记录创建集群的installer 中的集群名称
	CreateClusterClusterNameLabelKey = "create-cluster.form.portal/cluster-name"

	// NodeUpDownClusterNameLabelKey
	// 记录节点上下线的installer 中的集群名称
	NodeUpDownClusterNameLabelKey = "node-up-down.form.portal/cluster-name"

	// NodeUpDownTypeLabelKey
	// 记录节点上下线的installer 中的具体类型 nodeUp、nodeDown、batch
	NodeUpDownTypeLabelKey = "node-up-down.form.portal/type"

	// NodeUpDownIPLabelKey
	// 记录节点上下线的IP
	NodeUpDownIPLabelKey = "node-up-down.form.portal/ip"

	// CreateClusterInstallerTypeLabelValue
	// 标记创建集群类型的Installer
	CreateClusterInstallerTypeLabelValue = "create-cluster"
	// UpgradeClusterInstallerTypeLabelValue 集群升级类型的Installer
	UpgradeClusterInstallerTypeLabelValue = "upgrade-cluster"
	// NodeUpDownInstallerTypeLabelValue
	// 标记节点上下线类型的Installer
	NodeUpDownInstallerTypeLabelValue = "node-up-down"
)

const (

	// CreateClusterNodeTypeAnnotationKey
	// 标记节点类型用于前端回显
	CreateClusterNodeTypeAnnotationKey = "create-cluster.form.portal/node-config-type"
	// CreateClusterNodeOSAnnotationKey
	// 标记节点操作系统用于前端回显
	CreateClusterNodeOSAnnotationKey = "create-cluster.form.portal/node-os"

	// CreateClusterNodeArchAnnotationKey
	// 标记节点架构用于前端回显
	CreateClusterNodeArchAnnotationKey = "create-cluster.form.portal/node-arch"

	// CreateClusterDefaultNodeAnnotationKey
	// 标记表单中哪些节点是默认节点
	CreateClusterDefaultNodeAnnotationKey = "create-cluster.form.portal/default-nodes"

	// InstallerCRITypeAnnotationKey
	// 记录任务所属集群的CRI类型
	InstallerCRITypeAnnotationKey = "cluster-cri-type"
)

const (
	TaskNameForNodeResetSisyphusSolutionApply        = "node-reset-sisyphus-solution-apply-task"
	TaskDescriptionForNodeResetSisyphusSolutionApply = "初始化西西弗斯solution-node-reset"

	TaskNameForNodeResetSisyphusSolutionExec        = "node-reset-sisyphus-solution-exec-task"
	TaskDescriptionForNodeResetSisyphusSolutionExec = "执行西西弗斯solution-node-reset"

	TaskNameForInitialNode        = "node-initial-task"
	TaskDescriptionForInitialNode = "初始化西西弗斯节点"

	TaskNameForSisyphusSolutionApply        = "sisyphus-solution-apply-task"
	TaskDescriptionForSisyphusSolutionApply = "初始化西西弗斯solution"

	TaskNameForSisyphusSolutionExec        = "sisyphus-solution-exec-task"
	TaskDescriptionForSisyphusSolutionExec = "执行西西弗斯solution"

	TaskNameForManagedCluster        = "sisyphus-managed-cluster"
	TaskDescriptionForManagedCluster = "纳管集群"

	TaskNameForMergeStcMeta        = "merge-stc-meta-task"
	TaskDescriptionForMergeStcMeta = "执行stc元数据合并"

	TaskNameAfterSisyphusManagedClusterSolutionExec        = "after-sisyphus-managed-cluster-solution-exec-task"
	TaskDescriptionAfterSisyphusManagedClusterSolutionExec = "执行集群纳管后置行为"

	TaskDescriptionForInstallerTemplate = "installer 模版创建"
)

const (
	// StcDescriptionAnnotationKey
	// stc 的annotation 记录集群 的描述信息
	StcDescriptionAnnotationKey = "description"
)

var UnKnowKubernetesVersion = "UnKnow"
var UnKnowCRIVersion = "UnKnow"
