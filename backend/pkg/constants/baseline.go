package constants

import "time"

const (
	BaselineResourcePrefixName = "baseline"
	BaselineCheckerPrefixName  = "checker"
	BaselineMonitorPrefixName  = "monitor"

	// BaselineNamespace 基线命名空间
	BaselineNamespace = "baseline"
	// BaselineInstanceName 基线实例
	BaselineInstanceName = "baseline"
)

const (
	// BaselineNodeSelectorSeparator 基线节点标签选择符分隔符
	BaselineNodeSelectorSeparator = ";"
)

const (
	// BaselineMonitorIdLabelKey  监控项monitorId label
	BaselineMonitorIdLabelKey = "baseline.unified-platform.harmonycloud.cn/monitor-id"
	// BaselineMonitorClusterLabelKey  监控项cluster label
	BaselineMonitorClusterLabelKey = "baseline.harmonycloud.cn/monitor-cluster"
	// BaselineStrategyIDLabelKey   策略id label
	BaselineStrategyIDLabelKey = "baseline.unified-platform.harmonycloud.cn/strategy-id"
	// BaselineStandardIDLabelKey   标准id label
	BaselineStandardIDLabelKey = "baseline.unified-platform.harmonycloud.cn/standard-id"
	// BaselineStandardCheckIDLabelKey 基线标准检查项ID
	BaselineStandardCheckIDLabelKey = "baseline.unified-platform.harmonycloud.cn/standard-check-id"
	// BaselineStandardJobIDLabelKey 基线标准任务ID
	BaselineStandardJobIDLabelKey = "baseline.unified-platform.harmonycloud.cn/standard-job-id"
	// BaselineCheckJobIDLabelKey    检查项任务ID
	BaselineCheckJobIDLabelKey = "baseline.unified-platform.harmonycloud.cn/check-job-id"
	// BaselineCheckerIDLabelKey   检查项id label
	BaselineCheckerIDLabelKey = "baseline.unified-platform.harmonycloud.cn/check-id"
	// BaselineClusterNameLabelKey   集群名称
	BaselineClusterNameLabelKey = "baseline.unified-platform.harmonycloud.cn/cluster-name"

	// BaselineStrategyIDsAnnotationKey 策略ids
	BaselineStrategyIDsAnnotationKey = "baseline.unified-platform.harmonycloud.cn/strategy-ids"
	// BaselineStandardIDsAnnotationKey 标准ids
	BaselineStandardIDsAnnotationKey = "baseline.unified-platform.harmonycloud.cn/standard-ids"

	// BaselineStrategyExecutionCronExpressionAnnotationKey 策略执行定时表达式
	BaselineStrategyExecutionCronExpressionAnnotationKey = "baseline.unified-platform.harmonycloud.cn/strategy-execution-cron-expression"
	// BaselineStrategyExecutionTimeAnnotationKey BaselineStrategyExecutionTime 策略执行时间
	BaselineStrategyExecutionTimeAnnotationKey = "baseline.unified-platform.harmonycloud.cn/strategy-execution-time"
	// BaselineStrategyExecutionStartTimeAnnotationKey BaselineStrategyExecutionStartTime 策略执行开始时间
	BaselineStrategyExecutionStartTimeAnnotationKey = "baseline.unified-platform.harmonycloud.cn/strategy-execution-start-time"
	// BaselineStrategyExecutionEndTimeAnnotationKey BaselineStrategyExecutionEndTime 策略执行结束时间
	BaselineStrategyExecutionEndTimeAnnotationKey = "baseline.unified-platform.harmonycloud.cn/strategy-execution-end-time"
	// BaselineStrategyExecutionDaemonLabelKey 为true时,表示使用守护执行
	BaselineStrategyExecutionDaemonLabelKey = "baseline.unified-platform.harmonycloud.cn/strategy-execution-daemon"
	// BaselineStrategyExecutionDaemonLabelValueTrue 为true时,表示使用守护执行
	BaselineStrategyExecutionDaemonLabelValueTrue = "true"

	// BaselineStrategyExecutionTypeLabelKey 策略执行类型
	BaselineStrategyExecutionTypeLabelKey = "baseline.unified-platform.harmonycloud.cn/strategy-execution-type"
	// BaselineStrategyExecutionRecurringTypeLabelKey 策略执行定时类型
	BaselineStrategyExecutionRecurringTypeLabelKey = "baseline.unified-platform.harmonycloud.cn/strategy-execution-recurring-type"

	// BaselineInfraBuiltinLabelKey 是否是底座内置
	BaselineInfraBuiltinLabelKey = "baseline.harmonycloud.cn/builtin"
	// BaselineInfraBuiltinLabelValueTrue 默认值
	BaselineInfraBuiltinLabelValueTrue = "true"
	// BaselineDescriptionAnnotationKey 说明
	BaselineDescriptionAnnotationKey = "baseline.harmonycloud.cn/description"
	// BaselineAdviceAnnotationKey 建议
	BaselineAdviceAnnotationKey = "baseline.harmonycloud.cn/advice"
	// BaselineComponentTypeLabelAnnotateKey 组件类型
	BaselineComponentTypeLabelAnnotateKey = "baseline.harmonycloud.cn/component-type"
	// BaselineComponentNameLabelAnnotateKey 表示当前检查项所属于的组件名称
	BaselineComponentNameLabelAnnotateKey = "baseline.harmonycloud.cn/component-name"
	// BaselineComponentVersionLabelAnnotateKey 组件检查项的版本
	BaselineComponentVersionLabelAnnotateKey = "baseline.harmonycloud.cn/component-version"
)

const (

	// BaselineStrategyJobTimeoutDefault 策略任务超时时间
	BaselineStrategyJobTimeoutDefault = 60 * time.Minute
	// BaselineStrategyJobTimeoutEnv 策略任务超时时间环境变量
	BaselineStrategyJobTimeoutEnv = "BASELINE_STRATEGY_JOB_TIMEOUT"

	// BaselineStandardJobTimeoutDefault 基线检查任务超时时间
	BaselineStandardJobTimeoutDefault = 30 * time.Minute
	// BaselineStandardJobTimeoutEnv 基线检查任务超时时间环境变量
	BaselineStandardJobTimeoutEnv = "BASELINE_STANDARD_JOB_TIMEOUT"

	// BaselineResourceRemainTimeDefault 资源保留时间
	BaselineResourceRemainTimeDefault = 24 * time.Hour
	// BaselineResourceRemainTimeEnv 资源保留时间环境变量
	BaselineResourceRemainTimeEnv = "BASELINE_RESOURCE_REMAIN_TIME"
	// BaselineResourceRemainTimeConfigKey 资源保留时间系统配置key
	BaselineResourceRemainTimeConfigKey = "baseline_resource_remain_time"

	// BaselineStrategyJobMaxConcurrencyDefault 策略任务并发数
	BaselineStrategyJobMaxConcurrencyDefault = 200
	// BaselineStrategyJobMaxConcurrencyEnv 策略任务并发数环境变量
	BaselineStrategyJobMaxConcurrencyEnv = "BASELINE_STANDARD_JOB_MAX_CONCURRENCY"

	// BaselineStandardJobMaxConcurrencyDefault 基线标准任务并发数
	BaselineStandardJobMaxConcurrencyDefault = 2000
	// BaselineStandardJobMaxConcurrencyEnv 基线标准任务并发数环境变量
	BaselineStandardJobMaxConcurrencyEnv = "BASELINE_STANDARD_JOB_MAX_CONCURRENCY"

	// BaselineMonitorHostPathPrefix 基线主机路径前缀
	BaselineMonitorHostPathPrefix = ""
	// BaselineMonitorHostPathPrefixEnv baseline monitor host path prefix 环境变量
	BaselineMonitorHostPathPrefixEnv = "BASELINE_MONITOR_HOST_PATH_PREFIX"

	// BaselineJobRecordRemainTimeDefault 基线任务记录保留时间 30天
	BaselineJobRecordRemainTimeDefault = 30 * 24 * time.Hour

	// BaselineSystemConfigKeyJobRecordRemainTimeConfigKey 基线任务记录保留时间系统配置key
	BaselineSystemConfigKeyJobRecordRemainTimeConfigKey = "baseline_job_record_remain_time"
	// BaselineSystemConfigKeyJobRecordRemainTimeEnv 基线任务记录保留时间系统配置key环境变量
	BaselineSystemConfigKeyJobRecordRemainTimeEnv = "BASELINE_JOB_RECORD_REMAIN_TIME"
	// BaselineSystemConfigType 基线系统配置类型
	BaselineSystemConfigType = "baseline"

	// BaselineScanTimeoutJobCronDefault 基线超时任务定时表达式
	// 每5分钟执行
	BaselineScanTimeoutJobCronDefault = "*/5 * * * *"
	// BaselineScanTimeoutJobCronConfigKey 基线超时任务定时表达式
	BaselineScanTimeoutJobCronConfigKey = "baseline_scan_timeout_job_cron"

	// BaselineScanExpiredJobResourceCronDefault 基线过期任务资源定时表达式
	// 3点24分执行
	BaselineScanExpiredJobResourceCronDefault = "24 3 * * *"
	// BaselineScanExpiredJobResourceCronConfigKey 基线过期任务资源key
	BaselineScanExpiredJobResourceCronConfigKey = "baseline_scan_expired_job_resource_cron"

	// BaselineScanExpiredJobRecordCronDefault 基线过期任务记录定时表达式
	// 3点45分执行
	BaselineScanExpiredJobRecordCronDefault = "45 3 * * *"
	// BaselineScanExpiredJobRecordCronConfigKey 基线过期任务记录key
	BaselineScanExpiredJobRecordCronConfigKey = "baseline_scan_expired_job_record_cron"

	// BaselineImportBuiltinBaselineCompletedDefault 导入内置基线完成状态
	BaselineImportBuiltinBaselineCompletedDefault = "false"

	// BaselineImportBuiltinBaselineCompletedTrue 导入内置基线完成状态true
	BaselineImportBuiltinBaselineCompletedTrue = "true"

	// BaselineImportBuiltinBaselineCompletedConfigKey 导入内置基线完成状态key
	BaselineImportBuiltinBaselineCompletedConfigKey = "baseline_import_builtin_baseline_completed"
	// BaselineImportBuiltinBaselineIncludeBizClusterConfigKey 导入内置基线包含下层集群key
	BaselineImportBuiltinBaselineIncludeBizClusterConfigKey = "import_builtin_baseline_include_biz_cluster"
	// BaselineImportBuiltinBaselineIncludeBizClusterConfigValueDefault 默认导入下层集群基线
	BaselineImportBuiltinBaselineIncludeBizClusterConfigValueDefault = true
)

const (
	// BaselineStrategyTaskChannel Redis channel for baseline task events
	BaselineStrategyTaskChannel = "baseline::strategy::task::events"

	// BaselineStrategyTaskEventAdd Task event types
	BaselineStrategyTaskEventAdd = "ADD"
	// BaselineStrategyTaskEventUpdate add
	BaselineStrategyTaskEventUpdate = "UPDATE"
	// BaselineStrategyTaskEventDelete delete
	BaselineStrategyTaskEventDelete = "DELETE"
)
