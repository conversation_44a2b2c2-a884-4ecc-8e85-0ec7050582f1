package constants

const (
	SchedulesCronType    = "protal.velero/cronType"
	StorageServerId      = "protal.velero/storageServerId"
	ExecuteType          = "protal.velero/executeType"
	RestoreTemplateId    = "protal.velero/restoreTemplateId"
	RestoreTemplateName  = "protal.velero/restoreTemplateName"
	ResourcesSize        = "protal.velero/resourcesSize"
	PersistentVolumeSize = "protal.velero/persistentVolumeSize"
	BackupNamespaces     = "protal.velero/backupNamespaces"
	RestoreNamespaces    = "protal.velero/restoreNamespaces"
	S3                   = "S3"
	RestoreType          = "protal.velero/restoreType"
	FromCluster          = "protal.velero/fromCluster"

	CronTypeMonth  = "month"
	CronTypeWeek   = "week"
	CronTypeDate   = "date"
	CronTypeHour   = "hour"
	CronTypeCustom = "custom"

	// TODO 从stc里获取
	VeleroNamespace = "velero-system"

	Cross                  = "cross"
	Original               = "original"
	RestoreModifyConfigKey = "restore-modify.yaml"

	PreflightRequestRedisResKey    = "PreflightRequestResult"
	PreflightScRedisResKey         = "PreflightScResult"
	PreflightNetworkRedisResKey    = "PreflightNetworkResult"
	PreflightRequestRedisReasonKey = "PreflightRequestReason"
	PreflightScRedisReasonKey      = "PreflightScReason"
	PreflightNetworkRedisReasonKey = "PreflightNetworkReason"
)

const (
	VeleroPVCNameAnnotation = "velero.io/pvc-name"
	Ipv4HdareaAnnotation    = "heimdallr.harmonycloud.cn/ipv4hdarea-eth0"
	Ipv6HdareaAnnotation    = "heimdallr.harmonycloud.cn/ipv6hdarea-eth0"
	DualHdareaAnnotation    = "heimdallr.harmonycloud.cn/dualhdarea-eth0"

	Ipv4HdpoolsAnnotation = "heimdallr.harmonycloud.cn/ipv4hdpools-eth0"
	Ipv6HdpoolsAnnotation = "heimdallr.harmonycloud.cn/ipv6hdpools-eth0"
	DualHdpoolsAnnotation = "heimdallr.harmonycloud.cn/dualhdpools-eth0"
)
