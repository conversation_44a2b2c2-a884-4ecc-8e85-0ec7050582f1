package constants

type AddonEnum struct {
	EnName         string `json:"enName,omitempty"`
	CnName         string `json:"cnName,omitempty"`
	PlatformEnName string `json:"platformEnName,omitempty"`
	PlatformChName string `json:"platformChName,omitempty"`
	Description    string `json:"description,omitempty"`
}

var (
	AddonEnumsMap                  = map[string]AddonEnum{}
	AddonEnums         []AddonEnum = make([]AddonEnum, 0, 15)
	ELK                            = AddonEnum{"elk", "日志插件", "ELK", "日志采集", "实现日志管理相关功能"}
	CORE_DNS                       = AddonEnum{"coredns", "coreDNS", "CoreDNS", "域名解析", "用于解决K8s的服务发现、域名解析问题"}
	CALICO                         = AddonEnum{"calico", "网络", "Calico", "Calico网络", "实现Calico网络CNI管理"}
	MONITORING                     = AddonEnum{"monitoring", "监控", "Monitoring", "监控告警", "实现监控管理相关功能"}
	PROBLEM_ISOLATION              = AddonEnum{"problem-isolation", "故障隔离", "Problem-Isolation", "故障隔离", "实现主机故障隔离相关功能"}
	RESOURCE_AGGREGATE             = AddonEnum{"resource-aggregate", "关联资源", "Resource-Aggregate", "资源关联控制器", "资源关联控制器 Resource-Aggregate 实现查询关联资源的功能，例如通过一个deployment来查询相关的pod，ReplicaSet，ResourceTracker，Application 等"}
	APP_DECOMPILE                  = AddonEnum{"app-decompile", "应用反编译", "App-Decompile", "反编译", "实现纳管组件的反编译功能"}
	NODE_POOL                      = AddonEnum{"node-pool", "资源池", "Node-Pool", "节点资源池", "实现集群节点资源池化精细化管理相关功能"}
	SISYPHUS                       = AddonEnum{"sisyphus", "西西弗斯", "Sisyphus", "西西弗斯", "用于实现集群升级、节点上下线等功能"}
	HEIMDALLR                      = AddonEnum{"heimdallr", "统一网络模型", "Heimdallr", "统一网络模型", "屏蔽多网络CNI差异实现管理的统一"}
	GPU                            = AddonEnum{"gpu", "GPU", "GPU", "GPU组件", "GPU节点信息"}
	HPA                            = AddonEnum{"hpa", "水平扩缩容", "Hpa", "水平扩缩容", "水平扩缩容"}
	APP_MODEL                      = AddonEnum{"app-model", "应用模型", "App-Model", "应用模型", "实现应用模型管理相关功能"}
	ACL                            = AddonEnum{"acl", "网络隔离", "ACL", "网络隔离", "网络隔离"}
	KUBEOVN                        = AddonEnum{"ovn", "虚拟机网络", "ovn", "虚拟机网络", "虚拟机网络"}
	BASELINE_CHECKER               = AddonEnum{"baseline-checker", "基线检查", "Baseline-Checker", "基线检查", "提供基线检查功能"}
)

func init() {
	AddonEnums = append(
		AddonEnums,
		ELK,
		CORE_DNS,
		CALICO,
		MONITORING,
		PROBLEM_ISOLATION,
		RESOURCE_AGGREGATE,
		APP_DECOMPILE,
		NODE_POOL,
		SISYPHUS,
		HEIMDALLR,
		GPU,
		HPA,
		APP_MODEL,
		ACL,
		KUBEOVN,
		BASELINE_CHECKER,
	)
	for _, v := range AddonEnums {
		AddonEnumsMap[v.EnName] = v
	}

	BaseAddonEnums = append(BaseAddonEnums, BASE_COMPONENT_API_SERVER, BASE_COMPONENT_API_ETCD, BASE_COMPONENT_CONTROLLER_MANAGE, BASE_COMPONENT_SCHEDULER)
	for _, v := range BaseAddonEnums {
		BaseAddonEnumsMap[v.Name] = v
	}
}

type AddonStatusEnum int

// addons的一些状态
//type AddonStatusEnums struct {
//	ERROR     AddonStatusEnum // 错误
//	UN_SWITCH AddonStatusEnum // 未启用
//	ON_SWITCH AddonStatusEnum // 启用
//	RUNNING   AddonStatusEnum // 运行
//}

const (
	ERROR     AddonStatusEnum = iota // 错误
	UN_SWITCH                        // 未启用
	ON_SWITCH                        // 启用中
	RUNNING                          // 运行中
)

func ConverAddonsStatus2String(status AddonStatusEnum) string {
	switch status {
	case ERROR:
		return "ERROR"
	case UN_SWITCH:
		return "UN_SWITCH"
	case ON_SWITCH:
		return "ON_SWITCH"
	case RUNNING:
		return "RUNNING"
	default:
		return "UNKNOWN"
	}
}

const (
	ADDON_HEALTH   = "False"
	ADDON_UNHEALTH = "True" // true表示健康健康检查出问题
)

type AddonMenuEnum int

const (
	NORMAL   AddonMenuEnum = iota // 正常
	UNKNOWN                       // 未知
	ABNORMAL                      // 异常
)

type BaseAddonEnum struct {
	Name            string `json:"name,omitempty"`
	HealthCheckName string `json:"healthCheckName,omitempty"`
	PlatformEnName  string `json:"platformEnName,omitempty"`
	PlatformChName  string `json:"platformChName,omitempty"`
	Description     string `json:"description,omitempty"`
}

var (
	BaseAddonEnumsMap = make(map[string]BaseAddonEnum)
	// BaseAddonEnums 在当前init中初始化
	BaseAddonEnums                   = make([]BaseAddonEnum, 0, 4)
	BASE_COMPONENT_API_SERVER        = BaseAddonEnum{"apiserver", "ApiserverUnhealthy", "API-Server", "K8S接口服务器", "集群数据交互中枢，负责各功能模块之间的通信"}
	BASE_COMPONENT_API_ETCD          = BaseAddonEnum{"etcd", "ETCDUnhealthy", "ETCD", "K8S元数据库", "负责集群全生命周期数据存储"}
	BASE_COMPONENT_CONTROLLER_MANAGE = BaseAddonEnum{"controller-manager", "ControllerManagerUnhealthy", "Controller-Manager", "K8S控制管理器", "集群控制中心，负责集群资源对象的统一管理"}
	BASE_COMPONENT_SCHEDULER         = BaseAddonEnum{"scheduler", "SchedulerUnhealthy", "Scheduler", "K8S调度器", "负责调度Pod到合适的主机节点上去"}
)
