package constants

// 自有集群
const (
	ClusterLabelKeyOfSelfCluster   = "unified-platform.harmonycloud.cn/self-cluster"
	ClusterLabelValueOfSelfCluster = "true"
)

// labels and annotation
const (
	// ClusterLabelsKeyBaseline 集群基线
	ClusterLabelsKeyBaseline = "caas-infra-baseline"
	// UpgradeClusterNameLabelKey 集群升级Installer名称label key
	UpgradeClusterNameLabelKey = "upgrade-cluster.form.portal/cluster-name"
	// UpgradeClusterInstallerPrefix 集群升级Installer prefix
	UpgradeClusterInstallerPrefix = "upgrade-cluster"
	// InstallerAnnotationUpgradeClusterNodeUpgradeType Installer存放集群升级过程中节点升级方式
	InstallerAnnotationUpgradeClusterNodeUpgradeType = "upgrade-cluster.form.portal/node-upgrade-type"
)

// message
const (
	// ClusterUpgradeInfoMessageNotSupportManager 集群列表升级信息：暂不支持线上升级管理集群
	ClusterUpgradeInfoMessageNotSupportManager = "暂不支持线上升级管理集群"
	// ClusterUpgradeInfoMessageNotSupportUnknown 集群列表升级信息：暂不支持线上升级非平台创建的集群
	ClusterUpgradeInfoMessageNotSupportUnknown = "暂不支持线上升级非平台创建的集群"
	PackageMessageSisyphusLackSolution         = "缺少西西弗西解决方案,请联系管理员上传升级包"
)

// excel template
const (
	// ClusterUpgradeExcelTemplateCode 集群升级Excel模板code
	ClusterUpgradeExcelTemplateCode = "cluster_upgrade_nodes"
	// ContextKeyClusterUpgradeNodes Context存放节点列表变量key
	ContextKeyClusterUpgradeNodes = "nodeMap"
)

// k8s constants
const (
	// NamespaceSystem kube-system
	NamespaceSystem = "kube-system"
	// NamespaceCaasSystem caas-system
	NamespaceCaasSystem = "caas-system"
	// NamespaceDefault default
	NamespaceDefault = "default"
	// DefaultNginxIngressController 负载均衡名称
	DefaultNginxIngressController = "nginx-ingress-controller"
	// DefaultNginxIngressControllerContainer 负载均衡容器名称
	DefaultNginxIngressControllerContainer = "nginx-ingress"
	// NginxIngressControllerArgsPublishStatusAddress 负载均衡参数地址
	NginxIngressControllerArgsPublishStatusAddress = "--publish-status-address="
	// NodeRoleMaster 节点角色master
	NodeRoleMaster = "master"
	// ClusterUpgradeVersionMapping 西西弗西解决方案配置文件名称
	ClusterUpgradeVersionMapping = "cluster-upgrade-version-mapping"
)

// upgrade installer task
const (
	InstallerTaskNameInitialNode        = "initial-upgrade-node-task"
	InstallerTaskDescriptionInitialNode = "集群升级节点"

	InstallerTaskNameSisyphusSolutionComponent        = "sisyphus-solution-component-task"
	InstallerTaskDescriptionSisyphusSolutionComponent = "基础设施组件升级solution"

	InstallerTaskNameSisyphusSolution122k8s        = "sisyphus-solution-1.22k8s-task"
	InstallerTaskDescriptionSisyphusSolution122k8s = "1.22k8s-solution"

	InstallerTaskNameSisyphusSolution123k8s        = "sisyphus-solution-1.23k8s-task"
	InstallerTaskDescriptionSisyphusSolution123k8s = "1.23k8s-solution"

	InstallerTaskNameSisyphusSolutionComponentFrontExec        = "sisyphus-solution-exec-component-front-task"
	InstallerTaskDescriptionSisyphusSolutionComponentFrontExec = "执行基础设施组件升级前置solution"

	InstallerTaskNameSisyphusSolution122k8sExec        = "sisyphus-solution-exec-1.22k8s-task"
	InstallerTaskDescriptionSisyphusSolution122k8sExec = "执行1.22k8s solution"

	InstallerTaskNameSisyphusSolution123k8sExec        = "sisyphus-solution-exec-1.23k8s-task"
	InstallerTaskDescriptionSisyphusSolution123k8sExec = "执行1.23k8s solution"

	InstallerTaskNameSisyphusSolutionComponentRearExec        = "sisyphus-solution-exec-component-rear-task"
	InstallerTaskDescriptionSisyphusSolutionComponentRearExec = "执行基础设施组件升级后置solution"

	SolutionCodeComponents = "component"
	SolutionCodeK8s122     = "k8s1.22"
	SolutionCodeK8s123     = "k8s1.23"

	SolutionCodeComponentsFrontExecTaskCode = "component-front"
	SolutionCodeK8s122ExecTaskCode          = "k8s1.22"
	SolutionCodeK8s123ExecTaskCode          = "k8s1.23"
	SolutionCodeComponentsRearExecTaskCode  = "component-rear"
)
