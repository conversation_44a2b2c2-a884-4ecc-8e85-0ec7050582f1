package controllers

//
//import (
//	"context"
//	"encoding/json"
//	"reflect"
//	"strconv"
//	"time"
//
//	"github.com/go-logr/logr"
//	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
//	handlers "harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/baseline"
//	models "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/baseline"
//	corev1 "k8s.io/api/core/v1"
//	apierrors "k8s.io/apimachinery/pkg/api/errors"
//	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
//	"k8s.io/client-go/kubernetes"
//	ctrl "sigs.k8s.io/controller-runtime"
//	"sigs.k8s.io/controller-runtime/pkg/client"
//	"sigs.k8s.io/controller-runtime/pkg/controller/controllerutil"
//	"sigs.k8s.io/controller-runtime/pkg/predicate"
//	"sigs.k8s.io/controller-runtime/pkg/reconcile"
//)
//
//var _ reconcile.Reconciler = &BaselineConfigReconciler{}
//
//const baselineFinalizer = "baseline.unified-platform.harmonycloud.cn/finalizer"
//
//func NewBaselineConfigReconciler(client client.Client, kubeClient kubernetes.Interface, log logr.Logger) *BaselineConfigReconciler {
//	return &BaselineConfigReconciler{
//		Client:     client,
//		KubeClient: kubeClient,
//		jobHandler: handlers.GetDefaultRecurringHandler(),
//		log:        log.WithName("baseline-config-controller"),
//	}
//}
//
//type BaselineConfigReconciler struct {
//	Client     client.Client
//	KubeClient kubernetes.Interface
//	log        logr.Logger
//	jobHandler handlers.RecurringJobInterface
//}
//
//// reconcileFinalizer
//func (r *BaselineConfigReconciler) reconcileFinalizer(ctx context.Context, baselineConfig *corev1.ConfigMap) error {
//	if !controllerutil.ContainsFinalizer(baselineConfig, baselineFinalizer) {
//		controllerutil.AddFinalizer(baselineConfig, baselineFinalizer)
//		return r.Client.Update(ctx, baselineConfig)
//	}
//	return nil
//}
//
//// Reconcile Reconcile
//func (r *BaselineConfigReconciler) Reconcile(ctx context.Context, req reconcile.Request) (reconcile.Result, error) {
//	baselineConfig := &corev1.ConfigMap{}
//	if err := r.Client.Get(ctx, req.NamespacedName, baselineConfig); err != nil {
//		// 如果 Baseline 资源已被删除，移除定时任务
//		if apierrors.IsNotFound(err) {
//			return reconcile.Result{}, nil
//		}
//		return reconcile.Result{}, err
//	}
//	if !baselineConfig.DeletionTimestamp.IsZero() {
//		return reconcile.Result{}, r.reconcileDelete(ctx, baselineConfig)
//	}
//
//	if err := r.reconcileFinalizer(ctx, baselineConfig); err != nil {
//		return reconcile.Result{}, err
//	}
//	if err := r.reconcile(ctx, baselineConfig); err != nil {
//		return reconcile.Result{}, err
//	}
//
//	return reconcile.Result{RequeueAfter: 5 * time.Minute}, nil
//}
//
//func (r *BaselineConfigReconciler) reconcile(ctx context.Context, baselineConfig *corev1.ConfigMap) error {
//	executionType := r.getExecutionTypeFromObject(baselineConfig)
//	if executionType != string(models.ExecutionTypeRecurring) &&
//		executionType != string(models.ExecutionTypeScheduled) {
//		return nil
//	}
//	strategyID := r.strategyIdFromObject(baselineConfig)
//
//	getRecurringJobResponse, err := r.jobHandler.Get(ctx, &models.GetRecurringJobRequest{
//		StrategyID: strategyID,
//	})
//	if err != nil {
//		return err
//	}
//	if !getRecurringJobResponse.Existed {
//		if _, err := r.jobHandler.Add(ctx, &models.AddRecurringJobRequest{
//			StrategyID: strategyID,
//		}); err != nil {
//			return err
//		}
//		return nil
//	}
//	data := map[string]string{}
//	if baselineConfig.Data != nil {
//		data = baselineConfig.Data
//	}
//	configRawData := data["config"]
//	executionConfig := &models.ExecutionConfig{}
//	if err := json.Unmarshal([]byte(configRawData), executionConfig); err != nil {
//		r.log.Error(err, "Unmarshal execution config failed", "name", baselineConfig.Name)
//	}
//	if !reflect.DeepEqual(*executionConfig, getRecurringJobResponse.ExecutionConfig) {
//		if _, err := r.jobHandler.Delete(ctx, &models.DeleteRecurringJobRequest{StrategyID: strategyID}); err != nil {
//			return err
//		}
//		if _, err := r.jobHandler.Add(ctx, &models.AddRecurringJobRequest{
//			StrategyID: strategyID,
//		}); err != nil {
//			return err
//		}
//		return nil
//	}
//	return nil
//
//}
//
//// reconcileDelete reconcile delete
//func (r *BaselineConfigReconciler) reconcileDelete(ctx context.Context, baselineConfig *corev1.ConfigMap) error {
//	if controllerutil.ContainsFinalizer(baselineConfig, baselineFinalizer) {
//		strategyID := r.strategyIdFromObject(baselineConfig)
//
//		getRecurringJobResponse, err := r.jobHandler.Get(ctx, &models.GetRecurringJobRequest{
//			StrategyID: strategyID,
//		})
//		if err != nil {
//			return err
//		}
//
//		if getRecurringJobResponse.Existed {
//			if _, err := r.jobHandler.Delete(ctx, &models.DeleteRecurringJobRequest{
//				StrategyID: strategyID,
//			}); err != nil {
//				return err
//			}
//		}
//		controllerutil.RemoveFinalizer(baselineConfig, baselineFinalizer)
//		return r.Client.Update(context.Background(), baselineConfig)
//	}
//	return nil
//}
//
//func (r *BaselineConfigReconciler) strategyIdFromObject(clientObject client.Object) int64 {
//	if clientObject.GetLabels() != nil {
//		if id, err := strconv.ParseInt(clientObject.GetLabels()[constants.BaselineStrategyIDLabelKey],
//			10, 64); err != nil {
//			r.log.WithValues("baseline", client.ObjectKeyFromObject(clientObject)).
//				Error(err, "failed to parse strategy id")
//			return 0
//		} else {
//			return id
//		}
//	}
//	return 0
//}
//
//func (r *BaselineConfigReconciler) getExecutionTypeFromObject(clientObject client.Object) string {
//	if clientObject.GetLabels() != nil {
//		return clientObject.GetLabels()[constants.BaselineStrategyExecutionTypeLabelKey]
//	}
//	return ""
//
//}
//
//func (r *BaselineConfigReconciler) SetupWithManager(mgr ctrl.Manager) error {
//	// _, err := r.KubeClient.Discovery().ServerResourcesForGroupVersion(v1.GroupVersion.String())
//	// if err != nil {
//	// 	r.log.Error(fmt.Errorf("setup baeline controller failed"), "baseline crd not found")
//	// 	return nil
//	// }
//	labelSelectorPredicate, err := predicate.LabelSelectorPredicate(
//		metav1.LabelSelector{
//			MatchExpressions: []metav1.LabelSelectorRequirement{
//				{
//					Key:      constants.BaselineStrategyExecutionDaemonLabelKey,
//					Operator: metav1.LabelSelectorOpIn,
//					Values:   []string{constants.BaselineStrategyExecutionDaemonLabelValueTrue},
//				},
//				{
//					Key:      constants.BaselineStrategyExecutionTypeLabelKey,
//					Operator: metav1.LabelSelectorOpExists,
//				},
//				{
//					Key:      constants.BaselineStrategyExecutionRecurringTypeLabelKey,
//					Operator: metav1.LabelSelectorOpExists,
//				},
//				{
//					Key:      constants.BaselineStrategyIDLabelKey,
//					Operator: metav1.LabelSelectorOpExists,
//				},
//			},
//		})
//	if err != nil {
//		return err
//	}
//	return ctrl.NewControllerManagedBy(mgr).WithEventFilter(labelSelectorPredicate).
//		For(&corev1.ConfigMap{}).
//		Complete(r)
//}
