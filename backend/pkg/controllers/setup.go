package controllers

import (
	ctrl "sigs.k8s.io/controller-runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

type Setup interface {
	SetupWithManager(mgr ctrl.Manager) error
}

// SetupControllers ...
func SetupControllers(client client.Client, mgr ctrl.Manager) error {
	var setups = []Setup{}
	//if baseline_helper.IsFeatureBaselineEnabled() {
	//	setups = append(setups, NewBaselineConfigReconciler(client, clientmgr.GetLocalCluster().GetClient().GetKubeClient(), mgr.GetLogger()))
	//}
	for _, setup := range setups {
		if err := setup.SetupWithManager(mgr); err != nil {
			return err
		}
	}
	return nil

}
