package errors

import (
	"strings"
)

func Join(errs ...error) error {
	n := 0
	for _, err := range errs {
		if err != nil {
			n++
		}
	}
	if n == 0 {
		return nil
	}
	e := &joinError{
		errs: make([]error, 0, n),
	}
	for _, err := range errs {
		if err != nil {
			e.errs = append(e.errs, err)
		}
	}
	return e
}

type joinError struct {
	errs []error
}

func (e *joinError) Error() string {
	if len(e.errs) == 1 {
		return e.errs[0].Error()
	}

	var builder strings.Builder
	builder.WriteString(e.errs[0].Error()) // 写入第一个错误

	for _, err := range e.errs[1:] {
		builder.WriteString("\n")
		builder.WriteString(err.Error())
	}

	// 返回拼接后的错误字符串
	return builder.String()
}

func (e *joinError) Unwrap() []error {
	return e.errs
}
