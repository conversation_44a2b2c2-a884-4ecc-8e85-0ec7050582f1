package errors

import (
	"fmt"
	"reflect"
	"strconv"
	"testing"

	"github.com/spf13/viper"
	"k8s.io/apimachinery/pkg/util/sets"
)

func Test_Var(t *testing.T) {
	t.Run("Test_Var", func(t *testing.T) {
		value := reflect.ValueOf(Var)

		// 检查ErrorCode 是否存在0值
		for i := 0; i < value.NumField(); i++ {
			item := value.Field(i).Interface()
			errorCode := item.(ErrorCode)
			if reflect.DeepEqual(errorCode, ErrorCode{}) {
				t.Errorf("empty ErrorCode %s", value.Type().Field(i).Name)
				return
			}
		}

		// 获取全部ErrorCode
		var errorCodeList []ErrorCode
		for i := 0; i < value.NumField(); i++ {
			item := value.Field(i).Interface()
			errorCodeList = append(errorCodeList, item.(ErrorCode))
		}

		// 检查Error code 是否重复
		errorIntegerCodeSet := sets.New[int]()
		for _, ec := range errorCodeList {
			if errorIntegerCodeSet.Has(ec.ResponseCode) {
				t.Errorf("repeated ResponseCode %d", ec.ResponseCode)
				return
			} else {
				errorIntegerCodeSet.Insert(ec.ResponseCode)
			}
		}
		// 检查code是否有序
		for index, ec := range errorCodeList {
			if index == 0 {
				continue
			}
			if ec.ResponseCode < errorCodeList[index-1].ResponseCode {
				t.Errorf("code %d is not sortd last is %d", ec.ResponseCode, errorCodeList[index-1].ResponseCode)
				return
			}
		}

		// 检查繁体中文翻译
		checkTranslate(errorCodeList, "../../../error-num-language-pkg/zh-HK.properties", t)

		// 检查英语翻译
		checkTranslate(errorCodeList, "../../../error-num-language-pkg/en-US.properties", t)
	})

}

func checkTranslate(errorCodeList []ErrorCode, translateFilePath string, t *testing.T) {
	fmt.Println("----------", "start analyze file", translateFilePath)
	v := viper.New()
	v.SetConfigType("properties")
	v.SetConfigFile(translateFilePath)
	if err := v.ReadInConfig(); err != nil {
		t.Error(err)
		return
	}
	allKey := sets.New[string](v.AllKeys()...)
	for _, ec := range errorCodeList {
		code := ec.ResponseCode
		if !allKey.Has(strconv.Itoa(code)) {
			t.Errorf("code %d is not exist,str is %s", code, ec.Message)
		}
	}
}
