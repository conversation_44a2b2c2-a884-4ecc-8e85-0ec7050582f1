package errors

import (
	"context"
	goerrors "errors"
	"fmt"
	"strings"

	"github.com/go-playground/validator/v10"
	"github.com/pkg/errors"
	languageerrors "harmonycloud.cn/unifiedportal/translate-sdk-golang/errors"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
)

type Error struct {
	ErrorCode
	Detail string
}

func (e Error) Error() string {
	var sb strings.Builder
	if e.ResponseCode != 0 {
		sb.WriteString(fmt.Sprintf("code: %v", e.ResponseCode))
	}
	if e.Message != "" {
		sb.WriteString(fmt.Sprintf(" message: %v", e.Message))
	}
	if e.Detail != "" {
		sb.WriteString(fmt.Sprintf(" detail: %v", e.Detail))
	}
	return sb.String()
}

func NewFromCode(code ErrorCode) Error {
	return Error{
		ErrorCode: code,
	}
}

// NewFromCodeFormatMessageWithDetail creates a new Error with the specified ErrorCode,
// formats the message using the provided arguments, and sets the given detail string.
func NewFromCodeFormatMessageWithDetail(code ErrorCode, detail string, args ...any) Error {
	err := NewFromCodeFormatMessage(code, args...)
	err.Detail = detail
	return err
}

// NewFromCodeFormatMessage creates a new Error with the specified ErrorCode,
// formats the message using the provided arguments.
func NewFromCodeFormatMessage(code ErrorCode, args ...any) Error {
	err := Error{
		ErrorCode: code,
	}
	formatMessage := fmt.Sprintf(err.Message, args...)
	err.Message = formatMessage
	return err
}

func NewFromCodeWithMessage(code ErrorCode, detail string) Error {
	return Error{
		ErrorCode: code,
		Detail:    detail,
	}
}

func NewFromError(ctx context.Context, e error) Error {
	errorHandleFunc := func(e error) Error {
		switch {
		case errors.As(e, &Error{}):
			var ne Error
			errors.As(e, &ne)
			return ne
		case errors.As(e, &validator.ValidationErrors{}):
			var ves validator.ValidationErrors
			errors.As(e, &ves)
			sb := strings.Builder{}
			for _, ve := range ves {
				property := ve.Namespace()
				tag := ve.Tag()
				sb.WriteString(fmt.Sprintf("field %s is illegal,not meeting the conditions '%s'", property, tag))
				if ve.Param() != "" {
					sb.WriteString(fmt.Sprintf(" with '%s'", ve.Param()))
				}
			}
			return Error{
				ErrorCode: Var.ParamError,
				Detail:    sb.String(),
			}
		default:
			var bizErr Error
			bizErr.Detail = e.Error()
			bizErr.ErrorCode = Var.UnKnow
			if statusErr, ok := e.(apierrors.APIStatus); ok || goerrors.As(e, &statusErr) {
				bizErr = HandleK8sError(e)
				bizErr.HTTPCode = int(statusErr.Status().Code)
			}
			return bizErr
		}
	}
	err := errorHandleFunc(e)
	AsK8sError(err, &Error{})
	errorCode := err.ResponseCode
	message := err.Message
	messageTranslate := languageerrors.TranslateByIntErrorCodeWithDefault(ctx, errorCode, message)
	err.Message = messageTranslate
	return err
}

type ErrorCode struct {
	HTTPCode     int
	ResponseCode int
	Message      string
}

func newErrorCode(httpCode, responseCode int, message string) ErrorCode {
	return ErrorCode{
		HTTPCode:     httpCode,
		ResponseCode: responseCode,
		Message:      message,
	}
}

func Is(err error, code ErrorCode) bool {
	switch {
	case errors.As(err, &Error{}):
		var errError Error
		errors.As(err, &errError)
		return errError.ErrorCode == code
	default:
		return false
	}
}

func AsK8sError(e error, t *Error) bool {
	// 检查常见的 Kubernetes 错误

	setFn := func(bizErr Error, t any) {
		if err, ok := t.(*Error); ok {
			err.ErrorCode = bizErr.ErrorCode
			err.Detail = bizErr.Detail
		}
	}
	switch {

	case apierrors.IsAlreadyExists(e):
		setFn(NewFromCodeWithMessage(Var.K8sAlreadyExists, e.Error()), t)
	case apierrors.IsNotFound(e):
		setFn(NewFromCodeWithMessage(Var.K8sNotFound, e.Error()), t)
	case apierrors.IsUnauthorized(e):
		setFn(NewFromCodeWithMessage(Var.K8sUnauthorized, e.Error()), t)
	case apierrors.IsForbidden(e):
		setFn(NewFromCodeWithMessage(Var.K8sForbidden, e.Error()), t)
	case apierrors.IsConflict(e):
		setFn(NewFromCodeWithMessage(Var.K8sConflict, e.Error()), t)
	case apierrors.IsGone(e):
		setFn(NewFromCodeWithMessage(Var.K8sGone, e.Error()), t)
	case apierrors.IsInvalid(e):
		setFn(NewFromCodeWithMessage(Var.K8sInvalid, e.Error()), t)
	case apierrors.IsTimeout(e):
		setFn(NewFromCodeWithMessage(Var.K8sTimeout, e.Error()), t)
	case apierrors.IsServerTimeout(e):
		setFn(NewFromCodeWithMessage(Var.K8sServerTimeout, e.Error()), t)
	case apierrors.IsTooManyRequests(e):
		setFn(NewFromCodeWithMessage(Var.K8sTooManyRequests, e.Error()), t)
	case apierrors.IsBadRequest(e):
		setFn(NewFromCodeWithMessage(Var.K8sBadRequest, e.Error()), t)
	case apierrors.IsMethodNotSupported(e):
		setFn(NewFromCodeWithMessage(Var.K8sMethodNotAllowed, e.Error()), t)
	case apierrors.IsNotAcceptable(e):
		setFn(NewFromCodeWithMessage(Var.K8sNotAcceptable, e.Error()), t)
	case apierrors.IsRequestEntityTooLargeError(e):
		setFn(NewFromCodeWithMessage(Var.K8sRequestEntityTooLarge, e.Error()), t)
	case apierrors.IsUnsupportedMediaType(e):
		setFn(NewFromCodeWithMessage(Var.K8sUnsupportedMediaType, e.Error()), t)
	case apierrors.IsServiceUnavailable(e):
		setFn(NewFromCodeWithMessage(Var.K8sServiceUnavailable, e.Error()), t)
	case apierrors.IsInternalError(e):
		setFn(NewFromCodeWithMessage(Var.K8sInternalError, e.Error()), t)
	case apierrors.IsResourceExpired(e):
		setFn(NewFromCodeWithMessage(Var.K8sExpired, e.Error()), t)
	case errors.As(e, &Error{}):
		// 捕获自定义错误
		var k8sErr Error
		errors.As(e, &k8sErr)
		if k8sErr.ResponseCode >= 8000 && k8sErr.ResponseCode <= 8999 {
			setFn(k8sErr, t)
			return true
		}
		return false
	default:
		// 未知错误直接返回
		return false
	}
	return true
}

func HandleK8sError(e error) Error {
	// 检查常见的 Kubernetes 错误
	var k8sErr Error
	if AsK8sError(e, &k8sErr) {
		return k8sErr
	}
	// 未知错误返回通用错误
	return NewFromCodeWithMessage(Var.K8sError, e.Error())
}
