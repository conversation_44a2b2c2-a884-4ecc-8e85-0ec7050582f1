package errors

import (
	"errors"
	"testing"
)

func TestJoin(t *testing.T) {
	type args struct {
		errs []error
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
		wantMsg string
	}{
		{
			name: "No errors",
			args: args{
				errs: []error{},
			},
			wantErr: false,
			wantMsg: "",
		},
		{
			name: "Single error",
			args: args{
				errs: []error{errors.New("error 1")},
			},
			wantErr: true,
			wantMsg: "error 1",
		},
		{
			name: "Multiple errors",
			args: args{
				errs: []error{errors.New("error 1"), errors.New("error 2")},
			},
			wantErr: true,
			wantMsg: "error 1\nerror 2",
		},
		{
			name: "Nil error in multiple",
			args: args{
				errs: []error{errors.New("error 1"), nil, errors.New("error 2")},
			},
			wantErr: true,
			wantMsg: "error 1\nerror 2",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := Join(tt.args.errs...)
			if (err != nil) != tt.wantErr {
				t.Errorf("Join() error = %v, wantErr %v", err, tt.wantErr)
			}

			// 检查返回的错误消息
			if err != nil && err.Error() != tt.wantMsg {
				t.Errorf("Join() error message = %v, wantMsg %v", err.Error(), tt.wantMsg)
			}
		})
	}
}
