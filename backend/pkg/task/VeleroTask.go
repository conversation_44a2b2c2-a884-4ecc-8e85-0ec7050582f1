package task

import (
	"context"
	"fmt"

	"go.uber.org/zap"
	"harmonycloud.cn/unifiedportal/olympus-cluster/pkg/client"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/storageserver"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/velero"
)

type veleroTask struct {
	storageServersInterface       storageserver.StorageServersInterface
	veleroStorageServersInterface storageserver.VeleroStorageServersInterface
}

func NewVeleroTask() Executor {
	return &veleroTask{
		storageServersInterface:       storageserver.NewStorageServerInterface(),
		veleroStorageServersInterface: storageserver.NewVeleroStorageServerInterface(),
	}
}

func (t *veleroTask) execute() {
	ctx := context.Background()

	// 获取存储服务列表并转为map
	storageServersList, listStorageServersError := t.storageServersInterface.ListStorageServers(ctx, "", "", "")
	if listStorageServersError != nil {
		logger.GetLogger().Error("ListStorageServers error. error: {err}", zap.Any("{err}", listStorageServersError))
	}
	storageServersMap := make(map[string]velero.StorageServers)
	for _, storageServer := range storageServersList {
		storageServersMap[storageServer.StorageServersId] = storageServer
	}

	// 获取在线集群
	clusterList := client.ListOnlineClusters()

	for _, cluster := range clusterList {
		// 获取集群上所有bsl
		bslList, listBackupStorageLocationError := t.veleroStorageServersInterface.ListBackupStorageLocation(ctx, cluster.GetName())
		if listBackupStorageLocationError != nil {
			logger.GetLogger().Error("ListBackupStorageLocation error. error: {err}", zap.Any("{err}", listBackupStorageLocationError))
		}

		// 获取集群上所有sechdules
		schedulesList, listSchedulesError := t.veleroStorageServersInterface.ListSchedules(ctx, cluster.GetName())
		if listSchedulesError != nil {
			logger.GetLogger().Error("ListSchedules error. error: {err}", zap.Any("{err}", listSchedulesError))
		}

		// 遍历bsl
		for _, bsl := range bslList {
			// 获取存储服务id
			storageServerId, exist := bsl.Annotations[constants.StorageServerId]

			// id存在则为平台创建
			if exist {
				// 判断数据库内是否还存在该数据
				_, ok := storageServersMap[storageServerId]

				// 不存在则删除
				if !ok {
					// 判断是否存在schedules还在使用
					isUsed := false

					for _, schedules := range schedulesList {
						if schedules.BackupStorageLocationName == bsl.Name {
							isUsed = true
							break
						}
					}

					// 没有使用的
					if !isUsed {
						// 删除
						if err := t.veleroStorageServersInterface.DeleteBackupStorageLocation(ctx, cluster.GetName(), bsl.Name); err != nil {
							logger.GetLogger().Error("DeleteBackupStorageLocation error. error: {err}", zap.Any("{err}", err))
						}
					} else {
						// 还有使用的打日志
						logger.GetLogger().Info(fmt.Sprintf("BackupStorageLocation %v is in use and will not be deleted", bsl.Name))
					}
				}
			}
		}
	}
}
