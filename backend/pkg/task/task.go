package task

import (
	"github.com/robfig/cron/v3"
	"go.uber.org/zap"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
)

type cronTask struct {
	cronString string
	Executor
}

type Executor interface {
	execute()
}

func initCronTaskList() []cronTask {
	return []cronTask{
		{"0 1 * * *", NewVeleroTask()},
	}
}

func AddTask() {
	c := cron.New()

	for _, task := range initCronTaskList() {
		entryID, err := c.AddFunc(task.cronString, task.Executor.execute)
		if err != nil {
			logger.GetLogger().Error("add cron(entryID) executor error. error: {err}", zap.Any("entryID", entryID), zap.Any("{err}", err))
		}
	}

	c.Start()
}
