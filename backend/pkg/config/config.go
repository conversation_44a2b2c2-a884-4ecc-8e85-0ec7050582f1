package config

import (
	"strings"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
)

type Flag struct {
	Name        string // flag name
	Value       string // flag value
	Description string // flag Description
}

func NewFlag(name, value, description string) *Flag {
	return &Flag{
		Name:        name,
		Value:       value,
		Description: description,
	}
}

var Flags = []*Flag{
	CloudServiceName,
	//KubeConfig,
	Port,
	EnableSwagger,
	JwtSecret,
	DatabaseDriver,
	AMPDatabaseHost,
	AMPDatabasePort,
	AMPDatabaseUsername,
	AMPDatabasePassword,
	AMPDatabaseScheme,
	AMPDatabaseArgs,
	CaasDatabaseHost,
	CaasDatabasePort,
	CaasDatabaseUsername,
	CaasDatabasePassword,
	CaasDatabaseScheme,
	CaasDatabaseArgs,
	RedisAddress,
	RedisPassword,
	RedisSelectDB,
	TranslateLanguageCookieKey,
	TranslateDefaultLanguageCode,
	TranslateErrorCodeFileDir,
	DevopsAmpURL,
	DisasterRecoveryEnabled,
	StellariesAddress,
	StellariesPort,
	StandbyStellariesAddress,
	StandbyStellariesPort,
	SisyphusAddress,
	SisyphusUsername,
	SisyphusPassword,
	ForceUseTopSisyphus,
	DefaultRegistryProtocol,
	DefaultRegistryHost,
	DefaultRegistryPort,
	ClusterCreateStepConfigPath,
	TopClusterIngressAddress,
	FilePath,
	PreflightExpiredTime,
	OlympusCoreAddress,
	MinioEndpoint,
	MinioAccessKey,
	MinioSecretKey,
	MinioRegion,
	MinioURLPrefix,
	MinioInitBuckets,
	FeatureBaselineEnabled,
	BaselineCheckerServerURL,
	BaselineResourceRemainTime,
	BaselineStandardJobTimeout,
	BaselineStrategyJobTimeout,
	BaselineStrategyJobMaxConcurrency,
	BaselineStandardJobMaxConcurrency,
	NodeConsoleImage,
}

var (
	CloudServiceName = NewFlag("cloud-service-name", "unified-platform", "cloud service name")
	// KubeConfig kubeconfig 不要再改这个值了   改了记得改回去 ｜ 使用golang提供的参数配置器
	KubeConfig = NewFlag("kubeconfig", "", "hub cluster kube-config path")
	// Port server port
	Port = NewFlag("port", ":8080", "http server port")
	// EnableSwagger swagger enabled
	EnableSwagger = NewFlag("enable-swagger", "false", "wether swagger eanbled")
	// JwtSecret jwt token verify secret
	JwtSecret = NewFlag("jwt-secret", "4ab4a93a857043f9b58c2abfb8442e74", "JWT Verify Secret")
	// DatabaseDriver database driver
	DatabaseDriver = NewFlag("use-database-driver", "mysql", "what database driver you want to use,now support [mysql]")

	//

	// AMPDatabaseHost
	// AMPDatabasePort
	// AMPDatabaseUsername
	// AMPDatabasePassword
	// AMPDatabaseScheme
	// AMPDatabaseArgs
	// amp database
	AMPDatabaseHost     = NewFlag("amp-database-host", "caas-mysql", "amp database connect host")
	AMPDatabasePort     = NewFlag("amp-database-port", "3306", "amp database connect port")
	AMPDatabaseUsername = NewFlag("amp-database-username", "root", "amp database connect username")
	AMPDatabasePassword = NewFlag("amp-database-password", "Hc@Cloud01", "amp database connect username")
	AMPDatabaseScheme   = NewFlag("amp-database-scheme", "app_management", "amp which database you select")
	AMPDatabaseArgs     = NewFlag("amp-database-args", "charset=utf8mb4&loc=Local&parseTime=true", "amp database connect args")

	// CaasDatabaseHost
	// CaasDatabasePort
	// CaasDatabaseUsername
	// CaasDatabasePassword
	// CaasDatabaseScheme
	// CaasDatabaseArgs
	// amp database
	CaasDatabaseHost     = NewFlag("caas-database-host", "caas-mysql", "amp database connect host")
	CaasDatabasePort     = NewFlag("caas-database-port", "3306", "amp database connect port")
	CaasDatabaseUsername = NewFlag("caas-database-username", "root", "amp database connect username")
	CaasDatabasePassword = NewFlag("caas-database-password", "Hc@Cloud01", "amp database connect username")
	CaasDatabaseScheme   = NewFlag("caas-database-scheme", "caas", "amp which database you select")
	CaasDatabaseArgs     = NewFlag("caas-database-args", "charset=utf8mb4&loc=Local&parseTime=true", "amp database connect args")

	// RedisAddress
	// RedisPassword
	// RedisSelectDB
	// redis
	RedisAddress  = NewFlag("redis-address", "api-redis-service:6379", "what redis server address you want connect")
	RedisPassword = NewFlag("redis-password", "Hc@Cloud01", "redis password")
	RedisSelectDB = NewFlag("redis-select-db", "0", "what redis database used.")

	// TranslateLanguageCookieKey
	// TranslateDefaultLanguageCode
	// languageContext
	TranslateLanguageCookieKey   = NewFlag("translate-language-cookie-key", "language", "translate context of language cookie key")
	TranslateDefaultLanguageCode = NewFlag("translate-default-language-code", "zh-CN", "translate context of language cookie key")

	// TranslateErrorCodeFileDir
	// 错误code文件 路径
	TranslateErrorCodeFileDir = NewFlag("translate-error-code-file-dir", "./error-num-language-pkg", "translate context of language cookie key")

	// DevopsAmpURL
	// devops-amp 的默认路径
	DevopsAmpURL = NewFlag("devops-amp-url", "http://app-management-svc.caas-system:9060", "the url of devops-amp-svc")

	// OlympusCoreAddress
	// olympus-core 的默认路径
	OlympusCoreAddress = NewFlag("olympus-url", "http://olympus-core-svc.caas-system:8080", "the address of olympus-core")
	// DisasterRecoveryEnabled
	// 平台主备切换是否开启
	DisasterRecoveryEnabled = NewFlag("disaster-recovery", "false", "Is the platform master backup switch enabled")
	// StellariesAddress
	// StellariesPort
	// StandbyStellariesAddress
	// StandbyStellariesPort
	// 主备 stellaries address and port
	StellariesAddress        = NewFlag("stellaries-address", "changeme", "core stellaries address")
	StellariesPort           = NewFlag("stellaries-port", "80", "core stellaries port")
	StandbyStellariesAddress = NewFlag("standby-stellaries-address", "standby-changeme", "standby stellaries address")
	StandbyStellariesPort    = NewFlag("standby-stellaries-port", "80", "standby stellaries port")

	// DefaultRegistryProtocol
	// DefaultRegistryHost
	// DefaultRegistryPort
	// 平台默认的制品服务地址
	DefaultRegistryProtocol = NewFlag("default-registry-protocol", "https", "platform default registry protocol")
	DefaultRegistryHost     = NewFlag("default-registry-host", "", "platform default registry host")
	DefaultRegistryPort     = NewFlag("default-registry-port", "8443", "platform default registry port")

	// SisyphusAddress
	// ClusterCreateStepConfigPath
	// 	ForceUseTopSisyphus
	SisyphusAddress              = NewFlag("sisyphus-url", "http://cluster-manager-sisyphus-server.sisyphus-system:8080", "the address of sisyphus")
	SisyphusUsername             = NewFlag("sisyphus-username", "admin", "username of sisyphus platform")
	SisyphusPassword             = NewFlag("sisyphus-password", "Ab123456", "username of sisyphus platform")
	ForceUseTopSisyphus          = NewFlag("force-use-top-sisyphus", "false", "Is it mandatory to use upper level Sisyphus")
	ClusterCreateStepConfigPath  = NewFlag("cluster-create-config-path", "./cluster-create-config.yaml", "path of cluster create step config")
	ClusterUpgradeStepConfigPath = NewFlag("cluster-upgrade-config-path", "./cluster-upgrade-config.yaml", "path of cluster upgrade step config")
	NodeUpDownStepConfigPath     = NewFlag("node-up-down-config-path", "./node-up-down-config.yaml", "path of cluster create step config")
	NodeResetStepConfigPath      = NewFlag("node-reset-config-path", "./node-reset-config.yaml", "path of cluster create step config")

	// TopClusterIngressAddress 表示管理集群ingress 访问地址
	TopClusterIngressAddress = NewFlag("top-cluster-ingress-address", "changeme", "top cluster ingress address")

	FilePath             = NewFlag("file-path", "/usr/local/velero", "velero download file path")
	PreflightExpiredTime = NewFlag("preflightExpiredTime", "30m", "expired time")

	// MinioEndpoint minio endpoint
	MinioEndpoint = NewFlag("minio-endpoint", "minio-svc.minio:9000", "minio endpoint")
	// MinioAccessKey minio access key
	MinioAccessKey = NewFlag("minio-access-key", "minio", "minio access key")
	// MinioSecretKey minio secret key
	MinioSecretKey = NewFlag("minio-secret-key", "Hc@Cloud01", "minio secret key")
	// MinioRegion minio default region
	MinioRegion = NewFlag("minio-region", "cn-north-1", "minio region")
	// MinioURLPrefix minio url nginx proxy prefix
	MinioURLPrefix = NewFlag("minio-url-prefix", "/minio", "minio url prefix")
	// MinioInitBuckets for init buckets
	MinioInitBuckets = NewFlag("minio-init-buckets", strings.Join([]string{
		constants.MinioBaselineBucketName,
		constants.MinioBaselineReportBucketName,
	}, ","), "minio init buckets sure the bucket already exists ")

	//NodeConsoleImage 节点控制台默认镜像
	NodeConsoleImage    = NewFlag("nodeconsole-image", "k8s-deploy/busybox:1.31.kube", "default node console image for debugging pods")
	NodeSSHConsoleImage = NewFlag("node-ssh-console-image", "*************/k8s-deploy/alpine-ssh-client:latest", "default node ssh console image for ssh pods") // 新增

	// FeatureBaselineEnabled 是否开启基线检查功能
	FeatureBaselineEnabled = NewFlag("feature-baseline-enabled", "true", "Is the baseline feature enabled")
	// BaselineCheckerServerURL The Following Variables are Baseline Checker Args
	BaselineCheckerServerURL = NewFlag("baseline-checker-server-url", "http://baseline-checker-server.baseline:30002", "the address of baseline checker server ")
	// BaselineResourceRemainTime 资源保留时间
	BaselineResourceRemainTime = NewFlag("baseline-resource-remain-time", "24h", "baseline resource remain time, includes baselines,checkers, monitors crd")
	// BaselineJobRecordRemainTime 任务记录保留时间
	BaselineJobRecordRemainTime = NewFlag("baseline-job-record-remain-time", "720h", "baseline job record remain time")
	// BaselineStandardJobTimeout 标准任务超时时间
	BaselineStandardJobTimeout = NewFlag("baseline-standard-job-timeout", "30m", "baseline standard job timeout")
	// BaselineStrategyJobTimeout 策略任务超时时间
	BaselineStrategyJobTimeout = NewFlag("baseline-strategy-job-timeout", "60m", "baseline strategy job timeout")
	// BaselineStrategyJobMaxConcurrency 策略任务并发数
	BaselineStrategyJobMaxConcurrency = NewFlag("baseline-strategy-job-max-concurrency", "200", "baseline strategy job max concurrency")
	// BaselineStandardJobMaxConcurrency 标准任务并发数
	BaselineStandardJobMaxConcurrency = NewFlag("baseline-standard-job-max-concurrency", "2000", "baseline standard job max concurrency")
)
