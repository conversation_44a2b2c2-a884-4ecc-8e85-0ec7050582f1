package resources

import (
	"fmt"
	"strconv"

	"reflect"
	"sort"
	"strings"
	"time"

	"github.com/iancoleman/strcase"
	jsoniter "github.com/json-iterator/go"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/klog/v2"
)

const (
	Labels         = "labels"
	Annotations    = "annotations"
	SortFuncString = "string"
	SortFuncTime   = "time"
	SortFuncNumber = "number"
	SortOrderAsc   = "asc"
	SortOrderDesc  = "desc"
)

var json = jsoniter.ConfigCompatibleWithStandardLibrary

type mapEntity map[string]interface{}
type arrayEntity []interface{}

type Filter[T any] struct {
	Exact       map[string]string
	Fuzzy       map[string]string
	GreaterThan map[string]string
	Ge          map[string]string
	LessThan    map[string]string
	Le          map[string]string
	NotEqual    []string
	Fn          []func(item T) bool
	Limit       *int
	Offset      *int
	SortName    string
	SortOrder   string
	SortFunc    string
}

// WithFn add Filter Func to filter
func (f *Filter[T]) WithFn(fn ...func(item T) bool) *Filter[T] {
	f.Fn = append(f.Fn, fn...)
	return f
}

// WithSort ...
func (f *Filter[T]) WithSort(name, order, funcName string) *Filter[T] {
	f.SortName = name
	f.SortOrder = order
	f.SortFunc = funcName
	return f
}

// WithPage ...
func (f *Filter[T]) WithPage(pageNum, pageSize int) *Filter[T] {
	var offset *int
	limit := &pageSize
	if pageNum < 1 {
		tmpOffset := 0
		offset = &tmpOffset
	} else {
		tmpOffset := (pageNum - 1) * pageSize
		offset = &tmpOffset
	}
	f.Offset = offset
	f.Limit = limit
	return f

}

// FilterResult filter result by exact/fuzzy/gt match, sort, page
func (f *Filter[T]) FilterResult(list []T) (*models.PageableResponse[T], error) {
	pageableResponse := &models.PageableResponse[T]{Items: list}
	var items []T = pageableResponse.Items
	if len(items) == 0 {
		items = []T{}
		return pageableResponse, nil
	}
	var err error
	// match selector
	if items, err = f.exactMatch(items); err != nil {
		return nil, err
	}
	if items, err = f.fuzzyMatch(items); err != nil {
		return nil, err
	}
	if items, err = f.gtMatch(items); err != nil {
		return nil, err
	}
	if items, err = f.geMatch(items); err != nil {
		return nil, err
	}
	if items, err = f.ltMatch(items); err != nil {
		return nil, err
	}
	if items, err = f.leMatch(items); err != nil {
		return nil, err
	}
	if items, err = f.notEqualMatch(items); err != nil {
		return nil, err
	}
	items = f.filterFn(items)
	pageableResponse.TotalCount = len(items)
	// sort
	items = f.sort(items)
	// page
	items = f.page(items)
	pageableResponse.Items = items
	return pageableResponse, nil

}

func (f *Filter[T]) FilterResultWithoutPage(list []T) (*models.PageableResponse[T], error) {
	pageableResponse := &models.PageableResponse[T]{Items: list}
	var items []T = pageableResponse.Items
	if len(items) == 0 {
		items = []T{}
		return pageableResponse, nil
	}
	var err error
	// match selector
	if items, err = f.exactMatch(items); err != nil {
		return nil, err
	}
	if items, err = f.fuzzyMatch(items); err != nil {
		return nil, err
	}
	if items, err = f.gtMatch(items); err != nil {
		return nil, err
	}
	if items, err = f.geMatch(items); err != nil {
		return nil, err
	}
	if items, err = f.ltMatch(items); err != nil {
		return nil, err
	}
	if items, err = f.leMatch(items); err != nil {
		return nil, err
	}
	if items, err = f.notEqualMatch(items); err != nil {
		return nil, err
	}
	items = f.filterFn(items)
	pageableResponse.TotalCount = len(items)
	// sort
	items = f.sort(items)
	pageableResponse.Items = items
	return pageableResponse, nil

}

//// filter result by exact/fuzzy/gt match, sort, page
//func (f *Filter) FilterResultToMap(body []byte) K8sJson {
//	var result K8sJson
//
//	err := json.Unmarshal(body, &result)
//	if err != nil {
//		return nil
//	}
//
//	// list type
//	if result["items"] != nil {
//		if items, ok := result["items"].(K8sJsonArr); ok {
//			// match selector
//			items = f.exactMatch(items)
//			items = f.fuzzyMatch(items)
//			items = f.gtMatch(items)
//			items = f.geMatch(items)
//			result["total"] = len(items)
//			// sort
//			items = f.sort(items)
//			// page
//			items = f.page(items)
//			result["items"] = items
//		}
//	}
//
//	return result
//}

// exact search
func (f *Filter[T]) exactMatch(items []T) ([]T, error) {
	if len(f.Exact) == 0 {
		return items, nil
	}
	var result []T

	// every list record
	for _, item := range items {
		flag := true
		// every exact match condition
		for key, value := range f.Exact {
			// key = .metadata.xxx.xxx， multi level

			realValue, err := GetDeepValue(item, key)
			if err != nil {
				return result, err
			}
			// if one condition not match
			if !strings.EqualFold(realValue, value) {
				flag = false
				break
			}
		}
		// if every exact condition match
		if flag {
			result = append(result, item)
		}
	}
	return result, nil
}

// fuzzy search
func (f *Filter[T]) fuzzyMatch(items []T) ([]T, error) {
	if len(f.Fuzzy) == 0 {
		return items, nil
	}
	var result []T
	// every list record
	for _, item := range items {
		flag := true
		// every fuzzy match condition
		for key, value := range f.Fuzzy {
			// key = metadata.xxx.xxx， multi level
			realValue, err := GetDeepValue(item, key)
			if err != nil {
				return nil, err
			}
			// if one condition not match
			if !strings.Contains(realValue, value) {
				flag = false
				break
			}
		}
		// if every fuzzy condition match
		if flag {
			result = append(result, item)
		}
	}
	return result, nil
}

// gt search
func (f *Filter[T]) gtMatch(items []T) ([]T, error) {
	if len(f.GreaterThan) == 0 {
		return items, nil
	}

	var result []T
	// every list record
	for _, item := range items {
		flag := true
		// every fuzzy match condition
		for key, value := range f.GreaterThan {
			// key = metadata.xxx.xxx， multi level
			realValue, err := GetDeepValue(item, key)
			if err != nil {
				return nil, err
			}
			// if one condition not match
			if result := strings.Compare(realValue, value); result <= 0 {
				flag = false
				break
			}
		}
		// if every fuzzy condition match
		if flag {
			result = append(result, item)
		}
	}
	return result, nil
}

// ge search
func (f *Filter[T]) geMatch(items []T) ([]T, error) {
	if len(f.Ge) == 0 {
		return items, nil
	}
	var result []T

	// every list record
	for _, item := range items {
		flag := true
		// every fuzzy match condition
		for key, value := range f.Ge {
			// key = metadata.xxx.xxx， multi level
			realValue, err := GetDeepValue(item, key)
			if err != nil {
				return nil, err
			}
			// if one condition not match
			if result := strings.Compare(realValue, value); result < 0 {
				flag = false
				break
			}
		}
		// if every fuzzy condition match
		if flag {
			result = append(result, item)
		}
	}
	return result, nil
}

// gt search
func (f *Filter[T]) ltMatch(items []T) ([]T, error) {
	if len(f.GreaterThan) == 0 {
		return items, nil
	}

	var result []T
	// every list record
	for _, item := range items {
		flag := true
		// every fuzzy match condition
		for key, value := range f.GreaterThan {
			// key = metadata.xxx.xxx， multi level
			realValue, err := GetDeepValue(item, key)
			if err != nil {
				return nil, err
			}
			// if one condition not match
			if result := strings.Compare(realValue, value); result <= 0 {
				flag = false
				break
			}
		}
		// if every fuzzy condition match
		if flag {
			result = append(result, item)
		}
	}
	return result, nil
}

// ge search
func (f *Filter[T]) leMatch(items []T) ([]T, error) {
	if len(f.Ge) == 0 {
		return items, nil
	}
	var result []T

	// every list record
	for _, item := range items {
		flag := true
		// every fuzzy match condition
		for key, value := range f.Ge {
			// key = metadata.xxx.xxx， multi level
			realValue, err := GetDeepValue(item, key)
			if err != nil {
				return nil, err
			}
			// if one condition not match
			if result := strings.Compare(realValue, value); result > 0 {
				flag = false
				break
			}
		}
		// if every fuzzy condition match
		if flag {
			result = append(result, item)
		}
	}
	return result, nil
}

// not equal search
func (f *Filter[T]) notEqualMatch(items []T) ([]T, error) {
	if len(f.NotEqual) == 0 {
		return items, nil
	}
	var result []T

	// every list record
	for _, item := range items {
		flag := true
		// every fuzzy match condition
		for _, str := range f.NotEqual {
			ss := strings.Split(str, "!=")
			key, value := ss[0], ss[1]
			// key = metadata.xxx.xxx， multi level
			realValue, err := GetDeepValue(item, key)
			if err != nil {
				return nil, err
			}
			// if one condition not match
			if strings.EqualFold(realValue, value) {
				flag = false
				break
			}
		}
		// if every fuzzy condition match
		if flag {
			result = append(result, item)
		}
	}
	return result, nil
}

// filterFn filter fn by provide the items
func (f *Filter[T]) filterFn(items []T) []T {
	if len(f.Fn) == 0 {
		return items
	}
	var result []T
	for _, item := range items {
		flag := true
		for _, fn := range f.Fn {
			if fn != nil && !fn(item) {
				flag = false
			}
		}
		if flag {
			result = append(result, item)
		}
	}
	return result
}

// sort by .metadata.name/.metadata.creationTimestamp
func (f *Filter[T]) sort(items []T) []T {
	if len(items) == 0 {
		return items
	}

	sort.Slice(items, lessFunc(items, "string", "metadata.name", "asc"))
	sort.Slice(items, lessFunc(items, f.SortFunc, f.SortName, f.SortOrder))
	return items
}

func lessFunc[T any](items []T, sortFunc, sortName, sortOrder string) func(i, j int) bool {
	return func(i, j int) bool {
		switch sortFunc {
		case "string":
			si, _ := GetDeepValue(items[i], sortName)
			sj, _ := GetDeepValue(items[j], sortName)
			if sortOrder == "asc" {
				return strings.Compare(si, sj) < 0
			} else {
				return strings.Compare(si, sj) > 0
			}
		case "time":
			tid, _ := GetDeepValue(items[i], sortName)
			ti, err := parseTime(tid)
			if err != nil {
				return false
			}
			tjd, _ := GetDeepValue(items[j], sortName)
			tj, err := parseTime(tjd)
			if err != nil {
				return false
			} else if sortOrder == "asc" {
				return ti.Before(tj)
			} else {
				return ti.After(tj)
			}
		case "number":
			ni, _ := GetDeepFloat64(items[i], sortName)
			nj, _ := GetDeepFloat64(items[j], sortName)
			if sortOrder == "asc" {
				return ni < nj
			} else if sortOrder == "desc" {
				return ni > nj
			} else {
				return ni < nj
			}
		default:
			si, _ := GetDeepValue(items[i], sortName)
			sj, _ := GetDeepValue(items[j], sortName)
			if sortOrder == "asc" {
				return strings.Compare(si, sj) < 0
			} else {
				return strings.Compare(si, sj) > 0
			}
		}

	}
}

// page
func (f *Filter[T]) page(items []T) []T {
	if len(items) == 0 {
		return items
	}
	if f.Limit == nil && f.Offset == nil {
		return items
	}

	size := len(items)
	offset, limit := 0, size
	if f.Offset != nil {
		offset = *f.Offset
	}
	if f.Limit != nil {
		limit = *f.Limit
	}
	if offset >= size {
		return items[0:0]
	}
	end := offset + limit
	if end > size {
		end = size
	}
	return items[offset:end]
}

// get value by metadata.xx.xx.xx, multi level key
func GetDeepValue(item interface{}, keyStr string) (value string, err error) {
	defer func() {
		if err := recover(); err != nil {
			value = ""
			return
		}
	}()
	var m map[string]interface{}
	switch obj := item.(type) {
	case map[string]interface{}:
		m = obj
	case unstructured.Unstructured:
		m = obj.Object
	case *unstructured.Unstructured:
		if obj != nil {
			m = obj.Object
		}
	default:
		m, err = runtime.DefaultUnstructuredConverter.ToUnstructured(item)
		if err != nil {
			m, err = runtime.DefaultUnstructuredConverter.ToUnstructured(&obj)
			if err != nil {
				return
			}
		}
	}

	keys := parseKeys(keyStr)
	var deepValue interface{}
	deepValue, _, err = unstructured.NestedFieldNoCopy(m, keys...)
	if err != nil {
		klog.Warningf("Failed to get deep value for key %s, err: %+v", keyStr, err)
		return "", err
	}
	if value, ok := deepValue.([]interface{}); ok {
		if len(value) == 0 {
			return "", nil
		}
		raw, err := json.Marshal(value)
		if err != nil {
			return "", err
		}
		return string(raw), nil
	} else if value, ok := deepValue.(map[string]interface{}); ok {
		raw, err := json.Marshal(value)
		if err != nil {
			return "", err
		}
		return string(raw), nil
	} else if value, ok := deepValue.(string); ok {
		return value, nil
	}
	//var info mapEntity
	//bytes, err := json.Marshal(item)
	//if err != nil {
	//	return "", err
	//}
	//if err := json.Unmarshal(bytes, &info); err != nil {
	//	return "", err
	//}
	//
	//// key = metadata.xxx.xxx， multi level
	//keys := parseKeys(keyStr)
	//n := len(keys)
	//i := 0
	//for ; n > 0 && i < n-1; i++ {
	//	temp, ok := info[keys[i]].(mapEntity)
	//	if !ok {
	//		temp = info[keys[i]].(arrayEntity)[0].(mapEntity)
	//	}
	//	info = temp
	//
	//	if keys[i] == Labels || keys[i] == Annotations {
	//		i++
	//		break
	//	}
	//}
	//key := strings.Join(keys[i:], ".")
	//value = info[key].(string)
	return
}

// parseKeys support Escape character such as annotations.demo\.example\.com
// means { "annotations": {"demo.example.com" : "xxx"}}
func parseKeys(keyStr string) []string {
	var keys []string
	var currentKey strings.Builder
	escaped := false

	for _, r := range keyStr {
		if escaped {
			// if before char is '\' add the char
			currentKey.WriteRune(r)
			escaped = false
		} else {
			if r == '\\' {
				escaped = true
			} else if r == '.' {
				// if is . append
				if currentKey.Len() > 0 {
					keys = append(keys, currentKey.String())
				}
				currentKey.Reset()
			} else {
				// write value
				currentKey.WriteRune(r)
			}
		}
	}
	// add last key
	keys = append(keys, currentKey.String())

	return keys
}

func parseTime(timeStr string) (time.Time, error) {
	parseFns := []func(string) (time.Time, error){
		func(s string) (time.Time, error) { return time.Parse(time.DateTime, s) },
		func(s string) (time.Time, error) { return time.Parse(time.RFC3339, s) },
		func(s string) (time.Time, error) { return time.Parse(time.RFC3339Nano, s) },
	}
	for _, parseFn := range parseFns {
		if t, err := parseFn(timeStr); err == nil {
			return t, nil
		}
	}
	return time.Time{}, fmt.Errorf("invalid time format: %s", timeStr)
}

// GetDeepFloat64 get float64 value by metadata.xx.xx.xx, multi level key
func GetDeepFloat64(item interface{}, keyStr string) (value float64, err error) {
	defer func() {
		if err := recover(); err != nil {
			value = 0
			return
		}
	}()

	var m map[string]interface{}
	switch obj := item.(type) {
	case map[string]interface{}:
		m = obj
	case unstructured.Unstructured:
		m = obj.Object
	case *unstructured.Unstructured:
		if obj != nil {
			m = obj.Object
		}
	default:
		m, err = runtime.DefaultUnstructuredConverter.ToUnstructured(item)
		if err != nil {
			m, err = runtime.DefaultUnstructuredConverter.ToUnstructured(&obj)
			if err != nil {
				return
			}
		}
	}
	keys := parseKeys(keyStr)
	var deepValue interface{}
	deepValue, _, err = unstructured.NestedFieldNoCopy(m, keys...)
	if err != nil {
		klog.Warningf("Failed to get deep value for key %s, err: %+v", keyStr, err)
		return 0, err
	}
	if float, err := strconv.ParseFloat(fmt.Sprintf("%v", deepValue), 64); err != nil {
		klog.Warningf("Failed to get deep value for key %s, err: %+v", keyStr, err)
		return 0, nil
	} else {
		value = float
	}
	//if value, ok := deepValue.(float64); ok {
	//	return value, nil
	//}
	//var temp map[string]interface{}
	//var bytes []byte
	//bytes, err = json.Marshal(item)
	//if err != nil {
	//	return
	//}
	//if err = json.Unmarshal(bytes, &temp); err != nil {
	//	return
	//}
	//
	//// key = metadata.xxx.xxx， multi level
	//keys := strings.Split(keyStr, ".")
	//n := len(keys)
	//i := 0
	//for ; n > 0 && i < n-1; i++ {
	//	newTemp := temp[keys[i]]
	//	temp = newTemp.(map[string]interface{})
	//	if keys[i] == Labels || keys[i] == Annotations {
	//		i++
	//		break
	//	}
	//}
	//key := strings.Join(keys[i:], ".")
	//value = temp[key].(float64)
	return
}

//// get map by spec.selector.matchLabels={xx= xx}
//func GetDeepMap(item interface{}, keyStr string) (value K8sJson) {
//	defer func() {
//		if err := recover(); err != nil {
//			value = nil
//			return
//		}
//	}()
//
//	temp := item.(K8sJson)
//	// key = spec.selector.matchLabels， multi level
//	keys := strings.Split(keyStr, ".")
//	n := len(keys)
//	i := 0
//	for ; n > 0 && i < n-1; i++ {
//		temp = temp[keys[i]].(K8sJson)
//		if keys[i] == Labels || keys[i] == Annotations {
//			i++
//			break
//		}
//	}
//	key := strings.Join(keys[i:], ".")
//	value = temp[key].(K8sJson)
//	return
//}
//
//// get metadata.ownerReference[0]
//func GetDeepArray(item interface{}, keyStr string) (value K8sJsonArr) {
//	defer func() {
//		if err := recover(); err != nil {
//			value = nil
//			return
//		}
//	}()
//
//	temp := item.(K8sJson)
//	// key = metadata.ownerReference[0]， multi level
//	keys := strings.Split(keyStr, ".")
//	n := len(keys)
//	i := 0
//	for ; n > 0 && i < n-1; i++ {
//		temp = temp[keys[i]].(K8sJson)
//		if keys[i] == Labels || keys[i] == Annotations {
//			i++
//			break
//		}
//	}
//	key := strings.Join(keys[i:], ".")
//	value = temp[key].(K8sJsonArr)
//	return
//}

func (f *Filter[T]) DeepCopy() *Filter[T] {
	result := &Filter[T]{
		Exact:       make(map[string]string, len(f.Exact)),
		Fuzzy:       make(map[string]string, len(f.Fuzzy)),
		GreaterThan: make(map[string]string, len(f.GreaterThan)),
		Ge:          make(map[string]string, len(f.Ge)),
		LessThan:    make(map[string]string, len(f.LessThan)),
		Le:          make(map[string]string, len(f.Le)),
		NotEqual:    make([]string, len(f.NotEqual)),
		Fn:          make([]func(item T) bool, len(f.Fn)),
		Limit:       f.Limit,
		Offset:      f.Offset,
		SortName:    f.SortName,
		SortOrder:   f.SortOrder,
		SortFunc:    f.SortFunc,
	}

	for k, v := range f.Exact {
		result.Exact[k] = v
	}
	for k, v := range f.Fuzzy {
		result.Fuzzy[k] = v
	}
	for k, v := range f.GreaterThan {
		result.GreaterThan[k] = v
	}
	for k, v := range f.Ge {
		result.Ge[k] = v
	}
	for i, v := range f.NotEqual {
		result.NotEqual[i] = v
	}
	for i, v := range f.Fn {
		result.Fn[i] = v
	}

	return result
}

// DeepCopyIntoFilter ...
func DeepCopyIntoFilter[T any, S any](source Filter[S], target Filter[T]) Filter[T] {
	target.Le = source.Le
	target.Ge = source.Ge
	target.LessThan = source.LessThan
	target.GreaterThan = source.GreaterThan
	target.NotEqual = source.NotEqual
	target.Exact = source.Exact
	target.Fuzzy = source.Fuzzy
	target.Limit = source.Limit
	target.Offset = source.Offset
	target.SortName = source.SortName
	target.SortOrder = source.SortOrder
	target.SortFunc = source.SortFunc
	return target
}

func getTableColumnToFieldMap(table schema.Tabler) map[string]string {
	fieldMap := make(map[string]string)
	t := reflect.TypeOf(table)
	if t.Kind() == reflect.Ptr {
		t = t.Elem()
	}
	for i := 0; i < t.NumField(); i++ {
		field := t.Field(i)
		fieldName := strcase.ToLowerCamel(field.Name)
		gormTags := field.Tag.Get("gorm")
		if gormTags == "" {
			continue
		}
		for _, gormTag := range strings.Split(gormTags, ",") {
			keyPairs := strings.Split(gormTag, ";")
			for i := 0; i < len(keyPairs); i++ {
				keyPair := strings.Split(keyPairs[i], ":")
				if len(keyPair) != 2 {
					continue
				}
				if keyPair[0] != "column" {
					continue
				}
				columnTag := keyPair[1]
				if columnTag == "" {
					continue
				}
				fieldMap[fieldName] = columnTag
				break
			}
		}
	}
	return fieldMap
}

// ApplyToDB 将过滤器应用到查询中，并支持分页和排序
func (f *Filter[T]) ApplyToDB(tx *gorm.DB, table schema.Tabler, fieldMappings ...map[string]string) (*gorm.DB, int64) {
	columnToFieldMap := getTableColumnToFieldMap(table)
	if len(columnToFieldMap) == 0 {
		return tx, 0
	}
	for _, mapping := range fieldMappings {
		for k, v := range mapping {
			columnToFieldMap[k] = v
		}
	}

	// 用于记录已添加的查询条件，避免重复
	conditionSet := make(map[string]struct{})

	// 应用过滤条件
	tx = f.applyFilters(tx, columnToFieldMap, conditionSet)

	// 获取记录总量
	var total int64
	tx.Count(&total)

	// 应用分页
	tx = f.applyPagination(tx, total)

	// 应用排序
	tx = f.applySorting(tx, columnToFieldMap)

	return tx, total
}

// applyFilters 应用各种查询条件
func (f *Filter[T]) applyFilters(tx *gorm.DB, columnToFieldMap map[string]string, conditionSet map[string]struct{}) *gorm.DB {
	addCondition := func(condition string, value interface{}) {
		if _, exists := conditionSet[condition]; !exists {
			tx = tx.Where(condition, value)
			conditionSet[condition] = struct{}{}
		}
	}

	for key, value := range f.Exact {
		if column, ok := columnToFieldMap[key]; ok {
			addCondition(fmt.Sprintf("%s = ?", column), value)
		}
	}

	for key, value := range f.Fuzzy {
		if column, ok := columnToFieldMap[key]; ok {
			addCondition(fmt.Sprintf("%s LIKE ?", column), fmt.Sprintf("%%%s%%", value))
		}
	}

	for key, value := range f.GreaterThan {
		if column, ok := columnToFieldMap[key]; ok {
			addCondition(fmt.Sprintf("%s > ?", column), value)
		}
	}

	for key, value := range f.Ge {
		if column, ok := columnToFieldMap[key]; ok {
			addCondition(fmt.Sprintf("%s >= ?", column), value)
		}
	}

	for key, value := range f.LessThan {
		if column, ok := columnToFieldMap[key]; ok {
			addCondition(fmt.Sprintf("%s < ?", column), value)
		}
	}

	for key, value := range f.Le {
		if column, ok := columnToFieldMap[key]; ok {
			addCondition(fmt.Sprintf("%s <= ?", column), value)
		}
	}

	for _, key := range f.NotEqual {
		if column, ok := columnToFieldMap[key]; ok {
			addCondition(fmt.Sprintf("%s != ?", column), "")
		}
	}

	return tx
}

// applyPagination 应用分页逻辑
func (f *Filter[T]) applyPagination(tx *gorm.DB, total int64) *gorm.DB {
	if f.Limit != nil && f.Offset != nil {

		offset, limit := *f.Offset, *f.Limit
		if limit < 10 {
			limit = 10
		}
		if offset > int(total) {
			offset = int(total)
			limit = 0
		}
		tx = tx.Offset(offset).Limit(limit)
	}
	return tx
}

// applySorting 应用排序逻辑
func (f *Filter[T]) applySorting(tx *gorm.DB, columnToFieldMap map[string]string) *gorm.DB {
	if f.SortName != "" {
		if column, ok := columnToFieldMap[f.SortName]; ok {
			sortOrder := "ASC"
			if strings.ToUpper(f.SortOrder) == "DESC" {
				sortOrder = "DESC"
			}
			tx = tx.Order(fmt.Sprintf("%s %s", column, sortOrder))
		}
	}
	return tx
}
