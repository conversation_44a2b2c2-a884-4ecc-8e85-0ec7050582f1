package template

import (
	"github.com/xuri/excelize/v2"
	database_aop "harmonycloud.cn/unifiedportal/translate-sdk-golang/database-aop"
)

func init() {
	database_aop.GroupInfo(ExcelMeta{}, database_aop.TranslateGroupInfo{
		Name:          "olympus-excel-metadata",
		UniqueKeyName: "Code",
	})

	database_aop.GroupInfo(ExcelTitleRow{}, database_aop.TranslateGroupInfo{
		Name:          "olympus-excel-column",
		UniqueKeyName: "ExcelCode/Code",
	})
}

// ExcelTemplateCode
// Excel 文件名的编码 便于国际化翻译，同时也作为获取文件的唯一编码
type ExcelTemplateCode string

var (
	// ExcelTemplateCodeClusterCreateNodesAllInOne 表示节点Code
	// ExcelFinaNameClusterCreateNodesAllInOne 表示文件名称
	// ExcelTitleClusterCreateNodesAllInOne 表示标题名称
	// 表示集群创建时的模版信息 - All-in-One
	ExcelTemplateCodeClusterCreateNodesAllInOne ExcelTemplateCode = "cluster_create_nodes_template_AllInOne"
	ExcelFinaNameClusterCreateNodesAllInOne                       = "All-in-One批量导入模版.xlsx"
	ExcelTitleClusterCreateNodesAllInOne                          = `填写说明：
模版导入后为全量覆盖，即平台已存在的手动添加节点将会被覆盖；
All-in-One模式使用单台机器部署集群所有组件，适用于希望低成本快速配置的开发测试或PoC集群；
其中，淡黄色表头当且仅当“节点数据盘自动挂载”设置为True时才需要填写，否则即使填写也不会生效；
`

	// ExcelTemplateCodeClusterCreateNodesMinimizeHA 表示节点Code
	// ExcelFinaNameClusterCreateNodesMinimizeHA 表示文件名称
	// ExcelTitleClusterCreateNodesMinimizeHA 表示标题名称
	// 表示集群创建时的模版信息 - 最小化高可用
	ExcelTemplateCodeClusterCreateNodesMinimizeHA ExcelTemplateCode = "cluster_create_nodes_template_MinimizeHA"
	ExcelFinaNameClusterCreateNodesMinimizeHA                       = "最小化高可用批量导入模版.xlsx"
	ExcelTitleClusterCreateNodesMinimizeHA                          = `填写说明：
模版导入后为全量覆盖，即平台已存在的手动添加节点将会被覆盖；
最小化高可用模式包含3个Master节点，Worker节点数量不作限制，Master节点可部署系统组件及业务负载，适用于机器数量较少但对稳定性有一定要求的集群；
其中，淡黄色表头当且仅当“节点数据盘自动挂载”设置为True时才需要填写，否则即使填写也不会生效；
`

	// ExcelTemplateCodeClusterCreateNodesStandardNoneHA 表示节点Code
	// ExcelFinaNameClusterCreateNodesStandardNoneHA 表示文件名称
	// ExcelTitleClusterCreateNodesStandardNoneHA 表示标题名称
	// 表示集群创建时的模版信息 - 标准非高可用
	ExcelTemplateCodeClusterCreateNodesStandardNoneHA ExcelTemplateCode = "cluster_create_nodes_template_StandardNoneHA"
	ExcelFinaNameClusterCreateNodesStandardNoneHA                       = "标准非高可用批量导入模版.xlsx"
	ExcelTitleClusterCreateNodesStandardNoneHA                          = `填写说明：
模版导入后为全量覆盖，即平台已存在的手动添加节点将会被覆盖；
标准非高可用模式包含单个Master节点，以及至少3个Worker节点用于部署系统组件，Master节点不用于部署系统组件及业务负载，适用于机器数量较少且对稳定性要求不高的集群；
其中，淡黄色表头当且仅当“节点数据盘自动挂载”设置为True时才需要填写，否则即使填写也不会生效；
`

	// ExcelTemplateCodeClusterCreateNodesStandardHA 表示节点Code
	// ExcelFinaNameClusterCreateNodesStandardHA 表示文件名称
	// ExcelTitleClusterCreateNodesStandardHA 表示标题名称
	// 表示集群创建时的模版信息 - 标准高可用
	ExcelTemplateCodeClusterCreateNodesStandardHA ExcelTemplateCode = "cluster_create_nodes_template_StandardHA"
	ExcelFinaNameClusterCreateNodesStandardHA                       = "标准高可用批量导入模版.xlsx"
	ExcelTitleClusterCreateNodesStandardHA                          = `填写说明：
模版导入后为全量覆盖，即平台已存在的手动添加节点将会被覆盖；
标准高可用模式包含3个Master节点，以及至少4个Worker节点用于部署系统组件，Master节点不用于部署系统组件及业务负载 ，适用于对稳定性要求高的生产集群；
其中，淡黄色表头当且仅当“节点数据盘自动挂载”设置为True时才需要填写，否则即使填写也不会生效；
`

	// ExcelTemplateCodeClusterNodeBatchAppend 表示文件code
	// ExcelFinaNameClusterNodeBatchAppend 表示文件名称
	// ExcelTitleClusterNodeBatchAppend 表示标题名称
	// 表示集群批量添加节点
	ExcelTemplateCodeClusterNodeBatchAppend ExcelTemplateCode = "cluster_node_batch_append"
	ExcelFinaNameClusterNodeBatchAppend                       = "主机模版.xlsx"
	ExcelTitleClusterNodeBatchAppend                          = `填写说明：
模版导入后为全量覆盖，即平台已存在的手动添加节点将会被覆盖；请至少填写一台主机信息；
其中，淡黄色表头当且仅当“节点数据盘自动挂载”设置为True时才需要填写，否则即使填写也不会生效；`

	ExcelTemplateCodeClusterUpgradeNode ExcelTemplateCode = "cluster_upgrade_nodes"
	ExcelFinaNameClusterUpgradeNode                       = "集群升级主机信息导入模版.xlsx"
	ExcelTitleClusterUpgradeNode                          = `填写说明：
请补充完善表格信息，节点名称以及节点IP数据请勿新增或修改`
)

type ExcelMeta struct {
	// 文件名的编码 便于国际化翻译，同时也作为获取文件的唯一编码
	Code ExcelTemplateCode `json:"templateCode"`

	// 文件名
	FileName string `json:"fileName" translate:"keyName=filename"`

	// Title 标题 表示提示信息
	Title string `json:"title" translate:"keyName=title"`

	// TitleColor 标题颜色
	TitleColor string `json:"titleColor"`

	// TitleHeight title 行高
	TitleHeight float64 `json:"titleHeight"`

	// TitleRowHeight TitleRows 的行高
	TitleRowHeight float64 `json:"titleHeight"`

	// TitleRows
	// Excel 的列名表头
	TitleRows []ExcelTitleRow `json:"titleRows" translateObject:""`
}

type ExcelTitleRow struct {
	// ExcelCode 列归属的excel code
	ExcelCode ExcelTemplateCode `json:"excelTemplateCode"`
	// Code row Code
	Code string `json:"code"`
	// Name row Name
	Name string `json:"name" translate:"keyName=name"`
	// 列宽
	Weight float64 `json:"weight"`
	// 风格
	Style *excelize.Style `json:"style"`
}
