package models

import (
	"strings"
	"time"

	database_aop "harmonycloud.cn/unifiedportal/translate-sdk-golang/database-aop"
	appsv1 "k8s.io/api/apps/v1"
	batchv1 "k8s.io/api/batch/v1"
)

func init() {
	// translate group CloudService register
	database_aop.GroupInfo(CloudService{}, database_aop.TranslateGroupInfo{
		Name:          "k8s-resource-skyview-cloud-service",
		UniqueKeyName: "CloudServiceName",
	})

	// translate group CloudServiceComponent register
	database_aop.GroupInfo(CloudServiceComponent{}, database_aop.TranslateGroupInfo{
		Name:          "k8s-resource-skyview-cloud-service-component",
		UniqueKeyName: "CloudServiceName/CloudComponentName",
	})
}

type CloudServiceList []CloudService

// Len is the number of elements in the collection.
func (arr CloudServiceList) Len() int {
	return len(arr)
}

// Less reports whether the element with index i
// must sort before the element with index j.
//
// If both Less(i, j) and Less(j, i) are false,
// then the elements at index i and j are considered equal.
// Sort may place equal elements in any order in the final result,
// while Stable preserves the original input order of equal elements.
//
// Less must describe a transitive ordering:
//   - if both Less(i, j) and Less(j, k) are true, then Less(i, k) must be true as well.
//   - if both Less(i, j) and Less(j, k) are false, then Less(i, k) must be false as well.
//
// Note that floating-point comparison (the < operator on float32 or float64 values)
// is not a transitive ordering when not-a-number (NaN) values are involved.
// See Float64Slice.Less for a correct implementation for floating-point values.
func (arr CloudServiceList) Less(i, j int) bool {
	iCreateTime := arr[i].CreateTime.UnixNano()
	jCreateTime := arr[j].CreateTime.UnixNano()
	return iCreateTime < jCreateTime
}

// Swap swaps the elements with indexes i and j.
func (arr CloudServiceList) Swap(i, j int) {
	arr[i], arr[j] = arr[j], arr[i]
}

// CloudService
// 云服务
type CloudService struct {
	CloudServiceName         string                    `json:"cloudServiceName"`                            // 云服务名称
	CreateTime               time.Time                 `json:"createTime"`                                  // 云服务创建时间
	Icon                     IconInfo                  `json:"icon,omitempty"`                              // 图标信息
	Description              string                    `json:"description" translate:"keyName=description"` // 云服务详细信息
	DisplayName              string                    `json:"displayName" translate:"keyName=displayName"` // 云服务展示名称
	Version                  string                    `json:"version"`                                     // 云服务版本
	Status                   CloudServiceStatus        `json:"status"`                                      // 云服务状态 Running  Unhealthy Exception
	CpuLimit                 float64                   `json:"cpuLimit"`                                    // 云服务CPU限制量
	MemoryLimit              float64                   `json:"memoryLimit"`                                 // 云服务内存限制量
	CpuRequest               float64                   `json:"cpuRequest"`                                  // 云服务CPU请求量
	MemoryRequest            float64                   `json:"memoryRequest"`                               // 云服务内存请求量
	CpuUsage                 float64                   `json:"cpuUsage"`                                    // 云服务CPU使用量
	MemoryUsage              float64                   `json:"memoryUsage"`                                 // 云服务内存使用量
	PodNum                   int64                     `json:"pods"`                                        // 云服务总Pod数量
	ReadyPodNumber           int64                     `json:"readypods"`                                   // 云服务Ready 的 Pod数量
	ComponentNumber          int64                     `json:"components"`                                  // 云服务的云组件数量
	ExceptionComponentNumber int64                     `json:"exceptionCom"`                                // 云服务的故障云组件数量
	ComponentConditions      []CloudComponentCondition `json:"componentConditions"`                         // 云服务的云组件状态概要信息
}

// IconInfo
// 图标消息
type IconInfo struct {
	Name string `json:"name,omitempty"` // 图标Name
}

// CloudServiceStatus
// 云服务状态
type CloudServiceStatus string

var (
	// CloudServiceRunning
	// 组件正常
	CloudServiceRunning CloudServiceStatus = "Running"
	// CloudServiceUnhealthy
	// 组件部分不正常
	CloudServiceUnhealthy CloudServiceStatus = "Unhealthy"
	// CloudServiceException
	// 组件异常
	CloudServiceException CloudServiceStatus = "Exception"
)

// CloudServiceComponentStatus
// 云组件状态
type CloudServiceComponentStatus string

var (
	// CloudServiceComponentRunning
	// 组件正常
	CloudServiceComponentRunning CloudServiceComponentStatus = "Running"
	// CloudServiceComponentUnhealthy
	// 组件部分不正常
	CloudServiceComponentUnhealthy CloudServiceComponentStatus = "Unhealthy"
	// CloudServiceComponentException
	// 组件异常
	CloudServiceComponentException CloudServiceComponentStatus = "Exception"
)

// CloudComponentCondition
// 云服务的云组件状态概要信息
type CloudComponentCondition struct {
	Name   string                      `json:"name"`   // 云组件名称
	Status CloudServiceComponentStatus `json:"status"` // 云组件状态 Running  Unhealthy  Exception
}

// CloudServiceGrafanaParam
// grafana 参数
type CloudServiceGrafanaParam struct {
	Name string `json:"name"`
	Url  string `json:"url"`
}

// CloudServiceComponentSortByCluster
// 根据集群整理云组件
type CloudServiceComponentSortByCluster struct {
	Cluster    string                  `json:"cluster"`                           // 集群名称
	Components []CloudServiceComponent `json:"workComponents" translateObject:""` // 集群下的云组件列表
}

// CloudServiceComponent
// 云组件
type CloudServiceComponent struct {
	CloudServiceName          string                          `json:"cloudServiceName"`                            // 云组件归属的云服务名称
	CloudComponentName        string                          `json:"cloudComponentName"`                          // 云组件名称
	ClusterPolicy             CloudServiceComponentPolicy     `json:"clusterPolicy"`                               // 云组件类型  managed work
	Cluster                   string                          `json:"cluster"`                                     // 集群名称
	CreateTime                time.Time                       `json:"createTime"`                                  // 云组件创建时间
	Description               string                          `json:"description" translate:"keyName=description"` // 云组件详细详细
	DisplayName               string                          `json:"displayName" translate:"keyName=displayName"` // 云组件展示名称
	Version                   string                          `json:"version"`                                     // 云组件版本
	Status                    CloudServiceComponentStatus     `json:"status"`                                      // 云组件状态  Running  Unhealthy Exception
	CpuLimit                  float64                         `json:"cpuLimit"`                                    // 云组件CPU限制量
	MemoryLimit               float64                         `json:"memoryLimit"`                                 // 云组件内存限制量
	CpuRequest                float64                         `json:"cpuRequest"`                                  // 云组件CPU请求量
	MemoryRequest             float64                         `json:"memoryRequest"`                               // 云组件内存请求量
	CpuUsage                  float64                         `json:"cpuUsage"`                                    // 云组件CPU使用量
	MemoryUsage               float64                         `json:"memoryUsage"`                                 // 云组件内存使用量
	PodNum                    int64                           `json:"pods"`                                        // 云组件Pod总数
	ReadyPodNumber            int64                           `json:"readypods"`                                   // 云组件Ready 的Pod总数
	UnHealthWorkloadCondition CloudComponentWorkloadCondition `json:"unHealthWorkloadCondition,omitempty"`         // 未健康的工作负载信息 Status 固定返回为False
}

// CloudComponentWorkloadCondition
// 云组件事件
type CloudComponentWorkloadCondition struct {
	Status    string                   `json:"status"`    // 状态 False True UnKnow
	Reason    string                   `json:"reason"`    // 原因
	Message   string                   `json:"message"`   // 详情信息
	Workloads []CloudComponentWorkload `json:"workloads"` // 工作负载信息
}

// CloudServiceComponentPolicy
// 云组件类型
type CloudServiceComponentPolicy string

var (
	// CloudServiceComponentPolicyManaged
	// 标记云组件为管控组件
	CloudServiceComponentPolicyManaged CloudServiceComponentPolicy = "managed"

	// CloudServiceComponentPolicyWork
	// 标记云组件为数据组件
	CloudServiceComponentPolicyWork CloudServiceComponentPolicy = "work"
)

// CloudComponentWorkloadReadParam
// 获取云组件的工作负载请求参数
type CloudComponentWorkloadReadParam struct {
	CloudServiceName   string       `binding:"required"` // 云服务名称 require
	CloudComponentName string       `binding:"required"` // 云组件名称  require
	WorkloadType       WorkloadType // 工作负载类型 option
	Namespace          string       // 所在Namespace option
	Cluster            string       `binding:"required"` // 云组件所在汲取 option
}

// CloudComponentWorkload
// 云组件的工作负载
type CloudComponentWorkload struct {
	Namespace    string        `json:"namespace"`            // 工作负载 所在分区
	Name         string        `json:"name"`                 // 工作负载 名称
	CreateTime   time.Time     `json:"createTime,omitempty"` // 创建时间
	State        workloadState `json:"state,omitempty"`      // 工作负载状态  rollingupdate starting started stopping stopped succeed failed unknown
	WorkloadType WorkloadType  `json:"workloadType"`         // 工作负载类型  CronJob DaemonSet Deployment Job Statefulset
}

type workloadState string

const (
	WorkloadRollingUpdate = workloadState("rollingupdate")
	WorkloadStarting      = workloadState("starting")
	WorkloadStarted       = workloadState("started")
	WorkloadStopping      = workloadState("stopping")
	WorkloadStopped       = workloadState("stopped")
	WorkloadSuccess       = workloadState("succeed")
	WorkloadFailed        = workloadState("failed")
	WorkloadRunning       = workloadState("Running")
	WorkloadUnknown       = workloadState("unknown")
)

func GetDeploymentState(deployment appsv1.Deployment) workloadState {
	var expectReplicasPtr *int32
	expectReplicasPtr = deployment.Spec.Replicas
	if expectReplicasPtr == nil {
		return WorkloadUnknown
	}
	var expectReplicas, replicas, unavailableReplicas, readyReplicas int32
	expectReplicas = *expectReplicasPtr
	replicas = deployment.Status.Replicas
	unavailableReplicas = deployment.Status.UnavailableReplicas
	readyReplicas = deployment.Status.ReadyReplicas
	if expectReplicas > 0 && replicas != 0 && replicas > expectReplicas {
		return WorkloadRollingUpdate
	}

	if expectReplicas > 0 && unavailableReplicas > 0 {
		return WorkloadStarting
	}

	if expectReplicas > 0 && unavailableReplicas == 0 && readyReplicas != 0 && readyReplicas == expectReplicas {
		return WorkloadStarted
	}
	if expectReplicas == 0 && readyReplicas > 0 {
		return WorkloadStopping
	}
	if expectReplicas == 0 && replicas == 0 && readyReplicas == 0 {
		return WorkloadStopped
	}
	return WorkloadUnknown
}

func GetDaemonSetState(daemonSet appsv1.DaemonSet) workloadState {
	var numberAvailable, desiredNumberScheduled int32
	numberAvailable = daemonSet.Status.NumberAvailable
	desiredNumberScheduled = daemonSet.Status.DesiredNumberScheduled
	if numberAvailable != 0 && desiredNumberScheduled != 0 && numberAvailable == desiredNumberScheduled {
		return WorkloadStarted
	}
	return WorkloadStarting
}

func GetStatefulSetState(statefulset appsv1.StatefulSet) workloadState {
	var expectReplicasPtr *int32
	expectReplicasPtr = statefulset.Spec.Replicas
	if expectReplicasPtr == nil {
		return WorkloadUnknown
	}
	var expectReplicas, replicas, readyReplicas int32
	expectReplicas = *expectReplicasPtr
	replicas = statefulset.Status.Replicas
	readyReplicas = statefulset.Status.ReadyReplicas
	if expectReplicas > 0 && replicas != 0 && expectReplicas > replicas {
		return WorkloadStarting
	}

	if expectReplicas > 0 && replicas != 0 && expectReplicas == replicas && readyReplicas != 0 && readyReplicas < expectReplicas {
		return WorkloadStarting
	}

	if expectReplicas > 0 && replicas != 0 && expectReplicas == replicas && readyReplicas == 0 {
		return WorkloadStarting
	}

	if expectReplicas > 0 && expectReplicas == readyReplicas {
		return WorkloadStarted
	}

	if expectReplicas == 0 && replicas > 0 {
		return WorkloadStopping
	}

	if expectReplicas == 0 && replicas == 0 {
		return WorkloadStopped
	}
	return WorkloadUnknown

}

func GetJobState(job batchv1.Job) workloadState {
	var active, success, failed int32
	active = job.Status.Active
	success = job.Status.Succeeded
	failed = job.Status.Failed
	if active > 0 {
		return WorkloadRunning
	}

	if success > 0 {
		return WorkloadSuccess
	}

	if failed > 0 {
		return WorkloadFailed
	}

	return WorkloadStopped

}

func GetCronJobState(cronjob batchv1.CronJob) workloadState {
	var suspendPtr *bool
	suspendPtr = cronjob.Spec.Suspend
	if suspendPtr == nil || !*suspendPtr {
		return WorkloadRunning
	}
	return WorkloadStopped
}

// WorkloadType
// 工作负载类型
type WorkloadType string

var (
	// CronJob
	// CronJob 类型的工作负载
	CronJob = WorkloadType("CronJob")

	// DaemonSet
	// DaemonSet 类型的工作负载
	DaemonSet = WorkloadType("DaemonSet")

	// Deployment
	// Deployment 类型的工作负载
	Deployment = WorkloadType("Deployment")

	// Job
	// Job 类型的工作负载
	Job = WorkloadType("Job")

	// Statefulset
	// Statefulset 类型的工作负载
	Statefulset = WorkloadType("Statefulset")
)

type CloudServiceWorkloadList []*CloudComponentWorkload

// Len is the number of elements in the collection.
func (list CloudServiceWorkloadList) Len() int {
	return len(list)
}

// Less reports whether the element with index i
// must sort before the element with index j.
//
// If both Less(i, j) and Less(j, i) are false,
// then the elements at index i and j are considered equal.
// Sort may place equal elements in any order in the final result,
// while Stable preserves the original input order of equal elements.
//
// Less must describe a transitive ordering:
//   - if both Less(i, j) and Less(j, k) are true, then Less(i, k) must be true as well.
//   - if both Less(i, j) and Less(j, k) are false, then Less(i, k) must be false as well.
//
// Note that floating-point comparison (the < operator on float32 or float64 values)
// is not a transitive ordering when not-a-number (NaN) values are involved.
// See Float64Slice.Less for a correct implementation for floating-point values.
func (list CloudServiceWorkloadList) Less(i, j int) bool {
	workload := list[j]
	beforeWorkload := list[i]

	// compare by workload type
	workloadTypeCompare := strings.Compare(string(workload.WorkloadType), string(beforeWorkload.WorkloadType))
	if workloadTypeCompare > 0 {
		return true
	} else if workloadTypeCompare < 0 {
		return false
	}
	// compare by {namespace}/name
	workloadCompareKey := workload.Namespace + "/" + workload.Name
	beforeWorkloadCompareKey := beforeWorkload.Namespace + "/" + beforeWorkload.Name
	workloadKeyCompare := strings.Compare(workloadCompareKey, beforeWorkloadCompareKey)
	if workloadKeyCompare > 0 {
		return true
	}
	return false

}

// Swap swaps the elements with indexes i and j.
func (list CloudServiceWorkloadList) Swap(i, j int) {
	list[i], list[j] = list[j], list[i]
}

type CloudComponentWorkloadPodInstanceReadParam struct {
	CloudServiceName   string       `binding:"required"` // 云服务名称 require
	CloudComponentName string       `binding:"required"` // 云组件名称  require
	WorkloadType       WorkloadType // 工作负载类型 Option
	Namespace          string       // 所在Namespace Option
	WorkloadName       string       // 工作负载名称 Option apply only WorkloadType and Namespace not empty
	Cluster            string       `binding:"required"` // 云组件所在汲取 require

}

// CloudComponentWorkloadPodGrouper
// 获取云服务的Pod instance 信息
type CloudComponentWorkloadPodGrouper struct {
	CloudComponentWorkload
	Pods []CloudComponentWorkloadPodInstance `json:"pods"` // 容器组列表
}

// CloudComponentWorkloadPodInstance
// 云组件Workload 的Pod
type CloudComponentWorkloadPodInstance struct {
	Namespace      string                                       `json:"namespace"`      // Pod所在 命名空间
	Name           string                                       `json:"name"`           // Pod 名称
	Status         PodPhase                                     `json:"status"`         // Pod 状态 Pending Running Succeeded Failed Unknown
	PodIps         []PodIP                                      `json:"podIps"`         // Pod IP列表
	ScheduleNode   string                                       `json:"scheduleNode"`   // Pod 所在节点
	CreateTime     time.Time                                    `json:"createTime"`     // 创建时间
	InitContainers []CloudComponentWorkloadPodInstanceContainer `json:"initContainers"` // 初始化容器列表
	Containers     []CloudComponentWorkloadPodInstanceContainer `json:"containers"`     // 容器列表
}

type PodPhase string

// These are the valid statuses of pods.
const (
	// PodPending means the pod has been accepted by the system, but one or more of the containers
	// has not been started. This includes time before being bound to a node, as well as time spent
	// pulling images onto the host.
	PodPending PodPhase = "Pending"
	// PodRunning means the pod has been bound to a node and all of the containers have been started.
	// At least one container is still running or is in the process of being restarted.
	PodRunning PodPhase = "Running"
	// PodSucceeded means that all containers in the pod have voluntarily terminated
	// with a container exit code of 0, and the system is not going to restart any of these containers.
	PodSucceeded PodPhase = "Succeeded"
	// PodFailed means that all containers in the pod have terminated, and at least one container has
	// terminated in a failure (exited with a non-zero exit code or was stopped by the system).
	PodFailed PodPhase = "Failed"
	// PodUnknown means that for some reason the state of the pod could not be obtained, typically due
	// to an error in communicating with the host of the pod.
	// Deprecated: It isn't being set since 2015 (74da3b14b0c0f658b3bb8d2def5094686d0e9095)
	PodUnknown PodPhase = "Unknown"
)

// PodIP represents a single IP address allocated to the pod.
type PodIP struct {
	// IP is the IP address assigned to the pod
	IP string `json:"ip,omitempty" protobuf:"bytes,1,opt,name=ip"`
}

// CloudComponentWorkloadPodInstanceContainer
// 容器信息
type CloudComponentWorkloadPodInstanceContainer struct {
	Name     string    `json:"name"`     // 容器名称
	LogPaths []LogPath `json:"logPaths"` // 容器日志路径列表
}

// LogPath pod log mount path
type LogPath struct {
	// Name volume name
	Name string `json:"name"`
	// Path mount path
	Path string `json:"path"`
}
