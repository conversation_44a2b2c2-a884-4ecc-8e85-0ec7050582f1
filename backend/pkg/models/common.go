package models

import "fmt"

type FilterRequest struct {
	Selector  string
	PageSize  int
	PageNum   int
	SortName  string
	SortOrder string
	SortFunc  string
}

type PageableResponse[T any] struct {
	Items      []T `json:"items" description:"paging data" translateObject:""`
	TotalCount int `json:"total" description:"total"`
}

// DisplayKVPLevel 级别
// 只读/读写 r/rw
type DisplayKVPLevel string

// DisplayKVP
// key-value at annotation or labels的管理
type DisplayKVP struct {
	Key   string          `json:"key"`
	Value string          `json:"value"`
	Level DisplayKVPLevel `json:"level"`
}

type PodLogRequest struct {
	// Cluster 集群名称
	Cluster string `uri:"cluster" json:"cluster"`
	// Namespace 命名空间
	Namespace string `uri:"namespace" json:"namespace"`
	// Name pod名称
	Name string `uri:"name" json:"name"`
	// SearchText 搜索内容
	SearchText string `form:"searchText" json:"searchText"`
	// Container 容器名
	Container string `form:"container" json:"container"`
	// Follow 是否持续输出日志
	Follow bool `form:"follow" json:"follow"`
	// Previous 是否返回上次终止容器的日志
	Previous bool `form:"previous" json:"previous"`
	// SinceSeconds 当前时间往前推几秒查询
	SinceSeconds *int64 `form:"sinceSeconds" json:"sinceSeconds"`
	// TailLines 最后显示几行
	TailLines *int64 `form:"tailLines" json:"tailLines"`
}

func (p *PodLogRequest) Validate() error {
	if len(p.Cluster) == 0 {
		return fmt.Errorf("cluser不能为空")
	}
	if len(p.Namespace) == 0 {
		return fmt.Errorf("namespace不能为空")
	}
	if len(p.Name) == 0 {
		return fmt.Errorf("podName不能为空")
	}
	if len(p.Container) == 0 {
		return fmt.Errorf("container 不能为空")
	}
	if p.SinceSeconds != nil && *p.SinceSeconds < 0 {
		return fmt.Errorf("sinceSeconds 不能小于0")
	}
	if p.TailLines != nil && (*p.TailLines < 0 || *p.TailLines > 5000) {
		return fmt.Errorf("tailLines 须在 0-5000 之间")

	}
	return nil
}

// 切换时返回的信息
type SwitchObject struct {
	ResourceInstanceId       string `json:"resourceInstanceId"`
	Name                     string `json:"name"`
	ParentResourceInstanceId string `json:"parentResourceInstanceId"`
	ParentPermissionCode     string `json:"parentPermissionCode"`
}

type SwitchObjectResp struct {
	Code         string         `json:"code"`
	Name         string         `json:"name"`
	ResourceList []SwitchObject `json:"resourceList"`
}
