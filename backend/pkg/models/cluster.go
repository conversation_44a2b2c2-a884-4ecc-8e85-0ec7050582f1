package models

type ClusterModel struct {
	Hub           bool     `json:"hub"`
	ClusterId     string   `json:"clusterId"`
	ClusterName   string   `json:"clusterName"`
	Config        *Config  `json:"config"`
	Status        string   `json:"status"`
	Labels        string   `json:"labels"`
	Description   string   `json:"description"`
	NodeCount     int      `json:"nodeCount"`
	K8sVersion    string   `json:"k8sVersion"`
	Architectures []string `json:"architectures"`
}

type ListResponse []ClusterModel

type Config struct {
	Host string `json:"host"`
	// Server requires Bearer authentication. This client will not attempt to use
	// refresh tokens for an OAuth2 flow.
	// TODO: demonstrate an OAuth2 compatible client.
	BearerToken string `json:"token"`
}
