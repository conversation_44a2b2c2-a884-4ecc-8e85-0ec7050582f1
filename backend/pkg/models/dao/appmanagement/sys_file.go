package appmanagement

import "time"

type SysFile struct {
	Id         int64     `gorm:"primaryKey;autoIncrement;comment:'id'"`                                         // ID
	Path       string    `gorm:"type:varchar(512);not null;default:'';comment:'存储路径'"`                          // 存储路径
	Name       string    `gorm:"type:varchar(256);not null;default:'';comment:'附件名称'"`                          // 附件名称
	UniqueKey  string    `gorm:"type:varchar(256);not null;default:'';comment:'存储系统的唯一键'"`                      // 存储系统的唯一键
	Size       string    `gorm:"type:varchar(32);not null;default:'';comment:'大小,单位字节'"`                        // 大小, 单位字节
	Link       string    `gorm:"type:varchar(2048);comment:'在线附件地址'"`                                           // 在线附件地址
	Version    int       `gorm:"not null;default:1;comment:'版本号'"`                                              // 版本号
	CreateTime time.Time `gorm:"not null;default:CURRENT_TIMESTAMP;comment:'创建时间'"`                             // 创建时间
	CreateBy   string    `gorm:"type:varchar(64);comment:'创建人id'"`                                              // 创建人 ID
	UpdateTime time.Time `gorm:"not null;default:CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP;comment:'更新时间'"` // 更新时间
	UpdateBy   string    `gorm:"type:varchar(64);comment:'更新人id'"`                                              // 更新人 ID
	DelFlg     bool      `gorm:"not null;default:0;comment:'逻辑删除 0正常 1删除'"`                                     // 逻辑删除标志
}

// TableName 自定义表名
func (*SysFile) TableName() string {
	return "sys_file"
}
