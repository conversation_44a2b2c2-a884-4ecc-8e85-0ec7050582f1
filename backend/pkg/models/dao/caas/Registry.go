package caas

import "time"

type Registry struct {
	Id                   int32     `gorm:"column:id;primary_key;AUTO_INCREMENT;NOT NULL;comment:'id'"`
	RegistryId           string    `gorm:"column:registry_id;NOT NULL;comment:'制品服务id'"`
	RegistryName         string    `gorm:"column:registry_name;NOT NULL;comment:'制品库名称'"`
	RegistryType         string    `gorm:"column:registry_type;NOT NULL;comment:'制品服务类型'"`
	RegistryProtocol     string    `gorm:"column:registry_protocol;NOT NULL;comment:'访问协议'"`
	RegistryHost         string    `gorm:"column:registry_host;NOT NULL;comment:'制品服务地址'"`
	RegistryPort         int32     `gorm:"column:registry_port;NOT NULL;comment:'制品库端口'"`
	Attributes           string    `gorm:"column:attributes;default:NULL;comment:'制品服务属性'"`
	Annotation           string    `gorm:"column:annotation;default:NULL;comment:'描述'"`
	CreateTime           time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:'创建时间'"`
	UpdateTime           time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:'修改时间'"`
	RegistryMajorVersion int32     `gorm:"column:registry_major_version;NOT NULL;comment:'制品库主版本号'"`
	RegistryMinorVersion int32     `gorm:"column:registry_minor_version;NOT NULL;comment:'制品库次版本号'"`
	IsUpload             int8      `gorm:"column:is_upload;default:0;NOT NULL"`
	IsDelete             int8      `gorm:"column:is_delete;default:0;NOT NULL"`
	MakeTag              int8      `gorm:"column:make_tag;default:0;NOT NULL"`
	AutoCount            int8      `gorm:"column:auto_count;default:0;NOT NULL"`
}

func (r *Registry) TableName() string {
	return "registry"
}
