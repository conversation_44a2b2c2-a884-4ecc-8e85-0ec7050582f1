// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package caas

import (
	"time"

	"gorm.io/gorm"
)

const TableNameBaselineStandard = "baseline_standard"

// BaselineStandard 基线标准表
type BaselineStandard struct {
	ID          int64          `gorm:"column:id;primaryKey;autoIncrement:true;comment:基线标准ID" json:"id"`             // 基线标准ID
	Name        string         `gorm:"column:name;not null;comment:基线标准名称" json:"name"`                              // 基线标准名称
	Description string         `gorm:"column:description;comment:基线标准说明" json:"description"`                         // 基线标准说明
	Builtin     bool           `gorm:"column:builtin;not null;comment:是否内置" json:"builtin"`                          // 是否内置
	GroupID     int64          `gorm:"column:group_id;comment:基线标准分类ID (与分类表关联)" json:"group_id"`                    // 基线标准分类ID (与分类表关联)
	CreateUser  string         `gorm:"column:create_user;comment:创建用户" json:"create_user"`                           // 创建用户
	UpdateUser  string         `gorm:"column:update_user;comment:更新用户" json:"update_user"`                           // 更新用户
	CreateTime  time.Time      `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"` // 创建时间
	UpdateTime  time.Time      `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
	DeleteTime  gorm.DeletedAt `gorm:"column:delete_time;comment:删除时间" json:"delete_time"`                           // 删除时间
}

// TableName BaselineStandard's table name
func (*BaselineStandard) TableName() string {
	return TableNameBaselineStandard
}
