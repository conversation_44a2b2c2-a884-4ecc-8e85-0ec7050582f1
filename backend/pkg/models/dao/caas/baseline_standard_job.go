// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package caas

import (
	"time"
)

const TableNameBaselineStandardJob = "baseline_standard_job"

// BaselineStandardJob mapped from table <baseline_standard_job>
type BaselineStandardJob struct {
	ID                       int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:基线标准作业ID" json:"id"`                            // 基线标准作业ID
	StrategyJobID            int64     `gorm:"column:strategy_job_id;not null;comment:策略作业ID" json:"strategy_job_id"`                         // 策略作业ID
	StrategyID               int64     `gorm:"column:strategy_id;not null;comment:检查策略ID" json:"strategy_id"`                                 // 检查策略ID
	StandardID               int64     `gorm:"column:standard_id;not null;comment:基线标准ID" json:"standard_id"`                                 // 基线标准ID
	ClusterName              string    `gorm:"column:cluster_name;comment:集群名称" json:"cluster_name"`                                          // 集群名称
	BaselineName             string    `gorm:"column:baseline_name;comment:基线资源名称" json:"baseline_name"`                                      // 基线资源名称
	Result                   string    `gorm:"column:result;comment:执行结果" json:"result"`                                                      // 执行结果
	Request                  string    `gorm:"column:request;comment:获取报告构造的请求" json:"request"`                                               // 获取报告构造的请求
	Status                   string    `gorm:"column:status;not null;comment:作业状态" json:"status"`                                             // 作业状态
	Passed                   bool      `gorm:"column:passed;comment:是否通过 (true/false)" json:"passed"`                                         // 是否通过 (true/false)
	Reason                   string    `gorm:"column:reason;comment:任务失败没有通过的yuan" json:"reason"`                                             // 任务失败没有通过的yuan
	Message                  string    `gorm:"column:message;comment:详细的错误信息" json:"message"`                                                 // 详细的错误信息
	CompletedTime            time.Time `gorm:"column:completed_time;comment:完成时间" json:"completed_time"`                                      // 完成时间
	CheckCount               int32     `gorm:"column:check_count;comment:检查项总量" json:"check_count"`                                           // 检查项总量
	UncheckedCount           int32     `gorm:"column:unchecked_count;comment:未检查的检查项数量" json:"unchecked_count"`                               // 未检查的检查项数量
	PassedCount              int32     `gorm:"column:passed_count;comment:通过的检查项数量" json:"passed_count"`                                      // 通过的检查项数量
	CheckUnpassedHighCount   int32     `gorm:"column:check_unpassed_high_count;comment:未通过的检查项 (高危) 数量" json:"check_unpassed_high_count"`     // 未通过的检查项 (高危) 数量
	CheckUnpassedMediumCount int32     `gorm:"column:check_unpassed_medium_count;comment:未通过的检查项 (中危) 数量" json:"check_unpassed_medium_count"` // 未通过的检查项 (中危) 数量
	CheckUnpassedLowCount    int32     `gorm:"column:check_unpassed_low_count;comment:未通过的检查项 (低危) 数量" json:"check_unpassed_low_count"`       // 未通过的检查项 (低危) 数量
	CreateUser               string    `gorm:"column:create_user;comment:创建用户" json:"create_user"`                                            // 创建用户
	UpdateUser               string    `gorm:"column:update_user;comment:更新用户" json:"update_user"`                                            // 更新用户
	CreateTime               time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:作业创建时间" json:"create_time"`                // 作业创建时间
	UpdateTime               time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:作业更新时间" json:"update_time"`                // 作业更新时间
}

// TableName BaselineStandardJob's table name
func (*BaselineStandardJob) TableName() string {
	return TableNameBaselineStandardJob
}
