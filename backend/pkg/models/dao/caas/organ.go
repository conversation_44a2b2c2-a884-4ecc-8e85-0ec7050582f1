package caas

import "time"

type OrganQuota struct {
	Id                int32     `gorm:"column:id;primary_key;AUTO_INCREMENT;NOT NULL;comment:'主键自增id'"`
	OrganId           string    `gorm:"column:organ_id;default:NULL;comment:'组织id'"`
	ClusterName       string    `gorm:"column:cluster_name;default:NULL;comment:'集群名'"`
	NodePoolName      string    `gorm:"column:node_pool_name;default:NULL;comment:'资源池名，非资源池别名，当资源类型为资源池资源时，此值才有意义'"`
	Kind              string    `gorm:"column:kind;default:NULL;comment:'资源类型，例如cpu,memory,gpu_core,gpu_memory,storage...'"`
	Name              string    `gorm:"column:name;default:NULL;comment:'存储的类型，此值只对kind为storage的资源有意义'"`
	Value             string    `gorm:"column:value;default:NULL;comment:'资源的值'"`
	CreateUser        string    `gorm:"column:create_user;default:NULL;comment:'创建的用户'"`
	UpdateUser        string    `gorm:"column:update_user;default:NULL;comment:'修改的用户'"`
	CreateTime        time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:'创建时间'"`
	UpdateTime        time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:'更新时间'"`
	NodePoolAliasName string    `gorm:"column:node_pool_alias_name;default:NULL;comment:'资源池别名'"`
}

func (o *OrganQuota) TableName() string {
	return "organ_quota"
}
