// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package caas

import (
	"time"
)

const TableNameBaselineStrategyStandard = "baseline_strategy_standard"

// BaselineStrategyStandard 基线策略标准关联表
type BaselineStrategyStandard struct {
	ID          int64     `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	StrategyID  int64     `gorm:"column:strategy_id;comment:基线策略ID" json:"strategy_id"`                         // 基线策略ID
	StandardID  int64     `gorm:"column:standard_id;comment:基线标准ID" json:"standard_id"`                         // 基线标准ID
	ClusterName string    `gorm:"column:cluster_name;comment:集群名称" json:"cluster_name"`                         // 集群名称
	CreateUser  string    `gorm:"column:create_user;comment:创建用户" json:"create_user"`                           // 创建用户
	UpdateUser  string    `gorm:"column:update_user;comment:更新用户" json:"update_user"`                           // 更新用户
	CreateTime  time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"` // 创建时间
	UpdateTime  time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
}

// TableName BaselineStrategyStandard's table name
func (*BaselineStrategyStandard) TableName() string {
	return TableNameBaselineStrategyStandard
}
