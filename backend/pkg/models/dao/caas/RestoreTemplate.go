package caas

import (
	"encoding/json"
	"time"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/velero"
	"k8s.io/klog/v2"
)

type RestoreTemplate struct {
	ID            int64      `gorm:"primarykey;column:id;not null;"`                         // 主键ID
	FromCluster   string     `gorm:"column:from_cluster;"`                                   // 来源集群
	ScheduleName  string     `gorm:"column:schedule_name"`                                   //备份策略名称
	TargetCluster string     `gorm:"column:target_cluster;"`                                 // 目标集群
	RestoreType   int        `gorm:"column:restore_type"`                                    //还原类型 0-原集群 1-跨集群
	IsModify      bool       `gorm:"column:is_modify"`                                       //是否修改资源
	Modify        string     `gorm:"column:modify"`                                          //修改资源 json
	Restore       string     `gorm:"column:restore;not null;"`                               // restore恢复json
	CreateUser    string     `gorm:"column:create_user;not null;"`                           // 创建人
	UpdateUser    string     `gorm:"column:update_user;not null;"`                           // 创建人
	CreateTime    *time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;"` // 创建时间
	UpdateTime    *time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP;"` // 更新时间
}

func (*RestoreTemplate) TableName() string {
	return "restore_template"
}

func (r *RestoreTemplate) RevertRestoreTemplate(restore velero.Restore) *velero.RestoreTemplate {
	restoreTemplate := &velero.RestoreTemplate{
		RestoreTemplateId:   restore.RestoreTemplateId,
		RestoreTemplateName: restore.RestoreTemplateName,
		ScheduleName:        r.ScheduleName,
		RestoreType:         r.RestoreType,
		TargetCluster:       r.TargetCluster,
		FromCluster:         r.FromCluster,
		Description:         restore.Description,
		CreateTime:          r.CreateTime.In(time.UTC).Format("2006-01-02T15:04:05Z"),
		BackupsName:         restore.BackupsName,
		Namespaces:          restore.Namespaces,
		IncludeResources:    restore.IncludeResources,
		ExcludeResources:    restore.ExcludeResources,
		IsModify:            r.IsModify,
	}
	if restore.Status == "" {
		restoreTemplate.Status = "Waiting"
	} else {
		restoreTemplate.Status = restore.Status
	}

	if r.IsModify {
		var modify []velero.Modify
		// 将字符串解析回对象
		if err := json.Unmarshal([]byte(r.Modify), &modify); err != nil {
			klog.Errorf("JSON 解析失败: %v", err)
		}
		restoreTemplate.Modifys = modify
	}

	return restoreTemplate
}
