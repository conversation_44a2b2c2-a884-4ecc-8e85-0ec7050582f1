// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package caas

import (
	"time"

	"gorm.io/gorm"
)

const TableNameBaselineStandardGroup = "baseline_standard_group"

// BaselineStandardGroup 基线标准分类表
type BaselineStandardGroup struct {
	ID          int64          `gorm:"column:id;primaryKey;autoIncrement:true;comment:分类ID" json:"id"`               // 分类ID
	Name        string         `gorm:"column:name;not null;comment:分类名称" json:"name"`                                // 分类名称
	Builtin     bool           `gorm:"column:builtin;comment:是否内置类型" json:"builtin"`                                 // 是否内置类型
	Description string         `gorm:"column:description;comment:分类说明" json:"description"`                           // 分类说明
	CreateUser  string         `gorm:"column:create_user;comment:创建用户" json:"create_user"`                           // 创建用户
	UpdateUser  string         `gorm:"column:update_user;comment:更新用户" json:"update_user"`                           // 更新用户
	CreateTime  time.Time      `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"` // 创建时间
	UpdateTime  time.Time      `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
	DeleteTime  gorm.DeletedAt `gorm:"column:delete_time;comment:删除时间" json:"delete_time"`                           // 删除时间
}

// TableName BaselineStandardGroup's table name
func (*BaselineStandardGroup) TableName() string {
	return TableNameBaselineStandardGroup
}
