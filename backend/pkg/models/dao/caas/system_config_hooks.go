package caas

import (
	"time"

	"gorm.io/gorm"
	"harmonycloud.cn/unifiedportal/midware-go/midwares/auth"
)

var _ DatabaseHooks = (*SystemConfig)(nil)

func (s *SystemConfig) BeforeCreate(tx *gorm.DB) (err error) {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		s.CreateUser = user.GetUserName()
	}
	s.CreateTime = time.Now()
	return nil
}

func (s *SystemConfig) BeforeUpdate(tx *gorm.DB) (err error) {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		s.UpdateUser = user.GetUserName()
	}
	s.UpdateTime = time.Now()
	return nil
}

func (s *SystemConfig) BeforeDelete(tx *gorm.DB) (err error) {
	// do nothing
	return nil
}
