// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package caas

import (
	"time"

	"gorm.io/gorm"
)

const TableNameBaselineRule = "baseline_rule"

// BaselineRule 基线检查项表
type BaselineRule struct {
	ID                    int64          `gorm:"column:id;primaryKey;autoIncrement:true;comment:检查项ID" json:"id"`                                    // 检查项ID
	Name                  string         `gorm:"column:name;not null;comment:检查项名称" json:"name"`                                                     // 检查项名称
	Description           string         `gorm:"column:description;comment:检查项描述" json:"description"`                                                // 检查项描述
	Suggestion            string         `gorm:"column:suggestion;comment:检查项建议" json:"suggestion"`                                                  // 检查项建议
	Kind                  string         `gorm:"column:kind;comment:检查项类型 (Innode 内置类型)" json:"kind"`                                                // 检查项类型 (Innode 内置类型)
	Builtin               bool           `gorm:"column:builtin;not null;comment:是否为内置标识" json:"builtin"`                                             // 是否为内置标识
	RiskLevel             string         `gorm:"column:risk_level;comment:风险类型 (高危、中危、低危)" json:"risk_level"`                                        // 风险类型 (高危、中危、低危)
	Version               string         `gorm:"column:version;comment:检查项版本 (对应baseline crd版本，以做兼容)" json:"version"`                                // 检查项版本 (对应baseline crd版本，以做兼容)
	CheckType             string         `gorm:"column:check_type;comment:检查类型，与baseline 的rule类型做映射" json:"check_type"`                              // 检查类型，与baseline 的rule类型做映射
	CheckResourceType     string         `gorm:"column:check_resource_type;comment:受检查的资源类型，如Docker, Kubernetes, ETCD 等" json:"check_resource_type"` // 受检查的资源类型，如Docker, Kubernetes, ETCD 等
	CheckMode             string         `gorm:"column:check_mode;not null;comment:检查方式 命令,文件 (Command/File)" json:"check_mode"`                     // 检查方式 命令,文件 (Command/File)
	NodeRoles             string         `gorm:"column:node_roles;not null;comment:节点角色 (Master/Worker/System)" json:"node_roles"`                   // 节点角色 (Master/Worker/System)
	NodeSelectors         string         `gorm:"column:node_selectors;comment:节点选择器,多个';'分隔" json:"node_selector"`                                   // 节点选择器
	FileType              string         `gorm:"column:file_type;comment:文件类型 (Yaml/Json/TEXT)" json:"file_type"`                                    // 文件类型 (Yaml/Json/TEXT)
	FileLocationMode      string         `gorm:"column:file_location_mode;comment:文件位置模式 (K8sResource/HostPath)" json:"file_location_mode"`          // 文件位置模式 (K8sResource/HostPath)
	FileLocationPath      string         `gorm:"column:file_location_path;comment:文件路径 (当文件位置模式为 HostPath 时)" json:"file_location_path"`             // 文件路径 (当文件位置模式为 HostPath 时)
	K8sResourceAPIVersion string         `gorm:"column:k8s_resource_api_version;comment:K8s 资源的 apiVersion" json:"k8s_resource_api_version"`         // K8s 资源的 apiVersion
	K8sResourceName       string         `gorm:"column:k8s_resource_name;comment:K8s 资源的 name" json:"k8s_resource_name"`                             // K8s 资源的 name
	K8sResourceNamespace  string         `gorm:"column:k8s_resource_namespace;comment:K8s 资源的 namespace" json:"k8s_resource_namespace"`              // K8s 资源的 namespace
	K8sResourceKind       string         `gorm:"column:k8s_resource_kind;comment:K8s 资源的 kind" json:"k8s_resource_kind"`                             // K8s 资源的 kind
	K8sResourceLabels     string         `gorm:"column:k8s_resource_labels;comment:K8s 资源的labels" json:"k8s_resource_labels"`                        // K8s 资源的 labels
	FileMatchContent      string         `gorm:"column:file_match_content;comment:匹配内容" json:"file_match_content"`                                   // 匹配内容
	FileContentID         int64          `gorm:"column:file_content_id;comment:文件ID" json:"file_content_id"`                                         // 文件ID
	FileContentPath       string         `gorm:"column:file_content_path;comment:文件路径" json:"file_content_path"`                                     // 文件路径
	FileContentUniqueKey  string         `gorm:"column:file_content_unique_key;comment:文件唯一键" json:"file_content_unique_key"`                        // 文件唯一键
	FileContentName       string         `gorm:"column:file_content_name;comment:文件名称" json:"file_content_name"`                                     // 文件名称
	FileContentLink       string         `gorm:"column:file_content_link;comment:文件链接地址" json:"file_content_link"`                                   // 文件链接地址
	Command               string         `gorm:"column:command;comment:命令" json:"command"`                                                           // 命令
	CommandMatchType      string         `gorm:"column:command_match_type;comment:命令匹配类型 (模糊/精确)" json:"command_match_type"`                         // 命令匹配类型 (模糊/精确)
	CommandMatchValue     string         `gorm:"column:command_match_value;comment:命令匹配值" json:"command_match_value"`                                // 命令匹配值
	CreateUser            string         `gorm:"column:create_user;comment:创建用户" json:"create_user"`                                                 // 创建用户
	UpdateUser            string         `gorm:"column:update_user;comment:更新用户" json:"update_user"`                                                 // 更新用户
	CreateTime            time.Time      `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`                       // 创建时间
	UpdateTime            time.Time      `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`                       // 更新时间
	DeleteTime            gorm.DeletedAt `gorm:"column:delete_time;comment:删除时间" json:"delete_time"`                                                 // 删除时间
}

// TableName BaselineRule's table name
func (*BaselineRule) TableName() string {
	return TableNameBaselineRule
}
