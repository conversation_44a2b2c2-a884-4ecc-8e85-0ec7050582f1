package caas

// 数据中心表
type DataCenter struct {
	Id      int64  `gorm:"column:id;primary_key;NOT NULL;comment:'数据中心id'" json:"id"`
	Name    string `gorm:"column:name;NOT NULL;comment:'数据中心名称'" json:"name"`
	Remarks string `gorm:"column:remarks;comment:'数据中心说明'" json:"remarks"`
	Code    string `gorm:"column:code;NOT NULL;comment:'数据中心标识'" json:"code"`
}

func (DataCenter) TableName() string {
	return "data_center"
}

// 逻辑单元表
type LogicalUnit struct {
	Id           int64  `json:"id" gorm:"column:id;primary key;NOT NULL;comment:'逻辑单元id'"`
	DataCenterId int64  `json:"dataCenterId" gorm:"column:data_center_id;NOT NULL;comment:'数据中心id'"`
	Name         string `json:"name" gorm:"column:name;NOT NULL;comment:'逻辑单元名称'"`
	Code         string `json:"code" gorm:"column:code;NOT NULL;comment:'逻辑单元标识'"`
	Type         string `json:"type" gorm:"column:type;NOT NULL;comment:'逻辑单元类型'"`
	Territory    string `json:"territory" gorm:"column:territory;comment:'逻辑单元所在区域'"`
	Remarks      string `json:"remarks" gorm:"column:remarks;comment:'逻辑单元说明'"`
}

func (LogicalUnit) TableName() string {
	return "logical_unit"
}

type LogicalInfo struct {
	Id            int64  `gorm:"column:id" json:"id"`
	LogicalUnitId int64  `gorm:"column:logical_unit_id" json:"logicalUnitId"`
	InfoType      string `gorm:"column:info_type" json:"infoType"`
	Info          string `gorm:"column:info" json:"info"`
}

func (LogicalInfo) TableName() string {
	return "logical_unit_info"
}
