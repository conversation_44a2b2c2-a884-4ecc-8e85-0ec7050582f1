// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package caas

import (
	"time"
)

const TableNameBaselineStandardRuleJob = "baseline_standard_rule_job"

// BaselineStandardRuleJob mapped from table <baseline_standard_rule_job>
type BaselineStandardRuleJob struct {
	ID             int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:基线标准检查项作业ID" json:"id"`          // 基线标准检查项作业ID
	StrategyJobID  int64     `gorm:"column:strategy_job_id;not null;comment:策略任务id" json:"strategy_job_id"`                   // 策略任务id
	StandardJobID  int64     `gorm:"column:standard_job_id;not null;comment:基线标准作业ID" json:"standard_job_id"`        // 基线标准作业ID
	StrategyID     int64     `gorm:"column:strategy_id;not null;comment:检查策略ID" json:"strategy_id"`                  // 检查策略ID
	StandardID     int64     `gorm:"column:standard_id;not null;comment:基线标准ID" json:"standard_id"`                  // 基线标准ID
	RuleID         int64     `gorm:"column:rule_id;not null;comment:检查项ID" json:"rule_id"`                           // 检查项ID
	StandardRuleID int64     `gorm:"column:standard_rule_id;comment:基线标准与检查项关联表ID" json:"standard_rule_id"`          // 基线标准与检查项关联表ID
	CheckerName    string    `gorm:"column:checker_name;comment:对应checer_name资源的名称" json:"checker_name"`             // 对应checer_name资源的名称
	MonitorName    string    `gorm:"column:monitor_name;comment:对应monitor资源的名称" json:"monitor_name"`                 // 对应monitor资源的名称
	ClusterName    string    `gorm:"column:cluster_name;comment:集群名称" json:"cluster_name"`                           // 集群名称
	Status         string    `gorm:"column:status;not null;comment:作业状态" json:"status"`                              // 作业状态
	Passed         bool      `gorm:"column:passed;comment:是否通过 (true/false)" json:"passed"`                          // 是否通过 (true/false)
	Reason         string    `gorm:"column:reason;comment:任务失败没有通过的原因" json:"reason"`                                // 任务失败没有通过的原因
	Message        string    `gorm:"column:message;comment:详细的错误信息" json:"message"`                                  // 详细的错误信息
	CompletedTime  time.Time `gorm:"column:completed_time;comment:完成时间" json:"completed_time"`                       // 完成时间
	CreateUser     string    `gorm:"column:create_user;comment:创建用户" json:"create_user"`                             // 创建用户
	UpdateUser     string    `gorm:"column:update_user;comment:更新用户" json:"update_user"`                             // 更新用户
	CreateTime     time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:作业创建时间" json:"create_time"` // 作业创建时间
	UpdateTime     time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:作业更新时间" json:"update_time"` // 作业更新时间
}

// TableName BaselineStandardRuleJob's table name
func (*BaselineStandardRuleJob) TableName() string {
	return TableNameBaselineStandardRuleJob
}
