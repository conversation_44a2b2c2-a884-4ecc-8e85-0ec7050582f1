// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package caas

import (
	"time"
	"gorm.io/gorm"
)

const TableNameBaselineStrategy = "baseline_strategy"

// BaselineStrategy 检查策略表
type BaselineStrategy struct {
	ID                       int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:检查策略ID" json:"id"`                                            // 检查策略ID
	Name                     string    `gorm:"column:name;not null;comment:检查策略名称" json:"name"`                                                             // 检查策略名称
	Description              string    `gorm:"column:description;comment:检查策略说明" json:"description"`                                                        // 检查策略说明
	ExecutionStrategy        string    `gorm:"column:execution_strategy;not null;comment:执行策略 (即时/延迟/定时)" json:"execution_strategy"`                        // 执行策略 (即时/延迟/定时)
	ExecutionDeferredSeconds int64     `gorm:"column:execution_deferred_seconds;comment:延迟执行秒数" json:"execution_deferred_seconds"`                          // 延迟执行秒数
	ExecutionTime            time.Time `gorm:"column:execution_time;comment:执行时间" json:"execution_time"`                                                    // 执行时间
	ExecutionRecurringType   string    `gorm:"column:execution_recurring_type;comment:重复执行类型 Daily, Weekly, Monthly, Cron" json:"execution_recurring_type"` // 重复执行类型 Daily, Weekly, Monthly, Cron
	ExecutionPerDays         int32     `gorm:"column:execution_per_days;comment:每隔几天执行" json:"execution_per_days"`                                          // 每隔几天执行
	ExecutionDaysOfWeek      string    `gorm:"column:execution_days_of_week;comment:周几执行，多个用逗号分隔" json:"execution_days_of_week"`                            // 周几执行，多个用逗号分隔
	ExecutionDaysOfMonth     string    `gorm:"column:execution_days_of_month;comment:每月几号执行，多个用逗号分隔" json:"execution_days_of_month"`                        // 每月几号执行，多个用逗号分隔
	ExecutionCron            string    `gorm:"column:execution_cron;comment:定时执行 CronTab 表达式" json:"execution_cron"`                                        // 定时执行 CronTab 表达式
	ExecutionTimezone        string    `gorm:"column:execution_timezone;comment:执行的默认时区,默认为UTC+08:00" json:"execution_timezone"`                            // 执行的默认时区,默认为UTC+08:00
	ExecutionStartedAt       time.Time `gorm:"column:execution_started_at;default:CURRENT_TIMESTAMP;comment:开始执行时间" json:"execution_started_at"`            // 开始执行时间
	ExecutionStoppedAt       time.Time `gorm:"column:execution_stopped_at;default:CURRENT_TIMESTAMP;comment:结束执行时间" json:"execution_stopped_at"`            // 结束执行时间
	ClusterNames             string    `gorm:"column:cluster_names;comment:集群名称，多个逗号分隔" json:"cluster_names"`                                               // 集群名称，多个逗号分隔
	Enabled                  int32     `gorm:"column:enabled;comment:是否启用策略 (0 关闭, 1 开启)" json:"enabled"`                                                   // 是否启用策略 (0 关闭, 1 开启)
	CreateUser               string    `gorm:"column:create_user;comment:创建用户" json:"create_user"`                                                          // 创建用户
	UpdateUser               string    `gorm:"column:update_user;comment:更新用户" json:"update_user"`                                                          // 更新用户
	CreateTime               time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"`                                // 创建时间
	UpdateTime               time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"`                                // 更新时间
	DeleteTime              gorm.DeletedAt `gorm:"column:delete_time;comment:删除时间" json:"delete_time"`                                                          // 删除时间
}

// TableName BaselineStrategy's table name
func (*BaselineStrategy) TableName() string {
	return TableNameBaselineStrategy
}
