// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package caas

import (
	"time"
)

const TableNameBaselineStrategyJob = "baseline_strategy_job"

// BaselineStrategyJob mapped from table <baseline_strategy_job>
type BaselineStrategyJob struct {
	ID                         int64     `gorm:"column:id;primaryKey;autoIncrement:true;comment:作业ID" json:"id"`                                    // 作业ID
	StrategyID                 int64     `gorm:"column:strategy_id;not null;comment:检查策略ID" json:"strategy_id"`                                     // 检查策略ID
	ClusterNames               string    `gorm:"column:cluster_names;comment:集群名称, 多个以逗号分隔" json:"cluster_names"`                                   // 集群名称, 多个以逗号分隔
	Status                     string    `gorm:"column:status;not null;comment:作业状态 (待执行/执行中等)" json:"status"`                                      // 作业状态 (待执行/执行中等)
	CompletedTime              time.Time `gorm:"column:completed_time;comment:完成时间" json:"completed_time"`                                          // 完成时间
	Passed                     bool      `gorm:"column:passed;comment:是否通过" json:"passed"`                                                          // 是否通过
	Reason                     string    `gorm:"column:reason;comment:是否tong'g" json:"reason"`                                                      // 是否tong'g
	Message                    string    `gorm:"column:message;comment:详细的错误信息" json:"message"`                                                     // 详细的错误信息
	ClusterCount               int32     `gorm:"column:cluster_count;comment:集群总数" json:"cluster_count"`                                            // 集群总数
	ClusterRiskCount           int32     `gorm:"column:cluster_risk_count;comment:有风险的集群数量" json:"cluster_risk_count"`                              // 有风险的集群数量
	ClusterNoRiskCount         int32     `gorm:"column:cluster_no_risk_count;comment:无风险的集群数量" json:"cluster_no_risk_count"`                        // 无风险的集群数量
	ClusterUncheckedCount      int32     `gorm:"column:cluster_unchecked_count;comment:未检查的集群数量" json:"cluster_unchecked_count"`                    // 未检查的集群数量
	ClusterFailedCount         int32     `gorm:"column:cluster_failed_count;comment:检查失败的集群数量" json:"cluster_failed_count"`                         // 检查失败的集群数量
	StandardCount              int32     `gorm:"column:standard_count;comment:基线标准总数" json:"standard_count"`                                        // 基线标准总数
	StandardPassedCount        int32     `gorm:"column:standard_passed_count;comment:通过的基线标准数量" json:"standard_passed_count"`                       // 通过的基线标准数量
	StandardUncheckedCount     int32     `gorm:"column:standard_unchecked_count;comment:未检查的基线标准数量" json:"standard_unchecked_count"`                // 未检查的基线标准数量
	StandardUnpassedCount      int32     `gorm:"column:standard_unpassed_count;comment:未通过的基线标准数量" json:"standard_unpassed_count"`                  // 未通过的基线标准数量
	CheckCount                 int32     `gorm:"column:check_count;comment:检查项总数" json:"check_count"`                                               // 检查项总数
	CheckUncheckedCount        int32     `gorm:"column:check_unchecked_count;comment:未检查的检查项数量" json:"check_unchecked_count"`                       // 未检查的检查项数量
	CheckPassedCount           int32     `gorm:"column:check_passed_count;comment:通过的检查项数" json:"check_passed_count"`                               // 通过的检查项数
	CheckUnpassedCriticalCount int32     `gorm:"column:check_unpassed_critical_count;comment:未通过的检查项 (严重) 数量" json:"check_unpassed_critical_count"` // 未通过的检查项 (严重) 数量
	CheckUnpassedHighCount     int32     `gorm:"column:check_unpassed_high_count;comment:未通过的检查项 (高危) 数量" json:"check_unpassed_high_count"`         // 未通过的检查项 (高危) 数量
	CheckUnpassedMediumCount   int32     `gorm:"column:check_unpassed_medium_count;comment:未通过的检查项 (中危) 数量" json:"check_unpassed_medium_count"`     // 未通过的检查项 (中危) 数量
	CheckUnpassedLowCount      int32     `gorm:"column:check_unpassed_low_count;comment:未通过的检查项 (低危) 数量" json:"check_unpassed_low_count"`           // 未通过的检查项 (低危) 数量
	CreateUser                 string    `gorm:"column:create_user;comment:创建用户" json:"create_user"`                                                // 创建用户
	UpdateUser                 string    `gorm:"column:update_user;comment:更新用户" json:"update_user"`                                                // 更新用户
	CreateTime                 time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:作业创建时间" json:"create_time"`                    // 作业创建时间
	UpdateTime                 time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:作业更新时间" json:"update_time"`                    // 作业更新时间
}

// TableName BaselineStrategyJob's table name
func (*BaselineStrategyJob) TableName() string {
	return TableNameBaselineStrategyJob
}
