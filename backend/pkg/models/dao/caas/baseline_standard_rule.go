// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package caas

const TableNameBaselineStandardRule = "baseline_standard_rule"

// BaselineStandardRule 基线标准与检查项关联表
type BaselineStandardRule struct {
	ID            int64 `gorm:"column:id;primaryKey;autoIncrement:true;comment:关联表ID" json:"id"` // 关联表ID
	RefID         int64 `gorm:"column:ref_id;comment:关联表对应的ID" json:"ref_id"`                    // 关联表对应的ID
	StandardID    int64 `gorm:"column:standard_id;comment:基线标准ID" json:"standard_id"`            // 基线标准ID
	RuleID        int64 `gorm:"column:rule_id;comment:检查项ID" json:"rule_id"`                     // 检查项ID
	RiskLevel            string `gorm:"column:risk_level;comment:风险等级" json:"risk_level"`                                     // 风险等级
	CheckValuePrefix     string `gorm:"column:check_value_prefix;comment:前缀类型 (Input/RegExp/File)" json:"check_value_prefix"` // 前缀类型 (Input/RegExp/File)
	CheckValueContent    string `gorm:"column:check_value_content;comment:检查值 (取决于前缀类型)" json:"check_value_content"`          // 检查值 (取决于前缀类型)
	FileContentID        int64  `gorm:"column:file_content_id;comment:文件ID" json:"file_content_id"`                           // 文件ID
	FileContentPath      string `gorm:"column:file_content_path;comment:文件路径" json:"file_content_path"`                       // 文件路径
	FileContentUniqueKey string `gorm:"column:file_content_unique_key;comment:文件唯一键" json:"file_content_unique_key"`          // 文件唯一键
	FileContentName      string `gorm:"column:file_content_name;comment:文件名称" json:"file_content_name"`                       // 文件名称
	FileContentLink      string `gorm:"column:file_content_link;comment:文件链接地址" json:"file_content_link"`                     // 文件链接地址
}

// TableName BaselineStandardRule's table name
func (*BaselineStandardRule) TableName() string {
	return TableNameBaselineStandardRule
}
