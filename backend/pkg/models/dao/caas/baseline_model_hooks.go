package caas

import (
	"time"

	"gorm.io/gorm"
	"harmonycloud.cn/unifiedportal/midware-go/midwares/auth"
)

var _ DatabaseHooks = (*BaselineRule)(nil)

// BeforeCreate GORM hook, executed before create operation
func (r *BaselineRule) BeforeCreate(tx *gorm.DB) (err error) {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		r.UpdateUser = user.GetUserName()
	}
	r.CreateTime = time.Now()
	return nil
}

// BeforeUpdate GORM hook, executed before update operation
func (r *BaselineRule) BeforeUpdate(tx *gorm.DB) (err error) {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		r.UpdateUser = user.GetUserName()
	}
	r.UpdateTime = time.Now()
	return nil
}

// BeforeDelete GORM hook, executed before delete operation
func (r *BaselineRule) BeforeDelete(tx *gorm.DB) (err error) {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		r.UpdateUser = user.GetUserName()
	}
	// 设置 DeleteTime 字段为当前时间并启用软删除
	r.DeleteTime = gorm.DeletedAt{
		Time:  time.Now(), // 设置当前时间作为删除时间
		Valid: true,       // 设置 Valid 为 true，表示这是一个有效的软删除
	}
	return nil
}

var _ DatabaseHooks = (*BaselineStandard)(nil)

func (s *BaselineStandard) BeforeCreate(tx *gorm.DB) error {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		s.UpdateUser = user.GetUserName()
	}
	s.CreateTime = time.Now()
	return nil
}

func (s *BaselineStandard) BeforeUpdate(tx *gorm.DB) error {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		s.UpdateUser = user.GetUserName()
	}
	s.UpdateTime = time.Now()
	return nil
}

func (s *BaselineStandard) BeforeDelete(tx *gorm.DB) error {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		s.UpdateUser = user.GetUserName()
	}
	s.DeleteTime = gorm.DeletedAt{
		Time:  time.Now(),
		Valid: true,
	}
	return nil
}

var _ DatabaseHooks = (*BaselineStandardGroup)(nil)

// BeforeCreate GORM hook, executed before create operation
func (r *BaselineStandardGroup) BeforeCreate(tx *gorm.DB) (err error) {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		r.CreateUser = user.GetUserName()
	}
	r.CreateTime = time.Now() // 创建时间字段
	return nil
}

// BeforeUpdate GORM hook, executed before update operation
func (r *BaselineStandardGroup) BeforeUpdate(tx *gorm.DB) (err error) {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		r.UpdateUser = user.GetUserName()
	}
	r.UpdateTime = time.Now() // 更新时间字段
	return nil
}

// BeforeDelete GORM hook, executed before delete operation
func (r *BaselineStandardGroup) BeforeDelete(tx *gorm.DB) (err error) {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		r.UpdateUser = user.GetUserName()
	}
	r.DeleteTime = gorm.DeletedAt{
		Time:  time.Now(), // 设置当前时间作为删除时间
		Valid: true,       // 设置 Valid 为 true，表示这是一个有效的软删除
	}
	return nil
}

var _ DatabaseHooks = (*BaselineStrategy)(nil)

// BeforeCreate GORM hook, executed before create operation
func (r *BaselineStrategy) BeforeCreate(tx *gorm.DB) (err error) {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		r.CreateUser = user.GetUserName()
	}
	r.CreateTime = time.Now() // 创建时间字段
	return nil
}

// BeforeUpdate GORM hook, executed before update operation
func (r *BaselineStrategy) BeforeUpdate(tx *gorm.DB) (err error) {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		r.UpdateUser = user.GetUserName()
	}
	r.UpdateTime = time.Now() // 更新时间字段
	return nil
}

// BeforeDelete GORM hook, executed before delete operation
func (r *BaselineStrategy) BeforeDelete(tx *gorm.DB) (err error) {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		r.UpdateUser = user.GetUserName()
	}
	r.DeleteTime = gorm.DeletedAt{
		Time:  time.Now(), // 设置当前时间作为删除时间
		Valid: true,       // 设置 Valid 为 true，表示这是一个有效的软删除
	}
	return nil
}

var _ DatabaseHooks = (*BaselineStrategyJob)(nil)
var _ DatabaseHooks = (*BaselineStandardJob)(nil)
var _ DatabaseHooks = (*BaselineStandardRuleJob)(nil)

// BaselineStrategyJob hooks
func (r *BaselineStrategyJob) BeforeCreate(tx *gorm.DB) (err error) {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		r.CreateUser = user.GetUserName()
	}
	r.CreateTime = time.Now()
	return nil
}

func (r *BaselineStrategyJob) BeforeUpdate(tx *gorm.DB) (err error) {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		r.UpdateUser = user.GetUserName()
	}
	r.UpdateTime = time.Now()
	return nil
}

func (r *BaselineStrategyJob) BeforeDelete(tx *gorm.DB) (err error) {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		r.UpdateUser = user.GetUserName()
	}
	return nil
}

// BaselineStandardJob hooks
func (r *BaselineStandardJob) BeforeCreate(tx *gorm.DB) (err error) {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		r.CreateUser = user.GetUserName()
	}
	r.CreateTime = time.Now()
	return nil
}

func (r *BaselineStandardJob) BeforeUpdate(tx *gorm.DB) (err error) {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		r.UpdateUser = user.GetUserName()
	}
	r.UpdateTime = time.Now()
	return nil
}

func (r *BaselineStandardJob) BeforeDelete(tx *gorm.DB) (err error) {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		r.UpdateUser = user.GetUserName()
	}
	return nil
}

func (r *BaselineStandardRuleJob) BeforeCreate(tx *gorm.DB) (err error) {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		r.CreateUser = user.GetUserName()
	}
	r.CreateTime = time.Now()
	return nil
}

func (r *BaselineStandardRuleJob) BeforeUpdate(tx *gorm.DB) (err error) {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		r.UpdateUser = user.GetUserName()
	}
	r.UpdateTime = time.Now()
	return nil
}

func (r *BaselineStandardRuleJob) BeforeDelete(tx *gorm.DB) (err error) {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		r.UpdateUser = user.GetUserName()
	}
	return nil
}

var _ DatabaseHooks = (*BaselineStrategyStandard)(nil)

// BeforeCreate 钩子，在创建记录前执行
func (b *BaselineStrategyStandard) BeforeCreate(tx *gorm.DB) (err error) {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		b.CreateUser = user.GetUserName()
	}
	b.CreateTime = time.Now()
	return nil
}

// BeforeUpdate 钩子，在更新记录前执行
func (b *BaselineStrategyStandard) BeforeUpdate(tx *gorm.DB) (err error) {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		b.UpdateUser = user.GetUserName()
	}
	b.UpdateTime = time.Now()
	return nil
}

// BeforeDelete 钩子，在删除记录前执行
func (b *BaselineStrategyStandard) BeforeDelete(tx *gorm.DB) (err error) {
	ctx := tx.Statement.Context
	user, _ := auth.GetCurrentUser(ctx)
	if user != nil {
		b.UpdateUser = user.GetUserName()
	}
	return nil
}
