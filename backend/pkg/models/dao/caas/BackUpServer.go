package caas

import (
	"encoding/json"
	"time"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/backupserver"
)

// BackupServer 备份服务器表
type BackupServer struct {
	Id         int64     `gorm:"column:id;primary_key;NOT NULL;comment:'备份服务器的唯一标识符'"`
	Nickname   string    `gorm:"column:nickname;comment:'备份服务器别名'"`
	Config     string    `gorm:"column:config;comment:'备份服务器的配置信息'"`
	Type       string    `gorm:"column:type;comment:'备份服务器的类型'"`
	CreateUser string    `gorm:"column:create_user;default:admin;comment:'创建用户'"`
	UpdateUser string    `gorm:"column:update_user;default:admin;comment:'修改用户'"`
	CreateTime time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:'记录创建时间'"`
	UpdateTime time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:'记录最后更新时间'"`
}

func (b *BackupServer) TableName() string {
	return "backup_server"
}

func (b *BackupServer) RevertS3StorageServersConfig() *backupserver.S3StorageServersConfig {
	var s3StorageServersConfig *backupserver.S3StorageServersConfig
	err := json.Unmarshal([]byte(b.Config), &s3StorageServersConfig)
	if err != nil {
		return &backupserver.S3StorageServersConfig{}
	}
	return s3StorageServersConfig
}

// BackupServerOrganizationBucket 租户与存储桶关联表
type BackupServerOrganizationBucket struct {
	Id              int64     `gorm:"column:id;primary_key;NOT NULL;comment:'主键ID'"`
	OrganId         int64     `gorm:"column:organ_id;NOT NULL;comment:'租户ID'"`
	StorageServerId int64     `gorm:"column:storage_server_id;NOT NULL;comment:'S3服务器ID'"`
	Bucket          string    `gorm:"column:bucket;comment:'存储桶'"`
	CreateUser      string    `gorm:"column:create_user;default:admin;comment:'创建的用户'"`
	UpdateUser      string    `gorm:"column:update_user;default:admin;comment:'修改的用户'"`
	CreateTime      time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:'创建时间'"`
	UpdateTime      time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:'更新时间'"`
}

func (b *BackupServerOrganizationBucket) TableName() string {
	return "backup_server_organization_bucket"
}

type BackupServerBucketProject struct {
	Id              int64     `gorm:"column:id;primary_key;AUTO_INCREMENT;NOT NULL"`
	StorageServerId int64     `gorm:"column:storage_server_id;NOT NULL;comment:'服务器ID'"`
	Bucket          string    `gorm:"column:bucket;NOT NULL;comment:'存储桶'"`
	OrganId         int64     `gorm:"column:organ_id;NOT NULL;comment:'租户ID'"`
	ProjectId       int64     `gorm:"column:project_id;NOT NULL;comment:'项目ID'"`
	CreateUser      string    `gorm:"column:create_user;default:admin;comment:'创建的用户'"`
	UpdateUser      string    `gorm:"column:update_user;default:admin;comment:'修改的用户'"`
	CreateTime      time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:'创建时间'"`
	UpdateTime      time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:'更新时间'"`
}

// TableName 设置表名为 `backup_server_bucket_project`
func (BackupServerBucketProject) TableName() string {
	return "backup_server_bucket_project"
}
