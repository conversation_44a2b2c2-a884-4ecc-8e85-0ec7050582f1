package caas

import (
	"encoding/json"
	"strconv"
	"time"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/velero"
)

type StorageServer struct {
	ID       int64  `gorm:"primarykey;column:id;not null;"` // 主键ID
	NickName string `gorm:"column:nickname;not null;"`      // 显示名称
	//Clusters       string     `gorm:"column:clusters;"`
	Description    string     `gorm:"column:description;"`
	BackupServerId string     `gorm:"column:backup_server_id;"`
	BslName        string     `gorm:"column:bsl_name;"`
	Bucket         string     `gorm:"column:bucket;"`
	CreateUser     string     `gorm:"column:create_user;not null;"`                           // 创建人
	UpdateUser     string     `gorm:"column:update_user;not null;"`                           // 创建人
	CreateTime     *time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;"` // 创建时间
	UpdateTime     *time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP;"` // 更新时间
}

func (*StorageServer) TableName() string {
	return "storage_server"
}

func (s *StorageServer) RevertStorageServer(config string) *velero.StorageServers {
	var s3StorageServersConfig = new(velero.S3StorageServersConfig)

	if config != "" {
		if err := json.Unmarshal([]byte(config), &s3StorageServersConfig); err != nil {
			logger.GetSugared().Errorf("json.Unmarshal failed: %v", err)
		}
	}
	var createdAt, updatedAt time.Time
	if s.CreateTime != nil {
		createdAt = *s.CreateTime
	} else {
		logger.GetSugared().Warn("CreateTime is nil, setting to default time")
		createdAt = time.Now()
	}
	if s.UpdateTime != nil {
		updatedAt = *s.UpdateTime
	} else {
		logger.GetSugared().Warn("UpdateTime is nil, setting to default time")
		updatedAt = time.Now()
	}
	s3StorageServersConfig.BackupStorageLocationName = s.BslName
	s3StorageServersConfig.Bucket = s.Bucket

	return &velero.StorageServers{
		StorageServersId:       strconv.FormatInt(s.ID, 10),
		NickName:               s.NickName,
		Description:            s.Description,
		CreatedAt:              createdAt,
		UpdatedAt:              updatedAt,
		Bucket:                 s.Bucket,
		BackupServerId:         s.BackupServerId,
		S3StorageServersConfig: *s3StorageServersConfig,
	}
}

type StorageLocation struct {
	ID              int64  `gorm:"primarykey;column:id;not null;"` // 主键ID
	StorageServerId int64  `gorm:"column:storage_server_id;"`
	Cluster         string `gorm:"column:cluster;"`
}

func (*StorageLocation) TableName() string {
	return "storage_location"
}
