package caas

import "time"

type ResourceUsage struct {
	Id           int64     `gorm:"column:id;primary_key;"`
	ResourceType string    `grom:"column:resource_type"`
	ResourceId   int64     `grom:"column:Resource_id"`
	ProjectId    int64     `grom:"column:project_id"`
	OrganId      int64     `grom:"column:organ_id"`
	UsageBy      string    `grom:"column:usage_by"`
	IsDeleted    bool      `grom:"column:is_deleted"`
	CreateTime   time.Time `gorm:"column:create_time;default:CURRENT_TIMESTAMP;comment:'记录创建时间'"`
	UpdateTime   time.Time `gorm:"column:update_time;default:CURRENT_TIMESTAMP;comment:'记录最后更新时间'"`
}

func (b *ResourceUsage) TableName() string {
	return "resource_usage"
}
