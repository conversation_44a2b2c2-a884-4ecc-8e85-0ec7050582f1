package caas

type OrganIC struct {
	Id          int32  `gorm:"column:id;primary_key;AUTO_INCREMENT;NOT NULL;comment:'主键自增id'"`
	OrganId     string `gorm:"column:organ_id;default:NULL;comment:'组织id'"`
	ClusterName string `gorm:"column:cluster_name;default:NULL;comment:'集群名'"`
	icType      string `gorm:"column:ic_type"`
	icName      string `gorm:"colunm:ic_name"`
}

func (o *OrganIC) TableName() string {
	return "organ_ic"
}
