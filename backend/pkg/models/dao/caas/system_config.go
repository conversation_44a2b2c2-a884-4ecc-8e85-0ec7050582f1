// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package caas

import (
	"time"
)

const TableNameSystemConfig = "system_config"

// SystemConfig mapped from table <system_config>
type SystemConfig struct {
	ID          int32     `gorm:"column:id;primaryKey;autoIncrement:true;comment:-- part of primary key /*id*/" json:"id"` // -- part of primary key /*id*/
	ConfigName  string    `gorm:"column:config_name;comment:配置名" json:"config_name"`                                       // 配置名
	ConfigValue string    `gorm:"column:config_value" json:"config_value"`
	ConfigType  string    `gorm:"column:config_type;comment:配置类型" json:"config_type"`                                    // 配置类型
	CreateUser  string    `gorm:"column:create_user;comment:创建人" json:"create_user"`                                     // 创建人
	UpdateUser  string    `gorm:"column:update_user;comment:修改人" json:"update_user"`                                     // 修改人
	CreateTime  time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;comment:创建时间" json:"create_time"` // 创建时间
	UpdateTime  time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP;comment:更新时间" json:"update_time"` // 更新时间
}

// TableName SystemConfig's table name
func (*SystemConfig) TableName() string {
	return TableNameSystemConfig
}
