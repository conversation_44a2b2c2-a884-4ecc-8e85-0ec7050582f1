package dao

import "k8s.io/apimachinery/pkg/util/sets"

type Role struct {
	CommonModel
	Name        string  `gorm:"column:name;not null;"`                  // 名称
	Code        string  `gorm:"column:code;not null;"`                  // 编码
	RoleType    int     `gorm:"column:type;"`                           // 1.admin 2.平台角色 3.组织角色 4.资源集角色 5.组织管理员
	IsEditable  bool    `gorm:"column:is_editable;not null;default:0;"` // 是否可改 0不可 1可以
	Description *string `gorm:"column:description;"`                    // 备注
	Annotations *string `gorm:"column:annotations"`                     // 扩展字段
}

func (*Role) TableName() string {
	return "sys_role"
}

type Permission struct {
	CommonModel
	ParentId       int64   `gorm:"column:parent_id;not null;"`         // 父级ID
	Name           string  `gorm:"column:name;not null;"`              // 名称
	Code           string  `gorm:"column:code;not null;"`              // 编码
	PermissionType int     `gorm:"column:type;not null;"`              // 类型 1.菜单 2.页面元素
	Kind           int     `gorm:"column:kind;not null;"`              // 种类 1.平台菜单 2.组织管理菜单 3.应用菜单
	AppId          *int64  `gorm:"column:appId;"`                      // 应用ID
	Icon           *string `gorm:"column:icon;"`                       // 图标
	Method         int     `gorm:"column:method;not null;default:1;"`  // 请求方式 1.GET 2.POST 3.PUT 4.DELETE 5.PATCH
	Url            *string `gorm:"column:url;"`                        // 接口列表
	Visible        *bool   `gorm:"column:visible;not null;default:1;"` //0-不可见 1-可见
	SortId         int     `gorm:"column:sort_id;not null;default:1;"` // 排序号
	Description    *string `gorm:"column:description;"`                // 备注
	Annotations    *string `gorm:"column:annotations"`                 // 扩展字段
}

func (*Permission) TableName() string {
	return "sys_permission"
}

type RolePermission struct {
	Model
	RoleID       int64  `gorm:"column:role_id;"`       // 角色ID
	PermissionID int64  `gorm:"column:permission_id;"` // 权限ID
	OrganID      *int64 // 组织ID
}

func (*RolePermission) TableName() string {
	return "sys_role_permission"
}

type PermissionList []Permission

// AsIdMap
// 以ID为key生成Map
func (arr PermissionList) AsIdMap() map[int64]*Permission {
	var result = make(map[int64]*Permission, len(arr)<<2)
	for _, permission := range arr {
		permission := permission
		result[permission.ID] = &permission
	}
	return result
}

// LeftPermissions
// 获取所有叶子节点
func (arr PermissionList) LeftPermissions() []Permission {
	idSets := sets.New[int64]()
	parentIdSets := sets.New[int64]()
	for _, permission := range arr {
		idSets.Insert(permission.ID)
		parentIdSets.Insert(permission.ParentId)
	}
	childIdSet := sets.New[int64]()
	for _, id := range idSets.UnsortedList() {
		id := id
		if !parentIdSets.Has(id) {
			childIdSet.Insert(id)
		}
	}

	var leafIds []int64 = childIdSet.UnsortedList()
	var result = make([]Permission, 0, len(leafIds))
	idMap := arr.AsIdMap()
	for _, id := range leafIds {
		permission, exist := idMap[id]
		if exist {
			result = append(result, *permission)
		}
	}

	return result
}
