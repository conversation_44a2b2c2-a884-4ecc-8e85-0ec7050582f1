#!/bin/bash
# 生成gorm结构体
path="gen.yaml"
export GOPROXY="https://goproxy.cn,direct"
# 检查 gentool 是否已安装
if ! command -v gentool &> /dev/null; then
    echo "gentool 未安装，正在安装..."

    go install gorm.io/gen/tools/gentool@latest
else
    echo "gentool 已安装，跳过安装步骤。"
fi

# 运行 gentool，根据 gen.yaml 配置文件生成代码
gentool -c "$path"
echo "代码生成中，请稍候..."

# 提取 withQuery 的值
withQuery=$(grep 'withQuery:' "$path" | awk -F': ' '{print $2}' | tr -d '"')

# 提取 outPath 的值
outPath=$(grep 'outPath:' "$path" | awk -F': ' '{print $2}' | tr -d '"')

# 检查是否成功提取 outPath
if [ -z "$outPath" ]; then
    echo "未能从配置文件中提取 outPath 的值。"
    exit 1
fi

# 根据 withQuery 的值决定是否删除 query 目录
if [ "$withQuery" == "false" ]; then
    if [ -d "$outPath" ]; then
        echo "withQuery 为 false，删除目录 $outPath 下的内容..."
        rm -rf "$outPath"
    else
        echo "目录 $outPath 不存在，无需删除。"
    fi
else
    echo "withQuery 为 true，保留目录 $outPath。"
fi

# 提取 modelPkgName 的值
modelPkgName=$(grep 'modelPkgName:' "$path" | awk -F': ' '{print $2}' | tr -d '"')

# 检查提取是否成功
if [ -z "$modelPkgName" ]; then
    echo "未能从配置文件中提取 modelPkgName 的值。"
    exit 1
fi

# 输出提取的 modelPkgName
echo "提取的 modelPkgName 值为: $modelPkgName"

# 遍历 .gen.go 文件并将其改名为 .go
find "$modelPkgName" -type f -name "*.gen.go" | while read -r file; do
    mv "$file" "${file%.gen.go}.go"
done

echo "代码生成完成！"
