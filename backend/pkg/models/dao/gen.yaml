# 数据库连接配置
database:
  # 数据库连接字符串（DSN），包含用户名、密码、主机地址、端口和数据库名称
  # 格式：username:password@tcp(host:port)/dbname?charset=utf8mb4&parseTime=True&loc=Local
  dsn: "root:Hc@Cloud01@tcp(*************:30306)/caas?charset=utf8mb4&parseTime=True&loc=Local"

  # 数据库类型，支持 mysql、postgres、sqlite、sqlserver 等
  db: "mysql"

  # 需要生成代码的数据表列表
  # 这里指定了 "test" 表，您可以添加多个表名
  # 软删除 需要自己修改表结构

  tables:
#    - "baseline_rule"
    #- "baseline_standard"
    #- "baseline_standard_job"
    #- "baseline_standard_group"
    - "baseline_standard_rule"
    - "baseline_standard_rule_job"
    #- "baseline_strategy"
    #- "baseline_strategy_job"
    #- "baseline_strategy_standard"
  # 生成的模型代码的包名
  # 这里设置为 "./baseline"，表示模型代码将生成在该目录下
  modelPkgName: "./caas"

  # 生成的查询代码的输出路径
  # 这里设置为 "./baseline/query"，表示查询代码将生成在该目录下
  outPath: "./caas/query"

  # 是否生成query
  withQuery: false

  # 是否为查询代码生成单元测试
  # 设置为 false 表示不生成单元测试
  withUnitTest: false

  # 当字段允许为 NULL 时，是否使用指针类型
  # 设置为 false 表示不使用指针类型
  fieldNullable: false

  # 是否为字段生成 GORM 的索引标签
  # 设置为 false 表示不生成索引标签
  fieldWithIndexTag: false

  # 是否为字段生成 GORM 的类型标签
  # 设置为 false 表示不生成类型标签
  fieldWithTypeTag: false
