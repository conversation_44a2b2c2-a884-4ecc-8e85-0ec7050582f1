package dao

type ResourceTranslateConfig struct {
	ID           int64  `gorm:"primarykey;column:id;not null;"`                                   // 主键ID
	GroupName    string `gorm:"column:group_name;not null;uniqueIndex:table_key_translation;"`    // 那个对象组
	UniqueValue  string `gorm:"column:unique_value;not null;uniqueIndex:table_key_translation;"`  // 翻译的对象的instance_id
	LanguageCode string `gorm:"column:language_code;not null;uniqueIndex:table_key_translation;"` // 语种code
	Property     string `gorm:"column:property;not null;uniqueIndex:table_key_translation;"`      // 记录需要翻译的字段
	Translation  string `gorm:"column:translation;not null;"`                                     // 翻译
}

func (*ResourceTranslateConfig) TableName() string {
	return "sys_resource_translate_config"
}

type RegexResourceConfig struct {
	ID        int64  `gorm:"primarykey;column:id;not null;"`                      // 主键ID
	GroupName string `gorm:"column:group_name;not null;uniqueIndex:group_regex;"` // 那个正则组
	Regex     string `gorm:"column:regex;not null;uniqueIndex:group_regex;"`      // 那个正则表达式
}

func (*RegexResourceConfig) TableName() string {
	return "sys_regex_resource_config"
}

type RegexResourceTranslateConfig struct {
	ID           int64  `gorm:"primarykey;column:id;not null;"`                                   // 主键ID
	RegexID      int64  `gorm:"column:regex_id;not null;uniqueIndex:group_regex_translate;"`      // 那个对象组
	LanguageCode string `gorm:"column:language_code;not null;uniqueIndex:group_regex_translate;"` // 那个对象组
	Translation  string `gorm:"column:translation;not null;"`                                     // 那个对象组
}

func (*RegexResourceTranslateConfig) TableName() string {
	return "sys_regex_resource_translate_config"
}
