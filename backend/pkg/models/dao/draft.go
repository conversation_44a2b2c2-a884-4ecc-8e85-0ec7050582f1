package dao

type UserDraft struct {
	ID     int64  `gorm:"primarykey;column:id;not null;"` // 主键ID
	UserId int64  `gorm:"column:user_id;not null;uniqueIndex:user_id_draft_type_unique_index;"`
	Type   string `gorm:"column:draft_type;not null;uniqueIndex:user_id_draft_type_unique_index;"`
	Data   []byte `gorm:"column:draft_data;not null;"`
}

func (ud *UserDraft) WithData(data []byte) *UserDraft {
	ud.Data = data
	return ud
}

func (ud *UserDraft) TableName() string {
	return "sys_user_draft"
}
