package dao

import "time"

type CommonModel struct {
	ID         int64      `gorm:"primarykey;column:id;not null;"`                         // 主键ID
	CreateBy   string     `gorm:"column:create_by;not null;"`                             // 创建人
	CreateTime *time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;"` // 创建时间
	UpdateBy   string     `gorm:"column:update_by;not null;"`                             // 更新人
	UpdateTime *time.Time `gorm:"column:update_time;not null;default:CURRENT_TIMESTAMP;"` // 更新时间
	IsDelete   bool       `gorm:"column:is_deleted;not null;default:0;"`                  // 逻辑删除 0正常 1删除
	Status     int        `gorm:"column:status;not null;default:0;"`                      // 状态 0正常 1锁定
	Version    int        `gorm:"column:version;not null;"`                               // 乐观锁
}

type Model struct {
	ID         int64      `gorm:"primarykey;column:id;not null;"`                         // 主键ID
	CreateBy   string     `gorm:"column:create_by;not null;"`                             // 创建人
	CreateTime *time.Time `gorm:"column:create_time;not null;default:CURRENT_TIMESTAMP;"` // 创建时间
}
