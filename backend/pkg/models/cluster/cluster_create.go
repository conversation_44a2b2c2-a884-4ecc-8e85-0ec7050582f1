package cluster

import (
	"fmt"
	"math/big"
	"net"
	"regexp"
	"strings"

	installerv1alpha1 "harmonycloud.cn/unifiedportal/cloudservice-operator/api/installer/v1alpha1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	database_aop "harmonycloud.cn/unifiedportal/translate-sdk-golang/database-aop"
)

func init() {
	database_aop.GroupInfo(CreateProcessResponse{}, database_aop.TranslateGroupInfo{
		Name:          "olympus-cluster-create-group",
		UniqueKeyName: "Code",
	})
	database_aop.GroupInfo(CreateProcessStepResponse{}, database_aop.TranslateGroupInfo{
		Name:          "olympus-cluster-create-step",
		UniqueKeyName: "Code",
	})
}

type CreateLogQueryRequest struct {
	Name     string  `json:"clusterName" uri:"clusterName" binding:"required"`
	StepName *string `json:"stepName" uri:"stepName"`
}

// RegistryType
// 表示镜像仓库的类型
type RegistryType string

var (
	// RegistryTypeDefault
	// 表示用户未勾选自定义仓库 len(Registries) = 0
	RegistryTypeDefault RegistryType = "default"
	// RegistryTypeCustom
	// 表示已勾选 自定义仓库  len(Registries) = 1
	RegistryTypeCustom RegistryType = "custom"
)

// CreateRequest
// 创建集群请求表单
type CreateRequest struct {
	ClusterName              string                          `json:"clusterName" example:"cluster-create-test-name" binding:"required,kubernetes_instance_name"` // 集群名称
	StartFromFailed          bool                            `json:"startFromFailed,omitempty" example:"false"`                                                  // 失败从失败处开始
	Reset                    bool                            `json:"reset,omitempty" example:"false"`                                                            // 是否进行集群重置 若为true，则忽略startFromFailed，若为false，则看startFromFailed的值是true还是false
	KubernetesVersion        string                          `json:"kubernetesVersion" example:"1.21.5" binding:"required,not_trim_space_empty"`                 // k8s 版本信息
	KubernetesCRIVersion     string                          `json:"kubernetesCRIVersion" example:"Docker-v19.03.15" binding:"required,not_trim_space_empty"`    // k8s CRI信息
	Labels                   map[string]string               `json:"labels"`                                                                                     // 标签
	Description              string                          `json:"description" example:"这是创建集群的描述信息"`                                                          // 描述
	Registry                 *CreateRegistryConfigRequest    `json:"registry"`                                                                                   // 制品服务信息
	HubClusterIngressAddress string                          `json:"hubClusterIngressAddress" example:"***********" binding:"required,ipv4ipv6"`                 // 管理集群 ingress访问地址
	StellarisComponent       CreateStellarisComponentRequest `json:"stellarisComponent"`                                                                         // stellaris component 信息
	NodeConfigs              CreateNodeConfigInfoRequest     `json:"nodeConfigs"`                                                                                // 节点配置
	NetworkConfigs           CreateNetworkConfigRequest      `json:"networkConfigs"`                                                                             // 网络配置
	CRI                      CRIType                         `json:"cri"`
}

// CreateRegistryConfigRequest
// 创建集群请求Registry信息
type CreateRegistryConfigRequest struct {
	Protocol string `json:"protocol" example:"http" binding:"required,protocol_web"`       // 协议
	Address  string `json:"address" example:"test.harbor.work" binding:"required,address"` // 地址
	Port     int    `json:"port" example:"80" binding:"required,port"`                     // 端口
}

// CreateStellarisComponentRequest
// 创建集群请求 Stellaris 信息
type CreateStellarisComponentRequest struct {
	Address        string  `json:"address" example:"*********" binding:"required,address"` // 地址
	Port           int     `json:"port" example:"8686" binding:"required,port"`            // 端口
	StandbyAddress *string `json:"standbyAddress" example:"*********" binding:"address"`   // 备 - 地址
	StandbyPort    *int    `json:"standbyPort" example:"8686" binding:"port"`              // 备 - 端口
}

type NodeConfigType string

func ParseNodeConfigType(str string) (NodeConfigType, error) {
	switch str {
	case string(NodeConfigTypeAllInOne):
		return NodeConfigTypeAllInOne, nil
	case string(NodeConfigTypeMinimizeHA):
		return NodeConfigTypeMinimizeHA, nil
	case string(NodeConfigTypeStandardNoneHA):
		return NodeConfigTypeStandardNoneHA, nil
	case string(NodeConfigTypeStandardHA):
		return NodeConfigTypeStandardHA, nil
	}
	return "", errors.NewFromCodeWithMessage(errors.Var.NodeConfigTypeIllegal, str)
}

var (
	// NodeConfigTypeAllInOne
	// All-in-One
	NodeConfigTypeAllInOne NodeConfigType = "AllInOne"
	// NodeConfigTypeMinimizeHA
	// 最小化高可用
	NodeConfigTypeMinimizeHA NodeConfigType = "MinimizeHA"
	// NodeConfigTypeStandardNoneHA
	// 标准非高可用
	NodeConfigTypeStandardNoneHA NodeConfigType = "StandardNoneHA"
	// NodeConfigTypeStandardHA
	// 标准高可用
	NodeConfigTypeStandardHA NodeConfigType = "StandardHA"
)

// CreateNodeConfigInfoRequest
// 添加节点表单
type CreateNodeConfigInfoRequest struct {
	NodeConfigType NodeConfigType              `json:"type" example:"AllInOne" binding:"required"` // 节点配置类型 支持值  AllInOne(All-in-One) | MinimizeHA(最小化高可用) | StandardNoneHA(标准非高可用) |  StandardHA(标准高可用) 由前端同学进行控制
	Nodes          CreateNodeConfigListRequest `json:"nodes" binding:"dive" `                      // 节点列表
}

type NodeRole string

var (
	Master NodeRole = "master"
	Worker NodeRole = "worker"
)

// CreateNodeConfigListRequest
// 添加节点列表
type CreateNodeConfigListRequest []CreateNodeConfigRequest

func (arr CreateNodeConfigListRequest) HasAutoStorageNode() bool {
	for _, node := range arr {
		if node.Storage.Type == NodeStorageTypeAuto {
			return true
		}
	}
	return false
}

func (arr CreateNodeConfigListRequest) Master() CreateNodeConfigListRequest {
	var masterNode CreateNodeConfigListRequest
	for _, item := range arr {
		item := item
		if item.Role == Master {
			masterNode = append(masterNode, item)
		}
	}
	return masterNode
}

// CreateNodeConfigRequest
// 添加节点信息
type CreateNodeConfigRequest struct {
	Role       NodeRole           `json:"role" example:"master" binding:"required"`           // 节点角色 支持master、worker
	SystemNode bool               `json:"systemNode" example:"true"`                          // 是否为系统节点
	Ip         string             `json:"ip" example:"*********" binding:"required,ipv4ipv6"` // 节点IP
	Port       int                `json:"port" example:"22" binding:"required,port"`          // 节点端口
	Auth       *NodeAuthRequest   `json:"auth" binding:"required"`                            // 认证信息
	SupportGpu bool               `json:"supportGpu" example:"false"`                         // 是否支持GPU
	IsDefault  bool               `json:"isDefault" example:"false"`                          // 记录是否为默认节点 用于前端回显
	Storage    NodeStorageRequest `json:"storage"`                                            // 节点存储配置
}

type NodeStorageType string

var (
	NodeStorageTypeAuto   NodeStorageType = "auto"
	NodeStorageTypeManual NodeStorageType = "manual"
)

type NodeStorageRequest struct {
	Type     NodeStorageType      `json:"type" binding:"nodeStorage" example:"auto"` // 节点存储类型 支持 auto、manual
	DiskPath *NodeDiskPathRequest `json:"diskPath"`                                  // 若节点存储类型为 manual 时填写，为etcd、docker、kubelet、system 的表单
}

type NodeDiskPathRequest struct {
	// todo 实现filePathBinding，若部位空 则进行校验
	//ETCD    string `json:"etcd,omitempty" binding:"filePaths"`    // 表示ETCD挂载盘位置
	//Docker  string `json:"docker,omitempty" binding:"filePaths"`  // 表示Docker挂载盘位置
	//Kubelet string `json:"kubelet,omitempty" binding:"filePaths"` // 表示Kubelet挂载盘位置
	//System  string `json:"system,omitempty" binding:"filePaths"`  // 表示System挂载盘位置

	ETCD    string `json:"etcd,omitempty"`     // 表示ETCD挂载盘位置
	Docker  string `json:"docker,omitempty"`   // 表示Docker挂载盘位置
	Kubelet string `json:"kubelet,omitempty" ` // 表示Kubelet挂载盘位置
	System  string `json:"system,omitempty"`   // 表示System挂载盘位置
}

type NodeAuthType string

var (
	UserNameAndPasswordAuthType NodeAuthType = "username_password"
)

type NodeAuthRequest struct {
	AuthType NodeAuthType           `json:"authType" example:"username_password"` // 认证类型 目前支持 username_password
	Param    map[string]interface{} `json:"param" binding:"required"`             // 认证类型对应的参数 {"username":"用户名","password":"密码","authorizationPassword":"提权密码"}
}

type NodeAuthParamRequest interface {
	GetAuthParamType() NodeAuthType
}

// NodeAuthUsernameAndPasswordParamRequest
// 基于账号或密码的节点认证需求
type NodeAuthUsernameAndPasswordParamRequest struct {
	Username              string  `json:"username"`              // 用户名
	Password              string  `json:"password"`              // 密码
	AuthorizationPassword *string `json:"authorizationPassword"` // 提权密码
}

func (request NodeAuthUsernameAndPasswordParamRequest) Validate() error {
	if request.Username == "" {
		return errors.NewFromCode(errors.Var.IllegalNodeUsername)
	}
	if request.Password == "" {
		return errors.NewFromCode(errors.Var.IllegalNodePassword)
	}
	return nil
}

func (t *NodeAuthUsernameAndPasswordParamRequest) GetAuthParamType() NodeAuthType {
	return UserNameAndPasswordAuthType
}

type ProleType string

var (
	ProleTypeHub      ProleType = "hub"
	ProleTypeBusiness ProleType = "business"
)

func (p ProleType) In(clusterProleList []ProleType) bool {
	for _, clusterProle := range clusterProleList {
		if clusterProle == p {
			return true
		}
	}
	return false
}

type CNIType string

var (
	CNITypeCalico  CNIType = "calico"
	CNITypeMacvlan CNIType = "macvlan"
	CNITypeKubeOVM CNIType = "kube-ovn"
)

// CreateNetworkConfigRequest
// 集群创建网络配置
type CreateNetworkConfigRequest struct {
	CNIs         map[CNIType]CreateCNIConfigRequest `json:"cnis"`         // CNI 配置信息 map key 可选值 calico｜macvlan｜kube-ovn
	ApiServerVIP *CreateApiServerVipConfigRequest   `json:"apiServerVIP"` // APIServer Vip配置
	LoadBalance  *CreateLbConfigRequest             `json:"loadBalance"`  // 负载均衡配置
}

func (req CreateNetworkConfigRequest) CNITypeList() []CNIType {
	var res = make([]CNIType, 0, len(req.CNIs))
	for k, _ := range req.CNIs {
		res = append(res, k)
	}
	return res
}

type CreateCNIConfigRequest struct {
	Ipv4Param map[string]interface{} `json:"ipv4"` // IPv4 类型参数 calico: {"cidr":"10.0.0.0/24"} macvlan: {"networkCard":"eth0","startIp":"10.0.0.0","endIp":"**********","gateway":"********","mask":"24","vlanId":"10","reservedIp":"*********-*********"} kube-ovn: {"networkCard":"绑定的网卡名称"}
	Ipv6Param map[string]interface{} `json:"ipv6"` // IPv6 类型参数
}

type CreateCNIConfigParamRequest interface {
	GetCNIType() CNIType
	Validator() error
}

// CreateCNIConfigCalicoParamRequest
// calico 类型的CNI type 请求表单
type CreateCNIConfigCalicoParamRequest struct {
	CIDR string `json:"cidr"` // cidr地址
}

func (cni *CreateCNIConfigCalicoParamRequest) GetCNIType() CNIType {
	return CNITypeCalico
}
func (cni *CreateCNIConfigCalicoParamRequest) Validator() error {
	_, _, err := net.ParseCIDR(cni.CIDR)
	if err != nil {
		return errors.NewFromCode(errors.Var.CIDRIllegal)
	}
	return nil
}

// CreateCNIConfigMacvlanParamRequest
// Macvlan类型的CNI type 请求表单
type CreateCNIConfigMacvlanParamRequest struct {
	StartIP     string `json:"startIp"`     // 起始IP
	EndIP       string `json:"endIp"`       // 结束IP
	Gateway     string `json:"gateway"`     // 网关
	Mask        int    `json:"mask"`        // 掩码长度
	VlanId      int    `json:"vlanId"`      // vlanID
	NetworkCard string `json:"networkCard"` // 网卡
	ReservedIp  string `json:"reservedIp"`  // 保留IP
}

var reservedIpRegex = regexp.MustCompile("^([a-zA-Z0-9:.]+( )*~( )*[a-zA-Z0-9:.]+)(( )*,( )*([a-zA-Z0-9:.]+( )*~( )*[a-zA-Z0-9:.]+))*$")

func (cni *CreateCNIConfigMacvlanParamRequest) GetCNIType() CNIType {
	return CNITypeMacvlan
}
func (cni *CreateCNIConfigMacvlanParamRequest) Validator() error {

	if strings.EqualFold(strings.TrimSpace(cni.NetworkCard), "") {
		return errors.NewFromCode(errors.Var.NetworkCardEmpty)
	}

	parseIpFunc := func(ipStr string) (net.IP, error) {
		ip := net.ParseIP(ipStr)
		if ip == nil {
			return nil, errors.NewFromCodeWithMessage(errors.Var.IPIllegal, ipStr)
		}
		return ip, nil
	}

	// 先进行 startIP、endIp、gateway 的 IP 校验
	var start, end, gateway net.IP
	var parseIpErr error
	// start ip 校验
	if start, parseIpErr = parseIpFunc(cni.StartIP); parseIpErr != nil {
		return parseIpErr
	}
	// end ip 校验
	if end, parseIpErr = parseIpFunc(cni.EndIP); parseIpErr != nil {
		return parseIpErr
	}
	// gateway ip 校验
	if gateway, parseIpErr = parseIpFunc(cni.Gateway); parseIpErr != nil {
		return parseIpErr
	}
	// end ip 必须在 startIP 之后
	var startIPBigint, endIpBigInt *big.Int
	startIPBigint = ipToIntFunc(start)
	endIpBigInt = ipToIntFunc(end)
	if startIPBigint.Cmp(endIpBigInt) >= 0 {
		return errors.NewFromCode(errors.Var.StartIpEndIpError)
	}

	// 根据 start、mask 生成 CIDR（物理大网段），end、gateway 需在大网段内
	startCidr := fmt.Sprintf("%s/%d", cni.StartIP, cni.Mask)
	_, cidr, err := net.ParseCIDR(startCidr)
	if err != nil {
		return errors.NewFromCodeWithMessage(errors.Var.NetworkMaskError, fmt.Sprintf("network mask = %d", cni.Mask))
	}
	// end ip 在 cidr 内
	if !cidr.Contains(end) {
		return errors.NewFromCodeWithMessage(errors.Var.IPMaskError, cni.EndIP)
	}

	// gateway 在 cidr 内
	if !cidr.Contains(gateway) {
		return errors.NewFromCodeWithMessage(errors.Var.IPMaskError, cni.Gateway)
	}
	reservedIpList, err := cni.ReservedIpList()
	if err != nil {
		return err
	}
	if len(reservedIpList) != 0 {
		checkReservedIpFunc := func(cidr *net.IPNet, startInt, endInt *big.Int, reservedIp []string) error {
			rLeft := net.ParseIP(reservedIp[0])
			rRight := net.ParseIP(reservedIp[1])
			// left 和 right 在 网段内
			if !cidr.Contains(rLeft) || !cidr.Contains(rRight) {
				return errors.NewFromCodeWithMessage(errors.Var.ReservedMaskError, fmt.Sprintf("%s~%s", reservedIp[0], reservedIp[1]))
			}
			// left 和 right 在 start 和 end 之间
			if ipToIntFunc(rLeft).Cmp(startInt) < 0 || ipToIntFunc(rRight).Cmp(endInt) > 0 {
				return errors.NewFromCodeWithMessage(errors.Var.ReservedNotInStartAndEnd, fmt.Sprintf("%s~%s", reservedIp[0], reservedIp[1]))
			}
			return nil
		}
		for _, item := range reservedIpList {
			if err := checkReservedIpFunc(cidr, startIPBigint, endIpBigInt, item); err != nil {
				return err
			}
		}
	}

	return nil
}

func (cni *CreateCNIConfigMacvlanParamRequest) ReservedIpList() ([][]string, error) {
	return parseReservedIpList(cni.ReservedIp)
}

// CreateCNIConfigKubeOVNParamRequest
// KubeOVM类型的CNI type 请求表单
type CreateCNIConfigKubeOVNParamRequest struct {
	NetworkCard string `json:"networkCard"` // 网卡
}

func (cni *CreateCNIConfigKubeOVNParamRequest) GetCNIType() CNIType {
	return CNITypeKubeOVM
}

func (cni *CreateCNIConfigKubeOVNParamRequest) Validator() error {
	if strings.EqualFold(strings.TrimSpace(cni.NetworkCard), "") {
		return errors.NewFromCode(errors.Var.NetworkCardEmpty)
	}
	return nil
}

func parseReservedIpList(reservedIp string) ([][]string, error) {
	var reservedIpList [][]string
	reservedIp = strings.TrimSpace(reservedIp)
	if strings.EqualFold(reservedIp, "") {
		return reservedIpList, nil
	}
	if !reservedIpRegex.Match([]byte(reservedIp)) {
		return nil, errors.NewFromCodeWithMessage(errors.Var.ReservedIpFormatError, reservedIp)
	}
	ipSecList := strings.Split(reservedIp, ",")
	for _, ipSec := range ipSecList {
		ipSec := strings.TrimSpace(ipSec)
		ipAddrList := strings.Split(ipSec, "~")
		reservedIpList = append(reservedIpList, []string{strings.TrimSpace(ipAddrList[0]), strings.TrimSpace(ipAddrList[1])})
	}
	// ip 校验
	for _, reservedIpSec := range reservedIpList {
		startIp := reservedIpSec[0]
		endIp := reservedIpSec[1]
		start := net.ParseIP(startIp)
		end := net.ParseIP(endIp)
		if start == nil || end == nil {
			return nil, errors.NewFromCodeWithMessage(errors.Var.ReservedIpFormatError, "ip illegal")
		}
		// start 必须 小于 end
		startInt := ipToIntFunc(start)
		endInt := ipToIntFunc(end)
		if startInt.Cmp(endInt) > 0 {
			return nil, errors.NewFromCodeWithMessage(errors.Var.ReservedIpFormatError, "start ip must <= end ip")
		}
	}
	return reservedIpList, nil
}

func ipToIntFunc(ip net.IP) *big.Int {
	ip = ip.To16()
	ipInt := new(big.Int).SetBytes(ip)
	return ipInt
}

// CreateApiServerVipConfigRequest
// 集群创建ApiServerVip 配置
type CreateApiServerVipConfigRequest struct {
	Protocol    string `json:"protocol" example:"http" binding:"required,protocol_web"` // 协议
	Address     string `json:"address" example:"*********" binding:"required,ipv4ipv6"` // 地址
	Port        int    `json:"port" example:"6443" binding:"required,port"`             // 端口
	Mask        int    `json:"mask" example:"24" binding:"required"`                    // 掩码
	NetworkCard string `json:"networkCard" example:"eth0" binding:"required"`           // 网卡
}

func (req CreateApiServerVipConfigRequest) AsVIP() string {
	return fmt.Sprintf("%s/%d", req.Address, req.Mask)
}

// CreateLbConfigRequest
// 集群创建 负载均衡配置
type CreateLbConfigRequest struct {
	Address     string `json:"address" example:"***********" binding:"required,ipv4ipv6"` // 地址
	Mask        int    `json:"mask" example:"24" binding:"required"`                      // 掩码
	NetworkCard string `json:"networkCard" example:"eth0" binding:"required"`             // 网卡
}

func (req CreateLbConfigRequest) AsVIP() string {
	return fmt.Sprintf("%s/%d", req.Address, req.Mask)
}

// CreateResponse
// 创建集群响应
type CreateResponse struct {
	Name                     string                           `json:"clusterName" example:"cluster-create-test-name"`  // 集群名称
	KubernetesVersion        string                           `json:"kubernetesVersion" example:"1.21.5"`              // k8s 版本信息
	KubernetesCRIVersion     string                           `json:"kubernetesCRIVersion" example:"Docker-v19.03.15"` // k8s CRI信息
	Labels                   map[string]string                `json:"labels"`                                          // 标签
	Description              string                           `json:"description" example:"这是创建集群的描述信息"`               // 描述
	RegistryType             []RegistryType                   `json:"registryType" example:"default,custom"`           // 表示镜像仓库的类型 可选值 default、custom
	Registry                 *CreateRegistryConfigResponse    `json:"registry"`                                        // 制品服务信息
	HubClusterIngressAddress string                           `json:"hubClusterIngressAddress" example:"***********"`  // 管理集群ingress 访问地址
	StellarisComponent       CreateStellarisComponentResponse `json:"stellarisComponent"`                              // stellaris component 信息
	NodeConfigs              CreateNodeConfigInfoResponse     `json:"nodeConfigs"`                                     // 节点配置
	NetworkConfigs           CreateNetworkConfigResponse      `json:"networkConfigs"`                                  // 网络配置
	CRI                      CRIType                          `json:"cri"`
}

func (response CreateResponse) Convert2CreateRequest() CreateRequest {
	return CreateRequest{
		ClusterName:              response.Name,
		KubernetesVersion:        response.KubernetesVersion,
		KubernetesCRIVersion:     response.KubernetesCRIVersion,
		Labels:                   response.Labels,
		Description:              response.Description,
		Registry:                 response.Registry.ConvertToRequest(),
		HubClusterIngressAddress: response.HubClusterIngressAddress,
		StellarisComponent:       response.StellarisComponent.ConvertToRequest(),
		NodeConfigs:              response.NodeConfigs.ConvertToRequest(),
		NetworkConfigs:           response.NetworkConfigs.ConvertToRequest(),
	}
}

// CreateRegistryConfigResponse
// 创建集群响应Registry信息
type CreateRegistryConfigResponse struct {
	Protocol string `json:"protocol" example:"http"`            // 协议
	Address  string `json:"address" example:"test.harbor.work"` // 地址
	Port     int    `json:"port" example:"80"`                  // 端口
}

func (response *CreateRegistryConfigResponse) ConvertToRequest() *CreateRegistryConfigRequest {
	if response == nil {
		return nil
	}
	return &CreateRegistryConfigRequest{
		Protocol: response.Protocol,
		Address:  response.Address,
		Port:     response.Port,
	}
}

// CreateStellarisComponentResponse
// 创建集群响应 Stellaris 信息
type CreateStellarisComponentResponse struct {
	Address        string  `json:"address" example:"*********"`        // 地址
	Port           int     `json:"port" example:"8686"`                // 端口
	StandbyAddress *string `json:"standbyAddress" example:"*********"` // 备 - 地址
	StandbyPort    *int    `json:"standbyPort" example:"8686"`         // 备 - 端口
}

func (response CreateStellarisComponentResponse) ConvertToRequest() CreateStellarisComponentRequest {
	return CreateStellarisComponentRequest{
		Address:        response.Address,
		Port:           response.Port,
		StandbyAddress: response.StandbyAddress,
		StandbyPort:    response.StandbyPort,
	}
}

// CreateNodeConfigInfoResponse
// 添加节点表单响应
type CreateNodeConfigInfoResponse struct {
	NodeConfigType NodeConfigType               `json:"type" example:"AllInOne"` // 节点配置类型 支持值  AllInOne(All-in-One) | MinimizeHA(最小化高可用) | StandardNoneHA(标准非高可用) |  StandardHA(标准高可用) 由前端同学进行控制
	Nodes          CreateNodeConfigListResponse `json:"nodes" `                  // 节点列表
}

func (response CreateNodeConfigInfoResponse) ConvertToRequest() CreateNodeConfigInfoRequest {
	return CreateNodeConfigInfoRequest{
		NodeConfigType: response.NodeConfigType,
		Nodes:          response.Nodes.ConvertToRequest(),
	}
}

// CreateNodeConfigListResponse
// 添加节点列表响应
type CreateNodeConfigListResponse []CreateNodeConfigResponse

func (arr CreateNodeConfigListResponse) RenderNodeDiskConfigs(input map[string]*NodeStorageResponse) {
	for idx, item := range arr {
		storage, exist := input[item.Ip]
		if !exist {
			arr[idx].Storage.Type = NodeStorageTypeManual
		} else {
			arr[idx].Storage = *storage
		}
	}

}

func (arr CreateNodeConfigListResponse) ConvertToRequest() CreateNodeConfigListRequest {
	var reqs CreateNodeConfigListRequest
	for _, item := range arr {
		reqs = append(reqs, item.ConvertToRequest())
	}
	return reqs
}

// CreateNodeConfigResponse
// 添加节点信息响应
type CreateNodeConfigResponse struct {
	Role       NodeRole            `json:"role" example:"master"`      // 节点角色 支持master、worker
	SystemNode bool                `json:"systemNode" example:"true"`  // 是否为系统节点
	Ip         string              `json:"ip" example:"*********"`     // 节点IP
	Port       int                 `json:"port" example:"22"`          // 节点端口
	Auth       *NodeAuthResponse   `json:"auth"`                       // 认证信息
	SupportGpu bool                `json:"supportGpu" example:"false"` // 是否支持GPU
	IsDefault  bool                `json:"isDefault" example:"false"`  // 记录是否为默认节点 用于前端回显
	Storage    NodeStorageResponse `json:"storage"`                    // 节点存储配置
}

func (response CreateNodeConfigResponse) ConvertToRequest() CreateNodeConfigRequest {
	return CreateNodeConfigRequest{
		Role:       response.Role,
		SystemNode: response.SystemNode,
		Ip:         response.Ip,
		Port:       response.Port,
		Auth:       response.Auth.Convert2Request(),
		SupportGpu: response.SupportGpu,
		IsDefault:  response.IsDefault,
		Storage:    *response.Storage.ConvertToRequest(),
	}
}

type NodeStorageResponse struct {
	Type     NodeStorageType       `json:"type"  example:"auto"` // 节点存储类型 支持 auto、manual
	DiskPath *NodeDiskPathResponse `json:"diskPath,omitempty"`   // 若节点存储类型为 manual 时填写，为etcd、docker、kubelet、system 的表单
}

func (response *NodeStorageResponse) ConvertToRequest() *NodeStorageRequest {
	if response == nil {
		return nil
	}
	return &NodeStorageRequest{
		Type:     response.Type,
		DiskPath: response.DiskPath.ConvertToRequest(),
	}
}

type NodeDiskPathResponse struct {
	// todo 实现filePathBinding，若部位空 则进行校验
	ETCD    string `json:"etcd,omitempty" binding:"filePath"`    // 表示ETCD挂载盘位置
	Docker  string `json:"docker,omitempty" binding:"filePath"`  // 表示Docker挂载盘位置
	Kubelet string `json:"kubelet,omitempty" binding:"filePath"` // 表示Kubelet挂载盘位置
	System  string `json:"system,omitempty" binding:"filePath"`  // 表示System挂载盘位置
}

func (response *NodeDiskPathResponse) ConvertToRequest() *NodeDiskPathRequest {
	if response == nil {
		return nil
	}
	return &NodeDiskPathRequest{
		ETCD:    response.ETCD,
		Docker:  response.Docker,
		Kubelet: response.Kubelet,
		System:  response.System,
	}
}

type NodeAuthResponse struct {
	AuthType NodeAuthType           `json:"authType" example:"username_password"` // 认证类型 目前支持 username_password
	Param    map[string]interface{} `json:"param"`                                // 认证类型对应的参数 {"username":"用户名","password":"密码","authorizationPassword":"提权密码"}
}

func (response *NodeAuthResponse) Convert2Request() *NodeAuthRequest {
	if response == nil {
		return nil
	}
	return &NodeAuthRequest{
		AuthType: response.AuthType,
		Param:    response.Param,
	}
}

// CreateNetworkConfigResponse
// 集群创建网络配置响应
type CreateNetworkConfigResponse struct {
	PlatformCNITypes []CNIType                           `json:"mode" example:"calico,macvlan,kube-ovn"` // 表示平台已支持的CNI 类型
	CNIs             map[CNIType]CreateCNIConfigResponse `json:"cnis"`                                   // CNI 配置信息
	ApiServerVIP     *CreateApiServerVipConfigResponse   `json:"apiServerVIP"`                           // APIServer Vip配置
	LoadBalance      *CreateLbConfigResponse             `json:"loadBalance"`                            // 负载均衡配置
}

func (response *CreateNetworkConfigResponse) ConvertToRequest() CreateNetworkConfigRequest {

	var cnis = make(map[CNIType]CreateCNIConfigRequest, len(response.CNIs))
	for k, v := range response.CNIs {
		cnis[k] = v.ConvertToRequest()
	}

	return CreateNetworkConfigRequest{
		CNIs:         cnis,
		ApiServerVIP: response.ApiServerVIP.ConvertToRequest(),
		LoadBalance:  response.LoadBalance.ConvertToRequest(),
	}
}

type CreateCNIConfigResponse struct {
	DualStack bool                   `json:"dualStack" example:"false"` // 表示是否支持双栈
	Ipv4Param map[string]interface{} `json:"ipv4"`                      // IPv4 类型参数 calico: {"cidr":"10.0.0.0/24"} |  macvlan: {"networkCard":"eth0","startIp":"10.0.0.0","endIp":"**********","gateway":"********","mask":"24","vlanId":"10","reservedIp":[{"startIp":"*********","endIp":"*********"}]} kube-ovn: {"networkCard":"绑定的网卡名称"}
	Ipv6Param map[string]interface{} `json:"ipv6"`                      // IPv6 类型参数
}

func (response CreateCNIConfigResponse) ConvertToRequest() CreateCNIConfigRequest {
	return CreateCNIConfigRequest{
		Ipv4Param: response.Ipv4Param,
		Ipv6Param: response.Ipv6Param,
	}
}

// CreateApiServerVipConfigResponse
// 集群创建ApiServerVip 配置响应
type CreateApiServerVipConfigResponse struct {
	Protocol    string `json:"protocol" example:"http"`     // 协议
	Address     string `json:"address" example:"*********"` // 地址
	Port        int    `json:"port" example:"6443"`         // 端口
	Mask        int    `json:"mask" example:"24"`           // 掩码
	NetworkCard string `json:"networkCard" example:"eth0"`  // 网卡
}

func (response *CreateApiServerVipConfigResponse) ConvertToRequest() *CreateApiServerVipConfigRequest {
	if response == nil {
		return nil
	}
	return &CreateApiServerVipConfigRequest{
		Protocol:    response.Protocol,
		Address:     response.Address,
		Port:        response.Port,
		Mask:        response.Mask,
		NetworkCard: response.NetworkCard,
	}
}

// CreateLbConfigResponse
// 集群创建 负载均衡配置响应
type CreateLbConfigResponse struct {
	Address     string `json:"address" example:"***********"` // 地址
	Mask        int    `json:"mask" example:"24"`             // 掩码
	NetworkCard string `json:"networkCard" example:"eth0"`    // 网卡
}

func (response *CreateLbConfigResponse) ConvertToRequest() *CreateLbConfigRequest {
	if response == nil {
		return nil
	}
	return &CreateLbConfigRequest{
		Address:     response.Address,
		Mask:        response.Mask,
		NetworkCard: response.NetworkCard,
	}
}

// CreateStatusResponse
// 创建集群状态返回接口
type CreateStatusResponse struct {
	Name               string                    `json:"clusterName" example:"cluster-create-test-name"` // 集群名称
	NodeConfigType     NodeConfigType            `json:"type" example:"AllInOne" binding:"required"`     // 节点配置类型 支持值  AllInOne(All-in-One) | MinimizeHA(最小化高可用) | StandardNoneHA(标准非高可用) |  StandardHA(标准高可用) 由前端同学进行控制
	Status             StatusType                `json:"status" example:"preflighting"`                  // 表示集群状态
	Processing         CreateProcessListResponse `json:"processing" translateObject:""`
	CNITypes           []CNIType                 `json:"CNITypes"`
	Arch               string                    `json:"arch"`
	HasAutoStorageNode bool                      `json:"hasAutoStorageNode" example:"true"` // 是否存在自动挂载磁盘的节点
	CRI                CRIType                   `json:"cri"`
}
type CreateProcessListResponse []CreateProcessResponse

func (arr CreateProcessListResponse) HasFailedStatus() bool {
	for _, item := range arr {
		if item.Status == ProcessStatusFail {
			return true
		}
	}
	return false
}

func (arr CreateProcessListResponse) FindByCode(code string) (*CreateProcessResponse, bool) {
	if len(arr) == 0 {
		return nil, false
	}
	for index, item := range arr {
		if item.Code == code {
			return &arr[index], true
		}
	}
	return nil, false
}

type ProcessStatus string

var (
	ProcessStatusWaiting    ProcessStatus = "wait"
	ProcessStatusProcessing ProcessStatus = "process"
	ProcessStatusSuccess    ProcessStatus = "finish"
	ProcessStatusFail       ProcessStatus = "error"
)

func MustParseByInstallerStatus(status installerv1alpha1.StatusPhase) ProcessStatus {
	switch status {
	case installerv1alpha1.StatusPhaseSuccess:
		return ProcessStatusSuccess
	case installerv1alpha1.StatusPhaseFailed:
		return ProcessStatusFail
	case installerv1alpha1.StatusPhaseRunning:
		return ProcessStatusProcessing
	case installerv1alpha1.StatusPhasePending:
		return ProcessStatusWaiting
	default:
		return ProcessStatusWaiting
	}
}

type CreateProcessResponse struct {
	Code        string                        `json:"code" example:"distribute-pre-inspection-tools"`             // 步骤码
	Name        string                        `json:"name" example:"下发预检工具" translate:"keyName=name"`             // 步骤中文
	Description string                        `json:"description" example:"描述信息" translate:"keyName=description"` // 描述信息
	Status      ProcessStatus                 `json:"status" example:"process"`                                   // 步骤状态 wait process finish error
	Steps       CreateProcessStepListResponse `json:"steps" translateObject:""`                                   // 子步骤列表
}
type CreateProcessStepListResponse []CreateProcessStepResponse
type CreateProcessStepResponse struct {
	Code         string        `json:"code" example:"cluster-connect-check"`                                // 子步骤码
	Name         string        `json:"name" example:"集群联通性检查" translate:"keyName=name"`                     // 子步骤中文
	Description  string        `json:"description" example:"集群联通性检查必须...." translate:"keyName=description"` // 子步骤描述
	Status       ProcessStatus `json:"status" example:"error"`                                              // 步骤状态 wait process finish error
	ErrorType    *string       `json:"errorType" example:"check_fail"`                                      // 错误类型 on Status = error
	ErrorMessage *string       `json:"errMsg" example:"不支持arm 主机"`                                          // 错误详情 on Status = error
}

type CRIType string

var (
	CRITypeDocker     CRIType = "docker"     // cri type docker
	CRITypeContainerd CRIType = "containerd" // cri type containerd
)
