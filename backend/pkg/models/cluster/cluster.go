package cluster

import (
	"fmt"
	"time"

	stellarisv1alhpha1 "harmonycloud.cn/stellaris/pkg/apis/stellaris/v1alpha1"
	"harmonycloud.cn/unifiedportal/cloudservice-operator/pkg/probe"
	"k8s.io/apimachinery/pkg/util/sets"
)

// StateType
// 集群状态信息
type StateType string

var (
	// StateTypeControlled 已纳管
	// StateTypeUnControlled 未纳管
	// 集群State 分2大类
	StateTypeControlled   StateType = "controlled"
	StateTypeUnControlled StateType = "un-controlled"
)

// StatusType
// 集群生命周期状态信息
type StatusType string

func MustParseStatusTypeByStcType(stestellarisClusterType stellarisv1alhpha1.ClusterStatusType) StatusType {
	switch stestellarisClusterType {
	case stellarisv1alhpha1.OnlineStatus:
		return StatusTypeOnlineStatus
	case stellarisv1alhpha1.OfflineStatus:
		return StatusTypeOfflineStatus
	case stellarisv1alhpha1.InitializingStatus:
		return StatusTypeInitializingStatus
	default:
		return StatusTypeUnKnow
	}
}

var FailedClusterStatusTypes = sets.New[StatusType](StatusTypeCreateInitialFailed, StatusTypePreflightFailed, StatusTypeInstallFailed, StatusTypeJoinFail)

var (
	// StatusTypeDeleting
	// 表示集群｜创建集群的任务正在删除中
	StatusTypeDeleting StatusType = "deleting" // 删除中
	// StatusTypeCreateInitialing
	// StatusTypePreflighting
	// StatusTypeInstalling
	// StatusTypeJoining
	// 以上4个状态为集群创建过程中的状态 允许查看 不允许编辑信息和删除
	StatusTypeCreateInitialing    StatusType = "createInitial"       // 集群初始化中
	StatusTypePreflighting        StatusType = "preflighting"        // 检预中
	StatusTypeInstalling          StatusType = "installing"          // 创建中
	StatusTypeJoining             StatusType = "joining"             // 纳管中
	StatusTypeComponentInstalling StatusType = "componentInstalling" // 集群组件安装中

	// StatusTypeCreateInitialFailed
	// StatusTypePreflightFailed
	// StatusTypeInstallFailed
	// StatusTypeJoinFail
	// 以上三个状态为集群创建失败的状态 允许查看、编辑信息、删除
	StatusTypeCreateInitialFailed    StatusType = "createInitialFailed"    // 集群初始化失败
	StatusTypePreflightFailed        StatusType = "preflightFailed"        // 预检失败
	StatusTypeInstallFailed          StatusType = "installFailed"          // 创建失败
	StatusTypeJoinFail               StatusType = "joinFailed"             // 纳管失败
	StatusTypeComponentInstallFailed StatusType = "componentInstallFailed" // 集群组件安装失败

	// StatusTypeOnlineStatus
	// StatusTypeOfflineStatus
	// StatusTypeInitializingStatus
	// 以上三个状态为已纳管集群的状态 允许操作: 编辑标签 编辑描述 删除 与进入集群空间
	StatusTypeOnlineStatus       StatusType = "online"       // 在线
	StatusTypeOfflineStatus      StatusType = "offline"      // 离线
	StatusTypeInitializingStatus StatusType = "initializing" // 初始化中

	// StatusTypeUnKnow
	// 以上状态为后端预留状态 一般不会出现 出现该状态时直接显示未知即可（前端无法对该状态集群进行任何操作）
	StatusTypeUnKnow StatusType = "unknow" // 未知
)

// ApiServerStatusType
// api-server 联通性状态
type ApiServerStatusType string

func MustParseApiServerStatus(status string) ApiServerStatusType {
	switch status {
	case string(probe.SolutionStatusSuccess):
		return ApiServerStatusTypeSuccess
	case string(probe.SolutionStatusFail):
		return ApiServerStatusTypeFail
	case string(probe.SolutionStatusInitialize):
		fallthrough
	default:
		return ApiServerStatusTypeInitialize

	}
}

var (
	ApiServerStatusTypeSuccess    ApiServerStatusType = "success"
	ApiServerStatusTypeFail       ApiServerStatusType = "fail"
	ApiServerStatusTypeInitialize ApiServerStatusType = "initialize"
)

// ListResponse
// 获取集群列表返回
type ListResponse []Response

// Len is the number of elements in the collection.
func (arr ListResponse) Len() int {
	return len(arr)
}

// Less reports whether the element with index i
// must sort before the element with index j.
//
// If both Less(i, j) and Less(j, i) are false,
// then the elements at index i and j are considered equal.
// Sort may place equal elements in any order in the final result,
// while Stable preserves the original input order of equal elements.
//
// Less must describe a transitive ordering:
//   - if both Less(i, j) and Less(j, k) are true, then Less(i, k) must be true as well.
//   - if both Less(i, j) and Less(j, k) are false, then Less(i, k) must be false as well.
//
// Note that floating-point comparison (the < operator on float32 or float64 values)
// is not a transitive ordering when not-a-number (NaN) values are involved.
// See Float64Slice.Less for a correct implementation for floating-point values.
func (arr ListResponse) Less(i, j int) bool {
	// 根据集群状态与创建时间进行多维度排序
	// 若 若集群状态为在线｜离线 ｜ 初始化中 则按照 创建时间排序
	// 如 为创建集群的状态 靠后按创建时间排序
	// 删除中的集群靠最后进行排序
	indexI, indexJ := clusterStatusSortFunc(arr[i].Status), clusterStatusSortFunc(arr[j].Status)
	if indexI == indexJ {
		return arr[i].CreateTime.Before(arr[j].CreateTime)
	}
	return indexI < indexJ

}
func clusterStatusSortFunc(status StatusType) int {
	switch status {
	case StatusTypeOnlineStatus:
		fallthrough
	case StatusTypeOfflineStatus:
		fallthrough
	case StatusTypeInitializingStatus:
		return 0
	case StatusTypePreflightFailed:
		fallthrough
	case StatusTypeInstallFailed:
		fallthrough
	case StatusTypeJoinFail:
		return 1
	case StatusTypePreflighting:
		fallthrough
	case StatusTypeInstalling:
		fallthrough
	case StatusTypeJoining:
		return 2
	default:
		return 3
	}
}

// Swap swaps the elements with indexes i and j.
func (arr ListResponse) Swap(i, j int) {
	arr[i], arr[j] = arr[j], arr[i]
}

// Response
// 获取集群返回信息
type Response struct {
	Name               string              `json:"clusterName" example:"cluster-57"`                                                      // 集群名称
	Labels             *string             `json:"labels" example:"unified-platform.harmonycloud.cn/api-server-status=success,test=test"` // 标签信息 格式 key=value,key1=value1....
	Description        *string             `json:"description" example:"这是集群的描述信息"`                                                       // 集群描述
	NodeConfigType     NodeConfigType      `json:"type" example:"AllInOne"`                                                               // 节点配置类型 支持值  AllInOne(All-in-One) | MinimizeHA(最小化高可用) | StandardNoneHA(标准非高可用) |  StandardHA(标准高可用) 由前端同学进行控制
	State              StateType           `json:"controlState" example:"controlled"`                                                     // 集群状态 controlled(已纳管)、un-controlled(未纳管)
	Status             StatusType          `json:"status" example:"online"`                                                               // 集群所处生命周期状态 preflighting(预检中)、preflightFailed(预检失败)、installing(创建中)、installFailed(创建失败)、online(在线)、offline(离线)、initializing(初始化中)
	ApiServerStatus    ApiServerStatusType `json:"apiServerStatus" example:"success"`                                                     // api-server 联通性状态 success(成功),fail(失败),initialize(探测中)
	NodeCount          int                 `json:"nodeCount" example:"5"`                                                                 // 节点数量
	K8sVersion         *string             `json:"k8sVersion" example:"v1.21.5-hc.1"`                                                     // k8s版本
	Structure          []string            `json:"structure" example:"AMD64"`                                                             // 集群架构
	NetworkType        []string            `json:"networkType" example:"Calico-v3.23.5"`                                                  // 集群网络插件
	CNITypes           []CNIType           `json:"CNITypes"`
	CreateTime         time.Time           `json:"createTime"`
	HasAutoStorageNode bool                `json:"hasAutoStorageNode" example:"true"` // 是否存在自动挂载磁盘的节点
	Baseline           string              `json:"baseline"`                          // 创建集群基线版本
	Prole              []ProleType         `json:"prole"`                             // 集群平台角色
	CRI                CRIType             `json:"cri"`
}

// ExistResponse
// 判断集群是否存在返回结构体
type ExistResponse struct {
	Exist   bool   `json:"exist" example:"true"`                                                                                       // true 表示集群已存在 false 表示集群不存在
	Message string `json:"message,omitempty" example:"集群:cluster-57 已纳管" regexTranslate:"staticGroupName=cluster-exist-message-match"` // 当Exist为true时的提示信息
}

// ExistResponseMessageForStcExist
// 判断集群是否存在 - stc 存在 返回的消息
func ExistResponseMessageForStcExist(clusterName string) string {
	return fmt.Sprintf("集群:%s 已纳管", clusterName)
}

// ExistResponseMessageForInstallerExist
// 判断集群是否存在 - installer 存在 返回的消息
func ExistResponseMessageForInstallerExist(clusterName string) string {
	return fmt.Sprintf("集群:%s 创建中", clusterName)
}
