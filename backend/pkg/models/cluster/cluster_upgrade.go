package cluster

import (
	"net"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	database_aop "harmonycloud.cn/unifiedportal/translate-sdk-golang/database-aop"
)

func init() {
	database_aop.GroupInfo(UpgradeClusterInfo{}, database_aop.TranslateGroupInfo{
		Name:          "upgrade-cluster-info",
		UniqueKeyName: "UpgradeCode",
	})
}

type UpgradeCode string

var (
	//PreChecking 预检中
	PreChecking UpgradeCode = "preflighting"
	//PreCheckFailed 预检失败
	PreCheckFailed UpgradeCode = "preflightFailed"
	//Upgrading 升级中
	Upgrading UpgradeCode = "upgrading"
	//UpgradeFailed 升级失败
	UpgradeFailed UpgradeCode = "upgradeFailed"
	//UpgradeSucceeded 升级成功
	UpgradeSucceeded UpgradeCode = "upgradeSucceeded"
	//SupportUpgrade 未预检
	SupportUpgrade    UpgradeCode = "supportUpgrade"
	NotSupportManager UpgradeCode = "NotSupportManager"
	NotSupportUnknown UpgradeCode = "NotSupportUnknown"
)

// UpgradeClusterInfo 集群列表结构体
type UpgradeClusterInfo struct {
	Name        string      `json:"clusterName"`                         // 集群名称
	Baseline    string      `json:"baseline"`                            // 创建集群基线版本
	Prole       []ProleType `json:"prole"`                               // 集群平台角色
	Upgrade     bool        `json:"upgrade"`                             // 是否可升级
	Message     string      `json:"message" translate:"keyName=message"` // 升级信息
	UpgradeCode UpgradeCode `json:"upgradeCode"`                         // 集群升级状态
}

type ListUpgradeClusterInfo []UpgradeClusterInfo

type AvailableType bool

var (
	AvailableTypeTrue  AvailableType = true
	AvailableTypeFalse AvailableType = false
)

// PackageInfo 集群升级包信息
type PackageInfo struct {
	PackageName string        `json:"packageName"` // 包名称
	Available   AvailableType `json:"available"`   // 是否可用
	Message     string        `json:"message"`     // 升级信息
}

type ListPackageInfo []PackageInfo

type ComponentUpgradeAvailableType string

var (
	// ComponentUpgradeAvailableTypeUpgrade 升级
	ComponentUpgradeAvailableTypeUpgrade ComponentUpgradeAvailableType = "upgrade"
	// ComponentUpgradeAvailableTypeDefault 默认
	ComponentUpgradeAvailableTypeDefault ComponentUpgradeAvailableType = "default"
	// ComponentUpgradeAvailableTypeConfiguration 组件配置参数变更
	ComponentUpgradeAvailableTypeConfiguration ComponentUpgradeAvailableType = "configuration"
	// ComponentUpgradeAvailableTypeChanged 组件变更
	ComponentUpgradeAvailableTypeChanged ComponentUpgradeAvailableType = "changed"
)

// PackageContent 集群升级包内容
type PackageContent struct {
	PackageName   string          `json:"packageName"`
	ComponentList []ComponentInfo `json:"componentList"`
}
type ComponentType string

var (
	ComponentTypeNative ComponentType = "native"
	ComponentTypeSystem ComponentType = "system"
)

type ComponentInfo struct {
	ComponentName  string                        `json:"componentName"`
	ComponentType  ComponentType                 `json:"componentType"`
	Status         constants.AddonStatusEnum     `json:"status"`
	CurrentVersion string                        `json:"currentVersion"`
	TargetVersion  string                        `json:"targetVersion"`
	Available      ComponentUpgradeAvailableType `json:"available"`
}

// NodeInfo 集群升级节点列表
type NodeInfo struct {
	NodeName string   `json:"nodeName"`
	Ip       string   `json:"ip"`
	NodeType NodeType `json:"nodeType"`
	Status   string   `json:"status"`
}

type ListNodeInfo []NodeInfo

// UpgradeClusterNodeInfo 添加节点信息
type UpgradeClusterNodeInfo struct {
	NodeName string           `json:"nodeName" binding:"required"`
	Ip       string           `json:"ip" binding:"required,ipv4ipv6"`
	Port     int              `json:"port" binding:"required,port"`
	Auth     *NodeAuthRequest `json:"auth" binding:"required"`
}

// VersionMapping 集群升级版本映射
type VersionMapping struct {
	PackageName string      `json:"packageName"`
	Count       int         `json:"count"`
	Solution    []solution  `json:"solution"`
	Components  []Component `json:"components"`
}

// solution底座解决方案
type solution struct {
	Name string `json:"name"`
	ID   string `json:"id"`
}

// Component 底座组件信息
type Component struct {
	ComponentName string                        `json:"componentName"`
	ComponentType ComponentType                 `json:"componentType"`
	Status        ComponentUpgradeAvailableType `json:"status"`
	Version       string                        `json:"version"`
}

type UpgradeType string

var (
	//	UpgradeTypeSequential 节点串行升级
	UpgradeTypeSequential UpgradeType = "sequential"
	//	UpgradeTypeParallel 节点并行升级
	UpgradeTypeParallel UpgradeType = "parallel"
)

// UpgradeClusterRequest 集群升级请求
type UpgradeClusterRequest struct {
	ClusterName   string          `json:"clusterName"`
	PackageName   string          `json:"packageName"`
	LbAddress     string          `json:"lbAddress"`
	IncludeNative bool            `json:"includeNative"`
	UpgradeType   UpgradeType     `json:"upgradeType"`
	Nodes         []*NodeAuthInfo `json:"nodes"`
	Registry      *RegistryInfo   `json:"registry"`
}

type NodeType string

var (
	// NodeTypeMaster0 主控节点(常用master)
	NodeTypeMaster0 NodeType = "master0"
	// NodeTypeMaster 主控节点
	NodeTypeMaster NodeType = "master"
	// NodeTypeWorker 非主控节点
	NodeTypeWorker NodeType = "worker"
)

type NodeAuthInfo struct {
	NodeName string           `json:"nodeName"`
	NodeType NodeType         `json:"nodeType"`
	Ip       string           `json:"ip"`
	Port     int              `json:"port"`
	Auth     *NodeAuthRequest `json:"auth" binding:"required"`
}

type RegistryInfo struct {
	Protocol string `json:"protocol"`
	Address  string `json:"address"`
	Port     string `json:"port"`
	Username string `json:"username"`
	Password string `json:"password"`
}

// UpgradeClusterStepResponse 集群升级步骤返回
type UpgradeClusterStepResponse struct {
	ClusterName string              `json:"clusterName"`
	Status      UpgradeCode         `json:"status"`
	Processing  []UpgradeProcessing `json:"processing" translateObject:""`
}

// UpgradeProcessing 集群升级折叠步骤
type UpgradeProcessing struct {
	Code        string               `json:"code"`
	Name        string               `json:"name" translate:"keyName=name"`
	Description string               `json:"description" translate:"keyName=description"`
	Status      ProcessStatus        `json:"status"`
	Steps       []UpgradeProcessStep `json:"steps" translateObject:""`
}

// UpgradeProcessStep 集群升级小步骤
type UpgradeProcessStep struct {
	Code         string        `json:"code"`
	Name         string        `json:"name"  translate:"keyName=name"`
	Description  string        `json:"description"  translate:"keyName=description"`
	Status       ProcessStatus `json:"status"`
	ErrorType    *string       `json:"errorType"`
	ErrorMessage *string       `json:"errMsg"`
}

// SortNodeIp 自定义排序逻辑
type SortNodeIp []NodeInfo

func (a SortNodeIp) Len() int      { return len(a) }
func (a SortNodeIp) Swap(i, j int) { a[i], a[j] = a[j], a[i] }
func (a SortNodeIp) Less(i, j int) bool {
	return net.ParseIP(a[i].Ip).String() < net.ParseIP(a[j].Ip).String()
}

type SortIp []*NodeAuthInfo

func (a SortIp) Len() int      { return len(a) }
func (a SortIp) Swap(i, j int) { a[i], a[j] = a[j], a[i] }
func (a SortIp) Less(i, j int) bool {
	return net.ParseIP(a[i].Ip).String() < net.ParseIP(a[j].Ip).String()
}
