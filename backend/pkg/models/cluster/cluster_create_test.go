package cluster

import (
	"fmt"
	"reflect"
	"testing"
)

func Test_CreateCNIConfigCalicoParamRequest_Validator(t *testing.T) {
	params := []struct {
		request   CreateCNIConfigCalicoParamRequest
		wantError bool
	}{
		{
			request: CreateCNIConfigCalicoParamRequest{
				CIDR: "",
			},
			wantError: true,
		},
		{
			request: CreateCNIConfigCalicoParamRequest{
				CIDR: "/24",
			},
			wantError: true,
		},
		{
			request: CreateCNIConfigCalicoParamRequest{
				CIDR: "***********",
			},
			wantError: true,
		},
		{
			request: CreateCNIConfigCalicoParamRequest{
				CIDR: "120:",
			},
			wantError: true,
		},
		{
			request: CreateCNIConfigCalicoParamRequest{
				CIDR: "2022::9:0:",
			},
			wantError: true,
		},
		{
			request: CreateCNIConfigCalicoParamRequest{
				CIDR: "***********/33",
			},
			wantError: true,
		},
		{
			request: CreateCNIConfigCalicoParamRequest{
				CIDR: "***********/32",
			},
			wantError: false,
		},
		{
			request: CreateCNIConfigCalicoParamRequest{
				CIDR: "***********/24",
			},
			wantError: false,
		},
		{
			request: CreateCNIConfigCalicoParamRequest{
				CIDR: "2022::9:0/120",
			},
			wantError: false,
		},
		{
			request: CreateCNIConfigCalicoParamRequest{
				CIDR: "2022:0000:0000:0000:0000:0000:0009:0000/120",
			},
			wantError: false,
		},
	}
	for index, param := range params {
		t.Run(fmt.Sprintf("testIndex_%d", index), func(t *testing.T) {
			err := param.request.Validator()
			if (err != nil) != param.wantError {
				t.Errorf("want err is %v,err is %v", param.wantError, err)
			}
		})
	}
}

func Test_CreateCNIConfigMacvlanParamRequest_Validator(t *testing.T) {
	params := []struct {
		request   CreateCNIConfigMacvlanParamRequest
		wantError bool
	}{
		{
			request: CreateCNIConfigMacvlanParamRequest{
				StartIP: "***********77",
				EndIP:   "***********55",
				Gateway: "***********",
				Mask:    24,
			},
			wantError: true,
		},
		{
			request: CreateCNIConfigMacvlanParamRequest{
				StartIP: "***********0",
				EndIP:   "***********77",
				Gateway: "***********",
				Mask:    24,
			},
			wantError: true,
		},
		{
			request: CreateCNIConfigMacvlanParamRequest{
				StartIP: "***********0",
				EndIP:   "***********55",
				Gateway: "***********000",
				Mask:    24,
			},
			wantError: true,
		},
		{
			request: CreateCNIConfigMacvlanParamRequest{
				StartIP: "***********55",
				EndIP:   "***********0",
				Gateway: "***********",
				Mask:    24,
			},
			wantError: true,
		},
		{
			request: CreateCNIConfigMacvlanParamRequest{
				StartIP: "***********0",
				EndIP:   "***********55",
				Gateway: "***********",
				Mask:    64,
			},
			wantError: true,
		},
		{
			request: CreateCNIConfigMacvlanParamRequest{
				StartIP: "***********0",
				EndIP:   "*************",
				Gateway: "***********",
				Mask:    24,
			},
			wantError: true,
		},
		{
			request: CreateCNIConfigMacvlanParamRequest{
				StartIP: "***********0",
				EndIP:   "***********55",
				Gateway: "***********",
				Mask:    24,
			},
			wantError: true,
		},
		{
			request: CreateCNIConfigMacvlanParamRequest{
				StartIP:    "***********0",
				EndIP:      "***********55",
				Gateway:    "***********",
				Mask:       24,
				ReservedIp: "***********~192.",
			},
			wantError: true,
		},
		{
			request: CreateCNIConfigMacvlanParamRequest{
				StartIP:    "***********0",
				EndIP:      "***********55",
				Gateway:    "***********",
				Mask:       24,
				ReservedIp: "      ",
			},
			wantError: false,
		},
		{
			request: CreateCNIConfigMacvlanParamRequest{
				StartIP: "***********0",
				EndIP:   "***********55",
				Gateway: "***********",
				Mask:    24,
			},
			wantError: false,
		},
		{
			request: CreateCNIConfigMacvlanParamRequest{
				StartIP:    "***********0",
				EndIP:      "***********55",
				Gateway:    "***********",
				Mask:       24,
				ReservedIp: "***********~***********0",
			},
			wantError: true,
		},
		{
			request: CreateCNIConfigMacvlanParamRequest{
				StartIP:    "***********0",
				EndIP:      "***********55",
				Gateway:    "***********",
				Mask:       24,
				ReservedIp: "***********0~***********55",
			},
			wantError: false,
		},
		{
			request: CreateCNIConfigMacvlanParamRequest{
				StartIP:    "***********0",
				EndIP:      "***********55",
				Gateway:    "***********",
				Mask:       24,
				ReservedIp: "***********0~***********0, ***********1~***********55",
			},
			wantError: false,
		},
		{
			request: CreateCNIConfigMacvlanParamRequest{
				StartIP: "2022::9:0",
				EndIP:   "2022::9:FF",
				Gateway: "2022::9:1",
				Mask:    120,
			},
			wantError: false,
		},
		{
			request: CreateCNIConfigMacvlanParamRequest{
				StartIP:    "2022::9:0",
				EndIP:      "2022::9:FF",
				Gateway:    "2022::9:1",
				Mask:       120,
				ReservedIp: "2022::9:0~2022::9:2, 2022::9:3~2022::9:4",
			},
			wantError: false,
		},
		{
			request: CreateCNIConfigMacvlanParamRequest{
				StartIP:    "2022::9:0",
				EndIP:      "2022::9:FF",
				Gateway:    "2022::9:1",
				Mask:       120,
				ReservedIp: "2022::8:0~2022::9:2, 2022::9:3~2022::9:4",
			},
			wantError: true,
		},
	}
	for index, param := range params {
		t.Run(fmt.Sprintf("testIndex_%d", index), func(t *testing.T) {
			err := param.request.Validator()
			if (err != nil) != param.wantError {
				t.Errorf("want err is %v,err is %v", param.wantError, err)
			}
		})
	}
}

func Test_CreateCNIConfigKubeOVNParamRequest_Validator(t *testing.T) {
	params := []struct {
		request   CreateCNIConfigKubeOVNParamRequest
		wantError bool
	}{
		{
			request:   CreateCNIConfigKubeOVNParamRequest{},
			wantError: true,
		},
		{
			request: CreateCNIConfigKubeOVNParamRequest{
				// CIDR:    "10.0.0.0/24",
				// Gateway: "",
			},
			wantError: true,
		},
		{
			request: CreateCNIConfigKubeOVNParamRequest{
				// CIDR:    "10.0.0.0/24",
				// Gateway: "********",
			},
			wantError: true,
		},
		{
			request: CreateCNIConfigKubeOVNParamRequest{
				// CIDR:    "10.0.0.0/24",
				// Gateway: "**********",
			},
			wantError: false,
		},
		{
			request: CreateCNIConfigKubeOVNParamRequest{
				// CIDR:       "10.0.0.0/24",
				// Gateway:    "**********",
				// ReservedIp: "*******~*********",
			},
			wantError: true,
		},
		{
			request: CreateCNIConfigKubeOVNParamRequest{
				// CIDR:       "10.0.0.0/24",
				// Gateway:    "**********",
				// ReservedIp: "*******~********",
			},
			wantError: true,
		},
		{
			request: CreateCNIConfigKubeOVNParamRequest{
				// CIDR:       "10.0.0.0/24",
				// Gateway:    "**********",
				// ReservedIp: "*******~********",
			},
			wantError: true,
		},
		{
			request: CreateCNIConfigKubeOVNParamRequest{
				// CIDR:       "10.0.0.0/24",
				// Gateway:    "**********",
				// ReservedIp: "********~********",
			},
			wantError: false,
		},
	}

	for index, param := range params {
		t.Run(fmt.Sprintf("testIndex_%d", index), func(t *testing.T) {
			err := param.request.Validator()
			if (err != nil) != param.wantError {
				t.Errorf("want err is %v,err is %v", param.wantError, err)
			}
		})
	}
}

func Test_parseReservedIpList(t *testing.T) {
	params := []struct {
		reservedIp string
		wantError  bool
		wantResult [][]string
	}{
		{
			reservedIp: "a~b,c~d",
			wantError:  true,
		},
		{
			reservedIp: "***********~***********",
			wantError:  false,
			wantResult: [][]string{{"***********", "***********"}},
		},
		{
			reservedIp: "*********** ~ ***********",
			wantError:  false,
			wantResult: [][]string{{"***********", "***********"}},
		},
		{
			reservedIp: "*********** ~ ***********",
			wantError:  false,
			wantResult: [][]string{{"***********", "***********"}},
		},
		{
			reservedIp: "*********** ~ ***********,",
			wantError:  true,
		},
		{
			reservedIp: "*********** ~ ***********",
			wantError:  true,
		},
		{
			reservedIp: "*********** ~ ***********,*********** ~ ***********",
			wantError:  false,
			wantResult: [][]string{{"***********", "***********"}, {"***********", "***********"}},
		},
		{
			reservedIp: "*********** ~ *********** ,*********** ~ ***********",
			wantError:  false,
			wantResult: [][]string{{"***********", "***********"}, {"***********", "***********"}},
		},
		{
			reservedIp: "*********** ~ ***********, *********** ~ ***********",
			wantError:  false,
			wantResult: [][]string{{"***********", "***********"}, {"***********", "***********"}},
		},
		{
			reservedIp: "*********** ~ *********** ,    *********** ~ ***********",
			wantError:  false,
			wantResult: [][]string{{"***********", "***********"}, {"***********", "***********"}},
		},
		{
			reservedIp: "2022::9:1 ~ 2022::9:FF",
			wantError:  false,
			wantResult: [][]string{{"2022::9:1", "2022::9:FF"}},
		},
	}
	for index, param := range params {
		t.Run(fmt.Sprintf("testIndex_%d", index), func(t *testing.T) {
			result, err := parseReservedIpList(param.reservedIp)

			if (err != nil) != param.wantError {
				t.Errorf("want err is %v,err is %v", param.wantError, err)
				return
			}
			if !reflect.DeepEqual(result, param.wantResult) {
				t.Errorf("want result is %v,result is %v", param.wantResult, result)
			}
		})
	}
}
