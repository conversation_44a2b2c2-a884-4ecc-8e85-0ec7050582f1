package monitoring

const (
	DashboardURL      = "/d/%s%s"
	LabelKeyGrafana   = "grafana_dashboard"
	LabelValueGrafana = "1"
)

type ScopeLevel string

const (
	LevelCluster      ScopeLevel = "cluster"
	LevelNode         ScopeLevel = "node"
	LevelWorkload     ScopeLevel = "workload"
	LevelPod          ScopeLevel = "pod"
	LevelCloudService ScopeLevel = "cloudservice"
	LevelComponent    ScopeLevel = "cloudcomponent"
)

type Dashboard struct {
	UID             string     `form:"uid" json:"uid"`                         // 唯一ID
	Title           string     `form:"title" json:"title"`                     // 标题
	ConfigName      string     `form:"configName" json:"configName"`           // 配置名称
	ConfigNamespace string     `form:"configNamespace" json:"configNamespace"` // 配置命名空间
	URL             string     `form:"url" json:"url"`                         // 监控大盘地址
	Level           ScopeLevel `json:"-"`
}

type DashboardModel struct {
	UID         string `json:"uid"`         // 唯一ID
	Title       string `json:"title"`       // 名称
	Description string `json:"description"` // 描述
}

type ObjectKey struct {
	Level              ScopeLevel `json:"level" form:"level" binding:"required"`
	NodeName           string     `json:"nodeName,omitempty" form:"nodeName"`
	Namespace          string     `json:"namespace,omitempty" form:"namespace"`
	NamespaceList      string     `json:"namespaceList"`
	WorkloadKind       string     `json:"workloadKind,omitempty" form:"workloadKind"`
	WorkloadName       string     `json:"workloadName,omitempty" form:"workloadName"`
	PodName            string     `json:"podName,omitempty" form:"podName"`
	CloudServiceName   string     `json:"cloudServiceName" form:"cloudServiceName"`
	CloudComponentName string     `json:"cloudComponentName,omitempty" form:"cloudComponentName"`
	PVCName            string     `json:"PVCName,omitempty" form:"PVCName"`
	Cluster            string     `json:"cluster,omitempty" form:"cluster"`
	Datasource         string     `json:"datasource"`
	WorkloadList       string     `json:"workloadList"`
}

func (o ObjectKey) Verify() error {
	switch o.Level {
	case LevelNode:
		if o.NodeName == "" {
			return nil
			//errors.New("NodeName is required for node level")
		}
	case LevelWorkload:
		if o.WorkloadName == "" || o.WorkloadKind == "" || o.Namespace == "" {
			return nil
			//errors.New("WorkloadName and WorkloadKind and Namespace is required for workload level")
		}
	case LevelPod:
		if o.PodName == "" || o.Namespace == "" {
			return nil
			//errors.New("PodName and Namespace is required for pod level")
		}
	case LevelCloudService:
		if o.CloudServiceName == "" || o.Namespace == "" {
			return nil
			//errors.New("ApplicationName and Namespace is required for application level")
		}
	case LevelComponent:
		if o.CloudComponentName == "" || o.Namespace == "" {
			return nil
			//errors.New("ComponentName and Namespace is required for component level")
		}
	}
	return nil
}
