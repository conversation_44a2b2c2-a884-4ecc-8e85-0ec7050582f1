package backupserver

import (
	"time"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/formatter"
)

type Bucket struct {
	Name    string                  `json:"name"`
	Objects uint64                  `json:"objects"`
	Quota   BucketQuota             `json:"bucketQuota"`
	Size    formatter.FormattedSize `json:"size"`
	Read    bool                    `json:"read"`
	Write   bool                    `json:"write"`
}

type ResourceBucket struct {
	Bucket     `json:",inline"`
	OrganId    string    `json:"organId"`
	OrganName  string    `json:"organName"`
	CreateTime time.Time `json:"createTime"`
}

type ResourceBucketList struct {
	StorageServerId string                  `json:"storageServerId"`
	TotalObjects    uint64                  `json:"totalObjects"`
	TotalSize       formatter.FormattedSize `json:"totalSize"`
	Buckets         []ResourceBucket        `json:"buckets"`
	ServerInfo      StorageServerInfo       `json:"serverInfo"`
	Total           int                     `json:"total"`
	CurrentTotal    int                     `json:"currentTotal"`
}

type OrganBucket struct {
	Name        string                  `json:"name"`
	ProjectId   string                  `json:"projectId"`
	ProjectName string                  `json:"projectName"`
	Objects     uint64                  `json:"objects"`
	Size        formatter.FormattedSize `json:"size"`
	Quota       BucketQuota             `json:"bucketQuota"`
	CreateTime  CustomTime              `json:"createTime"`
}

type ProjectBucket struct {
	Name       string                  `json:"name"`
	Objects    uint64                  `json:"objects"`
	Size       formatter.FormattedSize `json:"size"`
	Quota      BucketQuota             `json:"bucketQuota"`
	CreateTime CustomTime              `json:"createTime"`
}

type QuotaType string

const (
	// HardQuota specifies a hard quota of usage for bucket
	HardQuota QuotaType = "hard"
)

// IsValid returns true if quota type is one of Hard
func (t QuotaType) IsValid() bool {
	return t == HardQuota
}

// BucketQuota holds bucket quota restrictions
type BucketQuota struct {
	Quota formatter.FormattedGiBSize `json:"quota"`
	Type  QuotaType                  `json:"quotatype,omitempty"`
}
