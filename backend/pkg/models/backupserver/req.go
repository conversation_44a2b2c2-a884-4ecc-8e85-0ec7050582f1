package backupserver

import (
	"encoding/json"
	"fmt"
	"math"
	"net"
	"strconv"
	"strings"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/regex"
)

const (
	// LimitQuotaSize 1<<63-1 = 9223372036854775807
	LimitQuotaSize uint64 = math.MaxInt64
)

type CreateBucketsReq struct {
	Name      string `json:"name"`
	Quota     bool   `json:"quota"`
	QuotaSize uint64 `json:"quotaSize"`
}

// UnmarshalJSON 自定义解析 JSON 输入，将 QuotaSize 从 GiB 转换为字节
func (c *CreateBucketsReq) UnmarshalJSON(data []byte) error {
	type Alias CreateBucketsReq
	aux := &struct {
		QuotaSize float64 `json:"quotaSize"` // 用 float64 读取 GiB 值
		*Alias
	}{
		Alias: (*Alias)(c),
	}

	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}
	// 将 QuotaSize 从 GiB 转换为字节
	c.QuotaSize = uint64(aux.QuotaSize * (1 << 30))
	return nil
}

type UpdateBucketReq struct {
	Name      string `json:"name"`
	Quota     bool   `json:"quota"`
	QuotaSize uint64 `json:"quotaSize"`
}

// UnmarshalJSON 自定义解析 JSON 输入，将 QuotaSize 从 GiB 转换为字节
func (c *UpdateBucketReq) UnmarshalJSON(data []byte) error {
	type Alias CreateBucketsReq
	aux := &struct {
		QuotaSize float64 `json:"quotaSize"` // 用 float64 读取 GiB 值
		*Alias
	}{
		Alias: (*Alias)(c),
	}

	if err := json.Unmarshal(data, &aux); err != nil {
		return err
	}
	// 将 QuotaSize 从 GiB 转换为字节
	c.QuotaSize = uint64(aux.QuotaSize * (1 << 30))
	return nil
}

type DeleteBucketReq struct {
	Name string `json:"name"`
}

type CheckBucketReq struct {
	Name string `json:"name"`
}

type AssignOrganToBucketReq struct {
	OrganId    string `json:"organId"`
	BucketName string `json:"bucketName"`
}

type UnassignOrganToBucketReq struct {
	OrganId    string `json:"organId"`
	BucketName string `json:"bucketName" `
}

type AssignProjectToBucketReq struct {
	StorageId  string `json:"storageId"`
	ProjectId  string `json:"projectId"`
	BucketName string `json:"bucketName" `
}

type UnassignProjectToBucketReq struct {
	StorageId  string `json:"storageId"`
	ProjectId  string `json:"projectId"`
	BucketName string `json:"bucketName"`
}

type GetProjectByNameReq struct {
	Name string `json:"name"`
}

type ProjectsReq struct {
	ProjectIds []string `json:"projectIds"`
}

type StorageServersReq struct {
	StorageServerType      string                 `json:"type"`
	NickName               string                 `json:"nickName"`
	S3StorageServersConfig S3StorageServersConfig `json:"s3StorageServersConfig"`
}

type S3StorageServersConfig struct {
	Url      ServersUrl `json:"url"`
	Username string     `json:"username"`
	Password string     `json:"password"`
}

type ServersUrl struct {
	Protocol string `json:"protocol"` // 访问协议
	Ip       string `json:"ip"`       // ip地址
	Port     int    `json:"port"`     // 端口
}

func (s *ServersUrl) SpellUrl() string {
	return s.Protocol + "://" + s.Ip + ":" + strconv.Itoa(s.Port)
}

func (s *ServersUrl) EndPoint() string {
	return s.Ip + ":" + strconv.Itoa(s.Port)
}

func ValidateStorageServerRequest(req StorageServersReq) error {
	// 验证存储类型是否为必填且是"S3"
	if req.StorageServerType == "" {
		return fmt.Errorf("存储类型是必填项")
	}
	if req.StorageServerType != "S3" {
		return fmt.Errorf("存储类型目前只支持S3类型")
	}

	// 验证名称是否为必填且长度不超过32个字符
	if req.NickName == "" {
		return fmt.Errorf("请输入名称")
	}
	if len([]rune(req.NickName)) > 32 {
		return fmt.Errorf("名称不得超过32个字符")
	}

	// 验证访问协议是否为必填项
	if req.S3StorageServersConfig.Url.Protocol != "http" &&
		req.S3StorageServersConfig.Url.Protocol != "https" {
		return fmt.Errorf("访问协议不正确")
	}

	// 验证访问地址的格式 (IP或域名)
	if !isValidHostOrIP(req.S3StorageServersConfig.Url.Ip) {
		return fmt.Errorf("地址格式不正确，请输入正确的IP或域名, 如***********或hc.com")
	}

	// 验证端口是否为必填且是数字
	if req.S3StorageServersConfig.Url.Port <= 0 {
		return fmt.Errorf("端口只能输入数字")
	}

	// 验证用户名是否为必填项
	if req.S3StorageServersConfig.Username == "" {
		return fmt.Errorf("请输入用户名")
	}

	// 验证密码是否为必填项
	if req.S3StorageServersConfig.Password == "" {
		return fmt.Errorf("请输入密码")
	}

	return nil
}

// 检查地址是否是合法的IP或域名
func isValidHostOrIP(address string) bool {
	return net.ParseIP(address) != nil || regex.URL(address)
}

func ValidateCreateBucket(req CreateBucketsReq) error {
	name := req.Name

	// 1. 检查名称长度
	if len(name) < 3 || len(name) > 63 {
		return fmt.Errorf("存储桶名称长度必须在3到63个字符之间")
	}

	// 2. 检查名称是否只包含小写字母、数字、点（.）和连字符（-）
	for _, ch := range name {
		if !(ch >= 'a' && ch <= 'z') && !(ch >= '0' && ch <= '9') && ch != '.' && ch != '-' {
			return fmt.Errorf("存储桶名称只能由小写字母、数字、点（.）和连字符（-）组成")
		}
	}

	// 3. 检查名称是否以小写字母或数字开头和结尾
	if !(name[0] >= 'a' && name[0] <= 'z' || name[0] >= '0' && name[0] <= '9') ||
		!(name[len(name)-1] >= 'a' && name[len(name)-1] <= 'z' || name[len(name)-1] >= '0' && name[len(name)-1] <= '9') {
		return fmt.Errorf("存储桶名称只能由小写字母或数字作为开头和结尾")
	}

	// 4. 检查名称是否包含两个相邻的点或与连字符相邻的点
	if strings.Contains(name, "..") || strings.Contains(name, ".-") || strings.Contains(name, "-.") {
		return fmt.Errorf("存储桶名称不得包含两个相邻的”.”或与连字符相邻的”.”")
	}

	// 5. 检查名称是否格式化为 IP 地址（例如，***********）
	segments := strings.Split(name, ".")
	if len(segments) == 4 {
		isIP := true
		for _, segment := range segments {
			if num, err := strconv.Atoi(segment); err != nil || num < 0 || num > 255 {
				isIP = false
				break
			}
		}
		if isIP {
			return fmt.Errorf("存储桶名称不得格式化为IP地址（例如，***********）")
		}
	}

	// 6. 检查名称是否以前缀 "xn--" 开头
	if strings.HasPrefix(name, "xn--") {
		return fmt.Errorf("存储桶名称不得以前缀 'xn--' 开头")
	}

	// 7. 检查名称是否以后缀 "-s3alias" 结尾
	if strings.HasSuffix(name, "-s3alias") {
		return fmt.Errorf("存储桶名称不得以后缀 '-s3alias' 结尾")
	}

	if req.Quota && req.QuotaSize > LimitQuotaSize {
		return fmt.Errorf("存储桶配额不能超过最大值")
	}
	return nil
}
func ValidateUpdateBucket(req UpdateBucketReq) error {
	if req.Quota && req.QuotaSize > LimitQuotaSize {
		return fmt.Errorf("存储桶配额不能超过最大值")
	}
	return nil
}
