package backupserver

import (
	"fmt"
	"time"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/formatter"
)

const timeFormat = "2006-01-02 15:04:05"

type CustomTime struct {
	time.Time
}

func (ct *CustomTime) UnmarshalJSON(b []byte) error {
	tStr := string(b)
	tStr = tStr[1 : len(tStr)-1]
	parsedTime, err := time.Parse(timeFormat, tStr)
	if err != nil {
		return err
	}
	ct.Time = parsedTime
	return nil
}

func (ct *CustomTime) MarshalJSON() ([]byte, error) {
	formatted := fmt.Sprintf("\"%s\"", ct.Format(timeFormat))
	return []byte(formatted), nil
}

type ResourceStorageServer struct {
	StorageServerId string    `json:"storageServerId"`
	NickName        string    `json:"nickName"`
	Type            string    `json:"type"`
	Url             string    `json:"url"`
	Status          Status    `json:"status"`
	UserName        string    `json:"userName"`
	Password        string    `json:"password"`
	CreatedAt       time.Time `json:"createdAt"`
	UpdatedAt       time.Time `json:"updatedAt"`
}

type OrganStorageServer struct {
	StorageServerId string        `json:"storageServerId"`
	NickName        string        `json:"nickName"`
	Type            string        `json:"type"`
	Status          Status        `json:"status"`
	BucketList      []OrganBucket `json:"bucketList"`
	// Url             string                  `json:"url"`
	Objects   uint64                  `json:"objects"`
	Size      formatter.FormattedSize `json:"size"`
	CreatedAt CustomTime              `json:"createdAt"`
}

type ProjectStorageServer struct {
	StorageServerId string          `json:"storageServerId"`
	NickName        string          `json:"nickName"`
	Type            string          `json:"type"`
	Status          Status          `json:"status"`
	BucketList      []ProjectBucket `json:"bucketList"`
	// Url             string                  `json:"url"`
	Objects   uint64                  `json:"objects"`
	Size      formatter.FormattedSize `json:"size"`
	CreatedAt CustomTime              `json:"createdAt"`
}

type StorageServerInfo struct {
	NickName     string `json:"nickName"`
	Type         string `json:"type"`
	Url          string `json:"url"`
	UserName     string `json:"userName"`
	Password     string `json:"password"`
	ServerStatus Status `json:"status"`
}

type Status struct {
	IsHealthy bool   `json:"isHealthy"`
	Reason    string `json:"reason"`
}

type ProjectStorageServerInfo struct {
	StorageServerId string `json:"storageServerId"`
	StorageServerInfo
}
