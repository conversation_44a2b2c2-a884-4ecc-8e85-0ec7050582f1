package workspace

import (
	database_aop "harmonycloud.cn/unifiedportal/translate-sdk-golang/database-aop"
	v1 "k8s.io/api/core/v1"
)

func init() {
	// translate group CloudService register
	database_aop.GroupInfo(CloudServiceResource{}, database_aop.TranslateGroupInfo{
		Name:          "portal-workspace-tab-name",
		UniqueKeyName: "CloudServiceName",
	})

	database_aop.GroupInfo(Resource{}, database_aop.TranslateGroupInfo{
		Name:          "portal-workspace-resource-name",
		UniqueKeyName: "ResourceName",
	})

	database_aop.GroupInfo(ResourceState{}, database_aop.TranslateGroupInfo{
		Name:          "portal-workspace-resource-state",
		UniqueKeyName: "ResourceStateName",
	})
}

type Icon struct {
	// Name icon name
	Name string `json:"name,omitempty"`
	// URL icon url
	URL string `json:"url,omitempty"`
}

type CloudServiceResource struct {
	CloudServiceName    string          `json:"cloudServiceName"`
	TabName             string          `json:"tabName"`
	TabAlias            string          `json:"tabAlias" translate:"keyName=tabAlias"`
	ResourceGroups      []ResourceGroup `json:"resourceGroups" translateObject:""`
	ProjectResourceList ProjectResource `json:"projectResourceList" translateObject:""`
}

type ResourceGroup struct {
	WithGroup    bool       `json:"withGroup"`
	GroupName    *string    `json:"groupName"`
	Number       *int       `json:"number"`
	ResourceList []Resource `json:"resourceList" translateObject:""`
}
type Resource struct {
	Id                 int             `json:"id"`
	ResourceName       string          `json:"resourceName"`
	ResourceAlias      string          `json:"resourceAlias" translate:"keyName=resourceAlias"`
	ResourceTotalCount int             `json:"resourceTotalCount"`
	Icon               Icon            `json:"icon"`
	ResourceStateList  []ResourceState `json:"resourceStateList" translateObject:""`
}

type ResourceState struct {
	ResourceStateColor string `json:"resourceStateColor"`
	ResourceStateName  string `json:"resourceStateName"`
	ResourceStateAlias string `json:"resourceStateAlias" translate:"keyName=resourceStateAlias"`
	Count              int    `json:"count"`
	Tips               string `json:"tips,omitempty" translate:"keyName=tips"`
}

type ProjectResource struct {
	ResourceType            string                `json:"resourceType"`
	EnableJump              bool                  `json:"enableJump"`
	JumpUrl                 string                `json:"jumpUrl"`
	ProjectResourceInfoList []ProjectResourceInfo `json:"projectResourceInfoList" translateObject:""`
}

type ProjectResourceInfo struct {
	OrganId              string          `json:"organId"`
	CreateTime           string          `json:"createTime"`
	ProjectId            string          `json:"projectId"`
	ProjectName          string          `json:"projectName"`
	ProjectResourceState []ResourceState `json:"projectResourceState" translateObject:""`
}

type ClusterNamespace struct {
	Cluster       string         `json:"cluster,omitempty"`
	NamespaceList []v1.Namespace `json:"namespaceList,omitempty"`
}

type ResourceInstanceInfo struct {
	ResourceInstanceList []ResourceInstance `json:"data"`
}

type ResourceInstance struct {
	OrganId              string `json:"organId"`
	ResourceInstanceId   string `json:"resourceInstanceId"`
	ResourceInstanceName string `json:"resourceInstanceName"`
	CreateTime           string `json:"createTime"`
}

type WorkspaceResourceType struct {
	ID            int    `json:"id"`
	ResourceType  string `json:"resourceType"`
	ResourceName  string `json:"resourceName"`
	ResourceAlias string `json:"resourceAlias"`
	Group         string `json:"group"`
	Version       string `json:"version"`
	EnableJump    bool   `json:"EnableJump"`
	Url           string `json:"url"`
	IconName      string `json:"iconName"`
	IconUrl       string `json:"iconUrl"`
}
