package addon

import (
	stellarisv1alhpha1 "harmonycloud.cn/stellaris/pkg/apis/stellaris/v1alpha1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	database_aop "harmonycloud.cn/unifiedportal/translate-sdk-golang/database-aop"
)

// ComponentRequestList 组件请求列表
type ComponentRequestList struct {
	List []ComponentRequest `json:"list"` //
}

// ComponentRequest 组件请求
type ComponentRequest struct {
	Type                    string                                        `json:"type" example:"类型"` // 类型
	Name                    string                                        `json:"name" example:"名称"` // 名称
	ElkConfig               ElkConfig                                     `json:"elkConfig" `        // es配置
	SisyphusConfig          SisyphusConfig                                `json:"sisyphusConfig" `   // node-up-down配置
	MonitoringConfig        MonitoringConfig                              `json:"monitoringConfig" `
	CoreDnsConfig           CoreDnsConfig                                 `json:"coreDnsConfig" `
	BaselineCheckerConfig   BaselineCheckerConfig                         `json:"baselineCheckerConfig" `
	Selectors               []EndpointSelector                            `json:"selectors" `
	Schema                  []stellarisv1alhpha1.AddonConfigurationSchema `json:"schemas" `
	Statics                 []StaticInfo                                  `json:"statics" `
	BaseFlag                bool                                          `json:"baseFlag" example:"false"`
	ResourceAggregateConfig ResourceAggregateConfig                       `json:"resourceAggregateConfig" `
	AppDecompileConfig      AppDecompileConfig                            `json:"appDecompileConfig" `
	GpuConfig               GpuConfig                                     `json:"gpuConfig" `
}

// GpuConfig GPU配置
type GpuConfig struct {
	GpuNodes []GpuNodes `json:"gpuNodes"`
}

// GpuNodes GPU节点
type GpuNodes struct {
	Allocatable map[string]string `json:"allocatable"`
	Capacity    map[string]string `json:"capacity"`
}

// AppDecompileConfig app反编译配置
type AppDecompileConfig struct {
	Protocol string `json:"protocol,omitempty"`
	Ip       string `json:"ip,omitempty"`
	Port     int    `json:"port,omitempty"`
}

// ResourceAggregateConfig 资源聚合配置
type ResourceAggregateConfig struct {
	Protocol string `json:"protocol,omitempty" example:"协议"`
	Port     int    `json:"port,omitempty" example:"8080"`
	Ip       string `json:"ip,omitempty" example:"***********"`
}

// StaticInfo 静态信息
type StaticInfo struct {
	EndPoint string `json:"endPoint,omitempty" example:"endPoint"`
	Type     string `json:"type,omitempty" example:"type"`
}

// EndpointSelector 节点选择器
type EndpointSelector struct {
	Namespace string            `json:"namespace,omitempty" example:"命名空间"`
	Labels    map[string]string `json:"labels,omitempty"`
	Include   string            `json:"include,omitempty" example:"包含"`
	Type      string            `json:"type,omitempty" example:"组件实例类型"`
}

// CoreDnsConfig CoreDns配置
type CoreDnsConfig struct {
	EnableErrorLogging bool        `json:"enableErrorLogging,omitempty" example:"true"`
	Autopath           bool        `json:"autopath,omitempty" example:"true"`
	CacheTime          int         `json:"cacheTime,omitempty" example:"3000"`
	Hosts              []DnsDomain `json:"hosts,omitempty" `
	Forward            []DnsDomain `json:"forward,omitempty" `
}

// DnsDomain 域名
type DnsDomain struct {
	Domain     string   `json:"domain" example:"域名"`
	Resolution []string `json:"resolution" `
}

// MonitoringConfig 监控配置
type MonitoringConfig struct {
	Protocol      string `json:"protocol,omitempty" example:"协议"`
	Port          int    `json:"port,omitempty" example:"8080"`
	Ip            string `json:"ip,omitempty" example:"***********"`
	Retention     string `json:"retention,omitempty" example:"持久化时间"`
	AlertPort     int    `json:"alertPort,omitempty" example:"8080"`
	AlertIp       string `json:"alertIP,omitempty" example:"***********"`
	AlertProtocol string `json:"alertProtocol,omitempty" example:"grafana协议"`
}

// ElkConfig Elk配置
type ElkConfig struct {
	Protocol     string `json:"protocol" example:"协议"`
	Port         int    `json:"port" example:"8080"`
	Ip           string `json:"ip" example:"***********"`
	Name         string `json:"esName" example:"用户名"`
	Password     string `json:"esPassword" example:"密码"`
	EnableBackup bool   `json:"enableBackup" example:"false"`
}

// SisyphusConfig Sisyphus配置
type SisyphusConfig struct {
	Ip               string `json:"ip,omitempty"`
	Port             int    `json:"port,omitempty"`
	Protocol         string `json:"protocol,omitempty"`
	SisyphusName     string `json:"sisyphusName,omitempty"`
	SisyphusPassword string `json:"sisyphusPassword,omitempty"`
}

// BaselineCheckerConfig 基线检查
type BaselineCheckerConfig struct {
	// Ip 默认ip
	Ip string `json:"ip" example:"***********"`
	// Port 默认 30002
	Port int `json:"port" example:"8080"`
	// 默认 http
	Protocol string `json:"protocol" example:"协议"`
}

type SisyphusAddressConfig struct {
	Address  string
	Username string
	Password string
}

// ComponentDetailResponse
// 获取组件返回信息
type ComponentDetailResponse struct {
	Type                  string                                        `json:"type"`                     // 类型
	Name                  string                                        `json:"name"`                     // 名称
	NickName              string                                        `json:"nickName"`                 // 显示名称
	Description           string                                        `json:"description"`              // 描述
	ElkConfig             ElkConfig                                     `json:"elkConfig"`                // es配置
	Sisyphus              SisyphusConfig                                `json:"sisyphusConfig"`           // node-up-down配置
	Monitoring            MonitoringConfig                              `json:"monitoringConfig"`         // prom配置
	CoreDNS               CoreDnsConfig                                 `json:"coreDnsConfig"`            // coreDns配置
	BaselineCheckerConfig BaselineCheckerConfig                         `json:"baselineCheckerConfig"`    // 基线检查
	Selectors             []EndpointSelector                            `json:"selectors"`                // selectors
	Schemas               []stellarisv1alhpha1.AddonConfigurationSchema `json:"schemas"`                  // schemas（假设JSONObject对应于JSON结构）
	Statics               []StaticInfo                                  `json:"statics"`                  // statics
	Status                constants.AddonStatusEnum                     `json:"status"`                   // 组件状态
	LastMonitor           string                                        `json:"lastMonitorTime"`          // 最后检测时间
	PodInfo               []ComponentPodInfo                            `json:"podInfoList"`              // pod信息
	Healthcheck           []HealthcheckCondition                        `json:"healthcheckConditionList"` // healthcheck条件列表
	Resource              ResourceAggregateConfig                       `json:"resourceAggregateConfig"`  // 关联资源配置
	AppDecompile          AppDecompileConfig                            `json:"appDecompileConfig"`       // app反编译配置
}

// ComponentPodInfo 组件pod信息
type ComponentPodInfo struct {
	Name      string `json:"name,omitempty"`
	Status    string `json:"status,omitempty"`
	Namespace string `json:"namespace,omitempty"`
	Timestamp string `json:"timestamp,omitempty"`
	Type      string `json:"type,omitempty"`
	Endpoint  string `json:"endpoint,omitempty"`
}

// HealthcheckCondition 健康检查条件
type HealthcheckCondition struct {
	Timestamp string `json:"timestamp,omitempty" example:"时间"`
	Message   string `json:"message,omitempty" example:"信息"`
	Reason    string `json:"reason,omitempty" example:"原因"`
	Type      string `json:"type,omitempty" example:"类型"`
	Status    *bool  `json:"status,omitempty" example:"true"`
}

// ListComponentDetailResponse
// 返回集群插件所有信息
type ListComponentDetailResponse []ComponentDetailResponse

// ListComponentRequest 集群组件请求信息
type ListComponentRequest []ComponentRequest

// ListResponse 集群组件列表
type ListResponse []ComponentListDTO

func init() {
	database_aop.GroupInfo(ComponentListDTO{}, database_aop.TranslateGroupInfo{
		Name:          "k8s-resource-cluster-component",
		UniqueKeyName: "Name",
	})
}

// ComponentListDTO 集群组件列表
type ComponentListDTO struct {
	Name        string                    `json:"name" example:"名称"`
	EnName      string                    `json:"enName" example:"英文名称"`
	ChName      string                    `json:"chName" example:"中文名称"  translate:"keyName=chName"`
	Description string                    `json:"description" example:"说明" translate:"keyName=description"`
	Status      constants.AddonStatusEnum `json:"status" `
	BaseFlag    bool                      `json:"baseFlag" example:"false"`
	// todo lanchao deadline: 2024年6月24日 增加组件状态，等待底座返回
	Version string `json:"version" example:"版本"`
}

func init() {
	database_aop.GroupInfo(ComponentScanResponse{}, database_aop.TranslateGroupInfo{
		Name:          "k8s-resource-cluster-component",
		UniqueKeyName: "Name",
	})
}

// ComponentScanResponse 集群组件扫描信息
type ComponentScanResponse struct {
	Name        string `json:"name,omitempty" example:"组件名称"`
	NickName    string `json:"nickName,omitempty" example:"组件名称" translate:"keyName=chName"`
	Description string `json:"description,omitempty" example:"组件描述" translate:"keyName=description"`
	Active      bool   `json:"active" example:"true"`
}

// ListComponentScanResponse 集群组件扫描信息
type ListComponentScanResponse []ComponentScanResponse

// ComponentDto 集群组件请求信息
type ComponentDto struct {
	Type                     string                                        `json:"type" binding:"required"`  // 类型
	Name                     string                                        `json:"name" binding:"required"`  // 名称
	EnName                   string                                        `json:"enName"`                   // 英文名称
	ChName                   string                                        `json:"chName"`                   // 中文名称
	Description              string                                        `json:"description"`              // 说明
	ElkConfig                ElkConfig                                     `json:"elkConfig"`                // es配置
	SisyphusConfig           SisyphusConfig                                `json:"sisyphusConfig"`           // node-up-down配置
	Monitoring               MonitoringConfig                              `json:"monitoringConfig"`         // prom配置
	BaselineCheckerConfig    BaselineCheckerConfig                         `json:"baselineCheckerConfig"`    // 基线检查
	Selectors                []EndpointSelector                            `json:"selectors"`                // selectors
	Statics                  []StaticInfo                                  `json:"static"`                   // statics
	CoreDnsConfig            CoreDnsConfig                                 `json:"dnsConfig"`                // dns配置
	LastMonitorTime          string                                        `json:"lastMonitorTime"`          // 最后检测时间
	Status                   constants.AddonStatusEnum                     `json:"status"`                   // 组件状态
	Schema                   []stellarisv1alhpha1.AddonConfigurationSchema `json:"schema,omitempty"`         // schemas（假设JSONObject对应于JSON结构）
	PodInfoList              []ComponentPodInfo                            `json:"podInfoList"`              // pod信息
	BaseFlag                 bool                                          `json:"baseFlag"`                 // 基础标志
	Resource                 ResourceAggregateConfig                       `json:"resourceAggregateConfig"`  // 关联资源配置
	AppDecompile             AppDecompileConfig                            `json:"appDecompileConfig"`       // app反编译配置
	HealthcheckConditionList []HealthcheckCondition                        `json:"healthcheckConditionList"` // healthcheck条件列表
}

func ConverComponentDto2ComponentDetailResponse(componentDto ComponentDto) ComponentDetailResponse {
	return ComponentDetailResponse{
		Type:                  componentDto.Type,
		Name:                  componentDto.Name,
		Description:           componentDto.Description,
		ElkConfig:             componentDto.ElkConfig,
		Sisyphus:              componentDto.SisyphusConfig,
		Monitoring:            componentDto.Monitoring,
		CoreDNS:               componentDto.CoreDnsConfig,
		BaselineCheckerConfig: componentDto.BaselineCheckerConfig,
		Selectors:             componentDto.Selectors,
		Schemas:               componentDto.Schema,
		Statics:               componentDto.Statics,
		LastMonitor:           componentDto.LastMonitorTime,
		PodInfo:               componentDto.PodInfoList,
		Healthcheck:           componentDto.HealthcheckConditionList,
		Resource:              componentDto.Resource,
		AppDecompile:          componentDto.AppDecompile,
		Status:                componentDto.Status,
	}
}

func ConverComponentRequest2ComponentDto(request ComponentRequest) ComponentDto {
	componentDto := ComponentDto{
		Type:                  request.Type,
		Name:                  request.Name,
		ElkConfig:             request.ElkConfig,
		SisyphusConfig:        request.SisyphusConfig,
		Monitoring:            request.MonitoringConfig,
		BaselineCheckerConfig: request.BaselineCheckerConfig,
		Selectors:             request.Selectors,
		Statics:               request.Statics,
		CoreDnsConfig:         request.CoreDnsConfig,
		Schema:                request.Schema,
		BaseFlag:              request.BaseFlag,
		Resource:              request.ResourceAggregateConfig,
		AppDecompile:          request.AppDecompileConfig,
	}
	// Schema特殊处理
	// heimdallr
	if request.Name == "heimdallr" {
		componentDto.Schema = []stellarisv1alhpha1.AddonConfigurationSchema{
			{
				Name: "heimdallr",
				Type: "unstructuredList",
				UnstructuredList: &stellarisv1alhpha1.UnstructuredListSchema{
					Group:   "heimdallr.harmonycloud.cn",
					Version: "v1alpha1",
					Kind:    "NetworkDetailList",
				},
				Data: []stellarisv1alhpha1.AddonConfigurationData{
					{
						Key: "heimdallrCueRender",
						CueRenderRule: `output: [
  for _, v in {
      for item in context.items {
          "\(item.metadata.name)": {
            name: item.metadata.name
            bgpMode: item.spec.bgpMode
            stackMode: item.spec.stackMode
            poolMode: item.spec.poolMode
            addMode: item.spec.addMode
            fixMode: item.spec.fixMode
            outMode: item.spec.outMode
            vlanMode: item.spec.vlanMode
            auditMode: item.spec.auditMode
            poolUpdate: item.spec.poolUpdate
            service: item.spec.service
            gateway: item.spec.gateway
            mainNetOnly: item.spec.mainNetOnly
          }
      }
  } {v}
]
`,
					},
				},
			},
		}
	}
	// gpu
	if request.Name == "gpu" {
		componentDto.Schema = []stellarisv1alhpha1.AddonConfigurationSchema{
			{
				Name: "gpu",
				Type: "unstructuredList",
				UnstructuredList: &stellarisv1alhpha1.UnstructuredListSchema{
					Group:   "",
					Version: "v1",
					Kind:    "NodeList",
				},
				Data: []stellarisv1alhpha1.AddonConfigurationData{
					{
						Key: "gpuCueRender",
						CueRenderRule: `import (
  "strings"
)

hasOrion: bool | *false

hasGPUManager: bool | *false

for item in context.items {
  if item.metadata.labels != _|_ && item.metadata.labels."nvidia-device-enable" != _|_ {
    hasOrion: true
  }
  if item.metadata.labels != _|_ && item.metadata.labels."nodetype" != _|_ {
    hasGPUManager: true
  }
}

nodes: [
  for _, v in {
    for item in context.items {
      if item.metadata.labels != _|_ && item.metadata.labels."nvidia-device-enable" != _|_ && item.status.capacity != _|_ && item.status.capacity."virtaitech.com/gpu" != _|_ {
        "\(item.metadata.name)-orion": {
          nodeName: item.metadata.name
          gpuType: "orion"
          capacity: {
            for k, value in item.status.capacity {
              if strings.Contains(k, "virtaitech.com") {
                "\(k)": value
              }
            }
          }
          if item.status.allocatable != _|_ && item.status.capacity."virtaitech.com/gpu" != _|_ {
            allocatable: {
              for k, value in item.status.allocatable {
                if strings.Contains(k, "virtaitech.com") {
                  "\(k)": value
                }
              }
            }
          }
        }
      }
      if item.metadata.labels != _|_ && item.metadata.labels."nodetype" != _|_ && item.status.capacity != _|_ && item.status.allocatable."tencent.com/vcuda-core" != _|_ {
        "\(item.metadata.name)-gpu": {
          nodeName: item.metadata.name
          gpuType: "gpuManager"
          capacity: {
            for k, value in item.status.capacity {
              if strings.Contains(k, "tencent.com") {
                "\(k)": value
              }
            }
          }
          if item.status.allocatable != _|_ && item.status.allocatable."tencent.com/vcuda-core" != _|_ {
            allocatable: {
              for k, value in item.status.allocatable {
                if strings.Contains(k, "tencent.com") {
                  "\(k)": value
                }
              }
            }
          }
        }
      }
    }
  } {v}
]

output: {
  gpuNodes: nodes
  if hasOrion && !hasGPUManager {
    gpuType: "orion"
  }
  if hasGPUManager && !hasOrion {
    gpuType: "gpuManager"
  }
  if hasOrion && hasGPUManager {
    gpuType: "orion,gpuManager"
  }
  if !hasOrion && !hasGPUManager {
    gpuType: ""
  }
}
`,
					},
				},
			},
		}
	}
	return componentDto
}

type ComponentsResponse struct {
	Name        string `json:"name"`
	NickName    string `json:"nickName"`
	Description string `json:"description"`
	Status      string `json:"status"`
	BaseFlag    bool   `json:"baseFlag"`
}

func ConvertComponentDto2ComponentResponse(dto ComponentListDTO) ComponentsResponse {
	return ComponentsResponse{
		Name:        dto.Name,
		NickName:    GetNickName(dto.ChName, dto.EnName),
		Description: dto.Description,
		Status:      constants.ConverAddonsStatus2String(dto.Status),
		BaseFlag:    dto.BaseFlag,
	}
}

func ConverResponseList2ComponentsList(listResponse ListResponse) []ComponentsResponse {
	list := make([]ComponentsResponse, 0, len(listResponse))
	for _, dto := range listResponse {
		list = append(list, ConvertComponentDto2ComponentResponse(dto))
	}
	return list
}

func GetNickName(cnName, enName string) string {
	if enName == "" {
		return cnName + ""
	}
	return cnName + "(" + enName + ")"
}
