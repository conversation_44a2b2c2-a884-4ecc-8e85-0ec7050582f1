package velero

//type RestoreTemplateItem struct {
//	RestoresTemplateId   string `json:"restoresTemplateId"`   //恢复策略id
//	RestoresTemplateName string `json:"restoresTemplateName"` // 恢复策略名称
//	Status               string `json:"status"`               // 状态
//	ClusterName          string `json:"clusterName"`          // 集群名称
//	BackupsName          string `json:"backupsName"`          // 备份文件名称
//	Description          string `json:"description"`          // 描述
//	CreateTime           string `json:"createTime"`           // 创建时间
//}

type RestoreTemplate struct {
	RestoreTemplateId    string               `json:"restoreTemplateId"`   //恢复策略id
	RestoreTemplateName  string               `json:"restoreTemplateName"` // 恢复策略名称
	ScheduleName         string               `json:"scheduleName"`
	RestoreType          int                  `json:"restoreType"`
	TargetCluster        string               `json:"targetCluster"`
	FromCluster          string               `json:"fromCluster"`
	Status               string               `json:"status"`           // 状态
	Description          string               `json:"description"`      // 描述
	CreateTime           string               `json:"createTime"`       // 创建时间
	BackupsName          string               `json:"backupsName"`      // 备份文件名称
	Namespaces           []string             `json:"namespaces"`       // 恢复命名空间
	IncludeResources     map[string]string    `json:"includeResources"` // 指定资源标签
	ExcludeResources     map[string]string    `json:"excludeResources"` // 排除资源标签
	IsModify             bool                 `json:"isModify"`         //修改资源
	Modifys              []Modify             `json:"modify"`
	StorageServers       StorageServers       `json:"storageServers"`       // 备份服务器信息
	StorageServersStatus StorageServersStatus `json:"storageServersStatus"` //备份服务器状态
	StorageServersInfo   string               `json:"storageServersInfo"`   //备份服务器异常信息
}

type Modify struct {
	ResourceType string      `json:"resourceType"`
	Namespaces   []Namespace `json:"namespaces"`
	Contents     []Content   `json:"content"`
}

type Namespace struct {
	Name         string   `json:"name"`
	ResourceName []string `json:"resourceName"`
}

type Content struct {
	Type  string `json:"type"`
	Path  string `json:"path"`
	Value string `json:"value,omitempty"`
}

//func (r *RestoreTemplate) Convert2RestoreTemplateItem() *RestoreTemplateItem {
//	return &RestoreTemplateItem{
//		RestoresTemplateId:   r.RestoreTemplateId,
//		RestoresTemplateName: r.RestoreTemplateName,
//		Status:               r.Status,
//		//TargetCluster:          r.TargetCluster,
//		BackupsName: r.BackupsName,
//		Description: r.Description,
//		CreateTime:  r.CreateTime,
//	}
//}

type Restore struct {
	TargetCluster        string            `json:"targetCluster"`        // 集群名称
	RestoreName          string            `json:"restoreName"`          // 恢复记录名称
	RestoreTemplateId    string            `json:"restoreTemplateId"`    // 恢复策略id
	RestoreTemplateName  string            `json:"restoreTemplateName"`  // 恢复策略名称
	Description          string            `json:"description"`          // 描述
	BackupsName          string            `json:"backupsName"`          // 备份文件名称
	CreateTime           string            `json:"createTime"`           // 开始时间
	FinishTime           string            `json:"finishTime"`           // 完成时间
	Status               string            `json:"status"`               // 状态
	Namespaces           []string          `json:"namespaces"`           // 恢复命名空间
	IncludeResources     map[string]string `json:"includeResources"`     // 指定资源标签
	ExcludeResources     map[string]string `json:"excludeResources"`     // 排除资源标签
	ResourcesSize        int               `json:"resourcesSize"`        // 资源数
	PersistentVolumeSize int               `json:"persistentVolumeSize"` // 存储卷数
	Notes                string            `json:"notes"`                // 备注
}
