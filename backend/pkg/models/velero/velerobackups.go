package velero

type Backups struct {
	BackupsName          string   `json:"backupsName"`          // 备份文件名称
	OwnerSchedulesName   string   `json:"ownerSchedulesName"`   //备份策略名称
	CreateTime           string   `json:"createTime"`           // 开始时间
	FinishTime           string   `json:"finishTime"`           // 完成时间
	Namespaces           []string `json:"namespaces"`           // 备份命名空间
	ExecuteType          string   `json:"executeType"`          // 执行类型
	Status               string   `json:"status"`               // 状态
	ResourcesSize        int      `json:"resourcesSize"`        // 资源数
	PersistentVolumeSize int      `json:"persistentVolumeSize"` // 存储卷数
	Notes                string   `json:"notes"`                // 备注
}
