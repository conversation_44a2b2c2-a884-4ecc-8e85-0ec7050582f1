package velero

type ModifyRule struct {
	Version               string                 `json:"version"`
	ResourceModifierRules []ResourceModifierRule `json:"resourceModifierRules" yaml:"resourceModifierRules"`
}

type ResourceModifierRule struct {
	Conditions Condition `json:"conditions"`
	Patches    []Patch   `json:"patches"`
}

type Condition struct {
	GroupResource     string   `json:"groupResource" yaml:"groupResource"`
	ResourceNameRegex string   `json:"resourceNameRegex" yaml:"resourceNameRegex"`
	Namespaces        []string `json:"namespaces"`
}

type Patch struct {
	Operation string `json:"operation,omitempty"`
	Path      string `json:"path"`
	Value     string `json:"value"`
}
