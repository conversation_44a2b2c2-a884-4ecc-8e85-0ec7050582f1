package velero

import (
	"bytes"
	"encoding/base64"
	"reflect"
	"strconv"
	"time"

	"github.com/google/uuid"
	veleroV1 "github.com/vmware-tanzu/velero/pkg/apis/velero/v1"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/backupserver"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/formatter"
	k8sV1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// ServersUrl
// 服务器url
type ServersUrl struct {
	Protocol string `json:"protocol"` // 访问协议
	Ip       string `json:"ip"`       // ip地址
	Port     int    `json:"port"`     // 端口
}

func (s *ServersUrl) SpellUrl() string {
	return s.Protocol + "://" + s.Ip + ":" + strconv.Itoa(s.Port)
}
func (s *ServersUrl) EndPoint() string {
	return s.Ip + ":" + strconv.Itoa(s.Port)
}

type S3StorageServersConfig struct {
	Url      ServersUrl `json:"url"`      // 访问地址
	Bucket   string     `json:"bucket"`   // 存储桶
	Username string     `json:"username"` // 账户名
	Password string     `json:"password"` // 密码

	BackupStorageLocationName string `json:"backupStorageLocationName"` // bsl名称
}

func (s *S3StorageServersConfig) DecodePassword() string {
	decodedPassword, _ := base64.StdEncoding.DecodeString(s.Password)
	return string(decodedPassword)
}
func (s *S3StorageServersConfig) CreateBackupStorageLocation(storageServersId string) (*veleroV1.BackupStorageLocation, *k8sV1.Secret) {
	secretKey := "key"

	pwd, decodeError := base64.StdEncoding.DecodeString(s.Password)
	if decodeError != nil {
		return nil, nil
	}

	src := "[default]" + "\n" +
		"aws_access_key_id = " + s.Username + "\n" +
		"aws_secret_access_key = " + bytes.NewBuffer(pwd).String() + "\n"

	secret := k8sV1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      s.BackupStorageLocationName,
			Namespace: constants.VeleroNamespace,
		},
		Data: map[string][]byte{
			secretKey: []byte(src),
		},
	}

	bsl := veleroV1.BackupStorageLocation{
		ObjectMeta: metav1.ObjectMeta{
			Name:      s.BackupStorageLocationName,
			Namespace: constants.VeleroNamespace,
			Labels: map[string]string{
				constants.StorageServerId: storageServersId,
			},
		},
		Spec: veleroV1.BackupStorageLocationSpec{
			Provider: "aws",
			StorageType: veleroV1.StorageType{
				ObjectStorage: &veleroV1.ObjectStorageLocation{
					Bucket: s.Bucket,
				},
			},
			Config: map[string]string{
				"region":           "minio",
				"s3ForcePathStyle": "true",
				"s3Url":            s.Url.SpellUrl(),
			},
			Credential: &k8sV1.SecretKeySelector{
				LocalObjectReference: k8sV1.LocalObjectReference{
					Name: s.BackupStorageLocationName,
				},
				Key: secretKey,
			},
			Default: false,
		},
	}

	return &bsl, &secret
}

func (s *S3StorageServersConfig) UpdateBackupStorageLocation(storageServersId string, bsl *veleroV1.BackupStorageLocation, secret *k8sV1.Secret) (*veleroV1.BackupStorageLocation, *k8sV1.Secret) {
	secretKey := "key"

	pwd, decodeError := base64.StdEncoding.DecodeString(s.Password)
	if decodeError != nil {
		return nil, nil
	}

	src := "[default]" + "\n" +
		"aws_access_key_id = " + s.Username + "\n" +
		"aws_secret_access_key = " + bytes.NewBuffer(pwd).String() + "\n"

	secret.Data = map[string][]byte{
		secretKey: []byte(src),
	}

	bsl.Labels[constants.StorageServerId] = storageServersId
	bsl.Spec.StorageType.ObjectStorage.Bucket = s.Bucket
	bsl.Spec.Config["s3Url"] = s.Url.SpellUrl()
	bsl.Spec.Credential.LocalObjectReference.Name = s.BackupStorageLocationName
	bsl.Spec.Credential.Key = secretKey

	return bsl, secret
}

func EqualBackupStorageLocation(location veleroV1.BackupStorageLocation, storageLocation veleroV1.BackupStorageLocation) bool {
	return location.Name == storageLocation.Name &&
		location.Namespace == storageLocation.Namespace &&
		reflect.DeepEqual(location.Spec.Config, storageLocation.Spec.Config) &&
		location.Spec.Default == storageLocation.Spec.Default &&
		reflect.DeepEqual(*location.Spec.ObjectStorage, *storageLocation.Spec.ObjectStorage) &&
		location.Spec.Provider == storageLocation.Spec.Provider
}

// StorageServers
// 备份仓库
type StorageServers struct {
	StorageServersId       string                   `json:"storageServersId"` // 备份仓库id
	NickName               string                   `json:"nickname"`         // 显示名称
	Description            string                   `json:"description"`
	Clusters               []string                 `json:"clusters"`
	CreatedAt              time.Time                `column:"created_at" json:"createdAt"`
	UpdatedAt              time.Time                `column:"updated_at" json:"updatedAt"`
	Bucket                 string                   `json:"bucket"`
	BackupServerId         string                   `json:"backupServerId"`
	BackupServerName       string                   `json:"backupServerName"`
	BucketQuota            backupserver.BucketQuota `json:"bucketQuota"`
	BucketUsage            formatter.FormattedSize  `json:"bucketUsage"`
	S3StorageServersConfig S3StorageServersConfig   `json:"s3StorageServersConfig"` // S3类型存储服务器配置
}

func (s *StorageServers) PreStorageServerConfig() error {
	s.S3StorageServersConfig.BackupStorageLocationName = "bsl-" + uuid.New().String()
	return nil
}

func (s *StorageServers) UpdateProStorageServerConfig(storageServers *StorageServers) error {
	s.S3StorageServersConfig.BackupStorageLocationName = storageServers.S3StorageServersConfig.BackupStorageLocationName
	return nil
}

//func (s *StorageServers) GetStorageServerConfigString() (string, error) {
//	var config string
//	data, jsonError := json.Marshal(s.S3StorageServersConfig)
//	if jsonError != nil {
//		return "", jsonError
//	}
//	config = string(data)
//
//	return config, nil
//}

func (s *StorageServers) GetBackupStorageLocationName() string {
	return s.S3StorageServersConfig.BackupStorageLocationName
}

func (s *StorageServers) ClearInformation() {
	s.S3StorageServersConfig.Username = ""
	s.S3StorageServersConfig.Password = ""
}
