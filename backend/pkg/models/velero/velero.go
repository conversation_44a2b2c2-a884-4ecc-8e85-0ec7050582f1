package velero

type VeleroResources struct {
	ResourceType string `json:"resourceType"` // 资源类型
	Namespace    string `json:"namespace"`    // 命名空间
	ResourceName string `json:"resourceName"` // 资源名称
}

type VeleroVolumes struct {
	Namespace                 string `json:"namespace"`                 // 命名空间
	PersistentVolumeClaimName string `json:"persistentVolumeClaimName"` // 存储卷声明名称
	PodName                   string `json:"podName"`                   //所属pod名称
	VolumeName                string `json:"volumeName"`                // 挂载卷名称
}

type ResourceByType struct {
	ResourceType string              `json:"resourceType"`
	Namespace    []NamespaceResource `json:"namespace"`
}

type NamespaceResource struct {
	Namespace     string   `json:"namespace"`
	ResourceNames []string `json:"resourceNames"`
}
