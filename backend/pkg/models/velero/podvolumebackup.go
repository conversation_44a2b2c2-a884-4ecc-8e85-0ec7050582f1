package velero

import (
	veleroV1 "github.com/vmware-tanzu/velero/pkg/apis/velero/v1"
)

type PodVolumeBackup struct {
	Name                      string
	Namespace                 string
	Annotations               map[string]string
	Labels                    map[string]string
	Node                      string
	PodName                   string
	PodNamespace              string
	VolumeName                string
	BackupStorageLocationName string
	UploaderType              string
	Tags                      map[string]string
}

func Convert2PodVolumeBackup(podVolumeBackup veleroV1.PodVolumeBackup) *PodVolumeBackup {
	return &PodVolumeBackup{
		Name:                      podVolumeBackup.Name,
		Namespace:                 podVolumeBackup.Namespace,
		Annotations:               podVolumeBackup.Annotations,
		Labels:                    podVolumeBackup.Labels,
		Node:                      podVolumeBackup.Spec.Node,
		PodName:                   podVolumeBackup.Spec.Pod.Name,
		PodNamespace:              podVolumeBackup.Spec.Pod.Namespace,
		VolumeName:                podVolumeBackup.Spec.Volume,
		BackupStorageLocationName: podVolumeBackup.Spec.BackupStorageLocation,
		UploaderType:              podVolumeBackup.Spec.UploaderType,
		Tags:                      podVolumeBackup.Spec.Tags,
	}
}
