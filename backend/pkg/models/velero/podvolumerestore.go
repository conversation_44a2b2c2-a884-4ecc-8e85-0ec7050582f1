package velero

import veleroV1 "github.com/vmware-tanzu/velero/pkg/apis/velero/v1"

type PodVolumeRestore struct {
	Name                      string
	Namespace                 string
	Annotations               map[string]string
	Labels                    map[string]string
	PodName                   string
	PodNamespace              string
	VolumeName                string
	BackupStorageLocationName string
	UploaderType              string
}

func Convert2PodVolumeRestore(podVolumeRestore veleroV1.PodVolumeRestore) *PodVolumeRestore {
	return &PodVolumeRestore{
		Name:                      podVolumeRestore.Name,
		Namespace:                 podVolumeRestore.Namespace,
		Annotations:               podVolumeRestore.Annotations,
		Labels:                    podVolumeRestore.Labels,
		PodName:                   podVolumeRestore.Spec.Pod.Name,
		PodNamespace:              podVolumeRestore.Spec.Pod.Namespace,
		VolumeName:                podVolumeRestore.Spec.Volume,
		BackupStorageLocationName: podVolumeRestore.Spec.BackupStorageLocation,
		UploaderType:              podVolumeRestore.Spec.UploaderType,
	}
}

func (p *PodVolumeRestore) Convert2VeleroVolumes() *VeleroVolumes {
	return &VeleroVolumes{
		Namespace:  p.PodNamespace,
		PodName:    p.PodName,
		VolumeName: p.VolumeName,
	}
}
