package velero

import (
	"strconv"
	"strings"
	"time"

	"github.com/samber/lo"
	veleroV1 "github.com/vmware-tanzu/velero/pkg/apis/velero/v1"
	"golang.org/x/exp/maps"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	k8sV1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

type NameCheck struct {
	Name string `json:"name"` // 备份策略名称
}

type SchedulesItem struct {
	SchedulesName string `json:"schedulesName"` // 备份策略名称
	Status        string `json:"status"`        // 备份状态
	ClusterName   string `json:"clusterName"`   // 集群名称
	Type          string `json:"type"`          // 备份类型
	Description   string `json:"description"`   // 描述
	CreateTime    string `json:"createTime"`    // 创建时间
}

type Schedules struct {
	SchedulesName             string               `json:"schedulesName"`             // 备份策略名称
	Status                    string               `json:"status"`                    // 备份状态
	StorageServers            StorageServers       `json:"storageServers"`            // 备份服务器信息
	StorageServersStatus      StorageServersStatus `json:"storageServersStatus"`      //备份服务器状态
	StorageServersInfo        string               `json:"storageServersInfo"`        //备份服务器异常信息
	Description               string               `json:"description"`               // 描述
	CreateTime                string               `json:"createTime"`                // 创建时间
	ClusterName               string               `json:"clusterName"`               // 集群名称
	Namespaces                []string             `json:"namespaces"`                // 备份命名空间
	AllNamespaces             bool                 `json:"allNamespaces"`             // 是否备份集群所有命名空间
	IncludeResources          map[string]string    `json:"includeResources"`          // 指定资源标签
	ExcludeResources          map[string]string    `json:"excludeResources"`          // 排除资源标签
	DefaultVolumesToFsBackup  bool                 `json:"defaultVolumesToFsBackup"`  // 是否备份pv到FSB
	Type                      SchedulesType        `json:"type"`                      // 备份类型 time和handler
	Cron                      Cron                 `json:"cron"`                      // 备份周期
	Ttl                       int                  `json:"ttl"`                       // etcd数据留存时长(天)
	Execute                   bool                 `json:"execute"`                   // 立即执行
	BackupStorageLocationName string               `json:"backupStorageLocationName"` // bsl名称
}

type StorageServersStatus string

const (
	Normal       StorageServersStatus = "Normal"
	SyncError    StorageServersStatus = "SyncError"
	ConnectError StorageServersStatus = "ConnectError"
)

type SchedulesType string

const (
	Time   SchedulesType = "time"
	Handle SchedulesType = "handle"
)

func (s *Schedules) Convert2SchedulesItem() *SchedulesItem {
	return &SchedulesItem{
		SchedulesName: s.SchedulesName,
		Status:        s.Status,
		ClusterName:   s.ClusterName,
		Type:          string(s.Type),
		Description:   s.Description,
		CreateTime:    s.CreateTime,
	}
}

func BuildVeleroBackups(schedules *veleroV1.Schedule, clusterName string) *veleroV1.Backup {
	labels := make(map[string]string)
	maps.Copy(labels, schedules.Labels)
	labels[veleroV1.ScheduleNameLabel] = schedules.Name
	labels["cluster-name"] = clusterName

	backup := &veleroV1.Backup{
		ObjectMeta: k8sV1.ObjectMeta{
			Name:        schedules.TimestampedName(time.Now().UTC()),
			Namespace:   constants.VeleroNamespace,
			Labels:      labels,
			Annotations: schedules.Annotations,
		},
		Spec: schedules.Spec.Template,
	}
	if *schedules.Spec.UseOwnerReferencesInBackup {
		backup.OwnerReferences = []k8sV1.OwnerReference{
			{
				APIVersion: veleroV1.SchemeGroupVersion.String(),
				Kind:       "Schedule",
				Name:       schedules.Name,
				UID:        schedules.UID,
				Controller: lo.ToPtr(true),
			},
		}
	}
	return backup
}

type Cron struct {
	CycleType  string   `json:"cycleType"`  // 备份周期类型[month,week,date,hour,custom]
	CronString string   `json:"cronString"` // cron表达式字符串
	Days       []string `json:"Days"`       // 触发时间(日)
	Weeks      []string `json:"Weeks"`      // 触发时间(周)
	Hours      []string `json:"Hours"`      // 触发时间(时)
	Minutes    []string `json:"Minutes"`    // 触发时间(分)
}

func IsRange(number string, min, max int) bool {
	if num, err := strconv.Atoi(number); err == nil {
		return min <= num && num <= max
	} else {
		return false
	}
}

// 前端数据直接存json
func NewCron(cronString, cycleType string) *Cron {
	if cycleType == "" {
		cycleType = constants.CronTypeCustom
	}

	cron := &Cron{
		CycleType:  cycleType,
		CronString: cronString,
	}

	cronSlice := strings.Split(cron.CronString, constants.Blank)

	if len(cronSlice) == 5 && cron.CycleType != constants.CronTypeCustom {
		solve := func(slice string, min, max int, sli *[]string, cronType string) bool {
			*sli = strings.Split(slice, constants.Comma)
			flag := true
			for _, s := range *sli {
				flag = flag && IsRange(s, min, max)
			}
			return flag
		}

		switch cycleType {
		case constants.CronTypeMonth, constants.CronTypeWeek:
			if cycleType == constants.CronTypeMonth && !solve(cronSlice[2], 1, 31, &cron.Days, constants.CronTypeMonth) {
				cron.CycleType = constants.CronTypeCustom
				break
			} else if cycleType == constants.CronTypeWeek && !solve(cronSlice[4], 0, 6, &cron.Weeks, constants.CronTypeWeek) {
				cron.CycleType = constants.CronTypeCustom
				break
			}
			fallthrough
		case constants.CronTypeDate:
			if !solve(cronSlice[1], 0, 23, &cron.Hours, constants.CronTypeDate) {
				cron.CycleType = constants.CronTypeCustom
				break
			}
			fallthrough
		case constants.CronTypeHour:
			if !solve(cronSlice[0], 0, 59, &cron.Minutes, constants.CronTypeHour) {
				cron.CycleType = constants.CronTypeCustom
				break
			}
		default:
			cron.CycleType = constants.CronTypeCustom
		}
	}

	return cron
}
