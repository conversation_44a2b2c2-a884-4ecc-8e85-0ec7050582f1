package velero

import (
	"fmt"
	"testing"

	"github.com/robfig/cron/v3"
)

func Test_new_cron(t *testing.T) {
	// fmt.Println(newCron("1,2 1,2,3 1,2,3,4,5 * *", "month"))
	// fmt.Println(newCron("1,2 1,2,3 * * 1,2,3,4,5,6", "week"))
	// fmt.Println(newCron("1,2 1,2,3 * * *", "date"))
	// fmt.Println(newCron("1,2 * * * *", "hour"))
}

func Test_solve_cron_string(c *testing.T) {
	cron := Cron{
		CycleType: "month",
		Days:      []string{"1", "2", "3", "4"},
		Hours:     []string{"1", "2", "3"},
		Minutes:   []string{"1", "2"},
	}

	// cron.solveCronString()

	fmt.Println(cron)
}

func Test_cron_check(c *testing.T) {
	cronString := "0 0 31 2 *"
	if _, err := cron.ParseStandard(cronString); err != nil {
		fmt.Println(fmt.Sprintf("err: %+v", err))
	} else {
		fmt.Println("success")
	}
}
