package velero

import (
	"k8s.io/apimachinery/pkg/api/resource"
)

type DownloadFile struct {
	Name      string `json:"name,omitempty"`
	Namespace string `json:"namespace"`
	Data      string `json:"data"`
	Path      string `json:"path"`
}

type Resource struct {
	//Name      string                       `json:"name,omitempty"`
	//Namespace string                       `json:"namespace"`
	Requests map[string]resource.Quantity `json:"requests"`
	Replicas int                          `json:"replicas"`
}

type CompareResult struct {
	Kind   string `json:"kind"`
	Result string `json:"result"`
	Reason string `json:"reason"`
}
