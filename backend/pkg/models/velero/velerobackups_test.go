package velero

import (
	"encoding/json"
	"fmt"
	"testing"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/logger"
)

func Test_namespace_json(t *testing.T) {
	namespace := []string{"caas-system", "kube-system", "default", "velero"}

	namespaceByte, _ := json.Marshal(namespace)

	namespaceJson := string(namespaceByte)

	fmt.Println(namespaceJson)

	ans := make([]string, 0)
	if err := json.Unmarshal([]byte("[]"), &ans); err != nil {
		logger.GetSugared().Errorf("json.Unmarshal failed: %v", err)
	}

	fmt.Println(ans)
}
