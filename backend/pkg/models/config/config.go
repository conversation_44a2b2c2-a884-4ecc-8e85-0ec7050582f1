package config

import (
	"sort"
	"strings"

	"k8s.io/apimachinery/pkg/util/sets"
)

// Key
// 支持配置KEY
type Key string

func (key Key) ToString() string {
	return string(key)
}

// SupportKubernetesVersion
// SupportKubernetesCRIs
// HubIngressAddress
// HubStellariesComponent
var (
	SupportKubernetesVersion Key = "kubernetes_versions"
	SupportKubernetesCRIs    Key = "kubernetes_CRIS"
	HubIngressAddress        Key = "hub_cluster_ingress_address"
	HubStellariesComponent   Key = "hub_cluster_stellaries_component"
	DefaultCalicoCidr        Key = "default_calico_cidr"
	SisyphusSupport          Key = "support"
	DefaultRegistry          Key = "default_registry"
	ChronyInfo               Key = "chrony-info"
)

// ListResponse
// 批量获取配置返回
type ListResponse []Response

// Response
// 配置组响应
type Response struct {
	Key    Key             `json:"key" example:"kubernetes_versions"`
	Values []ValueResponse `json:"values"`
}

// ValueResponse
// 配置返回值响应
type ValueResponse struct {
	Code  string `json:"code" example:"1.21.5"`
	Value string `json:"value" example:"1.21.5"`
}

type ListSugarResponse []SugarResponse

type SugarResponse struct {
	Key   Key         `json:"key" example:"kubernetes_versions"`
	Sugar interface{} `json:"info"`
}

type SupportKubernetesVersionResponse struct {
	Version string `json:"version" example:"1.21.5"`
}

type SupportCRIVersionResponse struct {
	Version string `json:"version" example:"Docker-v19.03.15"`
}

type HubClusterIngressResponse struct {
	Address string `json:"address" example:"http://***********:30088"`
}

type DisasterRecoveryEnabled bool

func (dre DisasterRecoveryEnabled) ParseModel() StellariesComponentModel {
	if dre {
		return ActiveStandBy
	}
	return Single
}

type StellariesComponentModel string

func (scm StellariesComponentModel) ToString() string {
	return string(scm)
}

var (
	// ActiveStandBy
	// 主备切换模式
	ActiveStandBy StellariesComponentModel = "active_standby"

	// Single
	// 非主备切换模式
	Single StellariesComponentModel = "single"
)

type HubStellariesComponentResponse struct {
	Model          StellariesComponentModel `json:"model" example:"single"`
	Address        string                   `json:"address"  example:"***********"`
	Port           int                      `json:"port"  example:"30090"`
	StandbyAddress *string                  `json:"standbyAddress"  example:"***********"`
	StandbyPort    *int                     `json:"standbyPort"  example:"30090"`
}

type DefaultCalicoCidrResponse struct {
	IPv4 string `json:"ipv4,omitempty" example:"**********/16"`
	IPv6 string `json:"ipv6,omitempty" example:"2001:db8:42:0::/112"`
}

type NodeSupportResponse struct {
	Arch   string          `json:"arch"`
	Enable bool            `json:"enable"`
	Alias  string          `json:"alias"`
	OSList []NodeSupportOS `json:"osList"`
}

func (r *NodeSupportResponse) SetAndSort() {
	// 设置enable 的值
	osEnableSet := sets.New[bool]()
	for _, os := range r.OSList {
		enable := os.Enable
		osEnableSet.Insert(enable)
	}
	if osEnableSet.Has(true) {
		r.Enable = true
	}
	// 对架构进行排序
	var osList NodeSupportOSList = r.OSList
	sort.Sort(osList)
}

type NodeSupportOS struct {
	OS     string `json:"os"`
	Enable bool   `json:"enable"`
}
type NodeSupportOSList []NodeSupportOS

func (arr NodeSupportOSList) Len() int {
	return len(arr)
}

func (arr NodeSupportOSList) Less(i, j int) bool {
	osI, osJ := arr[i].OS, arr[j].OS
	return strings.Compare(osI, osJ) < 0
}

func (arr NodeSupportOSList) Swap(i, j int) {
	arr[i], arr[j] = arr[j], arr[i]
}

type Registry struct {
	Protocol string `json:"protocol"`
	Host     string `json:"host"`
	Port     int    `json:"port"`
}

type ChronyInfoStruct struct {
	TimeServer string `json:"timeServer"`
}
