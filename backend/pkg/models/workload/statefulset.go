package workload

import (
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	appsv1 "k8s.io/api/apps/v1"
)

// GetStatefulSetRequest ...
type GetStatefulSetRequest struct {
	common.ReadParam
}

// GetStatefulSetResponse ...
type GetStatefulSetResponse struct {
	appsv1.StatefulSet
}

// ListStatefulSetRequest ...
type ListStatefulSetRequest struct {
	common.ReadParam
}

// ListStatefulSetResponse ...
type ListStatefulSetResponse struct {
	models.PageableResponse[appsv1.StatefulSet]
}

// CreateStatefulSetRequest ...
type CreateStatefulSetRequest struct {
	common.WriteParam
}

// CreateStatefulSetResponse ...
type CreateStatefulSetResponse struct {
	StatefulSet appsv1.StatefulSet
}

// UpdateStatefulSetRequest ...
type UpdateStatefulSetRequest struct {
	common.WriteParam
}

// UpdateStatefulSetResponse ...
type UpdateStatefulSetResponse struct {
	StatefulSet appsv1.StatefulSet
}

// PatchStatefulSetRequest ...
type PatchStatefulSetRequest struct {
	common.WriteParam
}

// PatchStatefulSetResponse ...
type PatchStatefulSetResponse struct {
	StatefulSet appsv1.StatefulSet
}

// DeleteStatefulSetRequest ...
type DeleteStatefulSetRequest struct {
	common.WriteParam
}

// DeleteStatefulSetCollectionRequest ...
type DeleteStatefulSetCollectionRequest struct {
	common.WriteParam
}

// DeleteStatefulSetResponse ...
type DeleteStatefulSetResponse struct {
}

type DeleteAllStatefulSetRequest struct {
	common.WriteParam
}
type DeleteAllStatefulSetResponse struct{}
