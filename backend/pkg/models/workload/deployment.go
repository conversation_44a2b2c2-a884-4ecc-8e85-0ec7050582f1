package workload

import (
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/handler/common"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	appsv1 "k8s.io/api/apps/v1"
)

// GetDeploymentRequest ...
type GetDeploymentRequest struct {
	common.ReadParam
}

// GetDeploymentResponse ...
type GetDeploymentResponse struct {
	appsv1.Deployment
}

// ListDeploymentRequest ...
type ListDeploymentRequest struct {
	common.ReadParam
}

// ListDeploymentResponse ...
type ListDeploymentResponse struct {
	models.PageableResponse[appsv1.Deployment]
}

// CreateDeploymentRequest ...
type CreateDeploymentRequest struct {
	common.WriteParam
}

// CreateDeploymentResponse ...
type CreateDeploymentResponse struct {
	Deployment appsv1.Deployment
}

// UpdateDeploymentRequest ...
type UpdateDeploymentRequest struct {
	common.WriteParam
}

// UpdateDeploymentResponse ...
type UpdateDeploymentResponse struct {
	Deployment appsv1.Deployment
}

// PatchDeploymentRequest ...
type PatchDeploymentRequest struct {
	common.WriteParam
}

// PatchDeploymentResponse ...
type PatchDeploymentResponse struct {
	Deployment appsv1.Deployment
}

// DeleteDeploymentRequest ...
type DeleteDeploymentRequest struct {
	common.WriteParam
}

// DeleteDeploymentCollectionRequest ...
type DeleteDeploymentCollectionRequest struct {
	common.WriteParam
}

// DeleteDeploymentResponse ...
type DeleteDeploymentResponse struct {
}

type DeleteAllDeploymentRequest struct {
	common.WriteParam
}
type DeleteAllDeploymentResponse struct{}
