package node

type NodeVeriryRequest struct {
	Host         string  `json:"host" example:"***********" binding:"required,ipv4ipv6"` // 节点IP
	Post         int     `json:"post" example:"22"  binding:"required,port"`             // 节点端口
	Username     string  `json:"username" example:"root" binding:"required"`             // 账号
	Password     string  `json:"password" example:"Ab123456" binding:"required"`         // 密码
	SudoPassword *string `json:"sudoPassword,omitempty"  example:"Ab123456"`             // 提权密码
}

type NodeVerifyResponse struct {
	Success bool   `json:"success" example:"false"`                          // 是否成功，若全部成功返回true，否则只要有一台失败则返回false
	Message string `json:"message" example:"[***********]:'Connect Refuse'"` // 消息
}
