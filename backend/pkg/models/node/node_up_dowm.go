package node

import (
	"fmt"
	"time"

	"github.com/shopspring/decimal"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	database_aop "harmonycloud.cn/unifiedportal/translate-sdk-golang/database-aop"
	"k8s.io/apimachinery/pkg/util/sets"
)

// NodeUpDownType
// 定义节点上下线的类型
type NodeUpDownType string

var (
	NodeUpDownTypeNodeUp   NodeUpDownType = "nodeUp"   // 表示节点上线
	NodeUpDownTypeNodeDown NodeUpDownType = "nodeDown" // 表示节点下线
	NodeUpDownTypeBatch    NodeUpDownType = "batch"    // 表示节点上下线批量任务，该状态不会对前端进行透出
)

// NodeUpDownStatus
// 定义节点上下线任务状态
type NodeUpDownStatus string

var NodeUpDownStatusFailedList = sets.New[NodeUpDownStatus](NodeUpDownStatusCreateInitialFailed, NodeUpDownStatusPreflightFailed, NodeUpDownStatusInstallFailed)
var (
	NodeUpDownStatusCreateInitial NodeUpDownStatus = "createInitial" // 表示节点初始化中
	NodeUpDownStatusPreflighting  NodeUpDownStatus = "preflighting"  // 表示节点预检中
	NodeUpDownStatusInstalling    NodeUpDownStatus = "installing"    // 表示节点上下线任务执行中

	NodeUpDownStatusCreateInitialFailed NodeUpDownStatus = "createInitialFailed" // 表示节点初始化失败
	NodeUpDownStatusPreflightFailed     NodeUpDownStatus = "preflightFailed"     //  表示节点预检失败
	NodeUpDownStatusInstallFailed       NodeUpDownStatus = "installFailed"       // 表示节点上下线任务执行失败

	NodeUpDownStatusDeleting NodeUpDownStatus = "deleting" // 表示节点上下线任务删除中
	NodeUpDownStatusUnknow   NodeUpDownStatus = "unknow"   // 状态未知 正常不会出现
	NodeUpDownStatusSuccess  NodeUpDownStatus = "success"  // 表示节点上下线任务已完成
)

type NodeUpDownCountResponse struct {
	Total       int64                      `json:"total" example:"10"` // 表示任务总计数量
	Description NodeUpDownCountDescription `json:"description"`        // 任务数量详情
}

type NodeUpDownCountDescription struct {
	NodeUpTotal         int64                      `json:"nodeUpTotal" example:"10"`   // 表示上线类型的任务运行数量
	NodeDownTotal       int64                      `json:"nodeDownTotal" example:"10"` // 表示下线类型的任务运行数量
	NodeUpDescription   map[NodeUpDownStatus]int64 `json:"nodeUp"`                     // key 表示节点上下线状态code,value 表示该状态对应数量
	NodeDownDescription map[NodeUpDownStatus]int64 `json:"nodeDown"`                   // key 表示节点上下线状态code,value 表示该状态对应数量
}

type NodeUpDownCreateRequest struct {
	ClusterName            string                               `json:"clusterName,omitempty" example:"***********"`       // 集群名称 前端无需填写，后台会自动进行赋值
	ClusterBaselineVersion string                               `json:"clusterBaselineVersion" example:"1.1.0-baseline"`   // 集群基线版本 前端无需填写，后台会自动进行赋值
	StartFromFailed        bool                                 `json:"startFromFailed,omitempty" example:"false"`         // 失败从失败处开始
	Reset                  bool                                 `json:"reset,omitempty" example:"false"`                   // 重置
	Type                   NodeUpDownType                       `json:"type" example:"nodeUp" binding:"node_up_down_type"` // 节点上显现任务类型
	Registry               *cluster.CreateRegistryConfigRequest `json:"registry,omitempty"`                                // 节点上线时填写有效，表示自定义的制品服务地址
	ControlNode            NodeRequest                          `json:"controlNode"`                                       // 表示主控节点的信息 无论节点上线｜下线都需要填写
	TimeServer             *string                              `json:"timeServer" binding:"address"`                      // 节点时间同步服务器地址
	NodeConfig             NodeUpDownNodeConfigRequest          `json:"nodeConfig"`                                        // 节点上下线节点配置
	CRI                    cluster.CRIType                      `json:"cri"`
}

func (req NodeUpDownCreateRequest) HasAutoStorageNode() bool {
	if req.NodeConfig.NodeStorageRequest == nil {
		return false
	}
	return req.NodeConfig.NodeStorageRequest.Type == cluster.NodeStorageTypeAuto
}

type NodeUpDownBatchCreateRequest struct {
	ClusterName            string                               `json:"clusterName,omitempty" example:"***********"`     // 集群名称 前端无需填写，后台会自动进行赋值
	ClusterBaselineVersion string                               `json:"clusterBaselineVersion" example:"1.1.0-baseline"` // 集群基线版本 前端无需填写，后台会自动进行赋值
	ClusterCRI             cluster.CRIType                      `json:"clusterCri"`
	Type                   NodeUpDownType                       `json:"type" example:"nodeUp" binding:"node_up_down_type"` // 节点上显现任务类型
	Registry               *cluster.CreateRegistryConfigRequest `json:"registry,omitempty"`                                // 节点上线时填写有效，表示自定义的制品服务地址
	ControlNode            NodeRequest                          `json:"controlNode"`                                       // 表示主控节点的信息 无论节点上线｜下线都需要填写
	TimeServer             *string                              `json:"timeServer" binding:"address"`                      // 节点时间同步服务器地址
	NodeConfigs            []NodeUpDownNodeConfigRequest        `json:"nodeConfigs"`                                       // 节点上下线节点配置
}

func (batchRequest NodeUpDownBatchCreateRequest) ConvertToNodeUpDownCreateRequest() []NodeUpDownCreateRequest {
	var res = make([]NodeUpDownCreateRequest, 0, len(batchRequest.NodeConfigs))
	for _, node := range batchRequest.NodeConfigs {
		node := node
		res = append(res, NodeUpDownCreateRequest{
			ClusterName:            batchRequest.ClusterName,
			ClusterBaselineVersion: batchRequest.ClusterBaselineVersion,
			StartFromFailed:        false,
			Reset:                  false,
			Type:                   batchRequest.Type,
			Registry:               batchRequest.Registry,
			ControlNode:            batchRequest.ControlNode,
			TimeServer:             batchRequest.TimeServer,
			NodeConfig:             node,
			CRI:                    batchRequest.ClusterCRI,
		})
	}
	return res
}

type NodeRequest struct {
	Ip   string                   `json:"ip" example:"*********" binding:"required,ipv4ipv6"` // 节点IP
	Port int                      `json:"port" example:"22" binding:"required"`               // 节点端口
	Auth *cluster.NodeAuthRequest `json:"auth" binding:"required"`                            // 认证信息
}

type NodeUpDownNodeConfigRequest struct {
	Ip   string                   `json:"ip" example:"*********" binding:"required,ipv4ipv6"` // 节点IP
	Port int                      `json:"port" example:"22" binding:"required,port"`          // 节点端口
	Auth *cluster.NodeAuthRequest `json:"auth" binding:"required"`                            // 认证信息
	// GPU 仅当节点上线时有
	SupportGpu *bool `json:"supportGpu,omitempty" example:"false"` // 是否支持GPU
	// 磁盘信息仅节点上线有
	*cluster.NodeStorageRequest `json:",inline,omitempty"` // 节点存储配置
}

type NodeUpDownNodeConfigResponse struct {
	Ip   string                    `json:"ip" example:"*********" binding:"required,ipv4ipv6"` // 节点IP
	Port int                       `json:"port" example:"22" binding:"required,port"`          // 节点端口
	Auth *cluster.NodeAuthResponse `json:"auth" binding:"required"`                            // 认证信息
	// GPU 仅当节点上线时有
	SupportGpu *bool `json:"supportGpu,omitempty" example:"false"` // 是否支持GPU
	// 磁盘信息仅节点上线有
	*cluster.NodeStorageResponse `json:",inline"` // 节点存储配置
}

func (response *NodeUpDownNodeConfigResponse) RenderNodeDiskConfigs(input map[string]*cluster.NodeStorageResponse) {
	response.NodeStorageResponse = new(cluster.NodeStorageResponse)
	storage, exist := input[response.Ip]
	if !exist {
		response.Type = cluster.NodeStorageTypeManual
	} else {
		*response.NodeStorageResponse = *storage
	}
}

func (response NodeUpDownNodeConfigResponse) ConvertToRequest() NodeUpDownNodeConfigRequest {
	return NodeUpDownNodeConfigRequest{
		Ip:                 response.Ip,
		Port:               response.Port,
		Auth:               response.Auth.Convert2Request(),
		SupportGpu:         response.SupportGpu,
		NodeStorageRequest: response.NodeStorageResponse.ConvertToRequest(),
	}
}

type NodeUpDownResponseList []NodeUpDownResponse

// Len is the number of elements in the collection.
func (arr NodeUpDownResponseList) Len() int {
	return len(arr)
}

// Less reports whether the element with index i
// must sort before the element with index j.
//
// If both Less(i, j) and Less(j, i) are false,
// then the elements at index i and j are considered equal.
// Sort may place equal elements in any order in the final result,
// while Stable preserves the original input order of equal elements.
//
// Less must describe a transitive ordering:
//   - if both Less(i, j) and Less(j, k) are true, then Less(i, k) must be true as well.
//   - if both Less(i, j) and Less(j, k) are false, then Less(i, k) must be false as well.
//
// Note that floating-point comparison (the < operator on float32 or float64 values)
// is not a transitive ordering when not-a-number (NaN) values are involved.
// See Float64Slice.Less for a correct implementation for floating-point values.
func (arr NodeUpDownResponseList) Less(i, j int) bool {
	// 先根据Type 进行排序
	iType := arr[i].Type
	jType := arr[j].Type
	parseTypeAsNumber := func(downType NodeUpDownType) int {
		switch downType {
		case NodeUpDownTypeNodeUp:
			return 1
		case NodeUpDownTypeNodeDown:
			return 2
		default:
			return 3
		}
	}
	iTypeSortNumber := parseTypeAsNumber(iType)
	jTypeSortNumber := parseTypeAsNumber(jType)
	if iTypeSortNumber != jTypeSortNumber {
		return iTypeSortNumber < jTypeSortNumber
	}
	// 再根据时间进行排序
	return arr[i].CreateTime.Before(arr[j].CreateTime)
}

// Swap swaps the elements with indexes i and j.
func (arr NodeUpDownResponseList) Swap(i, j int) {
	arr[i], arr[j] = arr[j], arr[i]
}

type NodeStatusAlias string

var (
	// 上线枚举
	NodeStatusAliasNodeUPInitialing      NodeStatusAlias = "节点初始化中"
	NodeStatusAliasNodeUPPreflighting    NodeStatusAlias = "节点预检中"
	NodeStatusAliasNodeUPInstalling      NodeStatusAlias = "上线中"
	NodeStatusAliasNodeUPInitialFailed   NodeStatusAlias = "节点初始化失败"
	NodeStatusAliasNodeUPPreflightFailed NodeStatusAlias = "节点预检失败"
	NodeStatusAliasNodeUPInstallFailed   NodeStatusAlias = "上线失败"
	NodeStatusAliasNodeUPJobDeleteing    NodeStatusAlias = "上线任务删除中"
	NodeStatusAliasNodeUPJobSuccess      NodeStatusAlias = "上线成功"
	NodeStatusAliasNodeUPJobUnknow       NodeStatusAlias = "上线状态未知"

	// 下线枚举
	NodeStatusAliasNodeDownInitialing      NodeStatusAlias = "节点初始化中"
	NodeStatusAliasNodeDownPreflighting    NodeStatusAlias = "节点预检中"
	NodeStatusAliasNodeDownInstalling      NodeStatusAlias = "下线中"
	NodeStatusAliasNodeDownInitialFailed   NodeStatusAlias = "节点初始化失败"
	NodeStatusAliasNodeDownPreflightFailed NodeStatusAlias = "节点预检失败"
	NodeStatusAliasNodeDownInstallFailed   NodeStatusAlias = "下线失败"
	NodeStatusAliasNodeDownJobDeleteing    NodeStatusAlias = "下线任务删除中"
	NodeStatusAliasNodeDownJobSuccess      NodeStatusAlias = "下线成功"
	NodeStatusAliasNodeDownJobUnknow       NodeStatusAlias = "下线状态未知"

	NodeStatusAliasEmpty NodeStatusAlias = ""
)

func mustParseNodeStatusAlias(nodeUpDownType NodeUpDownType, status NodeUpDownStatus) NodeStatusAlias {
	switch nodeUpDownType {
	case NodeUpDownTypeNodeUp:
		switch status {
		case NodeUpDownStatusCreateInitial:
			return NodeStatusAliasNodeUPInitialing
		case NodeUpDownStatusPreflighting:
			return NodeStatusAliasNodeUPPreflighting
		case NodeUpDownStatusInstalling:
			return NodeStatusAliasNodeUPInstalling
		case NodeUpDownStatusCreateInitialFailed:
			return NodeStatusAliasNodeUPInitialFailed
		case NodeUpDownStatusPreflightFailed:
			return NodeStatusAliasNodeUPPreflightFailed
		case NodeUpDownStatusInstallFailed:
			return NodeStatusAliasNodeUPInstallFailed
		case NodeUpDownStatusDeleting:
			return NodeStatusAliasNodeUPJobDeleteing
		case NodeUpDownStatusUnknow:
			return NodeStatusAliasNodeUPJobUnknow
		case NodeUpDownStatusSuccess:
			return NodeStatusAliasNodeUPJobSuccess
		default:
			return NodeStatusAliasEmpty
		}

	case NodeUpDownTypeNodeDown:
		switch status {
		case NodeUpDownStatusCreateInitial:
			return NodeStatusAliasNodeDownInitialing
		case NodeUpDownStatusPreflighting:
			return NodeStatusAliasNodeDownPreflighting
		case NodeUpDownStatusInstalling:
			return NodeStatusAliasNodeDownInstalling

		case NodeUpDownStatusCreateInitialFailed:
			return NodeStatusAliasNodeDownInitialFailed
		case NodeUpDownStatusPreflightFailed:
			return NodeStatusAliasNodeDownPreflightFailed
		case NodeUpDownStatusInstallFailed:
			return NodeStatusAliasNodeDownInstallFailed

		case NodeUpDownStatusDeleting:
			return NodeStatusAliasNodeDownJobDeleteing
		case NodeUpDownStatusUnknow:
			return NodeStatusAliasNodeDownJobUnknow
		case NodeUpDownStatusSuccess:
			return NodeStatusAliasNodeDownJobSuccess
		default:
			return NodeStatusAliasEmpty
		}
	default:
		return NodeStatusAliasEmpty
	}
}

func init() {
	database_aop.GroupInfo(NodeUpDownResponse{}, database_aop.TranslateGroupInfo{
		Name:          "NODE::UP::DOWN::STATUS",
		UniqueKeyName: "Type/Status",
	})
}

type NodeUpDownResponse struct {
	ClusterName        string           `json:"clusterName" example:"test-cluster"`                         // 集群名称
	Ip                 string           `json:"ip" example:"*********" `                                    // 节点IP
	Type               NodeUpDownType   `json:"type" example:"nodeUp"`                                      // 节点上显现任务类型
	Status             NodeUpDownStatus `json:"status" example:"createInitial"`                             // 节点上下线任务状态
	StatusAlias        NodeStatusAlias  `json:"statusAlias" example:"上线成功" translate:"keyName=statusAlias"` //  节点上下线任务状态aliasName
	Total              int64            `json:"total" example:"3"`                                          // 表示总计步骤数
	Step               int64            `json:"step" example:"1"`                                           // 表示当前执行到第几部了
	CreateTime         time.Time        `json:"createTime" example:"2024-06-12 15:15:15"`                   // 表示创建时间
	HasAutoStorageNode bool             `json:"hasAutoStorageNode" example:"true"`                          // 是否存在自动挂载磁盘的节点
	Percent            float64          `json:"percent" example:"0.33"`                                     // 最后计算 表示Step/Total
	Process            string           `json:"process" example:"1/3"`
	CRI                cluster.CRIType  `json:"cri"`
}

func (resp *NodeUpDownResponse) Calculate() {
	// 计算百分比
	if resp == nil {
		// nothing to_do
		return
	}
	stepDecimal := decimal.NewFromInt(resp.Step)
	totalDecimal := decimal.NewFromInt(resp.Total)
	p, _ := stepDecimal.Div(totalDecimal).Round(3).Float64()
	resp.Percent = p * 100
	// 计算 process
	resp.Process = fmt.Sprintf("%d/%d", resp.Step, resp.Total)
	// 设置状态别名
	resp.StatusAlias = mustParseNodeStatusAlias(resp.Type, resp.Status)
}

type NodeUpDownStatusResponse struct {
	ClusterName        string                            `json:"clusterName" example:"test-cluster"`               // 集群名称
	Ip                 string                            `json:"ip" example:"*********" `                          // 节点IP
	Type               NodeUpDownType                    `json:"type" example:"nodeUp"`                            // 节点上显现任务类型
	Status             NodeUpDownStatus                  `json:"status" example:"createInitial"`                   // 节点上下线任务状态
	Processing         cluster.CreateProcessListResponse `json:"processing" translateObject:""`                    // 表示步骤
	HasAutoStorageNode bool                              `json:"hasAutoStorageNode" example:"true"`                // 是否存在自动挂载磁盘的节点
	NodeName           *string                           `json:"name,omitempty" example:"skyview-cluster-node001"` // 节点上线后的节点名称
	CRI                cluster.CRIType                   `json:"cri"`
}

type NodeUpDownCreateResponse struct {
	Type         NodeUpDownType                        `json:"type" example:"nodeUp"`                 // 节点上显现任务类型
	RegistryType []cluster.RegistryType                `json:"registryType" example:"default,custom"` // 表示镜像仓库的类型 可选值 default、custom
	Registry     *cluster.CreateRegistryConfigResponse `json:"registry,omitempty"`                    // 节点上线时填写有效，表示自定义的制品服务地址
	ControlNode  NodeResponse                          `json:"controlNode"`                           // 表示主控节点的信息
	NodeConfig   NodeUpDownNodeConfigResponse          `json:"nodeConfig"`                            // 节点上下线节点配置
	TimeServer   *string                               `json:"timeServer"`                            // 节点时间同步服务器地址
	nodeInfos    map[string]NodeResponse               // 用于暂存节点的元信息
	CRI          cluster.CRIType                       `json:"cri"`
}

func (response NodeUpDownCreateResponse) Convert2CreateRequest(clusterName string) NodeUpDownCreateRequest {
	return NodeUpDownCreateRequest{
		ClusterName: clusterName,
		Type:        response.Type,
		Registry:    response.Registry.ConvertToRequest(),
		ControlNode: response.ControlNode.ConvertToRequest(),
		TimeServer:  response.TimeServer,
		NodeConfig:  response.NodeConfig.ConvertToRequest(),
		CRI:         response.CRI,
	}
}

func (response *NodeUpDownCreateResponse) CacheNodeInfo(node NodeResponse) {
	if response.nodeInfos == nil {
		response.nodeInfos = make(map[string]NodeResponse)
	}
	response.nodeInfos[node.Ip] = node
}

func (response *NodeUpDownCreateResponse) CacheNodeToWorker(ip string) {
	host, exist := response.nodeInfos[ip]
	if !exist {
		return
	}
	response.NodeConfig.Ip = host.Ip
	response.NodeConfig.Port = host.Port
	response.NodeConfig.Auth = host.Auth
}

func (response *NodeUpDownCreateResponse) CacheNodeToControl(ip string) {
	host, exist := response.nodeInfos[ip]
	if !exist {
		return
	}
	response.ControlNode.Ip = host.Ip
	response.ControlNode.Port = host.Port
	response.ControlNode.Auth = host.Auth
}

type NodeResponse struct {
	Ip   string                    `json:"ip" example:"*********" binding:"required,ipv4ipv6"` // 节点IP
	Port int                       `json:"port" example:"22" binding:"required"`               // 节点端口
	Auth *cluster.NodeAuthResponse `json:"auth" binding:"required"`                            // 认证信息
}

func (response NodeResponse) ConvertToRequest() NodeRequest {
	return NodeRequest{
		Ip:   response.Ip,
		Port: response.Port,
		Auth: response.Auth.Convert2Request(),
	}
}

type UpDownLogQueryRequest struct {
	ClusterName string `json:"clusterName" uri:"clusterName" binding:"required"`
	IP          string `json:"node_ip" uri:"node_ip" binding:"required"`
}
