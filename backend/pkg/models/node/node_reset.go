package node

import "harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"

func NewNodeResetRequest(nodes []NodeRequest, startFromFailed, reset bool, clusterBaselineVersion string, cri cluster.CRIType) NodeResetRequest {
	return NodeResetRequest{
		Nodes:                  nodes,
		StartFromFailed:        startFromFailed,
		Reset:                  reset,
		ClusterBaselineVersion: clusterBaselineVersion,
		CRI:                    cri,
	}
}

// 节点重置表单
type NodeResetRequest struct {
	Nodes                  []NodeRequest
	StartFromFailed        bool
	Reset                  bool
	ClusterBaselineVersion string
	CRI                    cluster.CRIType
}
