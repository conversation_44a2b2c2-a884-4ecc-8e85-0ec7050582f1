package node

import (
	"strings"

	v1 "k8s.io/api/core/v1"
)

type SimpleNodeResponseList []SimpleNodeResponse

func (arr SimpleNodeResponseList) Len() int {
	return len(arr)
}
func (arr SimpleNodeResponseList) Less(i, j int) bool {
	// status Running > Pending > Terminated
	nodeStatusNumberFunc := func(status v1.NodePhase) int {
		switch status {
		case v1.NodeTerminated:
			return 0
		case v1.NodePending:
			return 1
		case v1.NodeRunning:
			return 2
		}
		return 3
	}
	iStatusNumber := nodeStatusNumberFunc(arr[i].Status)
	jStatusNumber := nodeStatusNumberFunc(arr[j].Status)
	if iStatusNumber != jStatusNumber {
		return iStatusNumber < jStatusNumber
	}
	return strings.Compare(arr[i].HostIP, arr[j].HostIP) < 0

}
func (arr SimpleNodeResponseList) Swap(i, j int) {
	arr[i], arr[j] = arr[j], arr[i]
}

type SimpleNodeResponse struct {
	HostName string       `json:"name" example:"cluster-57-node1"` // 节点名称
	HostIP   string       `json:"ip" example:"***********"`        // 节点IP
	Status   v1.NodePhase `json:"status" example:"Pending"`        // 节点状态
}
