package baseline

import (
	"fmt"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/resources"
)

// GetBaselineNameExistedRequest ...
type GetBaselineNameExistedRequest struct {
	// Name 基线名称
	Name string `json:"name" form:"name"`
}

// GetBaselineNameExistedResponse ...
type GetBaselineNameExistedResponse struct {
	// Existed 是否存在
	Existed bool `json:"existed"`
}

// GetCategoryNameExistedRequest ...
type GetCategoryNameExistedRequest struct {
	// Name 分类名称
	Name string `json:"name" form:"name"`
}

// GetCategoryNameExistedResponse ...
type GetCategoryNameExistedResponse struct {
	// Existed 是否存在
	Existed bool `json:"existed"`
}

// QueryCategoryStandardsRequest ...
type QueryCategoryStandardsRequest struct {
	// Builtin 是否内置
	Builtin *bool `json:"builtin" form:"builtin"`
	// StandardId 标准id
	StandardId *int64 `json:"standardId" form:"standardId"`
	// IsCopy 是否是复制页面
	IsCopy bool `json:"isCopy" form:"isCopy"`
	// Filter 过滤器
	Filter resources.Filter[*CategoryStandardsItem] `json:"-"`
}

// CategoryStandardsItem ...
type CategoryStandardsItem struct {
	// CategoryItem 标准类型
	CategoryItem `json:",inline"`
	// Standards 标准列表
	Standards []StandardItem `json:"standards"`
}

// QueryCategoryStandardsResponse 查询分类好的基线标准列表
type QueryCategoryStandardsResponse struct {
	//Items []CategoryStandardsItem `json:"items"`
	models.PageableResponse[*CategoryStandardsItem]
}

type ClusterStandardItem struct {
	// ID 基线标准
	ID int64 `json:"id"`
	// Name 基线标准名称
	Name string `json:"name"`
	// ClusterName 集群
	ClusterName string `json:"clusterName"`
	// CategoryID 分类ID
	CategoryID int64 `json:"categoryId"`
	// CategoryName 所属分类名称
	Builtin bool `json:"builtin"`
	// CheckItems 当前基线标准包含的基线id
	CheckItems []StandardCheckItem `json:"checkItems,omitempty"`
	// Description 基线标准描述
	Description string `json:"description"`
	// CreateTime 创建时间
	CreateTime string `json:"createTime"`
	// UpdateTime 更新时间
	UpdateTime string `json:"updateTime"`
}

// StandardItem 基线标准查询结果的结构体
type StandardItem struct {
	// ID 基线标准
	ID int64 `json:"id"`
	// Name 基线标准名称
	Name string `json:"name"`
	// CategoryID 分类ID
	CategoryID int64 `json:"categoryId"`
	// CategoryName 所属分类名称
	CategoryName string `json:"categoryName"`
	//  CategoryBuiltin 是否为内置分类 false 自定义 true 系统
	CategoryBuiltin bool `json:"categoryBuiltin"`
	// Builtin 是否内置
	Builtin bool `json:"builtin"`
	// CheckIds 当前基线标准包含的基线id
	// 只有新增/编辑基线标准的时候的聚和接口这个值才会返回
	CheckIds []int64 `json:"checkIds,omitempty"`
	// StandardCheckIds 当前基线标准包含的基线id
	// 只有在基线标准相关的地方会返回
	StandardCheckIds []int64 `json:"standardCheckIds,omitempty"`
	// CheckItem 当前基线标准所有的基线检查项列表
	// 只有新增/编辑基线标准的时候的聚和接口这个值才会返回
	CheckItem models.PageableResponse[StandardCheckIDItem] `json:"checkItem,omitempty"`
	// SelectedCheckInfo 选择的checkInfo
	SelectedCheckInfo *SelectedCheckInfo `json:"selectedCheckInfo,omitempty"`
	// Description 基线标准描述
	Description string `json:"description"`
	// CreateTime 创建时间
	CreateTime string `json:"createTime"`
	// UpdateTime 更新时间
	UpdateTime string `json:"updateTime"`
}

// StandardCheckIDItem 该Item只返回id
type StandardCheckIDItem struct {
	// ID  检查项ID
	ID int64 `json:"id"`
	// 基线与关联表的ID 保证唯一
	StandardCheckID int64 `json:"standardCheckId"`
}

// QueryBaselinesRequest 查询基线标准请求结构体
type QueryBaselinesRequest struct {
	// Name 名称
	Name string `json:"name" form:"name"`
	// Builtin 是否内置
	Builtin *bool `json:"builtin" form:"builtin"`
	// CategoryID 分类
	CategoryID *int64 `json:"categoryId" form:"categoryId"`
	// CategoryName 分类
	CategoryName string `json:"categoryName" form:"categoryName"`
	// CategoryBuiltin 分类是否内置 false 自定义 true 系统
	CategoryBuiltin *bool `json:"categoryBuiltin" form:"categoryBuiltin"`

	// Filter 过滤器
	Filter resources.Filter[*StandardItem] `json:"-"`
}

// QueryBaselinesResponse 查询基线标准响应结构体
type QueryBaselinesResponse struct {
	// 响应
	models.PageableResponse[*StandardItem] `json:",inline"`
}

// GetBaselineDetail 基线标准详细信息结构体
// 包含的检查项
type GetBaselineDetail struct {
	// ID 基线标准ID
	ID int64 `json:"id"`
	// Name 名称
	Name string `json:"name"`
	// CategoryName 基线分类
	CategoryName string `json:"categoryName"`
	// CategoryBuiltin 分类是否内置 false 自定义 true 系统
	CategoryBuiltin bool `json:"categoryBuiltin"`
}

// StandardCheckItem 标准关联的检查项
type StandardCheckItem struct {
	// StandardCheckID 关联表的ID
	StandardCheckID int64 `json:"standardCheckId"`
	// StandardID 当前检查项所属于的标准ID
	StandardID int64 `json:"standardId"`
	// CheckItem 基础信息
	CheckItem `json:",inline"`
	// CheckValue 检查值
	CheckValue CheckValueItem `json:"checkValue"`
	// Suggestion 检查项建议
	Suggestion string `json:"suggestion"`
}

type ValuePrefix string

const (
	// ValuePrefixRegExp 正则
	ValuePrefixRegExp ValuePrefix = "RegExp"
	// ValuePrefixInput 输入框
	ValuePrefixInput ValuePrefix = "Input"
	// ValuePrefixFile 文件上传
	ValuePrefixFile ValuePrefix = "File"
)

type FileItem struct {
	// Id 文件id
	Id int64 `json:"id"`
	// Path 桶名
	Path string `json:"path"`
	// UniqueKey md5加密后的名字
	UniqueKey string `json:"uniqueKey"`
	// Name 文件名称
	Name string `json:"name"`
	// Link 链接地址, 当前浏览器地址拼上该链接访问
	// 比如 10.120.1.58 环境, 则拼上 `10.120.1.58${link}` 10.120.1.58/minio/applicaiton/aaa.txt
	Link string `json:"link"`
}

// CheckValueItem 检查值
type CheckValueItem struct {
	// Prefix 前缀
	Prefix ValuePrefix `json:"prefix,omitempty"`
	// File 上传的文件信息
	// 当 Prefix 为 File 时使用该File
	File *FileItem `json:"file"`
	// Value 检查值
	// 当 Prefix 为 ExactMatch FuzzyMatch 时使用value
	Value string `json:"value"`
}

func (c CheckValueItem) String() string {
	return fmt.Sprintf("%v", c.Value)
}

// GetBaselineDetailsRequest 查询基线标准详情的请求结构体
type GetBaselineDetailsRequest struct {
	// ID 基线标准ID
	ID int64 `json:"id" form:"id"`
	// ParentID 父基线标准
	ParentID *int64 `json:"parentId" form:"parentId"`
	// RiskLevel 风险级别
	RiskLevel string `json:"riskLevel" form:"riskLevel"`
	// Name 名称
	Name string `json:"name" form:"name"`
	// CheckFilter 检查项过滤器
	CheckFilter resources.Filter[*StandardCheckItem] `json:"-"`
}

//// SelectBuiltinStandardCheck 选择的内置基线标准检查项
//type SelectBuiltinStandardCheck struct {
//	// StandardID 基线标准ID
//	StandardID int64 `json:"standardId"`
//	// CheckIDs 检查项ID
//	CheckIDs []int64 `json:"checkIds"`
//	// IsAll 是否全选
//	IsAll bool `json:"isAll"`
//}

// GetBaselineDetailsResponse 查询基线标准详情的响应结构体
type GetBaselineDetailsResponse struct {
	// ID 基线标准ID
	ID int64 `json:"id"`
	// Name 名称
	Name string `json:"name"`
	// Description 基线标准说明
	Description string `json:"description"`
	// CategoryID 分类ID
	CategoryID int64 `json:"categoryId"`
	// CategoryName 分类
	CategoryName string `json:"categoryName"`
	// CategoryBuiltin 分类是否内置 false 自定义 true 系统
	CategoryBuiltin bool `json:"categoryBuiltin"`
	// Builtin 是否内置
	Builtin bool `json:"builtin"`
	// CheckIds 基线下所有的checkIds
	CheckIds []int64 `json:"checkIds,omitempty"`
	// StandardCheckIds 关联的基线检查项ID
	StandardCheckIds []int64 `json:"standardCheckIds"`
	// SelectedCheckInfo 当前父基线在子基线下的选择信息
	// 包含了 选择的checkIds 和 对应的带有风险等级和检查值值信息的列表
	SelectedCheckInfo *SelectedCheckInfo `json:"selectedCheckInfo,omitempty"`
	//Items []StandardCheckItem `json:"items"`
	models.PageableResponse[*StandardCheckItem] `json:",inline"`
	// CreateTime 创建时间
	CreateTime string `json:"createTime"`
	// UpdateTime 更新时间
	UpdateTime string `json:"updateTime"`
}

// SelectedCheckInfo 选中的检查项相关信息
type SelectedCheckInfo struct {
	// CheckIds 当前父基线标准选中的检查项
	CheckIds []int64 `json:"checkIds"`
	// StandardCheckIds 当前父基线标准选中的关联检查项
	StandardCheckIds []int64 `json:"standardCheckIds"`
	// Checkers 当前的父基线标准选中的检查项列表
	Checkers []SimpleBaselineChecker `json:"checkers,omitempty"`
}

type SimpleBaselineChecker struct {
	// CheckID 检查项ID
	CheckID int64 `json:"checkId"`
	// ID 检查项和基线标准的关联表ID
	StandardCheckID int64 `json:"standardCheckId"`
	// StandardID 如果有则是关联的基线标准ID
	StandardID int64 `json:"standardId"`
	// RiskLevel 风险级别，例如 高危High、中危Medium、低危Low
	RiskLevel string `json:"riskLevel"`
	// CheckValue 检查值
	CheckValue *CheckValueItem `json:"checkValue"`
}

type GetBaselineCheckersRequest struct {
	// ID 基线ID
	ID int64 `json:"id" form:"id"`
	// CheckIds 查询的基线检查项ID
	CheckIds []int64 `json:"checkIds" form:"checkIds"`
}

// GetBaselineCheckersResponse ...
type GetBaselineCheckersResponse struct {
	// 获取对应的checkItem
	models.PageableResponse[*StandardCheckItem]
}

// CreateBaselineRequest 创建基线标准请求结构体
type CreateBaselineRequest struct {
	// Name 基线标准名称
	Name string `json:"name" form:"name" binding:"required,max=128"`
	// CategoryId 基线标准所属分类
	CategoryID *int64 `json:"categoryId" form:"categoryId" binding:"required"`
	// Description 基线标准描述
	Description string `json:"description" form:"description" binding:"omitempty,max=200"`
	// Checkers 关联创建的Checker 检查器（可选）
	// 不带有关联表的ID
	Checkers []SimpleBaselineChecker `json:"checkers,omitempty"`
	// StandardCheckers 基线相关的基线检查项
	// 需要带有关联表的ID
	StandardCheckers []SimpleBaselineChecker `json:"standardCheckers,omitempty"`
}

// CreateBaselineResponse 创建基线标准响应结构体
type CreateBaselineResponse struct {
}

// UpdateBaselineRequest 编辑基线标准请求结构体
type UpdateBaselineRequest struct {
	// ID 基线标准ID
	ID int64 `json:"id"`
	// Name 基线标准名称
	Name string `json:"name" form:"name" binding:"required,max=128"`
	// CategoryId 基线标准所属分类
	CategoryID *int64 `json:"categoryId" form:"categoryId" binding:"required"`
	// Description 基线标准描述
	Description string `json:"description" form:"description" binding:"omitempty,max=200"`
	// Checkers 关联创建的Checker 检查器（可选）
	Checkers []SimpleBaselineChecker `json:"checkers,omitempty"`
	// StandardCheckers 基线相关的基线检查项
	// 需要带有关联表的ID
	StandardCheckers []SimpleBaselineChecker `json:"standardCheckers,omitempty"`
}

// UpdateBaselineResponse 编辑基线标准响应结构体
type UpdateBaselineResponse struct {
}

// DeleteBaselineRequest 删除基线标准请求结构体
type DeleteBaselineRequest struct {
	ID int64 `json:"id"` // 基线标准ID
}

// DeleteBaselineResponse 删除基线标准响应结构体
type DeleteBaselineResponse struct {
}

const (
	// CategoryDefaultID 默认分类ID
	CategoryDefaultID   = 1
	CategoryDefaultName = "Default"
)

// QueryBaselineCategoriesRequest 查询基线标准分类请求结构体
type QueryBaselineCategoriesRequest struct {
	// Name 基线标准分类名称
	Name string `json:"name" form:"name"`

	// Builtin 是否内置
	Builtin *bool `json:"builtin" form:"builtin"`
	// Filter 过滤器
	Filter resources.Filter[*CategoryItem] `json:"-"`
}

// QueryBaselineCategoriesResponse 查询基线标准分类响应结构体
type QueryBaselineCategoriesResponse struct {
	// Items 基线标准分类列表
	//Items []CategoryItem `json:"items"` // 基线标准分类列表
	models.PageableResponse[*CategoryItem] `json:",inline"`
}

// CategoryItem 基线标准分类查询结构体
type CategoryItem struct {
	// ID 分类ID
	ID int64 `json:"id"` // 分类ID

	// Name 分类名称
	Name string `json:"name"` // 分类名称

	// Description 分类描述
	Description string `json:"description"` // 分类描述

	// Builtin 是否内置
	Builtin bool `json:"builtin"` // 是否内置

	// StandardCount 标准数量
	StandardCount int `json:"standardCount"`
}

// CreateBaselineCategoryRequest 创建基线标准分类请求结构体
//
//	@Description	创建新的基线分类时，客户端必须提供分类名称及可选的描述
type CreateBaselineCategoryRequest struct {
	// Name 分类名称，必填且不能超过20字符
	Name string `json:"name" binding:"required,max=20"` // 分类名称

	// Description 分类描述，选填且不能超过100字符
	Description string `json:"description,omitempty" binding:"max=200"` // 分类描述
}

// CreateBaselineCategoryResponse 创建基线标准分类响应结构体
type CreateBaselineCategoryResponse struct {
	// ID 基线分类ID
	ID int64 `json:"id"`
}

// EditBaselineCategoryRequest 编辑基线标准分类请求结构体
type EditBaselineCategoryRequest struct {
	// ID 分类ID
	ID int64 `json:"id"` // 分类ID

	// Name 分类名称，必填且不能超过20字符
	Name string `json:"name" binding:"required,max=20"` // 分类名称

	// Description 分类描述，选填且不能超过100字符
	Description string `json:"description,omitempty" binding:"max=200"` // 分类描述
}

// EditBaselineCategoryResponse 编辑基线标准分类响应结构体
type EditBaselineCategoryResponse struct {
	// ID 基线分类ID
	ID int64 `json:"id"`
}

// DeleteBaselineCategoryRequest 删除基线标准分类请求结构体
type DeleteBaselineCategoryRequest struct {
	// ID 分类ID
	ID int64 `json:"id"` // 分类ID
}

// DeleteBaselineCategoryResponse 删除基线标准分类响应结构体
type DeleteBaselineCategoryResponse struct {
}

type QueryAssociatedStrategiesRequest struct {
	ID int64 `json:"id"`
}

// SimpleStrategyItem ...
type SimpleStrategyItem struct {
	// Id 策略ID
	Id int64 `json:"id"`
	// Name 策略名称
	Name string `json:"name"`
	// ClusterCount 集群个数
	ClusterCount int `json:"clusterCount"`
	// ClusterNames 集群名称列表
	ClusterNames []string `json:"clusterNames"`
}

// QueryAssociatedStrategiesResponse 查询关联的基线策略表
type QueryAssociatedStrategiesResponse struct {
	// Strategies 关联的基线策略列表
	Strategies []SimpleStrategyItem `json:"strategies"`
}

// UpdateBindingStrategiesRequest 更新绑定关系
type UpdateBindingStrategiesRequest struct {
	// ID ...
	ID int64 `json:"id" form:"id"`
	// UnbindStrategyIds ...
	UnbindStrategyIds []int64 `json:"unbindStrategyIds"`
}

// UpdateBindingStrategiesResponse ...
type UpdateBindingStrategiesResponse struct {
}
