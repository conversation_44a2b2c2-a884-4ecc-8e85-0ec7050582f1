package baseline

import (
	"time"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/feign/baseline_master"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/dao/caas"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils/redislock"
)

// 检查项失败原因枚举
const (
	// JobReasonClusterError 集群错误
	JobReasonClusterError = "ClusterError"
	// JobReasonAddonAbnormalError 组件运行异常
	JobReasonAddonAbnormalError = "AddonAbnormalError"
	// JobReasonCheckStandardError 检查标准失败
	JobReasonCheckStandardError = "CheckStandardError"
	// JobReasonRunStandardJobsError 运行标准失败
	JobReasonRunStandardJobsError = "RunStandardJobsError"
	// JobReasonInitJobError 初始化作业失败
	JobReasonInitJobError = "InitJobError"
	// JobReasonSyncCheckerError 同步检查项失败
	JobReasonSyncCheckerError = "SyncCheckerError"
	// JobReasonSyncMonitorsError 同步监控项失败
	JobReasonSyncMonitorsError = "SyncMonitorsError"
	// JobReasonWaitMonitorsError 等待监控项失败
	JobReasonWaitMonitorsError = "WaitMonitorsError"
	// JobReasonCheckTimeout 检查超时
	JobReasonCheckTimeout = "CheckTimeout"
	// JobReasonBaselineCreateError 基线创建失败
	JobReasonBaselineCreateError = "BaselineCreateError"
	// JobCheckItemsNotPass 检查项未通过
	JobCheckItemsNotPass = "CheckItemsNotPass"
	// JobCheckItemsMissing 检查项不存在
	JobCheckItemsMissing = "CheckItemMissing"
	// JobReasonCheckerCreateError 检查项创建失败
	JobReasonCheckerCreateError = "CheckerCreateError"
	// JobReasonMonitorCreateError 监控项创建失败
	JobReasonMonitorCreateError = "MonitorCreateError"
	// JobReasonReportValueNotMatch 报告值不匹配
	JobReasonReportValueNotMatch = "ReportValueNotMatch"
	// JobReasonReportPointsEmpty 报告值为空
	JobReasonReportPointsEmpty = "ReportPointsEmpty"
	// JobReasonGetReportError 获取报告失败
	JobReasonGetReportError = "GetReportError"
	// JobReasonVerifyReportError 验证报告失败
	JobReasonVerifyReportError = "VerifyReportError"
	// JobReasonPanicError 程序崩溃
	JobReasonPanicError = "PanicError"
	// JobReasonSystemAbort 系统中止
	JobReasonSystemAbort = "SystemAbort"
	// JobReasonUnknown 未知
	JobReasonUnknown = "Unknown"
)

// StrategyJobTask 策略任务
type StrategyJobTask struct {
	// Record ...
	Record *caas.BaselineStrategy
	// Job 任务ID
	Job *caas.BaselineStrategyJob
	// StandardJobTask 任务列表
	StandardJobs []*StandardJobTask
	// StandardItem 基线标准
	ClusterStandardItems []ClusterStandardItem
	// SkipStandardJobIds 跳过的基线任务ID
	SkipStandardJobIds []int64
	// Completed 是否完成
	Completed bool
	// Lock 分布式锁
	Lock redislock.Lock
}

// StandardJobTask 标准任务
type StandardJobTask struct {
	// BaselineName 如果这个不为空，则直接使用这个进行检查
	BaselineName *string
	// Job 信息
	Job *caas.BaselineStandardJob
	// CheckJobs 检查项任务
	CheckJobs []*caas.BaselineStandardRuleJob
	// StandardItems 基线标准列表
	CheckItems []StandardCheckItem
	// SkipCheckJobIds 跳过的基线标准检查项任务ID
	// 如果上一个步骤检查失败，则跳过这些检查项
	// 如果上一个步骤检查成功，则不跳过这些检查项
	SkipCheckJobIds []int64
	// Report 基线报告
	Report *baseline_master.GetReportResponse
	// Completed 是否完成
	Completed bool
}

// StandardJobTaskStepStatus 标准任务步骤状态
type StandardJobTaskStepStatus struct {
	// Step 步骤
	Step string
	// Status 状态
	Status string
	// StartTime 开始时间
	StartTime time.Time
	// EndTime 结束时间
	EndTime time.Time
}

// CheckJobTask 检查项任务
type CheckJobTask struct {
	// Job 信息
	Job *caas.BaselineStandardRuleJob
	// StandardItems 基线标准列表
	CheckItem StandardCheckItem
}

// AddRecurringJobRequest 执行定时检查
type AddRecurringJobRequest struct {
	// StrategyID 基线策略ID
	StrategyID int64 `json:"strategyId" form:"strategyId"`
}

// AddRecurringJobResponse 执行定时检查
type AddRecurringJobResponse struct {
	// StrategyID 基线策略ID
	StrategyID int64 `json:"strategyId"`
	// JobId 任务ID
	JobId string `json:"jobId"`
}

// DeleteRecurringJobRequest 删除定时检查
type DeleteRecurringJobRequest struct {
	// StrategyID 基线策略ID
	StrategyID int64 `json:"strategyId" form:"strategyId"`
}

// DeleteRecurringJobResponse 删除定时检查
type DeleteRecurringJobResponse struct {
	// StrategyID 基线策略ID
	StrategyID int64 `json:"strategyId"`
}

// GetRecurringJobRequest 获取定时检查
type GetRecurringJobRequest struct {
	// StrategyID 基线策略ID
	StrategyID int64 `json:"strategyId" form:"strategyId"`
}

type GetRecurringJobResponse struct {
	// Existed 是否已经存在
	Existed bool `json:"existed"`
	// CronExpression
	CronExpression string `json:"cronExpression"`
	// ExecutionConfig
	ExecutionConfig *ExecutionConfig `json:"executionConfig"`
	// JobID 任务ID
	JobID int64 `json:"jobId"`
}

type ListRecurringJobResponse struct {
	// Items 任务列表
	Items []RecurringJobItem `json:"items"`
}

type RecurringJobItem struct {
	ID string `json:"id"`

	CronJobID string `json:"cronJobId"`

	CronExpression string `json:"cronExpression"`

	// Next time the job will run, or the zero time if Cron has not been
	// started or this entry's schedule is unsatisfiable
	Next string `json:"next"`

	// Prev is the last time this job was run, or the zero time if never.
	Prev string `json:"prev"`

	// Config 执行配置
	Config *ExecutionConfig `json:"config"`
}
