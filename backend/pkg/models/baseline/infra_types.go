package baseline

import (
	v1 "harmonycloud.cn/baseline-checker/api/v1"
)

// InfraMonitorItem 监控项
type InfraMonitorItem struct {
	// MonitorName 监控项Name
	MonitorName string
	// ClusterName 集群名称
	ClusterName string
	// StandardID 标准ID
	// 基线标准ID
	StandardID int64
	// StrategyID 策略ID
	// 基线策略ID
	StrategyID int64
	// CheckID 检查项ID
	CheckID int64
	// StandardCheckID 标准检查项ID
	StandardCheckID int64
	// CheckerConfig 检查项配置
	CheckerConfig CheckerConfig
}

// MonitorParam 监控参数
type MonitorParam struct {
	// ClusterName 集群名称
	ClusterName string
	// StandardID 标准ID
	// 基线标准ID
	StandardID int64
	// StrategyID 策略ID
	// 基线策略ID
	StrategyID int64
	// CheckID 检查项ID
	CheckID int64
	// StandardCheckID 标准检查项ID
	StandardCheckID int64
	// MonitorName 监控项名称
	MonitorName string
	// CheckerConfig 检查配置
	// 检查项配置，根据其中的信息构造监控相关的任务
	CheckerConfig CheckerConfig
	// EnsureMonitorNames 确保监控项名称
	EnsureMonitorNames []string
}

type CheckerParam struct {
	// ClusterName 集群名称
	ClusterName string
	// 基线标准ID
	StandardID int64
	// StandardCheckID 标准检查项ID
	StandardCheckID int64
	// 基线标准任务ID
	StandardJobID int64
	// CheckJobID
	CheckJobID int64
	// StrategyID 策略ID
	// 基线策略ID
	StrategyID int64
	// CheckID 检查项ID
	CheckID int64
	// CheckerName 检查项名称
	CheckerName string
	// MonitorName 监控项名称
	MonitorName string
	// CheckRawData 检查项 包含检查值，以及相关配置
	CheckRawData CheckRawData
}

type InfraCheckerItem struct {
	// ClusterName 集群名称
	ClusterName string
	// 基线标准ID
	StandardID int64
	// StrategyID 策略ID
	// 基线策略ID
	StrategyID int64
	// CheckID 检查项ID
	CheckID int64
	// CheckerName 检查项资源名称
	CheckerName string
	// CheckerResource 检查项资源
	CheckerResource v1.Checker
}

// BaselineParam 基线参数
type BaselineParam struct {
	// BaselineName 基线资源名称
	BaselineName string
	// ClusterName 集群名称
	ClusterName string
	// 基线标准ID
	StandardID int64
	// StrategyID 策略ID
	// 基线策略ID
	StrategyID int64
	// StandardJobID 任务ID
	StandardJobID int64
	// ExecutionConfig 执行配置
	ExecutionConfig ExecutionConfig
	// CheckerNames checker资源
	Checkers []CheckerParam
	// Daemon 该资源是否会长期运行
	Daemon bool
}

type InfraBaselineItem struct {
	// BaselineName 基线资源名称
	BaselineName string
	// ClusterName 集群名称
	ClusterName string
	// 基线标准ID
	StandardID int64
	// StrategyID 策略ID
	// 基线策略ID
	StrategyID int64
	// Checkers 检查规则
	Checkers []InfraBaselineCheckerItem
}

type InfraBaselineCheckerItem struct {
	// CheckerName 检查项名称
	CheckerName string
	// CheckId 检查项ID
	CheckID int64
}

// MonitorStatusResult monitor
type MonitorStatusResult struct {
	// Name monitor 名称
	Name string
	// Getter getter 状态
	Getter MonitorGetterStatus
}

const (
	MonitorGetterRunning string = "running"
)

type MonitorGetterStatus struct {
	// Reason 原因
	Reason string
	// Message 名称
	Message string
}
