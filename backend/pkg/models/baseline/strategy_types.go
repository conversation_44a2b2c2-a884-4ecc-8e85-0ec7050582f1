package baseline

import (
	"reflect"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/resources"
)

// 基线策略请求结构

// GetBaselineStrategyNameExistedRequest 基线策略名称是否存在
type GetBaselineStrategyNameExistedRequest struct {
	// Name 策略名称
	Name string `json:"name" form:"name"`
}

// GetBaselineStrategyNameExistedResponse 基线策略名称是否存在
type GetBaselineStrategyNameExistedResponse struct {
	// Existed 策略名称是否存在
	Existed bool `json:"existed"`
}

// StrategyItem 定义检查策略列表的结构
type StrategyItem struct {
	// Id 策略ID
	Id int64 `json:"id"`
	// Name 策略名称
	Name string `json:"name"`
	// BaselineStandardCount 基线标准数
	BaselineStandardCount int `json:"baselineStandardCount"`
	// CheckItemCount 检查项数
	CheckItemCount int `json:"checkItemCount"`
	// CheckClusterCount 检查集群数
	CheckClusterCount int `json:"checkClusterCount"`
	// ExecutionConfigHumanReadable 用于人可阅读的执行配置
	ExecutionConfigHumanReadable string `json:"executionConfigHumanReadable"`
	// ExecutionConfig 执行策略配置
	ExecutionConfig ExecutionConfig `json:"executionConfig"`
	// Enabled 策略开关状态，true 为开启，false 为关闭
	Enabled bool `json:"enabled"`
	// CreateTime 创建时间
	CreateTime string `json:"createTime"`
	// UpdateTime 更新时间
	UpdateTime string `json:"updateTime"`
}

// RecurringType 周期类型
type RecurringType string

const (
	// RecurringTypeDaily Daily 每天
	RecurringTypeDaily RecurringType = "Daily"
	// RecurringTypeWeekly Weekly 每周
	RecurringTypeWeekly RecurringType = "Weekly"
	// RecurringTypeMonthly Monthly 每月
	RecurringTypeMonthly RecurringType = "Monthly"
	// RecurringTypeCron CronExpression 自定义 Cron 表达式
	RecurringTypeCron RecurringType = "Cron"
)

// ExecutionType 执行类型
type ExecutionType string

const (
	// ExecutionTypeImmediate Immediate 立即执行
	ExecutionTypeImmediate ExecutionType = "Immediate"
	// ExecutionTypeScheduled Scheduled 指定时间执行
	ExecutionTypeScheduled ExecutionType = "Scheduled"
	// ExecutionTypeRecurring Recurring 周期执行
	ExecutionTypeRecurring ExecutionType = "Recurring"
)

// ExecutionConfig 定义执行类型的相关配置
type ExecutionConfig struct {
	// ExecutionType 执行类型，支持 "Immediate"（立即执行）、"Scheduled"（指定时间执行）、"Recurring"（周期执行）
	ExecutionType ExecutionType `json:"executionType" form:"executionType" binding:"required"`

	// ExecutionTime 执行时间只有时分秒给周期执行用 Recurring
	// 10:24:33 表示 10点24分33秒
	ExecutionTime string `json:"executionTime,omitempty" form:"executionTime" binding:"omitempty"`

	// ExecutionDate 执行时间包含年月日和时分秒 给 Scheduled
	ExecutionDate string `json:"executionDate,omitempty" form:"executionDate" binding:"omitempty"`

	// RecurringType 周期执行的类型，仅在 ExecutionType 为 "Recurring" 时使用
	RecurringType RecurringType `json:"recurringType,omitempty" form:"recurringType" binding:"omitempty"`

	// PerDays 周期执行的天数，仅在 RecurringType 为 "Daily" 时使用
	PerDays int `json:"perDays,omitempty" form:"perDays" binding:"omitempty,min=1"`

	// DaysOfWeek 周期执行的周几，仅在 RecurringType 为 "Weekly" 时使用
	// 如 [1, 2, 3, 4, 5] 表示周一到周五, [6] 表示周六 [0] 表示周日
	DaysOfWeek []int `json:"daysOfWeek,omitempty" form:"daysOfWeek" binding:"omitempty,min=1,max=7"`

	// DaysOfMonth 周期执行的天数，仅在 RecurringType 为 "Monthly" 时使用, 表示每月的哪几天
	// 如 [1, 3] 表示每月的第一天和第三天, 当月没有这两天时会跳过
	// 目前 该参数前端用不到， 但是后续可能会用到，先忽略
	DaysOfMonth []int `json:"-" form:"daysOfMonth" binding:"omitempty,min=1,max=31"`
	// StartDayOfMonth 周期执行的开始天数，仅在 RecurringType 为 "Monthly" 时使用
	StartDayOfMonth int `json:"startDayOfMonth,omitempty" form:"startDayOfMonth" binding:"omitempty,min=1,max=31"`
	// EndDayOfMonth 周期执行的结束天数，仅在 RecurringType 为 "Monthly" 时使用
	EndDayOfMonth int `json:"endDayOfMonth,omitempty" form:"endDayOfMonth" binding:"omitempty,min=1,max=31"`

	// CronExpression 周期执行的 Cron 表达式，仅在 ExecutionType 为 "Recurring" 且 RecurringType 为 "Cron" 时使用
	CronExpression string `json:"cronExpression,omitempty" form:"cronExpression" binding:"omitempty"`

	// StartTime 周期执行的开始时间，仅在 ExecutionType 为 "Recurring", "Scheduled" 时使用
	StartTime string `json:"startTime,omitempty" form:"startTime" binding:"omitempty"`

	// EndTime 周期执行的结束时间，仅在 ExecutionType 为 "Recurring", "Scheduled" 时使用
	EndTime string `json:"endTime,omitempty" form:"endTime" binding:"omitempty"`

	// Timezone 执行时的时区，例如 UTC+08:00
	Timezone string `json:"timezone,omitempty" form:"timezone" binding:"omitempty"`
}

// QueryBaselineStrategiesRequest 定义查询检查策略的请求结构体
type QueryBaselineStrategiesRequest struct {
	// Name 策略名称，用于根据名称模糊搜索
	Name string `json:"name,omitempty" form:"name"`
	// Enabled 策略状态，用于筛选开启或关闭的策略
	Enabled *bool `json:"enabled,omitempty" form:"enabled"`
	// Filter 过滤器
	// 降序基线标准数 baselineStandardCount https://api.url?sort_name=baselineStandardCount,sort_func=number,sort_desc=desc
	// 升序基线标准数 baselineStandardCount https://api.url?sort_name=baselineStandardCount,sort_func=number,sort_desc=asc
	// 其他类似
	Filter resources.Filter[*StrategyItem] `json:"-"`
}

func (r *QueryBaselineStrategiesRequest) ToFieldMapping() map[string]string {
	return map[string]string{
		"enabled":       "enabled",
		"executionType": "execution_strategy",
		"recurringType": "execution_recurring_type",
	}

}

// QueryBaselineStrategiesResponse 查询基线策略列表
type QueryBaselineStrategiesResponse struct {
	models.PageableResponse[*StrategyItem] `json:",inline"`
}

// CreateBaselineStrategyRequest 定义创建基线检查策略的请求结构
type CreateBaselineStrategyRequest struct {
	// Name 策略名称，必填，不超过 32 个字符
	Name string `json:"name" form:"name" binding:"required,max=32"`
	// Description 策略描述，非必填，不超过 200 个字符
	Description string `json:"description,omitempty" form:"description" binding:"omitempty,max=200"`
	// ExecutionConfig 执行配置，包含执行类型及相关参数
	ExecutionConfig ExecutionConfig `json:"executionConfig" form:"executionConfig" binding:"required"`
	// SelectedBaselineIds 选择的基线标准 ID 列表
	SelectedBaselineIds []int64 `json:"selectedBaselineIds" form:"selectedBaselineIds" binding:"required"`
	// SelectedClusterNames 选择的检查集群名称列表
	SelectedClusterNames []string `json:"selectedClusterNames" form:"selectedClusterNames" binding:"required"`
}

// CreateBaselineStrategyResponse 成功创建响应
type CreateBaselineStrategyResponse struct {
	// ID 创建成功后的ID
	ID int64 `json:"id"`
}

type GetBaselineStrategyDetailsRequest struct {
	ID int64 `json:"id"`
}

// GetBaselineStrategyDetailsResponse 获取基线策略详情
type GetBaselineStrategyDetailsResponse struct {
	// ID 策略ID
	ID int64 `json:"id"`
	// Name 策略名称，必填，不超过 32 个字符
	Name string `json:"name" form:"name" binding:"required,max=32"`
	// Description 策略描述，非必填，不超过 200 个字符
	Description string `json:"description,omitempty" form:"description" binding:"omitempty,max=200"`
	// ExecutionConfigHumanReadable 用于人可阅读的执行配置
	ExecutionConfigHumanReadable string `json:"executionConfigHumanReadable"`
	// ExecutionConfig 执行配置，包含执行类型及相关参数
	ExecutionConfig ExecutionConfig `json:"executionConfig" form:"executionConfig" binding:"required"`
	// SelectedBaselineIds 选择的基线标准 ID 列表
	SelectedBaselineIds []int64 `json:"selectedBaselineIds" form:"selectedBaselineIds" binding:"required"`
	// SelectedClusterNames 选择的检查集群名称列表
	SelectedClusterNames []string `json:"selectedClusterNames" form:"selectedClusterNames" binding:"required"`
	// Enabled 策略状态 是否开启
	Enabled bool `json:"enabled"`
}

// UpdateBaselineStrategyRequest 更新基线策略
type UpdateBaselineStrategyRequest struct {
	// ID 策略ID
	ID int64 `json:"id"`
	// Name 策略名称，必填，不超过 32 个字符
	Name string `json:"name" form:"name" binding:"required,max=32"`
	// Description 策略描述，非必填，不超过 200 个字符
	Description string `json:"description,omitempty" form:"description" binding:"omitempty,max=200"`
	// ExecutionConfig 执行配置，包含执行类型及相关参数
	ExecutionConfig ExecutionConfig `json:"executionConfig" form:"executionConfig" binding:"required"`
	// SelectedBaselineIds 选择的基线标准 ID 列表
	SelectedBaselineIds []int64 `json:"selectedBaselineIds" form:"selectedBaselineIds" binding:"required"`
	// SelectedClusterNames 选择的检查集群名称列表
	SelectedClusterNames []string `json:"selectedClusterNames" form:"selectedClusterNames" binding:"required"`
}

// UpdateBaselineStrategyResponse ...
type UpdateBaselineStrategyResponse struct {
}

// DeleteBaselineStrategyRequest 删除基线策略
type DeleteBaselineStrategyRequest struct {
	// ID 基线策略id
	ID int64 `json:"id"`
}

// DeleteBaselineStrategyResponse 删除基线策略
type DeleteBaselineStrategyResponse struct {
}

// SwitchBaselineStrategyRequest 启停策略
type SwitchBaselineStrategyRequest struct {
	// ID 基线策略ID
	ID int64 `json:"id" binding:"required"`
	// Enabled 是否开启基线策略
	Enabled *bool `json:"enabled" form:"enabled" binding:"required"`
}

// SwitchBaselineStrategyResponse 启停策略响应
type SwitchBaselineStrategyResponse struct {
}

// ------------------------//
// -----  check job ----- //
//-------------------------//

type GetLastCheckJobStatusRequest struct {
	ID int64 `json:"id"`
}
type GetLastCheckJobStatusResponse struct {
	// JobID 任务ID
	JobID int64 `json:"jobId"`
	// 状态
	Status JobStatus `json:"status"`
	// CheckTime 检查时间
	CheckTime string `json:"checkTime"`
	// 完成时间
	CompletedTime string `json:"completedTime"`
}

// ClusterRiskSummary 表示集群风险的汇总数据
type ClusterRiskSummary struct {
	// Total 总集群数
	Total int `json:"total"`
	// Risk 有风险的集群数
	Risk int `json:"risk"`
	// NoRisk 无风险的集群数
	NoRisk int `json:"noRisk"`
	// NotChecked 未检查的集群数
	NotChecked int `json:"notChecked"`
	// Failed 检查失败的集群数
	Failed int `json:"failed"`
}

// StandardRiskSummary 表示基线标准的汇总数据
type StandardRiskSummary struct {
	// Total 总基线标准数
	Total int `json:"total"`
	// NotPassed 未通过的基线标准数
	NotPassed int `json:"notPassed"`
	// Passed 已通过的基线标准数
	Passed int `json:"passed"`
	// NotChecked 未检查的基线标准数
	NotChecked int `json:"notChecked"`
}

// CheckRiskSummary 检查风险汇总
type CheckRiskSummary struct {
	// Critical 严重风险数
	Critical int `json:"critical"`
	// High 高风险数
	High int `json:"high"`
	// Medium 中风险数
	Medium int `json:"medium"`
	// Low 低风险数
	Low int `json:"low"`
	// NotPassed 未通过检查项
	NotPassed int `json:"notPassed"`
	// Total 总数
	Total int `json:"total"`
}

// GetBaselineStrategySummaryRequest 获取基线策略概要
type GetBaselineStrategySummaryRequest struct {
	// ID 基线策略ID
	ID int64 `json:"id"`
}

// GetBaselineStrategySummaryResponse 获取基线策略汇总
type GetBaselineStrategySummaryResponse struct {
	// ClusterRiskSummary 集群风险的汇总数据
	ClusterRiskSummary ClusterRiskSummary `json:"clusterRiskSummary"`
	// StandardRiskSummary 基线标准的汇总数据
	StandardRiskSummary StandardRiskSummary `json:"standardRiskSummary"`
	// CheckRiskSummary 检查风险汇总
	CheckRiskSummary CheckRiskSummary `json:"checkRiskSummary"`
	// Status 策略状态
	Status ResultStatus `json:"status"`
	// LastCheckJobID 最后检查的任务
	LastCheckJobID int64 `json:"lastCheckJobId"`
	// LastCheckTime 最后一次检查时间
	LastCheckTime string `json:"lastCheckTime"`
}

type JobStatus string

const (
	// JobStatusPending 等待中
	JobStatusPending JobStatus = "Pending"
	// JobStatusRunning 进行中
	JobStatusRunning JobStatus = "Running"
	// JobStatusCompleted 完成
	JobStatusCompleted JobStatus = "Completed"
)

type ResultStatus string

const (
	// ResultStatusChecking 检查中
	ResultStatusChecking ResultStatus = "Checking"
	// ResultStatusUnchecked 未检查
	ResultStatusUnchecked ResultStatus = "Unchecked"
	// ResultStatusPassed 完成
	ResultStatusPassed ResultStatus = "Passed"
	// ResultStatusFailed 失败
	ResultStatusFailed ResultStatus = "Failed"
	// ResultStatusError 发生错误
	ResultStatusError ResultStatus = "Error"
)

type ExecuteStrategyCheckScope string

const (
	// ExecuteCheckJobScopeStrategy 策略级
	ExecuteCheckJobScopeStrategy ExecuteStrategyCheckScope = "Strategy"
	// ExecuteCheckJobScopeCluster 集群级
	ExecuteCheckJobScopeCluster ExecuteStrategyCheckScope = "Cluster"
	// ExecuteCheckJobScopeStandard 基线标准级
	ExecuteCheckJobScopeStandard ExecuteStrategyCheckScope = "Standard"
	// ExecuteCheckJobScopeCheck 检查项级
	ExecuteCheckJobScopeCheck ExecuteStrategyCheckScope = "Check"
)

// CheckClusterStandardList 检查集群基线标准
type CheckClusterListRequest struct {
	// ClusterNames 集群名称列
	ClusterNames []string `json:"clusterNames"`
}

// CheckStandardCheckList 检查基线标准检查项
type CheckStandardListRequest struct {
	// StandardID 基线标准ID
	StandardID int64 `json:"standardId"`
	// ClusterNames 集群名称列表
	ClusterNames []string `json:"clusterNames"`
}

type CheckStandardCheckListRequest struct {
	// ClusterName 集群名称
	ClusterName string `json:"clusterName"`
	// StandardID 基线标准ID
	StandardID int64 `json:"standardId"`
	// CheckID 检查项ID
	CheckIDs []int64 `json:"checkIds"`
}

type ExecuteCheckJobRequest struct {
	// StrategyID 基线策略ID
	StrategyID int64 `json:"-" form:"strategyId"`
	// ClusterRequest 检查基线的请求
	ClusterRequest CheckClusterListRequest `json:"clusterRequest" form:"clusterRequest"`
	// StandardRequest 检查基线的请求
	StandardRequest CheckStandardListRequest `json:"standardRequest" form:"standardRequest"`
	// CheckRequest 检查基线的请求
	CheckRequest CheckStandardCheckListRequest `json:"checkRequest" form:"checkRequest"`
	// Scope 检查范围 Strategy Cluster Standard Check
	// Strategy 策略级 会在所有集群上检查策略包含的检查项 需要传 strategyId
	// Cluster 集群级 会在指定集群上检查策略包含的所有基线标准 需要传 ClusterRequest
	// Standard 基线标准级 会在指定集群上检查当前标准包含的所有检查项 需要传 StandardRequest
	// Check 检查项级 会在指定集群上检查指定检查项 需要传 CheckRequest
	Scope ExecuteStrategyCheckScope `json:"scope" form:"scope"`
}

// GetExecuteStrategyCheckScopeByJobRequestIfScopeEmpty if scope emtpy return strategy with struct judge
func GetExecuteStrategyCheckScopeByJobRequestIfScopeEmpty(request ExecuteCheckJobRequest) ExecuteStrategyCheckScope {
	if request.Scope == "" {
		switch {
		case !reflect.DeepEqual(request.CheckRequest, CheckStandardCheckListRequest{}):
			return ExecuteCheckJobScopeCheck
		case !reflect.DeepEqual(request.StandardRequest, CheckStandardListRequest{}):
			return ExecuteCheckJobScopeStandard
		case !reflect.DeepEqual(request.ClusterRequest, CheckClusterListRequest{}):
			return ExecuteCheckJobScopeCluster
		}
		return ExecuteCheckJobScopeStrategy
	}
	return request.Scope
}

type ExecuteCheckJobResponse struct {
}

type PublishDeleteJobRequest struct {
}

type GetCheckJobResultRequest struct {
	// JobID 任务ID
	JobID int64 `json:"jobId" form:"jobId"`
}

type ClusterResult struct {
	ClusterName string `json:"clusterName"`
	// Risk 有风险的集群数
	Risk int `json:"risk"`
	// NoRisk 无风险的集群数
	NoRisk int `json:"noRisk"`
	// NotChecked 未检查的集群数
	NotChecked int `json:"notChecked"`
	// Failed 检查失败的集群数
	Failed int `json:"failed"`
}

// GetClusterCheckResultsRequest 获取集群检查结果
type GetClusterCheckResultsRequest struct {
	// StrategyID 基线策略ID
	StrategyID int64 `json:"strategyId" form:"strategyId"`
	// ClusterName 集群名称
	ClusterName string `json:"clusterName" form:"clusterName"`
	// Filter 过滤
	Filter resources.Filter[*ClusterCheckResultItem] `json:"-"`
}

// ClusterCheckResultItem 集群检查结果
type ClusterCheckResultItem struct {
	// ClusterName 集群名称
	ClusterName string `json:"clusterName"`
	// Status 检查状态 (未检查、检查通过、检查失败、检查中等)
	// Unchecked, Passed, Failed, Checking
	Status ResultStatus `json:"status"` // 检查状态 (未检查、检查失败、检查中等)
	// Message 检查失败的错误信息
	Message string `json:"message"`
	// Progress 检查进度，百分比 0-100
	Progress int `json:"progress"` // 检查进度，百分比

}

// GetClusterCheckResultsResponse 获取集群检查结果
type GetClusterCheckResultsResponse struct {
	// 集群检查结果
	models.PageableResponse[*ClusterCheckResultItem] `json:",inline"`
}

// GetBaselineStandardCheckResultsRequest 获取基线标准检查结果
type GetBaselineStandardCheckResultsRequest struct {
	// StrategyID 基线策略ID
	StrategyID int64 `json:"strategyId" form:"strategyId"`
	// StandardName 基线标准名称 模糊
	StandardName string `json:"standardName" form:"standardName"`
	// ClusterName 集群名称 精确
	ClusterName string `json:"clusterName" form:"clusterName"`
	// Filter 过滤
	Filter resources.Filter[*StandardCheckResultItem] `json:"-"`
}

type StandardCheckResultItem struct {
	// ID 基线标准
	ID int64 `json:"id"`
	// Name 基线标准名称
	Name string `json:"name"`
	// ClusterName 集群名称
	ClusterName string `json:"clusterName"`
	// CheckRiskSummary 检查风险汇总
	CheckRiskSummary CheckRiskSummary `json:"checkRiskSummary"`
	// PassedCount 通过数量
	PassedCount int `json:"passedCount"`
	// Status 检查状态 (未检查、检查通过、检查失败、检查中等)
	// Unchecked, Passed, Failed, Checking
	Status ResultStatus `json:"status"` // 检查状态 (未检查、检查失败、检查中等)
	// Reason ...
	Reason string `json:"reason,omitempty"`
	// Message ...
	Message string `json:"message,omitempty"`
	// BaselineName 基线名称
	BaselineName string `json:"baselineName"`
	// LastCheckJobID 上次检查的checkJobId
	LastCheckJobID int64 `json:"lastCheckJobId"`
	// LastCheckTime 最后检查时间
	LastCheckTime string `json:"lastCheckTime"`
	// LastCompletedTime 最后完成时间
	LastCompletedTime string `json:"lastCompletedTime"`
}

// GetBaselineStandardCheckResultsResponse 获取基线策略检查结果
type GetBaselineStandardCheckResultsResponse struct {
	// 集群检查结果
	models.PageableResponse[*StandardCheckResultItem] `json:",inline"`
}

// DownloadBaselineStandardCheckResultsReportRequest 获取基线策略检查结果报告
type DownloadBaselineStandardCheckResultsReportRequest GetBaselineStandardCheckResultsRequest

// DownloadBaselineStandardCheckResultsReportResponse 获取基线策略检查结果报告
type DownloadBaselineStandardCheckResultsReportResponse DownloadFileResponse

type GetCheckResultsRequest struct {
	// StrategyID 基线策略ID
	StrategyID int64 `json:"strategyId" form:"strategyId"`
	// StandardID 基线标准ID
	StandardID int64 `json:"standardId" form:"standardId"`
	// ClusterName 集群名称
	ClusterName string `json:"clusterName" form:"clusterName"`
	// CheckName 检查名称模糊
	CheckName string `json:"checkName" form:"checkName"`
	// Filter 过滤
	Filter resources.Filter[*CheckResultItem] `json:"-"`
}

// CheckResultItem 检查结果
type CheckResultItem struct {
	// ID 检查项ID
	ID int64 `json:"id"`
	// Name 检查项名称
	Name string `json:"name"`
	// Description 说明
	Description string `json:"description"`
	// Suggestion 建议
	Suggestion string `json:"suggestion"`
	// StrategyID 基线策略ID
	StrategyID int64 `json:"strategyId" form:"strategyId"`
	// CheckID 检查项ID
	CheckID int64 `json:"checkId"`
	// StandardID 基线标准ID
	StandardID int64 `json:"standardId" form:"standardId"`
	// ClusterName 集群名称
	ClusterName string `json:"clusterName"`
	// RiskLevel 风险等级
	// High Medium Low
	RiskLevel string `json:"riskLevel"`
	// Status 检查结果
	// 检查中 通过 未通过
	// Checking Passed Failed
	Status ResultStatus `json:"status"`
	// Reason 原因
	Reason string `json:"reason"`
	// Message 错误消息
	Message string `json:"message"`
	// CheckerName 检查项名称
	CheckerName string `json:"checkerName"`
	// MonitorName 监控名称
	MonitorName string `json:"monitorName"`
	// LastCheckJobID 上次检查的checkJobId
	LastCheckJobID int64 `json:"lastCheckJobId"`
	// LastCheckTime 最后检查时间
	LastCheckTime string `json:"lastCheckTime"`
	// LastCompletedTime 最后完成时间
	LastCompletedTime string `json:"lastCompletedTime"`
}

// GetCheckResultsResponse 获取检查结果
type GetCheckResultsResponse struct {
	models.PageableResponse[*CheckResultItem] `json:",inline"`
	//Items []*CheckResultItem `json:"items"`
	//Total int64              `json:"total"`
}

// DownloadCheckResultsReportRequest 下载检查结果
type DownloadCheckResultsReportRequest GetCheckResultsRequest

const ApplicationSheetContentType = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"

type DownloadFileResponse struct {
	// FileName 文件名称
	FileName string
	// FileContent 文件内容
	FileContent []byte
}

type DownloadCheckResultsReportResponse DownloadFileResponse

// UpdateBindingBaselineStandardsRequest 解绑基线标准
type UpdateBindingBaselineStandardsRequest struct {
	// StrategyID 基线策略ID
	StrategyID int64 `json:"strategyId" form:"strategyId"`
	//// BindStandardIDs 需要绑定的基线标准IDs
	//BindStandardIDs []int64 `json:"bindStandardIds" form:"bindStandardIds"`
	// UnbindStandardIDs 需要解绑的基线标准IDs
	UnbindStandardIDs []int64 `json:"unbindStandardIds" form:"unbindStandardIds"`
}

// UpdateBindingBaselineStandardsResponse 解绑基线标准
type UpdateBindingBaselineStandardsResponse struct {
	// BindStandardIDs 需要绑定的基线标准IDs
	BindStandardIDs []int64 `json:"bindStandardIds"`
	// UnbindStandardIDs 基线解绑的标准ID
	UnbindStandardIDs []int64 `json:"unbindStandardIds"`
}
