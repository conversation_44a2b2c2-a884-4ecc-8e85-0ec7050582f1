package baseline

import (
	"harmonycloud.cn/baseline-checker/pkg/models/checker"
)

// Report 报告
type ReportList struct {
	// ClusterName 集群名称
	ClusterName string
	// StandardId 基线标准ID
	StandardId int64
	// StrategyId 策略ID
	StrategyId int64
	// Reports 检查报告
	Reports []CheckReport
}

// CheckReport 检查报告
type CheckReport struct {
	// CheckId 检查项ID
	CheckId int64
	// Summary 检查结果
	Summary checker.Summary
	// LastCheckTime 最后一次检查时间
	LastCheckTime string
	// Points 检查结果序列
	Points []checker.Point
}
