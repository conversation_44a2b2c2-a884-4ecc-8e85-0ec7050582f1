package baseline

import (
	"encoding/json"
	"errors"
	"fmt"
	"mime/multipart"
	"path/filepath"
	"reflect"
	"strings"

	"harmonycloud.cn/unifiedportal/portal/backend/pkg/constants"
	bizerrors "harmonycloud.cn/unifiedportal/portal/backend/pkg/errors"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/resources"
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/utils"
)

// UploadCheckerFileRequest 表示接收上传文件的请求结构
type UploadCheckerFileRequest struct {
	// File 上传的文件
	File *multipart.FileHeader `form:"file"`
	// Content 文件内容（二进制形式）
	Content []byte `form:"content"`
	// FileName 文件名（当使用 Content 时需要）
	FileName string `form:"fileName"`
}

// UploadCheckerFileResponse 上传返回的结构体
type UploadCheckerFileResponse struct {
	// FileItem
	FileItem `json:",inline"`
}

// AggregationCheckType 聚和的检查项类型
// Command -> 命令检查
// FileJSON -> 文件检查/JSON文件
// FileYAML -> 文件检查/YAML文件
type AggregationCheckType string

const (
	// AggregationCheckTypeCommand 命令检查
	AggregationCheckTypeCommand AggregationCheckType = "Command"
	// AggregationCheckTypeFileJSON 文件检查/JSON文件
	AggregationCheckTypeFileJSON AggregationCheckType = "FileJSON"
	// AggregationCheckTypeFileYAML 文件检查/YAML文件
	AggregationCheckTypeFileYAML AggregationCheckType = "FileYAML"
	// AggregationCheckTypeFileTEXT 文件检查/文本
	AggregationCheckTypeFileTEXT AggregationCheckType = "FileTEXT"
)

// NewAggregationCheckMode 获取聚和后的检查模式
func NewAggregationCheckMode(checkerConfig *CheckerConfig) AggregationCheckType {
	if checkerConfig == nil {
		return ""
	}
	switch checkerConfig.CheckMode {
	case CheckerModeCommand:
		return AggregationCheckTypeCommand
	case CheckerModeFile:
		if checkerConfig.File != nil {
			switch checkerConfig.File.FileType {
			case FileTypeJSON:
				return AggregationCheckTypeFileJSON
			case FileTypeYAML:
				return AggregationCheckTypeFileYAML
			case FileTypeTEXT:
				return AggregationCheckTypeFileTEXT
			}
		}
	}
	return ""
}

type RiskLevel = string

const (
	// RiskLevelHigh 高危
	RiskLevelHigh RiskLevel = "High"
	// RiskLevelMedium 中危
	RiskLevelMedium RiskLevel = "Medium"
	// RiskLevelLow 低危
	RiskLevelLow RiskLevel = "Low"
)

// CheckItem 定义检查项列表的结构
type CheckItem struct {
	// Id 检查项ID
	ID int64 `json:"id"`
	// Name 检查项名称
	Name string `json:"name"`
	// Kind 类型 如 Kubernetes 核心组件, 容器运行时
	Kind string `json:"kind"`
	// ResourceType 检查资源类型，例如 ETCD, Kubelet, Docker 等
	ResourceType string `json:"resourceType"`
	// RiskLevel 风险级别，例如 高危High、中危Medium、低危Low
	RiskLevel RiskLevel `json:"riskLevel"`
	// Builtin 是否内置
	Builtin bool `json:"builtin,omitempty" form:"builtin"`
	// Description 检查项说明
	Description string `json:"description"`
	// CheckType 列表中经过聚和后的类型
	// Command -> 命令检查
	// FileJSON -> 文件检查/JSON文件
	// FileYAML -> 文件检查/YAML文件
	CheckType AggregationCheckType `json:"checkMode" form:"checkMode"`
	// CheckerConfig 获取检查的具体内容
	CheckerConfig *CheckerConfig `json:"-"`
	// CreateTime 创建时间，格式为 YYYY-MM-DD HH:mm:ss
	CreateTime string `json:"createTime"`
	// UpdateTime 更新时间，格式为 YYYY-MM-DD HH:mm:ss
	UpdateTime string `json:"updateTime"`
}

// GetCustomCheckerNameExistedRequest ...
type GetCustomCheckerNameExistedRequest struct {
	// Name 名称
	Name string `json:"name" form:"name"`
}

// GetCustomCheckerNameExistedResponse ...
type GetCustomCheckerNameExistedResponse struct {
	// Existed true 为存在 false 为不存在
	Existed bool `json:"existed"`
}

// QueryCustomCheckersRequest 查询自定义检查项
type QueryCustomCheckersRequest struct {
	// ResourceType 检查资源类型
	ResourceType string `json:"resourceType,omitempty" form:"resourceType"`
	// RiskLevel 风险级别 high medium low
	RiskLevel string `json:"riskLevel,omitempty" form:"riskLevel"`
	// CheckMode 检查方式
	CheckMode string `json:"checkMode,omitempty" form:"checkMode"`
	// Builtin 是否内置
	Builtin *bool `json:"builtin,omitempty" form:"builtin"`
	// Filter 过滤器
	resources.Filter[*CheckItem]
}

func (r *QueryCustomCheckersRequest) ApplyFieldsToFilter() {
	if r.Filter.Exact == nil {
		r.Filter.Exact = map[string]string{}
	}
	if len(r.ResourceType) > 0 {
		r.Filter.Exact["resourceType"] = r.ResourceType
	}
	if len(r.RiskLevel) > 0 {
		r.Filter.Exact["riskLevel"] = r.RiskLevel
	}
	if len(r.CheckMode) > 0 {
		r.Filter.Exact["checkMode"] = r.CheckMode
	}
}

// ToFieldMapping 返回字段映射
func (r *QueryCustomCheckersRequest) ToFieldMapping() map[string]string {
	return map[string]string{
		"resourceType": "check_resource_type",
		"riskLevel":    "risk_level",
		"checkMode":    "check_type",
	}
}

// QueryCustomCheckersResponse 自定义检查项响应
type QueryCustomCheckersResponse struct {
	*models.PageableResponse[*CheckItem] `json:",inline"`
}

// CheckDetailItem 自定义检查项的详细信息
type CheckDetailItem struct {
	// Id 检查项ID
	ID int64 `json:"id"`
	// Name 检查项名称
	Name string `json:"name"`
	// ResourceType 检查资源类型，例如 ETCD, Kubelet, Docker 等
	ResourceType string `json:"resourceType"`
	// RiskLevel 风险级别，例如 高危High、中危Medium、低危Low
	RiskLevel RiskLevel `json:"riskLevel"`
	// Kind 类型 如 Kubernetes 核心组件, 容器运行时
	Kind string `json:"kind"`
	// Builtin 是否内置
	Builtin bool `json:"builtin,omitempty" form:"builtin"`
	// Description 检查项说明
	Description string `json:"description"`
	// Suggestion 处理建议
	Suggestion string `json:"suggestion"`
	// CheckerConfig 获取检查的具体内容
	CheckerConfig *CheckerConfig `json:"checkerConfig,omitempty"`
	// CreateTime string 	创建时间
	CreateTime string `json:"createTime"`
	// UpdateTime 更新时间，格式为 YYYY-MM-DD HH:mm:ss
	UpdateTime string `json:"updateTime"`
}

// GetCustomCheckerDetailsRequest 获取自定义检查项详情
type GetCustomCheckerDetailsRequest struct {
	// 检查项ID
	ID int64 `json:"id" form:"id"`
}

// GetCustomCheckerDetailsResponse 自定义检查项响应
type GetCustomCheckerDetailsResponse struct {
	// CheckDetailItem 自定义检查项
	*CheckDetailItem `json:",inline"`
}

type GetAssignStandardCheckersRequest struct {
	// StandardId 需要分配的基线标准ID
	StandardId int64 `json:"standardId" form:"standardId"`
	// IsCopy 是否拷贝
	IsCopy bool `json:"isCopy" form:"isCopy"`
	// 过滤器
	Filter resources.Filter[*StandardCheckItem] `json:"-"`
}

// GetAssignStandardCheckersResponse
// 获取分配给基线标准的自定义检查项
type GetAssignStandardCheckersResponse struct {
	// 所有的checkIds
	CheckIds []int64 `json:"checkIds"`
	// SelectedCheckInfo 选择的checkInfo
	SelectedCheckInfo *SelectedCheckInfo `json:"selectedCheckInfo,omitempty"`
	// PageableResponse 可以分给基线标准的所有自定义检查项
	models.PageableResponse[*StandardCheckItem] `json:",inline"`
}

const (
	// CustomCheckerKind 默认的自定义检查项类型
	CustomCheckerKind = "Custom"
	// BuiltinCheckerKind 内置的检查项类型
	BuiltinCheckerKind = "Builtin"

	// CheckerResourceTypeCoreComponent 核心组件
	CheckerResourceTypeCoreComponent = "CoreComponent"
	// CheckerResourceTypeSystemComponent 系统组件
	CheckerResourceTypeSystemComponent = "SystemComponent"
)

// CreateCustomCheckerRequest 定义创建自定义检查项的请求结构
type CreateCustomCheckerRequest struct {
	// Name 自定义检查项名称，不能为空且长度不超过32个字符
	Name string `json:"name" form:"name" binding:"required,max=128"`

	// ResourceType 检查资源类型，必填，支持的类型如 Kubernetes, Docker, Host
	ResourceType string `json:"resourceType" form:"resourceType" binding:"required"`

	// RiskLevel 风险级别，必填，支持的值包括 High、Medium、Low 高、中、低危
	RiskLevel RiskLevel `json:"riskLevel" form:"riskLevel" binding:"required,oneof=High Medium Low"`

	// Description 检查项说明，非必填，最长300个字符
	Description string `json:"description" form:"description" binding:"omitempty,max=300"`

	// Suggestion 处理建议，非必填，最长300个字符
	Suggestion string `json:"suggestion" form:"suggestion" binding:"omitempty,max=300"`

	// CheckerConfig 检查项配置，必填，定义了检查方式（命令或文件）
	CheckerConfig *CheckerConfig `json:"checkerConfig" form:"checkerConfig" binding:"required"`
}

func (r *CreateCustomCheckerRequest) Validate() error {
	if err := r.CheckerConfig.Validate(); err != nil {
		var bizErr bizerrors.Error
		if errors.As(err, &bizErr) {
			return bizErr
		}
		return bizerrors.NewFromCodeWithMessage(bizerrors.Var.ParamError, err.Error())
	}
	return nil
}

// CreateCustomCheckerResponse 创建自定义检查项响应
type CreateCustomCheckerResponse struct {
	// Id  创建后的id
	ID int64 `json:"id"`
}

// UpdateCustomCheckerRequest 编辑自定义检查项
type UpdateCustomCheckerRequest struct {
	// ID 自定义检查项ID
	ID int64 `json:"id" form:"id" binding:"omitempty"`
	// Name 自定义检查项名称，不能为空且长度不超过32个字符
	Name string `json:"name" form:"name" binding:"required,max=128"`
	// ResourceType 检查资源类型，必填，支持的类型如 Kubernetes, Docker, Host
	ResourceType string `json:"resourceType" form:"resourceType" binding:"required"`

	// RiskLevel 风险级别，必填，支持的值包括 High、Medium、Low 高、中、低危
	RiskLevel RiskLevel `json:"riskLevel" form:"riskLevel" binding:"required"`

	// Description 检查项说明，非必填，最长300个字符
	Description string `json:"description" form:"description" binding:"omitempty,max=300"`

	// Suggestion 处理建议，非必填，最长300个字符
	Suggestion string `json:"suggestion" form:"suggestion" binding:"omitempty,max=300"`

	// CheckerConfig 检查项配置，必填，定义了检查方式（命令或文件）
	CheckerConfig *CheckerConfig `json:"checkerConfig" form:"checkerConfig" binding:"required"`
}

func (r *UpdateCustomCheckerRequest) Validate() error {
	if err := r.CheckerConfig.Validate(); err != nil {
		var bizErr bizerrors.Error
		if errors.As(err, &bizErr) {
			return bizErr
		}
		return bizerrors.NewFromCodeWithMessage(bizerrors.Var.ParamError, err.Error())
	}
	return nil
}

type UpdateCustomCheckerResponse struct {
	ID int64 `json:"id"`
}

type CheckerMode string

const (
	// CheckerModeCommand 命令检查模式
	CheckerModeCommand CheckerMode = "Command"
	// CheckerModeFile 文件检查模式
	CheckerModeFile CheckerMode = "File"
)

type CheckRawData struct {
	Value  CheckValueItem `json:"value"`
	Config CheckerConfig  `json:"config"`
}

// Validate 检查项配置，用于描述命令检查或文件检查的具体信息
func (c *CheckRawData) Validate() error {
	config := &CheckerConfig{}
	err := utils.BeanCopy(c.Config, config)
	if err != nil {
		return bizerrors.NewFromCodeWithMessage(bizerrors.Var.ParamError, err.Error())
	}
	// 如果value 不为空 则把value的值塞到config 里
	if reflect.DeepEqual(c.Value, CheckValueItem{}) {
		switch config.CheckMode {
		case CheckerModeCommand:
			if config.Command != nil {
				if c.Value.Value != "" {
					config.Command.Command = c.Value.Value
				}
			}
		case CheckerModeFile:
			if config.File != nil {
				if c.Value.File != nil && c.Value.File.Name != "" && c.Value.File.Id > 0 {
					config.File.FileContent = c.Value.File
				} else if c.Value.Value != "" {
					config.File.MatchContent = &c.Value.Value
				}
			}
		}
	}
	if err := config.Validate(); err != nil {
		var bizErr bizerrors.Error
		if errors.As(err, &bizErr) {
			return bizErr
		}
		return bizerrors.NewFromCodeWithMessage(bizerrors.Var.ParamError, err.Error())
	}

	return nil
}
func (c *CheckRawData) Unmarshal(raw []byte) error {
	if err := json.Unmarshal(raw, &c.Value); err != nil {
		return fmt.Errorf("unmarshal value error: %v", err)
	}
	return nil
}

func (c *CheckRawData) Marshal() ([]byte, error) {
	raw, err := json.Marshal(c.Value)
	if err != nil {
		return nil, fmt.Errorf("marshal value error: %v", err)
	}
	return raw, nil
}

type CheckerNodeRole string

const (
	// CheckerNodeRoleMaster 主控节点
	CheckerNodeRoleMaster CheckerNodeRole = "Master"
	// CheckerNodeRoleWorker 工作节点
	CheckerNodeRoleWorker CheckerNodeRole = "Worker"
	// CheckerNodeRoleSystem 系统节点
	CheckerNodeRoleSystem CheckerNodeRole = "System"
)

// NodeSelector 节点选择器
type NodeSelector struct {
	// LabelSelectors 标签选择器
	// 格式为 key=value,key=value
	LabelSelectors []string `json:"labelSelectors" form:"labelSelectors"`
}

// NewNodeSelectorsFromNodeRoles 从节点角色创建节点选择器
func NewNodeSelectorsFromNodeRoles(nodeRoles []CheckerNodeRole) *NodeSelector {
	if len(nodeRoles) == 0 {
		return nil
	}
	labelSelectors := make([]string, 0)
	for _, nodeRole := range nodeRoles {
		switch nodeRole {
		case CheckerNodeRoleMaster:
			labelSelectors = append(labelSelectors, constants.NodeMasterRoleLabelKey)
			labelSelectors = append(labelSelectors, constants.NodeControlPlaneRoleLabelKey)
		case CheckerNodeRoleSystem:
			labelSelectors = append(labelSelectors, constants.NodeSystemRoleLabelKey)
		case CheckerNodeRoleWorker:
			labelSelectors = append(labelSelectors, fmt.Sprintf("!%s,!%s", constants.NodeMasterRoleLabelKey, constants.NodeControlPlaneRoleLabelKey))
		}
	}
	return &NodeSelector{
		LabelSelectors: labelSelectors,
	}
}

// CheckerConfig 检查项配置，用于描述命令检查或文件检查的具体信息
type CheckerConfig struct {
	// CheckMode 检查方式，必填，支持 "命令检查" 或 "文件检查"
	CheckMode CheckerMode `json:"checkMode" form:"checkMode" binding:"required,oneof=Command File"`

	// NodeRoles 节点检查范围，必填，支持值包括 Master, Worker, System
	NodeRoles []CheckerNodeRole `json:"nodeRoles" form:"nodeRoles" binding:"omitempty"`

	// NodeSelector 节点选择器
	NodeSelector *NodeSelector `json:"-" form:"nodeSelector,omitempty" binding:"omitempty"`

	// Command 命令检查方式，只有在 CheckMode 为 "命令检查" 时才有效
	Command *CommandChecker `json:"command" form:"command"`

	// File 文件检查方式，只有在 CheckMode 为 "文件检查" 时才有效
	File *FileChecker `json:"file" form:"file"`
}

func (c *CheckerConfig) Validate() error {
	switch c.CheckMode {
	case CheckerModeCommand:
		if c.Command == nil {
			return bizerrors.NewFromCodeWithMessage(bizerrors.Var.BaselineCustomCheckerConfigInvalid, "command checker is required")
		}
		return c.Command.Validate()
	case CheckerModeFile:
		if c.File == nil {
			return bizerrors.NewFromCodeWithMessage(bizerrors.Var.BaselineCustomCheckerConfigInvalid, "file checker is required")
		}
		return c.File.Validate()
	}
	return nil
}

type MatchType string

const (
	// MatchTypeFuzzy 模糊匹配
	MatchTypeFuzzy MatchType = "Fuzzy"
	// MatchTypeExact 精确匹配
	MatchTypeExact MatchType = "Exact"
)

// CommandChecker 命令检查配置，包含检查命令和匹配标准
type CommandChecker struct {
	// Command 执行的命令，最大长度为500字符
	Command string `json:"command,omitempty" form:"command,omitempty" binding:"omitempty,max=500"`

	// MatchType 匹配类型，支持 "模糊" 或 "精确" 匹配
	MatchType MatchType `json:"matchType,omitempty" form:"matchType,omitempty" binding:"omitempty,oneof=Fuzzy Exact"`

	// MatchValue 匹配值，用于命令输出匹配，支持模糊或精确匹配
	MatchValue string `json:"matchValue,omitempty" form:"matchValue,omitempty" binding:"omitempty,max=500"`
}

func (c *CommandChecker) Validate() error {
	if c.Command == "" {
		return bizerrors.NewFromCodeWithMessage(bizerrors.Var.BaselineCustomCheckerConfigInvalid, "command is required")
	}
	switch c.MatchType {
	case MatchTypeFuzzy, MatchTypeExact:
		if c.MatchValue == "" {
			return bizerrors.NewFromCodeWithMessage(bizerrors.Var.BaselineCustomCheckerConfigInvalid, "match value is required")
		}
	}
	return nil
}

type FileType string

const (
	// FileTypeYAML yaml
	FileTypeYAML FileType = "Yaml"
	// FileTypeJSON json
	FileTypeJSON FileType = "Json"
	// FileTypeTEXT text
	FileTypeTEXT FileType = "Text"
)

// FileChecker 文件检查配置，包含文件类型、位置和匹配内容
type FileChecker struct {
	// FileType 文件类型，支持 "yaml" 或 "json", "Text" 文本文件
	FileType FileType `json:"fileType,omitempty" form:"fileType,omitempty" binding:"required,oneof=Yaml Json Text"`

	// FileLocation 文件位置配置，定义文件的来源（K8s 资源或主机路径）
	FileLocation FileLocationConfig `json:"fileLocation" form:"fileLocation" binding:"required"`

	// MatchContent 匹配的文件内容，非必填，用于检查文件中是否包含特定内容,优先级高于 FileContent
	MatchContent *string `json:"matchContent,omitempty" form:"matchContent,omitempty" binding:"omitempty"`

	// FileContent 匹配的文件信息, 非必填, 用于检查文件中是否包含特定内容
	FileContent *FileItem `json:"fileContent,omitempty" form:"fileContent,omitempty" binding:"omitempty"`
}

func (c *FileChecker) Validate() error {
	if err := c.FileLocation.Validate(); err != nil {
		return bizerrors.NewFromCodeWithMessage(bizerrors.Var.BaselineCustomCheckerUploadInvalid, err.Error())
	}
	if c.MatchContent == nil && c.FileContent == nil {
		return bizerrors.NewFromCodeWithMessage(bizerrors.Var.BaselineCustomCheckerUploadInvalid, "one of matchContent or fileContent is required")
	}
	switch c.FileType {
	case FileTypeYAML:
		// check yaml is valid
		isValidYaml := true
		if c.FileContent != nil {
			c.FileContent.Name = strings.TrimSpace(c.FileContent.Name)
			ext, _ := strings.CutPrefix(filepath.Ext(c.FileContent.Name), ".")
			if ext != "yml" && ext != "yaml" {
				isValidYaml = false
			}
		}
		if isValidYaml && c.MatchContent != nil {
			if !utils.IsValidYaml(*c.MatchContent) {
				isValidYaml = false
			}
		}
		if !isValidYaml {
			return bizerrors.NewFromCodeWithMessage(bizerrors.Var.BaselineCustomCheckerConfigFileInvalidYAML,
				"file check content is not valid yaml")
		}
	case FileTypeJSON:
		// check json is valid
		isValidJson := true
		if c.FileContent != nil {
			c.FileContent.Name = strings.TrimSpace(c.FileContent.Name)
			ext, _ := strings.CutPrefix(filepath.Ext(c.FileContent.Name), ".")
			if ext != "json" {
				isValidJson = false
			}
		}
		if isValidJson && c.MatchContent != nil {
			if !utils.IsValidJson(*c.MatchContent) {
				isValidJson = false
			}
		}
		if !isValidJson {
			return bizerrors.NewFromCodeWithMessage(bizerrors.Var.BaselineCustomCheckerConfigFileInvalidJSON,
				"file check content is not valid json")
		}
	case FileTypeTEXT:
		// do nothing
	}
	return nil
}

type FileLocationMode string

const (
	// FileLocationModeK8sResource K8s 资源
	FileLocationModeK8sResource FileLocationMode = "K8sResource"
	// FileLocationModeHostPath 主机路径
	FileLocationModeHostPath FileLocationMode = "HostPath"
)

// FileLocationConfig 文件位置配置，定义文件的具体位置
type FileLocationConfig struct {
	// Mode 文件来源模式，支持 "K8sResource" 或 "HostPath"
	Mode FileLocationMode `json:"mode,omitempty" form:"mode,omitempty" binding:"required,oneof=K8sResource HostPath"`

	// Path 如果 Mode 为 "HostPath"，则此字段指定文件路径
	Path string `json:"path,omitempty" form:"path,omitempty" binding:"omitempty,max=200"`

	// K8sResourceRef 如果 Mode 为 "K8sResource"，此字段指定 Kubernetes 资源
	K8sResourceRef K8sResourceRef `json:"k8sResourceRef,omitempty" form:"k8sResourceRef,omitempty" binding:"omitempty"`
}

type K8sResourceRef struct {
	Namespace  string            `json:"namespace,omitempty" form:"namespace,omitempty" binding:"omitempty"`
	Name       string            `json:"name,omitempty" form:"name,omitempty" binding:"omitempty"`
	Kind       string            `json:"kind,omitempty" form:"kind,omitempty" binding:"omitempty"`
	APIVersion string            `json:"apiVersion,omitempty" form:"apiVersion,omitempty" binding:"omitempty"`
	Labels     map[string]string `json:"labels,omitempty" form:"labels,omitempty" binding:"omitempty"`
}

func (f *FileLocationConfig) Validate() error {
	switch f.Mode {
	case FileLocationModeK8sResource:
		if reflect.DeepEqual(f.K8sResourceRef, K8sResourceRef{}) {
			return fmt.Errorf(" kubernetes resources info is required")
		}
	case FileLocationModeHostPath:
		if f.Path == "" {
			return fmt.Errorf("host path is required")
		}
	default:
		return fmt.Errorf("unknown location mode: %s", f.Mode)
	}
	return nil
}

// DeleteCustomCheckerRequest 删除检查项请求
type DeleteCustomCheckerRequest struct {
	// Ids 需要删除的 checkers
	Ids []int64 `json:"ids"`
}

// DeleteCustomCheckerResponse 删除自定义检查项响应
type DeleteCustomCheckerResponse struct {
	// 成功删除的ids
	Ids []int64 `json:"ids"`
}

// QueryAssociatedBaselinesRequest 查询关联的基线标准
type QueryAssociatedBaselinesRequest struct {
	// Id 检查项ID
	Id string `json:"id"`
}

type AssociateBaselineItem struct {
	// StandardId 关联的基线标准id
	StandardId int64 `json:"standardId"`
	// StandardName 关联基线标准名称
	StandardName string `json:"standardName"`
	// CategoryName 分类名称
	CategoryName string `json:"categoryName"`
}

// QueryAssociatedBaselinesResponse 响应
type QueryAssociatedBaselinesResponse struct {
	// Items 关联的基线标准
	Items []AssociateBaselineItem `json:"items"`
}

// UpdateCustomCheckerBindingStandardsRequest 更新绑定的基线策略表
type UpdateCustomCheckerBindingStandardsRequest struct {
	// ID 基线策略ID
	ID int64 `json:"id" form:"id"`
	// UnbindStandardIds 基线id
	UnbindStandardIds []int64 `json:"unbindStandardIds" form:"unbindStandardIds"`
}

// UpdateCustomCheckerBindingStandardsResponse ...
type UpdateCustomCheckerBindingStandardsResponse struct {
}
