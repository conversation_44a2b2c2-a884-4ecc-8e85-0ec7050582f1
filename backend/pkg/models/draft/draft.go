package draft

import "encoding/json"

type Request struct {
	Type  string                 `json:"type,omitempty" example:"cluster_create"` // 模版类型
	Param map[string]interface{} `json:"param,omitempty"`                         // 模版参数
}

func (req *Request) GetParamBytes() ([]byte, error) {
	return json.Marshal(req.Param)
}

// Status
// 模版响应状态
type Status string

var (
	// Exists
	// 模版存在
	Exists Status = "draft_exists"
	// NotExists
	// 模版不存在
	NotExists Status = "draft_not_exists"
)

// Response
// 模版返回
type Response struct {
	Status Status        `json:"status" example:"draft_exists"` // 模版状态 支持值 模版存在:draft_exists;模版不存在:draft_not_exists
	Data   *DataResponse `json:"data"`                          // 模版信息
}
type DataResponse struct {
	Type  string                 `json:"type" example:"cluster_create"` // 模版类型
	Draft map[string]interface{} `json:"draft"`                         // 模版内容
}
