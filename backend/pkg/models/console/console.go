package console

// ConsoleSessionRequest 控制台会话请求
// @Description 控制台会话请求参数
type ConsoleSessionRequest struct {
	NodeName     string `json:"nodeName" binding:"required" example:"node-1"` // 节点名称
	Mode         string `json:"mode" example:"debug"`                         // 模式，支持debug和ssh
	Shell        string `json:"shell" example:"/bin/sh"`                      // Shell类型（debug模式）
	Privileged   bool   `json:"privileged" example:"true"`                    // 是否特权模式（debug模式）
	SSHKeySecret string `json:"sshKeySecret" example:"ssh-private-key"`       // SSH密钥secret名称（ssh模式）
	SSHUser      string `json:"sshUser,omitempty" example:"root"`             // SSH用户（ssh模式）
	SSHPort      string `json:"sshPort,omitempty" example:"22"`               // SSH端口（ssh模式）
	SSHPassword  string `json:"sshPassword,omitempty" example:"password"`     // 新增
}

// ConsoleSessionResponse 控制台会话响应
// @Description 控制台会话响应数据
type ConsoleSessionResponse struct {
	PodName  string `json:"podName" example:"node-debugger-node-1-abc12"` // Pod名称
	NodeName string `json:"nodeName" example:"node-1"`                    // 节点名称
	Status   string `json:"status" example:"Running"`                     // Pod状态
	Mode     string `json:"mode" example:"debug"`                         // 会话模式
}

// SessionStatusResponse 会话状态响应
// @Description 会话状态响应数据
type SessionStatusResponse struct {
	NodeName  string `json:"nodeName" example:"node-1"`                    // 节点名称
	Status    string `json:"status" example:"Running"`                     // Pod状态
	PodName   string `json:"podName" example:"node-debugger-node-1-abc12"` // Pod名称
	StartTime string `json:"startTime" example:"2025-06-18T00:00:00Z"`     // 开始时间
	Mode      string `json:"mode" example:"debug"`                         // 会话模式
}

// WebsocketMessage WebSocket消息
// @Description WebSocket消息格式
type WebsocketMessage struct {
	Type string `json:"type" example:"data"`   // 消息类型
	Data string `json:"data" example:"output"` // 消息数据
}

// WebSocketConnectionStatus WebSocket连接状态
// @Description WebSocket连接状态信息
type WebSocketConnectionStatus struct {
	Connected bool   `json:"connected" example:"true"`                     // 是否已连接
	PodName   string `json:"podName" example:"node-debugger-node-1-abc12"` // Pod名称
	Message   string `json:"message" example:"Connected to node console"`  // 连接消息
}
