package models

import "strings"

type CloudServiceComponentSortByClusterList []CloudServiceComponentSortByCluster

func (list CloudServiceComponentSortByClusterList) Len() int {
	return len(list)
}

func (list CloudServiceComponentSortByClusterList) Less(i, j int) bool {
	clusterI := list[i].Cluster
	clusterJ := list[j].Cluster
	return strings.Compare(clusterI, clusterJ) > 0
}

func (list CloudServiceComponentSortByClusterList) Swap(i, j int) {
	list[i], list[j] = list[j], list[i]
}

type CloudComponentWorkloadPodInstanceList []CloudComponentWorkloadPodInstance

func (list CloudComponentWorkloadPodInstanceList) Len() int {
	return len(list)
}

func (list CloudComponentWorkloadPodInstanceList) Less(i, j int) bool {
	namespaceNameI := list[i].Namespace + list[i].Name
	namespaceNameJ := list[j].Namespace + list[j].Name
	return strings.Compare(namespaceNameI, namespaceNameJ) > 0
}

func (list CloudComponentWorkloadPodInstanceList) Swap(i, j int) {
	list[i], list[j] = list[j], list[i]
}
