package logicalunit

import (
	"harmonycloud.cn/unifiedportal/portal/backend/pkg/models/cluster"
	"time"
)

type DataCenter struct {
	Id           int64           `json:"id"`
	Name         string          `json:"name"`
	Remarks      string          `json:"remarks"`
	Code         string          `json:"code"`
	LogicalUnits []UnitIdAndName `json:"logicalUnit"`
}

type DataCenterInfo struct {
	Id           int64              `json:"id"`
	Name         string             `json:"name"`
	Remarks      string             `json:"remarks"`
	Code         string             `json:"code"`
	LogicalUnits []LogicalUnitRough `json:"logicalUnits"`
}

type CreateDataCenter struct {
	Name    string `json:"name"`
	Remarks string `json:"remarks"`
	Code    string `json:"code"`
}

type UnitIdAndName struct {
	Id   int64  `json:"id"`
	Name string `json:"name"`
}

type LogicalUnit struct {
	Id           int64  `json:"id"`
	DataCenterId int64  `json:"dataCenterId"`
	Name         string `json:"name"`
	Code         string `json:"code"`
	Type         string `json:"type"`
	Territory    string `json:"territory"`
	Remarks      string `json:"remarks"`
}

type DataCenterAndUnit struct {
	Id              int64                     `json:"id"`
	Name            string                    `json:"name"`
	Remarks         string                    `json:"remarks"`
	Code            string                    `json:"code"`
	LogicalUnitList []LogicalUnit             `json:"logicalUnitList"`
	Clusters        []LogicalUnitClusteStatus `json:"clusters"`
}

type LogicalUnitRough struct {
	Id              int64  `json:"id"`
	DataCenterId    int64  `json:"dataCenterId"`
	Name            string `json:"name"`
	Code            string `json:"code"`
	Type            string `json:"type"`
	Territory       string `json:"territory"`
	Remarks         string `json:"remarks"`
	ClusterNum      int    `json:"clusterNum"`
	OnLineNum       int    `json:"onLineNum"`
	NotOnLineNum    int    `json:"notOnLineNum"`
	RegistryNum     int    `json:"registryNum"`
	BackupServerNum int    `json:"backupServerNum"`
}

type InfoBinding struct {
	LogicalUnitId int64    `json:"logicalUnitId"`
	InfoType      string   `json:"infoType"`
	Infos         []string `json:"infos"`
}

type LogicalUnitClusteStatus struct {
	ClusterName   string    `json:"clusterName"`
	ClusterStatus string    `json:"clusterStatus"`
	CreateTime    time.Time `json:"createTime"`
}

type InfoBindingIds struct {
	Id string `json:"id"`
}

type InfoBindingCluster struct {
	ClusterName     string              `json:"clusterName"`
	ClusterStatus   string              `json:"clusterStatus"`
	K8sVersion      *string             `json:"k8sVersion"`
	ClusterLabel    *string             `json:"clusterLabel"`
	Prole           []cluster.ProleType `json:"prole"`
	ClusterNetwork  map[string]string   `json:"clusterNetwork,omitempty"`
	ClusterStore    int                 `json:"clusterStore,omitempty"`
	NodeCount       int                 `json:"nodeCount" example:"5"`
	NodeNum         map[string]int      `json:"nodeNum,omitempty" example:"amd64:1"`
	IngressClassNum map[string]int      `json:"ingressClassNum,omitempty" example:"nginx:1"`
}

type UnitInfoDetails struct {
	LogicalUnit     LogicalUnit          `json:"logicalUnit"`
	BindingClusters []InfoBindingCluster `json:"bindingClusters"`
	BackupServerIds []InfoBindingIds     `json:"backupServerIds"`
	RegistryIds     []InfoBindingIds     `json:"registryIds"`
}

type ClusterIngressClass struct {
	IngressName string `json:"ingressName"`
	Count       int    `json:"count"`
}

type ClusterNetwork struct {
	NetworkName string `json:"networkName"`
	StackMode   string `json:"stackMode"`
}

type ClusterNode struct {
	NodeName string `json:"nodeName"`
	Count    int    `json:"count"`
}

type ClusterStorageClass struct {
	Count int `json:"count"`
}
