package models

import (
	"time"
)

// K8sResourceDeleteRequest 表示删除资源的请求结构
type K8sResourceDeleteRequest struct {
	Resources []ResourceIdentifier `json:"resources"` // 要删除的资源列表
}

// ResourceIdentifier 表示单个资源的标识符
type ResourceIdentifier struct {
	Name      string `json:"name"`      // 资源名称
	Namespace string `json:"namespace"` // 命名空间，集群级资源为空
}

// ApiResourceByGVK 表示获取ApiResource的返回结构
type ApiResourceByGVK struct {
	TotalNum                int                `json:"totalNum"`                // 资源总数
	NameSpaceApiResourceNum int                `json:"nameSpaceApiResourceNum"` // 命名空间资源数
	ClusterApiResourceNum   int                `json:"clusterApiResourceNum"`   // 集群资源数
	Items                   []*ApiResourceByGV `json:"items"`                   // 资源列表
}

// ApiResourceByGV 表示同一GV下ApiResource的资源列表
type ApiResourceByGV struct {
	GroupVersion string         `json:"groupVersion"` // GV
	Resources    []*APIResource `json:"resources"`    // api-resource资源
}

// APIResource 自定义的ApiResource结构（因为原ApiResource结构中有些参数暂时用不到，所以自定义简洁化展示）
type APIResource struct {
	Name         string `json:"name" protobuf:"bytes,1,opt,name=name"`
	SingularName string `json:"singularName" protobuf:"bytes,6,opt,name=singularName"`
	Namespaced   bool   `json:"namespaced" protobuf:"varint,2,opt,name=namespaced"`
	Group        string `json:"group,omitempty" protobuf:"bytes,8,opt,name=group"`
	Version      string `json:"version,omitempty" protobuf:"bytes,9,opt,name=version"`
	Kind         string `json:"kind" protobuf:"bytes,3,opt,name=kind"`
	IsWorkLoad   bool   `json:"isWorkLoad"`
}

// ResourceInfo 定义资源实例相关信息
type ResourceInfo struct {
	Name       string       `json:"name"`                // 资源实例名称
	Namespace  string       `json:"namespace,omitempty"` // 命名空间
	IsWorkLoad bool         `json:"isWorkLoad"`          // 是否是工作负载类型
	WorkLoad   WorkloadInfo `json:"workLoad,omitempty"`  // 工作负载
	CreateTime *time.Time   `json:"createTime"`          // 创建时间
}

// WorkloadInfo 定义工作负载相关信息
type WorkloadInfo struct {
	Ready   int `json:"ready"`   // 当前已就绪的副本数
	Desired int `json:"desired"` // 期望的副本数
}

// ResourcesList 定义资源实例返回列表
type ResourcesList struct {
	TotalNum int             `json:"totalNum"` // 资源实例数量
	Items    []*ResourceInfo `json:"items"`    // 资源实例列表
}
