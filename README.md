# Harmonycloud Unified Portal

## 联调环境

[平台](http://************)

master ************

密码: root/Hc@Cloud01


## 运行

启动

```shell
go run backend/cmd/backend-server/main.go \
  --kubeconfig ~/.kube/config  \
  --enable-swagger true \
  --database-host ${MYSQL_HOST} \
  --database-port ${MYSQL_PORT} \
  --database-username ${MYSQL_USER} \
  --database-password ${MYSQL_PASSWORD} 
```


命令参数

```shell
HarmonyCloud Cloud Service Backend

Usage:
  backend-server [flags]

Flags:
      --database-args string         database connect args (default "charset=utf8mb4&loc=Local&parseTime=true")
      --database-host string         database connect host (default "caas-mysql")
      --database-password string     database connect username (default "Hc@Cloud01")
      --database-port string         database connect port (default "3306")
      --database-scheme string       which database you select (default "amp")
      --database-username string     database connect username (default "root")
      --enable-swagger string        wether swagger eanbled (default "false")
  -h, --help                         help for backend-server
      --jwt-secret string            JWT Verify Secret (default "4ab4a93a857043f9b58c2abfb8442e74")
      --kubeconfig string            hub cluster kube-config path
      --port string                  http server port (default ":8080")
      --use-database-driver string   what database driver you want to use,now support [mysql] (default "mysql")

```