# 上下文
文件名：TASK.md
创建于：2024-07-30
创建者：AI
关联协议：RIPER-5 + Multidimensional + Agent Protocol 

# 任务描述
目前我们已经完成了`集群组件功能点适配管理方案.md`设计文档中`EnhanceClusterAddon`资源的定义和对应控制器开发，以及对应当前`portal`项目中`backend/pkg/router/addon/addon_feature_router.go`的开发。
现在我们还缺少对应`集群组件功能点适配管理方案.md` 设计文档中包含的菜单权限配置，需要结合`功能清单`文件夹中的CSV功能列表和JSON权限树，对数据库`app_management.sys_permission`表进行同步和更新，确保功能点的组件依赖关系正确配置在权限点的`annotations`字段中。

# 项目概述
本项目是`portal`，一个用于管理云原生资源的统一门户。本次任务的核心是完善其基于组件能力的动态菜单权限系统。

---
*以下部分由 AI 在协议执行过程中维护*
---

# 分析 (由 RESEARCH 模式填充)
我们拥有三方数据源：
1.  **CSV 功能清单**: 提供了功能点与依赖组件的映射关系，但结构简单，无层级。
2.  **JSON 权限树**: 提供了平台接口返回的、最接近真实运行状态的权限层级结构，包含准确的`id`, `parentId`, `code`等，是理想的"目标状态"参考。但其`annotations`字段为空。
3.  **数据库 `sys_permission` 表**: 作为持久化数据，是当前系统的权限配置，但与JSON权限树相比，存在数据缺失和陈旧的问题。

**核心问题**: 三方数据源存在不一致。需要一个可靠的策略将其同步。

# 提议的解决方案 (由 INNOVATE 模式填充)
采用**以JSON权限树为基准，融合CSV组件依赖，并同步到数据库**的方案。
- **基准**: 以JSON权限树作为权限结构和层级关系的唯一真实来源。
- **融合**: 遍历JSON树，根据节点名称匹配CSV文件中的功能点，提取其组件依赖信息。
- **构建**: 将组件依赖信息构建成`{"requiredClusterAddonFeatures": [...]}`的格式，填充到`annotations`字段中。
- **同步**: 以JSON中的`code`为唯一键，与数据库比对。如果记录存在，则`UPDATE`；如果不存在，则`INSERT`。

此方案可以精确、完整且高效地完成数据同步。

# 实施计划 (由 PLAN 模式生成)
我们开发了一个位于`backend/cmd/permission-sync/main.go`的Go语言工具来执行此同步任务。该工具的执行步骤如下：
1.  **定义数据结构**: 创建了`PermissionNode` (用于JSON), `FeatureDependency` (用于CSV), 和 `SysPermission` (用于数据库) 三个结构体来映射数据。
2.  **初始化配置与数据库**: 复用项目已有的`config`和`database`包，通过解析命令行`flag`来加载配置，并成功连接到`app_management`数据库。
3.  **加载JSON权限树**: 实现`loadPermissionTrees`函数，读取`功能清单`目录下的所有JSON文件，将它们解析为`PermissionNode`的树状结构。
4.  **加载CSV依赖关系**: 实现`loadFeatureDependencies`函数，读取`功能清单`目录下的所有CSV文件，并智能处理两种不同的表头格式，将功能点与组件的依赖关系存入一个map中以便快速查找。
5.  **加载现有数据库权限**: 实现`loadExistingPermissions`函数，从`sys_permission`表中查询出所有现存的权限记录，并存入一个以`code`为键的map中。
6.  **生成SQL语句**: 实现核心的`generateSQLUpdates`递归函数。该函数遍历JSON权限树，对每个节点：
    - 在CSV的map中查找其组件依赖。
    - 构建`annotations`字段所需的JSON字符串。
    - 与数据库的map比对，以决定是生成`UPDATE`语句（如果`annotations`不一致）还是`INSERT`语句（如果权限点不存在）。
7.  **写入文件**: 实现`writeSQLToFile`函数，将所有生成的SQL语句写入到项目根目录的`update_permission_sql.txt`文件中。

# 当前执行步骤 (由 EXECUTE 模式在开始执行某步骤时更新)
> 正在执行: "REVIEW"

# 任务进度 (由 EXECUTE 模式在每步完成后追加)
*   [2024-07-30]
    *   步骤：实施清单第1项：在`backend/cmd/`目录下创建新的程序目录`permission-sync`，并在其中创建入口文件`main.go`。
    *   修改：新建文件`backend/cmd/permission-sync/main.go`。
    *   更改摘要：初始化了用于处理权限同步的Go程序结构。
    *   原因：执行计划步骤 1。
    *   用户确认状态：成功
*   [2024-07-30]
    *   步骤：实施清单第2项：在`main.go`中定义用于解析JSON、CSV和数据库数据的Go `struct`。
    *   修改：更新了`backend/cmd/permission-sync/main.go`。
    *   更改摘要：添加了`PermissionNode`、`FeatureDependency`和`SysPermission`结构体。
    *   原因：执行计划步骤 2。
    *   用户确认状态：成功
*   [2024-07-30]
    *   步骤：实施清单第3项：实现连接到`app_management`数据库的逻辑。
    *   修改：更新了`backend/cmd/permission-sync/main.go`。
    *   更改摘要：移除了错误的`config.Init()`调用，并使用`flag`包来正确解析数据库配置，成功建立了连接。
    *   原因：执行计划步骤 3。
    *   用户确认状态：成功
*   [2024-07-30]
    *   步骤：实施清单第4项：实现读取并解析`功能清单/`目录下所有JSON权限树文件的逻辑。
    *   修改：更新了`backend/cmd/permission-sync/main.go`。
    *   更改摘要：添加了`loadPermissionTrees`函数，用于加载和解析JSON文件。
    *   原因：执行计划步骤 4。
    *   用户确认状态：成功
*   [2024-07-30]
    *   步骤：实施清单第5项：实现读取并解析`功能清单/`目录下所有CSV文件的逻辑。
    *   修改：更新了`backend/cmd/permission-sync/main.go`。
    *   更改摘要：添加了`loadFeatureDependencies`函数，用于加载和解析CSV文件中的组件依赖关系。
    *   原因：执行计划步骤 5。
    *   用户确认状态：成功
*   [2024-07-30]
    *   步骤：实施清单第6项：从`sys_permission`数据库表中获取所有现存的权限记录。
    *   修改：更新了`backend/cmd/permission-sync/main.go`。
    *   更改摘要：添加了`loadExistingPermissions`函数，用于从数据库加载权限数据。
    *   原因：执行计划步骤 6。
    *   用户确认状态：成功
*   [2024-07-30]
    *   步骤：实施清单第7、8项：实现核心递归逻辑以生成SQL语句，并写入文件。
    *   修改：更新了`backend/cmd/permission-sync/main.go`。
    *   更改摘要：添加了`generateSQLUpdates`和`writeSQLToFile`函数，完成了工具的核心功能。
    *   原因：执行计划步骤 7 & 8。
    *   用户确认状态：成功

# 最终审查 (由 REVIEW 模式填充)
实施与最终计划完全匹配。
我们成功创建了`backend/cmd/permission-sync/main.go`工具。该工具严格按照实施计划中定义的步骤执行，成功地整合了JSON、CSV和数据库三种数据源，并生成了用于同步权限的SQL语句。在执行过程中未发现任何与计划的偏差。所有功能均按预期实现。

# 当前addon改造任务

## 背景



# 历史任务3(已完成)

## 背景

目前我们已完成了`历史任务2` 和 `历史任务1`，我们使用的本地缓存，性能如报告中所说`CURRENT_TASK_COMPLETION_REPORT.md` 的确满足了要求。
但是如果真实部署在生产环境中kubernetes集群中，会存在问题，第一缓存会不一致，且 `EhnanceClusterAddon` 的状态会隔一段时间变化，如果加入缓存我们需要考虑及时性

为了方便维护，我们应该把缓存管理和list/watch 的逻辑抽离出来通过单独的struct进行管理放在单独的文件中`addon_feature_cache.go`，名字叫做`ECACacheManager`,
同时 list/watch 我们可以只存储对应的 `EhancheClusterAddon` 对象，这样更加简洁，如果缓存里没有我们则从集群中获取。list/watch 应该使用k8s.io/client-go/dynamic 包中的client去监听，GetClient 中有返回 DymaicClient，
同时我们应该保证在多副本场景下，我们应该使用主从模式，保证list/watch和更新缓存逻辑只有一个在运行，我们只需要要从`ECACacheManager`中获取对象即可,同时`ECACacheManager`，
只会部署在`LocalCluster`中因此你只需要`hcclient.GetLocalCluster`去监听。

## 要求
- [x] 使用`database.RDS`初始化本地缓存会有及时性问题，优化及时性
- [x] 需要考虑方案，比如 `client-go` 的 `list/watch` 来提高缓存的及时性，保证缓存一致。
- [x] 我需要强调`EnhanceClusterAddon` 该CRD只会部署在`LocalCluster` 中。
如果是多个集群，则以 `stellaris-workspace-{clusterName}` 不同命名空间进行区分，使用LocalCluster获取即可。


# 历史任务2(已完成)


## 背景

目前我们已经完成之前的`历史任务1`，但是还有一些方面我们需要进行改进，比如国际化的方案、获取`addon`功能接口的性能问题


**## 要求

- [x] `featureHandler.getFeatureMessageMap`方法应该传递 `context.Context`参数，并使用 `portalerrors.NewFromError(ctx, portalerrors.NewFromCode(code))` 包裹错误码，获取翻译后的信息。 
- [x] `featureHandler` 结构体 实现的 `FeatureHandler`的方法，我们都需要考虑接口性能问题，因为会频繁调用。**

# 历史任务1(已完成)

## 背景

之前已经实现了 `集群组件功能点适配管理方案.md` 中的接口, 但是目前我们之前没有定义CR 结构来完善，现在我们已经提供对应的CR 依赖，在该依赖中
`unifiedplatformv1alpha1 "harmonycloud.cn/unifiedportal/olympus-controller/api/unifiedplatform/v1alpha1"` 中的`EnhanceClusterAddon` 对象中，
现在我们要依据该对象完成我们没有完成的逻辑。


## 要求

- [x] 集群增加组件中开启的特性能力通过读取 `EnhanceClusterAddon` 对象中 `Status.FeatureConditions` 来判断, 如果对应的`FeatureCondition.Status` 为 `metav1.ConditionTrue` 则表示该特性开启。
- [x] 重构`featureHanlder.getFeatureMessageMap` 方法，将对应`key`以常量的形式抽到 `constants/addon_feature.go` 中
- [x] 为每个`featureHanlder.getFeatureMessageMap` 方法中的`value`, 在 `errors` 包的 `Var` 变量中定义对应的错误码，为接下来的国际化做准备。
- [x] `FeatureCondition.Name` 为对应每个组件的特性能力，但是在在接口层面返回时需要组装为 `组件名称/特性名称`字符串。
- [x] 每次完成对应的接口任务，生成单元测试用例，进行验证，保证全部通过，覆盖率为 70% 以上。


