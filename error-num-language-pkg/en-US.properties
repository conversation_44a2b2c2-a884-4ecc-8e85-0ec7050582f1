8401=Unauthorized permission
8402=Unauthorized operation
8403=Resource not found
8404=Resource already exists
8405=Resource Conflict
8406=Resource has been removed
8407=Request invalid
8408=Server timeout
8409=operation timeout
8410=Too many requests
8411=incorrect request
8412=Request method not allowed
8413=Request not acceptable
8414=Request entity too large
8415=unsupported media type
8416=k8s server internal error
8417=Resource has expired
8418=Service unavailable
8419=k8s error
8420=k8s resource is being deleted
8421=This resource is protected by Finalizers and cannot be deleted temporarily
10000=unknown error
10001=Resource does not exist
10002=Request rejected
10003=Invalid token
10004=Cluster does not exist
10005=Cluster not online
10006=Parameter error
10007=Connection failed
10008=Please enter a Boolean value, reference value true | false
10010=unknown user status
10011=User not logged in
10012=unresolved user structure
10013=token not effective
10014=token has expired
10020=Failed to obtain cloud service resource list
10050=Asynchronous task forcibly stopped
10051=Asynchronous task execution timeout
10052=Asynchronous task execution failed
10100=Duplicate backup warehouse name
10101=The current backup warehouse has been bound by the backup policy and cannot be deleted
10102=Request timeout, please check if the backup files exist in the backup repository
10103=Request failed
10104=Duplicate backup policy name
10105=Duplicate restoration strategy name
10106=Backup strategy has been used
10107=Restoration strategy does not exist
10108=Storage service does not exist
10201=Cluster name already exists, please modify the cluster name
10202=Node username cannot be empty
10203=Node password cannot be empty
10204=Solution name not set
10205=Unknown node authentication type
10206=value of installer without label
10207=System error, please contact the administrator. There is a cluster creation task with the same name in the background
10208=Create cluster task does not exist
10209=Cannot delete non execution failed cluster creation tasks
10210=In the primary backup switching mode, the Stellaris component access address information for the backup management cluster is required
10211=Please select at least one container network solution
10212=Cluster address required in high availability mode
10213=Cluster default load balancing in high availability mode
10214=The secret file for stellaries token configuration is missing. Please contact the administrator
10215=The secret file key for Stellaris token configuration is missing. Please contact the administrator
10216=solution step does not exist
10217=default load balancing node does not exist
10218=ETCD data disk must be filled in when selecting automatic mounting for Master node
10219=When selecting automatic mounting for system nodes, the system data disk must be filled in
10220=Node not filled in Containerd data disk
10221=Node not filled in Kubelet data disk
10222=baseline version is empty, please confirm that the cluster is created for the platform
10223=The platform does not support upgrading the current baseline version cluster
10224=Upgrade package name cannot be empty
10225=Node list cannot be empty
10226=Load balancing cannot be empty
10227=Cluster upgrade configuration file is missing, please contact the administrator
10228=Sisyphus component configuration lost
10300=Template file does not exist
10301=Port cannot be empty
10302=Please fill in the correct port range, reference value 0-65535
10303=Node username cannot be empty
10304=Node password cannot be empty
10305=GPU node parameter error, please enter Boolean value, reference value true | false
10306=Node IP input error
10307=Node IP duplication
10308=Please provide at least 1 node in All In One mode
10309=Please provide at least 3 nodes in minimized high availability mode
10310=Please provide at least 4 nodes in standard non high availability mode
10311=Please provide at least 7 nodes in standard high availability mode
10312=Cluster node configuration lost, please contact the administrator
10313=illegal cluster node configuration value
10314=The operation cluster must be a cluster that failed to be created
10315=Node operating system loss
10316=Node architecture loss
10317=Unknown architecture type
10318=There is no operating system for this architecture
10319=Solution version temporarily unavailable
10320=The uploaded content is empty, please fill in the information and upload again
10321=Node name cannot be empty
10322=duplicate node name
10323=nodes in the upload node that do not exist in the node list
10324=Node IP cannot be modified
10325=missing node information for uploading, please download the template again and fill in the information before uploading
10400=CIDR format error
10401=Illegal IP
10402=subnet mask filling error
10403=IP not within the mask representation range
10404=End IP needs to be after Start IP
10405=Reserved IP format error
10406=Reserved IP not within the network segment
10407=Reserved IP not between the starting IP and ending IP
10408=Please enter the name of the network card that needs to be bound
10500=The main control node and the upstream and downstream nodes are not allowed to be the same node
10501=IP duplication
10502=installer internal error, please contact the administrator to delete dirty data
10503=The main control node does not exist
10504=Existing nodes both online and offline
10505=Installer does not exist
10506=There are multiple records in the Installer, please contact the administrator
10507=Node on/off tasks can only be updated when they fail to run
10508=Node on/off tasks can only be deleted when they fail to run
10509=Cluster baseline version not initialized, please initialize baseline version. The cluster baseline version initialization can be completed using the command 'kubectl label stc {clusterName} caas-infra-baseline={version} '
10510=baseline version not adapted
10511=When the disk is automatically mounted, the online node must fill in Docker and Kubelet data disks
10512=Online node cannot exist in the cluster
10513=Offline nodes must exist within the cluster
10613=Backup server has been allocated and cannot be removed
10614=bucket has been used
10615=allocation failed
10616=removal failed
10617=Access address verification failed
10618=username and password verification failed
10619=Duplicate backup server name
10620=Bucket creation failed
10621=update bucket failed
10622=Failed to delete bucket
10623=Checking bucket failed
10624=Access address cannot be duplicated
10625=Bucket names cannot be duplicated on the same backup server
10626=Backup server is already in use by tenant and cannot be removed
10627=The backup server has been used by the project and cannot be removed
10628=The current storage bucket has been used by a tenant's project and cannot be unallocated
10629=The quota limit should be greater than the current usage