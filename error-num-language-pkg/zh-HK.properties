8401=許可權未授權
8402=無權操作
8403=資源未找到
8404=資源已存在
8405=資源衝突
8406=資源已被移除
8407=請求無效
8408=服務器超時
8409=操作超時
8410=請求過多
8411=錯誤請求
8412=請求方法不允許
8413=請求不可接受
8414=請求實體過大
8415=不支持的媒體類型
8416=k8s服務器內部錯誤
8417=資源已過期
8418=服務不可用
8419=k8s錯誤
8420=k8s資源正在删除中
8421=此資源被Finalizers（終結器）保護，暫不允許删除
10000=未知的錯誤
10001=資源不存在
10002=請求被拒絕
10003=無效的token
10004=集羣不存在
10005=集羣未線上
10006=參數錯誤
10007=連接失敗
10008=請輸入布林類型值，參攷值true|false
10010=未知的使用者狀態
10011=用戶未登錄
10012=無法解析的用戶結構
10013=token未生效
10014=token已過期
10020=獲取雲服務資源清單失敗
10050=非同步任務被強制停止
10051=非同步任務執行超時
10052=非同步任務執行失敗
10100=備份倉庫名稱重複
10101=當前備份倉庫已被備份策略綁定，無法删除
10102=請求超時，請檢查備份倉庫中備份檔案是否存在
10103=請求失敗
10104=備份策略名稱重複
10105=還原策略名稱重複
10106=備份策略已被使用
10107=還原策略不存在
10108=存儲服務不存在
10201=集羣名稱已存在，請修改集羣名稱
10202=節點用戶名不能為空
10203=節點密碼不能為空
10204=solution名稱未設定
10205=未知的節點認證類型
10206=installer不包含label的值
10207=系統錯誤，請聯系管理員，後臺存在同名的集羣創建任務
10208=創建集羣任務不存在
10209=不可删除非執行失敗的創建集羣任務
10210=主備切換模式下，備管理集羣Stellaris組件訪問地址資訊必填
10211=請至少選擇一種容器網絡解決方案
10212=高可用模式下集羣地址必填
10213=高可用模式下集羣默認負載均衡
10214=stellaries token配寘的secret檔案遺失，請聯系管理員
10215=stellaries token配寘的secret檔案key遺失，請聯系管理員
10216=solution step不存在
10217=默認負載均衡節點不存在
10218=Master節點選擇自動掛載時候ETCD數據盤必填寫
10219=系統節點選擇自動掛載時候系統數據盤必填寫
10220=存在節點未填寫Containerd數據盤
10221=存在節點未填寫Kubelet數據盤
10222=基線版本為空，請確定集羣為平臺創建
10223=平臺不支持當前基線版本集羣陞級
10224=升級包名稱不能為空
10225=節點清單不能為空
10226=負載均衡不能為空
10227=集羣陞級設定檔缺失，請聯系管理員
10228=西西弗斯組件配寘遺失
10300=模版檔案不存在
10301=埠不能為空
10302=請填寫正確的埠範圍，參攷值0-65535
10303=節點用戶名不能為空
10304=節點密碼不能為空
10305=是否為GPU節點參數錯誤，請輸入布林值，參攷值true|false
10306=節點IP填寫錯誤
10307=節點IP重複
10308=All-In-One模式下請至少提供1臺節點
10309=最小化高可用模式下請至少提供3臺節點
10310=標準非高可用模式下請至少提供4臺節點
10311=標準高可用模式下請至少提供7臺節點
10312=集羣節點配寘遺失，請聯系管理員
10313=集羣節點配寘值非法
10314=操作集羣必須為創建失敗的集羣
10315=節點作業系統遺失
10316=節點架構遺失
10317=未知的架構類型
10318=不存在該架構的作業系統
10319=解決方案版本暫不可用
10320=上傳內容為空，請填寫資訊後重新上傳
10321=節點名稱不能為空
10322=節點名稱重複
10323=上傳節點中存在節點清單中不存在的節點
10324=節點Ip不能修改
10325=上傳節點缺失節點資訊，請重新下載模版填寫資訊後上傳
10400=CIDR格式錯誤
10401=IP不合法
10402=子網路遮罩填寫錯誤
10403=IP不在遮罩表示範圍內
10404=結束IP需在啟始IP之後
10405=保留IP格式錯誤
10406=保留IP不在網段內
10407=保留IP不在啟始IP與結束IP之間
10408=請輸入需要綁定的網卡名稱
10500=主控節點與上下線節點不允許為同一臺節點
10501=IP重複
10502=installer內部錯誤，請聯系管理員删除髒數據
10503=主控節點不存在
10504=已存在節點上下線節點
10505=Installer不存在
10506=存在多條記錄Installer，請聯系管理員
10507=節點上下線任務只允許運行失敗時更新
10508=節點上下線任務只允許運行失敗時删除
10509=集羣基線版本未初始化，請初始化基線版本。 可使用命令kubectl label stc {clusterName} caas-infra-baseline={version}完成集羣基線版本初始化
10510=基線版本未適配
10511=當磁片自動掛載時，上線節點必須填寫Docker、Kubelet數據盤
10512=上線節點不可存在與集羣中
10513=下線節點必須存在與集羣中
10613=備份服務器已分配，無法移除
10614=桶已經被使用
10615=分配失敗
10616=移除失敗
10617=訪問地址校驗失敗
10618=用戶名密碼校驗失敗
10619=備份伺服器名稱重複
10620=創建桶失敗
10621=更新桶失敗
10622=删除桶失敗
10623=檢查桶失敗
10624=訪問地址不可重複
10625=存儲桶名稱在同個備份服務器下不可重複
10626=備份服務器已被租戶使用，無法移除
10627=備份服務器已被項目使用，無法移除
10628=當前存儲桶已被租戶下項目使用，無法取消分配
10629=配額上限應大於當前用量

