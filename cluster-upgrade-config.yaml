solutionInfos:
  - group: upgrade-components
    name: upgrade-components
    version: 1.1.0-1.0.0-universal
    id: upgrade-components:1.1.0-1.0.0-universal
  - group: upgrade-kubernetes-and-cri-1.22
    name: upgrade-kubernetes-and-cri
    version: 1.1.0-1.22k8s-1.0.0-universal
    id: upgrade-kubernetes-and-cri:1.1.0-1.22k8s-1.0.0-universal
  - group: upgrade-kubernetes-and-cri-1.23
    name: upgrade-kubernetes-and-cri
    version: 1.1.0-1.23k8s-1.0.0-universal
    id: upgrade-kubernetes-and-cri:1.1.0-1.23k8s-1.0.0-universal
nodeCodes:
  - 主控节点(常用master)
  - 主控节点
  - 非主控节点
groupInfos:
  component-front:
    - upgrade-component-front-group
  kubernetes-1.22:
    - upgrade-kubernetes-and-cri-1.22
  kubernetes-1.23:
    - upgrade-kubernetes-and-cri-1.23
  component-rear:
    - upgrade-component-rear-group
stepGroups:
  - code: upgrade-component-front-group
    alias: 组件前置升级
    steps:
      - code: upgrade-coredns-configuration
        alias: 升级coredns配置
      - code: upgrade-metrics-server
        alias: 升级基础监控组件
      - code: upgrade-nginx-ingress-controller
        alias: 升级nginx负载均衡
      - code: upgrade-application-model
        alias: 升级应用模型组件
      - code: install-egress
        alias: 安装出口网关组件
      - code: install-iris
        alias: 安装流量控制(QoS)组件
      - code: uninstall-node-up-down
        alias: 卸载节点上下线
      - code: install-mongodb
        alias: 安装mongodb
      - code: install-sisyphus-cluster-manager
        alias: 安装集群生命周期管理组件
  - code: upgrade-component-group
    alias: 组件后置升级
    steps:
      - code: import-data-from-sisyphus
        alias: 从部署平台中导入数据至目标集群
      - code: replace-expose-rule-on-default-nginx-lb
        alias: 替换对外服务暴露规则
      - code: patch-expose-rule-on-default-nginx-lb
        alias: 新增对外服务暴露规则
  - code: upgrade-kubernetes-and-cri-1.22
    alias: 集群升级至1.22
    steps:
      - code: upgrade-init-master-kubeadm
        alias: 升级初始化主控节点kubeadm配置文件
      - code: upgrade-init-master-kubernetes-binary
        alias: 升级初始化主控节点kubernetes软件包
      - code: upgrade-init-master-for-recreate-gpu-scheduler-policy
        alias: 初始化主控节点重新创建gpu调度策略
      - code: upgrade-node-kubeadm
        alias: 升级节点kubeadm配置文件
      - code: upgrade-node-kubernetes-binary
        alias: 升级节点kubernetes软件包
      - code: upgrade-node-for-recreate-gpu-scheduler-policy
        alias: 主控节点重新创建gpu调度策略
  - code: upgrade-kubernetes-and-cri-1.23
    alias: 集群升级至1.23
    steps:
      - code: upgrade-init-master-kubeadm
        alias: 升级初始化主控节点kubeadm配置文件
      - code: drain-init-master
        alias: 排干初始化主控节点
      - code: upgrade-init-master-node-cri-socket-annotations
        alias: 升级初始化主控节点CRI通信注解
      - code: stop-init-master-kubelet-service
        alias: 停止初始化主控节点kubelet服务
      - code: upgrade-init-master-cri-binary-for-containerd
        alias: 升级初始化主控节点容器运行时软件包(containerd)
      - code: upgrade-init-master-kubernetes-binary
        alias: 升级初始化主控节点kubernetes软件包
      - code: upgrade-init-master-for-recreate-gpu-scheduler-policy
        alias: 初始化主控节点重新创建gpu调度策略
      - code: uncordon-init-master
        alias: 恢复初始化主控节点可调度
      - code: upgrade-node-kubeadm
        alias: 升级节点kubeadm配置文件
      - code: drain-node
        alias: 排干节点
      - code: upgrade-nodes-cri-socket-annotations
        alias: 升级节点CRI通信注解
      - code: stop-node-kubelet-service
        alias: 停止节点节点kubelet服务
      - code: upgrade-node-cri-binary-for-containerd
        alias: 升级节点容器运行时软件包(containerd)
      - code: upgrade-node-kubernetes-binary
        alias: 升级节点kubernetes软件包
      - code: upgrade-node-for-recreate-gpu-scheduler-policy
        alias: 主控节点重新创建gpu调度策略
      - code: uncordon-node
        alias: 恢复节点可调度
initial:
  groupCode: init-group
  groupAlias: 升级前预检
  stepFirstCode: first-step-component-status-check
  stepFirstAlias: 待升级组件状态检查
  stepSecondCode: second-step-k8s-version-check
  stepSecondAlias: K8s版本检查
  stepThirdCode: third-step-node-status-check
  stepThirdAlias: 节点状态检查
  stepFourthCode: fourth-step-node-connectivity-check
  stepFourthAlias: 节点连通性检查