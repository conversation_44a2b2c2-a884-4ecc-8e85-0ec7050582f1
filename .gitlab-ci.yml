image: *************/library/golang:1.23-cicd-20250313
stages:
  - swag
  - test
  - build
  - deploy
variables:
  GOPROXY: "https://goproxy.cn,direct"
  REGISTRY_URL: "*************"
  REGISTRY_USER: "admin"
  REGISTRY_PASSWORD: "Harbor12345"
  REPOSITORY: k8s-deploy/olympus-portal
  VERSION: v3.6.0
  ENV_MASTER: "*************"
  ENV_MASTER_PWD: "Ab123456"

before_script:
  - echo "Setting up Go environment"
  - mkdir -p $GOPATH/src $GOPATH/bin
  - export IMG=${REGISTRY_URL}/${REPOSITORY}:${VERSION}-$(git rev-parse --short HEAD)


test:
  stage: test
  tags:
    - go
  script:
    - echo "Running tests..."
    #- make test
  retry: 
    max: 2
  only:
    - merge_requests
    # dev 开头的分支和merge 跑test
    - dev-v1.2.0-baseline
    #- /^dev-v[0-9]+\.[0-9]+\.[0-9]+$/

build:
  stage: build
  tags:
    - go
  retry: 
    max: 2
  script:
    - echo "Generating Swagger Docs ..."
    - make swagger
    - echo "Building..."
    - mkdir -p ~/.docker
    - export PWD_BASE64="$(echo -n "$REGISTRY_USER:$REGISTRY_PASSWORD" | base64)"
    - echo "{\"auths\":{\"${REGISTRY_URL}\":{\"auth\":\"${PWD_BASE64}\"}}}" > ~/.docker/config.json
    - BUILDCTL_ADDR=unix:///run/buildkit/buildkitd.sock PLATFORMS=linux/amd64 IMG=${IMG} make buildctl-build
  only:
    - dev-v1.2.0-baseline


deploy:
  stage: deploy
  tags:
    - go
  retry: 
    max: 2
  script:
    - echo "Update Env..."
    - |
      # 设置变量
      DEPLOY_CMD="kubectl -n caas-system set image deploy olympus-portal olympus-portal=${IMG}"
      RESTART_CMD="kubectl -n caas-system rollout restart deploy olympus-portal"
      # 执行 set image 并检查输出
      OUTPUT=$(sshpass -p ${ENV_MASTER_PWD} ssh -o StrictHostKeyChecking=no root@${ENV_MASTER} "${DEPLOY_CMD}")
      #sshpass -p "Hc@Cloud01" ssh -o StrictHostKeyChecking=no root@*********** "${DEPLOY_CMD}"
      # 检查输出是否包含 'updated'
      if echo "$OUTPUT" | grep -q "updated"; then
        echo "Image updated successfully, no restart needed."
      else
        echo "No updates detected, restarting deployment..."
        sshpass -p ${ENV_MASTER_PWD} ssh -o StrictHostKeyChecking=no root@${ENV_MASTER} "${RESTART_CMD}"
      fi
  only:
    - dev-v1.2.0-baseline