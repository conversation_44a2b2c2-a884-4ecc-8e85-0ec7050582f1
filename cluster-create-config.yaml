solutionInfos:
  - kubernetesVersion: v1.27
    criVersion: Containerd-1.6.28
    solutionName: caas-infra
    solutionVersion: 3.0.2_hotfix.2-1.0.1-universal
    mergeLabels:
      caas-infra-baseline: 1.2.0
    mergeAnnotations: { }
    dataImportVersion:
      nodeUpDown: 1.2.0-1.0.2-universal
      resetNode: 1.2.0-1.0.1-universal
  - kubernetesVersion: v1.23
    criVersion: Docker-19.03.15
    solutionName: caas-infra
    solutionVersion: 2.1.0_hotfix.1-1.0.0-universal
    mergeLabels:
      caas-infra-baseline: 2.1.0
    mergeAnnotations: { }
    dataImportVersion:
      nodeUpDown: 2.1.0-1.0.0-universal
      resetNode: 2.1.0-1.0.0-universal
groupInfo:
  initialing:
    - node-disk-mount-group
  prefligting:
    - cluster-preflight-group
  installing:
    - cluster-create-group
nodeHasGpuCode: GPU节点
nodeRoleMasterCodes:
  - 主控节点(常用master)
  - 主控节点
stepGroups:
  - code: node-disk-mount-group
    alias: 节点数据盘挂载
    steps:
      - code: sisyphus-disk-execution
        alias: 执行部署平台磁盘管理步骤
        labels:
          - AllInOne/auto
      - code: sisyphus-disk-execution
        alias: 执行部署平台磁盘管理步骤
        labels:
          - StandardNoneHA/auto
      - code: sisyphus-disk-execution
        alias: 执行部署平台磁盘管理步骤
        labels:
          - StandardHA/auto
      - code: sisyphus-disk-execution
        alias: 执行部署平台磁盘管理步骤
        labels:
          - MinimizeHA/auto
  - code: cluster-preflight-group
    alias: 集群创建预检
    steps:
      - code: sisyphus-system-step-prepare-execution
        alias: 执行部署平台初始化步骤
        labels:
          - AllInOne
      - code: precheck-check-os
        alias: 预检-检查CPU架构/操作系统
        labels:
          - AllInOne
      - code: precheck-check-linux-packages
        alias: 预检-检查是否存在已知的冲突软件包
        labels:
          - AllInOne
      - code: precheck-check-master-resources
        alias: 预检-检查主控节点资源是否符合要求
        labels:
          - AllInOne
      - code: precheck-check-system-nodes-resources
        alias: 预检-检查系统节点资源是否符合要求
        labels:
          - AllInOne
      - code: precheck-check-system-nodepool-resources
        alias: 预检-检查系统资源池是否符合要求
        labels:
          - AllInOne
      - code: precheck-check-system-node-vgs
        alias: 预检-检查系统节点VG是否创建
        labels:
          - AllInOne
      - code: sisyphus-system-step-prepare-execution
        alias: 执行部署平台初始化步骤
        labels:
          - StandardNoneHA
      - code: precheck-check-os
        alias: 预检-检查CPU架构/操作系统
        labels:
          - StandardNoneHA
      - code: precheck-check-linux-packages
        alias: 预检-检查是否存在已知的冲突软件包
        labels:
          - StandardNoneHA
      - code: precheck-check-master-resources
        alias: 预检-检查主控节点资源是否符合要求
        labels:
          - StandardNoneHA
      - code: precheck-check-system-nodes-resources
        alias: 预检-检查系统节点资源是否符合要求
        labels:
          - StandardNoneHA
      - code: precheck-check-system-nodepool-resources
        alias: 预检-检查系统资源池是否符合要求
        labels:
          - StandardNoneHA
      - code: precheck-check-system-node-vgs
        alias: 预检-检查系统节点VG是否创建
        labels:
          - StandardNoneHA
      - code: sisyphus-system-step-prepare-execution
        alias: 执行部署平台初始化步骤
        labels:
          - StandardHA
      - code: precheck-check-os
        alias: 预检-检查CPU架构/操作系统
        labels:
          - StandardHA
      - code: precheck-check-linux-packages
        alias: 预检-检查是否存在已知的冲突软件包
        labels:
          - StandardHA
      - code: precheck-check-master-resources
        alias: 预检-检查主控节点资源是否符合要求
        labels:
          - StandardHA
      - code: precheck-check-system-nodes-resources
        alias: 预检-检查系统节点资源是否符合要求
        labels:
          - StandardHA
      - code: precheck-check-system-nodepool-resources
        alias: 预检-检查系统资源池是否符合要求
        labels:
          - StandardHA
      - code: precheck-check-system-node-vgs
        alias: 预检-检查系统节点VG是否创建
        labels:
          - StandardHA
      - code: sisyphus-system-step-prepare-execution
        alias: 执行部署平台初始化步骤
        labels:
          - MinimizeHA
      - code: precheck-check-os
        alias: 预检-检查CPU架构/操作系统
        labels:
          - MinimizeHA
      - code: precheck-check-linux-packages
        alias: 预检-检查是否存在已知的冲突软件包
        labels:
          - MinimizeHA
      - code: precheck-check-master-resources
        alias: 预检-检查主控节点资源是否符合要求
        labels:
          - MinimizeHA
      - code: precheck-check-system-nodes-resources
        alias: 预检-检查系统节点资源是否符合要求
        labels:
          - MinimizeHA
      - code: precheck-check-system-nodepool-resources
        alias: 预检-检查系统资源池是否符合要求
        labels:
          - MinimizeHA
      - code: precheck-check-system-node-vgs
        alias: 预检-检查系统节点VG是否创建
        labels:
          - MinimizeHA
  - code: cluster-create-group
    alias: 集群创建
    steps:
      - code: edit-hostname
        alias: 修改主机名
        labels:
          - AllInOne
      - code: install-chrony-server
        alias: 安装时间同步服务(服务端)
        labels:
          - AllInOne
      - code: install-chrony-client
        alias: 安装时间同步服务(客户端)
        labels:
          - AllInOne
      - code: install-containerd
        alias: 安装containerd容器运行时
        labels:
          - AllInOne/containerd
      - code: install-docker
        alias: 安装docker容器运行时
        labels:
          - AllInOne/docker
      - code: init-kubernetes-ansible-module
        alias: 初始化kubernetes主控节点依赖
        labels:
          - AllInOne
      - code: create-kubernetes-cluster
        alias: 创建kubernetes集群
        labels:
          - AllInOne
      - code: create-etcd-backup
        alias: 创建etcd备份任务
        labels:
          - AllInOne
      - code: join-kubernetes-node
        alias: 添加kubernetes其它节点
        labels:
          - AllInOne
      - code: allow-master-schedulable
        alias: 允许主控节点可调度
        labels:
          - AllInOne
      - code: label-chrony-server
        alias: 添加内部时间同步服务器节点标签
        labels:
          - AllInOne
      - code: label-sisyphus-initialized-master
        alias: 添加主控节点角色标签(初始化后)
        labels:
          - AllInOne
      - code: label-system-node-pool-nodes
        alias: 系统资源池节点打标签
        labels:
          - AllInOne
      - code: install-node-pool
        alias: 安装资源池
        labels:
          - AllInOne
      - code: init-system-nodepool
        alias: 初始化系统资源池
        labels:
          - AllInOne
      - code: install-calico
        alias: 安装统一网络模型-安装calico(CNI)
        labels:
          - AllInOne/calico
      - code: install-bifrost
        alias: 安装统一网络模型-安装bifrost(CNI)
        labels:
          - AllInOne/macvlan
      - code: install-heimdallr
        alias: 安装统一网络模型-安装heimdallr
        labels:
          - AllInOne
      - code: install-multus
        alias: 安装统一网络模型-安装多网卡
        labels:
          - AllInOne
      - code: restart-coredns-when-single-bifrost
        alias: 重启CoreDNS组件
        labels:
          - AllInOne
      - code: install-route-override-controller
        alias: 安装统一网络模型-安装路由覆写组件
        labels:
          - AllInOne
      - code: install-mystra
        alias: 安装统一网络模型-安装网络隔离
        labels:
          - AllInOne
      - code: install-metrics-server
        alias: 安装基础监控服务
        labels:
          - AllInOne
      - code: install-lvm-csi-plugin
        alias: 安装LVM(本地存储管理服务)
        labels:
          - AllInOne
      - code: ingress-expose-helper
        alias: 安装负载均衡四层对外服务辅助
        labels:
          - AllInOne
      - code: install-nginx-ingress-controller
        alias: 安装默认负载均衡(NginxIngressController)
        labels:
          - AllInOne
      - code: install-prometheus
        alias: 安装Prometheus监控
        labels:
          - AllInOne
      - code: install-monitor-components
        alias: 安装监控探针套件
        labels:
          - AllInOne
      - code: install-grafana
        alias: 安装Grafana监控面板
        labels:
          - AllInOne
      - code: install-log-controller
        alias: 安装日志采集器(log-controller)
        labels:
          - AllInOne
      - code: install-elk
        alias: 安装ELK日志
        labels:
          - AllInOne
      - code: install-kube-eventer
        alias: 安装Kubernetes事件收集器
        labels:
          - AllInOne
      - code: install-pgsql-operator
        alias: 安装postgresql运维控制器
        labels:
          - AllInOne
      - code: install-baseline-checker
        alias: 安装基线检查组件
        labels:
          - AllInOne
      - code: install-gpu-manager
        alias: 安装GPU控制器
        labels:
          - AllInOne
      - code: install-gpu-exporter
        alias: 安装GPU监控
        labels:
          - AllInOne
      - code: install-resources-aggregated-controller
        alias: 安装资源关联控制器
        labels:
          - AllInOne
      - code: install-application-model
        alias: 安装应用模型
        labels:
          - AllInOne
      - code: install-problem-isolation
        alias: 安装故障隔离
        labels:
          - AllInOne
      - code: install-harmonycloud-hpa
        alias: 安装水平扩缩容(HPA)
        labels:
          - AllInOne
      - code: install-velero
        alias: 安装备份工具(velero)
        labels:
          - AllInOne
      - code: install-egress
        alias: 安装出口网关
        labels:
          - AllInOne
      - code: install-iris
        alias: 安装容器网络流量管理组件
        labels:
          - AllInOne
      - code: create-kata-runtime-class
        alias: 创建kata运行时
        labels:
          - AllInOne
      - code: install-kata-container
        alias: 安装kata-container运行时
        labels:
          - AllInOne
      - code: install-mongodb
        alias: 安装mongodb
        labels:
          - AllInOne
      - code: install-sisyphus-cluster-manager
        alias: 安装集群生命周期管理组件
        labels:
          - AllInOne
      - code: install-kube-ovn-cni
        alias: 安装KubeOVN网络插件
        labels:
          - AllInOne/kube-ovn
      - code: install-kubevirt
        alias: 安装KubeVirt虚拟机组件
        labels:
          - AllInOne/kube-ovn
      - code: install-cdi
        alias: 安装CDI组件
        labels:
          - AllInOne/kube-ovn
      - code: configure-log-collect-rules
        alias: 配置日志采集规则
        labels:
          - AllInOne
      - code: configure-baseline-checker-rules
        alias: 配置基线检查规则
        labels:
          - AllInOne
      - code: import-data-from-sisyphus
        alias: 从部署平台中导入数据至目标集群
        labels:
          - AllInOne
      - code: edit-hostname
        alias: 修改主机名
        labels:
          - StandardNoneHA
      - code: install-chrony-server
        alias: 安装时间同步服务(服务端)
        labels:
          - StandardNoneHA
      - code: install-chrony-client
        alias: 安装时间同步服务(客户端)
        labels:
          - StandardNoneHA
      - code: install-containerd
        alias: 安装containerd容器运行时
        labels:
          - StandardNoneHA/containerd
      - code: install-docker
        alias: 安装docker容器运行时
        labels:
          - StandardNoneHA/docker
      - code: init-kubernetes-ansible-module
        alias: 初始化kubernetes主控节点依赖
        labels:
          - StandardNoneHA
      - code: create-kubernetes-cluster
        alias: 创建kubernetes集群
        labels:
          - StandardNoneHA
      - code: create-etcd-backup
        alias: 创建etcd备份任务
        labels:
          - StandardNoneHA
      - code: join-kubernetes-node
        alias: 添加kubernetes其它节点
        labels:
          - StandardNoneHA
      - code: label-chrony-server
        alias: 添加内部时间同步服务器节点标签
        labels:
          - StandardNoneHA
      - code: label-sisyphus-initialized-master
        alias: 添加主控节点角色标签(初始化后)
        labels:
          - StandardNoneHA
      - code: label-system-node-pool-nodes
        alias: 系统资源池节点打标签
        labels:
          - StandardNoneHA
      - code: install-node-pool
        alias: 安装资源池
        labels:
          - StandardNoneHA
      - code: init-system-nodepool
        alias: 初始化系统资源池
        labels:
          - StandardNoneHA
      - code: install-calico
        alias: 安装统一网络模型-安装calico(CNI)
        labels:
          - StandardNoneHA/calico
      - code: install-bifrost
        alias: 安装统一网络模型-安装bifrost(CNI)
        labels:
          - StandardNoneHA/macvlan
      - code: install-heimdallr
        alias: 安装统一网络模型-安装heimdallr
        labels:
          - StandardNoneHA
      - code: install-multus
        alias: 安装统一网络模型-安装多网卡
        labels:
          - StandardNoneHA
      - code: restart-coredns-when-single-bifrost
        alias: 重启CoreDNS组件
        labels:
          - StandardNoneHA
      - code: install-route-override-controller
        alias: 安装统一网络模型-安装路由覆写组件
        labels:
          - StandardNoneHA
      - code: install-mystra
        alias: 安装统一网络模型-安装网络隔离
        labels:
          - StandardNoneHA
      - code: install-metrics-server
        alias: 安装基础监控服务
        labels:
          - StandardNoneHA
      - code: install-lvm-csi-plugin
        alias: 安装LVM(本地存储管理服务)
        labels:
          - StandardNoneHA
      - code: ingress-expose-helper
        alias: 安装负载均衡四层对外服务辅助
        labels:
          - StandardNoneHA
      - code: install-nginx-ingress-controller
        alias: 安装默认负载均衡(NginxIngressController)
        labels:
          - StandardNoneHA
      - code: install-prometheus
        alias: 安装Prometheus监控
        labels:
          - StandardNoneHA
      - code: install-monitor-components
        alias: 安装监控探针套件
        labels:
          - StandardNoneHA
      - code: install-grafana
        alias: 安装Grafana监控面板
        labels:
          - StandardNoneHA
      - code: install-log-controller
        alias: 安装日志采集器(log-controller)
        labels:
          - StandardNoneHA
      - code: install-elk
        alias: 安装ELK日志
        labels:
          - StandardNoneHA
      - code: install-kube-eventer
        alias: 安装Kubernetes事件收集器
        labels:
          - StandardNoneHA
      - code: install-pgsql-operator
        alias: 安装postgresql运维控制器
        labels:
          - StandardNoneHA
      - code: install-baseline-checker
        alias: 安装基线检查组件
        labels:
          - StandardNoneHA
      - code: install-gpu-manager
        alias: 安装GPU控制器
        labels:
          - StandardNoneHA
      - code: install-gpu-exporter
        alias: 安装GPU监控
        labels:
          - StandardNoneHA
      - code: install-resources-aggregated-controller
        alias: 安装资源关联控制器
        labels:
          - StandardNoneHA
      - code: install-application-model
        alias: 安装应用模型
        labels:
          - StandardNoneHA
      - code: install-problem-isolation
        alias: 安装故障隔离
        labels:
          - StandardNoneHA
      - code: install-harmonycloud-hpa
        alias: 安装水平扩缩容(HPA)
        labels:
          - StandardNoneHA
      - code: install-velero
        alias: 安装备份工具(velero)
        labels:
          - StandardNoneHA
      - code: install-egress
        alias: 安装出口网关
        labels:
          - StandardNoneHA
      - code: install-iris
        alias: 安装容器网络流量管理组件
        labels:
          - StandardNoneHA
      - code: create-kata-runtime-class
        alias: 创建kata运行时
        labels:
          - StandardNoneHA
      - code: install-kata-container
        alias: 安装kata-container运行时
        labels:
          - StandardNoneHA
      - code: install-mongodb
        alias: 安装mongodb
        labels:
          - StandardNoneHA
      - code: install-sisyphus-cluster-manager
        alias: 安装集群生命周期管理组件
        labels:
          - StandardNoneHA
      - code: install-kube-ovn-cni
        alias: 安装KubeOVN网络插件
        labels:
          - StandardNoneHA/kube-ovn
      - code: install-kubevirt
        alias: 安装KubeVirt虚拟机组件
        labels:
          - StandardNoneHA/kube-ovn
      - code: install-cdi
        alias: 安装CDI组件
        labels:
          - StandardNoneHA/kube-ovn
      - code: configure-log-collect-rules
        alias: 配置日志采集规则
        labels:
          - StandardNoneHA
      - code: configure-baseline-checker-rules
        alias: 配置基线检查规则
        labels:
          - StandardNoneHA
      - code: import-data-from-sisyphus
        alias: 从部署平台中导入数据至目标集群
        labels:
          - StandardNoneHA
      - code: edit-hostname
        alias: 修改主机名
        labels:
          - StandardHA
      - code: install-chrony-server
        alias: 安装时间同步服务(服务端)
        labels:
          - StandardHA
      - code: install-chrony-client
        alias: 安装时间同步服务(客户端)
        labels:
          - StandardHA
      - code: install-haproxy
        alias: 安装APIserver负载均衡(HAProxy)
        labels:
          - StandardHA
      - code: install-haproxy-keepalived
        alias: 安装APIserver负载均衡VIP
        labels:
          - StandardHA
      - code: install-containerd
        alias: 安装containerd容器运行时
        labels:
          - StandardHA/containerd
      - code: install-docker
        alias: 安装docker容器运行时
        labels:
          - StandardHA/docker
      - code: init-kubernetes-ansible-module
        alias: 初始化kubernetes主控节点依赖
        labels:
          - StandardHA
      - code: create-kubernetes-cluster
        alias: 创建kubernetes集群
        labels:
          - StandardHA
      - code: join-kubernetes-master
        alias: 添加kubernetes主控节点
        labels:
          - StandardHA
      - code: create-etcd-backup
        alias: 创建etcd备份任务
        labels:
          - StandardHA
      - code: join-kubernetes-node
        alias: 添加kubernetes其它节点
        labels:
          - StandardHA
      - code: label-chrony-server
        alias: 添加内部时间同步服务器节点标签
        labels:
          - StandardHA
      - code: label-sisyphus-initialized-master
        alias: 添加主控节点角色标签(初始化后)
        labels:
          - StandardHA
      - code: label-system-node-pool-nodes
        alias: 系统资源池节点打标签
        labels:
          - StandardHA
      - code: install-node-pool
        alias: 安装资源池
        labels:
          - StandardHA
      - code: init-system-nodepool
        alias: 初始化系统资源池
        labels:
          - StandardHA
      - code: install-calico
        alias: 安装统一网络模型-安装calico(CNI)
        labels:
          - StandardHA/calico
      - code: install-bifrost
        alias: 安装统一网络模型-安装bifrost(CNI)
        labels:
          - StandardHA/macvlan
      - code: install-heimdallr
        alias: 安装统一网络模型-安装heimdallr
        labels:
          - StandardHA
      - code: install-multus
        alias: 安装统一网络模型-安装多网卡
        labels:
          - StandardHA
      - code: restart-coredns-when-single-bifrost
        alias: 重启CoreDNS组件
        labels:
          - StandardHA
      - code: install-route-override-controller
        alias: 安装统一网络模型-安装路由覆写组件
        labels:
          - StandardHA
      - code: install-mystra
        alias: 安装统一网络模型-安装网络隔离
        labels:
          - StandardHA
      - code: install-metrics-server
        alias: 安装基础监控服务
        labels:
          - StandardHA
      - code: install-lvm-csi-plugin
        alias: 安装LVM(本地存储管理服务)
        labels:
          - StandardHA
      - code: install-ingress-keepalived
        alias: 安装ingressVip
        labels:
          - StandardHA
      - code: ingress-expose-helper
        alias: 安装负载均衡四层对外服务辅助
        labels:
          - StandardHA
      - code: install-nginx-ingress-controller
        alias: 安装默认负载均衡(NginxIngressController)
        labels:
          - StandardHA
      - code: install-prometheus
        alias: 安装Prometheus监控
        labels:
          - StandardHA
      - code: install-monitor-components
        alias: 安装监控探针套件
        labels:
          - StandardHA
      - code: install-grafana
        alias: 安装Grafana监控面板
        labels:
          - StandardHA
      - code: install-log-controller
        alias: 安装日志采集器(log-controller)
        labels:
          - StandardHA
      - code: install-elk
        alias: 安装ELK日志
        labels:
          - StandardHA
      - code: install-kube-eventer
        alias: 安装Kubernetes事件收集器
        labels:
          - StandardHA
      - code: install-pgsql-operator
        alias: 安装postgresql运维控制器
        labels:
          - StandardHA
      - code: install-baseline-checker
        alias: 安装基线检查组件
        labels:
          - StandardHA
      - code: install-gpu-manager
        alias: 安装GPU控制器
        labels:
          - StandardHA
      - code: install-gpu-exporter
        alias: 安装GPU监控
        labels:
          - StandardHA
      - code: install-resources-aggregated-controller
        alias: 安装资源关联控制器
        labels:
          - StandardHA
      - code: install-application-model
        alias: 安装应用模型
        labels:
          - StandardHA
      - code: install-problem-isolation
        alias: 安装故障隔离
        labels:
          - StandardHA
      - code: install-harmonycloud-hpa
        alias: 安装水平扩缩容(HPA)
        labels:
          - StandardHA
      - code: install-velero
        alias: 安装备份工具(velero)
        labels:
          - StandardHA
      - code: install-egress
        alias: 安装出口网关
        labels:
          - StandardHA
      - code: install-iris
        alias: 安装容器网络流量管理组件
        labels:
          - StandardHA
      - code: create-kata-runtime-class
        alias: 创建kata运行时
        labels:
          - StandardHA
      - code: install-kata-container
        alias: 安装kata-container运行时
        labels:
          - StandardHA
      - code: install-mongodb
        alias: 安装mongodb
        labels:
          - StandardHA
      - code: install-sisyphus-cluster-manager
        alias: 安装集群生命周期管理组件
        labels:
          - StandardHA
      - code: install-kube-ovn-cni
        alias: 安装KubeOVN网络插件
        labels:
          - StandardHA/kube-ovn
      - code: install-kubevirt
        alias: 安装KubeVirt虚拟机组件
        labels:
          - StandardHA/kube-ovn
      - code: install-cdi
        alias: 安装CDI组件
        labels:
          - StandardHA/kube-ovn
      - code: configure-log-collect-rules
        alias: 配置日志采集规则
        labels:
          - StandardHA
      - code: configure-baseline-checker-rules
        alias: 配置基线检查规则
        labels:
          - StandardHA
      - code: import-data-from-sisyphus
        alias: 从部署平台中导入数据至目标集群
        labels:
          - StandardHA
      - code: edit-hostname
        alias: 修改主机名
        labels:
          - MinimizeHA
      - code: install-chrony-server
        alias: 安装时间同步服务(服务端)
        labels:
          - MinimizeHA
      - code: install-chrony-client
        alias: 安装时间同步服务(客户端)
        labels:
          - MinimizeHA
      - code: install-haproxy
        alias: 安装APIserver负载均衡(HAProxy)
        labels:
          - MinimizeHA
      - code: install-mix-keepalived
        alias: 安装混合负载均衡
        labels:
          - MinimizeHA
      - code: install-containerd
        alias: 安装containerd容器运行时
        labels:
          - MinimizeHA/containerd
      - code: install-docker
        alias: 安装docker容器运行时
        labels:
          - MinimizeHA/docker
      - code: init-kubernetes-ansible-module
        alias: 初始化kubernetes主控节点依赖
        labels:
          - MinimizeHA
      - code: create-kubernetes-cluster
        alias: 创建kubernetes集群
        labels:
          - MinimizeHA
      - code: join-kubernetes-master
        alias: 添加kubernetes主控节点
        labels:
          - MinimizeHA
      - code: create-etcd-backup
        alias: 创建etcd备份任务
        labels:
          - MinimizeHA
      - code: join-kubernetes-node
        alias: 添加kubernetes其它节点
        labels:
          - MinimizeHA
      - code: allow-master-schedulable
        alias: 允许主控节点可调度
        labels:
          - MinimizeHA
      - code: label-chrony-server
        alias: 添加内部时间同步服务器节点标签
        labels:
          - MinimizeHA
      - code: label-sisyphus-initialized-master
        alias: 添加主控节点角色标签(初始化后)
        labels:
          - MinimizeHA
      - code: label-system-node-pool-nodes
        alias: 系统资源池节点打标签
        labels:
          - MinimizeHA
      - code: install-node-pool
        alias: 安装资源池
        labels:
          - MinimizeHA
      - code: init-system-nodepool
        alias: 初始化系统资源池
        labels:
          - MinimizeHA
      - code: install-calico
        alias: 安装统一网络模型-安装calico(CNI)
        labels:
          - MinimizeHA/calico
      - code: install-bifrost
        alias: 安装统一网络模型-安装bifrost(CNI)
        labels:
          - MinimizeHA/macvlan
      - code: install-heimdallr
        alias: 安装统一网络模型-安装heimdallr
        labels:
          - MinimizeHA
      - code: install-multus
        alias: 安装统一网络模型-安装多网卡
        labels:
          - MinimizeHA
      - code: restart-coredns-when-single-bifrost
        alias: 重启CoreDNS组件
        labels:
          - MinimizeHA
      - code: install-route-override-controller
        alias: 安装统一网络模型-安装路由覆写组件
        labels:
          - MinimizeHA
      - code: install-mystra
        alias: 安装统一网络模型-安装网络隔离
        labels:
          - MinimizeHA
      - code: install-metrics-server
        alias: 安装基础监控服务
        labels:
          - MinimizeHA
      - code: install-lvm-csi-plugin
        alias: 安装LVM(本地存储管理服务)
        labels:
          - MinimizeHA
      - code: ingress-expose-helper
        alias: 安装负载均衡四层对外服务辅助
        labels:
          - MinimizeHA
      - code: install-nginx-ingress-controller
        alias: 安装默认负载均衡(NginxIngressController)
        labels:
          - MinimizeHA
      - code: install-prometheus
        alias: 安装Prometheus监控
        labels:
          - MinimizeHA
      - code: install-monitor-components
        alias: 安装监控探针套件
        labels:
          - MinimizeHA
      - code: install-grafana
        alias: 安装Grafana监控面板
        labels:
          - MinimizeHA
      - code: install-log-controller
        alias: 安装日志采集器(log-controller)
        labels:
          - MinimizeHA
      - code: install-elk
        alias: 安装ELK日志
        labels:
          - MinimizeHA
      - code: install-kube-eventer
        alias: 安装Kubernetes事件收集器
        labels:
          - MinimizeHA
      - code: install-pgsql-operator
        alias: 安装postgresql运维控制器
        labels:
          - MinimizeHA
      - code: install-baseline-checker
        alias: 安装基线检查组件
        labels:
          - MinimizeHA
      - code: install-gpu-manager
        alias: 安装GPU控制器
        labels:
          - MinimizeHA
      - code: install-gpu-exporter
        alias: 安装GPU监控
        labels:
          - MinimizeHA
      - code: install-resources-aggregated-controller
        alias: 安装资源关联控制器
        labels:
          - MinimizeHA
      - code: install-application-model
        alias: 安装应用模型
        labels:
          - MinimizeHA
      - code: install-problem-isolation
        alias: 安装故障隔离
        labels:
          - MinimizeHA
      - code: install-harmonycloud-hpa
        alias: 安装水平扩缩容(HPA)
        labels:
          - MinimizeHA
      - code: install-velero
        alias: 安装备份工具(velero)
        labels:
          - MinimizeHA
      - code: install-egress
        alias: 安装出口网关
        labels:
          - MinimizeHA
      - code: install-iris
        alias: 安装容器网络流量管理组件
        labels:
          - MinimizeHA
      - code: create-kata-runtime-class
        alias: 创建kata运行时
        labels:
          - MinimizeHA
      - code: install-kata-container
        alias: 安装kata-container运行时
        labels:
          - MinimizeHA
      - code: install-mongodb
        alias: 安装mongodb
        labels:
          - MinimizeHA
      - code: install-sisyphus-cluster-manager
        alias: 安装集群生命周期管理组件
        labels:
          - MinimizeHA
      - code: install-kube-ovn-cni
        alias: 安装KubeOVN网络插件
        labels:
          - MinimizeHA/kube-ovn
      - code: install-kubevirt
        alias: 安装KubeVirt虚拟机组件
        labels:
          - MinimizeHA/kube-ovn
      - code: install-cdi
        alias: 安装CDI组件
        labels:
          - MinimizeHA/kube-ovn
      - code: configure-log-collect-rules
        alias: 配置日志采集规则
        labels:
          - MinimizeHA
      - code: configure-baseline-checker-rules
        alias: 配置基线检查规则
        labels:
          - MinimizeHA
      - code: import-data-from-sisyphus
        alias: 从部署平台中导入数据至目标集群
        labels:
          - MinimizeHA
clusterManageGroup:
  code: add-cluster-group
  alias: 集群纳管
  steps:
    - code: install-stellaris-proxy
      alias: 集群纳管
groupKeyDefaultLb: 默认负载均衡节点
nodeTypeGroup:
  allInOne:
    first:
      - 主控节点(常用master)
      - 默认负载均衡节点
      - 系统节点
      - Prometheus
      - ELK-ES
    other:
      - 工作节点
  standardNoneHA:
    first:
      - 主控节点(常用master)
    second:
      - 系统节点
      - 默认负载均衡节点
    third:
      - 系统节点
      - Prometheus
    fourth:
      - 系统节点
      - ELK-ES
    other:
      - 工作节点
  standardHA:
    first:
      - 主控节点(常用master)
    second:
      - 主控节点
    third:
      - 主控节点
    fourth:
      - 系统节点
      - Prometheus
      - Apiserver负载均衡节点
    fifth:
      - 系统节点
      - Prometheus
      - Apiserver负载均衡节点
      - ELK-ES
    sixth:
      - 系统节点
      - ELK-ES
      - 默认负载均衡节点
    seventh:
      - 系统节点
      - ELK-ES
      - 默认负载均衡节点
    other:
      - 工作节点
  minimizeHA:
    first:
      - 系统节点
      - ELK-ES
      - 主控节点(常用master)
    second:
      - 系统节点
      - ELK-ES
      - 混合负载均衡节点
      - Prometheus
      - 主控节点
    third:
      - 系统节点
      - ELK-ES
      - 混合负载均衡节点
      - Prometheus
      - 主控节点
    other:
      - 工作节点
  kubeOvnPlugin:
    addInFirst:
      matchProxy: anyMatch
      match:
        - 系统节点
      add:
        - ovn网络插件数据库
initial:
  groupCode: data-initial
  groupAlias: 信息提交
  nodeInitialCode: olympus-node-initial
  nodeInitialAlias: 节点信息提交及联通性检查
  sisyphusSolutionApplyCode: olympus-apply-sisyphus-solution
  sisyphusSolutionApplyAlias: 集群信息提交
afterAddCluster:
  code: after-add-cluster
  alias: 集群组件安装
  steps:
    - code: install-addons
      alias: 创建平台插件元数据
