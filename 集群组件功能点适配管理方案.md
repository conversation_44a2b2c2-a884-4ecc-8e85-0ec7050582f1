| 版本 | 作者 | 说明 |
| :--- | :--- | :--- |
| v1 | 韩诸浩琦 | 初始化文档 |
| v2 | 韩诸浩琦 | 补充适配未安装组件第三方集群适配详细说明 |
| v3 | 韩诸浩琦 | 新增EnhanceClusterAddon CRD  |
| v4 | 韩诸浩琦 | 新增clusterAddon同步EnhanceClusterAddon 逻辑<br/>完善消息枚举 |


# 背景
目前随着产品的不断迭代，产品功能所依赖底座的功能组件已经越来越多，开发时对于组件的调用并没有进行严格判断，导致依赖关系错综复杂。

目前需要平台支持纳管没有安装底座组件的原生集群或者rancher集群，一旦某个组件没有安装或者异常，将会导致核心功能失效，同时没有相对应的提示，覆盖范围也不广。

此次改造的要求

- [ ] 平台支持部署在原生集群
- [ ] k8s基础功能
- [ ] 用户管理
- [ ] 组织管理
- [ ] 权限管理
- [ ] 配额管理 - 虚拟一个默认资源池
- [ ] 纳管第三方集群，后端根据接入组件提供能力
- [ ] 提升: 根据对应集群组件安装状态展示功能菜单。

# 组件整理
集群组件

**截止门户 3.6.0 版本**

| **<font style="background-color:rgb(239, 240, 240);">组件</font>** | **<font style="background-color:rgb(239, 240, 240);">是否内置</font>** | **<font style="background-color:rgb(239, 240, 240);">说明</font>** |
| :--- | :--- | :--- |
| **<font style="background-color:rgb(206, 245, 247);">controller-manager</font>** | <font style="background-color:rgb(206, 245, 247);">component 内置</font> | <font style="background-color:rgb(206, 245, 247);">资源控制器，负责管理kubernetes的内部资源的生命周期。</font> |
| **<font style="background-color:rgb(206, 245, 247);">apiserver</font>** | <font style="background-color:rgb(206, 245, 247);">component 内置</font> | <font style="background-color:rgb(206, 245, 247);">集群API服务，kubernetes 资源的服务暴露的东西。</font> |
| **<font style="background-color:rgb(206, 245, 247);">scheduler</font>** | <font style="background-color:rgb(206, 245, 247);">component 内置</font> | <font style="background-color:rgb(206, 245, 247);">调度器，负责具体的pod的</font> |
| **<font style="background-color:rgb(232, 247, 207);">gpu</font>** | <font style="background-color:rgb(232, 247, 207);">in-tree 非内置</font> | <font style="background-color:rgb(232, 247, 207);">gpu组件，提供gpu能力</font> |
| **<font style="background-color:rgb(232, 247, 207);">kubevirt</font>** | <font style="background-color:rgb(232, 247, 207);">in-tree 非内置</font> | <font style="background-color:rgb(232, 247, 207);">虚拟机组件，提供集群创建虚拟机的能力</font> |
| **<font style="background-color:rgb(232, 247, 207);">coredns</font>** | <font style="background-color:rgb(232, 247, 207);">in-tree 非内置</font> | <font style="background-color:rgb(232, 247, 207);">集群内部域名解析器，网络组件，负责容器网络的域名解析。</font> |
| **<font style="background-color:rgb(232, 247, 207);">elk</font>** | <font style="background-color:rgb(232, 247, 207);">in-tree 非内置</font> | <font style="background-color:rgb(232, 247, 207);">采集日志，日志组件，负责采集pod容器的日志，支持日志文件和标准输出日志的持久化采集和查询。</font> |
| **<font style="background-color:rgb(232, 247, 207);">app-model</font>** | <font style="background-color:rgb(232, 247, 207);">in-tree 非内置</font> | <font style="background-color:rgb(232, 247, 207);">应用模型，容器服务应用发布组件，负责发布应用、管理应用生命周期。</font> |
| **<font style="background-color:rgb(232, 247, 207);">acl</font>** | <font style="background-color:rgb(232, 247, 207);">in-tree 非内置</font> | <font style="background-color:rgb(232, 247, 207);">网络隔离组件，提供容器网络间自定义隔离</font> |
| **<font style="background-color:rgb(232, 247, 207);">heimdallr</font>** | <font style="background-color:rgb(232, 247, 207);">in-tree 非内置</font> | <font style="background-color:rgb(232, 247, 207);">统一网络模型</font> |
| **<font style="background-color:rgb(232, 247, 207);">hpa</font>** | <font style="background-color:rgb(232, 247, 207);">in-tree 非内置</font> | <font style="background-color:rgb(232, 247, 207);">扩缩容</font> |
| **<font style="background-color:rgb(232, 247, 207);">calico</font>** | <font style="background-color:rgb(232, 247, 207);">in-tree 非内置</font> | <font style="background-color:rgb(232, 247, 207);">calico容器网络</font> |
| **<font style="background-color:rgb(232, 247, 207);">monitoring</font>** | <font style="background-color:rgb(232, 247, 207);">in-tree 非内置</font> | <font style="background-color:rgb(232, 247, 207);">监控</font> |
| **<font style="background-color:rgb(232, 247, 207);">node-pool</font>** | <font style="background-color:rgb(232, 247, 207);">in-tree 非内置</font> | <font style="background-color:rgb(232, 247, 207);">资源池</font> |
| **<font style="background-color:rgb(232, 247, 207);">ovn</font>** | <font style="background-color:rgb(232, 247, 207);">in-tree 非内置</font> | <font style="background-color:rgb(232, 247, 207);">kubeovn容器网络</font> |
| **<font style="background-color:rgb(232, 247, 207);">problem-isolation</font>** | <font style="background-color:rgb(232, 247, 207);">in-tree 非内置</font> | <font style="background-color:rgb(232, 247, 207);">故障隔离</font> |
| **<font style="background-color:rgb(232, 247, 207);">resource-aggregate</font>** | <font style="background-color:rgb(232, 247, 207);">in-tree 非内置</font> | <font style="background-color:rgb(232, 247, 207);">资源关联控制器</font> |
| **<font style="background-color:rgb(232, 247, 207);">app-decompile</font>** | <font style="background-color:rgb(232, 247, 207);">in-tree 非内置</font> | <font style="background-color:rgb(232, 247, 207);">应用反编译，应用渲染到kubernetes资源</font> |
| **<font style="background-color:rgb(232, 247, 207);">sisyphus</font>** | <font style="background-color:rgb(232, 247, 207);">in-tree 非内置</font> | <font style="background-color:rgb(232, 247, 207);">集群管理，部署平台</font> |
| **<font style="background-color:rgb(232, 247, 207);">velero</font>** | <font style="background-color:rgb(232, 247, 207);">in-tree 非内置</font> | <font style="background-color:rgb(232, 247, 207);">集群备份</font> |
| **<font style="background-color:rgb(232, 247, 207);">baseline-checker</font>** | <font style="background-color:rgb(232, 247, 207);">in-tree 非内置</font> | <font style="background-color:rgb(232, 247, 207);">基线检查</font> |


改造方法

# 详细设计
前提条件，大的架构目前无法轻易改动，因此该方案追求不改动原有结构，使用底座`stellaris`提供的多集群组件能力进行改造。

原有组件接口，目前只有对应的组件是否正常的功能，无法对某些功能进行细化

`http://*************/olympus-core/clusters/components`

返回结构如下，status为0表示组件正常，但是需要每次前端手动去判断。

```json
{
  "code": 0,
  "errorMsg": null,
  "errorDetail": null,
  "success": true,
  "data": {
    "cluster-187": [
      {
        "component": "controller-manager",
        "componentName": null,
        "status": 0
      },
      {
        "component": "coredns",
        "componentName": "域名解析",
        "status": 0
      },
      {
        "component": "resource-aggregate",
        "componentName": "资源关联控制器",
        "status": 0
      },
      {
        "component": "sisyphus",
        "componentName": "节点上下线",
        "status": 0
      },
      {
        "component": "baseline-checker",
        "componentName": null,
        "status": 0
      },
      {
        "component": "elk",
        "componentName": "日志采集",
        "status": 0
      },
      {
        "component": "node-pool",
        "componentName": "节点资源池",
        "status": 0
      },
      {
        "component": "ovn",
        "componentName": "虚拟机网络",
        "status": 0
      },
      {
        "component": "ControllerManagerUnhealthy",
        "componentName": "K8S控制管理器",
        "status": 1
      },
      {
        "component": "app-decompile",
        "componentName": "反编译",
        "status": 0
      },
      {
        "component": "acl",
        "componentName": "网络隔离",
        "status": 0
      },
      {
        "component": "monitoring",
        "componentName": "监控告警",
        "status": 1
      },
      {
        "component": "gpu",
        "componentName": "GPU组件",
        "status": 0
      },
      {
        "component": "scheduler",
        "componentName": null,
        "status": 0
      },
      {
        "component": "app-model",
        "componentName": "应用模型",
        "status": 0
      },
      {
        "component": "heimdallr",
        "componentName": "统一网络模型",
        "status": 0
      },
      {
        "component": "hpa",
        "componentName": "水平扩缩容",
        "status": 0
      },
      {
        "component": "velero",
        "componentName": null,
        "status": 0
      },
      {
        "component": "kubevirt",
        "componentName": null,
        "status": 0
      },
      {
        "component": "problem-isolation",
        "componentName": "故障隔离",
        "status": 0
      },
      {
        "component": "calico",
        "componentName": "Calico网络",
                "status": 0
            },
            {
                "component": "apiserver",
                "componentName": null,
                "status": 0
            },
            {
                "component": "etcd",
                "componentName": null,
                "status": 0
            }
        ]
    },
    "count": null,
    "errorStack": null
}
```

而且之前约定

`clusteraddon.spec.configurations.schema`由平台控制，因此可以在这里添加对应的特性开关和门控。

也可考虑直接单独抽成一个crd 名称为 `EhanceClusterAddon`

```yaml
apiVersion: unified-platform.harmonycloud.cn/v1alpha1
kind: EnhanceClusterAddon
metadata:
  name: elk
  namespace: stellaris-workspace-cluster161
spec:
  addonRef:
    name: elk
    namespace: stellaris-workspace-cluster161
  builtinFeatures: 
  - Installed
  - Ready
  features:
    - name: ElasticsearchReady
#    - name: ElasticsearchClusterHealthy
      description: check elasticsearch status
      healthCheckInterval: 10s
      healthChecks:
        - name: check elasticsearch status
          collector:
            http:
              # template 表示从原始addon中获取
              urlTemplate: '{{- $cfg := .status.configurations.configurationSchemaData.elk }}{{ printf "%s://%s:%s/_cluster/health" $cfg.protocol $cfg.ip $cfg.port }}'
              # template 表示从原始addon中获取esName 和 esPwd
              authorizationTemplate: '{{ $name := .status.configurations.configurationSchemaData.elk.esName }}{{ $pwd := .status.configurations.configurationSchemaData.elk.esPassword }}{{ printf "Basic %s" (printf "%s:%s" $name $pwd | base64Encode) }}'
              method: GET
              timeout: 5s
          filters:
            - jsonpath:
                expression: "{.status}"
          matchers:
             - matchExpressions:
                - operator: In
                  values:
                    - green
                    - yellow
    - name: ElasticsearchEnableBackup
      description: check elasticsearch backup configuration
      healthCheckInterval: 10s
      healthChecks:
        - name: check elasticsearch support fs backup
          logicOperator: OR
          collector:
            http:
              # template 表示从原始addon中获取
              urlTemplate: '{{- $cfg := .status.configurations.configurationSchemaData.elk }}{{ printf "%s://%s:%s/_cluster/settings?include_defaults=true" $cfg.protocol $cfg.ip $cfg.port }}'
              # template 表示从原始addon中获取esName 和 esPwd
              authorizationTemplate: '{{ $name := .status.configurations.configurationSchemaData.elk.esName }}{{ $pwd := .status.configurations.configurationSchemaData.elk.esPassword }}{{ printf "Basic %s" (printf "%s:%s" $name $pwd | base64Encode) }}'
              method: GET
              timeout: 5s
          filters:
            # 按照顺序处理值 上一个filter的值作为下一个filter的输入
            - goTemplate:
                # 获取 defaults.path.repo 的长度
                template: "{{ len .defaults.path.repo }}"
          matchers:
            # 要求defaults.path.repo 长度大于 0
            - matchExpressions:
                - operator: Gt
                  values:
                    - "0"
        - name: check elasticsearch support s3 backup
          logicOperator: OR
          collector:
            http:
              urlTemplate: '{{- $cfg := .status.configurations.configurationSchemaData.elk }}{{ printf "%s://%s:%s/_cat/plugins" $cfg.protocol $cfg.ip $cfg.port }}'
              authorizationTemplate: '{{ $name := .status.configurations.configurationSchemaData.elk.esName }}{{ $pwd := .status.configurations.configurationSchemaData.elk.esPassword }}{{ printf "Basic %s" (printf "%s:%s" $name $pwd | base64Encode) }}'
              method: GET
              timeout: 5s
          filters:
            - regex:
                captureGroupIndex: 1
                regex: ".*(repository-s3).*"
          matchers:
            # 要求匹配到 repository-s3
            - matchExpressions:
                - operator: Equal
                  values:
                    - "repository-s3"

```

## 组件能力
<font style="color:rgb(223, 42, 63);">预置能力，不需要特别设置</font>

**<font style="color:rgb(223, 42, 63);">installed 表示已安装</font>**

**<font style="color:rgb(223, 42, 63);">ready 表示组件已就绪</font>**

已 `elk`日志组件和 `hpa`扩缩容组件举例

+ elk

```yaml
features:
- "enableBackup"
```

+ `hpa`

```yaml
features: 
- "appAutoScale"
```

`features`特性能力根据对应前端的菜单、tab 进行梳理，同时通过`controller`按照一定的频率监听更新特性门控的状态。

## 集群组件特性功能接口
原有结构

```json
{
  "clusterName": "cluster-187",
  "labels": "caas-infra-baseline=3.0.3,unified-platform.harmonycloud.cn/api-server-status=success,unified-platform.harmonycloud.cn/hub-cluster=true",
  "description": "cluster-187 is good",
  "type": "",
  "controlState": "controlled",
  "status": "online",
  "apiServerStatus": "success",
  "nodeCount": 8,
  "k8sVersion": "v1.27.10",
  "structure": [
    "AMD64"
  ],
  "networkType": [
    "Calico-v3.23.5"
  ],
  "CNITypes": null,
  "createTime": "2025-03-20T16:42:05+08:00",
  "hasAutoStorageNode": false,
  "baseline": "3.0.3",
  "prole": [
    "hub",
    "business"
  ],
  "cri": ""
}
```

之后添加一个 类似`addonEnableFeatures`字段表示支持启用的功能

```json
{
  // 集群组件开启的功能
  "addonEnableFeatures": [
    // elk是否已经安装
    "elk/installed",
    // elk是否就绪
    "elk/ready",
    // 开启日志备份
    "elk/features.enableBacup",
    // hpa开启日志扩缩容
    "hpa/features.appAutoScale"
  ]
}
```



### 获取所有集群组件的能力
#### 请求
```http
GET http://{host}/olympus-portal/apis/v1/addons/features HTTP/1.1
```

#### 响应
```json
{
    "cluster-187": {
        "enabled": [
            "elk/Installed",
            "elk/Ready",
            "elk/ElasticsearchReady"
        ],
        "disabled": [
            "elk/ElasticsearchEnbaleBackup"
        ]
    }
}
```

#### 示例
```shell
export TOKEN="xxx"
export HOST="***********"
curl -XGET -H "Authorization: ${TOKEN}" "http://${HOST}/olympus-portal/apis/v1/addons/features"
```

### 获取某集群的组件能力
#### 请求
```http
GET http://{host}/olympus-portal/apis/v1/addons/clusters/:cluster/features HTTP/1.1
```

#### 参数
| 名称 | 位置 | 类型 | 说明 |
| --- | --- | --- | --- |
| cluster | path | string | 集群名称 |


#### 响应
```json
{
    "enabled": [
        "elk/Installed",
        "elk/Ready",
        "elk/ElasticsearchReady",
        "monitoring/AlertManagerReady",
        "monitoring/PrometheusReady"
    ],
    "disabled": [
        "elk/ElasticsearchEnableBackup"
    ]
}
```

#### 示例
```shell
export TOKEN="xxx"
export HOST="***********"
curl -XGET -H "Authorization: ${TOKEN}" "http://${HOST}/olympus-portal/apis/v1/addons/clusters/cluster-57/features"
```

### 获取某集群某组件的能力列表
#### 请求
```http
GET http://{host}/olympus-portal/apis/v1/addons/clusters/:cluster/components/:component/features HTTP/1.1
```

#### 参数
| 名称 | 位置 | 类型 | 说明 |
| --- | --- | --- | --- |
| cluster | path | string | 集群名称 |
| component | path | string | 组件名称 |


#### 响应
```json
{
    "enabled": [
        "elk/Installed",
        "elk/Ready",
        "elk/ElasticsearchReady",
        "monitoring/AlertManagerReady",
        "monitoring/PrometheusReady"
    ],
    "disabled": [
        "elk/ElasticsearchEnableBackup"
    ]
}
```

#### 示例
```shell
export TOKEN="xxx"
export HOST="***********"
curl -XGET -H "Authorization: ${TOKEN}" "http://${HOST}/olympus-portal/apis/v1/addons/clusters/cluster-57/components/elk/features"
```

### 获取不可用组件功能特性提示
#### 请求
```http
GET http://{host}/olympus-portal/apis/v1/addons/features/messages HTTP/1.1
```

#### 参数
| 名称 | 位置 | 类型 | 说明 |
| --- | --- | --- | --- |
| features | query | string | 禁用的features列表 |


#### 响应
```json
[
  {
    "feature": "elk/Installed",
    "message": "集群组件未安装"
  },
  {
    "feature": "elk/Ready",
    "message": "集群日志组件未就绪"
  },
  {
    "feature": "elk/ElasticsearchReady",
    "message": "集群日志组件elastichsearch未就绪"
  }
]
```

#### 示例
```shell
export TOKEN="xxx"
export HOST="***********"
curl -XGET -H "Authorization: ${TOKEN}" "http://${HOST}/olympus-portal/apis/v1/addons/features/messages?features=elk/ready,elk/elasticsearchReady"
```



## 权限点联动
`app_management.sys_permission`表结构, 其中的`annotations`支持扩展。

```plsql
-- auto-generated definition
create table sys_permission
(
  id                 bigint               not null comment '主键ID'
  primary key,
  # xxxx
  annotations        text                 null comment '扩展字段',
)
    comment '权限表' charset = utf8;
```

因此对于菜单每个权限点可以在`anntations`添加如下结构

```
{
  // 要求集群组件启用的功能特性
  "requeridClusterAddonFeatures": [
    // 比如这么填写则是要求有对应的能力
    "elk/Installed",
    "elk/Ready",
    "elk/ElasticsearchEnableBacup"
  ]
}
```

前端可以通过该字段数组是否为集群中`addonEnableFeatures`的子集来展示页面。



![](https://cdn.nlark.com/yuque/__mermaid_v3/baa27f66c660f4a1b13ee7cf9b387567.svg)

## CRD 设计
示例eca yaml

```yaml
apiVersion: unified-platform.harmonycloud.cn/v1alpha1
kind: EnhanceClusterAddon
metadata:
  name: elk                      # 必须与对应 ClusterAddon 名称一致
  namespace: stellaris-workspace-cluster161
  finalizers:
    - enhanceclusteraddon.unifiedplatform.harmonycloud.cn/finalizer  # 确保资源删除前清理逻辑执行
spec:
  addonRef:
    name: elk
    namespace: stellaris-workspace-cluster161

  builtinFeatures:
    - Installed                 # 需要开启的内建特性
    - Ready

  features:
    - name: ElasticsearchReady
      description: check elasticsearch status
      healthCheckInterval: 10s        # 特性统一检查间隔时间

      healthChecks:
        - name: check elasticsearch status
          collector:
            http:
              method: GET
              timeout: 5s
              urlTemplate: >           # 从配置中拼出 ES 健康状态接口
                {{- $cfg := .status.configurations.configurationSchemaData.elk }}
                {{ printf "%s://%s:%s/_cluster/health" $cfg.protocol $cfg.ip $cfg.port }}
              authorizationTemplate: > # 通过配置模板拼出 Basic Auth
                {{ $name := .status.configurations.configurationSchemaData.elk.esName }}
                {{ $pwd := .status.configurations.configurationSchemaData.elk.esPassword }}
                {{ printf "Basic %s" (printf "%s:%s" $name $pwd | base64Encode) }}

          filters:
            - http:
                extractField: body     # 从响应中提取 body 内容
            - jsonpath:
                expression: '{.status}'  # 提取字段 status

          matchers:
            - matchExpressions:
                - operator: In
                  values: [green, yellow]  # 状态在其中即匹配成功

    - name: ElasticsearchEnableBackup
      description: check elasticsearch backup configuration
      healthCheckInterval: 10s
      logicExpression: 0 OR 1           # 表示只要两个子检查中任意一个通过即可

      healthChecks:
        - name: check elasticsearch support fs backup
          collector:
            http:
              method: GET
              timeout: 5s
              urlTemplate: >
                {{- $cfg := .status.configurations.configurationSchemaData.elk }}
                {{ printf "%s://%s:%s/_cluster/settings?include_defaults=true" $cfg.protocol $cfg.ip $cfg.port }}
              authorizationTemplate: >
                {{ $name := .status.configurations.configurationSchemaData.elk.esName }}
                {{ $pwd := .status.configurations.configurationSchemaData.elk.esPassword }}
                {{ printf "Basic %s" (printf "%s:%s" $name $pwd | base64Encode) }}

          filters:
            - http:
                extractField: body
            - goTemplate:
                template: '{{ len .defaults.path.repo }}'  # 获取可用 repo 长度

          matchers:
            - matchExpressions:
                - operator: Gt
                  values: ["0"]         # 长度大于 0 表示存在 repo

        - name: check elasticsearch support s3 backup
          collector:
            http:
              method: GET
              timeout: 5s
              urlTemplate: >
                {{- $cfg := .status.configurations.configurationSchemaData.elk }}
                {{ printf "%s://%s:%s/_cat/plugins" $cfg.protocol $cfg.ip $cfg.port }}
              authorizationTemplate: >
                {{ $name := .status.configurations.configurationSchemaData.elk.esName }}
                {{ $pwd := .status.configurations.configurationSchemaData.elk.esPassword }}
                {{ printf "Basic %s" (printf "%s:%s" $name $pwd | base64Encode) }}

          filters:
            - http:
                extractField: body
            - regex:
                captureGroupIndex: 1
                regex: .*(repository-s3).*  # 判断是否包含 s3 插件

          matchers:
            - matchExpressions:
                - operator: Equal
                  values: [repository-s3]

status:
  phase: Synced                   # EnhanceClusterAddon 控制器自身状态
  addonPhase: Abnormal            # 被增强的 addon 的状态

  featureConditions:
    - name: Installed
      builtin: true
      status: "True"
      lastTransitionTime: "2025-06-07T03:16:36Z"

    - name: Ready
      builtin: true
      status: "False"
      reason: HealthCheckFailed
      message: ClusterAddon ... has failed HealthCheck condition: StorageUnHealthy

    - name: ElasticsearchReady
      status: "False"
      reason: ProbeFailed
      message: health check failed: evaluation error
      logicExpression: AND
      healthChecks:
        - name: check elasticsearch status
          reason: CollectorError
          message: connection refused
          lastTransitionTime: "2025-06-07T03:16:06Z"

    - name: ElasticsearchEnableBackup
      status: "False"
      reason: ProbeFailed
      message: has failed health checks
      logicExpression: 0 OR 1
      healthChecks:
        - name: check elasticsearch support fs backup
          reason: CollectorError
          message: connection refused
        - name: check elasticsearch support s3 backup
          reason: CollectorError
          message: connection refused
```

具体定义文件

```go
/*
Copyright 2024.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

package v1alpha1

import (
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// HealthCheckReasonType 定义健康检查状态的原因，用于标准化常见的状态原因。
// +kubebuilder:validation:Type=string
type HealthCheckReasonType string

// Common HealthCheck Reasons
const (
	HealthCheckReasonInitializing         HealthCheckReasonType = "Initializing"
	HealthCheckReasonHealthy              HealthCheckReasonType = "Healthy"
	HealthCheckReasonUnhealthy            HealthCheckReasonType = "Unhealthy"
	HealthCheckReasonExecutionError       HealthCheckReasonType = "ExecutionError"
	HealthCheckReasonCollectorError       HealthCheckReasonType = "CollectorError"
	HealthCheckReasonFilterError          HealthCheckReasonType = "FilterError"
	HealthCheckReasonMatcherError         HealthCheckReasonType = "MatcherError"
	HealthCheckReasonMatcherMismatchError HealthCheckReasonType = "MatcherMismatchError"
	HealthCheckReasonHttpStatusMatch      HealthCheckReasonType = "HttpStatusMatch"
	HealthCheckReasonHttpStatusNotMatch   HealthCheckReasonType = "HttpStatusNotMatch"
	HealthCheckReasonExpressionMatch      HealthCheckReasonType = "ExpressionMatch"
	HealthCheckReasonExpressionNotMatch   HealthCheckReasonType = "ExpressionNotMatch"
	HealthCheckReasonNoMatchersDefined    HealthCheckReasonType = "NoMatchersDefined"
	HealthCheckReasonNoMatcherMatched     HealthCheckReasonType = "NoMatcherMatched"
	HealthCheckReasonMissingStatus        HealthCheckReasonType = "MissingHealthCheckStatus"
	HealthCheckReasonNoChecksDefined      HealthCheckReasonType = "NoHealthChecksDefined"
	HealthCheckReasonStatusPending        HealthCheckReasonType = "HealthStatusPending"
	HealthCheckReasonFailed               HealthCheckReasonType = "Failed"
)

// EDIT THIS FILE!  THIS IS SCAFFOLDING FOR YOU TO OWN!
// NOTE: json tags are required.  Any new fields you add must have json tags for the fields to be serialized.

// EnhanceClusterAddonSpec defines the desired state of EnhanceClusterAddon
type EnhanceClusterAddonSpec struct {
	// AddonRef 关联的 ClusterAddon 资源引用
	// +optional
	AddonRef *AddonRef `json:"addonRef,omitempty"`
	// Type 增强类型
	// +optional
	Type AddonType `json:"type,omitempty"`
	// BuiltinFeatures 内置特性列表
	// +optional
	BuiltinFeatures []string `json:"builtinFeatures,omitempty"`
	// Features 特性列表
	// +optional
	Features []Feature `json:"features,omitempty"`
}

// AddonRef 定义 ClusterAddon 资源的引用信息
type AddonRef struct {
	// Name ClusterAddon 资源的名称
	// +required
	Name string `json:"name"`
	// Namespace ClusterAddon 资源所在的命名空间
	// +optional
	Namespace string `json:"namespace"`
	// Cluster ClusterAddon 资源所在的集群
	// +optional
	Cluster string `json:"cluster,omitempty"`
}

type AddonType string

const (
	AddonTypeCommon            AddonType = ""
	AddonTypeACL               AddonType = "acl"
	AddonTypeAppDecompile      AddonType = "app-decompile"
	AddonTypeAppModel          AddonType = "app-model"
	AddonTypeBaselineChecker   AddonType = "baseline-checker"
	AddonTypeCalico            AddonType = "calico"
	AddonTypeCoreDNS           AddonType = "coredns"
	AddonTypeELK               AddonType = "elk"
	AddonTypeGPU               AddonType = "gpu"
	AddonTypeHeimdallr         AddonType = "heimdallr"
	AddonTypeHPA               AddonType = "hpa"
	AddonTypeKubeVirt          AddonType = "kubevirt"
	AddonTypeMonitoring        AddonType = "monitoring"
	AddonTypeNodePool          AddonType = "node-pool"
	AddonTypeOVN               AddonType = "ovn"
	AddonTypeProblemIsolation  AddonType = "problem-isolation"
	AddonTypeResourceAggregate AddonType = "resource-aggregate"
	AddonTypeSisyphus          AddonType = "sisyphus"
	AddonTypeVelero            AddonType = "velero"
	// TODO: Add any other addon types if they exist or are added in the future
)

// FeatureType 特性类型
type FeatureType string

const (
	// FeatureTypeBuiltin 内置特性
	FeatureTypeBuiltin FeatureType = "builtin"
	// FeatureTypeCustom 自定义特性
	FeatureTypeCustom FeatureType = "custom"
)

// HealthCheckLogicOperator 健康检查逻辑操作符
type HealthCheckLogicOperator string

const (
	// HealthCheckLogicOperatorAnd 与操作
	HealthCheckLogicOperatorAnd HealthCheckLogicOperator = "AND"
	// HealthCheckLogicOperatorOr 或操作
	HealthCheckLogicOperatorOr HealthCheckLogicOperator = "OR"
)

// HealthCheckGroup 健康检查组

type Feature struct {
	// Name 特性名称
	// +required
	Name string `json:"name"`
	// Description 特性描述
	// +optional
	Description string `json:"description,omitempty"`
	// HealthChecks 健康检查列表
	// +optional
	HealthChecks HealthCheckList `json:"healthChecks"`
	// Interval 间隔
	// 10s 1m 1h etc..
	// +optional
	HealthCheckInterval string `json:"healthCheckInterval,omitempty"`
	// LogicExpression 逻辑表达式
	// +optional
	LogicExpression string `json:"logicExpression,omitempty"`
}

// HealthCheckList 健康检查列表
type HealthCheckList []HealthCheck

type HealthCheckCue struct {
	// Template 模板
	// +required
	Template string `json:"template"`
}

type HealthCheckCollector struct {
	// Http 收集器
	// +optional
	Http *HealthCheckCollectorHttp `json:"http,omitempty"`

	// KubernetesObject 收集器
	// +optional
	KubernetesObjectRef *KubernetesObjectRef `json:"kubernetesObjectRef,omitempty"`
}

// HealthCheckCollectorHttp http 健康检查
type HealthCheckCollectorHttp struct {
	// URL 请求地址
	// +optional
	URL string `json:"url,omitempty"`
	// URLTemplate 请求地址模板
	// 如果 URL 为空，则使用 URLTemplate 作为请求地址
	// URLTemplate 支持 goTemplate 语法
	// +optional
	URLTemplate string `json:"urlTemplate,omitempty"`
	// Method 请求方法
	// +optional
	// +default=GET
	Method string `json:"method,omitempty"`
	// Headers 请求头
	// +optional
	Headers map[string]string `json:"headers,omitempty"`
	// Authorization 请求授权
	// +optional
	Authorization string `json:"authorization,omitempty"`
	// AuthorizationTemplate 授权模板
	// +optional
	AuthorizationTemplate string `json:"authorizationTemplate,omitempty"`
	// Timeout 请求超时时间
	// +optional
	// +default=10s
	Timeout string `json:"timeout,omitempty"`
}

// KubernetesObjectRef kubernetes 健康检查
type KubernetesObjectRef struct {
	// APIVersion API 版本
	// +optional
	APIVersion string `json:"apiVersion,omitempty"`
	// Kind 资源类型
	// +optional
	Kind string `json:"kind,omitempty"`
	// Namespace 命名空间
	// +optional
	Namespace string `json:"namespace,omitempty"`
	// Name 资源名称
	// +optional
	Name string `json:"name,omitempty"`
	// LabelSelector 资源标签选择器
	// +optional
	LabelSelector *metav1.LabelSelector `json:"labelSelector,omitempty"`
}

// HealthCheckFilter 健康检查过滤器
type HealthCheckFilter struct {
	// GoTemplate 基于 goTemplate 过滤
	// +optional
	GoTemplate *HealthCheckFilterGoTemplate `json:"goTemplate,omitempty"`
	// JSONPath 基于 jsonpath 过滤
	// +optional
	JSONPath *HealthCheckFilterJSONPath `json:"jsonpath,omitempty"`
	// Cue 基于 cue 过滤
	// +optional
	Cue *HealthCheckFilterCue `json:"cue,omitempty"`
	// Regex 基于 regex 过滤
	// +optional
	Regex *HealthCheckFilterRegex `json:"regex,omitempty"`
	// HTTP 基于 http status 过滤
	// +optional
	HTTP *HealthCheckFilterHTTP `json:"http,omitempty"`
}

type HTTPExtractField string

const (
	// HttpExtractFieldHeader 基于 header 过滤
	HttpExtractFieldHeader HTTPExtractField = "header"
	// HttpExtractFieldBody 基于 body 过滤
	HttpExtractFieldBody HTTPExtractField = "body"
	// HTTPExtractFieldStatusCode 基于 status 过滤
	HTTPExtractFieldStatusCode HTTPExtractField = "statusCode"
)

// HealthCheckFilterHTTP http 健康检查
type HealthCheckFilterHTTP struct {
	// ExtractField 基于 extract 过滤
	// +kubebuilder:validation:Enum=header;body;statusCode
	// +optional
	ExtractField HTTPExtractField `json:"extractField,omitempty"`
	// HeaderName 基于 header 过滤
	// 当 ExtractField 为 header 时 如果 HeaderName 为空则获取所有 header
	// +optional
	HeaderName string `json:"headerName,omitempty"`
}

// HealthCheckFilterGoTemplate goTemplate 健康检查
type HealthCheckFilterGoTemplate struct {
	// Template 模板
	// +required
	Template string `json:"template"`
}

// HealthCheckFilterJSONPath jsonpath 健康检查
type HealthCheckFilterJSONPath struct {
	// Expression 表达式
	// +required
	Expression string `json:"expression"`
}

// HealthCheckFilterCue cue 健康检查
type HealthCheckFilterCue struct {
	// Template 模板
	// +required
	Template string `json:"template"`
}

// HealthCheckFilterRegex regex 健康检查
type HealthCheckFilterRegex struct {
	// Regex 正则表达式
	// +required
	Regex string `json:"regex"`
	// CaptureGroupIndex 捕获组
	// +required
	CaptureGroupIndex int64 `json:"captureGroupIndex"`
}

// MatchValueOperator 值匹配操作符
type MatchValueOperator string

const (
	// MatchValueOperatorEqual 等于
	MatchValueOperatorEqual MatchValueOperator = "Equal"
	// MatchValueOperatorNotEqual 不等于
	MatchValueOperatorNotEqual MatchValueOperator = "NotEqual"
	// MatchValueOperatorRegex 正则表达式
	MatchValueOperatorRegex MatchValueOperator = "Regex"
	// MatchValueOperatorIn 包含
	MatchValueOperatorIn MatchValueOperator = "In"
	// MatchValueOperatorNotIn 不包含
	MatchValueOperatorNotIn MatchValueOperator = "NotIn"
	// MatchValueOperatorGt 大于
	MatchValueOperatorGt MatchValueOperator = "Gt"
	// MatchValueOperatorGte 大于等于
	MatchValueOperatorGte MatchValueOperator = "Gte"
	// MatchValueOperatorLt 小于
	MatchValueOperatorLt MatchValueOperator = "Lt"
	// MatchValueOperatorLte 小于等于
	MatchValueOperatorLte MatchValueOperator = "Lte"
	// MatchValueOperatorContains 结果包含该字符串
	MatchValueOperatorContains MatchValueOperator = "Contains"
	// MatchValueOperatorNotContains 结果不包含该字符串
	MatchValueOperatorNotContains MatchValueOperator = "NotContains"
)

// MatchValueExpression 匹配值
type MatchValueExpression struct {
	// Key 键
	// Operator 操作符
	// kubebuilder:validation:Enum=Equal;NotEqual;Regex;In;NotIn;Gt;Gte;Lt;Lte;Contains;NotContains
	// +required
	Operator MatchValueOperator `json:"operator"`
	// Values 值
	// +required
	Values []string `json:"values"`
}

type HealthCheckMatcher struct {
	// MatchExpressions 匹配表达式
	// +optional
	MatchExpressions []MatchValueExpression `json:"matchExpressions,omitempty"`
}

//// MatchValueTerm 匹配表达式
//type MatchValueTerm struct {
//	// MatchExpressions 匹配表达式
//	// +optional
//	MatchExpressions []MatchValueExpression `json:"matchExpressions,omitempty"`
//}

// HealthCheck 健康检查
type HealthCheck struct {
	// Name 健康检查名称
	// +required
	Name string `json:"name,omitempty"`
	// Collector 收集器
	// +required
	Collector HealthCheckCollector `json:"collector"`
	// Filters 过滤
	// +optional
	Filters []HealthCheckFilter `json:"filters"`
	// Matchers 匹配
	// +required
	Matchers []HealthCheckMatcher `json:"matchers"`
}

// EnhanceClusterAddonPhase 增强集群插件阶段
type EnhanceClusterAddonPhase string

const (
	// EnhanceClusterAddonPhaseSyncing 同步中
	EnhanceClusterAddonPhaseSyncing EnhanceClusterAddonPhase = "Syncing"
	// EnhanceClusterAddonPhaseSynced 同步完成
	EnhanceClusterAddonPhaseSynced EnhanceClusterAddonPhase = "Synced"
	// EnhanceClusterAddonPhaseEmpty 空
	EnhanceClusterAddonPhaseEmpty EnhanceClusterAddonPhase = ""
)

// EnhanceClusterAddonStatus defines the observed state of EnhanceClusterAddon
type EnhanceClusterAddonStatus struct {
	// SupportedFeatures 支持的特性数组
	SupportedBuiltinFeatures []string `json:"supportedBuiltinFeatures,omitempty"`
	// EnabledFeatures 启用的特性数组
	EnabledBuiltinFeatures []string `json:"enabledBuiltinFeatures,omitempty"`
	// FeatureConditions 特性条件状态数组
	FeatureConditions []FeatureCondition `json:"featureConditions,omitempty"`
	// Phase 阶段
	// +optional
	// +default=Syncing
	Phase EnhanceClusterAddonPhase `json:"phase,omitempty"`
	// AddonPhase 关联的 ClusterAddon 资源状态
	AddonPhase ClusterAddonPhase `json:"addonPhase,omitempty"`
	// Message 状态描述信息
	// +optional
	Message string `json:"message,omitempty"`
	// LastTransitionTime 最后更新时间
	LastTransitionTime metav1.Time `json:"lastTransitionTime,omitempty"`
}

type ClusterAddonPhase string

const (
	// ClusterAddonPhasePending 待处理
	// 组件未找到
	ClusterAddonPhasePending ClusterAddonPhase = "Pending"
	// ClusterAddonPhaseReady 就绪
	ClusterAddonPhaseReady ClusterAddonPhase = "Ready"
	// ClusterAddonPhaseAbnormal 异常
	ClusterAddonPhaseAbnormal ClusterAddonPhase = "Abnormal"
)

type FeatureConditionReasonType string

const (
	// FeatureConditionReasonAddonNotFound 未找到
	FeatureConditionReasonAddonNotFound FeatureConditionReasonType = "AddonNotFound"
	// FeatureConditionReasonAddonEndpointNotFound ep 未找到
	FeatureConditionReasonAddonEndpointNotFound FeatureConditionReasonType = "AddonEndpointNotFound"

	// FeatureConditionReasonHealthCheckFailed 健康检查失败
	FeatureConditionReasonHealthCheckFailed FeatureConditionReasonType = "HealthCheckFailed"

	// FeatureConditionReasonConfigurationEmpty 配置为空
	FeatureConditionReasonConfigurationEmpty FeatureConditionReasonType = "ConfigurationEmpty"

	// FeatureConditionReasonError 错误
	FeatureConditionReasonError FeatureConditionReasonType = "Error"
	// FeatureConditionReasonElastichsearchCheckHealthError elasticsearch 健康检查失败
	FeatureConditionReasonElastichsearchCheckHealthError FeatureConditionReasonType = "ElasticsearchCheckHealthError"
	// FeatureConditionReasonSyncing 同步中
	FeatureConditionReasonSyncing FeatureConditionReasonType = "Syncing"
	// FeatureConditionReasonSynced 同步完成
	FeatureConditionReasonSynced FeatureConditionReasonType = "Synced"
	// FeatureConditionReasonAbnormal 异常
	FeatureConditionReasonAbnormal FeatureConditionReasonType = "Abnormal"
	// FeatureConditionReasonPending 等待中
	FeatureConditionReasonPending FeatureConditionReasonType = "Pending"
	// FeatureConditionReasonChecksNotConfigured 未配置健康检查
	FeatureConditionReasonChecksNotConfigured FeatureConditionReasonType = "ChecksNotConfigured"
	// FeatureConditionReasonUnknown 未知
	FeatureConditionReasonUnknown FeatureConditionReasonType = "Unknown"
	// FeatureConditionReasonProbeSucceeded 探测成功
	FeatureConditionReasonProbeSucceeded FeatureConditionReasonType = "ProbeSucceeded"
	// FeatureConditionReasonProbeFailed 探测失败
	FeatureConditionReasonProbeFailed FeatureConditionReasonType = "ProbeFailed"
)

// FeatureCondition 特性条件
type FeatureCondition struct {
	// Name 特性名称
	// +required
	Name string `json:"name"`
	// Builtin 是否内置
	Builtin *bool `json:"builtin,omitempty"`
	// Status 状态
	// +required
	Status metav1.ConditionStatus `json:"status"`
	// Reason 状态原因
	// +optional
	Reason FeatureConditionReasonType `json:"reason,omitempty"`
	// Message 状态描述信息
	// +optional
	Message string `json:"message,omitempty"`
	// LogicExpression 逻辑表达式，用于计算健康检查结果
	// 使用索引表示健康检查，例如：0 AND (1 OR 2)
	// +optional
	LogicExpression string `json:"logicExpression,omitempty"`
	// HealthChecks 健康检查
	// +optional
	HealthChecks []HealthCheckStatus `json:"healthChecks,omitempty"`
	// LastTransitionTime 最后状态转换时间
	// +optional
	LastTransitionTime metav1.Time `json:"lastTransitionTime,omitempty"`
}

// HealthCheckStatus 健康检查结果
type HealthCheckStatus struct {
	// Name 健康检查名称
	// +required
	Name string `json:"name"`
	// ActualValue 实际值
	ActualValue string `json:"actualValue,omitempty"`
	// Matched 是否匹配
	// +optional
	Matched *bool `json:"matched,omitempty"`
	// Reason 状态原因
	Reason HealthCheckReasonType `json:"reason,omitempty"`
	// Message 状态描述信息
	// +optional
	Message string `json:"message,omitempty"`
	// LastTransitionTime 最后状态转换时间
	// +optional
	LastTransitionTime metav1.Time `json:"lastTransitionTime,omitempty"`
}

//+kubebuilder:object:root=true
//+kubebuilder:subresource:status
//+kubebuilder:resource:shortName=eca
// +kubebuilder:printcolumn:name="AddonRef",type=string,JSONPath=`.spec.addonRef.name`
// +kubebuilder:printcolumn:name="BuiltinFeatures",type=string,JSONPath=`.status.enabledBuiltinFeatures[*]`
// +kubebuilder:printcolumn:name="Features",type=string,JSONPath=`.status.featureConditions[*].name`
// +kubebuilder:printcolumn:name="AddonPhase",type=string,JSONPath=`.status.addonPhase`
// +kubebuilder:printcolumn:name="Phase",type=string,JSONPath=`.status.phase`
// +kubebuilder:printcolumn:name="AGE",type="date",JSONPath=".metadata.creationTimestamp"

// EnhanceClusterAddon is the Schema for the enhanceclusteraddons API
type EnhanceClusterAddon struct {
	metav1.TypeMeta   `json:",inline"`
	metav1.ObjectMeta `json:"metadata,omitempty"`

	Spec   EnhanceClusterAddonSpec   `json:"spec,omitempty"`
	Status EnhanceClusterAddonStatus `json:"status,omitempty"`
}

//+kubebuilder:object:root=true

// EnhanceClusterAddonList contains a list of EnhanceClusterAddon
type EnhanceClusterAddonList struct {
	metav1.TypeMeta `json:",inline"`
	metav1.ListMeta `json:"metadata,omitempty"`
	Items           []EnhanceClusterAddon `json:"items"`
}

func init() {
	SchemeBuilder.Register(&EnhanceClusterAddon{}, &EnhanceClusterAddonList{})
}

```

##  controller 同步管理
### EnhanceClusterAddon
大致状态

![](https://cdn.nlark.com/yuque/__puml/ab3219c48b370b40cb3f45993737e76d.svg)

`AddonFeatureReconciler`

```go
// 特性门控协调逻辑
func (r *FeatureGateReconciler) Reconcile(ctx context.Context, req ctrl.Request) (ctrl.Result, error) {
    addon := &v1alpha1.ClusterAddon{}
    if err := r.Get(ctx, req.NamespacedName, addon); err != nil {
        return ctrl.Result{}, client.IgnoreNotFound(err)
    }
    // 解析特性配置
    featureStates := parseFeatures(addon.Spec.Configurations.Schema[0].Data)

    // 更新组件状态
    r.updateComponentStatus(addon, featureStates)

    // 同步能力矩阵
    r.syncCapabilityMatrix(addon, featureStates)

    return ctrl.Result{}, nil
}
```



### ClusterAddon
clusteraddon 同步流程

![](https://cdn.nlark.com/yuque/__puml/492566a6daf4d8e909b79f04f4302df9.svg)

## 组件能力判断
###  后端判断
**待定**

对于Java 项目目前可以考虑添加 AOP 注解，在执行对应的方法前检查集群组件对应的功能是否开启。

不方便AOP注解，则手动判断。

但是目前存在不同小组之间的java项目，如监控APM和门户的项目，可能比较麻烦。

<font style="background-color:rgb(236, 170, 4);">还有种方式通过网关去校验组件的要求，待定。</font>

注解方式示例:

```java
@Target(ElementType.METHOD)
@Retention(RetentionPolicy.RUNTIME)
public @interface MyAudit {
    String clusterExpr(); // 使用 SpEL 表达式，比如 "#name"、"#user.id"、"#names[0]"
    List<ClusterAddonFeatureEnum> features;
}

public enum ClusterAddonFeatureEnum {
    ELKEnableBakcup
}


public ServiceImpl {
    // 比如这样就是要求开启对应的集群elk支持日志备份
    // 集群中参数中拿
    @RequiredClusterAddon(
        // spel 表达式定位cluster参数        
        clusterExpr = "#cluster", 
        // 数组
        features = {ELKEnableBakcup})
    void doSomething(Cluster cluster);
}
```

对于Go 项目可以在GIN中添加对应的中间件，去校验对应的路由方法要求开启的功能或者直接在方法中执行。

<font style="color:rgb(223, 42, 63);">TODO</font>

###  前端限制逻辑
![](https://cdn.nlark.com/yuque/__mermaid_v3/a4b8a6ca8c287e8049a6da074e05bcc6.svg)

基于react的示例组件

> ai 生成
>

```jsx
import React, { useEffect, useState } from 'react';
import { Button, Card, message } from 'antd';
import axios from 'axios';

const AddonFeatureAction = ({ code, children, cluster, ...rest }) => {
  const [enabledFeatures, setEnabledFeatures] = useState([]);
  const [disabledFeatures, setDisabledFeatures] = useState([]);
  const [featureMessages, setFeatureMessages] = useState({});
  const [loading, setLoading] = useState(true);
  const [isAllowed, setIsAllowed] = useState(false);
  const [requiredFeatures, setRequiredFeatures] = useState([]);

  useEffect(() => {
    const fetchData = async () => {
      try {
        // Get required features from permission tree (mock implementation)
        const requeridClusterAddonFeatures = getRequiredFeaturesFromPermissionTree(code);
        setRequiredFeatures(requeridClusterAddonFeatures);

        // Get enabled/disabled features from cluster
        const featuresResponse = await axios.get(
          `http://${window.location.host}/olympus-portal/apis/v1/addons/clusters/${cluster}/features`
        );
        const { enabled, disabled } = featuresResponse.data;
        setEnabledFeatures(enabled || []);
        setDisabledFeatures(disabled || []);

        // Check if required features are subset of enabled features
        const allowed = requeridClusterAddonFeatures.every(feature => enabled.includes(feature));
        setIsAllowed(allowed);

        // Get messages for disabled required features
        const disabledRequiredFeatures = requeridClusterAddonFeatures.filter(feature => 
          disabled.includes(feature)
        );
        if (disabledRequiredFeatures.length > 0) {
          const messagesResponse = await axios.get(
            `http://${window.location.host}/olympus-portal/apis/v1/addons/features/messages`,
            { params: { features: disabledRequiredFeatures.join(',') } }
          );
          const messages = {};
          messagesResponse.data.forEach(item => {
            messages[item.feature] = item.message;
          });
          setFeatureMessages(messages);
        }
      } catch (error) {
        message.error('Failed to fetch feature permissions');
        console.error(error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [code, cluster]);

  const getRequiredFeaturesFromPermissionTree = (code) => {
    // Mock implementation - in real app this would come from permission tree
    return code.split(',');
  };

  const getDisabledMessage = () => {
    const disabledRequiredFeature = requiredFeatures.find(feature => 
      disabledFeatures.includes(feature)
    );
    return featureMessages[disabledRequiredFeature] || 'Feature is disabled';
  };

  if (loading) {
    return null;
  }

  if (isAllowed) {
    return children;
  }

  // Handle different element types
  const childType = children?.type?.displayName || children?.type?.name;
  
  if (childType === 'Button') {
    return React.cloneElement(children, {
      disabled: true,
      title: getDisabledMessage(),
      ...rest
    });
  }

  if (childType === 'Card') {
    return (
      <Card {...rest}>
        <div style={{ padding: 16, textAlign: 'center' }}>
          {getDisabledMessage()}
        </div>
      </Card>
    );
  }

  // Default case for other elements
  return React.cloneElement(children, {
    style: { opacity: 0.5, pointerEvents: 'none' },
    title: getDisabledMessage(),
    ...rest
  });
};

export default AddonFeatureAction;

```

## 消息整理
| **特性 (Feature)** | **错误码 (Error Code)** | **错误消息 (Message)** |
| --- | --- | --- |
| **ELK 组件功能特性** |  |  |
| elk/Installed | 10800 | ELK日志组件未安装 |
| elk/Ready | 10801 | ELK日志组件未就绪 |
| elk/ElasticsearchReady | 10802 | Elasticsearch服务未就绪 |
| elk/ElasticsearchEnableBackup | 10803 | Elasticsearch备份功能未启用 |
| **监控组件功能特性** |  |  |
| monitoring/Installed | 10804 | 监控组件未安装 |
| monitoring/Ready | 10805 | 监控组件未就绪 |
| monitoring/AlertManagerReady | 10806 | AlertManager服务未就绪 |
| monitoring/PrometheusReady | 10807 | Prometheus服务未就绪 |
| **Sisyphus 部署平台组件功能特性** |  |  |
| sisyphus/Installed | 10808 | Sisyphus部署平台组件未安装 |
| sisyphus/Ready | 10809 | Sisyphus部署平台组件未就绪 |
| **HPA 扩缩容组件功能特性** |  |  |
| hpa/Installed | 10810 | HPA扩缩容组件未安装 |
| hpa/Ready | 10811 | HPA扩缩容组件未就绪 |
| hpa/appAutoScale | 10812 | HPA应用自动扩缩容功能未启用 |
| **GPU 组件功能特性** |  |  |
| gpu/Installed | 10813 | GPU组件未安装 |
| gpu/Ready | 10814 | GPU组件未就绪 |
| **网络组件功能特性** |  |  |
| calico/Installed | 10815 | Calico网络组件未安装 |
| calico/Ready | 10816 | Calico网络组件未就绪 |
| ovn/Installed | 10817 | OVN网络组件未安装 |
| ovn/Ready | 10818 | OVN网络组件未就绪 |
| **应用模型组件功能特性** |  |  |
| app-model/Installed | 10819 | 应用模型组件未安装 |
| app-model/Ready | 10820 | 应用模型组件未就绪 |
| **基线检查组件功能特性** |  |  |
| baseline-checker/Installed | 10821 | 基线检查组件未安装 |
| baseline-checker/Ready | 10822 | 基线检查组件未就绪 |
| **备份组件功能特性** |  |  |
| velero/Installed | 10823 | Velero备份组件未安装 |
| velero/Ready | 10824 | Velero备份组件未就绪 |
| **虚拟机组件功能特性** |  |  |
| kubevirt/Installed | 10825 | KubeVirt虚拟机组件未安装 |
| kubevirt/Ready | 10826 | KubeVirt虚拟机组件未就绪 |
| **其他组件功能特性** |  |  |
| problem-isolation/Installed | 10827 | 故障隔离组件未安装 |
| problem-isolation/Ready | 10828 | 故障隔离组件未就绪 |
| resource-aggregate/Installed | 10829 | 资源关联控制器组件未安装 |
| resource-aggregate/Ready | 10830 | 资源关联控制器组件未就绪 |
| app-decompile/Installed | 10831 | 应用反编译组件未安装 |
| app-decompile/Ready | 10832 | 应用反编译组件未就绪 |
| heimdallr/Installed | 10833 | 统一网络模型组件未安装 |
| heimdallr/Ready | 10834 | 统一网络模型组件未就绪 |
| acl/Installed | 10835 | 网络隔离组件未安装 |
| acl/Ready | 10836 | 网络隔离组件未就绪 |
| node-pool/Installed | 10837 | 节点资源池组件未安装 |
| node-pool/Ready | 10838 | 节点资源池组件未就绪 |
| coredns/Installed | 10839 | CoreDNS组件未安装 |
| coredns/Ready | 10840 | CoreDNS组件未就绪 |


#  改造点
后端代码兼容组件的情况

##  特殊组件-负载均衡
+ apisix
+ nginx
+ traefik

<font style="background-color:rgb(236, 170, 4);">负载均衡目前没有对应的集群组件，并且使用了底座编写的</font>`<font style="background-color:rgb(236, 170, 4);">ingress-expose-helper</font>`<font style="background-color:rgb(236, 170, 4);">组件，无法通过统一方式进行区分兼容，需要做单独逻辑处理。</font>

##  特殊组件-S3存储
<font style="background-color:rgb(236, 170, 4);">s3组件目前没有对应的集群组件，需要单独判断。</font>

##  特殊组件-多集群
**<font style="color:rgb(223, 42, 63);">核心组件，纳管依赖于多集群，必须安装。</font>**

##  部署平台Sisyphus
只判断是否正常运行

负责集群管理、节点上下线。

###  特性功能
####  功能点
```yaml
apiVersion: unified-platform.harmonycloud.cn/v1alpha1
kind: EnhanceClusterAddon
metadata:
  finalizers:
  - enhanceclusteraddon.unifiedplatform.harmonycloud.cn/finalizer
  name: sisyphus
  namespace: stellaris-workspace-cluster161
spec:
  addonRef:
    name: sisyphus
    namespace: stellaris-workspace-cluster161
  builtinFeatures:
  - Installed
  - Ready
status:
  addonPhase: Ready
  enabledBuiltinFeatures:
  - Installed
  - Ready
  featureConditions:
  - builtin: true
    lastTransitionTime: "2025-06-07T03:16:36Z"
    name: Installed
    status: "True"
  - builtin: true
    lastTransitionTime: "2025-06-07T03:16:36Z"
    name: Ready
    status: "True"
  lastTransitionTime: "2025-06-07T08:12:05Z"
  message: Feature Runners Synced
  phase: Synced
  supportedBuiltinFeatures:
  - Installed
  - Ready
```

| **<font style="background-color:rgb(231, 233, 232);">功能点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台组件</font>** | **<font style="background-color:rgb(231, 233, 232);">能力点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台页面</font>** |
| :--- | :--- | :--- | :--- |
| 集群管理/创建集群 | olympus-portal | Ready | |
| 集群管理/升级集群 | olympus-portal | Ready | |
| 主机管理/主机上下线 | olympus-portal | Ready | [http://***********/?isBackend=1&navCode=unified_platform_sys_platform_mgr&isSubMenu=1&clusterName=cluster-57#/cluster/space/node/normal/detail/cluster-57-node0002.10.120.1.59](http://***********/?isBackend=1&navCode=unified_platform_sys_platform_mgr&isSubMenu=1&clusterName=cluster-57#/cluster/space/node/normal/detail/cluster-57-node0002.10.120.1.59) |
| 主机管理/支持批量添加arm、x86架构的主机，还可查看添加主机的进度 | olympus-portal | Ready | |
| 主机管理/纳管和使用具有GPU的主机		 | olympus-portal | Ready | |


####  权限点
菜单权限点要求开启的功能

+ 创建集群

```json
{
  // Sisyphus
  "requeridClusterAddonFeatures": [
    // 创建集群
    "sisyphus/Ready",
  ]
}
```

+ 节点上下线

```json
{
  // Sisyphus
  "requeridClusterAddonFeatures": [
    // 创建集群
    "sisyphus/Ready"
  ]
}
```

### 相关代码
#### 集群管理
portal

1. 集群管理/创建集群
2. 集群管理/升级集群

```go
// Handler
// 集群相关APi方法集
type Handler interface {
    ListClusters(ctx context.Context, options ...ListClusterOption) (clustermodel.ListResponse, error)
    ClusterExist(ctx context.Context, clusterName string) (*clustermodel.ExistResponse, error)
    WithClusterName(ctx context.Context, clusterName string) (*clustermodel.Response, error)
    SwitchClusterList(ctx context.Context) (*models.SwitchObjectResp, error)
}

// CreateClustersHandler
// 集群创建相关Api方法集
type CreateClustersHandler interface {
    // CreateCluster
    // 创建集群对外透出接口
    CreateCluster(ctx context.Context, request clustermodel.CreateRequest) error

    // UpdateCluster
    // 更新创建集群对外透出接口
    UpdateCluster(ctx context.Context, clusterName string, request clustermodel.CreateRequest) error

    // DeleteCluster
    // 删除创建失败的集群
    DeleteCluster(ctx context.Context, clusterName string) error

    // CreateClusterStatus
    // 集群创建状态获取
    CreateClusterStatus(ctx context.Context, clusterName string) (*clustermodel.CreateStatusResponse, error)

    // CreateResponse
    // 获取集群创建的回显表单
    CreateResponse(ctx context.Context, clusterName string) (*clustermodel.CreateResponse, error)

    // CreateLog
    // 读取创建集群过程中的日志
    CreateLog(ctx context.Context, request clustermodel.CreateLogQueryRequest) (<-chan []byte, context.Context, context.CancelFunc, error)

    // Retry
    // 集群创建失败重试
    Retry(ctx context.Context, clusterName string) error
}

type UpgradeClustersHandler interface {
    // ListClusterUpgrade 集群列表-集群升级显示
    ListClusterUpgrade(ctx *gin.Context) (*clustermodel.ListUpgradeClusterInfo, error)
    // ListClusterUpgradePackage 集群升级包列表
    ListClusterUpgradePackage(ctx *gin.Context, clusterName string) (*clustermodel.ListPackageInfo, error)
    // GetClusterUpgradePackage 集群升级包详情
    GetClusterUpgradePackage(ctx *gin.Context, clusterName string, packageName string) (*clustermodel.PackageContent, error)
    // GetClusterUpgradeLoadBalanceDefault 集群默认负载均衡
    GetClusterUpgradeLoadBalanceDefault(ctx *gin.Context, clusterName string) (*[]string, error)
    // ListClusterUpgradeNodes 集群升级节点列表
    ListClusterUpgradeNodes(ctx *gin.Context, clusterName string, includeNative string) (*clustermodel.ListNodeInfo, error)
    // DownloadClusterUpgradeNodeTemplate 下载集群模版添加节点列表
    DownloadClusterUpgradeNodeTemplate(ctx *gin.Context, clusterName string, includeNative string) (*template.FileDownloadInfo, error)
    // UploadClusterUpgradeNodeTemplate 上传集群模版添加节点列表
    UploadClusterUpgradeNodeTemplate(ctx *gin.Context, fhs []*multipart.FileHeader, clusterName string, includeNative string) (interface{}, error)
    // UpgradeCluster 创建集群升级对象
    UpgradeCluster(ctx *gin.Context, clusterName string, request clustermodel.UpgradeClusterRequest) error
    // GetClusterUpgradeStep 获取集群升级步骤
    GetClusterUpgradeStep(ctx *gin.Context, clusterName string) (*clustermodel.UpgradeClusterStepResponse, error)
    // RetryClusterUpgrade 重试集群升级
    RetryClusterUpgrade(ctx *gin.Context, clusterName string) error
    // GetClusterUpgrade 获取集群升级信息
    GetClusterUpgrade(ctx *gin.Context, clusterName string) (*clustermodel.UpgradeClusterRequest, error)
}
```

#### 主机管理
portal 目前涉及到的代码

1. 主机管理/主机上下线
2. 支持批量添加arm、x86架构的主机，还可查看添加主机的进度
3. 主机管理/纳管和使用具有GPU的主机**<font style="color:rgb(223, 42, 63);">(纳管和使用GPU主机对于部署平台实时上就是打上标签)</font>**

```go
// NodeUpDownHandler 节点上下线接口
type NodeUpDownHandler interface {
    // Count 获取节点上显现任务数量
    Count(ctx context.Context, clusterName string, filters ...NodeUpDownResponseFilter) (*node.NodeUpDownCountResponse, error)
    // Create 创建节点上下线任务
    Create(ctx context.Context, clusterName string, request node.NodeUpDownCreateRequest) error
    // BatchCreate 批量更新
    BatchCreate(ctx context.Context, clusterName string, batchRequest node.NodeUpDownBatchCreateRequest) error
    // List 获取节点上下线任务列表
    List(ctx context.Context, clusterName string, filters ...NodeUpDownResponseFilter) (node.NodeUpDownResponseList, error)

    // Status 获取任务状态执行详情
    Status(ctx context.Context, clusterName, nodeIp string) (*node.NodeUpDownStatusResponse, error)
    // CreateResponse 节点上下线表单回显
    CreateResponse(ctx context.Context, clusterName, nodeIp string) (*node.NodeUpDownCreateResponse, error)
    // Edit 编辑节点上下线信息
    Edit(ctx context.Context, clusterName, nodeIp string, request node.NodeUpDownCreateRequest) error
    // Retry 重试 相当于从失败处开始
    Retry(ctx context.Context, clusterName, nodeIp string) error
    // Delete 删除节点上下线任务
    Delete(ctx context.Context, clusterName, nodeIp string) error
    // Verify
    // 节点上下线节点校验
    Verify(ctx context.Context, clusterName string, request node.NodeUpDownCreateRequest) (*node.NodeVerifyResponse, error)
    // UpDownLog
    // 读取节点上下线日志过程中的日志
    UpDownLog(ctx context.Context, request node.UpDownLogQueryRequest) (<-chan []byte, context.Context, context.CancelFunc, error)
}

type ControlNodeHandler interface {
    // Nodes 获取主控节点
    Nodes(ctx context.Context, clusterName string, filters ...ListControlNodeFilter) (node.SimpleNodeResponseList, error)
    // Verify 验证节点IP
    Verify(ctx context.Context, clusterName string, nodeIP string) error
}
```

## 资源池NodePool
资源池比较特殊，如果没有安装`node-pool`组件，需要默认兼容存在一个**<font style="color:rgb(223, 42, 63);">默认资源池</font>**，保证平台基础功能正常。

只判断是否正常运行

### 4.5.1. 特性功能
#### *******. 功能点
```yaml
apiVersion: unified-platform.harmonycloud.cn/v1alpha1
kind: EnhanceClusterAddon
metadata:
  finalizers:
    - enhanceclusteraddon.unifiedplatform.harmonycloud.cn/finalizer
  generation: 1
  name: node-pool
  namespace: stellaris-workspace-cluster161
spec:
  addonRef:
    name: node-pool
    namespace: stellaris-workspace-cluster161
  builtinFeatures:
    - Installed
    - Ready
status:
  addonPhase: Ready
  enabledBuiltinFeatures:
    - Installed
    - Ready
  featureConditions:
    - builtin: true
      lastTransitionTime: "2025-06-07T03:16:36Z"
      name: Installed
      status: "True"
    - builtin: true
      lastTransitionTime: "2025-06-07T03:16:36Z"
      name: Ready
      status: "True"
  lastTransitionTime: "2025-06-07T08:14:06Z"
  message: Feature Runners Synced
  phase: Synced
  supportedBuiltinFeatures:
    - Installed
    - Ready

```

| **<font style="background-color:rgb(231, 233, 232);">功能点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台组件</font>** | **<font style="background-color:rgb(231, 233, 232);">能力点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台页面</font>** |
| :--- | :--- | :--- | :--- |
| 主机管理/支持主机资源池创建、删除及资源池内主机数量的增删调整 | olympus-core | Ready | [http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr&isSubMenu=1&clusterName=cluster-187#/cluster/space/node/resourcepool](http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr&isSubMenu=1&clusterName=cluster-187#/cluster/space/node/resourcepool)<br/>[http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr#/quotacenter/poolList](http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr#/quotacenter/poolList) |
| 支持主机资源池资源（CPU、内存、虚拟GPU显存、GPU算力、虚拟GPU卡、物理GPU卡）向组织及项目的分配 | olympus-core | Ready | [http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr#/quotacenter/poolList/detail/nodepool-1746602460-fcypt/%E5%8D%8E%E4%B8%BAiSula/cluster-187/false/false](http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr#/quotacenter/poolList/detail/nodepool-1746602460-fcypt/%E5%8D%8E%E4%B8%BAiSula/cluster-187/false/false) |


#### 4.5.1.2. 权限点
菜单权限点要求开启的功能

+ 资源池管理

```json
{
  // node-pool
  "requeridClusterAddonFeatures": [
    // 资源池管理
    "node-pool/Ready",
  ]
}
```

+ 资源池配额

```json
{
  // node-pool
  "requeridClusterAddonFeatures": [
    // 资源池配额
    "node-pool/Ready"
  ]
}
```

### 4.5.2. 相关代码
需要兼容资源池组件未安装的情况。

目前资源池与gpu、kubevirt 虚拟化相关代码存在关联，改造时候需要考虑拆分。

资源池抽象出对应的增强能力，封装获取该部分能力的接口。

对于kubevirt 和 gpu 进行封装。

示例:

```java
// 原生结构尽量不动，防止出现大量改动
public class NodePoolDTO {
    // 公共字段
    // gpu 相关字段
    // 是否包含GPU
    private Boolean hasGpu = false;
    // GPU驱动
    private String gpuDriver;    
    // GPU核总量
    private String gpuCoreTotal;
    // GPU核分配量
    private String gpuCoreRequest;
    // GPU显存总量
    private String gpuMemoryTotal;
    // GPU显存分配量
    private String gpuMemoryRequest;
    // GPU核总量
    private String gpuVirtualCoreTotal;
    // GPU核分配量
    private String gpuVirtualCoreRequest;
    // GPU显存总量
    private String gpuVirtualMemoryTotal;
    // GPU显存分配量
    private String gpuVirtualMemoryRequest;
    // GPU物理卡总量
    private String physicalGpuTotal;
    //GPU物理卡分配量
    private String physicalGpuRequest;
    //GPU虚拟算力
    private String gpuVirtualRatioTotal;
    //GPU虚拟算力分配量
    private String gpuVirtualRatioRequest;
    //GPU算力总量（物理+虚拟）
    private String gpuRatioTotal;
    //GPU算力分配量
    private String gpuRatioRequest;

    // 虚拟化相关字段
    @ApiModelProperty("是否为虚拟化")
    private boolean virtual;    
}

// 资源池能力增强接口
public interface NodePoolEnhancer {
    // 增强能力  GPU, VIRTUAL 
    String getType();
    // 是否支持
    boolean support(NodePoolDTO dto);
    // 获取增强能力信息
    Map<String, Object> extractEnhancement(NodePoolDTO dto);
    // 添加增强能力
    void applyEnhancement(NodePoolDTO dto, Map<String,Object> enhancementData);
    // 清除该增强能力信息
    void clearEnhancement(NodePoolDTO dto);
}
// GPU 实现
// 在对应实现中判断GPU是否安装, 是否要设置
public class GpuEnhancer implements NodePoolEnhancer {
    // xxx
}
// 虚拟机实现
// 在对应实现中判断virual 是否安装，判断是否要设置
public class VirtualEnhancer implements NodePoolEnhancer {
    // xxx
}

// 注册实现
public class EnhancerRegistry {

    private static final List<NodePoolEnhancer> enhancers = new ArrayList<>();

    static {
        registerEnhancer(new GpuEnhancer());
        registerEnhancer(new VirtualizationEnhancer());
    }

    public static void registerEnhancer(NodePoolEnhancer enhancer) {
        enhancers.add(enhancer);
    }

    public static NodePoolEnhancer getEnhancer(String type) {
        for (NodePoolEnhancer enhancer: enhancers){
            if (enhandcer.getType().equals(type)){
                return enhancer
            }
        }
        return null
    }

    public static Collection<NodePoolEnhancer> getAllEnhancers() {
        return enhancers;
    }
}

// 通过类统一设置
public class NodePoolEnhancerService {

    public Map<String, Map<String, Object>> getAllEnhancements(NodePoolDTO dto) {
        Map<String, Map<String, Object>> result = new HashMap<>();
        for (NodePoolEnhancer enhancer : EnhancerRegistry.getAllEnhancers()) {
            result.put(enhancer.getType(), enhancer.extractEnhancement(dto));
        }
        return result;
    }

    public void updateEnhancements(NodePoolDTO dto, Map<String, Object> enhancementData) {
        List<NodePoolEnhancer> enhancers = EnhancerRegistry.getAllEnhancers(type);
        for (NodePoolEnhancer enhancer: enhancers){
            if (enhandcer.support()){
                enhancer.applyEnhancement(dto,enhancementData)
            }
        }
    }

    public void removeEnhancements(NodePoolDTO dto, String type) {
        List<NodePoolEnhancer> enhancers = EnhancerRegistry.getAllEnhancers(type);
        for (NodePoolEnhancer enhancer: enhancers){
            if (enhandcer.support()){
                enhancer.clearEnhancement(dto,enhancementData)
            }
        }
    }
}
```

资源池管理相关代码

```java
public interface NodePoolService {
    /**
     * 获取资源池，包括资源池的资源请求量和总量
     *
     * @param clusterName  集群名
     * @param nodePoolName 资源池名
     * @return NodePoolDTO
     */
    @Nullable
    NodePoolDTO getNodePool(String clusterName, String nodePoolName);


    NodePoolDTO getK8sNodePoolDto(String clusterName, String nodePoolName);

    /**
     * 获取资源池的基本信息，可以包括租户和项目
     *
     * @param clusterName         集群名
     * @param nodePoolName        资源池名
     * @param withOrganAndProject 是否包含租户和项目名
     * @return NodePoolBaseInfoDTO
     */
    NodePoolBaseInfoDTO getNodePoolBaseInfo(String clusterName, String nodePoolName, boolean withOrganAndProject);
    NodePoolBaseInfoDTO getUnTranslateNodePoolBaseInfo(String clusterName, String nodePoolName, boolean withOrganAndProject);

    List<NodePoolBaseInfoDTO> listNodePoolBaseInfo(String clusterName, boolean withOrganAndProject);

    /**
     * 获取资源池资源配额，包括资源池的所有租户请求量和总量
     *
     * @param clusterName  集群名
     * @param nodePoolName 资源池名
     * @return NodePoolQuotaDTO
     */
    NodePoolQuotaDTO getNodePoolQuota(String clusterName, String nodePoolName);

    /**
     * 查询某个集群的资源池，包括资源池的基本信息和资源池配额, 如果集群名为null则，
     * 查询所有集群的所有资源池
     *
     * @param clusterName 集群名
     * @return List<NodePoolDTO>
     */
    List<NodePoolDTO> listNodePool(@Nullable String clusterName);

    /**
     * 查询所有集群所有的资源池，包括资源池的基本信息和资源池的配额
     *
     * @return List<NodePoolDTO>
     */
    List<NodePoolDTO> listAllNodePool();

    /**
     * 查询默认资源池，包括资源池的基本信息和资源池的配额
     *
     * @param clusterName 集群名
     * @return List<NodePoolDTO>
     */
    List<NodeDTO> listNodeOfDefaultNodePool(String clusterName);

    /**
     * 查询资源池包含的节点列表，为了效率，如不需要节点的资源请求量，请设置withNodeResourceRequest为false
     *
     * @param clusterName             集群名
     * @param nodePoolName            资源池名
     * @param withNodeResourceRequest 是否包含节点的资源请求量
     * @return 节点DTO
     */
    List<NodeDTO> listNodeOfNodePool(String clusterName, String nodePoolName, boolean withNodeResourceRequest);

    void createNodePool(NodePoolDTO nodePoolDTO);

    void deleteNodePool(String clusterName, String nodePoolName);

    void addNodeToNodePool(String clusterName, String nodePoolName, List<String> nodeNameList);

    void removeNodeFromNodePool(String clusterName, String nodePoolName, List<String> nodeNameList, Boolean expulsion);

    AsyncDeleteNamespaceMessage deleteListNodePool(List<ResourcesListDTO> resourcesListDTOList);

    /**
     * listClustersNodePoolBaseInfo 查找一组集群下的NodePool 的基本信息
     * @param clusterNames 集群列表
     * @return Map<String,List<NodePoolBaseInfoDTO>> group map , key 表示 集群名称 value 为 List 表示 集群下的NodePool基本信息列表
     * */
    Map<String,List<NodePoolBaseInfoDTO>> listClustersNodePoolBaseInfo(Set<String> clusterNames);


    NodePoolBaseInfoDTO getNodePoolByNodeName(String clusterName, String nodeName);
}
```

## 4.6. 日志ELK
涉及组件

+ olympus-core

### 4.6.1. 特性功能
#### *******. 功能点
```yaml
apiVersion: unified-platform.harmonycloud.cn/v1alpha1
kind: EnhanceClusterAddon
metadata:
  finalizers:
    - enhanceclusteraddon.unifiedplatform.harmonycloud.cn/finalizer
  generation: 2
  name: elk
  namespace: stellaris-workspace-cluster161
spec:
  addonRef:
    name: elk
    namespace: stellaris-workspace-cluster161
  builtinFeatures:
    - Installed
    - Ready
  features:
    - description: check elasticsearch status
      healthCheckInterval: 10s
      healthChecks:
        - collector:
            http:
              authorizationTemplate: '{{ $name := .status.configurations.configurationSchemaData.elk.esName }}{{ $pwd := .status.configurations.configurationSchemaData.elk.esPassword }}{{ printf "Basic %s" (printf "%s:%s" $name $pwd | base64Encode) }}'
              method: GET
              timeout: 5s
              urlTemplate: '{{- $cfg := .status.configurations.configurationSchemaData.elk }}{{ printf "%s://%s:%s/_cluster/health" $cfg.protocol $cfg.ip $cfg.port }}'
          filters:
            - http:
                extractField: body
            - jsonpath:
                expression: '{.status}'
          matchers:
            - matchExpressions:
                - operator: In
                  values:
                    - green
                    - yellow
          name: check elasticsearch status
      name: ElasticsearchReady
    - description: check elasticsearch backup configuration
      healthCheckInterval: 10s
      healthChecks:
        - collector:
            http:
              authorizationTemplate: '{{ $name := .status.configurations.configurationSchemaData.elk.esName }}{{ $pwd := .status.configurations.configurationSchemaData.elk.esPassword }}{{ printf "Basic %s" (printf "%s:%s" $name $pwd | base64Encode) }}'
              method: GET
              timeout: 5s
              urlTemplate: '{{- $cfg := .status.configurations.configurationSchemaData.elk }}{{ printf "%s://%s:%s/_cluster/settings?include_defaults=true" $cfg.protocol $cfg.ip $cfg.port }}'
          filters:
            - http:
                extractField: body
            - goTemplate:
                template: '{{ len .defaults.path.repo }}'
          matchers:
            - matchExpressions:
                - operator: Gt
                  values:
                    - "0"
          name: check elasticsearch support fs backup
        - collector:
            http:
              authorizationTemplate: '{{ $name := .status.configurations.configurationSchemaData.elk.esName }}{{ $pwd := .status.configurations.configurationSchemaData.elk.esPassword }}{{ printf "Basic %s" (printf "%s:%s" $name $pwd | base64Encode) }}'
              method: GET
              timeout: 5s
              urlTemplate: '{{- $cfg := .status.configurations.configurationSchemaData.elk }}{{ printf "%s://%s:%s/_cat/plugins" $cfg.protocol $cfg.ip $cfg.port }}'
          filters:
            - http:
                extractField: body
            - regex:
                captureGroupIndex: 1
                regex: .*(repository-s3).*
          matchers:
            - matchExpressions:
                - operator: Equal
                  values:
                    - repository-s3
          name: check elasticsearch support s3 backup
      logicExpression: 0 OR 1
      name: ElasticsearchEnableBackup
status:
  addonPhase: Abnormal
  enabledBuiltinFeatures:
    - Installed
    - Ready
  featureConditions:
    - builtin: true
      lastTransitionTime: "2025-06-07T03:16:36Z"
      name: Installed
      status: "True"
    - builtin: true
      lastTransitionTime: "2025-06-07T03:16:36Z"
      message: 'ClusterAddon ''stellaris-workspace-cluster161/elk'' has failed HealthCheck condition: StorageUnHealthy'
      name: Ready
      reason: HealthCheckFailed
      status: "False"
    - healthChecks:
        - lastTransitionTime: "2025-06-07T03:16:06Z"
          message: 'Get "http://10.120.1.162:29010/_cluster/health": dial tcp 10.120.1.162:29010: connect: connection refused'
          name: check elasticsearch status
          reason: CollectorError
      lastTransitionTime: "2025-06-07T03:16:06Z"
      logicExpression: AND
      message: 'health check failed: evaluation error'
      name: ElasticsearchReady
      reason: ProbeFailed
      status: "False"
    - healthChecks:
        - lastTransitionTime: "2025-06-07T03:16:06Z"
          message: 'Get "http://10.120.1.162:29010/_cat/plugins": dial tcp 10.120.1.162:29010: connect: connection refused'
          name: check elasticsearch support s3 backup
          reason: CollectorError
        - lastTransitionTime: "2025-06-07T03:16:16Z"
          message: 'Get "http://10.120.1.162:29010/_cluster/settings?include_defaults=true": dial tcp 10.120.1.162:29010: connect: connection refused'
          name: check elasticsearch support fs backup
          reason: CollectorError
      lastTransitionTime: "2025-06-07T03:16:16Z"
      logicExpression: 0 OR 1
      message: has failed health checks
      name: ElasticsearchEnableBackup
      reason: ProbeFailed
      status: "False"
  lastTransitionTime: "2025-06-07T08:14:35Z"
  message: Feature Runners Synced
  phase: Synced
  supportedBuiltinFeatures:
    - Installed
    - Ready
    - ElasticsearchReady
    - ElasticsearchEnableBackup

```

对应的



| **<font style="background-color:rgb(231, 233, 232);">功能点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台组件</font>** | **<font style="background-color:rgb(231, 233, 232);">能力点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台页面</font>** |
| :--- | :--- | :--- | :--- |
| 门户管理/日志中心/日志查询 | olympus-core | ElasticsearchReady | [http://*************/?isBackend=1&navCode=platform#/log/logQuery](http://*************/?isBackend=1&navCode=platform#/log/logQuery) |
| 门户管理/日志中心/系统日志 | olympus-core | ElasticsearchReady | [http://*************/?isBackend=1&navCode=platform#/log/systemLog](http://*************/?isBackend=1&navCode=platform#/log/systemLog) |
| 运维中心/日志查询 | olympus-core | ElasticsearchReady | [http://*************/?organId=3829142357954920448&projectId=1914506127637266432&appId=1902660165715365889&appCode=operation_center#/devops/space/logQuery](http://*************/?organId=3829142357954920448&projectId=1914506127637266432&appId=1902660165715365889&appCode=operation_center#/devops/space/logQuery) |
| 门户管理/日志中心/日志备份 | olympus-core | ElasticsearchEnableBackup | [http://*************/?isBackend=1&navCode=platform#/log/logBackup](http://*************/?isBackend=1&navCode=platform#/log/logBackup) |


#### 4.6.1.2. 权限点
菜单权限点要求开启的功能

+ 门户管理/日志中心/日志查询
+ 门户管理/日志中心/系统日志
+ 运维中心/日志查询

```json
{
  "requeridClusterAddonFeatures": [
    "elk/ElasticsearchReady",
  ]
}
```

+ 门户管理/日志中心/日志备份

```json
{
  "requeridClusterAddonFeatures": [
    "elk/ElasticsearchEnableBackup",
  ]
}
```

### 4.6.2. 相关代码
#### 4.6.2.1. 日志查询
需要在对应的日志查询开始前，检查`ELK`组件是否安装，状态是否正常。

```java
public interface LogService {

    LogQuery transLogQuery(LogQueryDTO logQueryDto) throws Exception;

    /**
     * ES条件
     * @param logQueryDto 条件
     * @param cluster 集群信息
     * @return ES条件
     */
    LogQuery transLogQuery(LogQueryDTO logQueryDto, Cluster cluster) throws Exception;


    List<LogDirDTO> listLogDirs(LogQuery logQuery) throws Exception;

    TreeSet<LogFileDTO> listfileName(LogQuery logQuery) throws Exception;

    TreeSet<LogFileDTO> listfileName(LogQuery logQuery, Cluster cluster) throws Exception;

    Map<String,Object> getLogContents(LogQuery logQuery)throws Exception;

    /**
     * 根据集群信息查询日志
     * @param logQuery ES条件
     * @param cluster 集群信息
     * @return 日志
     */
    Map<String, Object> getLogContents(LogQuery logQuery, Cluster cluster) throws Exception;

    /**
     * 将es的查询结果导出txt文件
     */
    void exportLog(LogQuery logQuery, HttpServletResponse response) throws Exception;

    /**
     * 根据集群查询日志
     *
     * @param logQuery ES条件
     * @param response response
     * @param cluster  集群信息
     */
    void exportLog(LogQuery logQuery, HttpServletResponse response, Cluster cluster) throws Exception;

    /**
     * 查询log上下文
     * @param logQuery ES条件
     * @param upOrDown 上、下
     * @param timestamp 时间戳
     * @return log上下文
     */
    Map<String, Object> getLogContext(LogQuery logQuery, String upOrDown, String timestamp) throws Exception;

    /**
     * 根据集群查询log上下文
     * @param logQuery ES条件
     * @param upOrDown 上、下
     * @param timestamp 时间戳
     * @param cluster 集群信息
     * @return log上下文
     */
    Map<String, Object> getLogContext(LogQuery logQuery, String upOrDown, String timestamp, Cluster cluster) throws Exception;

    ExportInfoDTO getExportInfo(String clusterName, String namespace, String podName);
}
```

#### 4.6.2.2. 日志备份
日志备份管理,需要在查询前判断**日志备份功能**是否开启。

```java
/**
 * 应用日志接口
 * 目前主要为备份业务
 */
public interface AppLogService {

    /**
     * 创建日志备份规则
     * @param appLogDtoIn
     */
    void setLogBackupRule(AppLogDTO appLogDtoIn);

    /**
     * 查询日志备份规则
     * @return
     */
    List<LogBackupRule> listLogBackupRules(String clusterNames, Boolean available);

    /**
     * 更新日志备份规则
     * @param appLogDtoIn
     */
    void updateLogBackupRule(AppLogDTO appLogDtoIn);

    /**
     * 删除日志备份规则
     * @param ruleId
     */
    void deleteLogBackupRule(Integer ruleId);

    /**
     * 停止日志备份规则
     * @param ruleId
     */
    void stopLogBackupRule(Integer ruleId);

    /**
     * 启动日志备份规则
     * @param ruleId
     */
    void startLogBackupRule(Integer ruleId);

    /**
     * 备份应用日志
     */
    void backupAppLog();
}
```

## 4.7. 监控Monitoring
### 4.7.1. 特性功能
#### *******. 功能点
```yaml
apiVersion: unified-platform.harmonycloud.cn/v1alpha1
kind: EnhanceClusterAddon
metadata:
  finalizers:
    - enhanceclusteraddon.unifiedplatform.harmonycloud.cn/finalizer
  generation: 3
  name: monitoring
  namespace: stellaris-workspace-cluster161
spec:
  addonRef:
    name: monitoring
    namespace: stellaris-workspace-cluster161
  builtinFeatures:
    - Installed
    - Ready
  features:
    - description: check grafana status
      healthChecks:
        - collector:
            kubernetesObjectRef: {}
          filters:
            - jsonpath:
                expression: $.status.healthCheck.conditions[?(@.type=="DashboardUnhealthy")].status
          matchers:
            - matchExpressions:
                - operator: Equal
                  values:
                    - "False"
          name: check grafana status
      name: GrafanaReady
    - description: check prometheus status
      healthChecks:
        - collector:
            kubernetesObjectRef: {}
          filters:
            - jsonpath:
                expression: $.status.healthCheck.conditions[?(@.type=="StorageUnhealthy")].status
          matchers:
            - matchExpressions:
                - operator: Equal
                  values:
                    - "False"
          name: check prometheus storage status
        - collector:
            kubernetesObjectRef: {}
          filters:
            - jsonpath:
                expression: $.status.healthCheck.conditions[?(@.type=="CollectionUnhealthy")].status
          matchers:
            - matchExpressions:
                - operator: Equal
                  values:
                    - "False"
          name: check prometheus collection status
        - collector:
            http:
              method: GET
              timeout: 5s
              urlTemplate: '{{- $cfg := .status.configurations.configurationSchemaData.monitoring }}{{ printf "%s://%s:%s/api/v1/query?query=1" $cfg.protocol $cfg.ip $cfg.port }}'
          filters:
            - http:
                extractField: body
            - jsonpath:
                expression: $.status
          matchers:
            - matchExpressions:
                - operator: Equal
                  values:
                    - success
          name: check prometheus query status
      logicExpression: 0 AND 1 AND 2
      name: PrometheusReady
    - description: check alert manager status
      healthChecks:
        - collector:
            kubernetesObjectRef: {}
          filters:
            - jsonpath:
                expression: $.status.healthCheck.conditions[?(@.type=="AlertPrometheusUnhealthy")].status
          matchers:
            - matchExpressions:
                - operator: Equal
                  values:
                    - "False"
          name: check alert manager status
        - collector:
            http:
              method: GET
              timeout: 5s
              urlTemplate: '{{- $cfg := .status.configurations.configurationSchemaData.monitoring }}{{ printf "%s://%s:%s/api/v2/status" $cfg.alertProtocol $cfg.alertIP $cfg.alertPort }}'
          filters:
            - http:
                extractField: statusCode
          matchers:
            - matchExpressions:
                - operator: Equal
                  values:
                    - "200"
          name: check alert manager connection status
      logicExpression: 0 AND 1
      name: AlertManagerReady
status:
  addonPhase: Abnormal
  enabledBuiltinFeatures:
    - Installed
    - Ready
  featureConditions:
    - builtin: true
      lastTransitionTime: "2025-06-07T10:35:06Z"
      name: Installed
      status: "True"
    - builtin: true
      lastTransitionTime: "2025-06-07T10:37:06Z"
      message: 'ClusterAddon ''stellaris-workspace-cluster161/monitoring'' has failed HealthCheck condition: StorageUnhealthy'
      name: Ready
      reason: HealthCheckFailed
      status: "False"
    - healthChecks:
        - actualValue: '"False"'
          lastTransitionTime: "2025-06-07T10:36:08Z"
          matched: true
          message: Health check probe matched successfully
          name: check grafana status
          reason: Healthy
      lastTransitionTime: "2025-06-07T10:36:08Z"
      message: health check passed
      name: GrafanaReady
      reason: ProbeSucceeded
      status: "True"
    - healthChecks:
        - actualValue: '"True"'
          lastTransitionTime: "2025-06-07T10:36:14Z"
          matched: false
          message: Health check probe did not match
          name: check prometheus storage status
          reason: MatcherMismatchError
        - actualValue: '"False"'
          lastTransitionTime: "2025-06-07T10:36:03Z"
          matched: true
          message: Health check probe matched successfully
          name: check prometheus collection status
          reason: Healthy
        - actualValue: '"success"'
          lastTransitionTime: "2025-06-07T10:36:23Z"
          matched: true
          message: Health check probe matched successfully
          name: check prometheus query status
          reason: Healthy
      lastTransitionTime: "2025-06-07T10:36:03Z"
      logicExpression: 0 AND 1 AND 2
      message: has failed health checks
      name: PrometheusReady
      reason: ProbeFailed
      status: "False"
    - healthChecks:
        - actualValue: '"True"'
          lastTransitionTime: "2025-06-07T10:36:55Z"
          matched: false
          message: Health check probe did not match
          name: check alert manager status
          reason: MatcherMismatchError
        - actualValue: "200"
          lastTransitionTime: "2025-06-07T10:37:05Z"
          matched: true
          message: Health check probe matched successfully
          name: check alert manager connection status
          reason: Healthy
      lastTransitionTime: "2025-06-07T10:36:55Z"
      logicExpression: 0 AND 1
      message: has failed health checks
      name: AlertManagerReady
      reason: ProbeFailed
      status: "False"
  lastTransitionTime: "2025-06-07T10:41:06Z"
  message: Feature Runners Synced
  phase: Synced
  supportedBuiltinFeatures:
    - Installed
    - Ready

```

| **<font style="background-color:rgb(231, 233, 232);">功能点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台组件</font>** | **<font style="background-color:rgb(231, 233, 232);">能力点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台页面</font>** |
| :--- | :--- | :--- | :--- |
| 门户管理/门户告警 | cloudmonitor-apm | 依赖promehtues，alertManager<br/>粗略点只要求**MonitoringReady**<br/>细分则为<br/>**PrometheusReady**<br/>**AlertManagerReady** | [http://*************/?isBackend=1&navCode=platform#/log/logQuery](http://*************/?isBackend=1&navCode=platform#/log/logQuery) |
| 门户管理/集群监控   产品管理/产品及组件监控 | olympus-portal | 依赖promehtues，grafana<br/>粗略点只要求**MonitoringReady**<br/>细分则为<br/>**PrometheusReady**<br/>**GrafanaReady** | [http://*************/?isBackend=1&navCode=platform#/monitoring](http://*************/?isBackend=1&navCode=platform#/monitoring) |
| 门户管理/门户运维/集群巡检 | caas-oam | **Ready** | |


#### 4.7.1.2. 权限点
菜单权限点要求开启的功能

+ 门户告警

```json
{
  "requeridClusterAddonFeatures": [
    // 监控正常运行
    "monitoring/PrometheusReady",
    "monitoring/AlertmanagerReady",
  ]
}
```

+ 门户监控

```json
{
  "requeridClusterAddonFeatures": [
    // 监控正常运行
    "monitoring/PrometheusReady",
  ]
}
```

+ 集群巡检

```json
{
  "requeridClusterAddonFeatures": [
    // 监控正常运行
    "monitoring/PrometheusReady",
  ]
}
```

### 4.7.2. 相关代码
#### 4.7.2.1. 告警
目前告警代码在监控的`apm`项目中，待定。

#### 4.7.2.2. 集群监控
集群监控及组件监控目前实现都是基于内嵌`gafana`大盘，如果`grafana`不可用不会导致平台核心功能不可用，可以延后或者不处理。  
内嵌通过`iframe`形式实现，**<font style="color:rgb(223, 42, 63);">需要前端通过权限点去判断。</font>**

#### 4.7.2.3. 集群巡检
caas-oam

在获取报告的接口中判断，集群组件monitoring 和 prometheus状态。

```java
public class MetricsReportServiceImpl implements MetricsReportService {

    public List<HealthMetricsThresholdDTO> getReportData(String clusterName) throws Exception {

    }
}
```

获取巡检报告接口

```java
/**
 * 指标报告接口
 *
 * @auther damiao
 * @date 2019-11-14
 */
public interface MetricsReportService {

    /**
     * 导出集群指标报告
     *
     * @param clusterName
     * @return
     */
    List<HealthMetricsThresholdDTO> exportReportByClusterName(String clusterName, HttpServletResponse response, HttpServletRequest request)  throws Exception;

    /**
     * 根据集群id生成报告
     * @param clusterName
     * @return
     * @throws Exception
     */
    Map<String,Object> getReportByClusterName(String clusterName)  throws Exception;

    /**
     * 根据集群id获取异常指标
     *
     * @param clusterName
     * @return
     * @throws Exception
     */
    Map<String, Map<String, Map<String, List<HealthMetricsThresholdDTO>>>> getAbnormalMetricsBycluster(String clusterName) throws Exception;

    /**
     * <AUTHOR>
     * @Date 2019/11/22 11:02 上午
     * @Description 构建主机指标数据的映射
     * @param metricsThresholdPOList
     * @return java.util.Map<java.lang.String,java.util.Map<java.lang.String,java.util.List<HealthMetricsThresholdDTO>>>
     * 返回结果结构如下：
     * {
     *     "node1":{
     *         "nodeBase":[
     *             "HealthMetricsThresholdDTO",
     *             "HealthMetricsThresholdDTO"
     *         ],
     *         "nodeNetwork":[
     *             "HealthMetricsThresholdDTO",
     *             "HealthMetricsThresholdDTO"
     *         ],
     *         "nodeOther":[
     *             "HealthMetricsThresholdDTO",
     *             "HealthMetricsThresholdDTO"
     *         ]
     *     },
     *     "node2":{
     *         "nodeBase":[
     *             "HealthMetricsThresholdDTO",
     *             "HealthMetricsThresholdDTO"
     *         ],
     *         "nodeNetwork":[
     *             "HealthMetricsThresholdDTO",
     *             "HealthMetricsThresholdDTO"
     *         ],
     *         "nodeOther":[
     *             "HealthMetricsThresholdDTO",
     *             "HealthMetricsThresholdDTO"
     *         ]
     *     }
     * }
     */
    public Map<String, Map<String, List<HealthMetricsThresholdDTO>>>  getNodeMap(List<HealthMetricsThresholdDTO> metricsThresholdPOList);

    /**
     * <AUTHOR>
     * @Date 2019/11/22 10:59 上午
     * @Description 构建组件指标数据的映射
     * @param metricsThresholdPOList
     * @return java.util.Map<java.lang.String,java.util.List<HealthMetricsThresholdDTO>>
     * 返回结果结构如下：
     * {
     *     "sysNamespace":[
     *         "HealthMetricsThresholdDTO",
     *         "HealthMetricsThresholdDTO"
     *     ],
     *     "moduleResource":[
     *         "HealthMetricsThresholdDTO",
     *         "HealthMetricsThresholdDTO"
     *     ]
     * }
     */
    public Map<String, List<HealthMetricsThresholdDTO>>
    getModuleMap(List<HealthMetricsThresholdDTO> metricsThresholdPOList);

    /**
     * <AUTHOR>
     * @Date 2019/11/25 7:34 下午
     * @Description 发送邮件，生成pdf
     * @param errorResultMap
     * @param cluster
     * @param fileName
     * @return java.lang.String
     */
    String createPdfForMail(Map<String,Map<String, Map<String, List<HealthMetricsThresholdDTO>>>> errorResultMap, Cluster cluster, String fileName) throws Exception;

    /**
     * 创建PDF信息
     * @param cluster
     * @param nodeMetricsMap
     * @param moduleMetricsMap
     * @return
     */
    String createHtml(Cluster cluster, Map<String, Map<String, List<HealthMetricsThresholdDTO>>> nodeMetricsMap,
                      Map<String, List<HealthMetricsThresholdDTO>> moduleMetricsMap);

    /**
     * 根据集群ID获取指标数据
     * @param clusterName
     * @return
     * @throws Exception
     */
    List<HealthMetricsThresholdDTO> getReportData(String clusterName) throws Exception;
}
```

## 4.8. 故障隔离**problem-isolation**
### 4.8.1. 特性功能
#### *******. 功能点
```yaml
apiVersion: unified-platform.harmonycloud.cn/v1alpha1
kind: EnhanceClusterAddon
metadata:
  finalizers:
    - enhanceclusteraddon.unifiedplatform.harmonycloud.cn/finalizer
  generation: 1
  name: problem-isolation
  namespace: stellaris-workspace-cluster161
spec:
  addonRef:
    name: problem-isolation
    namespace: stellaris-workspace-cluster161
  builtinFeatures:
    - Installed
    - Ready
status:
  addonPhase: Ready
  enabledBuiltinFeatures:
    - Installed
    - Ready
  featureConditions:
    - builtin: true
      lastTransitionTime: "2025-06-07T10:38:39Z"
      name: Installed
      status: "True"
    - builtin: true
      lastTransitionTime: "2025-06-07T10:38:39Z"
      name: Ready
      status: "True"
  lastTransitionTime: "2025-06-07T10:45:07Z"
  message: Feature Runners Synced
  phase: Synced
  supportedBuiltinFeatures:
    - Installed
    - Ready

```

| **<font style="background-color:rgb(231, 233, 232);">功能点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台组件</font>** | **<font style="background-color:rgb(231, 233, 232);">能力点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台页面</font>** |
| :--- | :--- | :--- | :--- |
| 资源中心/集群管理/主机管理故障隔离 | olympus-core | Ready | [http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr&isSubMenu=1&clusterName=cluster-187#/cluster/space/node/normal/detail/cluster-187-node0009.10.10.103.194](http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr&isSubMenu=1&clusterName=cluster-187#/cluster/space/node/normal/detail/cluster-187-node0009.10.10.103.194) |
| 门户管理/门户运维/故障隔离 | olympus-core | Ready | [http://*************/?isBackend=1&navCode=platform#/systemaudit/faultsolation](http://*************/?isBackend=1&navCode=platform#/systemaudit/faultsolation) |
| 容器服务/应用发布/故障隔离   容器服务/工作负载管理/故障隔离 | caas-core | Ready | |


#### 4.8.1.2. 权限点
```json
{
  "requeridClusterAddonFeatures": [
    // 监控正常运行
    "problem-isolation/Ready",
  ]
}
```

### 4.8.2. 代码改造
故障隔离功能如果无法正常运行，只会导致该功能异常，不影响主流程运行，但是需要提示。

无需代码改造。

## 4.9. 虚拟机kubevirt+kubeovn
### 4.9.1. 特性功能
#### 4.9.1.1. 功能点
+ kubevirt

```yaml
apiVersion: unified-platform.harmonycloud.cn/v1alpha1
kind: EnhanceClusterAddon
metadata:
  finalizers:
    - enhanceclusteraddon.unifiedplatform.harmonycloud.cn/finalizer
  generation: 1
  name: kubevirt
  namespace: stellaris-workspace-cluster161
spec:
  addonRef:
    name: kubevirt
    namespace: stellaris-workspace-cluster161
  builtinFeatures:
    - Installed
    - Ready
status:
  addonPhase: Pending
  enabledBuiltinFeatures:
    - Installed
    - Ready
  featureConditions:
    - builtin: true
      lastTransitionTime: "2025-06-07T10:38:39Z"
      message: ClusterAddon 'stellaris-workspace-cluster161/kubevirt' not found.
      name: Installed
      reason: AddonNotFound
      status: "False"
    - builtin: true
      lastTransitionTime: "2025-06-07T10:38:39Z"
      message: ClusterAddon 'stellaris-workspace-cluster161/kubevirt' not found.
      name: Ready
      reason: AddonNotFound
      status: "False"
  lastTransitionTime: "2025-06-07T10:46:07Z"
  message: Feature Runners Synced
  phase: Synced
  supportedBuiltinFeatures:
    - Installed
    - Ready

```

+ kubeovn

```yaml
apiVersion: unified-platform.harmonycloud.cn/v1alpha1
kind: EnhanceClusterAddon
metadata:
  finalizers:
    - enhanceclusteraddon.unifiedplatform.harmonycloud.cn/finalizer
  generation: 1
  name: ovn
  namespace: stellaris-workspace-cluster161
spec:
  addonRef:
    name: ovn
    namespace: stellaris-workspace-cluster161
  builtinFeatures:
    - Installed
    - Ready
status:
  addonPhase: Ready
  enabledBuiltinFeatures:
    - Installed
    - Ready
  featureConditions:
    - builtin: true
      lastTransitionTime: "2025-06-07T10:38:38Z"
      name: Installed
      status: "True"
    - builtin: true
      lastTransitionTime: "2025-06-07T10:38:38Z"
      name: Ready
      status: "True"
  lastTransitionTime: "2025-06-07T10:46:06Z"
  message: Feature Runners Synced
  phase: Synced
  supportedBuiltinFeatures:
    - Installed
    - Ready

```

| **<font style="background-color:rgb(231, 233, 232);">功能点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台组件</font>** | **<font style="background-color:rgb(231, 233, 232);">能力点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台页面</font>** |
| :--- | :--- | :--- | :--- |
| 资源中心/网络管理/虚拟机网络 | olympus-core | ovn/Ready | [http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr#/networkmanage/virtualmachine](http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr#/networkmanage/virtualmachine) |
| 容器服务/虚拟机管理 | caas-core | ovn/Ready<br/>kubevirt/Ready | [http://*************/?isBackend=1&navCode=platform#/systemaudit/faultsolation](http://*************/?isBackend=1&navCode=platform#/systemaudit/faultsolation) |


#### *******. 权限点
+ 虚拟机网络

```json
{
  "requeridClusterAddonFeatures": [
    "ovn/Ready",
  ]
}
```

+ 虚拟机管理

```json
{
  "requeridClusterAddonFeatures": [
    // 正常运行
    "ovn/Ready",
    "kubevirt/Ready"
  ]
}
```

### 4.9.2. 代码改造
#### *******. 虚拟机网络
olympus-core

检测kubeovn 是否安装并且就绪

```java
/**
 * @Author: roy
 * @Date: 2023/12/4 16:48
 * @Description:
 */
public interface VirtualNetworkService {
    List<VlanDTO> listVirtualNetwork(String clusterName);

    List<VlanSubnetDTO> listVlanSubnetDTO(String clusterName, String subnetName);

    List<SubnetDTO> listOVNSubnetDTO(String clusterName, String subnetName);

    void updateSubnetDescription(String clusterName, String subnetName, String description);

    void createVlanSubnet(String clusterName, VlanSubnetDTO vlanSubnetDTO);

    List<SubnetDistributionDTO> getSubnetDistribution(String clusterName, String subnetName);

    void deleteSubnet(String clusterName, String subnetName);

    void setGlobalSharing(String clusterName, String subnetName, boolean globalSharing);

    void setSubnetOrganization(String clusterName, String subnetName, List<String> organizationIds);

    List<ProjectOrganizationSubnetDTO> getClusterSubnetCount(String organizationId, String projectId);

    List<SubnetDTO> getSystemSubnetUnassigned(String clusterName, String subnetName, String organizationId);

    void distributeSubnetToOrganization(List<DistributionRequest> distributionList, String organizationId);

    void deleteOrganizationFromSubnet(String clusterName, String subnetName, String organizationId);

    List<SubnetDTO> listOrganizationProjectVirtualNetwork(String clusterName, String organizationId, String projectId, String subnetName);

    List<SubnetDTO> listOrganizationProjectInfoVirtualNetwork(String clusterName, String organizationId, String subnetName);

    VlanSubnetDTO getVlanSubnetDTO(String clusterName, String organizationId, String subnetName);

    List<SubnetDistributionDTO> getSubnetProjectDistribution(String clusterName, String organizationId, String subnetName);

    void setSubnetProject(String clusterName, String subnetName, String organizationId, List<String> projectIds);

    List<SubnetDTO> getOrganizationSubnetUnassigned(String clusterName, String subnetName, String organizationId, String projectId);

    void distributeSubnetToProject(List<DistributionRequest> distributionList, String organizationId, String projectId);

    void deleteProjectFromSubnet(String clusterName, String subnetName, String organizationId, String projectId);

    VlanSubnetDTO getOrganizationProjectSubnet(String clusterName, String subnetName);

    void checkSubnetParam(String clusterName, VlanSubnetRequest request);

    List<String> getSubnetUnit(String clusterName, String subnetName);
}
```

#### *******. 虚拟机管理
caas-core

检查kubevirt是否安装，并且相关配置是否正确

```java
package com.skyview.caas.core.service.kubevirt;

import com.skyview.caas.common.model.EventDTO;
import com.skyview.caas.model.cluster.dto.kubevirt.VirtualMachineDTO;

import java.util.List;

/**
 * @Author: roy
 * @Date: 2023/11/28 10:53
 * @Description:
 */
public interface VirtualMachineService {
    VirtualMachineDTO getVirtualMachine(String organizationId, String projectId, String name, String namespace, String clusterName);

    List<VirtualMachineDTO> ListVirtualMachines(String organizationId, String projectId, String namespace, String clusterName);

    String getVirtualMachinesVNCAddress(String name, String namespace, String clusterName);

    void createVirtualMachine(VirtualMachineDTO virtualMachineDTO);

    void deleteVirtualMachine(String organizationId, String projectId, String name, String namespace, String clusterName);

    void updateVirtualMachine(VirtualMachineDTO virtualMachineDTO);

    void startVirtualMachine(String name, String namespace, String clusterName);

    void stopVirtualMachine(String name, String namespace, String clusterName);

    void restartVirtualMachine(String name, String namespace, String clusterName);

    List<EventDTO> getVirtualMachineEvents(String name, String namespace, String clusterName);

}
```

## 4.10. **扩缩容hpa**
### 4.10.1. **特性功能**
#### ********. **功能点**
```yaml
apiVersion: unified-platform.harmonycloud.cn/v1alpha1
kind: EnhanceClusterAddon
metadata:
  finalizers:
    - enhanceclusteraddon.unifiedplatform.harmonycloud.cn/finalizer
  generation: 1
  name: hpa
  namespace: stellaris-workspace-cluster161
spec:
  addonRef:
    name: hpa
    namespace: stellaris-workspace-cluster161
  builtinFeatures:
    - Installed
    - Ready
status:
  addonPhase: Ready
  enabledBuiltinFeatures:
    - Installed
    - Ready
  featureConditions:
    - builtin: true
      lastTransitionTime: "2025-06-07T10:38:38Z"
      name: Installed
      status: "True"
    - builtin: true
      lastTransitionTime: "2025-06-07T10:38:38Z"
      name: Ready
      status: "True"
  lastTransitionTime: "2025-06-07T10:46:37Z"
  message: Feature Runners Synced
  phase: Synced
  supportedBuiltinFeatures:
    - Installed
    - Ready

```

| **<font style="background-color:rgb(231, 233, 232);">功能点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台组件</font>** | **<font style="background-color:rgb(231, 233, 232);">能力点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台页面</font>** |
| :--- | :--- | :--- | :--- |
| **容器服务/单集群应用/HPA管理**   **容器服务/多集群应用/HPA管理** | **caas-core** | **hpa/Ready** | [http://*************/?isBackend=1&navCode=platform#/systemaudit/faultsolation](http://*************/?isBackend=1&navCode=platform#/systemaudit/faultsolation) |


#### 4.10.1.2. **权限点**
```json
{
  "requeridClusterAddonFeatures": [
    // 正常运行
    "hpa/ready",
  ]
}
```

### 4.10.2. **代码改造**
#### 4.10.2.1. **应用hpa管理**
**caas-core**

hpa不做改造，如果没有装只会影响到应用扩缩容，不影响主流程，可以添加相应组件没有安装的提示。

## 4.11. 备份还原velero
### 4.11.1. **特性功能**
#### 4.11.1.1. **功能点**
```yaml
apiVersion: unified-platform.harmonycloud.cn/v1alpha1
kind: EnhanceClusterAddon
metadata:
  finalizers:
    - enhanceclusteraddon.unifiedplatform.harmonycloud.cn/finalizer
  generation: 1
  name: velero
  namespace: stellaris-workspace-cluster161
spec:
  addonRef:
    name: velero
    namespace: stellaris-workspace-cluster161
  builtinFeatures:
    - Installed
    - Ready
status:
  addonPhase: Abnormal
  enabledBuiltinFeatures:
    - Installed
    - Ready
  featureConditions:
    - builtin: true
      lastTransitionTime: "2025-06-07T10:38:40Z"
      message: ClusterAddon 'stellaris-workspace-cluster161/velero' status endpoints is empty.
      name: Installed
      reason: ElasticsearchCheckHealthError
      status: "False"
    - builtin: true
      lastTransitionTime: "2025-06-07T10:46:07Z"
      message: 'ClusterAddon ''stellaris-workspace-cluster161/velero'' has failed HealthCheck condition: AgentUnhealthy'
      name: Ready
      reason: HealthCheckFailed
      status: "False"
  lastTransitionTime: "2025-06-07T10:47:07Z"
  message: Feature Runners Synced
  phase: Synced
  supportedBuiltinFeatures:
    - Installed
    - Ready

```

| **<font style="background-color:rgb(231, 233, 232);">功能点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台组件</font>** | **<font style="background-color:rgb(231, 233, 232);">能力点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台页面</font>** |
| :--- | :--- | :--- | :--- |
| 门户管理/门户运维 | olympus-portal | velero/Ready | [http://*************/?isBackend=1&navCode=platform#/systemaudit/backupRecovery](http://*************/?isBackend=1&navCode=platform#/systemaudit/backupRecovery) |


#### 4.11.1.2. **权限点**
```json
{
  "requeridClusterAddonFeatures": [
    // 正常运行
    "velero/Ready",
  ]
}
```

### 4.11.2. **代码改造**
#### 4.11.2.1. **备份还原**
**olympus-portal**

备份还原相关接口，需要在执行对应的备份还原任务时，检测velero是否安装，是否正常。

```go
package backuprestore


type BackupInterface interface {
    ListBackups(ctx context.Context, clusterName string, matchLabels map[string]string, backupStatus string) ([]velero.Backups, error)

    DeleteBackups(ctx context.Context, clusterName, backupName string) error

    CreateBackupsBySchedules(ctx context.Context, clusterName, schedulesName string) error

    ListBackupsResources(ctx context.Context, clusterName, schedulesName, backupName string) ([]velero.VeleroResources, error)

    LogsBackups(ctx context.Context, clusterName, schedulesName, backupName string) (string, error)

    SolveBackupSize()
}



type PodVolumeBackupInterface interface {
    ListPodVolumeBackup(ctx context.Context, clusterName string, matchLabels map[string]string) ([]velero.PodVolumeBackup, error)

    ListBackupVolumes(ctx context.Context, clusterName, scheduleName, backupName string) ([]velero.VeleroVolumes, error)
}

type PodVolumeRestoreInterface interface {
    ListPodVolumeRestore(ctx context.Context, clusterName string, matchLabels map[string]string) ([]velero.PodVolumeRestore, error)

    ListRestoreVolumes(ctx context.Context, clusterName, restoreTemplateId, restoreName string) ([]velero.VeleroVolumes, error)
}


type RestoreInterface interface {
    ListRestore(ctx context.Context, clusterName string, matchLabels map[string]string) ([]velero.Restore, error)

    ListRestoresResources(ctx context.Context, clusterName, restoreName string) ([]velero.VeleroResources, error)

    CreateRestore(ctx context.Context, template velero.RestoreTemplate) error

    DeleteRestore(ctx context.Context, clusterName, restoresTemplateId string) error

    LogsRestore(ctx context.Context, clusterName, restoreName string) (string, error)

    PreflightRestoreTemplate(ctx context.Context, template *velero.RestoreTemplate)

    SolveRestoreSize()
}



type RestoreTemplateInterface interface {
    ListRestoreTemplates(ctx context.Context, fromCluster string, targetCluster string) ([]velero.RestoreTemplate, error)

    // ListRestore id为空则有集群查集群所有，没集群差所有集群；id不为空则查该id对应的，集群传则按照集群查，集群不传则会先去数据库查对应集群
    ListRestore(ctx context.Context, clusterName, restoresTemplateId string) ([]velero.Restore, error)

    CreateRestoreTemplate(ctx context.Context, template velero.RestoreTemplate) (string, error)

    EditRestoreTemplate(ctx context.Context, restoresTemplateId string, template velero.RestoreTemplate) error

    ListResourceType(ctx context.Context, cluster string, backupName string, namespaces []string) ([]velero.ResourceByType, error)

    GetPreflightResult(ctx context.Context, restoreTemplateId string) []velero.CompareResult

    GetRestoreTemplate(ctx context.Context, restoresTemplateId string) (*velero.RestoreTemplate, error)

    DeleteRestoreTemplate(ctx context.Context, restoresTemplateId string) error
}


type SchedulerInterface interface {
    ListSchedulers(ctx context.Context, clusterName string) ([]velero.Schedules, error)

    GetSchedulers(ctx context.Context, clusterName, schedulersName string) (*velero.Schedules, error)

    CreateSchedulers(ctx context.Context, clusterName string, schedules velero.Schedules) error

	UpdateSchedulers(ctx context.Context, clusterName string, schedules velero.Schedules) error

	DeleteSchedulers(ctx context.Context, clusterName, schedulesName string) error
}
```

## 4.12. GPU
### 4.12.1. **特性功能**
#### 4.12.1.1. **功能点**
```yaml
apiVersion: unified-platform.harmonycloud.cn/v1alpha1
kind: EnhanceClusterAddon
metadata:
  finalizers:
    - enhanceclusteraddon.unifiedplatform.harmonycloud.cn/finalizer
  generation: 1
  name: gpu
  namespace: stellaris-workspace-cluster161
spec:
  addonRef:
    name: gpu
    namespace: stellaris-workspace-cluster161
  builtinFeatures:
    - Installed
    - Ready
status:
  addonPhase: Abnormal
  enabledBuiltinFeatures:
    - Installed
    - Ready
  featureConditions:
    - builtin: true
      lastTransitionTime: "2025-06-07T10:38:38Z"
      name: Installed
      status: "True"
    - builtin: true
      lastTransitionTime: "2025-06-07T10:47:07Z"
      message: 'ClusterAddon ''stellaris-workspace-cluster161/gpu'' has failed HealthCheck condition: ExporterUnhealthy'
      name: Ready
      reason: HealthCheckFailed
      status: "False"
  lastTransitionTime: "2025-06-07T10:47:37Z"
  message: Feature Runners Synced
  phase: Synced
  supportedBuiltinFeatures:
    - Installed
    - Ready

```

| **<font style="background-color:rgb(231, 233, 232);">功能点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台组件</font>** | **<font style="background-color:rgb(231, 233, 232);">能力点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台页面</font>** |
| :--- | :--- | :--- | :--- |
| 集群管理/主机管理/支持节点GPU物理卡的算力和显存虚拟化 | olympus-core | gpu/Ready | [http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr&isSubMenu=1&clusterName=cluster-187#/cluster/space/node/normal/detail/cluster-187-node0002.10.10.103.189](http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr&isSubMenu=1&clusterName=cluster-187#/cluster/space/node/normal/detail/cluster-187-node0002.10.10.103.189) |
| 容器服务/应用管理/支持GPU应用 | caas-core | gpu/Ready | |


#### 4.12.1.2. **权限点**
```json
{
  "requeridClusterAddonFeatures": [
    // 正常运行
    "gpu/ready",
  ]
}
```

### 4.12.2. **代码改造**
目前若没有安装`GPU`组件影响不到主流程运行，且`GPU`组件相关代码已在`olympus-core`的代码中做了相应兼容，如果还需要做出相应提示，则需要前端判断。

没有安装 -> gpuType=none 

安装tencent -> gpuType=tencent 

安装orion -> gpuType=orion

```java
private void setClusterGpu(Cluster cluster) {
try {
    V1ClusterAddon v1ClusterAddon = this.readClusterAddon(cluster.getName(),ComponentEnum.GPU.getEnName());
    if (null == v1ClusterAddon) {
        cluster.setGpuType(ResourceConstant.NO_GPU);
        return;
    }
    if (v1ClusterAddon.getStatus().getConfigurations() != null) {
        JSONObject data = v1ClusterAddon.getStatus().getConfigurations().getConfigurationSchemaData();
        JSONObject gpu = data.getJSONObject("gpu");
        if (null == gpu) {
            cluster.setGpuType(ResourceConstant.NO_GPU);
            return;
        }
        JSONObject gpuCueRender = gpu.getJSONObject("gpuCueRender");
        if (null == gpuCueRender) {
            cluster.setGpuType(ResourceConstant.NO_GPU);
            return;
        }
        String gpuType = gpuCueRender.getString("gpuType");
        if (StringUtils.isBlank(gpuType)) {
            cluster.setGpuType(ResourceConstant.NO_GPU);
        } else if (gpuType.equals("gpuManager")) {
            cluster.setGpuType(ResourceConstant.TENCENT_DRIVE);
        } else if (gpuType.equals("orion")) {
            cluster.setGpuType(ResourceConstant.ORION_DRIVE);
        } else {
            cluster.setGpuType(gpuType);
        }
        return;
    }
} catch (ApiException e) {
    if (e.getCode() != 404) {
        log.error("fail cluster is {}", cluster.getName());
        log.error("", e);
    } else {
        log.debug("GPU 未接入 cluster is {}", cluster.getName());
    }
}
cluster.setGpuType(ResourceConstant.NO_GPU);
}
```

## 4.13. 网络隔离acl
### 4.13.1. **特性功能**
#### 4.13.1.1. **功能点**
```yaml
apiVersion: unified-platform.harmonycloud.cn/v1alpha1
kind: EnhanceClusterAddon
metadata:
  finalizers:
    - enhanceclusteraddon.unifiedplatform.harmonycloud.cn/finalizer
  generation: 1
  name: acl
  namespace: stellaris-workspace-cluster161
spec:
  addonRef:
    name: acl
    namespace: stellaris-workspace-cluster161
  builtinFeatures:
    - Installed
    - Ready
status:
  addonPhase: Ready
  enabledBuiltinFeatures:
    - Installed
    - Ready
  featureConditions:
    - builtin: true
      lastTransitionTime: "2025-06-07T10:38:38Z"
      name: Installed
      status: "True"
    - builtin: true
      lastTransitionTime: "2025-06-07T10:38:38Z"
      name: Ready
      status: "True"
  lastTransitionTime: "2025-06-07T10:48:06Z"
  message: Feature Runners Synced
  phase: Synced
  supportedBuiltinFeatures:
    - Installed
    - Ready

```

| **<font style="background-color:rgb(231, 233, 232);">功能点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台组件</font>** | **<font style="background-color:rgb(231, 233, 232);">能力点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台页面</font>** |
| :--- | :--- | :--- | :--- |
| 集群管理/集群空间/网络策略 | olympus-core | acl/Ready | [http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr&isSubMenu=1&clusterName=cluster-187#/cluster/space/network/strategy](http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr&isSubMenu=1&clusterName=cluster-187#/cluster/space/network/strategy) |


#### 4.13.1.2. **权限点**
```json
{
  "requeridClusterAddonFeatures": [
    // 正常运行
    "acl/Ready",
  ]
}
```

### 4.13.2. **代码改造**
olympus-core

需要在网络隔离策略相关方法执行前，判断网络隔离是否开启。

```java
/**
 * <AUTHOR>
 * @date 2023/2/21 16:52
 */
public interface SystemPodPolicyService {

    List<PodPolicyDTO> listPodPolicy(String clusterName, String namespace);

    List<PodPolicyDTO> listPodPolicy(String namespace);

    void deletePodPolicy(String podPolicyName, String clusterName, String namespace);

    void enablePodPolicy(String podPolicyName, String clusterName, String namespace, boolean hasEnable);

    void disablePodPolicy(String podPolicyName, String clusterName, String namespace, boolean hasEnable);

    void createPodPolicy(PodPolicyDetailDTO podPolicyDetailDTO);

    PodPolicyDetailDTO getPodPolicy(String podPolicyName, String clusterName, String namespace);

    AsyncDeleteNamespaceMessage deleteListPodPolicy(List<ResourcesListDTO> resourcesListDTOList);

    void updatePodPolicy(String podPolicyName, PodPolicyDetailDTO podPolicyDetailDTO);

    PodPolicyOverviewDTO getPodPolicyOverview(String clusterName);

    List<PodPolicyNetworkDTO> listPodPolicyNetworkSegment();
}
```

## 4.14. 统一网络模型**heimdallr**
### 4.14.1. **特性功能**
#### 4.14.1.1. **功能点**
+ heimdallr 统一网络模型

```yaml
apiVersion: unified-platform.harmonycloud.cn/v1alpha1
kind: EnhanceClusterAddon
metadata:
  name: heimdallr
  namespace: stellaris-workspace-cluster-187
spec:
  addonRef:
    name: heimdallr
    namespace: stellaris-workspace-cluster-187
  features: []
status:
  phase: Synced
  addonPhase: Ready
  lastUpdateTime: "2024-05-01T12:00:00Z"
  featureConditions:
    - name: installed
      builtin: true
      status: "True"
      lastTransitionTime: "2024-05-01T12:00:00Z"
    - name: ready
      builtin: true
      status: "True"
      lastTransitionTime: "2024-05-01T12:00:00Z"
```

| **<font style="background-color:rgb(231, 233, 232);">功能点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台组件</font>** | **<font style="background-color:rgb(231, 233, 232);">能力点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台页面</font>** |
| :--- | :--- | :--- | :--- |
| 资源中心/网络管理/网络规划 | olympus-core | heimdallr/ready | [http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr#/networkmanage/resource](http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr#/networkmanage/resource) |
| 资源中心/网络管理/网络域 | olympus-core | heimdallr/ready | [http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr#/quotacenter/networkarea](http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr#/quotacenter/networkarea) |
| 资源中心/网络管理/网络IP池 | olympus-core | heimdallr/ready | [http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr#/quotacenter/ippool](http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr#/quotacenter/ippool) |
| 资源中心/网络管理/网络策略 | olympus-core | heimdallr/ready | [http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr#/networkmanage/space/strategy](http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr#/networkmanage/space/strategy) |
| 资源中心/网络管理/网络模板 | olympus-core | heimdallr/ready | [http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr#/networkmanage/space/template](http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr#/networkmanage/space/template) |
| 资源中心/网络管理/网络配置 | olympus-core | heimdallr/ready | [http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr#/networkmanage/space/setting](http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr#/networkmanage/space/setting) |


#### ********. **权限点**
```json
{
  "requeridClusterAddonFeatures": [
    // 正常运行
    "heimdallr/ready",
  ]
}
```

### 4.14.2. **代码改造**
olympus-core

需要在网络管理相关方法执行前，判断统一网络模型是否开启。

MultiNamespaceNetworkService.java

NamespaceNetworkService.java

NetworkAttachmentDefinitionService.java

NetworkCardService.java

NetworkTemplateService.java

OrganNetworkAreaService.java

OrganNetworkIpPoolService.java

ProjectNetworkAreaService.java

ProjectNetworkIpPoolService.java

ProjectNetworkTemplateService.java

SystemNetworkAreaService.java

SystemNetworkIpPoolService.java

SystemNetworkResourceService.java

## 4.15. 应用模型app-model,反编译app-**decompile和资源关联控制器resource-aggregate**
### 4.15.1. **特性功能**
#### ********. **功能点**
+ 应用模型

```yaml
apiVersion: unified-platform.harmonycloud.cn/v1alpha1
kind: EnhanceClusterAddon
metadata:
  finalizers:
    - enhanceclusteraddon.unifiedplatform.harmonycloud.cn/finalizer
  generation: 1
  name: app-model
  namespace: stellaris-workspace-cluster161
spec:
  addonRef:
    name: app-model
    namespace: stellaris-workspace-cluster161
  builtinFeatures:
    - Installed
    - Ready
status:
  addonPhase: Abnormal
  enabledBuiltinFeatures:
    - Installed
    - Ready
  featureConditions:
    - builtin: true
      lastTransitionTime: "2025-06-07T10:38:38Z"
      name: Installed
      status: "True"
    - builtin: true
      lastTransitionTime: "2025-06-07T10:38:38Z"
      message: 'ClusterAddon ''stellaris-workspace-cluster161/app-model'' has failed HealthCheck condition: AppRollbackUnHealthy'
      name: Ready
      reason: HealthCheckFailed
      status: "False"
  lastTransitionTime: "2025-06-07T10:48:07Z"
  message: Feature Runners Synced
  phase: Synced
  supportedBuiltinFeatures:
    - Installed
    - Ready

```

+ 反编译

```yaml
apiVersion: unified-platform.harmonycloud.cn/v1alpha1
kind: EnhanceClusterAddon
metadata:
  finalizers:
    - enhanceclusteraddon.unifiedplatform.harmonycloud.cn/finalizer
  generation: 1
  name: app-decompile
  namespace: stellaris-workspace-cluster161
spec:
  addonRef:
    name: app-decompile
    namespace: stellaris-workspace-cluster161
  builtinFeatures:
    - Installed
    - Ready
status:
  addonPhase: Ready
  enabledBuiltinFeatures:
    - Installed
    - Ready
  featureConditions:
    - builtin: true
      lastTransitionTime: "2025-06-07T10:38:38Z"
      name: Installed
      status: "True"
    - builtin: true
      lastTransitionTime: "2025-06-07T10:38:38Z"
      name: Ready
      status: "True"
  lastTransitionTime: "2025-06-07T10:48:37Z"
  message: Feature Runners Synced
  phase: Synced
  supportedBuiltinFeatures:
    - Installed
    - Ready

```

+ 关联控制器

```yaml
apiVersion: unified-platform.harmonycloud.cn/v1alpha1
kind: EnhanceClusterAddon
metadata:
  finalizers:
    - enhanceclusteraddon.unifiedplatform.harmonycloud.cn/finalizer
  generation: 1
  name: resource-aggregate
  namespace: stellaris-workspace-cluster161
spec:
  addonRef:
    name: resource-aggregate
    namespace: stellaris-workspace-cluster161
  builtinFeatures:
    - Installed
    - Ready
status:
  addonPhase: Ready
  enabledBuiltinFeatures:
    - Installed
    - Ready
  featureConditions:
    - builtin: true
      lastTransitionTime: "2025-06-07T10:38:39Z"
      name: Installed
      status: "True"
    - builtin: true
      lastTransitionTime: "2025-06-07T10:38:39Z"
      name: Ready
      status: "True"
  lastTransitionTime: "2025-06-07T10:48:37Z"
  message: Feature Runners Synced
  phase: Synced
  supportedBuiltinFeatures:
    - Installed
    - Ready

```

| **<font style="background-color:rgb(231, 233, 232);">功能点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台组件</font>** | **<font style="background-color:rgb(231, 233, 232);">能力点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台页面</font>** |
| :--- | :--- | :--- | :--- |
| 容器服务/单集群应用/应用发布 | caas-core | app-model/Ready   resource-aggregate/Ready | |
| 容器服务/单集群应用/应用发布组件配置能力 | caas-core | app-model/Ready   app-decompile/Ready | |
| 容器服务/单集群应用/应用发布后详情管理-关联资源 | caas-core | resource-aggregate/Ready | |
| 容器服务/单集群应用/应用发布后详情管理-重启 | caas-core | app-model/Ready   app-decompile/Ready | |
| 容器服务/单集群应用/应用发版 | caas-core | app-model/Ready，<br/>app-decompile/Ready | |
| 容器服务/多集群应用/应用发布 | caas-core | stellaris/Ready<br/>app-model/Ready，<br/>app-decompile/Ready | |
| 容器服务/多集群应用/应用发布组件配置能力 | caas-core | stellaris/Ready<br/>app-model/Ready，<br/>app-decompile/Ready | |
| 容器服务/多集群应用/应用发布后详情管理-关联资源 | caas-core | stellaris/Ready<br/>app-model/Ready，<br/>app-decompile/Ready | |
| 容器服务/多集群应用/应用发布后详情管理-重启 | caas-core | stellaris/Ready<br/>app-model/Ready，<br/>app-decompile/Ready | |
| 容器服务/多集群应用/应用发版 | caas-core | stellaris/Ready<br/>app-model/Ready，<br/>app-decompile/Ready | |


#### 4.15.1.2. **权限点**
+ 容器服务/单集群应用

```json
{
  "requeridClusterAddonFeatures": [
    // 正常运行
    "app-model/Ready",
    "app-decompile/Ready",
    "resource-aggregate/Ready"
  ]
}
```

+ 容器服务/多集群应用

```json
{
  "requeridClusterAddonFeatures": [
    "app-model/Ready",
    "app-decompile/Ready",
    "resource-aggregate/Ready"
  ]
}
```

### 4.15.2. **代码改造**
caas-core

相应应用发布代码判断组件状态。

+ 单/多集群应用

```java
/**
 * <AUTHOR>
 * @date 2022/10/17 10:54
 */
public interface ApplicationService {

    void createApplication(String organId, String projectId, ApplicationVO applicationVO);

    /**
     * getOriginApplicationDto
     * 只以CR形式返回ApplicationDTO 不会查额外的资源
     */
    ApplicationVO getOriginApplicationDto(String projectId, String clusterName, String namespace, String appName);

    ApplicationVO getApplication(String projectId, String clusterName, String namespace, String appName,Boolean baseInfo);

    ApplicationDTO getApplicationDTO(String projectId, String clusterName, String namespace, String appName);

    /**
     * 查询应用列表，项目ID可以为null，id为null时查询集群项目下的应用
     * 集群可以为null，为null时查询项目下的所有应用
     * 命名空间可以为null，为null时，查询项目下某集群的所有应用
     *
     * @param projectId     项目ID
     * @param clusterName   集群名
     * @param namespace     命名空间
     * @param multiResource
     * @return 应用列表
     */
    List<ApplicationVO> listApplication(String projectId, String clusterName, @Nullable String namespace, Boolean multiResource);

    List<ApplicationDTO> listApplicationDTOs(String clusterName, String namespace);

    List<ApplicationVO> listApplications(String clusterName, String namespace);

    List<ApplicationVO> listApplications(Cluster cluster, String namespace);

    List<ApplicationVO> listAllApplications();

    void deleteApplication(String projectId, String clusterName, String namespace, String appName);

    void updateMultiApplication(String projectId,String clusterName, ApplicationVO applicationVO);

    void updateApplication(ApplicationVO applicationVO);
    void updateApplicationComponentMetadata(String projectId, String clusterName, String namespace, String appName, ComponentMetadataRequest request);
    void updateMultiApplicationComponentMetadata(String projectId, String namespace, String appName, ComponentVO componentVO);

    void updateComponentReplicas(String projectId, ComponentInfoDTO componentInfoDTO);

    void stopComponent(String projectId, ComponentInfoDTO componentInfoDTO);

    void startComponent(String projectId, ComponentInfoDTO componentInfoDTO);

    List<AssociatedResourceDTO> listAssociatedResource(String projectId, String clusterName, String namespace, String appName);

    List<ApplicationPodListItemDTO> listPodsOfComponent(String clusterName, String namespace, String type, String componentName, boolean withPrometheusData, boolean withExceptionThrow);

    List<ApplicationPodListItemDTO> lisPodsOfApplication(String projectId, String clusterName, String namespace, String appName, boolean withPrometheusData, boolean withExceptionThrow);

    AsyncDeleteNamespaceMessage deleteListApplication(String organId, String projectId, List<ResourcesListDTO> resourcesListDTOList);

    List<ApplicationPodListItemDTO> listApplicationsPodDTO(String organizationId, String projectId, String clusterName, String namespace, String resourceType, String resourceName);

    void reloadApplication(String projectId, String clusterName, String namespace, String appName);

    void restartApplication(String projectId, String clusterName, String namespace, String appName);

    void stopListComponent(String projectId, ComponentInfoDTO info, List<ComponentListDTO> componentListDTOList);

    void startListComponent(String projectId, ComponentInfoDTO info, List<ComponentListDTO> componentListDTOList);

    void updateMultiApplicationTopology(String organId, String projectId, String namespace, String appName, MultiApplicationTopologyRequest request);

    void updateMultiApplicationExpose(String organId, String projectId, String namespace, String appName, MultiApplicationExposeRequest request);

    List<ApplicationVO> listMultiApplication(String projectId, List<String> clusterNames, List<String> namespaces, List<String> failoverClusterNames);

    List<ApplicationPodListItemDTO> listPodsOfMultiApplication(String projectId, String clusterName, String namespace, String appName);

    List<AssociatedResourceDTO> getMultiApplicationAssociatedResources(String projectId, String clusterName, String namespace, String appName);

    void updateMultiApplicationScheduling(String organId, String projectId, String namespace, String appName, String componentName, MultiApplicationScheduling multiApplicationScheduling);

    void operationMultiComponent(String projectId, Cluster cluster, String namespace, String appName, List<String> componentNames, MultiApplicationOperationEnum operation);

    void removeClusters(String organId, String projectId, String namespace, String appName, AppClusterDetailVO appClusterDetailVO);

    void addClusters(String organId, String projectId, String namespace, String appName, ApplicationVO applicationVO);

    void updateMultiResourcesComponentReplicas(String projectId, ComponentInfoDTO info);

    ApplicationVO getApplicationComponents(String projectId, String name, String namespace, String appName, Boolean multiResource);

    ApplicationVO getApplicationPolicy(String projectId, String name, String namespace, String appName,boolean ischeduling);

    ApplicationVO getApplicationExpose(String projectId, String name, String namespace, String appName);

    void createApplication(ImageAppDTO imageApp);
}
```

## 4.16. 基线检查baseline-checker
### 4.16.1. **特性功能**
#### 4.16.1.1. **功能点**
+ baseline-checker 基线检查

```yaml
apiVersion: unified-platform.harmonycloud.cn/v1alpha1
kind: EnhanceClusterAddon
metadata:
  finalizers:
    - enhanceclusteraddon.unifiedplatform.harmonycloud.cn/finalizer
  generation: 1
  name: baseline-checker
  namespace: stellaris-workspace-cluster161
spec:
  addonRef:
    name: baseline-checker
    namespace: stellaris-workspace-cluster161
  builtinFeatures:
    - Installed
    - Ready
status:
  addonPhase: Abnormal
  enabledBuiltinFeatures:
    - Installed
    - Ready
  featureConditions:
    - builtin: true
      lastTransitionTime: "2025-06-07T10:38:38Z"
      name: Installed
      status: "True"
    - builtin: true
      lastTransitionTime: "2025-06-07T10:45:06Z"
      message: 'ClusterAddon ''stellaris-workspace-cluster161/baseline-checker'' has failed HealthCheck condition: BaselineServerUnhealthy'
      name: Ready
      reason: HealthCheckFailed
      status: "False"
  lastTransitionTime: "2025-06-07T10:50:07Z"
  message: Feature Runners Synced
  phase: Synced
  supportedBuiltinFeatures:
    - Installed
    - Ready

```

| **<font style="background-color:rgb(231, 233, 232);">功能点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台组件</font>** | **<font style="background-color:rgb(231, 233, 232);">能力点</font>** | **<font style="background-color:rgb(231, 233, 232);">平台页面</font>** |
| :--- | :--- | :--- | :--- |
| 门户管理/基线合规 | olympus-portal | baseline-checker/Ready | [http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr#/baseline/checkpolicy](http://*************/?isBackend=1&navCode=unified_platform_sys_platform_mgr#/baseline/checkpolicy) |


#### 4.16.1.2. **权限点**
```json
{
  "requeridClusterAddonFeatures": [
    // 正常运行
    "baseline-checker/Ready",
  ]
}
```

### 4.16.2. **代码改造**
olympus-portal

目前基线相关代码已做适配，如果没有安装组件只影响基线策略检查功能和导入内置基线，不影响其他基础功能使用。

