#!/bin/bash
image_name="cloudservice-operator:latest"
platform="linux/amd64,linux/arm64"
golang_image="golang:1.20"
alpine_image="alpine:3.18"
dockerfile="Dockerfile"
help_message=$(printf  \
"
docker build scripts for cloudservice-operator image

Usage:
      --help          get help message
      -p --platform   build platforms
      -i --image      build image_name
      -f              dockerfile name
"
)
function bind_args() {
  while [ $# -ne 0 ]; do
      arg="$1"
      arg="${arg/ //}"
      case $arg in
          -i|--image)
            image_name="$2"
            shift 2
            ;;
          --image=*)
            image_name="${arg#--image=}"
            shift 1
            ;;
          -p|--platform)
            platform="$2"
            shift 2
            ;;
          --platform=*)
            platform="${arg#--platform=}"
            shift 1
            ;;
          --golang-image)
            golang_image="$2"
            shift 2
            ;;
          --golang-image=*)
            golang_image="${arg#--golang-image=}"
            shift 1
            ;;
          --alpine-image)
            alpine_image="$2"
            shift 2
            ;;
          --alpine-image=*)
            alpine_image="${arg#--alpine-image=}"
            shift 1
            ;;
          -f)
            dockerfile="$2"
            shift 2
            ;;
          -h|--help|*)
              echo "${help_message}"
            shift 1
      esac
  done
}

function print_error() {
#    RED='\033[0;31m'   # 红色 ANSI 转义码
    echo -e "${RED}Error: $1" >&2  # 输出到标准错误并带有颜色
#    RED=""
}

function download_jq() {
  mkdir -p $HOME/.local/bin
  export PATH="$HOME/.local/bin:$PATH"
  if which jq;then
    return 0
  fi
  echo "not found jq downloading..."
  jq_version="1.7.1"
  jq_os="linux"
  jq_arch="amd64"
  os=`uname`
  case $os in
    Linux)
      jq_os="linux"
      ;;
    Darwin)
      jq_os="macos"
      ;;
    *)
      echo "Unsupported platform..."
      exit 1
      ;;
  esac
  arch=`uname -m`
  case $arch in
    arm64)
      jq_arch=arm64
    ;;
    x86_64|amd64)
      jq_arch=amd64
    ;;
    *)
      echo "Unsupported arch..."
      exit 1
      ;;
  esac
  if which curl; then
    if ! curl -o $HOME/.local/bin/jq "https://hub.gitmirror.com/https://github.com/jqlang/jq/releases/download/jq-${jq_version}/jq-${jq_os}-${jq_arch}" -k; then
      print_error "Download jq failed"
      exit 1
    fi
    chmod +x $HOME/.local/bin/jq
  fi
  if which wget; then
    if ! wget "https://hub.gitmirror.com/https://github.com/jqlang/jq/releases/download/jq-${jq_version}/jq-${jq_os}-${jq_arch}" -O $HOME/.local/bin/jq --no-check-certificate; then
      print_error "Download jq failed"
      exit 1
    fi
    chmod +x $HOME/.local/bin/jq
  fi
}


function init() {
  if which docker > /dev/null 2>&1;then
    export DOCKER_CLI_EXPERIMENTAL=enabled
    export DOCKER_BUILDKIT=1
    context_name=$(docker context ls | grep default | awk '{print $1}')
    if [[ -n ${context_name} ]];then
      docker buildx use "${context_name}"
      # shellcheck disable=SC2206
      build_platforms=(${platform/,/ })
      support_platform_string=$(docker buildx inspect | grep 'Platforms:'| sed 's/Platforms:\s//g;s/ //g;')
      cnt=0
      for bp in "${build_platforms[@]}"; do
        if [[ ${support_platform_string} == *${bp}* ]];then
          cnt=$((cnt+1))
        fi
      done
      if [ ${cnt} -ne  ${#build_platforms[@]} ];then
        if ! docker run -it --rm --privileged tonistiigi/binfmt --install all ;then
          print_error "install multiple architectures support failed"
          exit 1
        fi
        echo "unsupported ${platform} to build, already installed multiple architectures, please restart docker ...  "
      fi
    else
      print_error "docker default context is empty"
      exit 1
    fi
  fi
  download_jq
}

function build_image() {
  rm -rf ./*.swp
  if [ "${#platform[@]}" -ne 0 ]; then
    echo "Start build ${image_name} manifest ..."
    alpine_file_prefix=${alpine_image//:/-}
    alpine_file_prefix=${alpine_file_prefix//\//-}
    file_prefix=${image_name//:/-}
    file_prefix=${file_prefix//\//-}
    export alpine_manifest_json_file="${alpine_file_prefix}-manifests.list.swp"
    export manifest_list_file="${file_prefix}-manifests.list.swp"
    export cnt_file="${file_prefix}-cnt.swp"

    docker manifest inspect "${alpine_image}" --insecure > "${alpine_manifest_json_file}"
    echo "" > "${manifest_list_file}"
    echo 0 > "${cnt_file}"

    docker pull "${golang_image}"
    docker tag "${golang_image}" golang:1.20
    # shellcheck disable=SC2206
    platform_array=(${platform/,/ })
    for p in "${platform_array[@]}";do
        {
          parts=(${p//\// })
          os="${parts[0]}"
          arch="${parts[1]}"
          suffix="-${os}-${arch}"
          build_image="${image_name}${suffix}"
          # pull alpine image
          digest=`cat ${alpine_manifest_json_file} | jq ".manifests[] | select(.platform.os == \"${os}\" and .platform.architecture == \"${arch}\") | .digest" -r`
          alpine_image_with_digest="${alpine_image}@${digest}"
          docker pull "${alpine_image_with_digest}"
          if [ $? != 0 ]; then
            print_error "pull base build image failed"
            exit 1
          fi
          digest_image_name=${alpine_image_with_digest//\//\\/}
          digest_image_name=${alpine_image_with_digest//\./\\.}
          sed -e "s|FROM .*alpine.*|FROM ${digest_image_name}|g" "${dockerfile}" > "${dockerfile}${suffix}.swp"
          cat "${dockerfile}${suffix}.swp"
          if ! docker buildx build --platform "${p}" -t "${build_image}" -o=type=docker -f "${dockerfile}${suffix}.swp" . ; then
            print_error "building ${build_image} image failed"
            exit 1
          fi
          if ! docker push "${build_image}"; then
            print_error "push ${build_image} image failed"
            exit 1
          fi
          i=0
          while true; do
              i=$((i+1))
              sleep 1
              if [[ $i -gt 60 ]];then
                echo "Build image timeout ..."
                exit 1
              fi
              lock_file="${manifest_list_file}.lock.swp"
              if [[ ! -e "${lock_file}" ]]; then
                touch "${lock_file}"
                ls "${lock_file}"
                if ! grep "${build_image}" "${manifest_list_file}"; then
                   echo "$(cat "${manifest_list_file}") ${build_image}" > "${manifest_list_file}"
                fi
                cnt=$(cat "${cnt_file}")
                echo $((cnt+1)) > "${cnt_file}"
                rm -rf "${lock_file}"
                exit 0
              fi
          done
        } &
    done
    wait
    if [ $(cat ${cnt_file}) -ne ${#platform_array[@]} ]; then
        echo "Build multi arch image ${image_name} not completed..."
        return 1
    fi
    docker manifest rm "${image_name}" > /dev/null 2>&1
    # shellcheck disable=SC2046
    docker manifest create --insecure "${image_name}" $(cat ${manifest_list_file})
    for p in "${platform_array[@]}"
    do
        # shellcheck disable=SC2206
        parts=(${p//\// })
        os="${parts[0]}"
        arch="${parts[1]}"
        suffix="-${os}-${arch}"
        docker manifest annotate --arch "${arch}" --os "${os}" "${image_name}" "${image_name}${suffix}"
    done
    docker manifest push --insecure "${image_name}"
  else
    print_error "build platform is empty"
    exit 1
  fi
}


function clean_image() {
  rm -rf ./*.swp
  while IFS= read -r line; do
      item=${line}
      image_repository=$(echo "${item}" | awk '{print $1}')
      image_tag=$(echo "${item}" | awk '{print $2}')
      if [[ ${image_name} == *${image_repository}:${image_tag}* ]]; then
          id=$(echo "${item}" | awk '{print $3}')
          if [ -n "${id}" ]; then
              docker rmi -f "${id}"
          fi
      fi
  done <<< "$(docker images| awk '{if (NR > 1) print $0}')"
}


function main() {
  bind_args "$@"
  init
  set -x
  build_image
  set +x
  clean_image
}

main "$@"
