#!/bin/bash

help_message=$(printf  \
"
base build image sync script, will push multi arch image
Final images:
golang:1.20 # multi
golang:1.20-os-arch etc.

Usage:
      --help          get help message
      --images        sync images (default golang:1.20,alpine:1.20)
      --platform      sync platform (default linux/amd64,linux/arm64)
      --registry      sync registry (default *************)
      --repository    sync repository (default library)
      -f              dockerfile name
"
)


function bind_args() {
  while [ $# -ne 0 ]; do
      arg="$1"
      arg="${arg/ //}"
      case $arg in
          --images|--images=*)
            if [[ $arg == *"="* ]]; then
              images="${arg#--images=}"
              shift 1
            else
              images=$2
              shift 2
            fi
            ;;
          --platform|--platform=*)
            if [[ $arg == *"="* ]]; then
              platform="${arg#--platform=}"
              shift 1
            else
              platform=$2
              shift 2
            fi
            ;;
          --registry|--registry=*)
            if [[ $arg == *"="* ]]; then
              registry="${arg#--registry=}"
              shift 1
            else
              registry=$2
              shift 2
            fi
            ;;
          --repository|--repository=*)
            if [[ $arg == *"="* ]]; then
              repository="${arg#--repository=}"
              shift 1
            else
              repository=$2
              shift 2
            fi
            ;;
          -h|--help|*)
              echo "${help_message}"
            shift 1
      esac
  done
}


function download_jq() {
  mkdir -p $HOME/.local/bin
  export PATH="$HOME/.local/bin:$PATH"
  if which jq;then
    return 0
  fi
  echo "not found jq downloading..."
  jq_version="1.7.1"
  jq_os="linux"
  jq_arch="amd64"
  os=`uname`
  case $os in
    Linux)
      jq_os="linux"
      ;;
    Darwin)
      jq_os="macos"
      ;;
    *)
      echo "Unsupported platform..."
      exit 1
      ;;
  esac
  arch=`uname -m`
  case $arch in
    arm64)
      jq_arch=arm64
    ;;
    x86_64|amd64)
      jq_arch=amd64
    ;;
    *)
      echo "Unsupported arch..."
      exit 1
      ;;
  esac
  if which curl; then
    curl "https://github.com/jqlang/jq/releases/download/jq-${jq_version}/jq-${jq_os}-${jq_arch}" -o $HOME/.local/bin/jq
    return 0
  fi
  if which wget; then
    wget "https://github.com/jqlang/jq/releases/download/jq-${jq_version}/jq-${jq_os}-${jq_arch}" -O $HOME/.local/bin/jq
    return 0
  fi
}


function sync_image() {
  echo $images $platform
  image_array=(${images/,/ })
  platform_array=(${platform/,/ })
  for image in "${image_array[@]}";do
    echo "Start build ${target_image} manifest ..."
    file_prefix=${image/:/-}
    file_prefix=${file_prefix/\//-}
    manifest_json_file="${file_prefix}-manifests.json.swp"
    export manifest_list_file="${file_prefix}-manifests.list.swp"
    docker manifest inspect "${image}" --insecure > "${manifest_json_file}"
    export cnt_file="${file_prefix}-cnt.swp"
    echo 0 > "${cnt_file}"
    echo "" > "${manifest_list_file}"
    target_image="${registry}/${repository}/${image}"
    docker manifest rm "$target_image" > /dev/null 2>&1
    for platform in "${platform_array[@]}";do
      {
        parts=(${platform//\// })
        os="${parts[0]}"
        arch="${parts[1]}"
        suffix="-${os}-${arch}"
        digest=`cat ${manifest_json_file} | jq ".manifests[] | select(.platform.os == \"${os}\" and .platform.architecture == \"${arch}\") | .digest" -r`
        image_with_digest="${image}@${digest}"
        docker pull "${image_with_digest}"
        docker tag "${image_with_digest}" "${target_image}${suffix}"
        if ! docker push "${target_image}${suffix}"; then
          echo "Push ${target_image}${suffix} failed ..."
          exit 1
        fi
        i=0
        while true; do
            i=$((i+1))
            sleep 1
            if [[ $i -gt 10 ]];then
              echo "Build manifest timeout ..."
              exit 1
            fi
            lock_file="${image}.lock.swp"
            if [[ ! -e "${lock_file}" ]]; then
              echo "" > "${lock_file}"
              ls "${lock_file}"
              if ! grep "${target_image}${suffix}" "${manifest_list_file}"; then
                 echo "$(cat "${manifest_list_file}") ${target_image}${suffix}" > "${manifest_list_file}"
              fi
              cnt=$(cat "${cnt_file}")
              echo $((cnt+1)) > "${cnt_file}"
              rm -rf "${lock_file}"
              exit 0
            fi
        done
#        echo "`cat ${manifest_list_file}` ${target_image}${suffix}" > "${manifest_list_file}"
#        cnt=`cat "${cnt_file}"`
#        echo $((cnt+1)) > "${cnt_file}"
      } &
    done
    wait
    if [ `cat ${cnt_file}` -ne ${#platform_array[@]} ]; then
        echo "Sync multi arch image ${target_image} not completed..."
        break
    fi
    # shellcheck disable=SC2046
    docker manifest create --insecure "${target_image}" $(cat ${manifest_list_file})
    for platform in "${platform_array[@]}";do
      parts=(${platform//\// })
      os="${parts[0]}"
      arch="${parts[1]}"
      suffix="-${os}-${arch}"
      docker manifest annotate --arch "${arch}" --os "${os}" "${target_image}" "${target_image}${suffix}"
    done
    echo "Start push ${target_image} manifest ..."
    docker manifest push --insecure "${target_image}"
    rm -rf ./*.swp
    echo "Finished push ${target_image} manifest ..."
  done
}

function clean_image() {
    image_array=(${images//,/ })
    platform_array=(${platform//,/ })
    for image in "${image_array[@]}";do
      target_image="${registry}/${repository}/${image}"
      for platform in "${platform_array[@]}";do
        parts=(${platform//\// })
        os="${parts[0]}"
        arch="${parts[1]}"
        suffix="-${os}-${arch}"
        docker rmi -f "${target_image}${suffix}" > /dev/null 2>&1
      done
    done
    rm -rf ./*.swp  > /dev/null 2>&1
}

function main() {
    images="golang:1.20,alpine:3.18"
    platform="linux/amd64,linux/arm64"
    registry="*************"
    repository="library"
    set -x
    bind_args "$@"
    download_jq
    sync_image
    clean_image
#    set +x
}

main "$@"