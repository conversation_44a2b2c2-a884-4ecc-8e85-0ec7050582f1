PROJECT_NAME ?= olympus-portal


test:
	go test ./backend/pkg/... -coverprofile cover.out

GOIMPORTS ?= $(GOPATH)/bin/goimports
.PHONY: fmt
fmt: ### Run go fmt contains goimports
	@[ -f $(GOIMPORTS) ] || go install golang.org/x/tools/cmd/goimports@latest
	$(GOIMPORTS) -w ./backend
	go fmt ./backend/...


GOPATH = $(shell go env GOPATH)


SWAG_VERSION ?= v1.16.4
SWAG_TMP_DIR := $(shell mktemp -d)
.PHONY: swagger-install
swagger-install: ## Install swagger binary, apply code fix, and rebuild
	@if [ ! -f "$(GOPATH)/bin/swag" ]; then \
		go install github.com/swaggo/swag/cmd/swag@$(SWAG_VERSION); \
		cp -r $(GOPATH)/pkg/mod/github.com/swaggo/swag@$(SWAG_VERSION)/* $(SWAG_TMP_DIR); \
		sed -i '' 's/if len(pkg) == 0 || file\.Name\.Name == pkg {/if (len(pkg) == 0 || file.Name.Name == pkg) \&\& pkgDefs.files[file] != nil {/g' $(SWAG_TMP_DIR)/packages.go; \
		cd $(SWAG_TMP_DIR) && go build -o $(GOPATH)/bin/swag cmd/swag/main.go; \
	fi

.PHONY: swagger
swagger: swagger-install  ## Generate swagger docs
	swag fmt
	swag init -g router.go  -d backend/pkg/router  --parseDependency  --parseInternal --parseVendor --parseDepth=10

build:
	go build backend/cmd/backend-server/main.go

sync:
	go mod tidy
	go mod vendor

ENV ?= ***********
REDISPORT ?= 30379
run:
	go run backend/cmd/backend-server/main.go \
		 --kubeconfig=$(HOME)/.kube/config \
		 --enable-swagger=true \
		 --amp-database-host=$(ENV) \
		 --amp-database-port=29050 \
		 --amp-database-username=root \
		 --amp-database-password=Hc@Cloud01 \
		 --caas-database-host=$(ENV) \
		 --caas-database-port=29050 \
		 --caas-database-username=root \
		 --caas-database-password=Hc@Cloud01 \
		 --redis-address=$(ENV):$(REDISPORT) \
		 --redis-password=Hc@Cloud01 \
		 --redis-select-db=0 \
		 --devops-amp-url=http://$(ENV):30090 \
		 --sisyphus-url=http://************:38080



COMMIT_ID ?= $(shell git rev-parse --short HEAD)
REGISTRY ?= *************
IMG_REPO ?= k8s-deploy/$(PROJECT_NAME)
VERSION ?= v3.7.0
IMG_TAG ?= $(VERSION)-$(COMMIT_ID)
IMG ?= ${REGISTRY}/$(IMG_REPO):$(IMG_TAG)

GOLANG_IMAGE ?= *************/library/golang:1.23
ALPINE_IMAGE ?= *************/library/alpine:3.18
PLATFORMS ?= linux/amd64,linux/arm64
.PHONY: docker-script-build-cross
docker-script-build-cross:
	@sed -e '1 s/\(^FROM\)/FROM --platform=\$$\{BUILDPLATFORM\}/; t' -e ' 1,// s//FROM --platform=\$$\{BUILDPLATFORM\}/' Dockerfile-cross > Dockerfile.cross
	@chmod +x build/docker-build.sh
	build/docker-build.sh --image=$(IMG) --platform=$(PLATFORMS) --golang-image=$(GOLANG_IMAGE) --alpine-image=$(ALPINE_IMAGE) -f Dockerfile.cross
	@rm Dockerfile.cross

.PHONY: docker-script-build
docker-script-build: ## Run docker multi platform by scripts image build and push to registry
	@sed -e '1 s/\(^FROM\)/FROM --platform=\$$\{BUILDPLATFORM\}/; t' -e ' 1,// s//FROM --platform=\$$\{BUILDPLATFORM\}/' Dockerfile > Dockerfile.cross
	#@sed -e 's#FROM golang:1.23#FROM $(GOLANG_IMAGE)#g;s#FROM alpine:3.18#FROM $(ALPINE_IMAGE)#g' Dockerfile  | sed -e '1 s/\(^FROM\)/FROM --platform=\$$\{BUILDPLATFORM\}/; t' -e ' 1,// s//FROM --platform=\$$\{BUILDPLATFORM\}/' > Dockerfile.cross
	@chmod +x build/docker-build.sh
	build/docker-build.sh --image=$(IMG) --platform=$(PLATFORMS) --golang-image=$(GOLANG_IMAGE) --alpine-image=$(ALPINE_IMAGE) -f Dockerfile.cross
	@rm Dockerfile.cross

.PHONY: docker-sync-base-image
docker-sync-base-image:
	@chmod +x scripts/sync_image.sh
	scripts/sync_image.sh --images="golang:1.23,alpine:3.18" \
		--platform=$(PLATFORMS) \
		--registry=************* \
		--repository=library

.PHONY: docker-build
docker-build:
	@sed -e 's#FROM golang:1.23#FROM $(GOLANG_IMAGE)#g;s#FROM alpine:3.18#FROM $(ALPINE_IMAGE)#g' Dockerfile > Dockerfile.cross
	docker build -t ${IMG} -f Dockerfile.cross  .
	docker push ${IMG}
	@rm Dockerfile.cross

.PHONY: docker-buildx
docker-buildx:
	@sed -e 's#FROM golang:1.23#FROM --platform=$$BUILDPLATFORM $(GOLANG_IMAGE)#g;s#FROM alpine:3.18#FROM $(ALPINE_IMAGE)#g' Dockerfile > Dockerfile.cross
	docker buildx build -t ${IMG} --platform=$(PLATFORMS) --push -f Dockerfile.cross  .
	@rm Dockerfile.cross

KUBECONFIG ?= /data/k8s-conf/admin-16
.PHONY: image-deploy
image-deploy:
	kubectl set image deploy olympus-portal olympus-portal=$(IMG) -n caas-system --insecure-skip-tls-verify=true --kubeconfig=$(KUBECONFIG)

BUILDCTL_ADDR ?= tcp://*************:32234
.PHONY: buildctl-build
buildctl-build:
	sed -e 's|FROM golang:1.23|FROM --platform=$$BUILDPLATFORM golang:1.23|g' -e 's|FROM alpine:3.18|FROM --platform=$$TARGETPLATFORM alpine:3.18|g' -e 's|golang:1.23|$(GOLANG_IMAGE)|g' -e 's|alpine:3.18|$(ALPINE_IMAGE)|g'  Dockerfile > Dockerfile.buildctl
	buildctl --addr $(BUILDCTL_ADDR) build --frontend=dockerfile.v0  --local dockerfile=./  --local context=./  --opt filename=Dockerfile.buildctl  --opt platform=${PLATFORMS}  --output type=image,name=${IMG},push=true  --skip-token-tls-verify

.PHONY: audit-translate
audit-translate:
	go run backend/cmd/translate/audit_translate.go
