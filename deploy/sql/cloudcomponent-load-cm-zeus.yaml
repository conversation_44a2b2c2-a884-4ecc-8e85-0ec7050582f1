apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    unified-platform.harmonycloud.cn/translate: ""
    unified-platform.harmonycloud.cn/cloudservice-name: zeus
    cloudcomponent/translate: ""
    version: xxxxx
  name: zeus-cloudcomponent-upload-config-v3.4.0
data:
  translate.json: |-
    [{"operator":"add","configs":[{"resourceTranslates":[{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"zeus/zeus","property":"displayName","languageMap":{"en-US":"zeus","zh-HK":"zeus"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"zeus/zeus","property":"description","languageMap":{"en-US":"Zeus backend","zh-HK":"zeus後端"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"zeus/zeus-ui","property":"displayName","languageMap":{"en-US":"zeus-ui","zh-HK":"zeus-ui"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"zeus/zeus-ui","property":"description","languageMap":{"en-US":"Zeus front-end","zh-HK":"zeus前端"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"zeus/zeus-mysql","property":"displayName","languageMap":{"en-US":"zeus-mysql","zh-HK":"zeus-mysql"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"zeus/zeus-mysql","property":"description","languageMap":{"en-US":"Zeus MySQL database","zh-HK":"zeus mysql資料庫"}}]}]}]