apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    unified-platform.harmonycloud.cn/translate: ""
    unified-platform.harmonycloud.cn/cloudservice-name: container-service
    cloudservice/translate: ""
    version: v3.4.0
  name: container-service-cloudservice-upload-config-v3.4.0
data:
  translate.json: |-
    [{"operator":"add","configs":[{"resourceTranslates":[{"groupName":"k8s-resource-skyview-cloud-service","uniqueValue":"container-service","property":"displayName","languageMap":{"en-US":"Container Services","zh-HK":"容器服務"}},{"groupName":"k8s-resource-skyview-cloud-service","uniqueValue":"container-service","property":"description","languageMap":{"en-US":"A one-stop operation and maintenance management container service for applications, providing customers with complete application lifecycle management in multi cluster and multi tenant scenarios","zh-HK":"服務於應用的一站式運維管理容器服務，在多集羣、多租戶場景下為客戶提供完整的應用生命週期管理"}}]}]}]