-- 云服务与云组件翻译 繁体中文和语言 zeus
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service','zeus','displayName','en-US','Middleware Services');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service','zeus','description','en-US','Provide a fully automated middleware integrated management and supply capability based on the Kubernetes system for scenarios where the middleware technology stack is complex and difficult to implement in practice');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service','zeus','displayName','zh-HK','中介軟體服務');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service','zeus','description','zh-HK','針對中介軟體科技棧複雜且實踐落地難度高的場景，提供基於Kubernetes體系打造的全自動化運維的中介軟體一體化管理和供給能力');
-- component
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','zeus/zeus-mysql','description','en-US','Zeus MySQL database');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','zeus/zeus-mysql','description','zh-HK','zeus mysql資料庫');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','zeus/zeus-mysql','displayName','en-US','zeus-mysql');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','zeus/zeus-mysql','displayName','zh-HK','zeus-mysql');

insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','zeus/zeus-ui','description','en-US','Zeus front-end');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','zeus/zeus-ui','description','zh-HK','zeus前端');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','zeus/zeus-ui','displayName','en-US','zeus-ui');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','zeus/zeus-ui','displayName','zh-HK','zeus-ui');

insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','zeus/zeus','description','en-US','Zeus backend');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','zeus/zeus','description','zh-HK','zeus後端');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','zeus/zeus','displayName','en-US','zeus');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','zeus/zeus','displayName','zh-HK','zeus');



-- 云服务与云组件翻译 繁体中文和语言 unified-platform
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service','unified-platform','displayName','en-US','Basic portal platform');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service','unified-platform','description','en-US','Cloud native unified portal base, providing basic services such as organizational management, project management, and cloud service management');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service','unified-platform','displayName','zh-HK','基本門戶平臺');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service','unified-platform','description','zh-HK','雲原生統一門戶底座，提供組織管理、專案管理、雲服務管理等基礎服務');
-- component
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/caas-amp','description','en-US','User authentication system');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/caas-amp','description','zh-HK','用戶認證體系');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/caas-amp','displayName','en-US','User component');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/caas-amp','displayName','zh-HK','用戶組件');

insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/redis','description','en-US','redis');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/redis','description','zh-HK','redis');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/redis','displayName','en-US','redis');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/redis','displayName','zh-HK','redis');

insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/caas-oam','description','en-US','Platform alarm component');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/caas-oam','description','zh-HK','平臺告警組件');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/caas-oam','displayName','en-US','Platform alarm component');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/caas-oam','displayName','zh-HK','平臺告警組件');

insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/backuprestore-amp','description','en-US','Devops user system support');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/backuprestore-amp','description','zh-HK','devops用戶體系支持');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/backuprestore-amp','displayName','en-US','Devops user component');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/backuprestore-amp','displayName','zh-HK','devops用戶組件');

insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/caas-ui','description','en-US','Portal UI components');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/caas-ui','description','zh-HK','門戶UI組件');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/caas-ui','displayName','en-US','Portal UI components');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/caas-ui','displayName','zh-HK','門戶UI組件');

insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/mysql','description','en-US','database');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/mysql','description','zh-HK','資料庫');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/mysql','displayName','en-US','MySQL');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/mysql','displayName','zh-HK','MySQL');

insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/portal','description','en-US','Provide the basic functions of a unified portal platform');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/portal','description','zh-HK','提供統一門戶平臺的基礎功能');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/portal','displayName','en-US','Unified Portal');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/portal','displayName','zh-HK','統一門戶portal');

insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/caas-registry','description','en-US','Provide platform Harbor and other image service support');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/caas-registry','description','zh-HK','提供平臺Harbor等鏡像服務支援');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/caas-registry','displayName','en-US','Mirror Service Component');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/caas-registry','displayName','zh-HK','鏡像服務組件');

insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/stellaris-core','description','en-US','Multi cluster control components');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/stellaris-core','description','zh-HK','多集羣管控組件');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/stellaris-core','displayName','en-US','Multi cluster control components');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/stellaris-core','displayName','zh-HK','多集羣管控組件');

insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/stellaris-proxy-proxy','description','en-US','Multi cluster proxy component');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/stellaris-proxy-proxy','description','zh-HK','多集羣代理proxy組件');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/stellaris-proxy-proxy','displayName','en-US','Multi cluster agent');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/stellaris-proxy-proxy','displayName','zh-HK','多集羣代理');

insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/monitor','description','en-US','monitor');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/monitor','description','zh-HK','監控');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/monitor','displayName','en-US','monitor');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/monitor','displayName','zh-HK','監控');

insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/node-pool','description','en-US','Node Pool');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/node-pool','description','zh-HK','資源池');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/node-pool','displayName','en-US','Node Pool');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/node-pool','displayName','zh-HK','資源池');

insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/harbor','description','en-US','Mirror Warehouse');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/harbor','description','zh-HK','鏡像倉庫');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/harbor','displayName','en-US','Mirror Warehouse');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/harbor','displayName','zh-HK','鏡像倉庫');

insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/log','description','en-US','log');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/log','description','zh-HK','日誌');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/log','displayName','en-US','log');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/log','displayName','zh-HK','日誌');

insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/sisyphus','description','en-US','Node Up and Down Lines');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/sisyphus','description','zh-HK','節點上下線');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/sisyphus','displayName','en-US','Node Up and Down Lines');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','unified-platform/sisyphus','displayName','zh-HK','節點上下線');



-- 云服务与云组件翻译 繁体中文和语言 container-service
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service','container-service','displayName','en-US','Container Services');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service','container-service','description','en-US','A one-stop operation and maintenance management container service for applications, providing customers with complete application lifecycle management in multi cluster and multi tenant scenarios');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service','container-service','displayName','zh-HK','容器服務');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service','container-service','description','zh-HK','服務於應用的一站式運維管理容器服務，在多集羣、多租戶場景下為客戶提供完整的應用生命週期管理');

-- component
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','container-service/heimdallr','description','en-US','Application Model');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','container-service/heimdallr','description','zh-HK','應用模型');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','container-service/heimdallr','displayName','en-US','Application Model');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','container-service/heimdallr','displayName','zh-HK','應用模型');

insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','container-service/caas-core','description','en-US','Core components');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','container-service/caas-core','description','zh-HK','覈心組件');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','container-service/caas-core','displayName','en-US','caas-core');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','container-service/caas-core','displayName','zh-HK','caas-core');

insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','container-service/better-autoscaler-controller','description','en-US','Horizontal expansion and contraction capacity');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','container-service/better-autoscaler-controller','description','zh-HK','水准擴縮容');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','container-service/better-autoscaler-controller','displayName','en-US','Horizontal expansion and contraction capacity');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','container-service/better-autoscaler-controller','displayName','zh-HK','水准擴縮容');

insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','container-service/problem-controller-deployment','description','en-US','fault isolation');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','container-service/problem-controller-deployment','description','zh-HK','故障隔離');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','container-service/problem-controller-deployment','displayName','en-US','fault isolation');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','container-service/problem-controller-deployment','displayName','zh-HK','故障隔離');

insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','container-service/resources-aggregate-controller','description','en-US','Resource Association Controller');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','container-service/resources-aggregate-controller','description','zh-HK','資源關聯控制器');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','container-service/resources-aggregate-controller','displayName','en-US','Resource Association Controller');
insert into sys_resource_translate_config(`group_name`,`unique_value`,`property`,`language_code`,`translation`) values('k8s-resource-skyview-cloud-service-component','container-service/resources-aggregate-controller','displayName','zh-HK','資源關聯控制器');


