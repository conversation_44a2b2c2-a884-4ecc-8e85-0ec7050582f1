apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    unified-platform.harmonycloud.cn/translate: ""
    unified-platform.harmonycloud.cn/cloudservice-name: unified-platform
    cloudservice/translate: ""
    version: v1.0.0
  name: unified-platform-cloudservice-upload-config-v1.0.0
data:
  translate.json: |-
    [{"operator":"add","configs":[{"resourceTranslates":[{"groupName":"k8s-resource-skyview-cloud-service","uniqueValue":"unified-platform","property":"displayName","languageMap":{"en-US":"Basic portal platform","zh-HK":"基本門戶平臺"}},{"groupName":"k8s-resource-skyview-cloud-service","uniqueValue":"unified-platform","property":"description","languageMap":{"en-US":"Cloud native unified portal base, providing basic services such as organizational management, project management, and cloud service management","zh-HK":"雲原生統一門戶底座，提供組織管理、專案管理、雲服務管理等基礎服務"}}]}]}]