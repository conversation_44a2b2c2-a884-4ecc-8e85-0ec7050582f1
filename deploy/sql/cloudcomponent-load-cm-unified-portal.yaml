apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    unified-platform.harmonycloud.cn/translate: ""
    unified-platform.harmonycloud.cn/cloudservice-name: unified-platform
    cloudcomponent/translate: ""
    version: v1.0.0
  name: unified-platform-cloudcomponent-upload-config-v1.0.0
data:
  translate.json: |-
    [{"operator":"add","configs":[{"resourceTranslates":[{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/stellaris-proxy-proxy","property":"displayName","languageMap":{"en-US":"Multi cluster agent","zh-HK":"多集羣代理"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/stellaris-proxy-proxy","property":"description","languageMap":{"en-US":"Multi cluster proxy component","zh-HK":"多集羣代理proxy組件"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/stellaris-core","property":"displayName","languageMap":{"en-US":"Multi cluster control components","zh-HK":"多集羣管控組件"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/stellaris-core","property":"description","languageMap":{"en-US":"Multi cluster control components","zh-HK":"多集羣管控組件"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/sisyphus","property":"displayName","languageMap":{"en-US":"Node Up and Down Lines","zh-HK":"節點上下線"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/sisyphus","property":"description","languageMap":{"en-US":"Node Up and Down Lines","zh-HK":"節點上下線"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/redis","property":"displayName","languageMap":{"en-US":"redis","zh-HK":"redis"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/redis","property":"description","languageMap":{"en-US":"redis","zh-HK":"redis"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/portal","property":"displayName","languageMap":{"en-US":"Unified Portal","zh-HK":"統一門戶portal"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/portal","property":"description","languageMap":{"en-US":"Provide the basic functions of a unified portal platform","zh-HK":"提供統一門戶平臺的基礎功能"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/node-pool","property":"displayName","languageMap":{"en-US":"Node Pool","zh-HK":"資源池"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/node-pool","property":"description","languageMap":{"en-US":"Node Pool","zh-HK":"資源池"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/mysql","property":"displayName","languageMap":{"en-US":"MySQL","zh-HK":"MySQL"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/mysql","property":"description","languageMap":{"en-US":"database","zh-HK":"資料庫"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/monitor","property":"displayName","languageMap":{"en-US":"monitor","zh-HK":"監控"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/monitor","property":"description","languageMap":{"en-US":"monitor","zh-HK":"監控"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/log","property":"displayName","languageMap":{"en-US":"log","zh-HK":"日誌"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/log","property":"description","languageMap":{"en-US":"log","zh-HK":"日誌"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/harbor","property":"displayName","languageMap":{"en-US":"Mirror Warehouse","zh-HK":"鏡像倉庫"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/harbor","property":"description","languageMap":{"en-US":"Mirror Warehouse","zh-HK":"鏡像倉庫"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/devops-amp","property":"displayName","languageMap":{"en-US":"Devops user component","zh-HK":"devops用戶組件"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/devops-amp","property":"description","languageMap":{"en-US":"Devops user system support","zh-HK":"devops用戶體系支持"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/caas-ui","property":"displayName","languageMap":{"en-US":"Portal UI components","zh-HK":"門戶UI組件"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/caas-ui","property":"description","languageMap":{"en-US":"Portal UI components","zh-HK":"門戶UI組件"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/caas-registry","property":"displayName","languageMap":{"en-US":"Mirror Service Component","zh-HK":"鏡像服務組件"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/caas-registry","property":"description","languageMap":{"en-US":"Provide platform Harbor and other image service support","zh-HK":"提供平臺Harbor等鏡像服務支援"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/caas-oam","property":"displayName","languageMap":{"en-US":"Platform alarm component","zh-HK":"平臺告警組件"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/caas-oam","property":"description","languageMap":{"en-US":"Platform alarm component","zh-HK":"平臺告警組件"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/caas-amp","property":"displayName","languageMap":{"en-US":"User component","zh-HK":"用戶組件"}},{"groupName":"k8s-resource-skyview-cloud-service-component","uniqueValue":"unified-platform/caas-amp","property":"description","languageMap":{"en-US":"User authentication system","zh-HK":"用戶認證體系"}}]}]}]