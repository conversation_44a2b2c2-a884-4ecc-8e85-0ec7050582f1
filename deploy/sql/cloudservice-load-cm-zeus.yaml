apiVersion: v1
kind: ConfigMap
metadata:
  labels:
    unified-platform.harmonycloud.cn/translate: ""
    unified-platform.harmonycloud.cn/cloudservice-name: zeus
    cloudservice/translate: ""
    version: xxxxx
  name: zeus-cloudservice-upload-config-v3.4.0
data:
  translate.json: |-
    [{"operator":"add","configs":[{"resourceTranslates":[{"groupName":"k8s-resource-skyview-cloud-service","uniqueValue":"zeus","property":"displayName","languageMap":{"en-US":"Middleware Services","zh-HK":"中介軟體服務"}},{"groupName":"k8s-resource-skyview-cloud-service","uniqueValue":"zeus","property":"description","languageMap":{"en-US":"Provide a fully automated middleware integrated management and supply capability based on the Kubernetes system for scenarios where the middleware technology stack is complex and difficult to implement in practice","zh-HK":"針對中介軟體科技棧複雜且實踐落地難度高的場景，提供基於Kubernetes體系打造的全自動化運維的中介軟體一體化管理和供給能力"}}]}]}]