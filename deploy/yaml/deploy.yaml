---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: portal-namespace
  namespace: caas-system
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: portal-clusterrole
rules:
  - apiGroups:
      - '*'
    resources:
      - '*'
    verbs:
      - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: portal-clusterrole-binding
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: portal-clusterrole
subjects:
  - kind: ServiceAccount
    namespace: caas-system
    name: portal-namespace
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: portal
  namespace: caas-system
spec:
  selector:
    matchLabels:
      app: portal
  template:
    metadata:
      labels:
        app: portal
    spec:
      serviceAccountName: portal-namespace
      containers:
        - name: portal
          image: 10.10.103.151:8443/k8s-deploy/portal:base_package
          command:
            - /backend-server
          ports:
            - containerPort: 8080
              name: http
              protocol: TCP
          resources:
            requests:
              cpu: 500m
              memory: 512Mi
            limits:
              cpu: 500m
              memory: 512Mi
---
apiVersion: v1
kind: Service
metadata:
  name: portal-svc
  namespace: caas-system
spec:
  selector:
    app: portal
  ports:
    - port: 8080
      name: http
      protocol: TCP
      targetPort: 8080
