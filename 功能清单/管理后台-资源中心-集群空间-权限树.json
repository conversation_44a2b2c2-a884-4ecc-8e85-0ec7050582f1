[{"id": "1919660867339685891", "name": "集群总览", "code": "unified_platform_sys_cluster_overview", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685890", "parentName": "集群管理详情", "parentCode": "unified_platform_sys_cluster_manage_detail", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": "cluster-space-overview-menu", "url": "/cluster/space/ClusterOverview", "method": 1, "visible": true, "annotations": "{\"resources\":[\"heimdallr.harmonycloud.cn/networkdetails\",\"nodes\",\"batch/cronjobs\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/networkresources\",\"secrets\",\"configmaps\",\"heimdallr.harmonycloud.cn/hdareas\",\"apps/statefulsets\",\"namespaces\",\"apps/replicasets\",\"heimdallr.harmonycloud.cn/hdblocks\",\"persistentvolumeclaims\",\"apps/deployments\",\"heimdallr.harmonycloud.cn/hdpools\",\"pods\",\"storage.k8s.io/storageclasses\",\"serviceaccounts\",\"persistentvolumes\",\"apps/daemonsets\",\"batch/jobs\",\"replicationcontrollers\",\"resourcequotas\",\"harmonycloud.cn/nodepools\",\"services\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": [{"id": "1919660867339685892", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685891", "parentName": "集群总览", "parentCode": "unified_platform_sys_cluster_overview", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"replicationcontrollers\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}]}, {"id": "1919660867339685893", "name": "主机管理", "code": "unified_platform_sys_node_manage", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685890", "parentName": "集群管理详情", "parentCode": "unified_platform_sys_cluster_manage_detail", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": "nodes-menu", "url": "", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": [{"id": "1919660867339685894", "name": "普通视角", "code": "unified_platform_sys_node_manage_normal", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685893", "parentName": "主机管理", "parentCode": "unified_platform_sys_node_manage", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/cluster/space/node/normal", "method": 1, "visible": true, "annotations": "{\"resources\":[\"nodes\",\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"events\",\"isolate.harmonycloud.cn/isolatelocks\",\"isolate.harmonycloud.cn/hleases\",\"harmonycloud.cn/nodepools\",\"pods\",\"stellaris.harmonycloud.cn/clustertopologies\",\"stellaris.harmonycloud.cn/servicesyncs\",\"services\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": [{"id": "1919660867339685895", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685894", "parentName": "普通视角", "parentCode": "unified_platform_sys_node_manage_normal", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/nodes/{nodeName}/resources,/clusters/{clusterName}/nodes/{nodeName}/nodeInfo,/clusters/{clusterName}/nodes/{nodeName}/events", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660867339685896", "name": "新增主机", "code": "add", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685894", "parentName": "普通视角", "parentCode": "unified_platform_sys_node_manage_normal", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/nodes", "method": 2, "visible": true, "annotations": "{\"component\":\"node-up-down\",\"componentName\":\"主机上下线\"}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660867339685897", "name": "主机维护", "code": "nodeMaintain", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685894", "parentName": "普通视角", "parentCode": "unified_platform_sys_node_manage_normal", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"create\",\"delete\",\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"delete\",\"get\",\"patch\",\"create\",\"update\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"delete\",\"get\",\"patch\",\"create\",\"update\"],\"namespaces\":[\"create\",\"delete\",\"get\",\"patch\",\"update\"],\"nodes\":[\"create\",\"get\",\"list\",\"patch\",\"update\"],\"stellaris.harmonycloud.cn/clusters\":[\"delete\",\"watch\",\"get\",\"list\",\"patch\",\"create\",\"update\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919660867339685898", "name": "应用迁移", "code": "nodeDrain", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685894", "parentName": "普通视角", "parentCode": "unified_platform_sys_node_manage_normal", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"create\",\"delete\",\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"delete\",\"get\",\"patch\",\"create\",\"update\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"delete\",\"get\",\"patch\",\"create\",\"update\"],\"namespaces\":[\"create\",\"delete\",\"get\",\"patch\",\"update\"],\"nodes\":[\"create\",\"get\",\"list\",\"patch\",\"update\"],\"stellaris.harmonycloud.cn/clusters\":[\"delete\",\"watch\",\"get\",\"list\",\"patch\",\"create\",\"update\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}, {"id": "1919660867339685900", "name": "调度", "code": "dispatch", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685894", "parentName": "普通视角", "parentCode": "unified_platform_sys_node_manage_normal", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 6, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/nodes/{nodeName}/cordon,/clusters/{clusterName}/nodes/{nodeName}/uncordon", "method": 3, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"patch\",\"update\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\",\"patch\",\"update\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"patch\",\"update\"],\"pods\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 6, "children": []}, {"id": "1919660867339685901", "name": "编辑注解", "code": "editAnnotations", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685894", "parentName": "普通视角", "parentCode": "unified_platform_sys_node_manage_normal", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 7, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/nodes/{nodeName}/annotations", "method": 3, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"patch\",\"update\"],\"pods\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 7, "children": []}, {"id": "1919660867339685902", "name": "编辑标签", "code": "edit<PERSON><PERSON><PERSON>", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685894", "parentName": "普通视角", "parentCode": "unified_platform_sys_node_manage_normal", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 8, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/nodes/{nodeName}/labels", "method": 3, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"patch\",\"update\"],\"pods\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 8, "children": []}, {"id": "1919660867339685903", "name": "编辑污点", "code": "edit<PERSON><PERSON><PERSON>", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685894", "parentName": "普通视角", "parentCode": "unified_platform_sys_node_manage_normal", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 9, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/nodes/{nodeName}/taints", "method": 3, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\",\"patch\",\"update\"],\"pods\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 9, "children": []}, {"id": "1919660867339685904", "name": "主机下线", "code": "nodeOffLine", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685894", "parentName": "普通视角", "parentCode": "unified_platform_sys_node_manage_normal", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 10, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/nodes/drain", "method": 2, "visible": true, "annotations": "{\"component\":\"node-up-down\",\"componentName\":\"主机上下线\",\"resource_option\":{\"nodes\":[\"get\",\"list\",\"patch\",\"update\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 10, "children": []}, {"id": "1919660867339685905", "name": "主机监控", "code": "skyview_node_monitor", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685894", "parentName": "普通视角", "parentCode": "unified_platform_sys_node_manage_normal", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 11, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"component\":\"monitoring\",\"componentName\":\"监控\"}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 11, "children": []}]}, {"id": "1919660867339685906", "name": "资源池视角", "code": "unified_platform_sys_node_manage_resource_pool", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685893", "parentName": "主机管理", "parentCode": "unified_platform_sys_node_manage", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": "/cluster/space/node/resourcepool", "method": 1, "visible": true, "annotations": "{\"resources\":[\"nodes\",\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"events\",\"isolate.harmonycloud.cn/isolatelocks\",\"isolate.harmonycloud.cn/hleases\",\"harmonycloud.cn/nodepools\",\"pods\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": [{"id": "1919660867339685907", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685906", "parentName": "资源池视角", "parentCode": "unified_platform_sys_node_manage_resource_pool", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/clusters/nodepools", "method": 1, "visible": true, "annotations": "{\"component\":\"node-pool\",\"componentName\":\"资源池\",\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}]}]}, {"id": "1919660867339685908", "name": "命名空间", "code": "unified_platform_sys_cluster_namespace_manager", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685890", "parentName": "集群管理详情", "parentCode": "unified_platform_sys_cluster_manage_detail", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": "namespace-menu", "url": "/cluster/space/namespace", "method": 1, "visible": true, "annotations": "{\"resources\":[\"apps/statefulsets\",\"pods\",\"namespaces\",\"batch/jobs\",\"resourcequotas\",\"heimdallr.harmonycloud.cn/networkresources\",\"nodes\",\"stellaris.harmonycloud.cn/clusters\",\"events\",\"harmonycloud.cn/nodepools\",\"heimdallr.harmonycloud.cn/hdareas\",\"isolate.harmonycloud.cn/isolatelocks\",\"apps/deployments\",\"persistentvolumeclaims\",\"storage.k8s.io/storageclasses\",\"heimdallr.harmonycloud.cn/hdblocks\",\"apps/daemonsets\",\"batch/cronjobs\",\"isolate.harmonycloud.cn/hleases\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/hdpools\",\"heimdallr.harmonycloud.cn/hdsvcs\",\"heimdallr.harmonycloud.cn/networkdetails\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": [{"id": "1919660867339685909", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685908", "parentName": "命名空间", "parentCode": "unified_platform_sys_cluster_namespace_manager", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/namespaces", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/statefulsets\":[\"list\",\"get\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660867339685910", "name": "新增", "code": "add", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685908", "parentName": "命名空间", "parentCode": "unified_platform_sys_cluster_namespace_manager", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/namespaces", "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/statefulsets\":[\"list\",\"get\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\",\"patch\",\"create\",\"update\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\",\"patch\",\"create\",\"update\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660867339685911", "name": "编辑元数据", "code": "edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685908", "parentName": "命名空间", "parentCode": "unified_platform_sys_cluster_namespace_manager", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/namespaces/{namespaces}", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/statefulsets\":[\"list\",\"get\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\",\"patch\",\"create\",\"update\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\",\"patch\",\"create\",\"update\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919660867339685912", "name": "编辑描述", "code": "editDescription", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685908", "parentName": "命名空间", "parentCode": "unified_platform_sys_cluster_namespace_manager", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/namespaces/{namespaces}/description", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/statefulsets\":[\"list\",\"get\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\",\"patch\",\"create\",\"update\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\",\"patch\",\"create\",\"update\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}, {"id": "1919660867339685913", "name": "删除", "code": "remove", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685908", "parentName": "命名空间", "parentCode": "unified_platform_sys_cluster_namespace_manager", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/namespaces/{namespaces}", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/statefulsets\":[\"list\",\"get\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\",\"patch\",\"create\",\"update\",\"delete\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\",\"patch\",\"create\",\"update\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": []}, {"id": "1919660867339685914", "name": "分配项目", "code": "allocate_project", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685908", "parentName": "命名空间", "parentCode": "unified_platform_sys_cluster_namespace_manager", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 6, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/statefulsets\":[\"list\",\"get\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\",\"update\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\",\"create\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 6, "children": []}]}, {"id": "1919660867339685915", "name": "集群组件", "code": "unified_platform_sys_cluster_component", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685890", "parentName": "集群管理详情", "parentCode": "unified_platform_sys_cluster_manage_detail", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": "components-menu", "url": "/cluster/space/clusterComp", "method": 1, "visible": true, "annotations": "{\"resources\":[\"pods\",\"resourcequotas\",\"stellaris.harmonycloud.cn/clusters\",\"events\",\"namespaces\",\"nodes\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": [{"id": "1919660867339685916", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685915", "parentName": "集群组件", "parentCode": "unified_platform_sys_cluster_component", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660867339685917", "name": "组件详情", "code": "detail", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685915", "parentName": "集群组件", "parentCode": "unified_platform_sys_cluster_component", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/components/{componentName}", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660867339685918", "name": "批量新增", "code": "batch", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685915", "parentName": "集群组件", "parentCode": "unified_platform_sys_cluster_component", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/components/switches", "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919660867339685919", "name": "编辑", "code": "edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685915", "parentName": "集群组件", "parentCode": "unified_platform_sys_cluster_component", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/components/{componentName}", "method": 3, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\",\"patch\",\"update\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}, {"id": "1919660867339685920", "name": "自动识别", "code": "autoRecognition", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685915", "parentName": "集群组件", "parentCode": "unified_platform_sys_cluster_component", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/components/scan", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\",\"patch\",\"update\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": []}, {"id": "1919660867339685921", "name": "接入", "code": "join", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685915", "parentName": "集群组件", "parentCode": "unified_platform_sys_cluster_component", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 6, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/components", "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\",\"patch\",\"update\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 6, "children": []}, {"id": "1919660867339685922", "name": "取消接入", "code": "remove", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685915", "parentName": "集群组件", "parentCode": "unified_platform_sys_cluster_component", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 7, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/components/{componentName}", "method": 4, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\",\"patch\",\"update\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 7, "children": []}]}, {"id": "1919660867339685923", "name": "工作负载", "code": "unified_platform_sys_clusters_workloads", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685890", "parentName": "集群管理详情", "parentCode": "unified_platform_sys_cluster_manage_detail", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": "load-menu", "url": "", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": [{"id": "1919660867339685924", "name": "无状态部署", "code": "unified_platform_sys_clusters_deployment", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685923", "parentName": "工作负载", "parentCode": "unified_platform_sys_clusters_workloads", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/cluster/space/resource/deployment/list", "method": 1, "visible": true, "annotations": "{\"resources\":[\"apps/deployments\",\"apps/replicasets\",\"events\",\"namespaces\",\"pods\",\"resourcequotas\",\"stellaris.harmonycloud.cn/clusters\",\"stellaris.harmonycloud.cn/multiclusterresources\",\"stellaris.harmonycloud.cn/multiclusterresourcebindings\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": [{"id": "1919660867339685925", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685924", "parentName": "无状态部署", "parentCode": "unified_platform_sys_clusters_deployment", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/deployments,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment},/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/describe,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/events,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/metadata,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/pods,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/replicasets,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/replicasets/{replicaset}/yaml,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/yaml", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660867339685926", "name": "新增", "code": "skyview_deployment_add", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685924", "parentName": "无状态部署", "parentCode": "unified_platform_sys_clusters_deployment", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/deployments", "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"create\",\"update\"],\"apps/replicasets\":[\"get\",\"list\",\"create\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660867339685927", "name": "编辑副本数", "code": "skyview_replicas_edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685924", "parentName": "无状态部署", "parentCode": "unified_platform_sys_clusters_deployment", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"create\",\"update\"],\"apps/replicasets\":[\"get\",\"list\",\"create\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919660867339685928", "name": "编辑元数据", "code": "skyview_deployment_metadata_edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685924", "parentName": "无状态部署", "parentCode": "unified_platform_sys_clusters_deployment", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"create\",\"update\"],\"apps/replicasets\":[\"get\",\"list\",\"create\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}, {"id": "1919660867339685929", "name": "版本管理", "code": "skyview_deployment_version", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685924", "parentName": "无状态部署", "parentCode": "unified_platform_sys_clusters_deployment", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"create\",\"update\"],\"apps/replicasets\":[\"get\",\"list\",\"create\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": []}, {"id": "1919660867339685930", "name": "查看版本yaml", "code": "skyview_deployment_yaml_check", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685924", "parentName": "无状态部署", "parentCode": "unified_platform_sys_clusters_deployment", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 6, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 6, "children": []}, {"id": "1919660867339685931", "name": "yaml对比", "code": "skyview_deployment_yaml_compare", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685924", "parentName": "无状态部署", "parentCode": "unified_platform_sys_clusters_deployment", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 7, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 7, "children": []}, {"id": "1919660867339685932", "name": "版本回滚", "code": "skyview_deployment_version_rollback", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685924", "parentName": "无状态部署", "parentCode": "unified_platform_sys_clusters_deployment", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 8, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/revisions/{revision}/rollback", "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 8, "children": []}, {"id": "1919660867339685933", "name": "编辑yaml", "code": "skyview_deployment_yaml_edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685924", "parentName": "无状态部署", "parentCode": "unified_platform_sys_clusters_deployment", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 9, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"update\"],\"apps/replicasets\":[\"get\",\"list\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 9, "children": []}, {"id": "1919660867339685934", "name": "删除", "code": "skyview_deployment_remove", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685924", "parentName": "无状态部署", "parentCode": "unified_platform_sys_clusters_deployment", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 10, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}", "method": 4, "visible": true, "annotations": "{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"delete\"],\"apps/replicasets\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 10, "children": []}]}, {"id": "1919660867339685935", "name": "有状态部署", "code": "unified_platform_sys_clusters_statefulset", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685923", "parentName": "工作负载", "parentCode": "unified_platform_sys_clusters_workloads", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": "/cluster/space/resource/statefulset/list", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"events\",\"resourcequotas\",\"apps/statefulsets\",\"pods\",\"stellaris.harmonycloud.cn/multiclusterresources\",\"stellaris.harmonycloud.cn/multiclusterresourcebindings\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": [{"id": "1919660867343880192", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685935", "parentName": "有状态部署", "parentCode": "unified_platform_sys_clusters_statefulset", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/namespaces/{namespace}/statefulsets/{statefulset},/clusters/{clusterName}/namespaces/{namespace}/statefulsets/{statefulset}/describe,/clusters/{clusterName}/namespaces/{namespace}/statefulsets/{statefulset}/events,/clusters/{clusterName}/namespaces/{namespace}/statefulsets/{statefulset}/metadata,/clusters/{clusterName}/namespaces/{namespace}/statefulsets/{statefulset}/pods,/clusters/{clusterName}/namespaces/{namespace}/statefulsets/{statefulset}/yaml,/clusters/{clusterName}/statefulsets", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/statefulsets\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660867343880193", "name": "新增", "code": "skyview_statefulset_add", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685935", "parentName": "有状态部署", "parentCode": "unified_platform_sys_clusters_statefulset", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/statefulsets", "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/statefulsets\":[\"get\",\"list\",\"create\",\"update\"],\"namespaces\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660867343880194", "name": "编辑副本数", "code": "skyview_replicas_edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685935", "parentName": "有状态部署", "parentCode": "unified_platform_sys_clusters_statefulset", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/statefulsets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919660867343880195", "name": "编辑元数据", "code": "skyview_statefulset_metadata_edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685935", "parentName": "有状态部署", "parentCode": "unified_platform_sys_clusters_statefulset", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/statefulsets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}, {"id": "1919660867343880196", "name": "编辑yaml", "code": "skyview_statefulset_yaml_edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685935", "parentName": "有状态部署", "parentCode": "unified_platform_sys_clusters_statefulset", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/statefulsets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": []}, {"id": "1919660867343880197", "name": "删除", "code": "skyview_statefulset_remove", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685935", "parentName": "有状态部署", "parentCode": "unified_platform_sys_clusters_statefulset", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 6, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/namespaces/{namespace}/statefulsets/{statefulset}", "method": 4, "visible": true, "annotations": "{\"resource_option\":{\"apps/statefulsets\":[\"get\",\"list\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 6, "children": []}]}, {"id": "1919660867343880198", "name": "守护进程", "code": "unified_platform_sys_clusters_daemonset", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685923", "parentName": "工作负载", "parentCode": "unified_platform_sys_clusters_workloads", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": "/cluster/space/resource/daemonset/list", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"apps/daemonsets\",\"pods\",\"stellaris.harmonycloud.cn/multiclusterresources\",\"stellaris.harmonycloud.cn/multiclusterresourcebindings\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": [{"id": "1919660867343880199", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880198", "parentName": "守护进程", "parentCode": "unified_platform_sys_clusters_daemonset", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/daemonsets,/clusters/{clusterName}/namespaces/{namespace}/daemonsets/{daemonset},/clusters/{clusterName}/namespaces/{namespace}/daemonsets/{daemonset}/describe,/clusters/{clusterName}/namespaces/{namespace}/daemonsets/{daemonset}/events,/clusters/{clusterName}/namespaces/{namespace}/daemonsets/{daemonset}/metadata,/clusters/{clusterName}/namespaces/{namespace}/daemonsets/{daemonset}/pods,/clusters/{clusterName}/namespaces/{namespace}/daemonsets/{daemonset}/yaml", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660867343880200", "name": "新增", "code": "skyview_daemonset_add", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880198", "parentName": "守护进程", "parentCode": "unified_platform_sys_clusters_daemonset", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/daemonsets", "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\",\"create\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660867343880201", "name": "编辑元数据", "code": "skyview_daemonset_metadata_edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880198", "parentName": "守护进程", "parentCode": "unified_platform_sys_clusters_daemonset", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919660867343880202", "name": "编辑yaml", "code": "skyview_daemonset_yaml_edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880198", "parentName": "守护进程", "parentCode": "unified_platform_sys_clusters_daemonset", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}, {"id": "1919660867343880203", "name": "删除", "code": "skyview_daemonset_remove", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880198", "parentName": "守护进程", "parentCode": "unified_platform_sys_clusters_daemonset", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/namespaces/{namespace}/daemonsets/{daemonset}", "method": 4, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": []}]}, {"id": "1919660867343880204", "name": "普通任务", "code": "unified_platform_sys_clusters_job", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685923", "parentName": "工作负载", "parentCode": "unified_platform_sys_clusters_workloads", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": "/cluster/space/resource/job/list", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"batch/jobs\",\"pods\",\"stellaris.harmonycloud.cn/multiclusterresources\",\"stellaris.harmonycloud.cn/multiclusterresourcebindings\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": [{"id": "1919660867343880205", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880204", "parentName": "普通任务", "parentCode": "unified_platform_sys_clusters_job", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/jobs,/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}/describe,/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}/events,/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}/info,/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}/metadata,/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}/pods,/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}/yaml", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660867343880206", "name": "新增", "code": "skyview_job_add", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880204", "parentName": "普通任务", "parentCode": "unified_platform_sys_clusters_job", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/jobs", "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"batch/jobs\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660867343880207", "name": "重新执行", "code": "skyview_job_restart", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880204", "parentName": "普通任务", "parentCode": "unified_platform_sys_clusters_job", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}/start", "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"batch/jobs\":[\"get\",\"list\",\"create\",\"update\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919660867343880208", "name": "编辑元数据", "code": "skyview_job_metadata_edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880204", "parentName": "普通任务", "parentCode": "unified_platform_sys_clusters_job", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"batch/jobs\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}, {"id": "1919660867343880209", "name": "编辑yaml", "code": "skyview_job_yaml_edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880204", "parentName": "普通任务", "parentCode": "unified_platform_sys_clusters_job", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"batch/jobs\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": []}, {"id": "1919660867343880210", "name": "删除", "code": "skyview_job_remove", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880204", "parentName": "普通任务", "parentCode": "unified_platform_sys_clusters_job", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 6, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}", "method": 4, "visible": true, "annotations": "{\"resource_option\":{\"batch/jobs\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 6, "children": []}]}, {"id": "1919660867343880211", "name": "定时任务", "code": "unified_platform_sys_clusters_cronjob", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685923", "parentName": "工作负载", "parentCode": "unified_platform_sys_clusters_workloads", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": "/cluster/space/resource/cronjob/list", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"batch/cronjobs\",\"pods\",\"stellaris.harmonycloud.cn/multiclusterresources\",\"stellaris.harmonycloud.cn/multiclusterresourcebindings\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": [{"id": "1919660867343880212", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880211", "parentName": "定时任务", "parentCode": "unified_platform_sys_clusters_cronjob", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/cronjobs,/clusters/{clusterName}/namespaces/{namespace}/cronjob/{cronjobName}/yaml,/clusters/{clusterName}/namespaces/{namespace}/cronjobs/{cronjobName}/describe,/clusters/{clusterName}/namespaces/{namespace}/cronjobs/{cronjobName}/events,/clusters/{clusterName}/namespaces/{namespace}/cronjobs/{cronjobName}/info,/clusters/{clusterName}/namespaces/{namespace}/cronjobs/{cronjobName}/jobs,/clusters/{clusterName}/namespaces/{namespace}/cronjobs/{cronjobName}/metadata", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660867343880213", "name": "新增", "code": "add", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880211", "parentName": "定时任务", "parentCode": "unified_platform_sys_clusters_cronjob", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/cronjobs", "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660867343880214", "name": "启停", "code": "schedule", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880211", "parentName": "定时任务", "parentCode": "unified_platform_sys_clusters_cronjob", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/namespaces/{namespace}/cronjobs/{cronjobName}", "method": 3, "visible": true, "annotations": "{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919660867343880215", "name": "编辑元数据", "code": "editMetaData", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880211", "parentName": "定时任务", "parentCode": "unified_platform_sys_clusters_cronjob", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/namespaces/{namespace}/cronjobs/{cronjobName}/metadata", "method": 3, "visible": true, "annotations": "{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}, {"id": "1919660867343880216", "name": "编辑策略", "code": "editInfo", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880211", "parentName": "定时任务", "parentCode": "unified_platform_sys_clusters_cronjob", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/namespaces/{namespace}/cronjob/{cronjobName}", "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": []}, {"id": "1919660867343880217", "name": "编辑yaml", "code": "<PERSON><PERSON><PERSON><PERSON>", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880211", "parentName": "定时任务", "parentCode": "unified_platform_sys_clusters_cronjob", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 6, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/cronjob/yaml", "method": 3, "visible": true, "annotations": "{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 6, "children": []}, {"id": "1919660867343880218", "name": "删除执行记录", "code": "deleteJob", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880211", "parentName": "定时任务", "parentCode": "unified_platform_sys_clusters_cronjob", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 7, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/namespaces/{namespace}/jobs/{jobName}", "method": 4, "visible": true, "annotations": "{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 7, "children": []}, {"id": "1919660867343880219", "name": "删除", "code": "delete", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880211", "parentName": "定时任务", "parentCode": "unified_platform_sys_clusters_cronjob", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 8, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/namespaces/{namespace}/cronjob/{cronjobName}", "method": 4, "visible": true, "annotations": "{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 8, "children": []}]}, {"id": "1919660867343880220", "name": "Pod容器组", "code": "unified_platform_sys_clusters_pod_container_group", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685923", "parentName": "工作负载", "parentCode": "unified_platform_sys_clusters_workloads", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 6, "kind": 1, "icon": null, "url": "/cluster/space/resource/pod", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"harmonycloud.cn/nodepools\",\"events\",\"pods\",\"nodes\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 6, "children": [{"id": "1919660867343880221", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880220", "parentName": "Pod容器组", "parentCode": "unified_platform_sys_clusters_pod_container_group", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/pods/filter,/clusters/{clusterName}/pods,/clusters/{clusterName}/namespaces/{namespace}/pods/{podName},/clusters/{clusterName}/namespaces/{namespace}/pods/{podName}/containers", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660867343880222", "name": "文件路径查询", "code": "pod_file_pwd", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880220", "parentName": "Pod容器组", "parentCode": "unified_platform_sys_clusters_pod_container_group", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"pods\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660867343880223", "name": "文件下载", "code": "pod_file_download", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880220", "parentName": "Pod容器组", "parentCode": "unified_platform_sys_clusters_pod_container_group", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/pods/filter,/clusters/{clusterName}/pods,/clusters/{clusterName}/namespaces/{namespace}/pods/{podName},/clusters/{clusterName}/namespaces/{namespace}/pods/{podName}/containers", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"pods\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919660867343880224", "name": "监控", "code": "monitor", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880220", "parentName": "Pod容器组", "parentCode": "unified_platform_sys_clusters_pod_container_group", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": "/cluster/{clusterName}/namespace/{namespaces}/getPodResource", "method": 1, "visible": true, "annotations": "{\"component\":\"monitoring\",\"componentName\":\"监控\",\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}, {"id": "1919660867343880225", "name": "pod日志", "code": "containerLog", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880220", "parentName": "Pod容器组", "parentCode": "unified_platform_sys_clusters_pod_container_group", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/namespaces/{namespace}/pods/{podName}/containers/{container}/stderrlogs", "method": 5, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"watch\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": []}, {"id": "1919660867343880226", "name": "pod控制台", "code": "containerTerminal", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880220", "parentName": "Pod容器组", "parentCode": "unified_platform_sys_clusters_pod_container_group", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 6, "kind": 1, "icon": null, "url": "/podTerminal", "method": 5, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 6, "children": []}, {"id": "1919660867343880227", "name": "事件", "code": "event", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880220", "parentName": "Pod容器组", "parentCode": "unified_platform_sys_clusters_pod_container_group", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 7, "kind": 1, "icon": null, "url": "/cluster/{clusterName}/namespaces/{namespace}/pods/{podName}/events", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 7, "children": []}, {"id": "1919660867343880228", "name": "查看yaml", "code": "yaml", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880220", "parentName": "Pod容器组", "parentCode": "unified_platform_sys_clusters_pod_container_group", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 8, "kind": 1, "icon": null, "url": "/cluster/{clusterName}/namespaces/{namespace}/pods/{podName}/yaml", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 8, "children": []}, {"id": "1919660867343880229", "name": "删除", "code": "remove", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880220", "parentName": "Pod容器组", "parentCode": "unified_platform_sys_clusters_pod_container_group", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 9, "kind": 1, "icon": null, "url": "/cluster/{clusterName}/namespaces/{namespace}/pods/{podName}", "method": 4, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 9, "children": []}]}]}, {"id": "1919660867343880230", "name": "配置挂载", "code": "unified_platform_sys_cluster_config_mount", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685890", "parentName": "集群管理详情", "parentCode": "unified_platform_sys_cluster_manage_detail", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 6, "kind": 1, "icon": "configure-mount-menu", "url": "", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 6, "children": [{"id": "1919660867343880231", "name": "配置文件", "code": "unified_platform_sys_cluster_configmap", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880230", "parentName": "配置挂载", "parentCode": "unified_platform_sys_cluster_config_mount", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/cluster/space/configMap/list", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"stellaris.harmonycloud.cn/multiclusterresources\",\"stellaris.harmonycloud.cn/multiclusterresourcebindings\",\"events\",\"pods\",\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"apps/replicasets\",\"batch/jobs\",\"batch/cronjobs\",\"configmaps\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": [{"id": "1919660867343880232", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880231", "parentName": "配置文件", "parentCode": "unified_platform_sys_cluster_configmap", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\",\"watch\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660867343880233", "name": "新增", "code": "skyview_configmap_add", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880231", "parentName": "配置文件", "parentCode": "unified_platform_sys_cluster_configmap", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660867343880234", "name": "编辑挂载数据", "code": "skyview_configmap_data_edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880231", "parentName": "配置文件", "parentCode": "unified_platform_sys_cluster_configmap", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919660867343880235", "name": "编辑元数据", "code": "skyview_configmap_meta_data_edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880231", "parentName": "配置文件", "parentCode": "unified_platform_sys_cluster_configmap", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}, {"id": "1919660867343880236", "name": "编辑Yaml", "code": "skyview_configmap_yaml_edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880231", "parentName": "配置文件", "parentCode": "unified_platform_sys_cluster_configmap", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": []}, {"id": "1919660867343880237", "name": "删除", "code": "skyview_configmap_remove", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880231", "parentName": "配置文件", "parentCode": "unified_platform_sys_cluster_configmap", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 6, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 6, "children": []}]}, {"id": "1919660867343880238", "name": "保密字典", "code": "unified_platform_sys_cluster_secret", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880230", "parentName": "配置挂载", "parentCode": "unified_platform_sys_cluster_config_mount", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": "/cluster/space/secret/list", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"pods\",\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"apps/replicasets\",\"batch/jobs\",\"batch/cronjobs\",\"secrets\",\"stellaris.harmonycloud.cn/multiclusterresources\",\"stellaris.harmonycloud.cn/multiclusterresourcebindings\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": [{"id": "1919660867343880239", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880238", "parentName": "保密字典", "parentCode": "unified_platform_sys_cluster_secret", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660867343880240", "name": "新增", "code": "skyview_secret_add", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880238", "parentName": "保密字典", "parentCode": "unified_platform_sys_cluster_secret", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660867343880241", "name": "编辑加密数据", "code": "skyview_secret_data_edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880238", "parentName": "保密字典", "parentCode": "unified_platform_sys_cluster_secret", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919660867343880242", "name": "编辑元数据", "code": "skyview_secret_meta_data_edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880238", "parentName": "保密字典", "parentCode": "unified_platform_sys_cluster_secret", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}, {"id": "1919660867343880243", "name": "编辑Yaml", "code": "skyview_secret_yaml_edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880238", "parentName": "保密字典", "parentCode": "unified_platform_sys_cluster_secret", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": []}, {"id": "1919660867343880244", "name": "删除", "code": "skyview_secret_remove", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880238", "parentName": "保密字典", "parentCode": "unified_platform_sys_cluster_secret", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 6, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 6, "children": []}]}]}, {"id": "1919660867343880245", "name": "存储", "code": "unified_platform_sys_cluster_storage_service", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685890", "parentName": "集群管理详情", "parentCode": "unified_platform_sys_cluster_manage_detail", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 7, "kind": 1, "icon": "storage-menu", "url": "", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 7, "children": [{"id": "1919660867343880246", "name": "存储卷声明", "code": "unified_platform_sys_cluster_pvc", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880245", "parentName": "存储", "parentCode": "unified_platform_sys_cluster_storage_service", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/cluster/space/pvc/list", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"pods\",\"storage.k8s.io/storageclasses\",\"persistentvolumeclaims\",\"persistentvolumes\",\"stellaris.harmonycloud.cn/multiclusterresources\",\"stellaris.harmonycloud.cn/multiclusterresourcebindings\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": [{"id": "1919660867348074496", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880246", "parentName": "存储卷声明", "parentCode": "unified_platform_sys_cluster_pvc", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/pvc,/clusters/{clusterName}/pvc/{pvcName}/describe,/clusters/{clusterName}/pvc/{pvcName}/events,/clusters/{clusterName}/pvc/{pvcName}/info,/clusters/{clusterName}/pvc/{pvcName}/pods,/clusters/{clusterName}/pvc/{pvcName}/yaml,/clusters/{clusterName}/pvc/available", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660867348074497", "name": "新增", "code": "skyview_pvc_add", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880246", "parentName": "存储卷声明", "parentCode": "unified_platform_sys_cluster_pvc", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\",\"create\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660867348074498", "name": "扩容", "code": "skyview_pvc_expand", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880246", "parentName": "存储卷声明", "parentCode": "unified_platform_sys_cluster_pvc", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\",\"update\",\"patch\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919660867348074499", "name": "编辑元数据", "code": "skyview_pvc_meta_data_edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880246", "parentName": "存储卷声明", "parentCode": "unified_platform_sys_cluster_pvc", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\",\"update\",\"patch\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}, {"id": "1919660867348074500", "name": "编辑Yaml", "code": "skyview_pvc_yaml_edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880246", "parentName": "存储卷声明", "parentCode": "unified_platform_sys_cluster_pvc", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\",\"update\",\"patch\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": []}, {"id": "1919660867348074501", "name": "删除", "code": "skyview_pvc_remove", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880246", "parentName": "存储卷声明", "parentCode": "unified_platform_sys_cluster_pvc", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 6, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\",\"delete\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 6, "children": []}]}, {"id": "1919660867348074502", "name": "存储卷", "code": "unified_platform_sys_cluster_pv", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880245", "parentName": "存储", "parentCode": "unified_platform_sys_cluster_storage_service", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": "/cluster/space/pv/list", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"pods\",\"storage.k8s.io/storageclasses\",\"persistentvolumeclaims\",\"persistentvolumes\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": [{"id": "1919660867348074503", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074502", "parentName": "存储卷", "parentCode": "unified_platform_sys_cluster_pv", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/persistentvolume,/clusters/{clusterName}/persistentvolume/{pvName},/clusters/{clusterName}/persistentvolume/bound/pods,/clusters/{clusterName}/persistentvolume/custom/storage,/clusters/{clusterName}/persistentvolume/describe,/clusters/{clusterName}/persistentvolume/events,/clusters/{clusterName}/persistentvolume/storageclass/names,/clusters/{clusterName}/persistentvolume/yaml", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660867348074504", "name": "新增", "code": "skyview_pv_add", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074502", "parentName": "存储卷", "parentCode": "unified_platform_sys_cluster_pv", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\",\"create\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660867348074505", "name": "编辑元数据", "code": "skyview_pv_meta_data_edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074502", "parentName": "存储卷", "parentCode": "unified_platform_sys_cluster_pv", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919660867348074506", "name": "编辑Yaml", "code": "skyview_pv_yaml_edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074502", "parentName": "存储卷", "parentCode": "unified_platform_sys_cluster_pv", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}, {"id": "1919660867348074507", "name": "删除", "code": "skyview_pv_remove", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074502", "parentName": "存储卷", "parentCode": "unified_platform_sys_cluster_pv", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\",\"delete\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": []}]}, {"id": "1919660867348074508", "name": "存储服务", "code": "unified_platform_sys_cluster_storage_class", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867343880245", "parentName": "存储", "parentCode": "unified_platform_sys_cluster_storage_service", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": "/cluster/space/scService/list", "method": 1, "visible": true, "annotations": "{\"resources\":[\"namespaces\",\"persistentvolumes\",\"pods\",\"resourcequotas\",\"events\",\"stellaris.harmonycloud.cn/clusters\",\"apps/deployments\",\"configmaps\",\"services\",\"secrets\",\"persistentvolumeclaims\",\"storage.k8s.io/storageclasses\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": [{"id": "1919660867348074509", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074508", "parentName": "存储服务", "parentCode": "unified_platform_sys_cluster_storage_class", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/storageservices,/clusters/{clusterName}/storageservices/{storageServiceName},/clusters/{clusterName}/storageservices/{storageServiceName}/associatedresources", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660867348074510", "name": "新增", "code": "skyview_sc_service_add", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074508", "parentName": "存储服务", "parentCode": "unified_platform_sys_cluster_storage_class", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\",\"create\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660867348074511", "name": "扩容", "code": "skyview_sc_service_expand", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074508", "parentName": "存储服务", "parentCode": "unified_platform_sys_cluster_storage_class", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\",\"update\",\"patch\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919660867348074512", "name": "删除", "code": "skyview_sc_service_remove", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074508", "parentName": "存储服务", "parentCode": "unified_platform_sys_cluster_storage_class", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"delete\"],\"configmaps\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\",\"delete\"],\"persistentvolumes\":[\"get\",\"list\",\"delete\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"delete\"],\"services\":[\"get\",\"list\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\",\"delete\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}]}]}, {"id": "1919660867348074513", "name": "网络", "code": "unified_platform_sys_cluster_network_manager", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685890", "parentName": "集群管理详情", "parentCode": "unified_platform_sys_cluster_manage_detail", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 8, "kind": 1, "icon": "network-menu", "url": "", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 8, "children": [{"id": "1919660867348074514", "name": "Service服务", "code": "unified_platform_sys_cluster_services", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074513", "parentName": "网络", "parentCode": "unified_platform_sys_cluster_network_manager", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/cluster/space/network/service", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"services\",\"expose.helper.harmonycloud.cn/layer4exposes\",\"networking.k8s.io/ingresses\",\"expose.helper.harmonycloud.cn/ingressclasses\",\"configmaps\",\"endpoints\",\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"apps/replicasets\",\"pods\",\"nodes\",\"stellaris.harmonycloud.cn/multiclusterresources\",\"stellaris.harmonycloud.cn/multiclusterresourcebindings\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": [{"id": "1919660867348074515", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074514", "parentName": "Service服务", "parentCode": "unified_platform_sys_cluster_services", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"endpoints\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660867348074516", "name": "新增", "code": "skyview_service_add", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074514", "parentName": "Service服务", "parentCode": "unified_platform_sys_cluster_services", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"create\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660867348074517", "name": "编辑", "code": "skyview_service_edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074514", "parentName": "Service服务", "parentCode": "unified_platform_sys_cluster_services", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919660867348074518", "name": "新增四层对外路由", "code": "skyview_service_add_four_layer_exponse", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074514", "parentName": "Service服务", "parentCode": "unified_platform_sys_cluster_services", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}, {"id": "1919660867348074519", "name": "新增七层对外路由", "code": "skyview_service_add_seven_layer_exponse", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074514", "parentName": "Service服务", "parentCode": "unified_platform_sys_cluster_services", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": []}, {"id": "1919660867348074520", "name": "编辑对外路由", "code": "edit_domains", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074514", "parentName": "Service服务", "parentCode": "unified_platform_sys_cluster_services", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 6, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 6, "children": []}, {"id": "1919660867348074521", "name": "删除对外路由", "code": "remove_domains", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074514", "parentName": "Service服务", "parentCode": "unified_platform_sys_cluster_services", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 7, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 7, "children": []}, {"id": "1919660867348074522", "name": "编辑元数据", "code": "skyview_service_metadata_edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074514", "parentName": "Service服务", "parentCode": "unified_platform_sys_cluster_services", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 8, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 8, "children": []}, {"id": "1919660867348074523", "name": "编辑yaml", "code": "skyview_service_yaml_edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074514", "parentName": "Service服务", "parentCode": "unified_platform_sys_cluster_services", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 9, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 9, "children": []}, {"id": "1919660867348074524", "name": "删除", "code": "skyview_service_delete", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074514", "parentName": "Service服务", "parentCode": "unified_platform_sys_cluster_services", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 10, "kind": 1, "icon": null, "url": null, "method": 4, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 10, "children": []}]}, {"id": "1919660867348074525", "name": "Ingress路由", "code": "unified_platform_sys_cluster_ingress", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074513", "parentName": "网络", "parentCode": "unified_platform_sys_cluster_network_manager", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": "/cluster/space/network/ingress", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"configmaps\",\"networking.k8s.io/ingresses\",\"apps/daemonsets\",\"apisix.apache.org/apisixroutes\",\"resourcequotas\",\"secrets\",\"namespaces\",\"apps/statefulsets\",\"nodes\",\"services\",\"pods\",\"events\",\"apps/replicasets\",\"endpoints\",\"stellaris.harmonycloud.cn/multiclusterresources\",\"expose.helper.harmonycloud.cn/ingressclasses\",\"expose.helper.harmonycloud.cn/layer4exposes\",\"apps/deployments\",\"stellaris.harmonycloud.cn/multiclusterresourcebindings\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": [{"id": "1919660867348074526", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074525", "parentName": "Ingress路由", "parentCode": "unified_platform_sys_cluster_ingress", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\"],\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"endpoints\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\",\"watch\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660867348074527", "name": "新增 Nginx", "code": "add_nginx", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074525", "parentName": "Ingress路由", "parentCode": "unified_platform_sys_cluster_ingress", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660867348074528", "name": "编辑Nginx", "code": "edit_nginx", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074525", "parentName": "Ingress路由", "parentCode": "unified_platform_sys_cluster_ingress", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919660867348074529", "name": "删除Nginx", "code": "delete_nginx", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074525", "parentName": "Ingress路由", "parentCode": "unified_platform_sys_cluster_ingress", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}, {"id": "1919660867348074530", "name": "新增APISIX", "code": "add_apisix", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074525", "parentName": "Ingress路由", "parentCode": "unified_platform_sys_cluster_ingress", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": null, "method": 3, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": []}, {"id": "1919660867348074531", "name": "编辑APISIX", "code": "edit_apisix", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074525", "parentName": "Ingress路由", "parentCode": "unified_platform_sys_cluster_ingress", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 6, "kind": 1, "icon": null, "url": null, "method": 3, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 6, "children": []}, {"id": "1919660867348074532", "name": "删除APISIX", "code": "delete_apisix", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074525", "parentName": "Ingress路由", "parentCode": "unified_platform_sys_cluster_ingress", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 7, "kind": 1, "icon": null, "url": null, "method": 3, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 7, "children": []}, {"id": "1919660867348074533", "name": "新增TLS", "code": "add_tls", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074525", "parentName": "Ingress路由", "parentCode": "unified_platform_sys_cluster_ingress", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 8, "kind": 1, "icon": null, "url": null, "method": 3, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 8, "children": []}, {"id": "1919660867348074534", "name": "编辑TLS", "code": "edit_tls", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074525", "parentName": "Ingress路由", "parentCode": "unified_platform_sys_cluster_ingress", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 9, "kind": 1, "icon": null, "url": null, "method": 3, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 9, "children": []}, {"id": "1919660867348074535", "name": "删除TLS", "code": "delete_tls", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074525", "parentName": "Ingress路由", "parentCode": "unified_platform_sys_cluster_ingress", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 10, "kind": 1, "icon": null, "url": null, "method": 3, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 10, "children": []}, {"id": "1919660867348074536", "name": "新增会话保持", "code": "add_session", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074525", "parentName": "Ingress路由", "parentCode": "unified_platform_sys_cluster_ingress", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 11, "kind": 1, "icon": null, "url": null, "method": 3, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 11, "children": []}, {"id": "1919660867348074537", "name": "编辑会话保持", "code": "edit_session", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074525", "parentName": "Ingress路由", "parentCode": "unified_platform_sys_cluster_ingress", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 12, "kind": 1, "icon": null, "url": null, "method": 3, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 12, "children": []}, {"id": "1919660867348074538", "name": "删除会话保持", "code": "delete_session", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074525", "parentName": "Ingress路由", "parentCode": "unified_platform_sys_cluster_ingress", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 13, "kind": 1, "icon": null, "url": null, "method": 3, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 13, "children": []}]}, {"id": "1919660867348074539", "name": "网络域", "code": "unified_platform_sys_cluster_network_area", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074513", "parentName": "网络", "parentCode": "unified_platform_sys_cluster_network_manager", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": "/cluster/space/network/networkarea", "method": 1, "visible": true, "annotations": "{\"resources\":[\"heimdallr.harmonycloud.cn/networkdetails\",\"resourcequotas\",\"mystra.heimdallr.harmonycloud.cn/podpolicies\",\"isolate.harmonycloud.cn/hleases\",\"heimdallr.harmonycloud.cn/hdsvcs\",\"stellaris.harmonycloud.cn/multiclusterresources\",\"events\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/hdpools\",\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"pods\",\"heimdallr.harmonycloud.cn/networkresources\",\"heimdallr.harmonycloud.cn/hdblocks\",\"heimdallr.harmonycloud.cn/hdareas\",\"configmaps\",\"stellaris.harmonycloud.cn/multiclusterresourcebindings\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": [{"id": "1919660867348074540", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074539", "parentName": "网络域", "parentCode": "unified_platform_sys_cluster_network_area", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/clusters/networkAreas", "method": 1, "visible": true, "annotations": "{\"component\":\"heimdallr\",\"componentName\":\"统一网络模型\",\"resource_option\":{\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\",\"watch\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}]}, {"id": "1919660867348074541", "name": "网络IP池", "code": "unified_platform_sys_cluster_network_ip_pool", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074513", "parentName": "网络", "parentCode": "unified_platform_sys_cluster_network_manager", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": "/cluster/space/network/ippool", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"heimdallr.harmonycloud.cn/networkresources\",\"heimdallr.harmonycloud.cn/networkdetails\",\"heimdallr.harmonycloud.cn/hdareas\",\"heimdallr.harmonycloud.cn/hdblocks\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/hdpools\",\"heimdallr.harmonycloud.cn/hdsvcs\",\"isolate.harmonycloud.cn/hleases\",\"mystra.heimdallr.harmonycloud.cn/podpolicies\",\"configmaps\",\"pods\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": [{"id": "1919660867348074542", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074541", "parentName": "网络IP池", "parentCode": "unified_platform_sys_cluster_network_ip_pool", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/clusters/networkIpPools", "method": 1, "visible": true, "annotations": "{\"component\":\"heimdallr\",\"componentName\":\"统一网络模型\",\"resource_option\":{\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}]}, {"id": "1919660867348074543", "name": "负载均衡", "code": "unified_platform_sys_cluster_network_loadbalance", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074513", "parentName": "网络", "parentCode": "unified_platform_sys_cluster_network_manager", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": "/cluster/space/network/loadbalance", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"services\",\"expose.helper.harmonycloud.cn/layer4exposes\",\"networking.k8s.io/ingresses\",\"expose.helper.harmonycloud.cn/ingressclasses\",\"apisix.apache.org/apisixroutes\",\"configmaps\",\"secrets\",\"endpoints\",\"pods\",\"nodes\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": [{"id": "1919660867352268800", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074543", "parentName": "负载均衡", "parentCode": "unified_platform_sys_cluster_network_loadbalance", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"endpoints\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660867352268801", "name": "新增", "code": "add", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074543", "parentName": "负载均衡", "parentCode": "unified_platform_sys_cluster_network_loadbalance", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\",\"create\",\"update\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660867352268802", "name": "编辑", "code": "edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074543", "parentName": "负载均衡", "parentCode": "unified_platform_sys_cluster_network_loadbalance", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 3, "visible": true, "annotations": "{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919660867352268807", "name": "nginx配置", "code": "edit_nginx", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074543", "parentName": "负载均衡", "parentCode": "unified_platform_sys_cluster_network_loadbalance", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": null, "method": 3, "visible": true, "annotations": "{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}, {"id": "1919660867352268808", "name": "删除", "code": "remove", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867348074543", "parentName": "负载均衡", "parentCode": "unified_platform_sys_cluster_network_loadbalance", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"nodes\":[\"get\",\"list\",\"update\",\"patch\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": []}]}]}, {"id": "1919660867352268809", "name": "Kubernetes资源", "code": "unified_platform_sys_kubernetes_resource", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685890", "parentName": "集群管理详情", "parentCode": "unified_platform_sys_cluster_manage_detail", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 9, "kind": 1, "icon": "ip-pool-menu", "url": "/cluster/space/kubernetesres", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 9, "children": [{"id": "1919660867352268810", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867352268809", "parentName": "Kubernetes资源", "parentCode": "unified_platform_sys_kubernetes_resource", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660867352268811", "name": "编辑Yaml", "code": "skyview_cluster_resource_yaml_edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867352268809", "parentName": "Kubernetes资源", "parentCode": "unified_platform_sys_kubernetes_resource", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660867352268812", "name": "删除", "code": "skyview_cluster_resource_remove", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867352268809", "parentName": "Kubernetes资源", "parentCode": "unified_platform_sys_kubernetes_resource", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}]}, {"id": "1919660867352268813", "name": "集群空间设置", "code": "unified_platform_cluster_space_setting", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685890", "parentName": "集群管理详情", "parentCode": "unified_platform_sys_cluster_manage_detail", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 10, "kind": 1, "icon": "space-setting-menu", "url": "", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 10, "children": [{"id": "1919660867352268814", "name": "网络策略", "code": "unified_platform_sys_cluster_network_podpolicy", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867352268813", "parentName": "集群空间设置", "parentCode": "unified_platform_cluster_space_setting", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/cluster/space/network/strategy", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"heimdallr.harmonycloud.cn/networkresources\",\"heimdallr.harmonycloud.cn/networkdetails\",\"heimdallr.harmonycloud.cn/hdareas\",\"heimdallr.harmonycloud.cn/hdblocks\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/hdpools\",\"heimdallr.harmonycloud.cn/hdsvcs\",\"isolate.harmonycloud.cn/hleases\",\"mystra.heimdallr.harmonycloud.cn/podpolicies\",\"configmaps\",\"pods\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": [{"id": "1919660867352268815", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867352268814", "parentName": "网络策略", "parentCode": "unified_platform_sys_cluster_network_podpolicy", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/clusters/{clusterName}/podPolicys/overview,/clusters/{clusterName}/namespaces/podPolicys", "method": 1, "visible": true, "annotations": "{\"component\":\"acl\",\"componentName\":\"网络隔离\",\"resource_option\":{\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660867352268816", "name": "添加策略", "code": "add", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867352268814", "parentName": "网络策略", "parentCode": "unified_platform_sys_cluster_network_podpolicy", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"component\":\"acl\",\"componentName\":\"网络隔离\",\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660867352268817", "name": "删除策略", "code": "remove", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867352268814", "parentName": "网络策略", "parentCode": "unified_platform_sys_cluster_network_podpolicy", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919660867352268818", "name": "开启/关闭策略", "code": "edit_state", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867352268814", "parentName": "网络策略", "parentCode": "unified_platform_sys_cluster_network_podpolicy", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}, {"id": "1919660867352268819", "name": "编辑策略", "code": "edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867352268814", "parentName": "网络策略", "parentCode": "unified_platform_sys_cluster_network_podpolicy", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": []}]}, {"id": "1919660867352268820", "name": "集群设置", "code": "unified_platform_sys_cluster_setting", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867352268813", "parentName": "集群空间设置", "parentCode": "unified_platform_cluster_space_setting", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": "/cluster/space/clusterSetting", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"isolate.harmonycloud.cn/isolatelocks\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": [{"id": "1919660867352268821", "name": "集群隔离锁重置", "code": "clusterIsolateLock", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867352268820", "parentName": "集群设置", "parentCode": "unified_platform_sys_cluster_setting", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\",\"create\",\"update\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660867352268822", "name": "集群删除", "code": "remove", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867352268820", "parentName": "集群设置", "parentCode": "unified_platform_sys_cluster_setting", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\",\"delete\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}]}, {"id": "1919660867352268823", "name": "Kubeconfig", "code": "unified_platform_sys_cluster_kubeconfig", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867352268813", "parentName": "集群空间设置", "parentCode": "unified_platform_cluster_space_setting", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": "/cluster/space/myCluster", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": [{"id": "1919660867352268824", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867352268823", "parentName": "Kubeconfig", "parentCode": "unified_platform_sys_cluster_kubeconfig", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/cluster/space/myCluster/kubeconfig", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"stellaris.harmonycloud.cn/clusters\":[\"watch\",\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660867352268825", "name": "下载", "code": "cluster_kubeconfig_download", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867352268823", "parentName": "Kubeconfig", "parentCode": "unified_platform_sys_cluster_kubeconfig", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": "clusters/{clusterName}/kubeconfig/download", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"stellaris.harmonycloud.cn/clusters\":[\"watch\",\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}]}]}, {"id": "3901589339600248832", "name": "集群巡检", "code": "cluster_inspection", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660867339685890", "parentName": "集群管理详情", "parentCode": "unified_platform_sys_cluster_manage_detail", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 11, "kind": 1, "icon": "v35_ClusterMonitor", "url": "/monitor/cluster/space/clusterInspection", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 11, "children": [{"id": "3901596533200707584", "name": "巡检策略开始巡检", "code": "cluster_inspection_policy_execute", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "3901589339600248832", "parentName": "集群巡检", "parentCode": "cluster_inspection", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}]}]