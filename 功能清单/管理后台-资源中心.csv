模块,功能点,功能描述,依赖集群组件
资源中心,集群总览,支持集群资源、工作负载、组件、主机、网络等数据的可视化查看及跳转,
资源中心,集群管理,支持通过指令快速纳管集群,"stellaris（集群纳管）
sisyphus（集群创建）"
资源中心,集群管理,支持集群标签和描述的可视化管理,"stellaris（集群纳管）
sisyphus（集群创建）"
资源中心,集群管理,支持集群集群开启、关闭、删除,"stellaris（集群纳管）
sisyphus（集群创建）"
资源中心,集群管理,支持展示集群状态、主机数量、集群版本、主机架构、网络类型等参数,"stellaris（集群纳管）
sisyphus（集群创建）"
资源中心,集群管理,支持集群控制台（终端）能力,"stellaris（集群纳管）
sisyphus（集群创建）"
资源中心,集群管理,集群空间详细能力见sheet「管理后台-集群空间」,"stellaris（集群纳管）
sisyphus（集群创建）"
资源中心,主机管理,支持主机的可视化上、下线,"sisyphus（节点上下线）
node-isolation（主机隔离）
prometheus（节点监控）
"
资源中心,主机管理,支持主机维护，并迁移业务pod到其它的主机上,"sisyphus（节点上下线）
node-isolation（主机隔离）
prometheus（节点监控）
"
资源中心,主机管理,支持主机隔离，当节点故障的时候可隔离主机拒绝业务pod的调度,"sisyphus（节点上下线）
node-isolation（主机隔离）
prometheus（节点监控）
"
资源中心,主机管理,支持批量添加arm、x86架构的主机，还可查看添加主机的进度,"sisyphus（节点上下线）
node-isolation（主机隔离）
prometheus（节点监控）
"
资源中心,主机管理,支持纳管和使用具有GPU的主机,"sisyphus（节点上下线）
node-isolation（主机隔离）
prometheus（节点监控）
"
资源中心,主机管理,支持节点GPU物理卡的算力和显存虚拟化,"sisyphus（节点上下线）
node-isolation（主机隔离）
prometheus（节点监控）
"
资源中心,主机管理,支持主机状态及CPU、内存、GPU算力、虚拟GPU显存、物理/虚拟GPU卡等资源使用情况的可视化展示,"sisyphus（节点上下线）
node-isolation（主机隔离）
prometheus（节点监控）
"
资源中心,主机管理,支持标签和污点的可视化管理,"sisyphus（节点上下线）
node-isolation（主机隔离）
prometheus（节点监控）
"
资源中心,主机管理,支持对主机是否允许调度可视化管理,"sisyphus（节点上下线）
node-isolation（主机隔离）
prometheus（节点监控）
"
资源中心,主机管理,支持实时查看主机上运行的全部Pod情况,"sisyphus（节点上下线）
node-isolation（主机隔离）
prometheus（节点监控）
"
资源中心,主机管理,支持实时查看主机的CPU、内存、磁盘、网络的使用情况,"sisyphus（节点上下线）
node-isolation（主机隔离）
prometheus（节点监控）
"
资源中心,主机管理,支持查看event事件及description详细信息,"sisyphus（节点上下线）
node-isolation（主机隔离）
prometheus（节点监控）
"
资源中心,主机管理,支持主机资源池创建、删除及资源池内主机数量的增删调整,node-pool（资源池）
资源中心,主机管理,支持主机资源池资源（CPU、内存、虚拟GPU显存、GPU算力、虚拟GPU卡、物理GPU卡）向组织及项目的分配,node-pool（资源池）
资源中心,命名空间,可查看集群下的全部命名空间及各命名空间的资源总量及用量,原生支持
资源中心,命名空间,支持单、多集群命名空间新增及删除,stellaris
资源中心,命名空间,支持从项目维度为命名空间进行资源池&存储服务&网络等资源分配、编辑、删除,原生支持
资源中心,命名空间,可按命名空间查看全部类型的K8S资源，且支持查看自定义类型的K8S资源,heimdallr-统一网络模型
资源中心,命名空间,支持以租户、命名空间、服务为维度，设置命名空间的网络隔离策略,原生支持
资源中心,命名空间,支持项目命名空间配额的扩缩容操作,原生支持
资源中心,命名空间,支持标签和注解查看、编辑,原生支持
资源中心,命名空间,支持查看event事件及description详细信息,原生支持
资源中心,命名空间,支持查看、下载yaml,原生支持
资源中心,集群组件,支持对组件及系统组件的统一管理,stellaris
资源中心,集群组件,支持对集群组件的自动识别扫描并接入，并监控接入状态,stellaris
资源中心,集群组件,支持查看集群组件的中文名称及功能描述,stellaris
资源中心,集群组件,支持组件的参数及yaml编辑、取消接入操作,stellaris
资源中心,集群组件,支持组件实例状态监控,stellaris
资源中心,集群组件,支持组件本身的健康状态监控及异常消息提示,stellaris
资源中心,资源池,支持将集群主机资源（CPU，内存，GPU，显存）进行资源池化，实现精细化管理，实现单个集群的不同资源池划分，帮助用户管理繁杂的污点和标签,node-pool（资源池）
资源中心,资源池,支持资源池管理器的注入机制，自动生成绑定特定资源池的污点和标签,node-pool（资源池）
资源中心,资源池,支持选择主机和选择污点、标签的规则可灵活创建资源池,node-pool（资源池）
资源中心,存储服务,支持表单新增（包括但不限于NFS、CEPH-RBD、GlusterFS、YRCloudFile、CephFS）、删除,平台支持
资源中心,存储服务,支持特定容量创建，而且可后续扩容,平台支持
资源中心,存储服务,支持查看存储资源和已分配量、已占用量、查看基础信息、关联资源等信息,平台支持
资源中心,存储服务,支持组织、项目资源分配、编辑、删除,平台支持
资源中心,存储服务,支持所有CSI标准的存储定制化接入,平台支持
资源中心,网络规划,任何一个集群可支持macvlan、calico等多种CNI顶层网络规划,heimdallr-统一网络模型
资源中心,网络规划,任何一种macvlan、calico或其它CNI可同时支持ipv4/ipv6网络管理,heimdallr-统一网络模型
资源中心,网络规划,支持顶层网络规划的网络可继续划分规划为网络域、网络IP池,heimdallr-统一网络模型
资源中心,网络域,支持表单创建、编辑、删除,heimdallr-统一网络模型
资源中心,网络域,支持单个或批量分配组织、项目并查看、编辑,heimdallr-统一网络模型
资源中心,网络域,支持全局共享查看、编辑,heimdallr-统一网络模型
资源中心,网络域,同一个网络域支持多个网络CNI、ipv4和ipv6网段共存,heimdallr-统一网络模型
资源中心,网络域,支持可视化查看各规划网段的IP已分配、可规划、预留等情况,heimdallr-统一网络模型
资源中心,网络域,支持查看已划分出去的网络IP池,heimdallr-统一网络模型
资源中心,网络IP池,支持表单创建、编辑、删除,heimdallr-统一网络模型
资源中心,网络IP池,支持单个或批量分配组织、项目并查看、编辑,heimdallr-统一网络模型
资源中心,网络IP池,支持全局共享查看、编辑,heimdallr-统一网络模型
资源中心,网络IP池,同一个网络IP池可创建固定（可指定IP个数及指定特定某几个IP）可随机IP池2种类型,heimdallr-统一网络模型
资源中心,网络IP池,同一个网络IP池支持规划同一个网络域内特定一种网络CNI，ipv4和ipv6网段共存,heimdallr-统一网络模型
资源中心,网络IP池,支持可视化查看各规划网段的IP已分配、可规划、预留等情况,heimdallr-统一网络模型
资源中心,网络IP池,支持查看已被工作负载使用及分配出去的IP,heimdallr-统一网络模型
资源中心,网络IP池,支持查看已被工作负载暂时锁定的IP并且可手动解锁IP,heimdallr-统一网络模型
资源中心,虚拟机网络,1. 支持Kube-OVN虚拟机网段划分与分配；,kube-ovn
资源中心,虚拟机网络,2. 支持IPv4/IPv6 双栈虚拟机；,kube-ovn
资源中心,虚拟机网络,3. 支持虚拟机IP固定；,kube-ovn
资源中心,网络策略,基于iptabels、networkpolicy的网络微隔离，提供了集群容器间网络策略下发和管理能力，通过设置防护对象的出站和入站规则实现命名空间/pod/ip维度的容器间网络访问控制,mystra
资源中心,网络模板,支持系统/项目层面应用网络模板多网卡（calico & macvlan、双栈）创建、编辑、删除,heimdallr-统一网络模型
资源中心,网络模板,支持系统层面网络模板全平台项目共享、项目层自建的网络模板则项目自己使用,heimdallr-统一网络模型
资源中心,网络模板,应用组件支持网络模板使用，配置多张网卡，每张网卡可指定不同网络CNI类型/ipv4或ipv6/IP可指定个数也可指定特定的IP,heimdallr-统一网络模型
资源中心,网络模板,应用组件支持网卡IP池扩缩容管理及查看IP状态,heimdallr-统一网络模型
资源中心,网络配置,支持基于不同集群pod配置全部允许/禁止/及自定义规则配置不同的网络隔离策略,heimdallr-统一网络模型
资源中心,网络配置,自定义网络隔离支持基于pod、命名空间、网络域及IP池网段及IP的规则配置,heimdallr-统一网络模型
资源中心,网络配置,支持网络策略规则开启、关闭,heimdallr-统一网络模型
资源中心,网络配置,支持网络隔离规则基础信息详情查看、二次编辑、删除,heimdallr-统一网络模型
资源中心,网络配置,支持一张网卡可分配的IP最大、最小IP地址范围自主配置、修改,heimdallr-统一网络模型
资源中心,备份服务器,支持新增、编辑、移除S3类型的备份服务器,s3存储
资源中心,备份服务器,支持新增、编辑、删除存储桶,s3存储
资源中心,备份服务器,支持将存储桶分配到租户,s3存储
资源中心,负载均衡,支持创建、编辑、删除基于Nginx及APISIX的负载均衡并进行高级参数配置修改,nginx，APISIX，Traefik
资源中心,负载均衡,可按全局、或特定组织/项目划分负载均衡的使用权限,nginx，APISIX，Traefik
资源中心,负载均衡,支持基于Nginx实现4层/7层负载均衡,nginx，APISIX，Traefik
资源中心,负载均衡,支持基于APISIX实现7层负载均衡,nginx，APISIX，Traefik
资源中心,负载均衡,支持对负载均衡进行可视化的域名和证书管理,nginx，APISIX，Traefik
资源中心,负载均衡,支持Https、http访问,nginx，APISIX，Traefik
资源中心,负载均衡,支持可视化查看负载均衡当前关联了哪些服务,nginx，APISIX，Traefik
资源中心,制品服务,支持可视化对接第三方Harbor,harbor
资源中心,制品服务,支持纳管开源的Habor镜像仓库、编辑、删除,harbor
资源中心,制品服务,支持可视化查看镜像仓库服务中的仓库数、镜像数、及镜像仓库服务的运行状态,harbor
资源中心,制品服务,支持项目仓库的创建、分配、编辑（仓库label）、删除、上传镜像,harbor
资源中心,制品服务,支持对项目仓库设置存储和镜像数量限额，查看镜像仓库的存储用量,harbor
资源中心,制品服务,支持对项目仓库进行安全漏洞扫描，并查看扫描情况,harbor
资源中心,制品服务,支持对项目仓库设置备份和同步规则，并支持自动或手动触发,harbor
资源中心,制品服务,支持对项目仓库设置内容信任、阻止不同危害级别的harbor镜像、添加CVE白名单,harbor
资源中心,制品服务,支持项目仓库的定时清理功能，镜像清理可选择基于版本和时间清理，同时镜像清理可选择需保留的镜像版本,harbor
资源中心,制品服务,支持定时对项目仓库进行垃圾清理，并查看清理任务的执行情况,harbor
资源中心,制品服务,支持按组织/项目分配公有、私有镜像仓库,harbor
资源中心,制品服务,公有仓库分配给组织项目后，项目层面具备公有仓库上传、删除、打镜像label等能力,harbor
资源中心,制品服务,harbor公有仓库可进行上传、删除、打镜像label精细化配置管理,harbor
资源中心,制品服务,支持对项目仓库内镜像进行多版本管理,harbor
资源中心,制品服务,支持对项目仓库内单个镜像查看基本信息、构建记录、漏洞扫描信息,harbor
资源中心,制品服务,支持对项目仓库内单个镜像进行复制pull指令、打label、上传、拉取&下载、仓库间复制传送指定镜像等管理,harbor
资源中心,备份服务器,支持S3备份服务器接入&管理,s3存储
资源中心,备份服务器,支持从备份服务器中存储桶的数据同步和增删改查,s3存储
资源中心,备份服务器,支持存储桶在租户/项目维度的资源分配能力（一个存储桶只能分配一个项目）,s3存储
