[{"id": "1919660881512239104", "name": "门户概览", "code": "unified_platform_sys_overview", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1814620967884820480", "parentName": "门户管理", "parentCode": "platform", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": "v35_PortalOverview", "url": "/quotacenter/space/overview", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clustersets\",\"crd.projectcalico.org/hostendpoints\",\"core.oam.dev/workloaddefinitions\",\"crd.projectcalico.org/clusterinformations\",\"replicationcontrollers\",\"componentstatuses\",\"batch/cronjobs\",\"mysql.middleware.harmonycloud.cn/mysqlreplicates\",\"storage.k8s.io/storageclasses\",\"crd.projectcalico.org/felixconfigurations\",\"rbac.authorization.k8s.io/rolebindings\",\"podtemplates\",\"configmaps\",\"batch/jobs\",\"redis.middleware.hc.cn/redisclusters\",\"mystra.heimdallr.harmonycloud.cn/identities\",\"keda.sh/scaledjobs\",\"secrets\",\"heimdallr.harmonycloud.cn/hdsvcs\",\"core.oam.dev/componentdefinitions\",\"persistentvolumeclaims\",\"apps/controllerrevisions\",\"crd.projectcalico.org/globalnetworkpolicies\",\"stellaris.harmonycloud.cn/multiclusterhorizontalpodautoscalers\",\"snapshot.storage.k8s.io/volumesnapshotclasses\",\"core.oam.dev/applications\",\"core.oam.dev/workflowstepdefinitions\",\"monitoring.coreos.com/prometheusrules\",\"policy/poddisruptionbudgets\",\"harmonycloud.cn/nodepools\",\"limitranges\",\"networking.k8s.io/ingresses\",\"monitoring.coreos.com/prometheuses\",\"rollouts.kruise.io/rollouts\",\"autoscaling.alibabacloud.com/cronhorizontalpodautoscalers\",\"scheduling.k8s.io/priorityclasses\",\"events.k8s.io/events\",\"crd.projectcalico.org/networksets\",\"crd.projectcalico.org/ipamblocks\",\"discovery.k8s.io/endpointslices\",\"core.oam.dev/policydefinitions\",\"core.oam.dev/applicationrevisions\",\"crd.projectcalico.org/ipamconfigs\",\"admissionregistration.k8s.io/mutatingwebhookconfigurations\",\"stellaris.harmonycloud.cn/multiclusterresourceschedulepolicies\",\"crd.projectcalico.org/blockaffinities\",\"stellaris.harmonycloud.cn/resourceaggregatepolicies\",\"namespaces\",\"crd.projectcalico.org/kubecontrollersconfigurations\",\"heimdallr.harmonycloud.cn/networkresources\",\"standard.oam.dev/rollouts\",\"mysql.middleware.harmonycloud.cn/mysqlbackups\",\"apps/replicasets\",\"harmonycloud.cn/podsecuritypolicytemplates\",\"monitoring.coreos.com/podmonitors\",\"rbac.authorization.k8s.io/clusterrolebindings\",\"stellaris.harmonycloud.cn/multiclusterresourceaggregatepolicies\",\"core.oam.dev/workflows\",\"monitoring.coreos.com/servicemonitors\",\"snapshot.storage.k8s.io/volumesnapshots\",\"storage.k8s.io/csidrivers\",\"certificates.k8s.io/certificatesigningrequests\",\"crd.projectcalico.org/networkpolicies\",\"crd.projectcalico.org/ipreservations\",\"apiregistration.k8s.io/apiservices\",\"es.middleware.hc.cn/esclusters\",\"crd.projectcalico.org/ipamhandles\",\"events\",\"isolate.harmonycloud.cn/hleases\",\"webhook.harmonycloud.cn/isolationwebhookconfigurations\",\"networking.k8s.io/ingressclasses\",\"nodes\",\"apps/deployments\",\"expose.helper.harmonycloud.cn/layer4exposes\",\"rollouts.kruise.io/batchreleases\",\"monitoring.coreos.com/alertmanagers\",\"keda.sh/scaledobjects\",\"rocketmq.middleware.hc.cn/brokerclusters\",\"apiextensions.k8s.io/customresourcedefinitions\",\"admissionregistration.k8s.io/validatingwebhookconfigurations\",\"crd.projectcalico.org/ippools\",\"heimdallr.harmonycloud.cn/hdblocks\",\"cluster.core.oam.dev/clustergateways\",\"networking.k8s.io/networkpolicies\",\"apps/daemonsets\",\"mysql.middleware.harmonycloud.cn/mysqlclusters\",\"coordination.k8s.io/leases\",\"persistentvolumes\",\"isolate.harmonycloud.cn/isolatelocks\",\"application.decompile.harmonycloud.cn/decompileconfigs\",\"stellaris.harmonycloud.cn/multiclusterresourceaggregaterules\",\"stellaris.harmonycloud.cn/clusters\",\"resourcequotas\",\"core.oam.dev/traitdefinitions\",\"mysql.middleware.harmonycloud.cn/mysqlbackupschedules\",\"snapshot.storage.k8s.io/volumesnapshotcontents\",\"heimdallr.harmonycloud.cn/hdareas\",\"serviceaccounts\",\"mystra.heimdallr.harmonycloud.cn/podpolicies\",\"rbac.authorization.k8s.io/clusterroles\",\"flowcontrol.apiserver.k8s.io/prioritylevelconfigurations\",\"bifrost.heimdallr.harmonycloud.cn/subnets\",\"metrics.k8s.io/nodes\",\"expose.helper.harmonycloud.cn/ingressclasses\",\"heimdallr.harmonycloud.cn/networkdetails\",\"extensions/ingresses\",\"storage.k8s.io/csinodes\",\"stellaris.harmonycloud.cn/namespacemappings\",\"crd.projectcalico.org/caliconodestatuses\",\"services\",\"crd.projectcalico.org/globalnetworksets\",\"bindings\",\"crd.projectcalico.org/bgpconfigurations\",\"stellaris.harmonycloud.cn/clusterresources\",\"flowcontrol.apiserver.k8s.io/flowschemas\",\"keda.sh/clustertriggerauthentications\",\"heimdallr.harmonycloud.cn/hdpods\",\"metrics.k8s.io/pods\",\"endpoints\",\"heimdallr.harmonycloud.cn/hdpools\",\"rbac.authorization.k8s.io/roles\",\"storage.k8s.io/volumeattachments\",\"crd.projectcalico.org/bgppeers\",\"keda.sh/triggerauthentications\",\"core.oam.dev/scopedefinitions\",\"k8s.cni.cncf.io/network-attachment-definitions\",\"stellaris.harmonycloud.cn/multiclusterresourcebindings\",\"heimdallr.harmonycloud.cn/iplocks\",\"mystra.heimdallr.harmonycloud.cn/clusternetworkpolicies\",\"stellaris.harmonycloud.cn/aggregatedresources\",\"apps/statefulsets\",\"autoscaling/horizontalpodautoscalers\",\"stellaris.harmonycloud.cn/multiclusterresources\",\"core.oam.dev/resourcetrackers\",\"pods\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": [{"id": "1919660881512239105", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660881512239104", "parentName": "门户概览", "parentCode": "unified_platform_sys_overview", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"admissionregistration.k8s.io/mutatingwebhookconfigurations\":[\"get\",\"list\"],\"admissionregistration.k8s.io/validatingwebhookconfigurations\":[\"get\",\"list\"],\"apiextensions.k8s.io/customresourcedefinitions\":[\"get\",\"list\"],\"apiregistration.k8s.io/apiservices\":[\"get\",\"list\"],\"application.decompile.harmonycloud.cn/decompileconfigs\":[\"get\",\"list\"],\"apps/controllerrevisions\":[\"get\",\"list\"],\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"autoscaling.alibabacloud.com/cronhorizontalpodautoscalers\":[\"get\",\"list\"],\"autoscaling/horizontalpodautoscalers\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"bifrost.heimdallr.harmonycloud.cn/subnets\":[\"get\",\"list\"],\"bindings\":[\"get\",\"list\"],\"certificates.k8s.io/certificatesigningrequests\":[\"get\",\"list\"],\"cluster.core.oam.dev/clustergateways\":[\"get\",\"list\"],\"componentstatuses\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"coordination.k8s.io/leases\":[\"get\",\"list\"],\"core.oam.dev/applicationrevisions\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\"],\"core.oam.dev/componentdefinitions\":[\"get\",\"list\"],\"core.oam.dev/policydefinitions\":[\"get\",\"list\"],\"core.oam.dev/resourcetrackers\":[\"get\",\"list\"],\"core.oam.dev/scopedefinitions\":[\"get\",\"list\"],\"core.oam.dev/traitdefinitions\":[\"get\",\"list\"],\"core.oam.dev/workflows\":[\"get\",\"list\"],\"core.oam.dev/workflowstepdefinitions\":[\"get\",\"list\"],\"core.oam.dev/workloaddefinitions\":[\"get\",\"list\"],\"crd.projectcalico.org/bgpconfigurations\":[\"get\",\"list\"],\"crd.projectcalico.org/bgppeers\":[\"get\",\"list\"],\"crd.projectcalico.org/blockaffinities\":[\"get\",\"list\"],\"crd.projectcalico.org/caliconodestatuses\":[\"get\",\"list\"],\"crd.projectcalico.org/clusterinformations\":[\"get\",\"list\"],\"crd.projectcalico.org/felixconfigurations\":[\"get\",\"list\"],\"crd.projectcalico.org/globalnetworkpolicies\":[\"get\",\"list\"],\"crd.projectcalico.org/globalnetworksets\":[\"get\",\"list\"],\"crd.projectcalico.org/hostendpoints\":[\"get\",\"list\"],\"crd.projectcalico.org/ipamblocks\":[\"get\",\"list\"],\"crd.projectcalico.org/ipamconfigs\":[\"get\",\"list\"],\"crd.projectcalico.org/ipamhandles\":[\"get\",\"list\"],\"crd.projectcalico.org/ippools\":[\"get\",\"list\"],\"crd.projectcalico.org/ipreservations\":[\"get\",\"list\"],\"crd.projectcalico.org/kubecontrollersconfigurations\":[\"get\",\"list\"],\"crd.projectcalico.org/networkpolicies\":[\"get\",\"list\"],\"crd.projectcalico.org/networksets\":[\"get\",\"list\"],\"discovery.k8s.io/endpointslices\":[\"get\",\"list\"],\"endpoints\":[\"get\",\"list\"],\"es.middleware.hc.cn/esclusters\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"events.k8s.io/events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"extensions/ingresses\":[\"get\",\"list\"],\"flowcontrol.apiserver.k8s.io/flowschemas\":[\"get\",\"list\"],\"flowcontrol.apiserver.k8s.io/prioritylevelconfigurations\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\",\"list\"],\"harmonycloud.cn/podsecuritypolicytemplates\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/iplocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/isolatelocks\":[\"get\",\"list\"],\"k8s.cni.cncf.io/network-attachment-definitions\":[\"get\",\"list\"],\"keda.sh/clustertriggerauthentications\":[\"get\",\"list\"],\"keda.sh/scaledjobs\":[\"get\",\"list\"],\"keda.sh/scaledobjects\":[\"get\",\"list\"],\"keda.sh/triggerauthentications\":[\"get\",\"list\"],\"limitranges\":[\"get\",\"list\"],\"metrics.k8s.io/nodes\":[\"get\",\"list\"],\"metrics.k8s.io/pods\":[\"get\",\"list\"],\"monitoring.coreos.com/alertmanagers\":[\"get\",\"list\"],\"monitoring.coreos.com/podmonitors\":[\"get\",\"list\"],\"monitoring.coreos.com/prometheuses\":[\"get\",\"list\"],\"monitoring.coreos.com/prometheusrules\":[\"get\",\"list\"],\"monitoring.coreos.com/servicemonitors\":[\"get\",\"list\"],\"mysql.middleware.harmonycloud.cn/mysqlbackups\":[\"get\",\"list\"],\"mysql.middleware.harmonycloud.cn/mysqlbackupschedules\":[\"get\",\"list\"],\"mysql.middleware.harmonycloud.cn/mysqlclusters\":[\"get\",\"list\"],\"mysql.middleware.harmonycloud.cn/mysqlreplicates\":[\"get\",\"list\"],\"mystra.heimdallr.harmonycloud.cn/clusternetworkpolicies\":[\"get\",\"list\"],\"mystra.heimdallr.harmonycloud.cn/identities\":[\"get\",\"list\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingressclasses\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\"],\"networking.k8s.io/networkpolicies\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"podtemplates\":[\"get\",\"list\"],\"policy/poddisruptionbudgets\":[\"get\",\"list\"],\"rbac.authorization.k8s.io/clusterrolebindings\":[\"get\",\"list\"],\"rbac.authorization.k8s.io/clusterroles\":[\"get\",\"list\"],\"rbac.authorization.k8s.io/rolebindings\":[\"get\",\"list\"],\"rbac.authorization.k8s.io/roles\":[\"get\",\"list\"],\"redis.middleware.hc.cn/redisclusters\":[\"get\",\"list\"],\"replicationcontrollers\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"rocketmq.middleware.hc.cn/brokerclusters\":[\"get\",\"list\"],\"rollouts.kruise.io/batchreleases\":[\"get\",\"list\"],\"rollouts.kruise.io/rollouts\":[\"get\",\"list\"],\"scheduling.k8s.io/priorityclasses\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"snapshot.storage.k8s.io/volumesnapshotclasses\":[\"get\",\"list\"],\"snapshot.storage.k8s.io/volumesnapshotcontents\":[\"get\",\"list\"],\"snapshot.storage.k8s.io/volumesnapshots\":[\"get\",\"list\"],\"standard.oam.dev/rollouts\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/aggregatedresources\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusterresources\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"watch\",\"get\",\"list\",\"list\"],\"stellaris.harmonycloud.cn/clustersets\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterhorizontalpodautoscalers\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourceaggregatepolicies\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourceaggregaterules\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourceschedulepolicies\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/namespacemappings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/resourceaggregatepolicies\":[\"get\",\"list\"],\"storage.k8s.io/csidrivers\":[\"get\",\"list\"],\"storage.k8s.io/csinodes\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"],\"storage.k8s.io/volumeattachments\":[\"get\",\"list\"],\"webhook.harmonycloud.cn/isolationwebhookconfigurations\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}]}, {"id": "1919655217100492801", "name": "企业组织", "code": "user", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1814620967884820480", "parentName": "门户管理", "parentCode": "platform", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": "v35_OrganizationalStructure", "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": [{"id": "1919655217100492802", "name": "租户管理", "code": "orgList", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492801", "parentName": "企业组织", "parentCode": "user", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": "/api/application/attachment/filename/组织管理.svg", "url": "/amp/orgList", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": [{"id": "1919655217100492803", "name": "新增", "code": "orgAdd", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492802", "parentName": "租户管理", "parentCode": "orgList", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660886847393792", "name": "配额", "code": "orgQuotaManage", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492802", "parentName": "租户管理", "parentCode": "orgList", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919655217100492804", "name": "删除", "code": "orgDelete", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492802", "parentName": "租户管理", "parentCode": "orgList", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919655217100492805", "name": "详情", "code": "orgDetailPage", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492802", "parentName": "租户管理", "parentCode": "orgList", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919655217100492806", "name": "编辑", "code": "orgEdit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492802", "parentName": "租户管理", "parentCode": "orgList", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": []}]}, {"id": "1919655217100492807", "name": "部门管理", "code": "departmentMgr", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492801", "parentName": "企业组织", "parentCode": "user", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": "/amp/departmentList", "method": 1, "visible": true, "annotations": "{\"resources\":[\"rbac.authorization.k8s.io/clusterrolebindings\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": [{"id": "1919655217100492808", "name": "新增", "code": "departmentAdd", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492807", "parentName": "部门管理", "parentCode": "departmentMgr", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919655217100492809", "name": "编辑", "code": "departmentUpdate", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492807", "parentName": "部门管理", "parentCode": "departmentMgr", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919655217100492810", "name": "删除", "code": "departmentDelete", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492807", "parentName": "部门管理", "parentCode": "departmentMgr", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"rbac.authorization.k8s.io/clusterrolebindings\":[\"get\",\"list\",\"delete\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919655217100492811", "name": "新增成员", "code": "departmentUserAdd", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492807", "parentName": "部门管理", "parentCode": "departmentMgr", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"rbac.authorization.k8s.io/clusterrolebindings\":[\"get\",\"list\",\"create\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}, {"id": "1919655217100492812", "name": "变更部门", "code": "departmentUserChange", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492807", "parentName": "部门管理", "parentCode": "departmentMgr", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"rbac.authorization.k8s.io/clusterrolebindings\":[\"get\",\"list\",\"create\",\"update\",\"patch\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": []}]}, {"id": "1919655217100492813", "name": "用户管理", "code": "userList", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492801", "parentName": "企业组织", "parentCode": "user", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": "/amp/userList", "method": 1, "visible": true, "annotations": "{\"resources\":[\"rbac.authorization.k8s.io/clusterrolebindings\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": [{"id": "1919655217100492814", "name": "新增", "code": "userAdd", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492813", "parentName": "用户管理", "parentCode": "userList", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"rbac.authorization.k8s.io/clusterrolebindings\":[\"get\",\"list\",\"create\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919655217100492815", "name": "启用/停用", "code": "userEnable", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492813", "parentName": "用户管理", "parentCode": "userList", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919655217100492816", "name": "删除", "code": "userDelete", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492813", "parentName": "用户管理", "parentCode": "userList", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"rbac.authorization.k8s.io/clusterrolebindings\":[\"get\",\"list\",\"delete\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919655217100492817", "name": "用户详情", "code": "userDetail", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492813", "parentName": "用户管理", "parentCode": "userList", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": null, "method": 1, "visible": false, "annotations": "{\"resources\":[\"rbac.authorization.k8s.io/clusterrolebindings\",\"rbac.authorization.k8s.io/rolebindings\",\"rbac.authorization.k8s.io/roles\",\"rbac.authorization.k8s.io/clusterroles\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": [{"id": "1919655217100492818", "name": "删除", "code": "userDetailDelete", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492817", "parentName": "用户详情", "parentCode": "userDetail", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"rbac.authorization.k8s.io/clusterrolebindings\":[\"get\",\"list\",\"delete\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919655217100492819", "name": "启用/停用", "code": "userEnable", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492817", "parentName": "用户详情", "parentCode": "userDetail", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919655217100492820", "name": "进/出租户", "code": "userOrganJR", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492817", "parentName": "用户详情", "parentCode": "userDetail", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"rbac.authorization.k8s.io/clusterrolebindings\":[\"get\",\"list\",\"delete\",\"create\",\"update\",\"patch\"],\"rbac.authorization.k8s.io/clusterroles\":[\"get\",\"list\"],\"rbac.authorization.k8s.io/rolebindings\":[\"get\",\"list\",\"delete\",\"create\",\"update\",\"patch\"],\"rbac.authorization.k8s.io/roles\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919655217100492821", "name": "个人信息", "code": "userInfo", "type": 3, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492817", "parentName": "用户详情", "parentCode": "userDetail", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": [{"id": "1919655217100492822", "name": "编辑", "code": "userInfoEdit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492821", "parentName": "个人信息", "parentCode": "userInfo", "parentType": 3, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}]}, {"id": "1919655217100492823", "name": "关联角色", "code": "userRole", "type": 3, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492817", "parentName": "用户详情", "parentCode": "userDetail", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resources\":[\"rbac.authorization.k8s.io/clusterrolebindings\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": [{"id": "1919655217100492824", "name": "新增", "code": "userRoleAdd", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492823", "parentName": "关联角色", "parentCode": "userRole", "parentType": 3, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"rbac.authorization.k8s.io/clusterrolebindings\":[\"get\",\"list\",\"create\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919655217100492825", "name": "移除", "code": "userRoleDelete", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492823", "parentName": "关联角色", "parentCode": "userRole", "parentType": 3, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"rbac.authorization.k8s.io/clusterrolebindings\":[\"get\",\"list\",\"delete\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}]}]}, {"id": "1919655217100492826", "name": "重置密码", "code": "passWordReset", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492813", "parentName": "用户管理", "parentCode": "userList", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": []}]}, {"id": "1919655217100492827", "name": "角色管理", "code": "roleMng", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492801", "parentName": "企业组织", "parentCode": "user", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": "/application/roleMng", "method": 1, "visible": true, "annotations": "{\"resources\":[\"rbac.authorization.k8s.io/clusterroles\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": [{"id": "1919655217100492828", "name": "新增", "code": "<PERSON><PERSON><PERSON>", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492827", "parentName": "角色管理", "parentCode": "roleMng", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"rbac.authorization.k8s.io/clusterroles\":[\"get\",\"list\",\"create\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919655217100492829", "name": "权限配置", "code": "roleConfig", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492827", "parentName": "角色管理", "parentCode": "roleMng", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"rbac.authorization.k8s.io/clusterroles\":[\"get\",\"list\",\"create\",\"update\",\"patch\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919655217100492830", "name": "删除", "code": "roleDelete", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492827", "parentName": "角色管理", "parentCode": "roleMng", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"rbac.authorization.k8s.io/clusterroles\":[\"get\",\"list\",\"delete\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919655217100492831", "name": "角色详情", "code": "RoleDetail", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492827", "parentName": "角色管理", "parentCode": "roleMng", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": null, "method": 1, "visible": false, "annotations": "{\"resources\":[\"rbac.authorization.k8s.io/clusterroles\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": [{"id": "1919655217100492832", "name": "角色信息", "code": "roleInfo", "type": 3, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492831", "parentName": "角色详情", "parentCode": "RoleDetail", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resources\":[\"rbac.authorization.k8s.io/clusterroles\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": [{"id": "1919655217100492833", "name": "编辑", "code": "roleInfoEdit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492832", "parentName": "角色信息", "parentCode": "roleInfo", "parentType": 3, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"rbac.authorization.k8s.io/clusterroles\":[\"get\",\"list\",\"create\",\"update\",\"patch\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}]}, {"id": "1919655217100492834", "name": "权限配置", "code": "rolePermission", "type": 3, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492831", "parentName": "角色详情", "parentCode": "RoleDetail", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919655217100492835", "name": "删除", "code": "roleDetailDelete", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492831", "parentName": "角色详情", "parentCode": "RoleDetail", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"rbac.authorization.k8s.io/clusterroles\":[\"get\",\"list\",\"delete\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}]}]}, {"id": "1919655217100492836", "name": "外联账号", "code": "cooperatorAccount", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492801", "parentName": "企业组织", "parentCode": "user", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": "/application/wideAccount", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": []}]}, {"id": "1919660881512239106", "name": "产品管理", "code": "unified_platform_sys_cloud_service", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1814620967884820480", "parentName": "门户管理", "parentCode": "platform", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": "v35_ProductManage", "url": "/cloundserving/list", "method": 1, "visible": true, "annotations": "{\"ceiling\":true}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": [{"id": "1919660881512239107", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660881512239106", "parentName": "产品管理", "parentCode": "unified_platform_sys_cloud_service", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"ceiling\":true}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}]}, {"id": "1919660886096613376", "name": "集群监控", "code": "unified_platform_sys_monitoring_centre", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1814620967884820480", "parentName": "门户管理", "parentCode": "platform", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": "v35_ClusterMonitor", "url": "/monitoring", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": [{"id": "1919660886096613377", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613376", "parentName": "集群监控", "parentCode": "unified_platform_sys_monitoring_centre", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}]}, {"id": "1919660886096613378", "name": "门户告警", "code": "unified_platform_sys_alarm_centre", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1814620967884820480", "parentName": "门户管理", "parentCode": "platform", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": "v35_Portal<PERSON>lerts", "url": "", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": [{"id": "1919660886096613379", "name": "告警列表", "code": "unified_platform_alert_rule_alarm_record", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613378", "parentName": "门户告警", "parentCode": "unified_platform_sys_alarm_centre", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/monitor/alarm/list", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": [{"id": "1919660886096613380", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613379", "parentName": "告警列表", "parentCode": "unified_platform_alert_rule_alarm_record", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660886096613381", "name": "处理", "code": "exec", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613379", "parentName": "告警列表", "parentCode": "unified_platform_alert_rule_alarm_record", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}]}, {"id": "1919660886096613382", "name": "告警规则", "code": "unified_platform_alert_rule_policy", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613378", "parentName": "门户告警", "parentCode": "unified_platform_sys_alarm_centre", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": "/monitor/alarm/rule", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": [{"id": "1919660886096613383", "name": "启用", "code": "start", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613382", "parentName": "告警规则", "parentCode": "unified_platform_alert_rule_policy", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660886096613384", "name": "停用", "code": "stop", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613382", "parentName": "告警规则", "parentCode": "unified_platform_alert_rule_policy", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660886096613385", "name": "新增", "code": "add", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613382", "parentName": "告警规则", "parentCode": "unified_platform_alert_rule_policy", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919660886096613386", "name": "删除", "code": "remove", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613382", "parentName": "告警规则", "parentCode": "unified_platform_alert_rule_policy", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}, {"id": "1919660886096613387", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613382", "parentName": "告警规则", "parentCode": "unified_platform_alert_rule_policy", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": []}, {"id": "1919660886096613388", "name": "修改", "code": "update", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613382", "parentName": "告警规则", "parentCode": "unified_platform_alert_rule_policy", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 6, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 6, "children": []}, {"id": "1919660886096613389", "name": "告警历史查看", "code": "history_view", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613382", "parentName": "告警规则", "parentCode": "unified_platform_alert_rule_policy", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 7, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 7, "children": []}, {"id": "1919660886096613390", "name": "告警历史处理", "code": "history_handle", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613382", "parentName": "告警规则", "parentCode": "unified_platform_alert_rule_policy", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 8, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 8, "children": []}]}, {"id": "1919660886096613391", "name": "通知对象组", "code": "unified_platform_alarm_template", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613378", "parentName": "门户告警", "parentCode": "unified_platform_sys_alarm_centre", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": "/monitor/alarm/notifyGroup", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": [{"id": "1919660886096613392", "name": "新增", "code": "add", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613391", "parentName": "通知对象组", "parentCode": "unified_platform_alarm_template", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660886096613393", "name": "删除", "code": "remove", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613391", "parentName": "通知对象组", "parentCode": "unified_platform_alarm_template", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660886096613394", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613391", "parentName": "通知对象组", "parentCode": "unified_platform_alarm_template", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919660886096613395", "name": "修改", "code": "update", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613391", "parentName": "通知对象组", "parentCode": "unified_platform_alarm_template", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}]}]}, {"id": "1919660886096613396", "name": "门户日志", "code": "unified_platform_sys_log_centre", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1814620967884820480", "parentName": "门户管理", "parentCode": "platform", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 6, "kind": 1, "icon": "v35_PortalLog", "url": "", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 6, "children": [{"id": "1919660886096613397", "name": "日志查询", "code": "unified_platform_sys_log_query", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613396", "parentName": "门户日志", "parentCode": "unified_platform_sys_log_centre", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/log/logQuery", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"apps/replicasets\",\"batch/jobs\",\"batch/cronjobs\",\"pods\",\"nodes\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": [{"id": "1919660886096613398", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613397", "parentName": "日志查询", "parentCode": "unified_platform_sys_log_query", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/apps/{appName}/applogs/filenames", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660886096613399", "name": "导出", "code": "export", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613397", "parentName": "日志查询", "parentCode": "unified_platform_sys_log_query", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": "/apps/{appName}/applogs/export", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}]}, {"id": "1919660886096613400", "name": "日志备份", "code": "unified_platform_log_backup", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613396", "parentName": "门户日志", "parentCode": "unified_platform_sys_log_centre", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": "/log/logBackup", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": [{"id": "1919660886096613401", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613400", "parentName": "日志备份", "parentCode": "unified_platform_log_backup", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/snapshotrules", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660886096613402", "name": "创建规则", "code": "add-<PERSON><PERSON><PERSON>", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613400", "parentName": "日志备份", "parentCode": "unified_platform_log_backup", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": "/snapshotrules", "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660886096613403", "name": "创建快照", "code": "add-snapshot", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613400", "parentName": "日志备份", "parentCode": "unified_platform_log_backup", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": "/snapshotrules/snapshots", "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919660886096613404", "name": "操作规则", "code": "edit-<PERSON><PERSON><PERSON>", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613400", "parentName": "日志备份", "parentCode": "unified_platform_log_backup", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": "/snapshotrules", "method": 3, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}, {"id": "1919660886096613405", "name": "恢复备份", "code": "restore-snapshot", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613400", "parentName": "日志备份", "parentCode": "unified_platform_log_backup", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": "/snapshotrules/snapshots", "method": 3, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": []}, {"id": "1919660886096613406", "name": "删除快照", "code": "delete-snapshot", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613400", "parentName": "日志备份", "parentCode": "unified_platform_log_backup", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 6, "kind": 1, "icon": null, "url": "/snapshotrules/snapshots", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 6, "children": []}, {"id": "1919660886096613407", "name": "删除备份记录", "code": "delete-restore", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613400", "parentName": "日志备份", "parentCode": "unified_platform_log_backup", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 7, "kind": 1, "icon": null, "url": "/snapshotrules/snapshots/restored/{date}", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 7, "children": []}]}, {"id": "1919660886096613408", "name": "系统日志", "code": "unified_platform_system_log", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613396", "parentName": "门户日志", "parentCode": "unified_platform_sys_log_centre", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": "/log/systemLog", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": [{"id": "1919660886096613409", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613408", "parentName": "系统日志", "parentCode": "unified_platform_system_log", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/deploys/{deployName}/logs/filenames", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660886096613410", "name": "导出", "code": "export", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613408", "parentName": "系统日志", "parentCode": "unified_platform_system_log", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": "/deploys/{deployName}/logs/export", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}]}, {"id": "3907185077487923200", "name": "事件查询", "code": "sys_event_query", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613396", "parentName": "门户日志", "parentCode": "unified_platform_sys_log_centre", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": "/monitor/analysis/event", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}]}, {"id": "1919655217100492837", "name": "门户运维", "code": "unified_operator_maintenance", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1814620967884820480", "parentName": "门户管理", "parentCode": "platform", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 8, "kind": 1, "icon": "v35_PortalMaintenance", "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 8, "children": [{"id": "1919660886096613411", "name": "门户灾备", "code": "unified_platform_sys_disaster_recovery", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492837", "parentName": "门户运维", "parentCode": "unified_operator_maintenance", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/systemaudit/disasterRecovery", "method": 1, "visible": true, "annotations": "{\"resources\":[\"harmonycloud.cn/disasterrecoveries\",\"stellaris.harmonycloud.cn/clusters\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": [{"id": "1919660886096613412", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613411", "parentName": "门户灾备", "parentCode": "unified_platform_sys_disaster_recovery", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/system/disaster/recovery,/system/disaster/recovery/log", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"harmonycloud.cn/disasterrecoveries\":[\"get\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\",\"watch\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660886096613413", "name": "配置", "code": "configuration", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613411", "parentName": "门户灾备", "parentCode": "unified_platform_sys_disaster_recovery", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": "/system/disaster/recovery", "method": 3, "visible": true, "annotations": "{\"resource_option\":{\"harmonycloud.cn/disasterrecoveries\":[\"get\",\"update\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\",\"watch\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660886096613414", "name": "切换", "code": "recovery", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613411", "parentName": "门户灾备", "parentCode": "unified_platform_sys_disaster_recovery", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": "/system/disaster/recovery", "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"harmonycloud.cn/disasterrecoveries\":[\"get\",\"update\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\",\"watch\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}]}, {"id": "1919655217100492838", "name": "操作审计", "code": "operateLog", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492837", "parentName": "门户运维", "parentCode": "unified_operator_maintenance", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": "/api/application/attachment/filename/操作日志.svg", "url": "/application/operateLog", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": [{"id": "1919655217100492839", "name": "清理规则设置", "code": "operateLogConfig", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492838", "parentName": "操作审计", "parentCode": "operateLog", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}]}, {"id": "1919655217100492840", "name": "主题配置", "code": "skyview_custom_settings\n", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492837", "parentName": "门户运维", "parentCode": "unified_operator_maintenance", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": "/api/application/attachment/filename/操作日志.svg", "url": "/individuation", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919660886096613415", "name": "备份还原", "code": "unified_platform_sys_backup_and_recovery", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492837", "parentName": "门户运维", "parentCode": "unified_operator_maintenance", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": "/systemaudit/backupRecovery", "method": 1, "visible": true, "annotations": "{\"resources\":[\"velero.io/backups\",\"velero.io/restores\",\"velero.io/schedules\",\"velero.io/backupstoragelocations\",\"velero.io/downloadrequests\",\"velero.io/podvolumebackups\",\"velero.io/podvolumerestores\",\"velero.io/deletebackuprequests\",\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"secrets\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": [{"id": "1919660886096613416", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613415", "parentName": "备份还原", "parentCode": "unified_platform_sys_backup_and_recovery", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/apis/v1/system/backuprestore/schedules", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"secrets\":[\"get\",\"list\"],\"velero.io/backups\":[\"get\",\"list\"],\"velero.io/backupstoragelocations\":[\"get\",\"list\"],\"velero.io/downloadrequests\":[\"get\",\"list\"],\"velero.io/podvolumebackups\":[\"get\",\"list\"],\"velero.io/schedules\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660886096613417", "name": "备份策略新增", "code": "unified_platform_schedules_add", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613415", "parentName": "备份还原", "parentCode": "unified_platform_sys_backup_and_recovery", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": "/apis/v1/system/backuprestore/schedules", "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"secrets\":[\"get\",\"list\",\"create\",\"delete\"],\"velero.io/backupstoragelocations\":[\"get\",\"list\",\"create\",\"delete\"],\"velero.io/schedules\":[\"get\",\"list\",\"create\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660886096613418", "name": "备份策略编辑", "code": "unified_platform_schedules_edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613415", "parentName": "备份还原", "parentCode": "unified_platform_sys_backup_and_recovery", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": "/apis/v1/system/backuprestore/schedules/{scheduleName}", "method": 3, "visible": true, "annotations": "{\"resource_option\":{\"velero.io/schedules\":[\"get\",\"list\",\"update\",\"patch\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919660886096613419", "name": "备份策略立即执行", "code": "unified_platform_schedules_execute", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613415", "parentName": "备份还原", "parentCode": "unified_platform_sys_backup_and_recovery", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": "/apis/v1/system/backuprestore/schedules/{scheduleName}/execute", "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"velero.io/schedules\":[\"get\",\"list\",\"update\",\"patch\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}, {"id": "1919660886096613420", "name": "备份策略启动", "code": "unified_platform_schedules_start", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613415", "parentName": "备份还原", "parentCode": "unified_platform_sys_backup_and_recovery", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": "/apis/v1/system/backuprestore/schedules/{scheduleName}/start", "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"velero.io/schedules\":[\"get\",\"list\",\"update\",\"patch\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": []}, {"id": "1919660886096613421", "name": "备份策略停止", "code": "unified_platform_schedules_stop", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613415", "parentName": "备份还原", "parentCode": "unified_platform_sys_backup_and_recovery", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 6, "kind": 1, "icon": null, "url": "/apis/v1/system/backuprestore/schedules/{scheduleName}/stop", "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"velero.io/schedules\":[\"get\",\"list\",\"update\",\"patch\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 6, "children": []}, {"id": "1919660886096613422", "name": "备份策略同步备份服务器信息", "code": "unified_platform_schedules_sync_storage_server", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613415", "parentName": "备份还原", "parentCode": "unified_platform_sys_backup_and_recovery", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 7, "kind": 1, "icon": null, "url": "/apis/v1/system/backuprestore/schedules/{scheduleName}/syncStorageServer", "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"secrets\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"velero.io/backupstoragelocations\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 7, "children": []}, {"id": "1919660886096613423", "name": "备份策略删除记录", "code": "unified_platform_schedules_backups_remove", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613415", "parentName": "备份还原", "parentCode": "unified_platform_sys_backup_and_recovery", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 8, "kind": 1, "icon": null, "url": "/apis/v1/system/backuprestore/schedules/{scheduleName}/backups/{backupName}", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"velero.io/backups\":[\"get\",\"list\"],\"velero.io/deletebackuprequests\":[\"get\",\"list\",\"create\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 8, "children": []}, {"id": "1919660886096613424", "name": "备份策略删除", "code": "unified_platform_schedules_remove", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613415", "parentName": "备份还原", "parentCode": "unified_platform_sys_backup_and_recovery", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 9, "kind": 1, "icon": null, "url": "/apis/v1/system/backuprestore/schedules/{scheduleName}", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"velero.io/schedules\":[\"get\",\"list\",\"delete\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 9, "children": []}, {"id": "1919660886096613425", "name": "恢复策略新增", "code": "unified_platform_restores_template_add", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613415", "parentName": "备份还原", "parentCode": "unified_platform_sys_backup_and_recovery", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 10, "kind": 1, "icon": null, "url": "/apis/v1/system/backuprestore/restoresTemplate", "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"velero.io/restores\":[\"get\",\"list\",\"create\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 10, "children": []}, {"id": "1919660886096613426", "name": "恢复策略重试", "code": "unified_platform_restores_template_retry", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613415", "parentName": "备份还原", "parentCode": "unified_platform_sys_backup_and_recovery", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 11, "kind": 1, "icon": null, "url": "/apis/v1/system/backuprestore/restoresTemplate/{restoresTemplateId}/retry", "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"velero.io/restores\":[\"get\",\"list\",\"create\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 11, "children": []}, {"id": "1919660886096613427", "name": "恢复策略删除", "code": "unified_platform_restores_template_remove", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886096613415", "parentName": "备份还原", "parentCode": "unified_platform_sys_backup_and_recovery", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 12, "kind": 1, "icon": null, "url": "/apis/v1/system/backuprestore/restoresTemplate/{restoresTemplateId}", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"velero.io/restores\":[\"get\",\"list\",\"delete\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 12, "children": []}]}, {"id": "1919660886100807685", "name": "集群巡检", "code": "unified_platform_sys_cluster_patrol", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492837", "parentName": "门户运维", "parentCode": "unified_operator_maintenance", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 6, "kind": 1, "icon": null, "url": "/systemaudit/clusterPatrol", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 6, "children": [{"id": "1919660886100807686", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886100807685", "parentName": "集群巡检", "parentCode": "unified_platform_sys_cluster_patrol", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/metricsreport/cluster/getreport", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660886100807687", "name": "巡检", "code": "patrol", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886100807685", "parentName": "集群巡检", "parentCode": "unified_platform_sys_cluster_patrol", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": "/metricsreport/cluster/getreport", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660886100807688", "name": "详情", "code": "detail", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886100807685", "parentName": "集群巡检", "parentCode": "unified_platform_sys_cluster_patrol", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": "/dashboards", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1919660886100807689", "name": "导出", "code": "export", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886100807685", "parentName": "集群巡检", "parentCode": "unified_platform_sys_cluster_patrol", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": "/metricsreport/cluster/exportreport", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}, {"id": "1919660886100807690", "name": "设置接收人", "code": "setreceiver", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886100807685", "parentName": "集群巡检", "parentCode": "unified_platform_sys_cluster_patrol", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 5, "kind": 1, "icon": null, "url": "/cluster/{clusterName}/health/monitor", "method": 3, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 5, "children": []}, {"id": "1919660886100807691", "name": "新增指标", "code": "<PERSON><PERSON><PERSON>", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886100807685", "parentName": "集群巡检", "parentCode": "unified_platform_sys_cluster_patrol", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 6, "kind": 1, "icon": null, "url": "/cluster/{clusterName}/health/thresholds/{metricsName}", "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 6, "children": []}, {"id": "1919660886100807692", "name": "删除指标", "code": "delete", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886100807685", "parentName": "集群巡检", "parentCode": "unified_platform_sys_cluster_patrol", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 7, "kind": 1, "icon": null, "url": "/cluster/{clusterName}/health/thresholds", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 7, "children": []}, {"id": "1919660886100807693", "name": "编辑指标", "code": "edit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886100807685", "parentName": "集群巡检", "parentCode": "unified_platform_sys_cluster_patrol", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 8, "kind": 1, "icon": null, "url": "/cluster/{clusterName}/health/thresholds/{metricsName}", "method": 3, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 8, "children": []}]}, {"id": "1919660886100807694", "name": "故障隔离", "code": "unified_platform_faultsolation", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492837", "parentName": "门户运维", "parentCode": "unified_operator_maintenance", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 7, "kind": 1, "icon": null, "url": "/systemaudit/faultsolation", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"apps/replicasets\",\"batch/jobs\",\"batch/cronjobs\",\"pods\",\"nodes\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 7, "children": [{"id": "1919660886100807695", "name": "查询", "code": "query", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886100807694", "parentName": "故障隔离", "parentCode": "unified_platform_faultsolation", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660886100807696", "name": "pod控制台", "code": "containerTerminal", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886100807694", "parentName": "故障隔离", "parentCode": "unified_platform_faultsolation", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660886100807697", "name": "删除", "code": "remove", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886100807694", "parentName": "故障隔离", "parentCode": "unified_platform_faultsolation", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}]}, {"id": "1919660886100807698", "name": "备份仓库", "code": "sys_backup_repository", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492837", "parentName": "门户运维", "parentCode": "unified_operator_maintenance", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 8, "kind": 1, "icon": null, "url": "/system/backupRepository", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 8, "children": [{"id": "1919660886100807699", "name": "新增", "code": "add_sys_backup_repository", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886100807698", "parentName": "备份仓库", "parentCode": "sys_backup_repository", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919660886100807700", "name": "编辑", "code": "edit_sys_backup_repository", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886100807698", "parentName": "备份仓库", "parentCode": "sys_backup_repository", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919660886100807701", "name": "删除", "code": "remove_sys_backup_repository", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919660886100807698", "parentName": "备份仓库", "parentCode": "sys_backup_repository", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}]}, {"id": "1919655217100492841", "name": "登录认证管理", "code": "unified_platform_login_setting", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492837", "parentName": "门户运维", "parentCode": "unified_operator_maintenance", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 9, "kind": 1, "icon": null, "url": "/loginManagement/setting", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 9, "children": [{"id": "1919655217100492842", "name": "ldap配置", "code": "ldapConfig", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492841", "parentName": "登录认证管理", "parentCode": "unified_platform_login_setting", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1919655217100492843", "name": "登录认证切换", "code": "authSwitch", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492841", "parentName": "登录认证管理", "parentCode": "unified_platform_login_setting", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1919655217100492844", "name": "Ldap用户数据同步", "code": "sync", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1919655217100492841", "parentName": "登录认证管理", "parentCode": "unified_platform_login_setting", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}]}]}, {"id": "2415922244366798849", "name": "开发配置", "code": "dev-config", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "1814620967884820480", "parentName": "门户管理", "parentCode": "platform", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 800, "kind": 1, "icon": "/api/application/attachment/filename/微服务.svg", "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 800, "children": [{"id": "2360451453664890880", "name": "应用管理", "code": "appList", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "2415922244366798849", "parentName": "开发配置", "parentCode": "dev-config", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 500, "kind": 1, "icon": "/api/application/attachment/filename/应用管理.svg", "url": "/application/appList", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 500, "children": [{"id": "1922893036438687751", "name": "应用详情", "code": "appDetail", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "2360451453664890880", "parentName": "应用管理", "parentCode": "appList", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/application/appDetail", "method": 1, "visible": false, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": [{"id": "1922893036438687752", "name": "概览", "code": "appInfo", "type": 3, "appId": "2014160962552623104", "appCode": "application", "parentId": "1922893036438687751", "parentName": "应用详情", "parentCode": "appDetail", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": [{"id": "1922893036438687753", "name": "编辑", "code": "appInfoEdit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1922893036438687752", "parentName": "概览", "parentCode": "appInfo", "parentType": 3, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}]}, {"id": "1922893036438687754", "name": "菜单配置", "code": "appMenu", "type": 3, "appId": "2014160962552623104", "appCode": "application", "parentId": "1922893036438687751", "parentName": "应用详情", "parentCode": "appDetail", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": [{"id": "1922893036438687755", "name": "新增", "code": "appMenuAdd", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1922893036438687754", "parentName": "菜单配置", "parentCode": "appMenu", "parentType": 3, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1922893036438687756", "name": "编辑", "code": "appMenuEdit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1922893036438687754", "parentName": "菜单配置", "parentCode": "appMenu", "parentType": 3, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1922893036438687757", "name": "删除", "code": "appMenuDelete", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1922893036438687754", "parentName": "菜单配置", "parentCode": "appMenu", "parentType": 3, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}]}, {"id": "1922893036438687758", "name": "资源类型", "code": "appResource", "type": 3, "appId": "2014160962552623104", "appCode": "application", "parentId": "1922893036438687751", "parentName": "应用详情", "parentCode": "appDetail", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": [{"id": "1922893036438687759", "name": "新增", "code": "appResourceAdd", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1922893036438687758", "parentName": "资源类型", "parentCode": "appResource", "parentType": 3, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1922893036438687760", "name": "编辑", "code": "appResourceEdit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1922893036438687758", "parentName": "资源类型", "parentCode": "appResource", "parentType": 3, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1922893036438687761", "name": "删除", "code": "appResourceDelete", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1922893036438687758", "parentName": "资源类型", "parentCode": "appResource", "parentType": 3, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}]}, {"id": "1922893036438687762", "name": "删除", "code": "appDetailDelete", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "1922893036438687751", "parentName": "应用详情", "parentCode": "appDetail", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}]}, {"id": "1922893036438687763", "name": "新增", "code": "appAdd", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "2360451453664890880", "parentName": "应用管理", "parentCode": "appList", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1922893036438687764", "name": "删除", "code": "appDelete", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "2360451453664890880", "parentName": "应用管理", "parentCode": "appList", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}, {"id": "1922893036438687765", "name": "详情", "code": "appDetailPage", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "2360451453664890880", "parentName": "应用管理", "parentCode": "appList", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 4, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 4, "children": []}]}, {"id": "2360450557325348864", "name": "微前端管理", "code": "microServeList", "type": 1, "appId": "2014160962552623104", "appCode": "application", "parentId": "2415922244366798849", "parentName": "开发配置", "parentCode": "dev-config", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 800, "kind": 1, "icon": "/api/application/attachment/filename/微服务.svg", "url": "/application/microServeList", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 800, "children": [{"id": "1922893036438687747", "name": "新增", "code": "microServeAdd", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "2360450557325348864", "parentName": "微前端管理", "parentCode": "microServeList", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 1, "kind": 1, "icon": null, "url": "/webs", "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 1, "children": []}, {"id": "1922893036438687748", "name": "编辑", "code": "microServeEdit", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "2360450557325348864", "parentName": "微前端管理", "parentCode": "microServeList", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 2, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 2, "children": []}, {"id": "1922893036438687749", "name": "删除", "code": "microServeDelete", "type": 2, "appId": "2014160962552623104", "appCode": "application", "parentId": "2360450557325348864", "parentName": "微前端管理", "parentCode": "microServeList", "parentType": 1, "parentAppId": "2014160962552623104", "parentAppCode": "application", "weight": 3, "kind": 1, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": null, "sortId": 3, "children": []}]}]}]