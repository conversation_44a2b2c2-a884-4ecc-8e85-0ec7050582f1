[{"id": "1919661131887022080", "name": "总览", "code": "container_service_sys_container_service_overview", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "0", "parentName": "根菜单", "parentCode": "root", "parentType": 1, "parentAppId": "-1", "parentAppCode": null, "weight": 1, "kind": 3, "icon": "v35_Overview", "url": "/project/space/overview", "method": 1, "visible": true, "annotations": "{\"resources\":[\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"apps/replicasets\",\"batch/jobs\",\"batch/cronjobs\",\"pods\",\"services\",\"secrets\",\"configmaps\",\"persistentvolumeclaims\",\"persistentvolumes\",\"storage.k8s.io/storageclasses\",\"serviceaccounts\",\"namespaces\",\"nodes\",\"resourcequotas\",\"harmonycloud.cn/nodepools\",\"heimdallr.harmonycloud.cn/hdareas\",\"heimdallr.harmonycloud.cn/hdblocks\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/hdpools\",\"heimdallr.harmonycloud.cn/networkdetails\",\"heimdallr.harmonycloud.cn/networkresources\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": [{"id": "1919661131887022081", "name": "查询", "code": "query", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022080", "parentName": "总览", "parentCode": "container_service_sys_container_service_overview", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": []}]}, {"id": "1919661131887022082", "name": "应用市场", "code": "container_service_sys_application_market", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "0", "parentName": "根菜单", "parentCode": "root", "parentType": 1, "parentAppId": "-1", "parentAppCode": null, "weight": 2, "kind": 3, "icon": "v35_MarketApp", "url": "/applicationMarket", "method": 1, "visible": true, "annotations": "{\"ceiling\":true,\"titleDescription\":\"He<PERSON>模板及应用模板中心\"}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": [{"id": "1919661131887022083", "name": "查询", "code": "query", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022082", "parentName": "应用市场", "parentCode": "container_service_sys_application_market", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": []}, {"id": "1919661131887022084", "name": "新增分类", "code": "skyview_application_market_type_add", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022082", "parentName": "应用市场", "parentCode": "container_service_sys_application_market", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": "/appstore/types/{appType}/tags", "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": []}, {"id": "1919661131887022085", "name": "编辑分类", "code": "skyview_application_market_type_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022082", "parentName": "应用市场", "parentCode": "container_service_sys_application_market", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 3, "kind": 3, "icon": null, "url": "/appstore/types/{appType}/tags", "method": 3, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 3, "children": []}, {"id": "1919661131887022086", "name": "删除分类", "code": "skyview_application_market_type_delete", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022082", "parentName": "应用市场", "parentCode": "container_service_sys_application_market", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 4, "kind": 3, "icon": null, "url": "/appstore/types/{appType}/tags", "method": 4, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 4, "children": []}, {"id": "1919661131887022087", "name": "新增", "code": "skyview_application_market_helm_chart_create", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022082", "parentName": "应用市场", "parentCode": "container_service_sys_application_market", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 5, "kind": 3, "icon": null, "url": "/appstore/charts/{chartName}/versions", "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 5, "children": []}, {"id": "1919661131887022088", "name": "chart管理", "code": "skyview_application_market_helm_chart_manager", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022082", "parentName": "应用市场", "parentCode": "container_service_sys_application_market", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 6, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 6, "children": []}, {"id": "1919661131887022089", "name": "调整分类", "code": "skyview_application_market_helm_chart_type_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022082", "parentName": "应用市场", "parentCode": "container_service_sys_application_market", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 7, "kind": 3, "icon": null, "url": "/appstore/types/{appType}/tags", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 7, "children": []}, {"id": "1919661131887022090", "name": "下架", "code": "skyview_application_market_helm_chart_delete", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022082", "parentName": "应用市场", "parentCode": "container_service_sys_application_market", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 8, "kind": 3, "icon": null, "url": null, "method": 4, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 8, "children": []}, {"id": "1919661131887022091", "name": "上传新版本", "code": "skyview_application_market_helm_chart_version_create", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022082", "parentName": "应用市场", "parentCode": "container_service_sys_application_market", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 9, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 9, "children": []}, {"id": "1919661131887022092", "name": "立即推送", "code": "skyview_application_market_helm_chart_version_notifications_push", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022082", "parentName": "应用市场", "parentCode": "container_service_sys_application_market", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 10, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 10, "children": []}]}, {"id": "1919661131887022093", "name": "应用管理", "code": "container_service_sys_project_applications", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "0", "parentName": "根菜单", "parentCode": "root", "parentType": 1, "parentAppId": "-1", "parentAppCode": null, "weight": 3, "kind": 3, "icon": "v35_ApplicationManage", "url": "", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 3, "children": [{"id": "1919661131887022094", "name": "单集群应用", "code": "container_service_sys_project_applicaiton_single", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022093", "parentName": "应用管理", "parentCode": "container_service_sys_project_applications", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": "/project/space/application/single", "method": 1, "visible": true, "annotations": "{\"resources\":[\"core.oam.dev/applications\",\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"apps/replicasets\",\"batch/jobs\",\"batch/cronjobs\",\"pods\",\"services\",\"secrets\",\"configmaps\",\"persistentvolumeclaims\",\"persistentvolumes\",\"storage.k8s.io/storageclasses\",\"serviceaccounts\",\"namespaces\",\"resourcequotas\",\"nodes\",\"harmonycloud.cn/nodepools\",\"heimdallr.harmonycloud.cn/hdareas\",\"heimdallr.harmonycloud.cn/hdblocks\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/hdpools\",\"heimdallr.harmonycloud.cn/networkdetails\",\"heimdallr.harmonycloud.cn/networkresources\",\"endpoints\",\"harmonycloud.cn/oamapprevisions\",\"stellaris.harmonycloud.cn/aggregatedresources\",\"expose.helper.harmonycloud.cn/layer4exposes\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": [{"id": "1919661131887022095", "name": "查询", "code": "query", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\"],\"endpoints\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/aggregatedresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": []}, {"id": "1919661131887022096", "name": "删除版本", "code": "version_delete", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\"],\"endpoints\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/aggregatedresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": []}, {"id": "1919661131887022097", "name": "新增", "code": "skyview_application_add", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 3, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 3, "children": []}, {"id": "1919661131887022098", "name": "保存草稿", "code": "skyview_application_template_add", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 4, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 4, "children": []}, {"id": "1919661131887022099", "name": "重新加载", "code": "skyview_application_reload", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 5, "kind": 3, "icon": null, "url": "/organizations/{organiz/clusters/{clusterName}/projects/{projectId}/allowGpuRemoteCall", "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 5, "children": []}, {"id": "1919661131887022100", "name": "重新启动", "code": "skyview_application_restart", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 6, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 6, "children": []}, {"id": "1919661131887022101", "name": "启动组件", "code": "skyview_application_component_start", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 7, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 7, "children": []}, {"id": "1919661131887022102", "name": "批量启动", "code": "skyview_application_batch_start", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 8, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 8, "children": []}, {"id": "1919661131887022103", "name": "停止组件", "code": "skyview_application_component_stop", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 9, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 9, "children": []}, {"id": "1919661131887022104", "name": "批量停止", "code": "skyview_application_batch_stop", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 10, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 10, "children": []}, {"id": "1919661131891216384", "name": "组件实例数变更", "code": "skyview_application_component_edit_replicas", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 11, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 11, "children": []}, {"id": "1919661131891216385", "name": "版本编辑更新", "code": "skyview_application_version_update", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 12, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 12, "children": []}, {"id": "1919661131891216386", "name": "执行批次", "code": "skyview_application_execute", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 13, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 13, "children": []}, {"id": "1919661131891216387", "name": "完成升级", "code": "skyview_application_update_finish", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 14, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 14, "children": []}, {"id": "1919661131891216388", "name": "接管流量", "code": "skyview_application_version_flow", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 15, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 15, "children": []}, {"id": "1919661131891216389", "name": "回滚升级", "code": "skyview_application_update_rollback", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 16, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 16, "children": []}, {"id": "1919661131891216390", "name": "版本回滚", "code": "skyview_application_version_rollback", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 17, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 17, "children": []}, {"id": "1919661131891216391", "name": "保存拓扑图", "code": "skyview_application_edit_canvas", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 18, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 18, "children": []}, {"id": "1919661131891216392", "name": "hpa新增", "code": "skyview_application_hpa_add", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 19, "kind": 3, "icon": null, "url": "/organizations/{organizationId}/projects/{projectId}/clusters/{clusterName}/namespaces/{namespace}/apps/{appName}/component/{componentName}/hpas", "method": 2, "visible": true, "annotations": "{\"component\":\"hpa\",\"componentName\":\"水平扩缩容\",\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 19, "children": []}, {"id": "1919661131895410688", "name": "hpa编辑", "code": "skyview_application_hpa_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 20, "kind": 3, "icon": null, "url": "/organizations/{organizationId}/projects/{projectId}/clusters/{clusterName}/namespaces/{namespace}/apps/{appName}/component/{componentName}/hpas", "method": 3, "visible": true, "annotations": "{\"component\":\"hpa\",\"componentName\":\"水平扩缩容\",\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 20, "children": []}, {"id": "1919661131895410689", "name": "hpa删除", "code": "skyview_application_hpa_remove", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 21, "kind": 3, "icon": null, "url": "/organizations/{organizationId}/projects/{projectId}/clusters/{clusterName}/namespaces/{namespace}/apps/{appName}/hpas", "method": 4, "visible": true, "annotations": "{\"component\":\"hpa\",\"componentName\":\"水平扩缩容\",\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 21, "children": []}, {"id": "1919661131895410690", "name": "集群内部暴露", "code": "skyview_application_expose_internalService", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 22, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 22, "children": []}, {"id": "1919661131895410691", "name": "集群外部暴露", "code": "skyview_application_expose_externalServices", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 23, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 23, "children": []}, {"id": "1919661131895410692", "name": "删除", "code": "skyview_application_remove", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 24, "kind": 3, "icon": null, "url": null, "method": 4, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 24, "children": []}, {"id": "1919661131895410693", "name": "草稿继续编辑", "code": "skyview_application_draft_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 25, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 25, "children": []}, {"id": "1919661131895410694", "name": "草稿删除", "code": "skyview_application_draft_remove", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 26, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 26, "children": []}, {"id": "1919661131895410695", "name": "纳管", "code": "skyview_application_decompile", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 27, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"component\":\"app-decompile\",\"componentName\":\"应用反编译\"}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 27, "children": []}, {"id": "1919661131895410696", "name": "单集群关联资源", "code": "skyview_application_associations", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 28, "kind": 3, "icon": null, "url": "/organizations/{organizationId}/projects/{projectId}/clusters/{clusterName}/namespaces/{namespace}/apps/{appName}/associations,/clusters/{clusterName}/resource/aggregate", "method": 1, "visible": true, "annotations": "{\"component\":\"resource-aggregate\",\"componentName\":\"资源关联控制器\"}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 28, "children": []}, {"id": "1919661131895410697", "name": "hpa查询", "code": "skyview_application_hpa_list", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 29, "kind": 3, "icon": null, "url": "/organizations/{organizationId}/projects/{projectId}/clusters/{clusterName}/namespaces/{namespace}/apps/{appName}/hpas", "method": 1, "visible": true, "annotations": "{\"component\":\"hpa\",\"componentName\":\"水平扩缩容\"}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 29, "children": []}, {"id": "1919661131895410698", "name": "应用监控", "code": "skyview_application_monitor", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 30, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"component\":\"monitoring\",\"componentName\":\"监控\"}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 30, "children": []}, {"id": "1919661131895410699", "name": "文件上传", "code": "skyview_application_file_upload", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 31, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 31, "children": []}, {"id": "*****************00", "name": "文件下载", "code": "skyview_application_file_download", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 32, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 32, "children": []}, {"id": "*****************01", "name": "文件路径查询", "code": "skyview_application_file_pwd", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022094", "parentName": "单集群应用", "parentCode": "container_service_sys_project_applicaiton_single", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 33, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 33, "children": []}]}, {"id": "*****************02", "name": "多集群应用", "code": "container_service_sys_project_applicaiton_multi", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022093", "parentName": "应用管理", "parentCode": "container_service_sys_project_applications", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": "/project/space/application/multi", "method": 1, "visible": true, "annotations": "{\"resources\":[\"core.oam.dev/applications\",\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"apps/replicasets\",\"batch/jobs\",\"batch/cronjobs\",\"pods\",\"services\",\"secrets\",\"configmaps\",\"persistentvolumeclaims\",\"persistentvolumes\",\"storage.k8s.io/storageclasses\",\"serviceaccounts\",\"namespaces\",\"resourcequotas\",\"stellaris.harmonycloud.cn/aggregatedresources\",\"stellaris.harmonycloud.cn/multiclusterresources\",\"stellaris.harmonycloud.cn/multiclusterresourcebindings\",\"expose.helper.harmonycloud.cn/layer4exposes\",\"nodes\",\"harmonycloud.cn/nodepools\",\"heimdallr.harmonycloud.cn/hdareas\",\"heimdallr.harmonycloud.cn/hdblocks\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/hdpools\",\"heimdallr.harmonycloud.cn/networkdetails\",\"heimdallr.harmonycloud.cn/networkresources\",\"endpoints\",\"harmonycloud.cn/oamapprevisions\",\"stellaris.harmonycloud.cn/clustertopologies\",\"stellaris.harmonycloud.cn/servicesyncs\",\"stellaris.harmonycloud.cn/clusters\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": [{"id": "1919661132272898048", "name": "查询", "code": "query", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\"],\"endpoints\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/aggregatedresources\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clustertopologies\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": []}, {"id": "1919661132272898049", "name": "新增", "code": "skyview_multi_application_add", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\"],\"endpoints\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/aggregatedresources\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clustertopologies\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": []}, {"id": "1919661132272898050", "name": "编辑", "code": "edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 3, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/aggregatedresources\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clustertopologies\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 3, "children": []}, {"id": "1919661132272898051", "name": "保存草稿", "code": "skyview_multi_application_template_add", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 4, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 4, "children": []}, {"id": "1919661132272898052", "name": "批量启动", "code": "skyview_multi_application_batch_start", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 5, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/aggregatedresources\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clustertopologies\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 5, "children": []}, {"id": "1919661132272898053", "name": "批量停止", "code": "skyview_multi_application_batch_stop", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 6, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/aggregatedresources\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clustertopologies\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 6, "children": []}, {"id": "1919661132272898054", "name": "编辑调度策略", "code": "skyview_multi_scheduling_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 7, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/aggregatedresources\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clustertopologies\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 7, "children": []}, {"id": "1919661132272898055", "name": "草稿继续编辑", "code": "skyview_multi_application_draft_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 8, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 8, "children": []}, {"id": "1919661132272898056", "name": "草稿删除", "code": "skyview_multi_application_draft_remove", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 9, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 9, "children": []}, {"id": "1919661132272898057", "name": "集群内部暴露新增", "code": "skyview_multi_application_expose_internalService", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 10, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/aggregatedresources\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clustertopologies\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 10, "children": []}, {"id": "1919661132272898058", "name": "集群外部暴露新增", "code": "skyview_multi_application_expose_externalServices", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 11, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/aggregatedresources\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clustertopologies\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 11, "children": []}, {"id": "1919661132277092352", "name": "删除", "code": "skyview_multi_application_remove", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 12, "kind": 3, "icon": null, "url": null, "method": 4, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"endpoints\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\"],\"stellaris.harmonycloud.cn/aggregatedresources\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clustertopologies\":[\"get\",\"list\",\"update\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 12, "children": []}, {"id": "1919661132277092353", "name": "保存拓扑图", "code": "skyview_multi_application_edit_canvas", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 13, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/aggregatedresources\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clustertopologies\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 13, "children": []}, {"id": "1919661132277092354", "name": "多集群hpa查询", "code": "skyview_multi_application_cluster_hpa_query", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 14, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\"],\"endpoints\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/aggregatedresources\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clustertopologies\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 14, "children": []}, {"id": "1919661132277092355", "name": "多集群hpa新增", "code": "skyview_multi_application_cluster_hpa_add", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 15, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/aggregatedresources\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clustertopologies\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 15, "children": []}, {"id": "1919661132277092356", "name": "多集群hpa编辑", "code": "skyview_multi_application_cluster_hpa_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 16, "kind": 3, "icon": null, "url": null, "method": 3, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/aggregatedresources\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clustertopologies\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 16, "children": []}, {"id": "1919661132277092357", "name": "多集群hpa删除", "code": "skyview_multi_application_cluster_hpa_remove", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 17, "kind": 3, "icon": null, "url": null, "method": 4, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/aggregatedresources\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clustertopologies\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 17, "children": []}, {"id": "1919661132277092358", "name": "编辑自治集群", "code": "skyview_multi_application_autonomy_cluster_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 18, "kind": 3, "icon": null, "url": null, "method": 3, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/aggregatedresources\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clustertopologies\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 18, "children": []}, {"id": "1919661132277092359", "name": "文件上传", "code": "skyview_multi_application_file_upload", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 19, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/aggregatedresources\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clustertopologies\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 19, "children": []}, {"id": "1919661132281286656", "name": "文件下载", "code": "skyview_multi_application_file_download", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 20, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/aggregatedresources\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clustertopologies\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 20, "children": []}, {"id": "1919661132281286657", "name": "文件路径查询", "code": "skyview_multi_application_file_pwd", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 21, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/aggregatedresources\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clustertopologies\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 21, "children": []}, {"id": "1919661132281286658", "name": "删除集群", "code": "skyview_multi_application_remove_cluster", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 22, "kind": 3, "icon": null, "url": null, "method": 4, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"core.oam.dev/applications\":[\"get\",\"list\",\"create\"],\"endpoints\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"harmonycloud.cn/oamapprevisions\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"serviceaccounts\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/aggregatedresources\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clustertopologies\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 22, "children": []}, {"id": "1919661132281286659", "name": "多集群服务聚合", "code": "skyview_multi_application_service_sync", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 23, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"core.oam.dev/applications\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clustertopologies\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/servicesyncs\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 23, "children": []}, {"id": "1919661132281286660", "name": "多集群服务聚合新增", "code": "skyview_multi_application_service_sync_add", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 24, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"core.oam.dev/applications\":[\"get\",\"list\",\"update\",\"patch\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clustertopologies\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/servicesyncs\":[\"get\",\"list\",\"update\",\"patch\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 24, "children": []}, {"id": "1919661132281286661", "name": "多集群服务聚合编辑", "code": "skyview_multi_application_service_sync_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 25, "kind": 3, "icon": null, "url": null, "method": 3, "visible": true, "annotations": "{\"resource_option\":{\"core.oam.dev/applications\":[\"get\",\"list\",\"update\",\"patch\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clustertopologies\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/servicesyncs\":[\"get\",\"list\",\"update\",\"patch\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 25, "children": []}, {"id": "1919661132281286662", "name": "多集群服务聚合删除", "code": "skyview_multi_application_service_sync_delete", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "*****************02", "parentName": "多集群应用", "parentCode": "container_service_sys_project_applicaiton_multi", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 26, "kind": 3, "icon": null, "url": null, "method": 4, "visible": true, "annotations": "{\"resource_option\":{\"core.oam.dev/applications\":[\"get\",\"list\",\"update\",\"patch\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/servicesyncs\":[\"get\",\"list\",\"update\",\"patch\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 26, "children": []}]}, {"id": "1919661132281286663", "name": "Helm Chart服务", "code": "container_service_sys_project_helm_chart_service", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661131887022093", "parentName": "应用管理", "parentCode": "container_service_sys_project_applications", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 3, "kind": 3, "icon": null, "url": "/project/space/application/helm<PERSON>hart", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 3, "children": [{"id": "1919661132281286664", "name": "查询", "code": "query", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132281286663", "parentName": "Helm Chart服务", "parentCode": "container_service_sys_project_helm_chart_service", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": null, "method": 3, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": []}, {"id": "1919661132281286665", "name": "应用市场新增", "code": "skyview_helm_chart_to_application_market_add", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132281286663", "parentName": "Helm Chart服务", "parentCode": "container_service_sys_project_helm_chart_service", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": "/appstore/types/chart/tags", "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": []}, {"id": "1919661132281286666", "name": "升级", "code": "skyview_helm_chart_upgrade", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132281286663", "parentName": "Helm Chart服务", "parentCode": "container_service_sys_project_helm_chart_service", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 3, "kind": 3, "icon": null, "url": "/clusters/{clusterName}/namespace/{namespace}/helms/{helmName}/upgrade", "method": 3, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 3, "children": []}, {"id": "1919661132281286667", "name": "版本回滚", "code": "skyview_helm_chart_version_rollback", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132281286663", "parentName": "Helm Chart服务", "parentCode": "container_service_sys_project_helm_chart_service", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 4, "kind": 3, "icon": null, "url": "/clusters/{clusterName}/namespace/{namespace}/helms/{helmName}/rollback", "method": 3, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 4, "children": []}, {"id": "1919661132281286668", "name": "参数对比", "code": "skyview_helm_chart_yaml_compare", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132281286663", "parentName": "Helm Chart服务", "parentCode": "container_service_sys_project_helm_chart_service", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 5, "kind": 3, "icon": null, "url": "/clusters/{clusterName}/namespace/{namespace}/helms/{helmName}/revision/{revision}/values", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 5, "children": []}, {"id": "1919661132281286669", "name": "文件下载", "code": "skyview_helm_chart_download", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132281286663", "parentName": "Helm Chart服务", "parentCode": "container_service_sys_project_helm_chart_service", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 6, "kind": 3, "icon": null, "url": "/clusters/{clusterName}/namespace/{namespace}/helms/{helmName}/revision/{revision}/download", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 6, "children": []}, {"id": "1919661132281286670", "name": "服务暴露编辑", "code": "skyview_helm_chart_expose_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132281286663", "parentName": "Helm Chart服务", "parentCode": "container_service_sys_project_helm_chart_service", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 7, "kind": 3, "icon": null, "url": null, "method": 3, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 7, "children": []}, {"id": "1919661132281286671", "name": "删除", "code": "skyview_helm_chart_delete", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132281286663", "parentName": "Helm Chart服务", "parentCode": "container_service_sys_project_helm_chart_service", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 8, "kind": 3, "icon": null, "url": "/clusters/{clusterName}/namespace/{namespace}/helms/{helmName}", "method": 4, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 8, "children": []}]}]}, {"id": "1919661132281286672", "name": "工作负载", "code": "container_service_sys_project_workload", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "0", "parentName": "根菜单", "parentCode": "root", "parentType": 1, "parentAppId": "-1", "parentAppCode": null, "weight": 4, "kind": 3, "icon": "v35_Workload", "url": "", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 4, "children": [{"id": "1919661132281286673", "name": "无状态部署", "code": "container_service_sys_project_deployment", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132281286672", "parentName": "工作负载", "parentCode": "container_service_sys_project_workload", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": "/project/space/resource/deployment/list", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"apps/deployments\",\"apps/replicasets\",\"events\",\"namespaces\",\"pods\",\"resourcequotas\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": [{"id": "1919661132281286674", "name": "查询", "code": "query", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132281286673", "parentName": "无状态部署", "parentCode": "container_service_sys_project_deployment", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": "/clusters/{clusterName}/deployments,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment},/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/describe,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/events,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/metadata,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/pods,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/replicasets,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/replicasets/{replicaset}/yaml,/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/yaml", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": []}, {"id": "1919661132281286675", "name": "新增", "code": "skyview_deployment_add", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132281286673", "parentName": "无状态部署", "parentCode": "container_service_sys_project_deployment", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"create\",\"update\"],\"apps/replicasets\":[\"get\",\"list\",\"create\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": []}, {"id": "1919661132281286676", "name": "编辑副本数", "code": "skyview_replicas_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132281286673", "parentName": "无状态部署", "parentCode": "container_service_sys_project_deployment", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 3, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"create\",\"update\"],\"apps/replicasets\":[\"get\",\"list\",\"create\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 3, "children": []}, {"id": "1919661132281286677", "name": "编辑元数据", "code": "skyview_deployment_metadata_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132281286673", "parentName": "无状态部署", "parentCode": "container_service_sys_project_deployment", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 4, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"create\",\"update\"],\"apps/replicasets\":[\"get\",\"list\",\"create\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 4, "children": []}, {"id": "1919661132281286678", "name": "版本管理", "code": "skyview_deployment_version", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132281286673", "parentName": "无状态部署", "parentCode": "container_service_sys_project_deployment", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 5, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"create\",\"update\"],\"apps/replicasets\":[\"get\",\"list\",\"create\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 5, "children": []}, {"id": "1919661132281286679", "name": "查看版本yaml", "code": "skyview_deployment_yaml_check", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132281286673", "parentName": "无状态部署", "parentCode": "container_service_sys_project_deployment", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 6, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 6, "children": []}, {"id": "1919661132285480960", "name": "yaml对比", "code": "skyview_deployment_yaml_compare", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132281286673", "parentName": "无状态部署", "parentCode": "container_service_sys_project_deployment", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 7, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 7, "children": []}, {"id": "1919661132285480961", "name": "版本回滚", "code": "skyview_deployment_version_rollback", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132281286673", "parentName": "无状态部署", "parentCode": "container_service_sys_project_deployment", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 8, "kind": 3, "icon": null, "url": "/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}/revisions/{revision}/rollback", "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"update\"],\"apps/replicasets\":[\"get\",\"list\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 8, "children": []}, {"id": "1919661132285480962", "name": "编辑yaml", "code": "skyview_deployment_yaml_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132281286673", "parentName": "无状态部署", "parentCode": "container_service_sys_project_deployment", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 9, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"update\"],\"apps/replicasets\":[\"get\",\"list\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 9, "children": []}, {"id": "1919661132285480963", "name": "删除", "code": "skyview_deployment_remove", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132281286673", "parentName": "无状态部署", "parentCode": "container_service_sys_project_deployment", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 10, "kind": 3, "icon": null, "url": "/clusters/{clusterName}/namespaces/{namespace}/deployments/{deployment}", "method": 1, "visible": true, "annotations": "{\"component\":\"node-up-down\",\"resource_option\":{\"apps/deployments\":[\"get\",\"list\",\"delete\"],\"apps/replicasets\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 10, "children": []}]}, {"id": "1919661132285480964", "name": "有状态部署", "code": "container_service_sys_project_statefulset", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132281286672", "parentName": "工作负载", "parentCode": "container_service_sys_project_workload", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": "/project/space/resource/statefulset/list", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"events\",\"resourcequotas\",\"apps/statefulsets\",\"pods\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": [{"id": "1919661132285480965", "name": "查询", "code": "query", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480964", "parentName": "有状态部署", "parentCode": "container_service_sys_project_statefulset", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/statefulsets\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": []}, {"id": "1919661132285480966", "name": "新增", "code": "skyview_statefulset_add", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480964", "parentName": "有状态部署", "parentCode": "container_service_sys_project_statefulset", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/statefulsets\":[\"get\",\"list\",\"create\",\"update\"],\"namespaces\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": []}, {"id": "1919661132285480967", "name": "编辑副本数", "code": "skyview_replicas_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480964", "parentName": "有状态部署", "parentCode": "container_service_sys_project_statefulset", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 3, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/statefulsets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 3, "children": []}, {"id": "1919661132285480968", "name": "编辑元数据", "code": "skyview_statefulset_metadata_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480964", "parentName": "有状态部署", "parentCode": "container_service_sys_project_statefulset", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 4, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/statefulsets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 4, "children": []}, {"id": "1919661132285480969", "name": "编辑yaml", "code": "skyview_statefulset_yaml_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480964", "parentName": "有状态部署", "parentCode": "container_service_sys_project_statefulset", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 5, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/statefulsets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 5, "children": []}, {"id": "1919661132285480970", "name": "删除", "code": "skyview_statefulset_remove", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480964", "parentName": "有状态部署", "parentCode": "container_service_sys_project_statefulset", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 6, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/statefulsets\":[\"get\",\"list\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 6, "children": []}]}, {"id": "1919661132285480971", "name": "守护进程", "code": "container_service_sys_project_daemonset", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132281286672", "parentName": "工作负载", "parentCode": "container_service_sys_project_workload", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 3, "kind": 3, "icon": null, "url": "/project/space/resource/daemonset/list", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"apps/daemonsets\",\"pods\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 3, "children": [{"id": "1919661132285480972", "name": "查询", "code": "query", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480971", "parentName": "守护进程", "parentCode": "container_service_sys_project_daemonset", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": []}, {"id": "1919661132285480973", "name": "新增", "code": "skyview_daemonset_add", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480971", "parentName": "守护进程", "parentCode": "container_service_sys_project_daemonset", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\",\"create\",\"update\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": []}, {"id": "1919661132285480974", "name": "编辑元数据", "code": "skyview_daemonset_metadata_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480971", "parentName": "守护进程", "parentCode": "container_service_sys_project_daemonset", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 3, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 3, "children": []}, {"id": "1919661132285480975", "name": "编辑yaml", "code": "skyview_daemonset_yaml_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480971", "parentName": "守护进程", "parentCode": "container_service_sys_project_daemonset", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 4, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 4, "children": []}, {"id": "1919661132285480976", "name": "删除", "code": "skyview_daemonset_remove", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480971", "parentName": "守护进程", "parentCode": "container_service_sys_project_daemonset", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 5, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 5, "children": []}]}, {"id": "1919661132285480977", "name": "普通任务", "code": "container_service_sys_project_job", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132281286672", "parentName": "工作负载", "parentCode": "container_service_sys_project_workload", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 4, "kind": 3, "icon": null, "url": "/project/space/resource/job/list", "method": 1, "visible": true, "annotations": "{\"resources\":[\"pods\",\"resourcequotas\",\"stellaris.harmonycloud.cn/clusters\",\"batch/jobs\",\"events\",\"namespaces\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 4, "children": [{"id": "1919661132285480978", "name": "查询", "code": "query", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480977", "parentName": "普通任务", "parentCode": "container_service_sys_project_job", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": []}, {"id": "1919661132285480979", "name": "新增", "code": "skyview_job_add", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480977", "parentName": "普通任务", "parentCode": "container_service_sys_project_job", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"batch/jobs\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": []}, {"id": "1919661132285480980", "name": "重新执行", "code": "skyview_job_restart", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480977", "parentName": "普通任务", "parentCode": "container_service_sys_project_job", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 3, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"batch/jobs\":[\"get\",\"list\",\"create\",\"update\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 3, "children": []}, {"id": "1919661132285480981", "name": "编辑元数据", "code": "skyview_job_metadata_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480977", "parentName": "普通任务", "parentCode": "container_service_sys_project_job", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 4, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"batch/jobs\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 4, "children": []}, {"id": "1919661132285480982", "name": "编辑yaml", "code": "skyview_job_yaml_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480977", "parentName": "普通任务", "parentCode": "container_service_sys_project_job", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 5, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"batch/jobs\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 5, "children": []}, {"id": "1919661132285480983", "name": "删除", "code": "skyview_job_remove", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480977", "parentName": "普通任务", "parentCode": "container_service_sys_project_job", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 6, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"batch/jobs\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 6, "children": []}]}, {"id": "1919661132285480984", "name": "定时任务", "code": "container_service_sys_project_cronjob", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132281286672", "parentName": "工作负载", "parentCode": "container_service_sys_project_workload", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 5, "kind": 3, "icon": null, "url": "/project/space/resource/cronjob/list", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"batch/cronjobs\",\"pods\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 5, "children": [{"id": "1919661132285480985", "name": "查询", "code": "query", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480984", "parentName": "定时任务", "parentCode": "container_service_sys_project_cronjob", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": []}, {"id": "1919661132285480986", "name": "新增", "code": "add", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480984", "parentName": "定时任务", "parentCode": "container_service_sys_project_cronjob", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": []}, {"id": "1919661132285480987", "name": "启停", "code": "schedule", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480984", "parentName": "定时任务", "parentCode": "container_service_sys_project_cronjob", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 3, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 3, "children": []}, {"id": "1919661132285480988", "name": "编辑元数据", "code": "editMetaData", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480984", "parentName": "定时任务", "parentCode": "container_service_sys_project_cronjob", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 4, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 4, "children": []}, {"id": "1919661132285480989", "name": "编辑策略", "code": "editInfo", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480984", "parentName": "定时任务", "parentCode": "container_service_sys_project_cronjob", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 5, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 5, "children": []}, {"id": "1919661132285480990", "name": "编辑yaml", "code": "<PERSON><PERSON><PERSON><PERSON>", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480984", "parentName": "定时任务", "parentCode": "container_service_sys_project_cronjob", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 6, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 6, "children": []}, {"id": "1919661132285480991", "name": "删除执行记录", "code": "deleteJob", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480984", "parentName": "定时任务", "parentCode": "container_service_sys_project_cronjob", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 7, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 7, "children": []}, {"id": "1919661132285480992", "name": "删除", "code": "delete", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480984", "parentName": "定时任务", "parentCode": "container_service_sys_project_cronjob", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 8, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"batch/cronjobs\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 8, "children": []}]}, {"id": "1919661132285480993", "name": "Pod容器组", "code": "container_service_sys_project_pod_container_group", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132281286672", "parentName": "工作负载", "parentCode": "container_service_sys_project_workload", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 6, "kind": 3, "icon": null, "url": "/project/space/resource/pod", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"harmonycloud.cn/nodepools\",\"events\",\"pods\",\"nodes\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 6, "children": [{"id": "1919661132285480994", "name": "查询", "code": "query", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480993", "parentName": "Pod容器组", "parentCode": "container_service_sys_project_pod_container_group", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": []}, {"id": "1919661132285480995", "name": "文件查询", "code": "pod_file_pwd", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480993", "parentName": "Pod容器组", "parentCode": "container_service_sys_project_pod_container_group", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"pods\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": []}, {"id": "1919661132285480996", "name": "文件下载", "code": "pod_file_download", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480993", "parentName": "Pod容器组", "parentCode": "container_service_sys_project_pod_container_group", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 3, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"pods\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 3, "children": []}, {"id": "1919661132285480997", "name": "监控", "code": "monitor", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480993", "parentName": "Pod容器组", "parentCode": "container_service_sys_project_pod_container_group", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 4, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 4, "children": []}, {"id": "1919661132285480998", "name": "pod日志", "code": "containerLog", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480993", "parentName": "Pod容器组", "parentCode": "container_service_sys_project_pod_container_group", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 5, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 5, "children": []}, {"id": "1919661132285480999", "name": "pod控制台", "code": "containerTerminal", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480993", "parentName": "Pod容器组", "parentCode": "container_service_sys_project_pod_container_group", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 6, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 6, "children": []}, {"id": "1919661132285481000", "name": "事件", "code": "event", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480993", "parentName": "Pod容器组", "parentCode": "container_service_sys_project_pod_container_group", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 7, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 7, "children": []}, {"id": "1919661132285481001", "name": "查看yaml", "code": "yaml", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480993", "parentName": "Pod容器组", "parentCode": "container_service_sys_project_pod_container_group", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 8, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 8, "children": []}, {"id": "1919661132285481002", "name": "删除", "code": "remove", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285480993", "parentName": "Pod容器组", "parentCode": "container_service_sys_project_pod_container_group", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 9, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"harmonycloud.cn/nodepools\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\",\"delete\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 9, "children": []}]}]}, {"id": "1919661132285481003", "name": "配置挂载", "code": "container_service_sys_project_config_mount", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "0", "parentName": "根菜单", "parentCode": "root", "parentType": 1, "parentAppId": "-1", "parentAppCode": null, "weight": 5, "kind": 3, "icon": "v35_ConfigMounting", "url": "", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 5, "children": [{"id": "1919661132289675264", "name": "配置文件", "code": "container_service_sys_project_configMap", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285481003", "parentName": "配置挂载", "parentCode": "container_service_sys_project_config_mount", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": "/project/space/configMap/list", "method": 1, "visible": true, "annotations": "{\"resources\":[\"apps/statefulsets\",\"namespaces\",\"apps/deployments\",\"apps/daemonsets\",\"resourcequotas\",\"stellaris.harmonycloud.cn/clusters\",\"configmaps\",\"apps/replicasets\",\"stellaris.harmonycloud.cn/multiclusterresources\",\"batch/jobs\",\"stellaris.harmonycloud.cn/multiclusterresourcebindings\",\"pods\",\"batch/cronjobs\",\"events\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": [{"id": "1919661132289675265", "name": "查询", "code": "query", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675264", "parentName": "配置文件", "parentCode": "container_service_sys_project_configMap", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": []}, {"id": "1919661132289675266", "name": "新增", "code": "skyview_configmap_add", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675264", "parentName": "配置文件", "parentCode": "container_service_sys_project_configMap", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\",\"create\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\",\"create\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": []}, {"id": "1919661132289675267", "name": "编辑挂载数据", "code": "skyview_configmap_data_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675264", "parentName": "配置文件", "parentCode": "container_service_sys_project_configMap", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 3, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 3, "children": []}, {"id": "1919661132289675268", "name": "编辑元数据", "code": "skyview_configmap_meta_data_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675264", "parentName": "配置文件", "parentCode": "container_service_sys_project_configMap", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 4, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 4, "children": []}, {"id": "1919661132289675269", "name": "编辑Yaml", "code": "skyview_configmap_yaml_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675264", "parentName": "配置文件", "parentCode": "container_service_sys_project_configMap", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 5, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 5, "children": []}, {"id": "1919661132289675270", "name": "删除", "code": "skyview_configmap_remove", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675264", "parentName": "配置文件", "parentCode": "container_service_sys_project_configMap", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 6, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\",\"delete\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\",\"delete\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 6, "children": []}, {"id": "1919661132289675271", "name": "多集群配置文件查询", "code": "skyview_multi_cluster_configmap_list", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675264", "parentName": "配置文件", "parentCode": "container_service_sys_project_configMap", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 7, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 7, "children": []}, {"id": "1919661132289675272", "name": "多集群配置文件新增", "code": "skyview_multi_cluster_add_configmap", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675264", "parentName": "配置文件", "parentCode": "container_service_sys_project_configMap", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 8, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 8, "children": []}, {"id": "1919661132289675273", "name": "多集群配置文件编辑", "code": "skyview_multi_cluster_edit_configmap", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675264", "parentName": "配置文件", "parentCode": "container_service_sys_project_configMap", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 9, "kind": 3, "icon": null, "url": null, "method": 3, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 9, "children": []}, {"id": "1919661132289675274", "name": "多集群配置文件删除", "code": "skyview_multi_cluster_delete_configmap", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675264", "parentName": "配置文件", "parentCode": "container_service_sys_project_configMap", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 10, "kind": 3, "icon": null, "url": null, "method": 4, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 10, "children": []}, {"id": "1919661132289675275", "name": "多集群配置文件查看差异化配置", "code": "skyview_multi_cluster_compare_configmap", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675264", "parentName": "配置文件", "parentCode": "container_service_sys_project_configMap", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 11, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 11, "children": []}]}, {"id": "1919661132289675276", "name": "保密字典", "code": "container_service_sys_project_Secret", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132285481003", "parentName": "配置挂载", "parentCode": "container_service_sys_project_config_mount", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": "/project/space/secret/list", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"events\",\"pods\",\"apps/daemonsets\",\"apps/deployments\",\"stellaris.harmonycloud.cn/multiclusterresources\",\"stellaris.harmonycloud.cn/multiclusterresourcebindings\",\"namespaces\",\"resourcequotas\",\"apps/statefulsets\",\"secrets\",\"batch/cronjobs\",\"apps/replicasets\",\"batch/jobs\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": [{"id": "1919661132289675277", "name": "查询", "code": "query", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675276", "parentName": "保密字典", "parentCode": "container_service_sys_project_Secret", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": []}, {"id": "1919661132289675278", "name": "新增", "code": "skyview_secret_add", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675276", "parentName": "保密字典", "parentCode": "container_service_sys_project_Secret", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\",\"create\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\",\"create\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": []}, {"id": "1919661132289675279", "name": "编辑加密数据", "code": "skyview_secret_data_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675276", "parentName": "保密字典", "parentCode": "container_service_sys_project_Secret", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 3, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 3, "children": []}, {"id": "1919661132289675280", "name": "编辑元数据", "code": "skyview_secret_meta_data_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675276", "parentName": "保密字典", "parentCode": "container_service_sys_project_Secret", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 4, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 4, "children": []}, {"id": "1919661132289675281", "name": "编辑Yaml", "code": "skyview_secret_yaml_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675276", "parentName": "保密字典", "parentCode": "container_service_sys_project_Secret", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 5, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 5, "children": []}, {"id": "1919661132289675282", "name": "删除", "code": "skyview_secret_remove", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675276", "parentName": "保密字典", "parentCode": "container_service_sys_project_Secret", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 6, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"batch/cronjobs\":[\"get\",\"list\"],\"batch/jobs\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\",\"delete\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\",\"delete\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 6, "children": []}, {"id": "1919661132289675283", "name": "多集群保密字典查询", "code": "skyview_multi_cluster_secret_list", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675276", "parentName": "保密字典", "parentCode": "container_service_sys_project_Secret", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 7, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 7, "children": []}, {"id": "1919661132289675284", "name": "多集群保密字典新增", "code": "skyview_multi_cluster_add_secret", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675276", "parentName": "保密字典", "parentCode": "container_service_sys_project_Secret", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 8, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 8, "children": []}, {"id": "1919661132289675285", "name": "多集群保密字典编辑", "code": "skyview_multi_cluster_edit_secret", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675276", "parentName": "保密字典", "parentCode": "container_service_sys_project_Secret", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 9, "kind": 3, "icon": null, "url": null, "method": 3, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 9, "children": []}, {"id": "1919661132289675286", "name": "多集群保密字典删除", "code": "skyview_multi_cluster_delete_secret", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675276", "parentName": "保密字典", "parentCode": "container_service_sys_project_Secret", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 10, "kind": 3, "icon": null, "url": null, "method": 4, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 10, "children": []}, {"id": "1919661132289675287", "name": "多集群保密字典查看差异化配置", "code": "skyview_multi_cluster_compare_secret", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675276", "parentName": "保密字典", "parentCode": "container_service_sys_project_Secret", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 11, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 11, "children": []}]}]}, {"id": "1919661132289675288", "name": "存储", "code": "container_service_sys_storage_service", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "0", "parentName": "根菜单", "parentCode": "root", "parentType": 1, "parentAppId": "-1", "parentAppCode": null, "weight": 6, "kind": 3, "icon": "v35_Storage", "url": "", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 6, "children": [{"id": "1919661132289675289", "name": "存储卷声明", "code": "container_service_sys_project_PVC", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675288", "parentName": "存储", "parentCode": "container_service_sys_storage_service", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": "/project/space/pvc/list", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/multiclusterresources\",\"storage.k8s.io/storageclasses\",\"pods\",\"resourcequotas\",\"persistentvolumeclaims\",\"stellaris.harmonycloud.cn/multiclusterresourcebindings\",\"persistentvolumes\",\"stellaris.harmonycloud.cn/clusters\",\"events\",\"namespaces\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": [{"id": "1919661132289675290", "name": "查询", "code": "query", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675289", "parentName": "存储卷声明", "parentCode": "container_service_sys_project_PVC", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": []}, {"id": "1919661132289675291", "name": "新增", "code": "skyview_pvc_add", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675289", "parentName": "存储卷声明", "parentCode": "container_service_sys_project_PVC", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\",\"create\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\",\"create\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\",\"create\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": []}, {"id": "1919661132289675292", "name": "扩容", "code": "skyview_pvc_expand", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675289", "parentName": "存储卷声明", "parentCode": "container_service_sys_project_PVC", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 3, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\",\"update\",\"patch\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 3, "children": []}, {"id": "1919661132289675293", "name": "编辑元数据", "code": "skyview_pvc_meta_data_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675289", "parentName": "存储卷声明", "parentCode": "container_service_sys_project_PVC", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 4, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\",\"update\",\"patch\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 4, "children": []}, {"id": "1919661132289675294", "name": "编辑Yaml", "code": "skyview_pvc_yaml_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675289", "parentName": "存储卷声明", "parentCode": "container_service_sys_project_PVC", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 5, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\",\"update\",\"patch\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 5, "children": []}, {"id": "1919661132289675295", "name": "删除", "code": "skyview_pvc_remove", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675289", "parentName": "存储卷声明", "parentCode": "container_service_sys_project_PVC", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 6, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\",\"delete\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/multiclusterresourcebindings\":[\"get\",\"list\",\"delete\"],\"stellaris.harmonycloud.cn/multiclusterresources\":[\"get\",\"list\",\"delete\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 6, "children": []}, {"id": "1919661132289675296", "name": "多集群存储卷声明查询", "code": "skyview_multi_cluster_pvc_list", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675289", "parentName": "存储卷声明", "parentCode": "container_service_sys_project_PVC", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 7, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 7, "children": []}, {"id": "1919661132289675297", "name": "多集群存储卷声明新增", "code": "skyview_multi_cluster_add_pvc", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675289", "parentName": "存储卷声明", "parentCode": "container_service_sys_project_PVC", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 8, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 8, "children": []}, {"id": "1919661132289675298", "name": "多集群存储卷声明编辑", "code": "skyview_multi_cluster_edit_pvc", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675289", "parentName": "存储卷声明", "parentCode": "container_service_sys_project_PVC", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 9, "kind": 3, "icon": null, "url": null, "method": 3, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 9, "children": []}, {"id": "1919661132289675299", "name": "多集群存储卷声明删除", "code": "skyview_multi_cluster_delete_pvc", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675289", "parentName": "存储卷声明", "parentCode": "container_service_sys_project_PVC", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 10, "kind": 3, "icon": null, "url": null, "method": 4, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 10, "children": []}, {"id": "1919661132289675300", "name": "多集群存储卷查看差异化配置", "code": "skyview_multi_cluster_compare_pvc", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675289", "parentName": "存储卷声明", "parentCode": "container_service_sys_project_PVC", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 11, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 11, "children": []}]}, {"id": "1919661132289675301", "name": "存储卷", "code": "container_service_sys_project_PV", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675288", "parentName": "存储", "parentCode": "container_service_sys_storage_service", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": "/project/space/pv/list", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"pods\",\"storage.k8s.io/storageclasses\",\"persistentvolumeclaims\",\"persistentvolumes\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": [{"id": "1919661132289675302", "name": "查询", "code": "query", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675301", "parentName": "存储卷", "parentCode": "container_service_sys_project_PV", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"events\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"persistentvolumeclaims\":[\"get\",\"list\"],\"persistentvolumes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"],\"storage.k8s.io/storageclasses\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": []}]}]}, {"id": "1919661132289675303", "name": "网络", "code": "container_service_sys_project_network_service", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "0", "parentName": "根菜单", "parentCode": "root", "parentType": 1, "parentAppId": "-1", "parentAppCode": null, "weight": 7, "kind": 3, "icon": "v35_Network", "url": "", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 7, "children": [{"id": "1919661132289675304", "name": "Service服务", "code": "container_service_sys_project_service", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675303", "parentName": "网络", "parentCode": "container_service_sys_project_network_service", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": "/project/space/network/service", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"services\",\"expose.helper.harmonycloud.cn/layer4exposes\",\"networking.k8s.io/ingresses\",\"expose.helper.harmonycloud.cn/ingressclasses\",\"configmaps\",\"endpoints\",\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"apps/replicasets\",\"pods\",\"nodes\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": [{"id": "1919661132289675305", "name": "查询", "code": "query", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675304", "parentName": "Service服务", "parentCode": "container_service_sys_project_service", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"endpoints\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": []}, {"id": "1919661132289675306", "name": "新增", "code": "skyview_service_add", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675304", "parentName": "Service服务", "parentCode": "container_service_sys_project_service", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"create\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": []}, {"id": "1919661132289675307", "name": "编辑", "code": "skyview_service_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675304", "parentName": "Service服务", "parentCode": "container_service_sys_project_service", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 3, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 3, "children": []}, {"id": "1919661132289675308", "name": "新增四层对外路由", "code": "skyview_service_add_four_layer_exponse", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675304", "parentName": "Service服务", "parentCode": "container_service_sys_project_service", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 4, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 4, "children": []}, {"id": "1919661132289675309", "name": "新增七层对外路由", "code": "skyview_service_add_seven_layer_exponse", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675304", "parentName": "Service服务", "parentCode": "container_service_sys_project_service", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 5, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 5, "children": []}, {"id": "1919661132289675310", "name": "编辑对外路由", "code": "edit_domains", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675304", "parentName": "Service服务", "parentCode": "container_service_sys_project_service", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 6, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 6, "children": []}, {"id": "1919661132289675311", "name": "删除对外路由", "code": "remove_domains", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675304", "parentName": "Service服务", "parentCode": "container_service_sys_project_service", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 7, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 7, "children": []}, {"id": "1919661132289675312", "name": "编辑元数据", "code": "skyview_service_metadata_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675304", "parentName": "Service服务", "parentCode": "container_service_sys_project_service", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 8, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 8, "children": []}, {"id": "1919661132289675313", "name": "编辑yaml", "code": "skyview_service_yaml_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675304", "parentName": "Service服务", "parentCode": "container_service_sys_project_service", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 9, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"create\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 9, "children": []}, {"id": "1919661132289675314", "name": "删除", "code": "skyview_service_delete", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675304", "parentName": "Service服务", "parentCode": "container_service_sys_project_service", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 10, "kind": 3, "icon": null, "url": null, "method": 4, "visible": true, "annotations": "{\"resource_option\":{\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"endpoints\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\",\"update\",\"patch\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"services\":[\"get\",\"list\",\"update\",\"patch\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 10, "children": []}]}, {"id": "1919661132289675315", "name": "Ingress路由", "code": "container_service_sys_project_ingress", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675303", "parentName": "网络", "parentCode": "container_service_sys_project_network_service", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": "/project/space/network/ingress", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"services\",\"expose.helper.harmonycloud.cn/layer4exposes\",\"networking.k8s.io/ingresses\",\"expose.helper.harmonycloud.cn/ingressclasses\",\"apisix.apache.org/apisixroutes\",\"configmaps\",\"secrets\",\"endpoints\",\"apps/deployments\",\"apps/statefulsets\",\"apps/daemonsets\",\"apps/replicasets\",\"pods\",\"nodes\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": [{"id": "1919661132289675316", "name": "查询", "code": "query", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675315", "parentName": "Ingress路由", "parentCode": "container_service_sys_project_ingress", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"apisix.apache.org/apisixroutes\":[\"get\",\"list\"],\"apps/daemonsets\":[\"get\",\"list\"],\"apps/deployments\":[\"get\",\"list\"],\"apps/replicasets\":[\"get\",\"list\"],\"apps/statefulsets\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"endpoints\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/ingressclasses\":[\"get\",\"list\"],\"expose.helper.harmonycloud.cn/layer4exposes\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"networking.k8s.io/ingresses\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"services\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\",\"watch\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": []}, {"id": "1919661132289675317", "name": "新增Nginx", "code": "add_nginx", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675315", "parentName": "Ingress路由", "parentCode": "container_service_sys_project_ingress", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": []}, {"id": "1919661132289675318", "name": "编辑Nginx", "code": "edit_nginx", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675315", "parentName": "Ingress路由", "parentCode": "container_service_sys_project_ingress", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 3, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 3, "children": []}, {"id": "1919661132289675319", "name": "删除Nginx", "code": "delete_nginx", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675315", "parentName": "Ingress路由", "parentCode": "container_service_sys_project_ingress", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 4, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 4, "children": []}, {"id": "1919661132289675320", "name": "新增APISIX", "code": "add_apisix", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675315", "parentName": "Ingress路由", "parentCode": "container_service_sys_project_ingress", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 5, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 5, "children": []}, {"id": "1919661132289675321", "name": "编辑APISIX", "code": "edit_apisix", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675315", "parentName": "Ingress路由", "parentCode": "container_service_sys_project_ingress", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 6, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 6, "children": []}, {"id": "1919661132289675322", "name": "删除APISIX", "code": "delete_apisix", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675315", "parentName": "Ingress路由", "parentCode": "container_service_sys_project_ingress", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 7, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 7, "children": []}, {"id": "1919661132289675323", "name": "新增TLS", "code": "add_tls", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675315", "parentName": "Ingress路由", "parentCode": "container_service_sys_project_ingress", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 8, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 8, "children": []}, {"id": "1919661132289675324", "name": "编辑TLS", "code": "edit_tls", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675315", "parentName": "Ingress路由", "parentCode": "container_service_sys_project_ingress", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 9, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 9, "children": []}, {"id": "1919661132289675325", "name": "删除TLS", "code": "delete_tls", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675315", "parentName": "Ingress路由", "parentCode": "container_service_sys_project_ingress", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 10, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 10, "children": []}, {"id": "1919661132289675326", "name": "新增会话保持", "code": "add_session", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675315", "parentName": "Ingress路由", "parentCode": "container_service_sys_project_ingress", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 11, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 11, "children": []}, {"id": "1919661132289675327", "name": "编辑会话保持", "code": "edit_session", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675315", "parentName": "Ingress路由", "parentCode": "container_service_sys_project_ingress", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 12, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 12, "children": []}, {"id": "1919661132289675328", "name": "删除会话保持", "code": "delete_session", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675315", "parentName": "Ingress路由", "parentCode": "container_service_sys_project_ingress", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 13, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 13, "children": []}]}]}, {"id": "1919661132289675329", "name": "Kubernetes资源", "code": "container_service_kubernetes_crd_resource", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "0", "parentName": "根菜单", "parentCode": "root", "parentType": 1, "parentAppId": "-1", "parentAppCode": null, "weight": 8, "kind": 3, "icon": "ip-pool-menu", "url": "/project/space/kubernetesres", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 8, "children": [{"id": "1919661132289675330", "name": "查询", "code": "kubernetes_resource_query", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675329", "parentName": "Kubernetes资源", "parentCode": "container_service_kubernetes_crd_resource", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": []}, {"id": "1919661132289675331", "name": "编辑yaml", "code": "kuberne<PERSON>_edit_yaml", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675329", "parentName": "Kubernetes资源", "parentCode": "container_service_kubernetes_crd_resource", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": []}, {"id": "1919661132289675332", "name": "删除", "code": "kubernetes_resource_delete", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675329", "parentName": "Kubernetes资源", "parentCode": "container_service_kubernetes_crd_resource", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 3, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 3, "children": []}]}, {"id": "1919661132289675333", "name": "虚拟机", "code": "container_service_sys_workspace_vm", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "0", "parentName": "根菜单", "parentCode": "root", "parentType": 1, "parentAppId": "-1", "parentAppCode": null, "weight": 8, "kind": 3, "icon": "v35_VirtualMachine", "url": "/project/space/virtualMachines", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"kubevirt.io/virtualmachines\",\"kubevirt.io/virtualmachineinstances\",\"cdi.kubevirt.io/datavolumes\",\"kubeovn.io/ips\",\"kubeovn.io/subnets\",\"secrets\",\"configmaps\",\"pods\",\"nodes\",\"events\",\"resourcequotas\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 8, "children": [{"id": "1919661132289675334", "name": "查询", "code": "query", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675333", "parentName": "虚拟机", "parentCode": "container_service_sys_workspace_vm", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"cdi.kubevirt.io/datavolumes\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"kubeovn.io/ips\":[\"get\",\"list\"],\"kubeovn.io/subnets\":[\"get\",\"list\"],\"kubevirt.io/virtualmachineinstances\":[\"get\",\"list\"],\"kubevirt.io/virtualmachines\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": []}, {"id": "1919661132293869568", "name": "创建", "code": "vm_add", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675333", "parentName": "虚拟机", "parentCode": "container_service_sys_workspace_vm", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"cdi.kubevirt.io/datavolumes\":[\"get\",\"list\",\"create\",\"delete\"],\"configmaps\":[\"get\",\"list\",\"create\"],\"events\":[\"get\",\"list\"],\"kubeovn.io/ips\":[\"get\",\"list\"],\"kubeovn.io/subnets\":[\"get\",\"list\"],\"kubevirt.io/virtualmachineinstances\":[\"get\",\"list\"],\"kubevirt.io/virtualmachines\":[\"get\",\"list\",\"create\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"create\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": []}, {"id": "1919661132293869569", "name": "编辑", "code": "vm_edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675333", "parentName": "虚拟机", "parentCode": "container_service_sys_workspace_vm", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 3, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"cdi.kubevirt.io/datavolumes\":[\"get\",\"list\",\"create\"],\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"kubeovn.io/ips\":[\"get\",\"list\"],\"kubeovn.io/subnets\":[\"get\",\"list\"],\"kubevirt.io/virtualmachineinstances\":[\"get\",\"list\"],\"kubevirt.io/virtualmachines\":[\"get\",\"list\",\"update\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 3, "children": []}, {"id": "1919661132293869570", "name": "删除", "code": "vm_delete", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675333", "parentName": "虚拟机", "parentCode": "container_service_sys_workspace_vm", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 4, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"cdi.kubevirt.io/datavolumes\":[\"get\",\"list\",\"delete\"],\"configmaps\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"kubeovn.io/ips\":[\"get\",\"list\"],\"kubeovn.io/subnets\":[\"get\",\"list\"],\"kubevirt.io/virtualmachineinstances\":[\"get\",\"list\"],\"kubevirt.io/virtualmachines\":[\"get\",\"list\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\",\"delete\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 4, "children": []}, {"id": "1919661132293869571", "name": "启动", "code": "vm_start", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675333", "parentName": "虚拟机", "parentCode": "container_service_sys_workspace_vm", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 5, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"cdi.kubevirt.io/datavolumes\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"kubeovn.io/ips\":[\"get\",\"list\"],\"kubeovn.io/subnets\":[\"get\",\"list\"],\"kubevirt.io/virtualmachineinstances\":[\"get\",\"list\"],\"kubevirt.io/virtualmachines\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 5, "children": []}, {"id": "1919661132293869572", "name": "停止", "code": "vm_stop", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675333", "parentName": "虚拟机", "parentCode": "container_service_sys_workspace_vm", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 6, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"cdi.kubevirt.io/datavolumes\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"kubeovn.io/ips\":[\"get\",\"list\"],\"kubeovn.io/subnets\":[\"get\",\"list\"],\"kubevirt.io/virtualmachineinstances\":[\"get\",\"list\"],\"kubevirt.io/virtualmachines\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 6, "children": []}, {"id": "1919661132293869573", "name": "重启", "code": "vm_restart", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675333", "parentName": "虚拟机", "parentCode": "container_service_sys_workspace_vm", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 7, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"cdi.kubevirt.io/datavolumes\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"kubeovn.io/ips\":[\"get\",\"list\"],\"kubeovn.io/subnets\":[\"get\",\"list\"],\"kubevirt.io/virtualmachineinstances\":[\"get\",\"list\"],\"kubevirt.io/virtualmachines\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 7, "children": []}, {"id": "1919661132293869574", "name": "VNC控制台", "code": "vm_vnc", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132289675333", "parentName": "虚拟机", "parentCode": "container_service_sys_workspace_vm", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 8, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": "{\"resource_option\":{\"cdi.kubevirt.io/datavolumes\":[\"get\",\"list\"],\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"kubeovn.io/ips\":[\"get\",\"list\"],\"kubeovn.io/subnets\":[\"get\",\"list\"],\"kubevirt.io/virtualmachineinstances\":[\"get\",\"list\"],\"kubevirt.io/virtualmachines\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"nodes\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"secrets\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 8, "children": []}]}, {"id": "1919661132293869575", "name": "仓库及模板", "code": "container_service_sys_repository_template", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "0", "parentName": "根菜单", "parentCode": "root", "parentType": 1, "parentAppId": "-1", "parentAppCode": null, "weight": 9, "kind": 3, "icon": "v35_RepositoryTemplate", "url": "", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 9, "children": [{"id": "1919661132293869576", "name": "镜像仓库", "code": "container_service_sys_image_repository", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869575", "parentName": "仓库及模板", "parentCode": "container_service_sys_repository_template", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": "/project/space/repository", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": [{"id": "1919661132293869577", "name": "查询", "code": "query", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869576", "parentName": "镜像仓库", "parentCode": "container_service_sys_image_repository", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": []}, {"id": "1919661132293869578", "name": "上传", "code": "upload_image", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869576", "parentName": "镜像仓库", "parentCode": "container_service_sys_image_repository", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": []}, {"id": "1919661132293869579", "name": "编辑仓库标签", "code": "repo_label_manage", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869576", "parentName": "镜像仓库", "parentCode": "container_service_sys_image_repository", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 3, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 3, "children": []}, {"id": "1919661132293869580", "name": "查看版本", "code": "view_version", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869576", "parentName": "镜像仓库", "parentCode": "container_service_sys_image_repository", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 4, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 4, "children": []}, {"id": "1919661132293869581", "name": "删除镜像", "code": "remove_image", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869576", "parentName": "镜像仓库", "parentCode": "container_service_sys_image_repository", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 5, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 5, "children": []}, {"id": "1919661132293869582", "name": "添加规则", "code": "add_rule", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869576", "parentName": "镜像仓库", "parentCode": "container_service_sys_image_repository", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 6, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 6, "children": []}, {"id": "1919661132293869583", "name": "立即执行", "code": "execute_rule", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869576", "parentName": "镜像仓库", "parentCode": "container_service_sys_image_repository", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 7, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 7, "children": []}, {"id": "1919661132293869584", "name": "模拟运行", "code": "simulation_run", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869576", "parentName": "镜像仓库", "parentCode": "container_service_sys_image_repository", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 8, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 8, "children": []}, {"id": "1919661132293869585", "name": "编辑规则", "code": "edit_rule", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869576", "parentName": "镜像仓库", "parentCode": "container_service_sys_image_repository", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 9, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 9, "children": []}, {"id": "1919661132293869586", "name": "启用/禁用规则", "code": "enable_disable_rule", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869576", "parentName": "镜像仓库", "parentCode": "container_service_sys_image_repository", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 10, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 10, "children": []}, {"id": "1919661132293869587", "name": "删除规则", "code": "remove_rule", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869576", "parentName": "镜像仓库", "parentCode": "container_service_sys_image_repository", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 11, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 11, "children": []}, {"id": "1919661132293869588", "name": "编辑定时任务", "code": "edit_timed_task", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869576", "parentName": "镜像仓库", "parentCode": "container_service_sys_image_repository", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 12, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 12, "children": []}, {"id": "1919661132293869589", "name": "中止", "code": "suspend_rule", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869576", "parentName": "镜像仓库", "parentCode": "container_service_sys_image_repository", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 13, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 13, "children": []}, {"id": "1919661132293869590", "name": "日志", "code": "log", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869576", "parentName": "镜像仓库", "parentCode": "container_service_sys_image_repository", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 14, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 14, "children": []}, {"id": "1919661132293869591", "name": "内容信任", "code": "content_trust", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869576", "parentName": "镜像仓库", "parentCode": "container_service_sys_image_repository", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 15, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 15, "children": []}, {"id": "1919661132293869592", "name": "阻止潜在漏洞镜像", "code": "Block_potential_vulnerability_mirroring", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869576", "parentName": "镜像仓库", "parentCode": "container_service_sys_image_repository", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 16, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 16, "children": []}, {"id": "1919661132293869593", "name": "自动扫描镜像", "code": "auto_scan", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869576", "parentName": "镜像仓库", "parentCode": "container_service_sys_image_repository", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 17, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 17, "children": []}, {"id": "1919661132293869594", "name": "编辑CVE白名单", "code": "CVE_whitelist", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869576", "parentName": "镜像仓库", "parentCode": "container_service_sys_image_repository", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 18, "kind": 3, "icon": null, "url": null, "method": 2, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 18, "children": []}, {"id": "1919661132293869595", "name": "镜像复制", "code": "copy", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869576", "parentName": "镜像仓库", "parentCode": "container_service_sys_image_repository", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 19, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 19, "children": []}, {"id": "1919661132293869596", "name": "构建记录", "code": "build_record", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869576", "parentName": "镜像仓库", "parentCode": "container_service_sys_image_repository", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 20, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 20, "children": []}, {"id": "1919661132293869597", "name": "漏洞扫描", "code": "bug_scan", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869576", "parentName": "镜像仓库", "parentCode": "container_service_sys_image_repository", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 21, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 21, "children": []}, {"id": "1919661132293869598", "name": "拉取/下载", "code": "download", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869576", "parentName": "镜像仓库", "parentCode": "container_service_sys_image_repository", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 22, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 22, "children": []}, {"id": "1919661132293869599", "name": "标签管理", "code": "label_manage", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869576", "parentName": "镜像仓库", "parentCode": "container_service_sys_image_repository", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 23, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 23, "children": []}, {"id": "1919661132293869600", "name": "删除镜像版本", "code": "images_remove", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869576", "parentName": "镜像仓库", "parentCode": "container_service_sys_image_repository", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 24, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 24, "children": []}, {"id": "1919661132293869601", "name": "部署镜像", "code": "deploy", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869576", "parentName": "镜像仓库", "parentCode": "container_service_sys_image_repository", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 25, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 25, "children": []}]}, {"id": "1919661132293869602", "name": "网络模板", "code": "container_service_sys_project_space_network_template", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869575", "parentName": "仓库及模板", "parentCode": "container_service_sys_repository_template", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": "/project/space/network/template", "method": 1, "visible": true, "annotations": "{\"resources\":[\"stellaris.harmonycloud.cn/clusters\",\"namespaces\",\"resourcequotas\",\"events\",\"heimdallr.harmonycloud.cn/networkresources\",\"heimdallr.harmonycloud.cn/networkdetails\",\"heimdallr.harmonycloud.cn/hdareas\",\"heimdallr.harmonycloud.cn/hdblocks\",\"heimdallr.harmonycloud.cn/hdpods\",\"heimdallr.harmonycloud.cn/hdpools\",\"heimdallr.harmonycloud.cn/hdsvcs\",\"isolate.harmonycloud.cn/hleases\",\"mystra.heimdallr.harmonycloud.cn/podpolicies\",\"configmaps\",\"pods\"]}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": [{"id": "1919661132293869603", "name": "查询", "code": "query", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869602", "parentName": "网络模板", "parentCode": "container_service_sys_project_space_network_template", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"configmaps\":[\"get\",\"list\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": []}, {"id": "1919661132293869604", "name": "新增", "code": "skyview_project_space_network_template_add", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869602", "parentName": "网络模板", "parentCode": "container_service_sys_project_space_network_template", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"create\",\"update\",\"patch\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": []}, {"id": "1919661132293869605", "name": "编辑", "code": "skyview_project_space_network_template_update", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869602", "parentName": "网络模板", "parentCode": "container_service_sys_project_space_network_template", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 3, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"delete\",\"create\",\"update\",\"patch\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"create\",\"update\",\"patch\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 3, "children": []}, {"id": "1919661132293869606", "name": "删除", "code": "skyview_project_space_network_template_remove", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661132293869602", "parentName": "网络模板", "parentCode": "container_service_sys_project_space_network_template", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 4, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": "{\"resource_option\":{\"configmaps\":[\"get\",\"list\",\"delete\"],\"events\":[\"get\",\"list\"],\"heimdallr.harmonycloud.cn/hdareas\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdblocks\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpods\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdpools\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/hdsvcs\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/networkdetails\":[\"get\",\"list\",\"delete\"],\"heimdallr.harmonycloud.cn/networkresources\":[\"get\",\"list\",\"delete\"],\"isolate.harmonycloud.cn/hleases\":[\"get\",\"list\",\"delete\"],\"mystra.heimdallr.harmonycloud.cn/podpolicies\":[\"get\",\"list\",\"delete\"],\"namespaces\":[\"get\",\"list\"],\"pods\":[\"get\",\"list\"],\"resourcequotas\":[\"get\",\"list\"],\"stellaris.harmonycloud.cn/clusters\":[\"get\",\"list\"]}}", "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 4, "children": []}]}]}, {"id": "1919661368428990464", "name": "持续交付", "code": "CI-CD", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "0", "parentName": "根菜单", "parentCode": "root", "parentType": 1, "parentAppId": "-1", "parentAppCode": null, "weight": 10, "kind": 3, "icon": "v35_Pipeline", "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 10, "children": [{"id": "1919661368428990465", "name": "流水线管理", "code": "pipelineMgr1", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661368428990464", "parentName": "持续交付", "parentCode": "CI-CD", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": "/efficiency/pipeline", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": [{"id": "1919661368428990466", "name": "新增", "code": "create_pipeline", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661368428990465", "parentName": "流水线管理", "parentCode": "pipelineMgr1", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": []}, {"id": "1919661368428990467", "name": "流水线详情", "code": "pipelineDetail", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661368428990465", "parentName": "流水线管理", "parentCode": "pipelineMgr1", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": null, "method": 1, "visible": false, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": [{"id": "1919661368428990473", "name": "添加成员", "code": "addUser", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661368428990467", "parentName": "流水线详情", "parentCode": "pipelineDetail", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 6, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 6, "children": []}, {"id": "1919661368428990475", "name": "编辑用户", "code": "editUser", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661368428990467", "parentName": "流水线详情", "parentCode": "pipelineDetail", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 8, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 8, "children": []}, {"id": "1919661368428990476", "name": "删除用户", "code": "deleteUser", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661368428990467", "parentName": "流水线详情", "parentCode": "pipelineDetail", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 9, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 9, "children": []}, {"id": "1919661368428990477", "name": "授权", "code": "pipelinePower", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661368428990467", "parentName": "流水线详情", "parentCode": "pipelineDetail", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 10, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 10, "children": []}]}]}, {"id": "1919661368428990478", "name": "流水线模版", "code": "pipeline-template", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661368428990464", "parentName": "持续交付", "parentCode": "CI-CD", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": "/efficiency/template-manage", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": [{"id": "1919661368428990479", "name": "新增", "code": "create_template", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661368428990478", "parentName": "流水线模版", "parentCode": "pipeline-template", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": []}]}, {"id": "1919661368428990480", "name": "构建缓存管理", "code": "dependency", "type": 1, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661368428990464", "parentName": "持续交付", "parentCode": "CI-CD", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 3, "kind": 3, "icon": null, "url": "/efficiency/dependency", "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 3, "children": [{"id": "1919661368428990481", "name": "新增", "code": "add", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661368428990480", "parentName": "构建缓存管理", "parentCode": "dependency", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 1, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 1, "children": []}, {"id": "1919661368428990482", "name": "删除", "code": "delete", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661368428990480", "parentName": "构建缓存管理", "parentCode": "dependency", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 2, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 2, "children": []}, {"id": "1919661368428990483", "name": "清理", "code": "clean", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661368428990480", "parentName": "构建缓存管理", "parentCode": "dependency", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 3, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 3, "children": []}, {"id": "1919661368428990484", "name": "编辑", "code": "edit", "type": 2, "appId": "****************216", "appCode": "container_manager", "parentId": "1919661368428990480", "parentName": "构建缓存管理", "parentCode": "dependency", "parentType": 1, "parentAppId": "****************216", "parentAppCode": "container_manager", "weight": 4, "kind": 3, "icon": null, "url": null, "method": 1, "visible": true, "annotations": null, "isIframe": false, "iframeUrl": null, "api": null, "resourceTypeCode": "project", "sortId": 4, "children": []}]}]}]