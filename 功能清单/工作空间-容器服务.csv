模块,功能点,功能描述,依赖集群组件
容器服务,总览,支持查看项目层面容器资源概览，如命名空间、存储服务、集群网络、工作负载资源用量、项目制品等数据的可视化查看,
容器服务,应用市场,支持管理员在应用市场上下架、编辑values.yaml、删除helm服务模板,平台能力
容器服务,应用市场,支持helm服务模板版本管理如，新增版本、删除版本、发布指定版本到项目上,平台能力
容器服务,应用市场,支持helm服务类型新增、分类、编辑、删除,平台能力
容器服务,应用市场,支持系统内，基于已发布helm服务模板的项目进行新上架版本自定义消息推送,平台能力
容器服务,应用市场,支持查看已发布helm服务基础信息、关联资源、chart文件,平台能力
容器服务,应用市场,支持已发布的helm服务进行chat版本升级、删除管理,平台能力
容器服务,应用市场,支持已发布的helm服务进行编辑values.yaml，并进行回滚、不同版本values.yaml参数对比、下载,平台能力
容器服务,应用市场,支持已发布的helm服务进行4/7层服务暴露,"nginx,apisix"
容器服务,应用管理,见sheet「容器服务-应用管理」,"nginx,apisix"
容器服务,无状态部署,支持白屏可视化表单新增、删除,原生能力
容器服务,无状态部署,支持调整副本数、查看基础信息、pod容器组信息,原生能力
容器服务,无状态部署,支持版本管理、回滚、查看不同版本yaml、及不同版本yaml对比,原生能力
容器服务,无状态部署,支持标签和注解查看、编辑,原生能力
容器服务,无状态部署,支持查看event事件及description详细信息,原生能力
容器服务,无状态部署,支持编辑、查看、下载yaml,原生能力
容器服务,有状态部署,支持新增、删除,原生能力
容器服务,有状态部署,支持调整副本数、查看基础信息、pod容器组信息,原生能力
容器服务,有状态部署,支持标签和注解查看、编辑,原生能力
容器服务,有状态部署,支持查看event事件及description详细信息,原生能力
容器服务,有状态部署,支持编辑、查看、下载yaml,原生能力
容器服务,守护进程,支持新增、删除,原生能力
容器服务,守护进程,支持调整副本数、查看基础信息、pod容器组信息,原生能力
容器服务,守护进程,支持标签和注解查看、编辑,原生能力
容器服务,守护进程,支持查看event事件及description详细信息,原生能力
容器服务,守护进程,支持编辑、查看、下载yaml,原生能力
容器服务,普通任务,支持新增、删除,原生能力
容器服务,普通任务,支持查看副本数、查看基础信息、pod容器组信息,原生能力
容器服务,普通任务,支持job任务策略表单配置，支持设置任务执行所需成功运行pod总数、并行启动POD数、最大重试次数、最大运行时间,原生能力
容器服务,普通任务,支持标签和注解查看、编辑,原生能力
容器服务,普通任务,支持查看event事件及description详细信息,原生能力
容器服务,普通任务,支持编辑、查看、下载yaml,原生能力
容器服务,定时任务,支持新增、删除,原生能力
容器服务,定时任务,支持调整任务参数策略、查看基础信息,原生能力
容器服务,定时任务,支持CronJob执行记录查看、删除,原生能力
容器服务,定时任务,支持CronJob启停操作,原生能力
容器服务,定时任务,支持标签和注解查看、编辑,原生能力
容器服务,定时任务,支持查看event事件及description详细信息,原生能力
容器服务,定时任务,支持编辑、查看、下载yaml,原生能力
容器服务,Pod容器组,支持集群pod统一查看、删除,原生能力
容器服务,Pod容器组,支持查看基础信息、所含容器信息,原生能力
容器服务,Pod容器组,支持查看CPU、内存、磁盘、网络等资源的使用情况并按时间区间和百分比显示,"prometheus,grafana"
容器服务,Pod容器组,支持查看单个容器日志、监控及控制台操作,原生能力
容器服务,Pod容器组,支持标签和注解查看,原生能力
容器服务,Pod容器组,支持查看event事件及description详细信息,原生能力
容器服务,Pod容器组,支持查看、下载yaml,原生能力
容器服务,Pod容器组,支持查看、下载pod容器文件,平台能力
容器服务,配置文件,支持二进制和非二进制文件类型,原生能力
容器服务,配置文件,支持utf-8/gbk/gb2312/gb18030多种编码格式,原生能力
容器服务,配置文件,支持一个ConfigMap中添加多个键值对并可二次编辑,原生能力
容器服务,配置文件,支持配置文件更新后的热加载,原生能力
容器服务,配置文件,可查看关联资源如Deployment,原生能力
容器服务,配置文件,支持标签和注解查看、编辑,原生能力
容器服务,配置文件,支持查看event事件及description详细信息,原生能力
容器服务,配置文件,支持编辑、查看、下载yaml,原生能力
容器服务,保密字典,支持键值对/TLS数字证书/用户名及密码/镜像仓库类型,原生能力
容器服务,保密字典,支持utf-8/gbk/gb2312/gb18030多种编码格式,原生能力
容器服务,保密字典,支持一个Secret中添加多组同类型数据,原生能力
容器服务,保密字典,支持配置文件更新后的热加载,原生能力
容器服务,保密字典,可查看关联资源如Deployment,原生能力
容器服务,保密字典,支持标签和注解查看、编辑,原生能力
容器服务,保密字典,支持查看event事件及description详细信息,原生能力
容器服务,保密字典,支持编辑、查看、下载yaml,原生能力
容器服务,存储卷声明,支持表单新增（可指定容量、读写权限、回收策略等）、删除,原生能力
容器服务,存储卷声明,支持动态存储服务创建PVC,原生能力
容器服务,存储卷声明,支持静态绑定PV创建PVC,原生能力
容器服务,存储卷声明,支持扩容,原生能力
容器服务,存储卷声明,支持查看存储资源、查看基础信息、pod容器组信息,原生能力
容器服务,存储卷声明,支持标签和注解查看、编辑,原生能力
容器服务,存储卷声明,支持查看event事件及description详细信息,原生能力
容器服务,存储卷声明,支持编辑、查看、下载yaml,原生能力
容器服务,存储卷,支持表单新增（可指定容量、读写权限、回收策略等）、删除,原生能力
容器服务,存储卷,支持动态存储服务创建PV,原生能力
容器服务,存储卷,支持自定义外部存储源直接创建PV,原生能力
容器服务,存储卷,支持查看存储资源、查看基础信息、pod容器组信息,原生能力
容器服务,存储卷,支持标签和注解查看、编辑,原生能力
容器服务,存储卷,支持查看event事件及description详细信息,原生能力
容器服务,存储卷,支持编辑、查看、下载yaml,原生能力
容器服务,Service服务,支持可视化创建ClusterIP、NodePort、LoadBalancer类型的Service、编辑、删除,原生能力
容器服务,Service服务,支持基于label关联Deployment、StatefulSet、DaemonSet多种工作负载,原生能力
容器服务,Service服务,支持复制集群内外访问链接、查看端口映射情况、查看基础信息、关联资源、pod容器组信息,原生能力
容器服务,Service服务,支持将平台外的服务IP或域名，例如数据库、中间件等服务注册为平台内部服务，并产生内部服务名。,原生能力
容器服务,Service服务,支持4层负载均衡TCP/UDP服务暴露、编辑、删除,"依赖对应负载均衡apisix,traefik"
容器服务,Service服务,支持标签和注解查看、编辑, 
容器服务,Service服务,支持查看event事件及description详细信息, 
容器服务,Service服务,支持编辑、查看、下载yaml, 
容器服务,Ingress路由,支持7层Nginx Ingress负载均衡http、https服务暴露、编辑、删除,nginx
容器服务,Ingress路由,支持7层APISIX Ingress负载均衡http、https服务暴露、编辑、删除,apisix
容器服务,Ingress路由,支持支持自定义域名和服务路径，实现同一个域名代理多个不同的service服务,"nginx,apisix"
容器服务,Ingress路由,支持APISIX Ingress 根据权重比例实现同一域名路径下多个后端服务访问分流的能力,apisix
容器服务,Ingress路由,支持配置会话保持及注解等参数且可二次编辑,"nginx,apisix"
容器服务,Ingress路由,支持查看基础信息、高级配置、关联service,原生能力
容器服务,Ingress路由,支持标签和注解查看、编辑,原生能力
容器服务,Ingress路由,支持查看event事件及description详细信息,原生能力
容器服务,Ingress路由,支持编辑、查看、下载yaml,原生能力
容器服务,Kubernetes资源,支持查看集群中全部类型（含自定义类型）的K8S资源，可按命名空间以及资源类型进行筛选,原生能力
容器服务,Kubernetes资源,支持查看部分K8s资源的对应各种状态下的实例数,原生能力
容器服务,Kubernetes资源,支持对各个K8s资源进行删除以及Yaml编辑更新,原生能力
容器服务,虚拟机,支持创建和管理 Linux 和 Windows 虚拟机；,"kubevirt容器虚拟化
kubeovn虚拟化网络
harbor组件"
容器服务,虚拟机,支持通过控制台和 CLI 工具连接至虚拟机；,"kubevirt容器虚拟化
kubeovn虚拟化网络
harbor组件"
容器服务,虚拟机,支持虚拟机生命周期管理：删除、启动、重启、停止；,"kubevirt容器虚拟化
kubeovn虚拟化网络
harbor组件"
容器服务,虚拟机,支持虚拟机改配：变更虚拟机的CPU、内存；,"kubevirt容器虚拟化
kubeovn虚拟化网络
harbor组件"
容器服务,虚拟机,支持查看虚拟机监控、事件；,"kubevirt容器虚拟化
kubeovn虚拟化网络
harbor组件"
容器服务,虚拟机,支持在镜像仓库Harbor中上传、管理QCOW2/Raw 磁盘格式的虚拟机镜像；,"kubevirt容器虚拟化
kubeovn虚拟化网络
harbor组件"
容器服务,虚拟机,镜像流转：支持多租户的授权与共享,"kubevirt容器虚拟化
kubeovn虚拟化网络
harbor组件"
容器服务,虚拟机,支持Kube-OVN虚拟机网段划分与分配；,"kubevirt容器虚拟化
kubeovn虚拟化网络
harbor组件"
容器服务,虚拟机,支持IPv4/IPv6 双栈虚拟机；,"kubevirt容器虚拟化
kubeovn虚拟化网络
harbor组件"
容器服务,虚拟机,支持虚拟机IP固定；,"kubevirt容器虚拟化
kubeovn虚拟化网络
harbor组件"
容器服务,虚拟机,支持虚拟机系统盘、数据盘挂载storageclass；,"kubevirt容器虚拟化
kubeovn虚拟化网络
harbor组件"
容器服务,虚拟机,支持虚拟机磁盘扩容；,"kubevirt容器虚拟化
kubeovn虚拟化网络
harbor组件"
容器服务,镜像仓库,支持查看、上传公有/私有镜像仓库,harbor
容器服务,镜像仓库,支持查看镜像版本,harbor
容器服务,网络模板,支持系统/项目层面应用网络模板多网卡（calico & macvlan、双栈）创建、编辑、删除,heimdallr统一网络模型
容器服务,网络模板,支持系统层面网络模板全平台项目共享、项目层自建的网络模板则项目自己使用,heimdallr统一网络模型
容器服务,网络模板,应用组件支持网络模板使用，配置多张网卡，每张网卡可指定不同网络CNI类型/ipv4或ipv6/IP可指定个数也可指定特定的IP,heimdallr统一网络模型
容器服务,网络模板,应用组件支持网卡IP池扩缩容管理及查看IP状态,heimdallr统一网络模型
容器服务,流水线管理,支持流水线工具实现从代码提交到上线所有环节的配置可视化，推动CI/CD流程可视化、标准化、自动化运行；同时通过子步骤的形式集成自动化测试、代码扫描等能力，且凭据的管理以及环境的管理工具主要面向运维人员，提供打通第三方应用交付平台能力，实现开发测试环境创建、部署自动化；基于GitOps实现代码即部署，提升部署程序可维护性；支持插件调用，数据库变更操作。,devops
容器服务,流水线模板,支持构建模板创建和复制，创建流水线时，支持从模板创建和自定义创建；同时根据语言（Java、Nodejs、Python等）、功能（制品、部署等）内置通用模板，满足用户开箱即用。,devops
容器服务,构建缓存管理,支持新增、清理、删除构建的缓存；支持对流水线设置构建缓存，提高构建效率。,devops
