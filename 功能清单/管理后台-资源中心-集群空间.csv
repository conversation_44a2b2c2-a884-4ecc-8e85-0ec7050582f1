模块,能力,功能点,依赖集群组件
集群管理,可视化总览,支持集群资源、工作负载、组件、主机、网络等数据的可视化查看及跳转,
集群管理,可视化总览,支持集群资源、工作负载、组件、主机、网络等数据的可视化查看及跳转,
集群管理,可视化总览,支持集群资源、工作负载、组件、主机、网络等数据的可视化查看及跳转,
集群管理,主机管理,支持主机的可视化上、下线,Sisyphus部署平台
集群管理,主机管理,支持主机维护，并迁移业务pod到其它的主机上,node-isolate故障隔离组件
集群管理,主机管理,支持主机隔离，当节点故障的时候可隔离主机拒绝业务pod的调度,Sisyphus部署平台
集群管理,主机管理,支持批量添加arm、x86架构的主机，还可查看添加主机的进度,Sisyphus部署平台
集群管理,主机管理,支持纳管和使用具有GPU的主机,"nvidia-manager，orion,prometheus"
集群管理,主机管理,支持节点GPU物理卡的算力和显存虚拟化,"nvidia-manager，orion,prometheus"
集群管理,主机管理,支持主机状态及CPU、内存、GPU算力、虚拟GPU显存、物理/虚拟GPU卡等资源使用情况的可视化展示,平台
集群管理,主机管理,支持标签和污点的可视化管理,原生能力
集群管理,主机管理,支持对主机是否允许调度可视化管理,原生能力
集群管理,主机管理,支持实时查看主机上运行的全部Pod情况,原生能力
集群管理,主机管理,支持实时查看主机的CPU、内存、磁盘、网络的使用情况,prometheus，metrics-server
集群管理,主机管理,支持查看event事件及description详细信息,原生能力
集群管理,主机管理,支持主机资源池创建、删除及资源池内主机数量的增删调整,node-pool
集群管理,主机管理,支持主机资源池资源（CPU、内存、虚拟GPU显存、GPU算力、虚拟GPU卡、物理GPU卡）向组织及项目的分配,node-pool
集群管理,主机管理,支持主机资源池资源（CPU、内存、虚拟GPU显存、GPU算力、虚拟GPU卡、物理GPU卡）向组织及项目的分配,node-pool
集群管理,主机管理,支持主机资源池资源（CPU、内存、虚拟GPU显存、GPU算力、虚拟GPU卡、物理GPU卡）向组织及项目的分配,node-pool
集群管理,命名空间管理,可查看集群下的全部命名空间及各命名空间的资源总量及用量,原生能力
集群管理,命名空间管理,支持单、多集群命名空间新增及删除,stellaris多集群管理
集群管理,命名空间管理,支持从项目维度为命名空间进行资源池&存储服务&网络等资源分配、编辑、删除,node-pool资源池 heimdallr 统一网络模型
集群管理,命名空间管理,可按命名空间查看全部类型的K8S资源，且支持查看自定义类型的K8S资源,原生能力
集群管理,命名空间管理,支持以租户、命名空间、服务为维度，设置命名空间的网络隔离策略,acl网络隔离组件
集群管理,命名空间管理,支持项目命名空间配额的扩缩容操作,原生鞥里
集群管理,命名空间管理,支持标签和注解查看、编辑,原生鞥里
集群管理,命名空间管理,支持查看event事件及description详细信息,原生鞥里
集群管理,命名空间管理,支持查看、下载yaml,原生鞥里
集群管理,命名空间管理,支持查看、下载yaml,原生鞥里
集群管理,命名空间管理,支持查看、下载yaml,原生鞥里
集群管理,集群组件管理,支持对组件及系统组件的统一管理,stellaris多集群管理
集群管理,集群组件管理,支持对集群组件的自动识别扫描并接入，并监控接入状态,stellaris多集群管理
集群管理,集群组件管理,支持查看集群组件的中文名称及功能描述,stellaris多集群管理
集群管理,集群组件管理,支持组件的参数及yaml编辑、取消接入操作,stellaris多集群管理
集群管理,集群组件管理,支持组件实例状态监控,stellaris多集群管理
集群管理,集群组件管理,支持组件本身的健康状态监控及异常消息提示,stellaris多集群管理
集群管理,集群组件管理,支持组件本身的健康状态监控及异常消息提示,stellaris多集群管理
集群管理,集群组件管理,支持组件本身的健康状态监控及异常消息提示,stellaris多集群管理
集群管理,"Deployment
无状态部署",支持表单新增、删除,原生能力
集群管理,"Deployment
无状态部署",支持调整副本数、查看基础信息、pod容器组信息,原生能力
集群管理,"Deployment
无状态部署",支持版本管理、回滚、查看不同版本yaml、及不同版本yaml对比,原生能力
集群管理,"Deployment
无状态部署",支持标签和注解查看、编辑,原生能力
集群管理,"Deployment
无状态部署",支持查看event事件及description详细信息,原生能力
集群管理,"Deployment
无状态部署",支持编辑、查看、下载yaml,原生能力
集群管理,"Deployment
无状态部署",支持编辑、查看、下载yaml,原生能力
集群管理,"Deployment
无状态部署",支持编辑、查看、下载yaml,原生能力
集群管理,"StatefulSet
有状态部署
/
DaemonSet
守护进程",支持表单新增、删除,原生能力
集群管理,"StatefulSet
有状态部署
/
DaemonSet
守护进程",支持调整副本数、查看基础信息、pod容器组信息,原生能力
集群管理,"StatefulSet
有状态部署
/
DaemonSet
守护进程",支持标签和注解查看、编辑,原生能力
集群管理,"StatefulSet
有状态部署
/
DaemonSet
守护进程",支持查看event事件及description详细信息,原生能力
集群管理,"StatefulSet
有状态部署
/
DaemonSet
守护进程",支持编辑、查看、下载yaml,原生能力
集群管理,"StatefulSet
有状态部署
/
DaemonSet
守护进程",支持编辑、查看、下载yaml,原生能力
集群管理,"StatefulSet
有状态部署
/
DaemonSet
守护进程",支持编辑、查看、下载yaml,原生能力
集群管理,Job普通任务,支持表单新增、删除,原生能力
集群管理,Job普通任务,支持查看副本数、查看基础信息、pod容器组信息,原生能力
集群管理,Job普通任务,支持job任务策略表单配置，支持设置任务执行所需成功运行pod总数、并行启动POD数、最大重试次数、最大运行时间,原生能力
集群管理,Job普通任务,支持标签和注解查看、编辑,原生能力
集群管理,Job普通任务,支持查看event事件及description详细信息,原生能力
集群管理,Job普通任务,支持编辑、查看、下载yaml,原生能力
集群管理,Job普通任务,支持编辑、查看、下载yaml,原生能力
集群管理,Job普通任务,支持编辑、查看、下载yaml,原生能力
集群管理,CronJob定时任务,支持表单新增、删除,原生能力
集群管理,CronJob定时任务,支持调整任务参数策略、查看基础信息,原生能力
集群管理,CronJob定时任务,支持CronJob执行记录查看、删除,原生能力
集群管理,CronJob定时任务,支持CronJob启停操作,原生能力
集群管理,CronJob定时任务,支持标签和注解查看、编辑,原生能力
集群管理,CronJob定时任务,支持查看event事件及description详细信息,原生能力
集群管理,CronJob定时任务,支持编辑、查看、下载yaml,原生能力
集群管理,CronJob定时任务,支持编辑、查看、下载yaml,原生能力
集群管理,CronJob定时任务,支持编辑、查看、下载yaml,原生能力
集群管理,Pod容器组,支持集群pod统一查看、删除,原生能力
集群管理,Pod容器组,支持查看基础信息、所含容器信息,原生能力
集群管理,Pod容器组,支持查看CPU、内存、磁盘、网络等资源的使用情况并按时间区间和百分比显示,prometheus，metrics-server
集群管理,Pod容器组,支持查看单个容器日志、监控及控制台操作,prometheus + 原生能力
集群管理,Pod容器组,支持标签和注解查看,原生能力
集群管理,Pod容器组,支持查看event事件及description详细信息,原生能力
集群管理,Pod容器组,支持查看、下载yaml,原生能力
集群管理,Pod容器组,支持查看、下载yaml,原生能力
集群管理,Pod容器组,支持查看、下载yaml,原生能力
集群管理,ConfigMap配置文件,支持二进制和非二进制文件类型,原生能力
集群管理,ConfigMap配置文件,支持utf-8/gbk/gb2312/gb18030多种编码格式,原生能力
集群管理,ConfigMap配置文件,支持一个ConfigMap中添加多个键值对并可二次编辑,原生能力
集群管理,ConfigMap配置文件,支持配置文件更新后的热加载,原生能力
集群管理,ConfigMap配置文件,可查看关联资源如Deployment,原生能力
集群管理,ConfigMap配置文件,支持标签和注解查看、编辑,原生能力
集群管理,ConfigMap配置文件,支持查看event事件及description详细信息,原生能力
集群管理,ConfigMap配置文件,支持编辑、查看、下载yaml,原生能力
集群管理,ConfigMap配置文件,支持编辑、查看、下载yaml,原生能力
集群管理,ConfigMap配置文件,支持编辑、查看、下载yaml,原生能力
集群管理,Secret保密字典,支持键值对/TLS数字证书/用户名及密码/镜像仓库类型,原生能力
集群管理,Secret保密字典,支持utf-8/gbk/gb2312/gb18030多种编码格式,原生能力
集群管理,Secret保密字典,支持一个Secret中添加多组同类型数据,原生能力
集群管理,Secret保密字典,支持配置文件更新后的热加载,原生能力
集群管理,Secret保密字典,可查看关联资源如Deployment,原生能力
集群管理,Secret保密字典,支持标签和注解查看、编辑,原生能力
集群管理,Secret保密字典,支持查看event事件及description详细信息,原生能力
集群管理,Secret保密字典,支持编辑、查看、下载yaml,原生能力
集群管理,Secret保密字典,支持编辑、查看、下载yaml,原生能力
集群管理,Secret保密字典,支持编辑、查看、下载yaml,原生能力
集群管理,PVC存储卷声明,支持表单新增（可指定容量、读写权限、回收策略等）、删除,原生能力
集群管理,PVC存储卷声明,支持动态存储服务创建PVC,原生能力
集群管理,PVC存储卷声明,支持静态绑定PV创建PVC,原生能力
集群管理,PVC存储卷声明,支持扩容,原生能力
集群管理,PVC存储卷声明,支持查看存储资源、查看基础信息、pod容器组信息,原生能力
集群管理,PVC存储卷声明,支持标签和注解查看、编辑,原生能力
集群管理,PVC存储卷声明,支持查看event事件及description详细信息,原生能力
集群管理,PVC存储卷声明,支持编辑、查看、下载yaml,原生能力
集群管理,PVC存储卷声明,支持编辑、查看、下载yaml,原生能力
集群管理,PVC存储卷声明,支持编辑、查看、下载yaml,原生能力
集群管理,PVC存储卷声明,支持编辑、查看、下载yaml,原生能力
集群管理,PV存储卷,支持表单新增（可指定容量、读写权限、回收策略等）、删除,原生能力
集群管理,PV存储卷,支持动态存储服务创建PV,原生能力
集群管理,PV存储卷,支持自定义外部存储源直接创建PV,原生能力
集群管理,PV存储卷,支持查看存储资源、查看基础信息、pod容器组信息,原生能力
集群管理,PV存储卷,支持标签和注解查看、编辑,原生能力
集群管理,PV存储卷,支持查看event事件及description详细信息,原生能力
集群管理,PV存储卷,支持编辑、查看、下载yaml,原生能力
集群管理,PV存储卷,支持编辑、查看、下载yaml,原生能力
集群管理,PV存储卷,支持编辑、查看、下载yaml,原生能力
集群管理,PV存储卷,支持编辑、查看、下载yaml,平台能力
集群管理,存储服务,支持表单新增（包括但不限于NFS、CEPH-RBD、GlusterFS、YRCloudFile、CephFS）、删除,平台能力
集群管理,存储服务,支持特定容量创建，而且可后续扩容,平台能力
集群管理,存储服务,支持查看存储资源和已分配量、已占用量、查看基础信息、关联资源等信息,平台能力
集群管理,存储服务,支持组织、项目资源分配、编辑、删除,平台能力
集群管理,存储服务,支持所有CSI标准的存储定制化接入,平台能力
集群管理,存储服务,支持所有CSI标准的存储定制化接入,平台能力
集群管理,存储服务,支持所有CSI标准的存储定制化接入,平台能力
集群管理,Service服务,支持可视化创建ClusterIP、NodePort、LoadBalancer类型的Service、编辑、删除,原生能力
集群管理,Service服务,支持基于label关联Deployment、StatefulSet、DaemonSet多种工作负载,原生能力
集群管理,Service服务,支持复制集群内外访问链接、查看端口映射情况、查看基础信息、关联资源、pod容器组信息,原生能力
集群管理,Service服务,支持将平台外的服务IP或域名，例如数据库、中间件等服务注册为平台内部服务，并产生内部服务名。,原生能力
集群管理,Service服务,支持4层负载均衡TCP/UDP服务暴露、编辑、删除,nginx，apisix，traefik
集群管理,Service服务,支持标签和注解查看、编辑,原生能力
集群管理,Service服务,支持查看event事件及description详细信息,原生能力
集群管理,Service服务,支持编辑、查看、下载yaml,原生能力
集群管理,Service服务,支持编辑、查看、下载yaml,原生能力
集群管理,Service服务,支持编辑、查看、下载yaml,原生能力
集群管理,Ingress路由,支持7层Nginx Ingress负载均衡http、https服务暴露、编辑、删除,nginx
集群管理,Ingress路由,支持7层APISIX Ingress负载均衡http、https服务暴露、编辑、删除,apisix
集群管理,Ingress路由,支持支持自定义域名和服务路径，实现同一个域名代理多个不同的service服务,nginx，apisix，traefik
集群管理,Ingress路由,支持APISIX Ingress 根据权重比例实现同一域名路径下多个后端服务访问分流的能力,apisix
集群管理,Ingress路由,支持配置会话保持及注解等参数且可二次编辑,nginx，apisix，traefik
集群管理,Ingress路由,支持查看基础信息、高级配置、关联service,nginx，apisix，traefik
集群管理,Ingress路由,支持标签和注解查看、编辑,原生能力
集群管理,Ingress路由,支持查看event事件及description详细信息,原生能力
集群管理,Ingress路由,支持编辑、查看、下载yaml,原生能力
集群管理,Ingress路由,支持编辑、查看、下载yaml,原生能力
集群管理,Ingress路由,支持编辑、查看、下载yaml,原生能力
集群管理,负载均衡 ,支持创建、编辑、删除基于Nginx及APISIX的负载均衡并进行高级参数配置修改,基于helm
集群管理,负载均衡 ,可按全局、或特定组织/项目划分负载均衡的使用权限,平台能力
集群管理,负载均衡 ,支持基于Nginx实现4层/7层负载均衡,nginx
集群管理,负载均衡 ,支持基于APISIX实现7层负载均衡,apisix
集群管理,负载均衡 ,支持对负载均衡进行可视化的域名和证书管理,平台能力
集群管理,负载均衡 ,支持Https、http访问,nginx，apisix，traefik
集群管理,负载均衡 ,支持可视化查看负载均衡当前关联了哪些服务,平台能力
集群管理,负载均衡 ,支持可视化查看负载均衡当前关联了哪些服务,平台能力
集群管理,负载均衡 ,支持可视化查看负载均衡当前关联了哪些服务,平台能力
集群管理,Kubernetes资源,支持查看集群中全部类型（含自定义类型）的K8S资源，可按命名空间以及资源类型进行筛选,原生能力
集群管理,Kubernetes资源,支持查看部分K8s资源的对应各种状态下的实例数,原生能力
集群管理,Kubernetes资源,支持对各个K8s资源进行删除以及Yaml编辑更新,原生能力
集群管理,Kubernetes资源,支持对各个K8s资源进行删除以及Yaml编辑更新,原生能力
集群管理,Kubernetes资源,支持对各个K8s资源进行删除以及Yaml编辑更新,原生能力
集群管理,集群空间配置,支持故障锁，主机发生隔离后，默认1小时内不会再次触发隔离，可点击重置故障锁，解除1小时“冷却期”限制,基于 kubectl drain 命令
集群管理,集群空间配置,支持集群删除，可删除特定不需要的集群,stellaris多集群管理
集群管理,集群空间配置,支持集群层面的网络隔离策略，通过设置防护对象的出站和入站规则实现容器间网络访问控制,acl网络隔离组件
集群管理,集群空间配置,支持集群Kubeconfig文件的查看及下载,stellaris多集群管理
