功能点,子功能点,功能描述,依赖集群组件
单集群应用,应用发布,支持发布过程中保存草稿至列表，可用于二次的发布,"resource-aggregate,app-model"
单集群应用,应用发布,应用组件支持新增/纳管无状态类型组件及外部组件,"resource-aggregate,app-model"
单集群应用,应用发布,纳管组件可进行二次编辑、删除,"resource-aggregate,app-model"
单集群应用,应用发布,支持应用组件之间画布编排、定义关联及启动顺序依赖关系,"resource-aggregate,app-model"
单集群应用,应用发布,支持应用组件画布编排，对内对外服务统一暴露、组件分组、编辑、取消分组等,"resource-aggregate,app-model"
单集群应用,应用发布,应用组件在集群内暴露支持Cluster IP、Headless类型,"resource-aggregate,app-model"
单集群应用,应用发布,应用组件在集群外暴露支持NodePort、Loadbalancer、HTTPS-7层对外路由、TCP-4层对外路由类型,"resource-aggregate,app-model"
单集群应用,应用发布,支持通过应用模板快速创建应用,"resource-aggregate,app-model"
单集群应用,应用发布组件配置能力,应用支持多个容器、init容器，并支持部分容器文件下载,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发布组件配置能力,支持暴露多个容器tcp/udp端口,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发布组件配置能力,支持容器使用推荐的CPU、内存资源配置模版，也可自定义期望的资源用量,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发布组件配置能力,支持为容器分配虚拟GPU卡以及GPU算力资源,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发布组件配置能力,支持设置镜像获取策略,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发布组件配置能力,支持应用下容器内的文件日志收集，支持多个目录、嵌套目录，支持收集标准输出日志,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发布组件配置能力,支持挂载多个配置文件/私密文件(configma/secret)，支持单个key或整个文件热加载，其中配置文件支持GBK编码,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发布组件配置能力,支持自定义容器内的启动命令和执行参数,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发布组件配置能力,支持配置容器的安全上下文（Security Context），可开启特权模式,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发布组件配置能力,支持配置应用的可用性和健康检查，设置健康探测的具体策略,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发布组件配置能力,支持配置钩子函数，在应用启动后或停止前调用特定接口或执行脚本命令,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发布组件配置能力,支持配置容器的时区信息,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发布组件配置能力,数据挂载支持存储卷声明（PVC）、主机路径（Hostpath）、临时路径（EmptyDir）、配置文件（Configmap）、保密字典（Secret）等类型,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发布组件配置能力,数据挂载临时路径（EmptyDir）支持存内存、磁盘2种方式；支持直接创建外，还支持共享其它容器已经创建好的EmptyDir,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发布组件配置能力,环境变量支持配置文件引用、保密字典引用、Pod字段引用、Container字段引用、手动添加,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发布组件配置能力,支持设置应用组件的亲和/反亲和，支持强制亲和/反亲和,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发布组件配置能力,支持设置应用组件按主机标签设置亲和调度策略，支持强制亲和,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发布组件配置能力,支持设置应用组件按主机或主机组设置分散调度,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发布组件配置能力,支持设置应用组件使用主机的PID命名空间、IPC命名空间、网络命名空间,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发布组件配置能力,支持设置应用组件自定义hosts,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发布组件配置能力,支持设置应用组件配置多张网卡，一张网卡即可为ipv4/6中的一种，也可为双栈,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发布组件配置能力,支持设置应用组件随机分配IP，还可以指定某几个IP或指定数量,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发布组件配置能力,支持设置应用组件外部镜像访问凭证,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发布组件配置能力,支持设置应用组件内核参数、选项配置、搜索域、解析IP服务器地址,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发布后详情管理,支持应用基础信息、应用版本、弹性伸缩、服务暴露,"app-model应用模型，
app-decompile 应用反编译
hpa 弹性伸缩组件"
单集群应用,应用发布后详情管理,支持应用组件独立/批量启停、实例数变更,"app-model应用模型，
app-decompile 应用反编译
hpa 弹性伸缩组件"
单集群应用,应用发布后详情管理,支持不同组件的应用多指标监控，如cpu/内存等(基于Granafa可自定义指标及面板),"prometheus,grafana"
单集群应用,应用发布后详情管理,支持不同组件的应用日志查询，指定特定容器或查看特定的日志行数,elk
单集群应用,应用发布后详情管理,支持应用组件查看基础信息详情、关联资源（包括Service、Ingress、Configmap、Secret、PV、PVC）、pod容器组等信息,resource-aggregate
单集群应用,应用发布后详情管理,支持应用重新启动（Pod重启）及重新加载（对应用关联的K8s资源重新渲染）,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发版,可针对应用创建发版任务，快速配置和同时管理多个应用组件的修改变更,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发版,支持一键回滚，回滚至执行该次发版动作前的状态,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发版,支持一键完成多个应用组件的批量升级,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发版,支持查看历史版本的组件及更新任务,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发版,支持应用组件蓝绿更新，可进行流量的切换,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发版,支持应用组件灰度更新，升级策略支持先启后删和先删后启；可进行流量的切换和调整,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用发版,支持应用组件灰度更新，可设定更新批次；可按Pod个数、按百分比更新；可设定各批次间的更新间隔时间,"app-model应用模型，
app-decompile 应用反编译"
单集群应用,应用HPA管理,支持基于CPU、内存hpa规则配置，还可设定冷却时间、扩缩容速率,hpa
单集群应用,应用HPA管理,支持基于自定义指标（如promethus  sql）hpa规则配置，还可设定冷却时间、扩缩容速率,hpa
单集群应用,应用HPA管理,基于表单或直接填写多条自定义的cron 时间规则，每个规则定义不同的实例数,hpa
单集群应用,应用HPA管理,支持HPA启停、删除管理,hpa
单集群应用,应用网络管理,应用组件支持网络模板使用，配置多张网卡，每张网卡可指定不同网络CNI类型/ipv4或ipv6/IP可指定个数也可指定特定的IP,heimdallr 统一网络模型
单集群应用,应用网络管理,应用组件支持网卡IP池扩缩容管理及查看IP状态,heimdallr 统一网络模型
多集群应用,多集群应用发布,支持发布过程中保存草稿至列表，可用于二次的发布,"stellaris,resource-aggregate,app-model，app-decompile "
多集群应用,多集群应用发布,支持选择多集群命名空间的下属集群进行应用发布,"stellaris,resource-aggregate,app-model，app-decompile "
多集群应用,多集群应用发布,应用组件支持新增/纳管无状态类型组件,"stellaris,resource-aggregate,app-model，app-decompile "
多集群应用,多集群应用发布,支持应用组件之间画布编排、定义关联及启动顺序依赖关系,"stellaris,resource-aggregate,app-model，app-decompile "
多集群应用,多集群应用发布,支持应用组件画布编排，对内对外服务统一暴露、组件分组、编辑、取消分组等,"stellaris,resource-aggregate,app-model，app-decompile "
多集群应用,多集群应用发布,应用组件在集群内暴露支持Cluster IP、Headless类型,"stellaris,resource-aggregate,app-model，app-decompile "
多集群应用,多集群应用发布,应用组件在集群外暴露支持NodePort、Loadbalancer、HTTPS-7层对外路由、TCP-4层对外路由类型,"stellaris,resource-aggregate,app-model，app-decompile "
多集群应用,多集群应用发布,支持应用组件服务暴露在多集群中的数据同步，同步类型支持ClusterIP、NodePort、Loadbalancer 类型的服务,"stellaris,resource-aggregate,app-model，app-decompile "
多集群应用,多集群应用发布组件配置能力,每个组件支持多个容器、init容器，并支持部分容器文件下载,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,支持配置多集群组件实例调度策略，支持复制策略以及权重策略,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,组件调度复制策略支持为多集群定义相同的组件副本数,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,组件调度权重策略支持为多集群定义不同的副本数权重以及副本数范围，并可定义所有集群的组件总副本数,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,支持以模版形式定义多集群中的容器通用配置信息，并可对不同集群的配置进行差异化修改,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,支持暴露多个容器tcp/udp端口,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,支持容器使用推荐的CPU、内存资源配置模版，也可自定义期望的资源用量,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,支持为容器分配虚拟GPU卡以及GPU算力资源,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,支持自定义容器内的启动命令和执行参数,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,支持设置镜像获取策略,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,支持应用下容器内的文件日志收集，支持多个目录、嵌套目录，支持收集标准输出日志,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,支持挂载多个配置文件/私密文件(configma/secret)，支持单个key或整个文件热加载，其中配置文件支持GBK编码,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,支持自定义容器内的启动命令和执行参数,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,支持配置容器的安全上下文（Security Context），可开启特权模式,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,支持配置应用的可用性和健康检查，设置健康探测的具体策略,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,支持配置钩子函数，在应用启动后或停止前调用特定接口或执行脚本命令,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,支持配置容器的时区信息,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,数据挂载支持存储卷声明（PVC）、主机路径（Hostpath）、临时路径（EmptyDir）、配置文件（Configmap）、保密字典（Secret）等类型,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,数据挂载临时路径（EmptyDir）支持存内存、磁盘2种方式；支持直接创建外，还支持共享其它容器已经创建好的EmptyDir,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,环境变量支持配置文件引用、保密字典引用、Pod字段引用、Container字段引用、手动添加,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,支持设置应用组件的亲和/反亲和，支持强制亲和/反亲和,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,支持设置应用组件按主机标签设置亲和调度策略，支持强制亲和,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,支持设置应用组件按主机或主机组设置分散调度,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,支持设置应用组件使用主机的PID命名空间、IPC命名空间、网络命名空间,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,支持设置应用组件自定义hosts,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,支持设置应用组件配置多张网卡，一张网卡即可为ipv4/6中的一种，也可为双栈,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,支持设置应用组件随机分配IP，还可以指定某几个IP或指定数量,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,支持设置应用组件外部镜像访问凭证,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布组件配置能力,支持设置应用组件内核参数、选项配置、搜索域、解析IP服务器地址,"stellaris,resource-aggregate,app-model"
多集群应用,多集群应用发布后详情管理,支持应用基础信息、应用版本、弹性伸缩、服务暴露,"app-model应用模型，
app-decompile 应用反编译
hpa 弹性伸缩组件"
多集群应用,多集群应用发布后详情管理,支持应用组件独立/批量启停、实例数变更,"app-model应用模型，
app-decompile 应用反编译
hpa 弹性伸缩组件"
多集群应用,多集群应用发布后详情管理,支持不同组件的应用多指标监控，如cpu/内存等(基于Granafa可自定义指标及面板),"prometheus,grafana"
多集群应用,多集群应用发布后详情管理,支持不同组件的应用日志查询，指定特定容器或查看特定的日志行数,elk
多集群应用,多集群应用发布后详情管理,支持应用组件查看基础信息详情、关联资源（包括Service、Ingress、Configmap、Secret、PV、PVC）、pod容器组等信息,resource-aggregate
多集群应用,多集群应用发布后详情管理,持应用重新启动（Pod重启）及重新加载（对应用关联的K8s资源重新渲染）,"app-model应用模型，
app-decompile 应用反编译"
多集群应用,应用网络管理,应用组件支持网络模板使用，配置多张网卡，每张网卡可指定不同网络CNI类型/ipv4或ipv6/IP可指定个数也可指定特定的IP,heimdallr 统一网络模型
多集群应用,应用网络管理,应用组件支持网卡IP池扩缩容管理及查看IP状态,heimdallr 统一网络模型
Helm Chart,—,支持查看已发布helm服务基础信息、关联资源、chart文件,平台能力
Helm Chart,—,支持已发布的helm服务进行chat版本升级、删除管理,平台能力
Helm Chart,—,支持已发布的helm服务进行编辑values.yaml，并进行回滚、不同版本values.yaml参数对比、下载,平台能力
Helm Chart,—,支持已发布的helm服务进行4/7层服务暴露,"依赖apisix,nginx,traefik"
