模块,功能点,功能描述,依赖集群组件
门户管理,门户概览,支持查看系统层面系统资源概览，如资源池、存储服务、网络、负载均衡、制品服务等数据的可视化查看,？
门户管理,企业组织,支持用户体系与第三方系统的数据同步及查询能力,无
门户管理,企业组织,支持系统层级的角色及功能权限的管控能力,无
门户管理,企业组织,支持新增、编辑、删除租户,无
门户管理,企业组织,支持在租户下新增、编辑用户，支持将平台用户拉入租户,无
门户管理,企业组织,支持租户下部门的创建、编辑、删除及层级关系设置，呈现树状结构，支持将用户分配到部门，变更部门,无
门户管理,企业组织,支持在租户下创建项目，并绑定项目所属部门,无
门户管理,企业组织,支持新增、编辑、启用/停用、删除用户账号,无
门户管理,产品管理,支持查看与云原生门户平台深度集成各个云服务，以产品服务的形式，提供统一的面向用户的产品服务的功能视图,"日志组件（标准输出日志，日志文件依赖es）
监控组件（组件监控依赖promtheus，grafana）"
门户管理,产品管理,支持面向平台管理员的产品服务运维视图，支持查看服务监控、管控面组件和业务面组件的管理及产品服务日志查询,"日志组件（标准输出日志，日志文件依赖es）
监控组件（组件监控依赖promtheus，grafana）"
门户管理,集群监控,支持集群、主机（节点）、应用组件、Pod、容器级别的监控面板查看,"监控组件（组件监控依赖promtheus，grafana）
其中grafana是上层部署的"
门户管理,集群监控,支持命名空间级别的监控面板查看,"监控组件（组件监控依赖promtheus，grafana）
其中grafana是上层部署的"
门户管理,集群监控,支持集群核心组件（apiserver\scheduler）级别的监控面板查看；并支持自定义监控面板,"监控组件（组件监控依赖promtheus，grafana）
其中grafana是上层部署的"
门户管理,门户告警,支持灵活配置连续分钟满足条件进行告警,promtheus，alertmanager
门户管理,门户告警,支持最大值、最小值、平均值、所有值告警,promtheus，alertmanager
门户管理,门户告警,支持设置通知间隔，减少通知频率,promtheus，alertmanager
门户管理,门户告警,支持超时自动关闭,promtheus，alertmanager
门户管理,门户告警,支持邮箱、钉钉会话、站内信、钉钉机器人等通知方式,promtheus，alertmanager
门户管理,门户告警,支持自定义告警内容,promtheus，alertmanager
门户管理,门户告警,支持各等级未关闭告警数量统计,promtheus，alertmanager
门户管理,门户告警,支持各等级告警数量生成分布统计,promtheus，alertmanager
门户管理,门户告警,支持批量关闭告警,promtheus，alertmanager
门户管理,门户告警,支持告警详情内展示告警时间段内指标趋势以及阈值线,promtheus，alertmanager
门户管理,门户告警,支持关闭填入解决方案,promtheus，alertmanager
门户管理,门户告警,针对告警填写可能原因以及处理建议，形成告警知识库,promtheus，alertmanager
门户管理,门户告警,支持告警收敛，统一告警压缩成一条告警,promtheus，alertmanager
门户管理,门户告警,支持告警条件模糊匹配，例如集群、命名空间支持包含、正则的条件匹配,promtheus，alertmanager
门户管理,门户日志,支持对各个已集成到门户的云服务组件的日志查询功能，支持部署、pod和容器等各粒度的日志查询功能,"日志组件（标准输出日志，日志文件依赖es）
快照能力（es）"
门户管理,门户日志,支持按日志级别检索、支持精确搜索、模糊搜索、分词搜索、正则表达式搜索,"日志组件（标准输出日志，日志文件依赖es）
快照能力（es）"
门户管理,门户日志,支持日志文件、标准日志查询、导出,"日志组件（标准输出日志，日志文件依赖es）
快照能力（es）"
门户管理,门户日志,支持基于集群的日志备份规则,"日志组件（标准输出日志，日志文件依赖es）
快照能力（es）"
门户管理,门户日志,支持日志快照的创建、删除,"日志组件（标准输出日志，日志文件依赖es）
快照能力（es）"
门户管理,门户日志,支持日志备份的恢复功能,"日志组件（标准输出日志，日志文件依赖es）
快照能力（es）"
门户管理,门户日志,支持系统平台日志查询、导出,"日志组件（标准输出日志，日志文件依赖es）
快照能力（es）"
门户管理,门户灾备,通过对管理集群的MySQL与ETCD元数据单向同步，实现两个控制平面的热备，从而支持管理平台的主备切换,otter
门户管理,操作审计,支持记录不同用户在特定时间点的平台相关操作和远程IP,无
门户管理,主体配置,支持自定义配置登录页背景、登录页logo、登录框logo、平台名称、Slogan、版权声明、左上角logo、浏览器tab页logo以及浏览器tab页标题,无
门户管理,备份还原,支持新增、编辑、删除集群下命名空间的备份策略,velero
门户管理,备份还原,支持通过指定资源标签、排除资源标签去选择备份对象,velero
门户管理,备份还原,支持备份持久卷,velero
门户管理,备份还原,支持手动备份和定时备份模式，支持自定义备份时间点及备份周期,velero
门户管理,备份还原,支持自定义持久卷数据留存时长,velero
门户管理,备份仓库,绑定存储桶和集群的关系，使集群备份数据可以存放在存储桶中,s3存储
门户管理,备份仓库,支持新增、删除集群和存储桶的关联关系,s3存储
门户管理,集群巡检,可手动/自动触发集群巡检，按阈值对主机异常和组件异常情况做提示,prometheus
门户管理,集群巡检,支持自定义指标异常的判定阈值,prometheus
门户管理,集群巡检,支持邮件通知集群巡检的结果,prometheus
门户管理,集群巡检,支持集群巡检报告以pdf文档形式导出,prometheus
门户管理,故障隔离,支持异常Pod的隔离（需在容器配置中开启故障隔离），保留故障现场，方便排查,problem-pod-controller
门户管理,故障隔离,支持隔离原因和触发来源的提示,problem-pod-controller
门户管理,故障隔离,可对隔离Pod进行手动删除操作,problem-pod-controller
