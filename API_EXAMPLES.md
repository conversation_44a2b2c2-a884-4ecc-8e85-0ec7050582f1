# 集群组件功能点适配管理接口示例

本文档展示了基于 `集群组件功能点适配管理方案.md` 实现的接口示例。

## 接口列表

### 1. 获取所有集群组件的能力

**请求**
```http
GET /apis/v1/addons/features
Authorization: Bearer <token>
```

**响应示例**
```json
{
  "code": 0,
  "success": true,
  "data": {
    "cluster-187": {
      "enabled": [
        "elk/Installed",
        "elk/Ready",
        "elk/ElasticsearchReady",
        "monitoring/Installed",
        "monitoring/Ready",
        "monitoring/AlertManagerReady",
        "monitoring/PrometheusReady"
      ],
      "disabled": [
        "elk/ElasticsearchEnableBackup"
      ]
    },
    "cluster-57": {
      "enabled": [
        "elk/Installed",
        "elk/Ready"
      ],
      "disabled": [
        "elk/ElasticsearchReady",
        "elk/ElasticsearchEnableBackup"
      ]
    }
  }
}
```

### 2. 获取某集群的组件能力

**请求**
```http
GET /apis/v1/addons/clusters/cluster-187/features
Authorization: Bearer <token>
```

**响应示例**
```json
{
  "code": 0,
  "success": true,
  "data": {
    "enabled": [
      "elk/Installed",
      "elk/Ready",
      "elk/ElasticsearchReady",
      "monitoring/Installed",
      "monitoring/Ready",
      "monitoring/AlertManagerReady",
      "monitoring/PrometheusReady"
    ],
    "disabled": [
      "elk/ElasticsearchEnableBackup"
    ]
  }
}
```

### 3. 获取某集群某组件的能力列表

**请求**
```http
GET /apis/v1/addons/clusters/cluster-187/components/elk/features
Authorization: Bearer <token>
```

**响应示例**
```json
{
  "code": 0,
  "success": true,
  "data": {
    "enabled": [
      "elk/Installed",
      "elk/Ready",
      "elk/ElasticsearchReady"
    ],
    "disabled": [
      "elk/ElasticsearchEnableBackup"
    ]
  }
}
```

### 4. 获取不可用组件功能特性提示

**请求**
```http
GET /apis/v1/addons/features/messages?features=elk/Installed,elk/Ready,elk/ElasticsearchReady
Authorization: Bearer <token>
```

**响应示例**
```json
{
  "code": 0,
  "success": true,
  "data": [
    {
      "feature": "elk/Installed",
      "message": "ELK日志组件未安装"
    },
    {
      "feature": "elk/Ready",
      "message": "ELK日志组件未就绪"
    },
    {
      "feature": "elk/ElasticsearchReady",
      "message": "Elasticsearch服务未就绪"
    }
  ]
}
```

## 错误响应示例

### 参数错误
```json
{
  "code": 400,
  "success": false,
  "errorMsg": "集群名称不能为空",
  "errorDetail": "cluster parameter is required"
}
```

### 集群不存在
```json
{
  "code": 404,
  "success": false,
  "errorMsg": "集群不存在",
  "errorDetail": "cluster not found: cluster-999"
}
```

### 服务器内部错误
```json
{
  "code": 500,
  "success": false,
  "errorMsg": "服务器内部错误",
  "errorDetail": "failed to get enhance cluster addons"
}
```

## 使用示例

### curl 命令示例

```bash
# 设置环境变量
export TOKEN="your-jwt-token"
export HOST="***********"

# 获取所有集群组件的能力
curl -X GET \
  -H "Authorization: Bearer ${TOKEN}" \
  "http://${HOST}/olympus-portal/apis/v1/addons/features"

# 获取某集群的组件能力
curl -X GET \
  -H "Authorization: Bearer ${TOKEN}" \
  "http://${HOST}/olympus-portal/apis/v1/addons/clusters/cluster-187/features"

# 获取某集群某组件的能力列表
curl -X GET \
  -H "Authorization: Bearer ${TOKEN}" \
  "http://${HOST}/olympus-portal/apis/v1/addons/clusters/cluster-187/components/elk/features"

# 获取不可用组件功能特性提示
curl -X GET \
  -H "Authorization: Bearer ${TOKEN}" \
  "http://${HOST}/olympus-portal/apis/v1/addons/features/messages?features=elk/Installed,elk/Ready"
```

## 功能特性命名规范

功能特性名称采用 `{组件名}/{特性名}` 的格式，例如：

- `elk/Installed` - ELK组件已安装
- `elk/Ready` - ELK组件已就绪
- `elk/ElasticsearchReady` - Elasticsearch服务已就绪
- `elk/ElasticsearchEnableBackup` - Elasticsearch备份功能已启用
- `monitoring/PrometheusReady` - Prometheus服务已就绪
- `monitoring/AlertManagerReady` - AlertManager服务已就绪
- `sisyphus/Ready` - 部署平台组件已就绪
- `hpa/appAutoScale` - 应用自动扩缩容功能已启用

## 注意事项

1. 所有接口都需要有效的JWT token进行身份验证
2. 集群名称和组件名称参数不能为空
3. features参数支持逗号分隔的多个功能特性
4. 当前实现使用模拟数据，实际部署时需要连接真实的EnhanceClusterAddon CRD资源
5. 功能特性的状态基于EnhanceClusterAddon CRD中的FeatureConditions字段
