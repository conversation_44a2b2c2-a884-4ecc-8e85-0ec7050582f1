basePath: /
definitions:
  addon.AppDecompileConfig:
    properties:
      ip:
        type: string
      port:
        type: integer
      protocol:
        type: string
    type: object
  addon.BaselineCheckerConfig:
    properties:
      ip:
        description: Ip 默认ip
        example: ***********
        type: string
      port:
        description: Port 默认 30002
        example: 8080
        type: integer
      protocol:
        description: 默认 http
        example: 协议
        type: string
    type: object
  addon.ComponentDetailResponse:
    properties:
      appDecompileConfig:
        allOf:
        - $ref: '#/definitions/addon.AppDecompileConfig'
        description: app反编译配置
      baselineCheckerConfig:
        allOf:
        - $ref: '#/definitions/addon.BaselineCheckerConfig'
        description: 基线检查
      coreDnsConfig:
        allOf:
        - $ref: '#/definitions/addon.CoreDnsConfig'
        description: coreDns配置
      description:
        description: 描述
        type: string
      elkConfig:
        allOf:
        - $ref: '#/definitions/addon.ElkConfig'
        description: es配置
      healthcheckConditionList:
        description: healthcheck条件列表
        items:
          $ref: '#/definitions/addon.HealthcheckCondition'
        type: array
      lastMonitorTime:
        description: 最后检测时间
        type: string
      monitoringConfig:
        allOf:
        - $ref: '#/definitions/addon.MonitoringConfig'
        description: prom配置
      name:
        description: 名称
        type: string
      nickName:
        description: 显示名称
        type: string
      podInfoList:
        description: pod信息
        items:
          $ref: '#/definitions/addon.ComponentPodInfo'
        type: array
      resourceAggregateConfig:
        allOf:
        - $ref: '#/definitions/addon.ResourceAggregateConfig'
        description: 关联资源配置
      schemas:
        description: schemas（假设JSONObject对应于JSON结构）
        items:
          $ref: '#/definitions/v1alpha1.AddonConfigurationSchema'
        type: array
      selectors:
        description: selectors
        items:
          $ref: '#/definitions/addon.EndpointSelector'
        type: array
      sisyphusConfig:
        allOf:
        - $ref: '#/definitions/addon.SisyphusConfig'
        description: node-up-down配置
      statics:
        description: statics
        items:
          $ref: '#/definitions/addon.StaticInfo'
        type: array
      status:
        allOf:
        - $ref: '#/definitions/constants.AddonStatusEnum'
        description: 组件状态
      type:
        description: 类型
        type: string
    type: object
  addon.ComponentListDTO:
    properties:
      baseFlag:
        example: false
        type: boolean
      chName:
        example: 中文名称
        type: string
      description:
        example: 说明
        type: string
      enName:
        example: 英文名称
        type: string
      name:
        example: 名称
        type: string
      status:
        $ref: '#/definitions/constants.AddonStatusEnum'
      version:
        description: 'todo lanchao deadline: 2024年6月24日 增加组件状态，等待底座返回'
        example: 版本
        type: string
    type: object
  addon.ComponentPodInfo:
    properties:
      endpoint:
        type: string
      name:
        type: string
      namespace:
        type: string
      status:
        type: string
      timestamp:
        type: string
      type:
        type: string
    type: object
  addon.ComponentRequest:
    properties:
      appDecompileConfig:
        $ref: '#/definitions/addon.AppDecompileConfig'
      baseFlag:
        example: false
        type: boolean
      baselineCheckerConfig:
        $ref: '#/definitions/addon.BaselineCheckerConfig'
      coreDnsConfig:
        $ref: '#/definitions/addon.CoreDnsConfig'
      elkConfig:
        allOf:
        - $ref: '#/definitions/addon.ElkConfig'
        description: es配置
      gpuConfig:
        $ref: '#/definitions/addon.GpuConfig'
      monitoringConfig:
        $ref: '#/definitions/addon.MonitoringConfig'
      name:
        description: 名称
        example: 名称
        type: string
      resourceAggregateConfig:
        $ref: '#/definitions/addon.ResourceAggregateConfig'
      schemas:
        items:
          $ref: '#/definitions/v1alpha1.AddonConfigurationSchema'
        type: array
      selectors:
        items:
          $ref: '#/definitions/addon.EndpointSelector'
        type: array
      sisyphusConfig:
        allOf:
        - $ref: '#/definitions/addon.SisyphusConfig'
        description: node-up-down配置
      statics:
        items:
          $ref: '#/definitions/addon.StaticInfo'
        type: array
      type:
        description: 类型
        example: 类型
        type: string
    type: object
  addon.ComponentRequestList:
    properties:
      list:
        items:
          $ref: '#/definitions/addon.ComponentRequest'
        type: array
    type: object
  addon.ComponentScanResponse:
    properties:
      active:
        example: true
        type: boolean
      description:
        example: 组件描述
        type: string
      name:
        example: 组件名称
        type: string
      nickName:
        example: 组件名称
        type: string
    type: object
  addon.CoreDnsConfig:
    properties:
      autopath:
        example: true
        type: boolean
      cacheTime:
        example: 3000
        type: integer
      enableErrorLogging:
        example: true
        type: boolean
      forward:
        items:
          $ref: '#/definitions/addon.DnsDomain'
        type: array
      hosts:
        items:
          $ref: '#/definitions/addon.DnsDomain'
        type: array
    type: object
  addon.DnsDomain:
    properties:
      domain:
        example: 域名
        type: string
      resolution:
        items:
          type: string
        type: array
    type: object
  addon.ElkConfig:
    properties:
      enableBackup:
        example: false
        type: boolean
      esName:
        example: 用户名
        type: string
      esPassword:
        example: 密码
        type: string
      ip:
        example: ***********
        type: string
      port:
        example: 8080
        type: integer
      protocol:
        example: 协议
        type: string
    type: object
  addon.EndpointSelector:
    properties:
      include:
        example: 包含
        type: string
      labels:
        additionalProperties:
          type: string
        type: object
      namespace:
        example: 命名空间
        type: string
      type:
        example: 组件实例类型
        type: string
    type: object
  addon.GpuConfig:
    properties:
      gpuNodes:
        items:
          $ref: '#/definitions/addon.GpuNodes'
        type: array
    type: object
  addon.GpuNodes:
    properties:
      allocatable:
        additionalProperties:
          type: string
        type: object
      capacity:
        additionalProperties:
          type: string
        type: object
    type: object
  addon.HealthcheckCondition:
    properties:
      message:
        example: 信息
        type: string
      reason:
        example: 原因
        type: string
      status:
        example: true
        type: boolean
      timestamp:
        example: 时间
        type: string
      type:
        example: 类型
        type: string
    type: object
  addon.MonitoringConfig:
    properties:
      alertIP:
        example: ***********
        type: string
      alertPort:
        example: 8080
        type: integer
      alertProtocol:
        example: grafana协议
        type: string
      ip:
        example: ***********
        type: string
      port:
        example: 8080
        type: integer
      protocol:
        example: 协议
        type: string
      retention:
        example: 持久化时间
        type: string
    type: object
  addon.ResourceAggregateConfig:
    properties:
      ip:
        example: ***********
        type: string
      port:
        example: 8080
        type: integer
      protocol:
        example: 协议
        type: string
    type: object
  addon.SisyphusConfig:
    properties:
      ip:
        type: string
      port:
        type: integer
      protocol:
        type: string
      sisyphusName:
        type: string
      sisyphusPassword:
        type: string
    type: object
  addon.StaticInfo:
    properties:
      endPoint:
        example: endPoint
        type: string
      type:
        example: type
        type: string
    type: object
  backupserver.BucketQuota:
    properties:
      quota:
        type: integer
      quotatype:
        $ref: '#/definitions/backupserver.QuotaType'
    type: object
  backupserver.QuotaType:
    enum:
    - hard
    type: string
    x-enum-varnames:
    - HardQuota
  baseline.AggregationCheckType:
    enum:
    - Command
    - FileJSON
    - FileYAML
    - FileTEXT
    type: string
    x-enum-varnames:
    - AggregationCheckTypeCommand
    - AggregationCheckTypeFileJSON
    - AggregationCheckTypeFileYAML
    - AggregationCheckTypeFileTEXT
  baseline.AssociateBaselineItem:
    properties:
      categoryName:
        description: CategoryName 分类名称
        type: string
      standardId:
        description: StandardId 关联的基线标准id
        type: integer
      standardName:
        description: StandardName 关联基线标准名称
        type: string
    type: object
  baseline.CategoryItem:
    properties:
      builtin:
        description: Builtin 是否内置
        type: boolean
      description:
        description: Description 分类描述
        type: string
      id:
        description: ID 分类ID
        type: integer
      name:
        description: Name 分类名称
        type: string
      standardCount:
        description: StandardCount 标准数量
        type: integer
    type: object
  baseline.CategoryStandardsItem:
    properties:
      builtin:
        description: Builtin 是否内置
        type: boolean
      description:
        description: Description 分类描述
        type: string
      id:
        description: ID 分类ID
        type: integer
      name:
        description: Name 分类名称
        type: string
      standardCount:
        description: StandardCount 标准数量
        type: integer
      standards:
        description: Standards 标准列表
        items:
          $ref: '#/definitions/baseline.StandardItem'
        type: array
    type: object
  baseline.CheckClusterListRequest:
    properties:
      clusterNames:
        description: ClusterNames 集群名称列
        items:
          type: string
        type: array
    type: object
  baseline.CheckItem:
    properties:
      builtin:
        description: Builtin 是否内置
        type: boolean
      checkMode:
        allOf:
        - $ref: '#/definitions/baseline.AggregationCheckType'
        description: |-
          CheckType 列表中经过聚和后的类型
          Command -> 命令检查
          FileJSON -> 文件检查/JSON文件
          FileYAML -> 文件检查/YAML文件
      createTime:
        description: CreateTime 创建时间，格式为 YYYY-MM-DD HH:mm:ss
        type: string
      description:
        description: Description 检查项说明
        type: string
      id:
        description: Id 检查项ID
        type: integer
      kind:
        description: Kind 类型 如 Kubernetes 核心组件, 容器运行时
        type: string
      name:
        description: Name 检查项名称
        type: string
      resourceType:
        description: ResourceType 检查资源类型，例如 ETCD, Kubelet, Docker 等
        type: string
      riskLevel:
        allOf:
        - $ref: '#/definitions/baseline.RiskLevel'
        description: RiskLevel 风险级别，例如 高危High、中危Medium、低危Low
      updateTime:
        description: UpdateTime 更新时间，格式为 YYYY-MM-DD HH:mm:ss
        type: string
    type: object
  baseline.CheckResultItem:
    properties:
      checkId:
        description: CheckID 检查项ID
        type: integer
      checkerName:
        description: CheckerName 检查项名称
        type: string
      clusterName:
        description: ClusterName 集群名称
        type: string
      description:
        description: Description 说明
        type: string
      id:
        description: ID 检查项ID
        type: integer
      lastCheckJobId:
        description: LastCheckJobID 上次检查的checkJobId
        type: integer
      lastCheckTime:
        description: LastCheckTime 最后检查时间
        type: string
      lastCompletedTime:
        description: LastCompletedTime 最后完成时间
        type: string
      message:
        description: Message 错误消息
        type: string
      monitorName:
        description: MonitorName 监控名称
        type: string
      name:
        description: Name 检查项名称
        type: string
      reason:
        description: Reason 原因
        type: string
      riskLevel:
        description: |-
          RiskLevel 风险等级
          High Medium Low
        type: string
      standardId:
        description: StandardID 基线标准ID
        type: integer
      status:
        allOf:
        - $ref: '#/definitions/baseline.ResultStatus'
        description: |-
          Status 检查结果
          检查中 通过 未通过
          Checking Passed Failed
      strategyId:
        description: StrategyID 基线策略ID
        type: integer
      suggestion:
        description: Suggestion 建议
        type: string
    type: object
  baseline.CheckRiskSummary:
    properties:
      critical:
        description: Critical 严重风险数
        type: integer
      high:
        description: High 高风险数
        type: integer
      low:
        description: Low 低风险数
        type: integer
      medium:
        description: Medium 中风险数
        type: integer
      notPassed:
        description: NotPassed 未通过检查项
        type: integer
      total:
        description: Total 总数
        type: integer
    type: object
  baseline.CheckStandardCheckListRequest:
    properties:
      checkIds:
        description: CheckID 检查项ID
        items:
          type: integer
        type: array
      clusterName:
        description: ClusterName 集群名称
        type: string
      standardId:
        description: StandardID 基线标准ID
        type: integer
    type: object
  baseline.CheckStandardListRequest:
    properties:
      clusterNames:
        description: ClusterNames 集群名称列表
        items:
          type: string
        type: array
      standardId:
        description: StandardID 基线标准ID
        type: integer
    type: object
  baseline.CheckValueItem:
    properties:
      file:
        allOf:
        - $ref: '#/definitions/baseline.FileItem'
        description: |-
          File 上传的文件信息
          当 Prefix 为 File 时使用该File
      prefix:
        allOf:
        - $ref: '#/definitions/baseline.ValuePrefix'
        description: Prefix 前缀
      value:
        description: |-
          Value 检查值
          当 Prefix 为 ExactMatch FuzzyMatch 时使用value
        type: string
    type: object
  baseline.CheckerConfig:
    properties:
      checkMode:
        allOf:
        - $ref: '#/definitions/baseline.CheckerMode'
        description: CheckMode 检查方式，必填，支持 "命令检查" 或 "文件检查"
        enum:
        - Command
        - File
      command:
        allOf:
        - $ref: '#/definitions/baseline.CommandChecker'
        description: Command 命令检查方式，只有在 CheckMode 为 "命令检查" 时才有效
      file:
        allOf:
        - $ref: '#/definitions/baseline.FileChecker'
        description: File 文件检查方式，只有在 CheckMode 为 "文件检查" 时才有效
      nodeRoles:
        description: NodeRoles 节点检查范围，必填，支持值包括 Master, Worker, System
        items:
          $ref: '#/definitions/baseline.CheckerNodeRole'
        type: array
    required:
    - checkMode
    type: object
  baseline.CheckerMode:
    enum:
    - Command
    - File
    type: string
    x-enum-varnames:
    - CheckerModeCommand
    - CheckerModeFile
  baseline.CheckerNodeRole:
    enum:
    - Master
    - Worker
    - System
    type: string
    x-enum-varnames:
    - CheckerNodeRoleMaster
    - CheckerNodeRoleWorker
    - CheckerNodeRoleSystem
  baseline.ClusterCheckResultItem:
    properties:
      clusterName:
        description: ClusterName 集群名称
        type: string
      message:
        description: Message 检查失败的错误信息
        type: string
      progress:
        description: Progress 检查进度，百分比 0-100
        type: integer
      status:
        allOf:
        - $ref: '#/definitions/baseline.ResultStatus'
        description: |-
          Status 检查状态 (未检查、检查通过、检查失败、检查中等)
          Unchecked, Passed, Failed, Checking
    type: object
  baseline.ClusterRiskSummary:
    properties:
      failed:
        description: Failed 检查失败的集群数
        type: integer
      noRisk:
        description: NoRisk 无风险的集群数
        type: integer
      notChecked:
        description: NotChecked 未检查的集群数
        type: integer
      risk:
        description: Risk 有风险的集群数
        type: integer
      total:
        description: Total 总集群数
        type: integer
    type: object
  baseline.CommandChecker:
    properties:
      command:
        description: Command 执行的命令，最大长度为500字符
        maxLength: 500
        type: string
      matchType:
        allOf:
        - $ref: '#/definitions/baseline.MatchType'
        description: MatchType 匹配类型，支持 "模糊" 或 "精确" 匹配
        enum:
        - Fuzzy
        - Exact
      matchValue:
        description: MatchValue 匹配值，用于命令输出匹配，支持模糊或精确匹配
        maxLength: 500
        type: string
    type: object
  baseline.CreateBaselineCategoryRequest:
    description: 创建新的基线分类时，客户端必须提供分类名称及可选的描述
    properties:
      description:
        description: Description 分类描述，选填且不能超过100字符
        maxLength: 200
        type: string
      name:
        description: Name 分类名称，必填且不能超过20字符
        maxLength: 20
        type: string
    required:
    - name
    type: object
  baseline.CreateBaselineCategoryResponse:
    properties:
      id:
        description: ID 基线分类ID
        type: integer
    type: object
  baseline.CreateBaselineRequest:
    properties:
      categoryId:
        description: CategoryId 基线标准所属分类
        type: integer
      checkers:
        description: |-
          Checkers 关联创建的Checker 检查器（可选）
          不带有关联表的ID
        items:
          $ref: '#/definitions/baseline.SimpleBaselineChecker'
        type: array
      description:
        description: Description 基线标准描述
        maxLength: 200
        type: string
      name:
        description: Name 基线标准名称
        maxLength: 128
        type: string
      standardCheckers:
        description: |-
          StandardCheckers 基线相关的基线检查项
          需要带有关联表的ID
        items:
          $ref: '#/definitions/baseline.SimpleBaselineChecker'
        type: array
    required:
    - categoryId
    - name
    type: object
  baseline.CreateBaselineResponse:
    type: object
  baseline.CreateBaselineStrategyRequest:
    properties:
      description:
        description: Description 策略描述，非必填，不超过 200 个字符
        maxLength: 200
        type: string
      executionConfig:
        allOf:
        - $ref: '#/definitions/baseline.ExecutionConfig'
        description: ExecutionConfig 执行配置，包含执行类型及相关参数
      name:
        description: Name 策略名称，必填，不超过 32 个字符
        maxLength: 32
        type: string
      selectedBaselineIds:
        description: SelectedBaselineIds 选择的基线标准 ID 列表
        items:
          type: integer
        type: array
      selectedClusterNames:
        description: SelectedClusterNames 选择的检查集群名称列表
        items:
          type: string
        type: array
    required:
    - executionConfig
    - name
    - selectedBaselineIds
    - selectedClusterNames
    type: object
  baseline.CreateBaselineStrategyResponse:
    properties:
      id:
        description: ID 创建成功后的ID
        type: integer
    type: object
  baseline.CreateCustomCheckerRequest:
    properties:
      checkerConfig:
        allOf:
        - $ref: '#/definitions/baseline.CheckerConfig'
        description: CheckerConfig 检查项配置，必填，定义了检查方式（命令或文件）
      description:
        description: Description 检查项说明，非必填，最长300个字符
        maxLength: 300
        type: string
      name:
        description: Name 自定义检查项名称，不能为空且长度不超过32个字符
        maxLength: 128
        type: string
      resourceType:
        description: ResourceType 检查资源类型，必填，支持的类型如 Kubernetes, Docker, Host
        type: string
      riskLevel:
        allOf:
        - $ref: '#/definitions/baseline.RiskLevel'
        description: RiskLevel 风险级别，必填，支持的值包括 High、Medium、Low 高、中、低危
        enum:
        - High
        - Medium
        - Low
      suggestion:
        description: Suggestion 处理建议，非必填，最长300个字符
        maxLength: 300
        type: string
    required:
    - checkerConfig
    - name
    - resourceType
    - riskLevel
    type: object
  baseline.CreateCustomCheckerResponse:
    properties:
      id:
        description: Id  创建后的id
        type: integer
    type: object
  baseline.DeleteBaselineCategoryResponse:
    type: object
  baseline.DeleteBaselineResponse:
    type: object
  baseline.DeleteBaselineStrategyResponse:
    type: object
  baseline.DeleteCustomCheckerRequest:
    properties:
      ids:
        description: Ids 需要删除的 checkers
        items:
          type: integer
        type: array
    type: object
  baseline.EditBaselineCategoryRequest:
    properties:
      description:
        description: Description 分类描述，选填且不能超过100字符
        maxLength: 200
        type: string
      id:
        description: ID 分类ID
        type: integer
      name:
        description: Name 分类名称，必填且不能超过20字符
        maxLength: 20
        type: string
    required:
    - name
    type: object
  baseline.EditBaselineCategoryResponse:
    properties:
      id:
        description: ID 基线分类ID
        type: integer
    type: object
  baseline.ExecuteCheckJobRequest:
    properties:
      checkRequest:
        allOf:
        - $ref: '#/definitions/baseline.CheckStandardCheckListRequest'
        description: CheckRequest 检查基线的请求
      clusterRequest:
        allOf:
        - $ref: '#/definitions/baseline.CheckClusterListRequest'
        description: ClusterRequest 检查基线的请求
      scope:
        allOf:
        - $ref: '#/definitions/baseline.ExecuteStrategyCheckScope'
        description: |-
          Scope 检查范围 Strategy Cluster Standard Check
          Strategy 策略级 会在所有集群上检查策略包含的检查项 需要传 strategyId
          Cluster 集群级 会在指定集群上检查策略包含的所有基线标准 需要传 ClusterRequest
          Standard 基线标准级 会在指定集群上检查当前标准包含的所有检查项 需要传 StandardRequest
          Check 检查项级 会在指定集群上检查指定检查项 需要传 CheckRequest
      standardRequest:
        allOf:
        - $ref: '#/definitions/baseline.CheckStandardListRequest'
        description: StandardRequest 检查基线的请求
    type: object
  baseline.ExecuteCheckJobResponse:
    type: object
  baseline.ExecuteStrategyCheckScope:
    enum:
    - Strategy
    - Cluster
    - Standard
    - Check
    type: string
    x-enum-varnames:
    - ExecuteCheckJobScopeStrategy
    - ExecuteCheckJobScopeCluster
    - ExecuteCheckJobScopeStandard
    - ExecuteCheckJobScopeCheck
  baseline.ExecutionConfig:
    properties:
      cronExpression:
        description: CronExpression 周期执行的 Cron 表达式，仅在 ExecutionType 为 "Recurring"
          且 RecurringType 为 "Cron" 时使用
        type: string
      daysOfWeek:
        description: |-
          DaysOfWeek 周期执行的周几，仅在 RecurringType 为 "Weekly" 时使用
          如 [1, 2, 3, 4, 5] 表示周一到周五, [6] 表示周六 [0] 表示周日
        items:
          type: integer
        maxItems: 7
        minItems: 1
        type: array
      endDayOfMonth:
        description: EndDayOfMonth 周期执行的结束天数，仅在 RecurringType 为 "Monthly" 时使用
        maximum: 31
        minimum: 1
        type: integer
      endTime:
        description: EndTime 周期执行的结束时间，仅在 ExecutionType 为 "Recurring", "Scheduled"
          时使用
        type: string
      executionDate:
        description: ExecutionDate 执行时间包含年月日和时分秒 给 Scheduled
        type: string
      executionTime:
        description: |-
          ExecutionTime 执行时间只有时分秒给周期执行用 Recurring
          10:24:33 表示 10点24分33秒
        type: string
      executionType:
        allOf:
        - $ref: '#/definitions/baseline.ExecutionType'
        description: ExecutionType 执行类型，支持 "Immediate"（立即执行）、"Scheduled"（指定时间执行）、"Recurring"（周期执行）
      perDays:
        description: PerDays 周期执行的天数，仅在 RecurringType 为 "Daily" 时使用
        minimum: 1
        type: integer
      recurringType:
        allOf:
        - $ref: '#/definitions/baseline.RecurringType'
        description: RecurringType 周期执行的类型，仅在 ExecutionType 为 "Recurring" 时使用
      startDayOfMonth:
        description: StartDayOfMonth 周期执行的开始天数，仅在 RecurringType 为 "Monthly" 时使用
        maximum: 31
        minimum: 1
        type: integer
      startTime:
        description: StartTime 周期执行的开始时间，仅在 ExecutionType 为 "Recurring", "Scheduled"
          时使用
        type: string
      timezone:
        description: Timezone 执行时的时区，例如 UTC+08:00
        type: string
    required:
    - executionType
    type: object
  baseline.ExecutionType:
    enum:
    - Immediate
    - Scheduled
    - Recurring
    type: string
    x-enum-varnames:
    - ExecutionTypeImmediate
    - ExecutionTypeScheduled
    - ExecutionTypeRecurring
  baseline.FileChecker:
    properties:
      fileContent:
        allOf:
        - $ref: '#/definitions/baseline.FileItem'
        description: FileContent 匹配的文件信息, 非必填, 用于检查文件中是否包含特定内容
      fileLocation:
        allOf:
        - $ref: '#/definitions/baseline.FileLocationConfig'
        description: FileLocation 文件位置配置，定义文件的来源（K8s 资源或主机路径）
      fileType:
        allOf:
        - $ref: '#/definitions/baseline.FileType'
        description: FileType 文件类型，支持 "yaml" 或 "json", "Text" 文本文件
        enum:
        - Yaml
        - Json
        - Text
      matchContent:
        description: MatchContent 匹配的文件内容，非必填，用于检查文件中是否包含特定内容,优先级高于 FileContent
        type: string
    required:
    - fileLocation
    - fileType
    type: object
  baseline.FileItem:
    properties:
      id:
        description: Id 文件id
        type: integer
      link:
        description: |-
          Link 链接地址, 当前浏览器地址拼上该链接访问
          比如 *********** 环境, 则拼上 `***********${link}` ***********/minio/applicaiton/aaa.txt
        type: string
      name:
        description: Name 文件名称
        type: string
      path:
        description: Path 桶名
        type: string
      uniqueKey:
        description: UniqueKey md5加密后的名字
        type: string
    type: object
  baseline.FileLocationConfig:
    properties:
      k8sResourceRef:
        allOf:
        - $ref: '#/definitions/baseline.K8sResourceRef'
        description: K8sResourceRef 如果 Mode 为 "K8sResource"，此字段指定 Kubernetes 资源
      mode:
        allOf:
        - $ref: '#/definitions/baseline.FileLocationMode'
        description: Mode 文件来源模式，支持 "K8sResource" 或 "HostPath"
        enum:
        - K8sResource
        - HostPath
      path:
        description: Path 如果 Mode 为 "HostPath"，则此字段指定文件路径
        maxLength: 200
        type: string
    required:
    - mode
    type: object
  baseline.FileLocationMode:
    enum:
    - K8sResource
    - HostPath
    type: string
    x-enum-varnames:
    - FileLocationModeK8sResource
    - FileLocationModeHostPath
  baseline.FileType:
    enum:
    - Yaml
    - Json
    - Text
    type: string
    x-enum-varnames:
    - FileTypeYAML
    - FileTypeJSON
    - FileTypeTEXT
  baseline.GetAssignStandardCheckersResponse:
    properties:
      checkIds:
        description: 所有的checkIds
        items:
          type: integer
        type: array
      items:
        items:
          $ref: '#/definitions/baseline.StandardCheckItem'
        type: array
      selectedCheckInfo:
        allOf:
        - $ref: '#/definitions/baseline.SelectedCheckInfo'
        description: SelectedCheckInfo 选择的checkInfo
      total:
        type: integer
    type: object
  baseline.GetBaselineCheckersResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/baseline.StandardCheckItem'
        type: array
      total:
        type: integer
    type: object
  baseline.GetBaselineDetailsResponse:
    properties:
      builtin:
        description: Builtin 是否内置
        type: boolean
      categoryBuiltin:
        description: CategoryBuiltin 分类是否内置 false 自定义 true 系统
        type: boolean
      categoryId:
        description: CategoryID 分类ID
        type: integer
      categoryName:
        description: CategoryName 分类
        type: string
      checkIds:
        description: CheckIds 基线下所有的checkIds
        items:
          type: integer
        type: array
      createTime:
        description: CreateTime 创建时间
        type: string
      description:
        description: Description 基线标准说明
        type: string
      id:
        description: ID 基线标准ID
        type: integer
      items:
        items:
          $ref: '#/definitions/baseline.StandardCheckItem'
        type: array
      name:
        description: Name 名称
        type: string
      selectedCheckInfo:
        allOf:
        - $ref: '#/definitions/baseline.SelectedCheckInfo'
        description: |-
          SelectedCheckInfo 当前父基线在子基线下的选择信息
          包含了 选择的checkIds 和 对应的带有风险等级和检查值值信息的列表
      standardCheckIds:
        description: StandardCheckIds 关联的基线检查项ID
        items:
          type: integer
        type: array
      total:
        type: integer
      updateTime:
        description: UpdateTime 更新时间
        type: string
    type: object
  baseline.GetBaselineNameExistedResponse:
    properties:
      existed:
        description: Existed 是否存在
        type: boolean
    type: object
  baseline.GetBaselineStrategyDetailsResponse:
    properties:
      description:
        description: Description 策略描述，非必填，不超过 200 个字符
        maxLength: 200
        type: string
      enabled:
        description: Enabled 策略状态 是否开启
        type: boolean
      executionConfig:
        allOf:
        - $ref: '#/definitions/baseline.ExecutionConfig'
        description: ExecutionConfig 执行配置，包含执行类型及相关参数
      executionConfigHumanReadable:
        description: ExecutionConfigHumanReadable 用于人可阅读的执行配置
        type: string
      id:
        description: ID 策略ID
        type: integer
      name:
        description: Name 策略名称，必填，不超过 32 个字符
        maxLength: 32
        type: string
      selectedBaselineIds:
        description: SelectedBaselineIds 选择的基线标准 ID 列表
        items:
          type: integer
        type: array
      selectedClusterNames:
        description: SelectedClusterNames 选择的检查集群名称列表
        items:
          type: string
        type: array
    required:
    - executionConfig
    - name
    - selectedBaselineIds
    - selectedClusterNames
    type: object
  baseline.GetBaselineStrategyNameExistedResponse:
    properties:
      existed:
        description: Existed 策略名称是否存在
        type: boolean
    type: object
  baseline.GetBaselineStrategySummaryResponse:
    properties:
      checkRiskSummary:
        allOf:
        - $ref: '#/definitions/baseline.CheckRiskSummary'
        description: CheckRiskSummary 检查风险汇总
      clusterRiskSummary:
        allOf:
        - $ref: '#/definitions/baseline.ClusterRiskSummary'
        description: ClusterRiskSummary 集群风险的汇总数据
      lastCheckJobId:
        description: LastCheckJobID 最后检查的任务
        type: integer
      lastCheckTime:
        description: LastCheckTime 最后一次检查时间
        type: string
      standardRiskSummary:
        allOf:
        - $ref: '#/definitions/baseline.StandardRiskSummary'
        description: StandardRiskSummary 基线标准的汇总数据
      status:
        allOf:
        - $ref: '#/definitions/baseline.ResultStatus'
        description: Status 策略状态
    type: object
  baseline.GetCategoryNameExistedResponse:
    properties:
      existed:
        description: Existed 是否存在
        type: boolean
    type: object
  baseline.GetCustomCheckerDetailsResponse:
    properties:
      builtin:
        description: Builtin 是否内置
        type: boolean
      checkerConfig:
        allOf:
        - $ref: '#/definitions/baseline.CheckerConfig'
        description: CheckerConfig 获取检查的具体内容
      createTime:
        description: "CreateTime string \t创建时间"
        type: string
      description:
        description: Description 检查项说明
        type: string
      id:
        description: Id 检查项ID
        type: integer
      kind:
        description: Kind 类型 如 Kubernetes 核心组件, 容器运行时
        type: string
      name:
        description: Name 检查项名称
        type: string
      resourceType:
        description: ResourceType 检查资源类型，例如 ETCD, Kubelet, Docker 等
        type: string
      riskLevel:
        allOf:
        - $ref: '#/definitions/baseline.RiskLevel'
        description: RiskLevel 风险级别，例如 高危High、中危Medium、低危Low
      suggestion:
        description: Suggestion 处理建议
        type: string
      updateTime:
        description: UpdateTime 更新时间，格式为 YYYY-MM-DD HH:mm:ss
        type: string
    type: object
  baseline.GetCustomCheckerNameExistedResponse:
    properties:
      existed:
        description: Existed true 为存在 false 为不存在
        type: boolean
    type: object
  baseline.GetLastCheckJobStatusResponse:
    properties:
      checkTime:
        description: CheckTime 检查时间
        type: string
      completedTime:
        description: 完成时间
        type: string
      jobId:
        description: JobID 任务ID
        type: integer
      status:
        allOf:
        - $ref: '#/definitions/baseline.JobStatus'
        description: 状态
    type: object
  baseline.JobStatus:
    enum:
    - Pending
    - Running
    - Completed
    type: string
    x-enum-varnames:
    - JobStatusPending
    - JobStatusRunning
    - JobStatusCompleted
  baseline.K8sResourceRef:
    properties:
      apiVersion:
        type: string
      kind:
        type: string
      labels:
        additionalProperties:
          type: string
        type: object
      name:
        type: string
      namespace:
        type: string
    type: object
  baseline.ListRecurringJobResponse:
    properties:
      items:
        description: Items 任务列表
        items:
          $ref: '#/definitions/baseline.RecurringJobItem'
        type: array
    type: object
  baseline.MatchType:
    enum:
    - Fuzzy
    - Exact
    type: string
    x-enum-varnames:
    - MatchTypeFuzzy
    - MatchTypeExact
  baseline.QueryAssociatedBaselinesResponse:
    properties:
      items:
        description: Items 关联的基线标准
        items:
          $ref: '#/definitions/baseline.AssociateBaselineItem'
        type: array
    type: object
  baseline.QueryAssociatedStrategiesResponse:
    properties:
      strategies:
        description: Strategies 关联的基线策略列表
        items:
          $ref: '#/definitions/baseline.SimpleStrategyItem'
        type: array
    type: object
  baseline.QueryCategoryStandardsResponse:
    properties:
      items:
        items:
          $ref: '#/definitions/baseline.CategoryStandardsItem'
        type: array
      total:
        type: integer
    type: object
  baseline.RecurringJobItem:
    properties:
      config:
        allOf:
        - $ref: '#/definitions/baseline.ExecutionConfig'
        description: Config 执行配置
      cronExpression:
        type: string
      cronJobId:
        type: string
      id:
        type: string
      next:
        description: |-
          Next time the job will run, or the zero time if Cron has not been
          started or this entry's schedule is unsatisfiable
        type: string
      prev:
        description: Prev is the last time this job was run, or the zero time if never.
        type: string
    type: object
  baseline.RecurringType:
    enum:
    - Daily
    - Weekly
    - Monthly
    - Cron
    type: string
    x-enum-varnames:
    - RecurringTypeDaily
    - RecurringTypeWeekly
    - RecurringTypeMonthly
    - RecurringTypeCron
  baseline.ResultStatus:
    enum:
    - Checking
    - Unchecked
    - Passed
    - Failed
    - Error
    type: string
    x-enum-varnames:
    - ResultStatusChecking
    - ResultStatusUnchecked
    - ResultStatusPassed
    - ResultStatusFailed
    - ResultStatusError
  baseline.RiskLevel:
    enum:
    - High
    - Medium
    - Low
    type: string
    x-enum-varnames:
    - RiskLevelHigh
    - RiskLevelMedium
    - RiskLevelLow
  baseline.SelectedCheckInfo:
    properties:
      checkIds:
        description: CheckIds 当前父基线标准选中的检查项
        items:
          type: integer
        type: array
      checkers:
        description: Checkers 当前的父基线标准选中的检查项列表
        items:
          $ref: '#/definitions/baseline.SimpleBaselineChecker'
        type: array
      standardCheckIds:
        description: StandardCheckIds 当前父基线标准选中的关联检查项
        items:
          type: integer
        type: array
    type: object
  baseline.SimpleBaselineChecker:
    properties:
      checkId:
        description: CheckID 检查项ID
        type: integer
      checkValue:
        allOf:
        - $ref: '#/definitions/baseline.CheckValueItem'
        description: CheckValue 检查值
      riskLevel:
        description: RiskLevel 风险级别，例如 高危High、中危Medium、低危Low
        type: string
      standardCheckId:
        description: ID 检查项和基线标准的关联表ID
        type: integer
      standardId:
        description: StandardID 如果有则是关联的基线标准ID
        type: integer
    type: object
  baseline.SimpleStrategyItem:
    properties:
      clusterCount:
        description: ClusterCount 集群个数
        type: integer
      clusterNames:
        description: ClusterNames 集群名称列表
        items:
          type: string
        type: array
      id:
        description: Id 策略ID
        type: integer
      name:
        description: Name 策略名称
        type: string
    type: object
  baseline.StandardCheckIDItem:
    properties:
      id:
        description: ID  检查项ID
        type: integer
      standardCheckId:
        description: 基线与关联表的ID 保证唯一
        type: integer
    type: object
  baseline.StandardCheckItem:
    properties:
      builtin:
        description: Builtin 是否内置
        type: boolean
      checkMode:
        allOf:
        - $ref: '#/definitions/baseline.AggregationCheckType'
        description: |-
          CheckType 列表中经过聚和后的类型
          Command -> 命令检查
          FileJSON -> 文件检查/JSON文件
          FileYAML -> 文件检查/YAML文件
      checkValue:
        allOf:
        - $ref: '#/definitions/baseline.CheckValueItem'
        description: CheckValue 检查值
      createTime:
        description: CreateTime 创建时间，格式为 YYYY-MM-DD HH:mm:ss
        type: string
      description:
        description: Description 检查项说明
        type: string
      id:
        description: Id 检查项ID
        type: integer
      kind:
        description: Kind 类型 如 Kubernetes 核心组件, 容器运行时
        type: string
      name:
        description: Name 检查项名称
        type: string
      resourceType:
        description: ResourceType 检查资源类型，例如 ETCD, Kubelet, Docker 等
        type: string
      riskLevel:
        allOf:
        - $ref: '#/definitions/baseline.RiskLevel'
        description: RiskLevel 风险级别，例如 高危High、中危Medium、低危Low
      standardCheckId:
        description: StandardCheckID 关联表的ID
        type: integer
      standardId:
        description: StandardID 当前检查项所属于的标准ID
        type: integer
      suggestion:
        description: Suggestion 检查项建议
        type: string
      updateTime:
        description: UpdateTime 更新时间，格式为 YYYY-MM-DD HH:mm:ss
        type: string
    type: object
  baseline.StandardCheckResultItem:
    properties:
      baselineName:
        description: BaselineName 基线名称
        type: string
      checkRiskSummary:
        allOf:
        - $ref: '#/definitions/baseline.CheckRiskSummary'
        description: CheckRiskSummary 检查风险汇总
      clusterName:
        description: ClusterName 集群名称
        type: string
      id:
        description: ID 基线标准
        type: integer
      lastCheckJobId:
        description: LastCheckJobID 上次检查的checkJobId
        type: integer
      lastCheckTime:
        description: LastCheckTime 最后检查时间
        type: string
      lastCompletedTime:
        description: LastCompletedTime 最后完成时间
        type: string
      message:
        description: Message ...
        type: string
      name:
        description: Name 基线标准名称
        type: string
      passedCount:
        description: PassedCount 通过数量
        type: integer
      reason:
        description: Reason ...
        type: string
      status:
        allOf:
        - $ref: '#/definitions/baseline.ResultStatus'
        description: |-
          Status 检查状态 (未检查、检查通过、检查失败、检查中等)
          Unchecked, Passed, Failed, Checking
    type: object
  baseline.StandardItem:
    properties:
      builtin:
        description: Builtin 是否内置
        type: boolean
      categoryBuiltin:
        description: CategoryBuiltin 是否为内置分类 false 自定义 true 系统
        type: boolean
      categoryId:
        description: CategoryID 分类ID
        type: integer
      categoryName:
        description: CategoryName 所属分类名称
        type: string
      checkIds:
        description: |-
          CheckIds 当前基线标准包含的基线id
          只有新增/编辑基线标准的时候的聚和接口这个值才会返回
        items:
          type: integer
        type: array
      checkItem:
        allOf:
        - $ref: '#/definitions/models.PageableResponse-baseline_StandardCheckIDItem'
        description: |-
          CheckItem 当前基线标准所有的基线检查项列表
          只有新增/编辑基线标准的时候的聚和接口这个值才会返回
      createTime:
        description: CreateTime 创建时间
        type: string
      description:
        description: Description 基线标准描述
        type: string
      id:
        description: ID 基线标准
        type: integer
      name:
        description: Name 基线标准名称
        type: string
      selectedCheckInfo:
        allOf:
        - $ref: '#/definitions/baseline.SelectedCheckInfo'
        description: SelectedCheckInfo 选择的checkInfo
      standardCheckIds:
        description: |-
          StandardCheckIds 当前基线标准包含的基线id
          只有在基线标准相关的地方会返回
        items:
          type: integer
        type: array
      updateTime:
        description: UpdateTime 更新时间
        type: string
    type: object
  baseline.StandardRiskSummary:
    properties:
      notChecked:
        description: NotChecked 未检查的基线标准数
        type: integer
      notPassed:
        description: NotPassed 未通过的基线标准数
        type: integer
      passed:
        description: Passed 已通过的基线标准数
        type: integer
      total:
        description: Total 总基线标准数
        type: integer
    type: object
  baseline.StrategyItem:
    properties:
      baselineStandardCount:
        description: BaselineStandardCount 基线标准数
        type: integer
      checkClusterCount:
        description: CheckClusterCount 检查集群数
        type: integer
      checkItemCount:
        description: CheckItemCount 检查项数
        type: integer
      createTime:
        description: CreateTime 创建时间
        type: string
      enabled:
        description: Enabled 策略开关状态，true 为开启，false 为关闭
        type: boolean
      executionConfig:
        allOf:
        - $ref: '#/definitions/baseline.ExecutionConfig'
        description: ExecutionConfig 执行策略配置
      executionConfigHumanReadable:
        description: ExecutionConfigHumanReadable 用于人可阅读的执行配置
        type: string
      id:
        description: Id 策略ID
        type: integer
      name:
        description: Name 策略名称
        type: string
      updateTime:
        description: UpdateTime 更新时间
        type: string
    type: object
  baseline.SwitchBaselineStrategyRequest:
    properties:
      enabled:
        description: Enabled 是否开启基线策略
        type: boolean
      id:
        description: ID 基线策略ID
        type: integer
    required:
    - enabled
    - id
    type: object
  baseline.SwitchBaselineStrategyResponse:
    type: object
  baseline.UpdateBaselineRequest:
    properties:
      categoryId:
        description: CategoryId 基线标准所属分类
        type: integer
      checkers:
        description: Checkers 关联创建的Checker 检查器（可选）
        items:
          $ref: '#/definitions/baseline.SimpleBaselineChecker'
        type: array
      description:
        description: Description 基线标准描述
        maxLength: 200
        type: string
      id:
        description: ID 基线标准ID
        type: integer
      name:
        description: Name 基线标准名称
        maxLength: 128
        type: string
      standardCheckers:
        description: |-
          StandardCheckers 基线相关的基线检查项
          需要带有关联表的ID
        items:
          $ref: '#/definitions/baseline.SimpleBaselineChecker'
        type: array
    required:
    - categoryId
    - name
    type: object
  baseline.UpdateBaselineResponse:
    type: object
  baseline.UpdateBaselineStrategyRequest:
    properties:
      description:
        description: Description 策略描述，非必填，不超过 200 个字符
        maxLength: 200
        type: string
      executionConfig:
        allOf:
        - $ref: '#/definitions/baseline.ExecutionConfig'
        description: ExecutionConfig 执行配置，包含执行类型及相关参数
      id:
        description: ID 策略ID
        type: integer
      name:
        description: Name 策略名称，必填，不超过 32 个字符
        maxLength: 32
        type: string
      selectedBaselineIds:
        description: SelectedBaselineIds 选择的基线标准 ID 列表
        items:
          type: integer
        type: array
      selectedClusterNames:
        description: SelectedClusterNames 选择的检查集群名称列表
        items:
          type: string
        type: array
    required:
    - executionConfig
    - name
    - selectedBaselineIds
    - selectedClusterNames
    type: object
  baseline.UpdateBaselineStrategyResponse:
    type: object
  baseline.UpdateBindingBaselineStandardsRequest:
    properties:
      strategyId:
        description: StrategyID 基线策略ID
        type: integer
      unbindStandardIds:
        description: |-
          // BindStandardIDs 需要绑定的基线标准IDs
          BindStandardIDs []int64 `json:"bindStandardIds" form:"bindStandardIds"`
          UnbindStandardIDs 需要解绑的基线标准IDs
        items:
          type: integer
        type: array
    type: object
  baseline.UpdateBindingBaselineStandardsResponse:
    properties:
      bindStandardIds:
        description: BindStandardIDs 需要绑定的基线标准IDs
        items:
          type: integer
        type: array
      unbindStandardIds:
        description: UnbindStandardIDs 基线解绑的标准ID
        items:
          type: integer
        type: array
    type: object
  baseline.UpdateBindingStrategiesResponse:
    type: object
  baseline.UpdateCustomCheckerBindingStandardsRequest:
    properties:
      id:
        description: ID 基线策略ID
        type: integer
      unbindStandardIds:
        description: UnbindStandardIds 基线id
        items:
          type: integer
        type: array
    type: object
  baseline.UpdateCustomCheckerBindingStandardsResponse:
    type: object
  baseline.UpdateCustomCheckerRequest:
    properties:
      checkerConfig:
        allOf:
        - $ref: '#/definitions/baseline.CheckerConfig'
        description: CheckerConfig 检查项配置，必填，定义了检查方式（命令或文件）
      description:
        description: Description 检查项说明，非必填，最长300个字符
        maxLength: 300
        type: string
      id:
        description: ID 自定义检查项ID
        type: integer
      name:
        description: Name 自定义检查项名称，不能为空且长度不超过32个字符
        maxLength: 128
        type: string
      resourceType:
        description: ResourceType 检查资源类型，必填，支持的类型如 Kubernetes, Docker, Host
        type: string
      riskLevel:
        allOf:
        - $ref: '#/definitions/baseline.RiskLevel'
        description: RiskLevel 风险级别，必填，支持的值包括 High、Medium、Low 高、中、低危
      suggestion:
        description: Suggestion 处理建议，非必填，最长300个字符
        maxLength: 300
        type: string
    required:
    - checkerConfig
    - name
    - resourceType
    - riskLevel
    type: object
  baseline.UpdateCustomCheckerResponse:
    properties:
      id:
        type: integer
    type: object
  baseline.UploadCheckerFileResponse:
    properties:
      id:
        description: Id 文件id
        type: integer
      link:
        description: |-
          Link 链接地址, 当前浏览器地址拼上该链接访问
          比如 *********** 环境, 则拼上 `***********${link}` ***********/minio/applicaiton/aaa.txt
        type: string
      name:
        description: Name 文件名称
        type: string
      path:
        description: Path 桶名
        type: string
      uniqueKey:
        description: UniqueKey md5加密后的名字
        type: string
    type: object
  baseline.ValuePrefix:
    enum:
    - RegExp
    - Input
    - File
    type: string
    x-enum-varnames:
    - ValuePrefixRegExp
    - ValuePrefixInput
    - ValuePrefixFile
  cluster.CreateApiServerVipConfigRequest:
    properties:
      address:
        description: 地址
        example: *********
        type: string
      mask:
        description: 掩码
        example: 24
        type: integer
      networkCard:
        description: 网卡
        example: eth0
        type: string
      port:
        description: 端口
        example: 6443
        type: integer
      protocol:
        description: 协议
        example: http
        type: string
    required:
    - address
    - mask
    - networkCard
    - port
    - protocol
    type: object
  cluster.CreateApiServerVipConfigResponse:
    properties:
      address:
        description: 地址
        example: *********
        type: string
      mask:
        description: 掩码
        example: 24
        type: integer
      networkCard:
        description: 网卡
        example: eth0
        type: string
      port:
        description: 端口
        example: 6443
        type: integer
      protocol:
        description: 协议
        example: http
        type: string
    type: object
  cluster.CreateCNIConfigRequest:
    properties:
      ipv4:
        additionalProperties: true
        description: 'IPv4 类型参数 calico: {"cidr":"10.0.0.0/24"} macvlan: {"networkCard":"eth0","startIp":"10.0.0.0","endIp":"**********","gateway":"********","mask":"24","vlanId":"10","reservedIp":"*********-*********"}
          kube-ovn: {"networkCard":"绑定的网卡名称"}'
        type: object
      ipv6:
        additionalProperties: true
        description: IPv6 类型参数
        type: object
    type: object
  cluster.CreateCNIConfigResponse:
    properties:
      dualStack:
        description: 表示是否支持双栈
        example: false
        type: boolean
      ipv4:
        additionalProperties: true
        description: 'IPv4 类型参数 calico: {"cidr":"10.0.0.0/24"} |  macvlan: {"networkCard":"eth0","startIp":"10.0.0.0","endIp":"**********","gateway":"********","mask":"24","vlanId":"10","reservedIp":[{"startIp":"*********","endIp":"*********"}]}
          kube-ovn: {"networkCard":"绑定的网卡名称"}'
        type: object
      ipv6:
        additionalProperties: true
        description: IPv6 类型参数
        type: object
    type: object
  cluster.CreateLbConfigRequest:
    properties:
      address:
        description: 地址
        example: ***********
        type: string
      mask:
        description: 掩码
        example: 24
        type: integer
      networkCard:
        description: 网卡
        example: eth0
        type: string
    required:
    - address
    - mask
    - networkCard
    type: object
  cluster.CreateLbConfigResponse:
    properties:
      address:
        description: 地址
        example: ***********
        type: string
      mask:
        description: 掩码
        example: 24
        type: integer
      networkCard:
        description: 网卡
        example: eth0
        type: string
    type: object
  cluster.CreateNetworkConfigRequest:
    properties:
      apiServerVIP:
        allOf:
        - $ref: '#/definitions/cluster.CreateApiServerVipConfigRequest'
        description: APIServer Vip配置
      cnis:
        additionalProperties:
          $ref: '#/definitions/cluster.CreateCNIConfigRequest'
        description: CNI 配置信息 map key 可选值 calico｜macvlan｜kube-ovn
        type: object
      loadBalance:
        allOf:
        - $ref: '#/definitions/cluster.CreateLbConfigRequest'
        description: 负载均衡配置
    type: object
  cluster.CreateNetworkConfigResponse:
    properties:
      apiServerVIP:
        allOf:
        - $ref: '#/definitions/cluster.CreateApiServerVipConfigResponse'
        description: APIServer Vip配置
      cnis:
        additionalProperties:
          $ref: '#/definitions/cluster.CreateCNIConfigResponse'
        description: CNI 配置信息
        type: object
      loadBalance:
        allOf:
        - $ref: '#/definitions/cluster.CreateLbConfigResponse'
        description: 负载均衡配置
      mode:
        description: 表示平台已支持的CNI 类型
        example:
        - calico
        - macvlan
        - kube-ovn
        items:
          type: string
        type: array
    type: object
  cluster.CreateNodeConfigInfoRequest:
    properties:
      nodes:
        description: 节点列表
        items:
          $ref: '#/definitions/cluster.CreateNodeConfigRequest'
        type: array
      type:
        description: 节点配置类型 支持值  AllInOne(All-in-One) | MinimizeHA(最小化高可用) | StandardNoneHA(标准非高可用)
          |  StandardHA(标准高可用) 由前端同学进行控制
        example: AllInOne
        type: string
    required:
    - type
    type: object
  cluster.CreateNodeConfigInfoResponse:
    properties:
      nodes:
        description: 节点列表
        items:
          $ref: '#/definitions/cluster.CreateNodeConfigResponse'
        type: array
      type:
        description: 节点配置类型 支持值  AllInOne(All-in-One) | MinimizeHA(最小化高可用) | StandardNoneHA(标准非高可用)
          |  StandardHA(标准高可用) 由前端同学进行控制
        example: AllInOne
        type: string
    type: object
  cluster.CreateNodeConfigRequest:
    properties:
      auth:
        allOf:
        - $ref: '#/definitions/cluster.NodeAuthRequest'
        description: 认证信息
      ip:
        description: 节点IP
        example: *********
        type: string
      isDefault:
        description: 记录是否为默认节点 用于前端回显
        example: false
        type: boolean
      port:
        description: 节点端口
        example: 22
        type: integer
      role:
        description: 节点角色 支持master、worker
        example: master
        type: string
      storage:
        allOf:
        - $ref: '#/definitions/cluster.NodeStorageRequest'
        description: 节点存储配置
      supportGpu:
        description: 是否支持GPU
        example: false
        type: boolean
      systemNode:
        description: 是否为系统节点
        example: true
        type: boolean
    required:
    - auth
    - ip
    - port
    - role
    type: object
  cluster.CreateNodeConfigResponse:
    properties:
      auth:
        allOf:
        - $ref: '#/definitions/cluster.NodeAuthResponse'
        description: 认证信息
      ip:
        description: 节点IP
        example: *********
        type: string
      isDefault:
        description: 记录是否为默认节点 用于前端回显
        example: false
        type: boolean
      port:
        description: 节点端口
        example: 22
        type: integer
      role:
        description: 节点角色 支持master、worker
        example: master
        type: string
      storage:
        allOf:
        - $ref: '#/definitions/cluster.NodeStorageResponse'
        description: 节点存储配置
      supportGpu:
        description: 是否支持GPU
        example: false
        type: boolean
      systemNode:
        description: 是否为系统节点
        example: true
        type: boolean
    type: object
  cluster.CreateProcessResponse:
    properties:
      code:
        description: 步骤码
        example: distribute-pre-inspection-tools
        type: string
      description:
        description: 描述信息
        example: 描述信息
        type: string
      name:
        description: 步骤中文
        example: 下发预检工具
        type: string
      status:
        description: 步骤状态 wait process finish error
        example: process
        type: string
      steps:
        description: 子步骤列表
        items:
          $ref: '#/definitions/cluster.CreateProcessStepResponse'
        type: array
    type: object
  cluster.CreateProcessStepResponse:
    properties:
      code:
        description: 子步骤码
        example: cluster-connect-check
        type: string
      description:
        description: 子步骤描述
        example: 集群联通性检查必须....
        type: string
      errMsg:
        description: 错误详情 on Status = error
        example: 不支持arm 主机
        type: string
      errorType:
        description: 错误类型 on Status = error
        example: check_fail
        type: string
      name:
        description: 子步骤中文
        example: 集群联通性检查
        type: string
      status:
        description: 步骤状态 wait process finish error
        example: error
        type: string
    type: object
  cluster.CreateRegistryConfigRequest:
    properties:
      address:
        description: 地址
        example: test.harbor.work
        type: string
      port:
        description: 端口
        example: 80
        type: integer
      protocol:
        description: 协议
        example: http
        type: string
    required:
    - address
    - port
    - protocol
    type: object
  cluster.CreateRegistryConfigResponse:
    properties:
      address:
        description: 地址
        example: test.harbor.work
        type: string
      port:
        description: 端口
        example: 80
        type: integer
      protocol:
        description: 协议
        example: http
        type: string
    type: object
  cluster.CreateRequest:
    properties:
      clusterName:
        description: 集群名称
        example: cluster-create-test-name
        type: string
      cri:
        type: string
      description:
        description: 描述
        example: 这是创建集群的描述信息
        type: string
      hubClusterIngressAddress:
        description: 管理集群 ingress访问地址
        example: ***********
        type: string
      kubernetesCRIVersion:
        description: k8s CRI信息
        example: Docker-v19.03.15
        type: string
      kubernetesVersion:
        description: k8s 版本信息
        example: 1.21.5
        type: string
      labels:
        additionalProperties:
          type: string
        description: 标签
        type: object
      networkConfigs:
        allOf:
        - $ref: '#/definitions/cluster.CreateNetworkConfigRequest'
        description: 网络配置
      nodeConfigs:
        allOf:
        - $ref: '#/definitions/cluster.CreateNodeConfigInfoRequest'
        description: 节点配置
      registry:
        allOf:
        - $ref: '#/definitions/cluster.CreateRegistryConfigRequest'
        description: 制品服务信息
      reset:
        description: 是否进行集群重置 若为true，则忽略startFromFailed，若为false，则看startFromFailed的值是true还是false
        example: false
        type: boolean
      startFromFailed:
        description: 失败从失败处开始
        example: false
        type: boolean
      stellarisComponent:
        allOf:
        - $ref: '#/definitions/cluster.CreateStellarisComponentRequest'
        description: stellaris component 信息
    required:
    - clusterName
    - hubClusterIngressAddress
    - kubernetesCRIVersion
    - kubernetesVersion
    type: object
  cluster.CreateResponse:
    properties:
      clusterName:
        description: 集群名称
        example: cluster-create-test-name
        type: string
      cri:
        type: string
      description:
        description: 描述
        example: 这是创建集群的描述信息
        type: string
      hubClusterIngressAddress:
        description: 管理集群ingress 访问地址
        example: ***********
        type: string
      kubernetesCRIVersion:
        description: k8s CRI信息
        example: Docker-v19.03.15
        type: string
      kubernetesVersion:
        description: k8s 版本信息
        example: 1.21.5
        type: string
      labels:
        additionalProperties:
          type: string
        description: 标签
        type: object
      networkConfigs:
        allOf:
        - $ref: '#/definitions/cluster.CreateNetworkConfigResponse'
        description: 网络配置
      nodeConfigs:
        allOf:
        - $ref: '#/definitions/cluster.CreateNodeConfigInfoResponse'
        description: 节点配置
      registry:
        allOf:
        - $ref: '#/definitions/cluster.CreateRegistryConfigResponse'
        description: 制品服务信息
      registryType:
        description: 表示镜像仓库的类型 可选值 default、custom
        example:
        - default
        - custom
        items:
          type: string
        type: array
      stellarisComponent:
        allOf:
        - $ref: '#/definitions/cluster.CreateStellarisComponentResponse'
        description: stellaris component 信息
    type: object
  cluster.CreateStatusResponse:
    properties:
      CNITypes:
        items:
          type: string
        type: array
      arch:
        type: string
      clusterName:
        description: 集群名称
        example: cluster-create-test-name
        type: string
      cri:
        type: string
      hasAutoStorageNode:
        description: 是否存在自动挂载磁盘的节点
        example: true
        type: boolean
      processing:
        items:
          $ref: '#/definitions/cluster.CreateProcessResponse'
        type: array
      status:
        description: 表示集群状态
        example: preflighting
        type: string
      type:
        description: 节点配置类型 支持值  AllInOne(All-in-One) | MinimizeHA(最小化高可用) | StandardNoneHA(标准非高可用)
          |  StandardHA(标准高可用) 由前端同学进行控制
        example: AllInOne
        type: string
    required:
    - type
    type: object
  cluster.CreateStellarisComponentRequest:
    properties:
      address:
        description: 地址
        example: *********
        type: string
      port:
        description: 端口
        example: 8686
        type: integer
      standbyAddress:
        description: 备 - 地址
        example: ********1
        type: string
      standbyPort:
        description: 备 - 端口
        example: 8686
        type: integer
    required:
    - address
    - port
    type: object
  cluster.CreateStellarisComponentResponse:
    properties:
      address:
        description: 地址
        example: *********
        type: string
      port:
        description: 端口
        example: 8686
        type: integer
      standbyAddress:
        description: 备 - 地址
        example: ********1
        type: string
      standbyPort:
        description: 备 - 端口
        example: 8686
        type: integer
    type: object
  cluster.ExistResponse:
    properties:
      exist:
        description: true 表示集群已存在 false 表示集群不存在
        example: true
        type: boolean
      message:
        description: 当Exist为true时的提示信息
        example: 集群:cluster-57 已纳管
        type: string
    type: object
  cluster.NodeAuthRequest:
    properties:
      authType:
        description: 认证类型 目前支持 username_password
        example: username_password
        type: string
      param:
        additionalProperties: true
        description: 认证类型对应的参数 {"username":"用户名","password":"密码","authorizationPassword":"提权密码"}
        type: object
    required:
    - param
    type: object
  cluster.NodeAuthResponse:
    properties:
      authType:
        description: 认证类型 目前支持 username_password
        example: username_password
        type: string
      param:
        additionalProperties: true
        description: 认证类型对应的参数 {"username":"用户名","password":"密码","authorizationPassword":"提权密码"}
        type: object
    type: object
  cluster.NodeDiskPathRequest:
    properties:
      docker:
        description: 表示Docker挂载盘位置
        type: string
      etcd:
        description: 表示ETCD挂载盘位置
        type: string
      kubelet:
        description: 表示Kubelet挂载盘位置
        type: string
      system:
        description: 表示System挂载盘位置
        type: string
    type: object
  cluster.NodeDiskPathResponse:
    properties:
      docker:
        description: 表示Docker挂载盘位置
        type: string
      etcd:
        description: todo 实现filePathBinding，若部位空 则进行校验
        type: string
      kubelet:
        description: 表示Kubelet挂载盘位置
        type: string
      system:
        description: 表示System挂载盘位置
        type: string
    type: object
  cluster.NodeStorageRequest:
    properties:
      diskPath:
        allOf:
        - $ref: '#/definitions/cluster.NodeDiskPathRequest'
        description: 若节点存储类型为 manual 时填写，为etcd、docker、kubelet、system 的表单
      type:
        description: 节点存储类型 支持 auto、manual
        example: auto
        type: string
    type: object
  cluster.NodeStorageResponse:
    properties:
      diskPath:
        allOf:
        - $ref: '#/definitions/cluster.NodeDiskPathResponse'
        description: 若节点存储类型为 manual 时填写，为etcd、docker、kubelet、system 的表单
      type:
        description: 节点存储类型 支持 auto、manual
        example: auto
        type: string
    type: object
  cluster.Response:
    properties:
      CNITypes:
        items:
          type: string
        type: array
      apiServerStatus:
        description: api-server 联通性状态 success(成功),fail(失败),initialize(探测中)
        example: success
        type: string
      baseline:
        description: 创建集群基线版本
        type: string
      clusterName:
        description: 集群名称
        example: cluster-57
        type: string
      controlState:
        description: 集群状态 controlled(已纳管)、un-controlled(未纳管)
        example: controlled
        type: string
      createTime:
        type: string
      cri:
        type: string
      description:
        description: 集群描述
        example: 这是集群的描述信息
        type: string
      hasAutoStorageNode:
        description: 是否存在自动挂载磁盘的节点
        example: true
        type: boolean
      k8sVersion:
        description: k8s版本
        example: v1.21.5-hc.1
        type: string
      labels:
        description: 标签信息 格式 key=value,key1=value1....
        example: unified-platform.harmonycloud.cn/api-server-status=success,test=test
        type: string
      networkType:
        description: 集群网络插件
        example:
        - Calico-v3.23.5
        items:
          type: string
        type: array
      nodeCount:
        description: 节点数量
        example: 5
        type: integer
      prole:
        description: 集群平台角色
        items:
          type: string
        type: array
      status:
        description: 集群所处生命周期状态 preflighting(预检中)、preflightFailed(预检失败)、installing(创建中)、installFailed(创建失败)、online(在线)、offline(离线)、initializing(初始化中)
        example: online
        type: string
      structure:
        description: 集群架构
        example:
        - AMD64
        items:
          type: string
        type: array
      type:
        description: 节点配置类型 支持值  AllInOne(All-in-One) | MinimizeHA(最小化高可用) | StandardNoneHA(标准非高可用)
          |  StandardHA(标准高可用) 由前端同学进行控制
        example: AllInOne
        type: string
    type: object
  config.Response:
    properties:
      key:
        example: kubernetes_versions
        type: string
      values:
        items:
          $ref: '#/definitions/config.ValueResponse'
        type: array
    type: object
  config.SugarResponse:
    properties:
      info: {}
      key:
        example: kubernetes_versions
        type: string
    type: object
  config.ValueResponse:
    properties:
      code:
        example: 1.21.5
        type: string
      value:
        example: 1.21.5
        type: string
    type: object
  console.ConsoleSessionRequest:
    description: 控制台会话请求参数
    properties:
      mode:
        description: 模式，支持debug和ssh
        example: debug
        type: string
      nodeName:
        description: 节点名称
        example: node-1
        type: string
      privileged:
        description: 是否特权模式（debug模式）
        example: true
        type: boolean
      shell:
        description: Shell类型（debug模式）
        example: /bin/sh
        type: string
      sshKeySecret:
        description: SSH密钥secret名称（ssh模式）
        example: ssh-private-key
        type: string
      sshPassword:
        description: 新增
        example: password
        type: string
      sshPort:
        description: SSH端口（ssh模式）
        example: "22"
        type: string
      sshUser:
        description: SSH用户（ssh模式）
        example: root
        type: string
    required:
    - nodeName
    type: object
  console.ConsoleSessionResponse:
    description: 控制台会话响应数据
    properties:
      mode:
        description: 会话模式
        example: debug
        type: string
      nodeName:
        description: 节点名称
        example: node-1
        type: string
      podName:
        description: Pod名称
        example: node-debugger-node-1-abc12
        type: string
      status:
        description: Pod状态
        example: Running
        type: string
    type: object
  console.SessionStatusResponse:
    description: 会话状态响应数据
    properties:
      mode:
        description: 会话模式
        example: debug
        type: string
      nodeName:
        description: 节点名称
        example: node-1
        type: string
      podName:
        description: Pod名称
        example: node-debugger-node-1-abc12
        type: string
      startTime:
        description: 开始时间
        example: "2025-06-18T00:00:00Z"
        type: string
      status:
        description: Pod状态
        example: Running
        type: string
    type: object
  constants.AddonStatusEnum:
    enum:
    - 0
    - 1
    - 2
    - 3
    type: integer
    x-enum-comments:
      ERROR: 错误
      ON_SWITCH: 启用中
      RUNNING: 运行中
      UN_SWITCH: 未启用
    x-enum-varnames:
    - ERROR
    - UN_SWITCH
    - ON_SWITCH
    - RUNNING
  models.CloudComponentCondition:
    properties:
      name:
        description: 云组件名称
        type: string
      status:
        description: 云组件状态 Running  Unhealthy  Exception
        type: string
    type: object
  models.CloudComponentWorkload:
    properties:
      createTime:
        description: 创建时间
        type: string
      name:
        description: 工作负载 名称
        type: string
      namespace:
        description: 工作负载 所在分区
        type: string
      state:
        allOf:
        - $ref: '#/definitions/models.workloadState'
        description: 工作负载状态  rollingupdate starting started stopping stopped succeed
          failed unknown
      workloadType:
        description: 工作负载类型  CronJob DaemonSet Deployment Job Statefulset
        type: string
    type: object
  models.CloudComponentWorkloadCondition:
    properties:
      message:
        description: 详情信息
        type: string
      reason:
        description: 原因
        type: string
      status:
        description: 状态 False True UnKnow
        type: string
      workloads:
        description: 工作负载信息
        items:
          $ref: '#/definitions/models.CloudComponentWorkload'
        type: array
    type: object
  models.CloudComponentWorkloadPodGrouper:
    properties:
      createTime:
        description: 创建时间
        type: string
      name:
        description: 工作负载 名称
        type: string
      namespace:
        description: 工作负载 所在分区
        type: string
      pods:
        description: 容器组列表
        items:
          $ref: '#/definitions/models.CloudComponentWorkloadPodInstance'
        type: array
      state:
        allOf:
        - $ref: '#/definitions/models.workloadState'
        description: 工作负载状态  rollingupdate starting started stopping stopped succeed
          failed unknown
      workloadType:
        description: 工作负载类型  CronJob DaemonSet Deployment Job Statefulset
        type: string
    type: object
  models.CloudComponentWorkloadPodInstance:
    properties:
      containers:
        description: 容器列表
        items:
          $ref: '#/definitions/models.CloudComponentWorkloadPodInstanceContainer'
        type: array
      createTime:
        description: 创建时间
        type: string
      initContainers:
        description: 初始化容器列表
        items:
          $ref: '#/definitions/models.CloudComponentWorkloadPodInstanceContainer'
        type: array
      name:
        description: Pod 名称
        type: string
      namespace:
        description: Pod所在 命名空间
        type: string
      podIps:
        description: Pod IP列表
        items:
          $ref: '#/definitions/models.PodIP'
        type: array
      scheduleNode:
        description: Pod 所在节点
        type: string
      status:
        allOf:
        - $ref: '#/definitions/models.PodPhase'
        description: Pod 状态 Pending Running Succeeded Failed Unknown
    type: object
  models.CloudComponentWorkloadPodInstanceContainer:
    properties:
      logPaths:
        description: 容器日志路径列表
        items:
          $ref: '#/definitions/models.LogPath'
        type: array
      name:
        description: 容器名称
        type: string
    type: object
  models.CloudService:
    properties:
      cloudServiceName:
        description: 云服务名称
        type: string
      componentConditions:
        description: 云服务的云组件状态概要信息
        items:
          $ref: '#/definitions/models.CloudComponentCondition'
        type: array
      components:
        description: 云服务的云组件数量
        type: integer
      cpuLimit:
        description: 云服务CPU限制量
        type: number
      cpuRequest:
        description: 云服务CPU请求量
        type: number
      cpuUsage:
        description: 云服务CPU使用量
        type: number
      createTime:
        description: 云服务创建时间
        type: string
      description:
        description: 云服务详细信息
        type: string
      displayName:
        description: 云服务展示名称
        type: string
      exceptionCom:
        description: 云服务的故障云组件数量
        type: integer
      icon:
        allOf:
        - $ref: '#/definitions/models.IconInfo'
        description: 图标信息
      memoryLimit:
        description: 云服务内存限制量
        type: number
      memoryRequest:
        description: 云服务内存请求量
        type: number
      memoryUsage:
        description: 云服务内存使用量
        type: number
      pods:
        description: 云服务总Pod数量
        type: integer
      readypods:
        description: 云服务Ready 的 Pod数量
        type: integer
      status:
        description: 云服务状态 Running  Unhealthy Exception
        type: string
      version:
        description: 云服务版本
        type: string
    type: object
  models.CloudServiceComponent:
    properties:
      cloudComponentName:
        description: 云组件名称
        type: string
      cloudServiceName:
        description: 云组件归属的云服务名称
        type: string
      cluster:
        description: 集群名称
        type: string
      clusterPolicy:
        description: 云组件类型  managed work
        type: string
      cpuLimit:
        description: 云组件CPU限制量
        type: number
      cpuRequest:
        description: 云组件CPU请求量
        type: number
      cpuUsage:
        description: 云组件CPU使用量
        type: number
      createTime:
        description: 云组件创建时间
        type: string
      description:
        description: 云组件详细详细
        type: string
      displayName:
        description: 云组件展示名称
        type: string
      memoryLimit:
        description: 云组件内存限制量
        type: number
      memoryRequest:
        description: 云组件内存请求量
        type: number
      memoryUsage:
        description: 云组件内存使用量
        type: number
      pods:
        description: 云组件Pod总数
        type: integer
      readypods:
        description: 云组件Ready 的Pod总数
        type: integer
      status:
        description: 云组件状态  Running  Unhealthy Exception
        type: string
      unHealthWorkloadCondition:
        allOf:
        - $ref: '#/definitions/models.CloudComponentWorkloadCondition'
        description: 未健康的工作负载信息 Status 固定返回为False
      version:
        description: 云组件版本
        type: string
    type: object
  models.CloudServiceComponentSortByCluster:
    properties:
      cluster:
        description: 集群名称
        type: string
      workComponents:
        description: 集群下的云组件列表
        items:
          $ref: '#/definitions/models.CloudServiceComponent'
        type: array
    type: object
  models.IconInfo:
    properties:
      name:
        description: 图标Name
        type: string
    type: object
  models.LogPath:
    properties:
      name:
        description: Name volume name
        type: string
      path:
        description: Path mount path
        type: string
    type: object
  models.PageableResponse-baseline_CategoryItem:
    properties:
      items:
        items:
          $ref: '#/definitions/baseline.CategoryItem'
        type: array
      total:
        type: integer
    type: object
  models.PageableResponse-baseline_CheckItem:
    properties:
      items:
        items:
          $ref: '#/definitions/baseline.CheckItem'
        type: array
      total:
        type: integer
    type: object
  models.PageableResponse-baseline_CheckResultItem:
    properties:
      items:
        items:
          $ref: '#/definitions/baseline.CheckResultItem'
        type: array
      total:
        type: integer
    type: object
  models.PageableResponse-baseline_ClusterCheckResultItem:
    properties:
      items:
        items:
          $ref: '#/definitions/baseline.ClusterCheckResultItem'
        type: array
      total:
        type: integer
    type: object
  models.PageableResponse-baseline_StandardCheckIDItem:
    properties:
      items:
        items:
          $ref: '#/definitions/baseline.StandardCheckIDItem'
        type: array
      total:
        type: integer
    type: object
  models.PageableResponse-baseline_StandardCheckResultItem:
    properties:
      items:
        items:
          $ref: '#/definitions/baseline.StandardCheckResultItem'
        type: array
      total:
        type: integer
    type: object
  models.PageableResponse-baseline_StandardItem:
    properties:
      items:
        items:
          $ref: '#/definitions/baseline.StandardItem'
        type: array
      total:
        type: integer
    type: object
  models.PageableResponse-baseline_StrategyItem:
    properties:
      items:
        items:
          $ref: '#/definitions/baseline.StrategyItem'
        type: array
      total:
        type: integer
    type: object
  models.PodIP:
    properties:
      ip:
        description: IP is the IP address assigned to the pod
        type: string
    type: object
  models.PodPhase:
    enum:
    - Pending
    - Running
    - Succeeded
    - Failed
    - Unknown
    type: string
    x-enum-varnames:
    - PodPending
    - PodRunning
    - PodSucceeded
    - PodFailed
    - PodUnknown
  models.workloadState:
    enum:
    - rollingupdate
    - starting
    - started
    - stopping
    - stopped
    - succeed
    - failed
    - Running
    - unknown
    type: string
    x-enum-varnames:
    - WorkloadRollingUpdate
    - WorkloadStarting
    - WorkloadStarted
    - WorkloadStopping
    - WorkloadStopped
    - WorkloadSuccess
    - WorkloadFailed
    - WorkloadRunning
    - WorkloadUnknown
  monitoring.Dashboard:
    properties:
      configName:
        description: 配置名称
        type: string
      configNamespace:
        description: 配置命名空间
        type: string
      title:
        description: 标题
        type: string
      uid:
        description: 唯一ID
        type: string
      url:
        description: 监控大盘地址
        type: string
    type: object
  monitoring.ScopeLevel:
    enum:
    - cluster
    - node
    - workload
    - pod
    - cloudservice
    - cloudcomponent
    type: string
    x-enum-varnames:
    - LevelCluster
    - LevelNode
    - LevelWorkload
    - LevelPod
    - LevelCloudService
    - LevelComponent
  node.NodeRequest:
    properties:
      auth:
        allOf:
        - $ref: '#/definitions/cluster.NodeAuthRequest'
        description: 认证信息
      ip:
        description: 节点IP
        example: *********
        type: string
      port:
        description: 节点端口
        example: 22
        type: integer
    required:
    - auth
    - ip
    - port
    type: object
  node.NodeResponse:
    properties:
      auth:
        allOf:
        - $ref: '#/definitions/cluster.NodeAuthResponse'
        description: 认证信息
      ip:
        description: 节点IP
        example: *********
        type: string
      port:
        description: 节点端口
        example: 22
        type: integer
    required:
    - auth
    - ip
    - port
    type: object
  node.NodeUpDownBatchCreateRequest:
    properties:
      clusterBaselineVersion:
        description: 集群基线版本 前端无需填写，后台会自动进行赋值
        example: 1.1.0-baseline
        type: string
      clusterCri:
        type: string
      clusterName:
        description: 集群名称 前端无需填写，后台会自动进行赋值
        example: ***********
        type: string
      controlNode:
        allOf:
        - $ref: '#/definitions/node.NodeRequest'
        description: 表示主控节点的信息 无论节点上线｜下线都需要填写
      nodeConfigs:
        description: 节点上下线节点配置
        items:
          $ref: '#/definitions/node.NodeUpDownNodeConfigRequest'
        type: array
      registry:
        allOf:
        - $ref: '#/definitions/cluster.CreateRegistryConfigRequest'
        description: 节点上线时填写有效，表示自定义的制品服务地址
      timeServer:
        description: 节点时间同步服务器地址
        type: string
      type:
        description: 节点上显现任务类型
        example: nodeUp
        type: string
    type: object
  node.NodeUpDownCountDescription:
    properties:
      nodeDown:
        additionalProperties:
          type: integer
        description: key 表示节点上下线状态code,value 表示该状态对应数量
        type: object
      nodeDownTotal:
        description: 表示下线类型的任务运行数量
        example: 10
        type: integer
      nodeUp:
        additionalProperties:
          type: integer
        description: key 表示节点上下线状态code,value 表示该状态对应数量
        type: object
      nodeUpTotal:
        description: 表示上线类型的任务运行数量
        example: 10
        type: integer
    type: object
  node.NodeUpDownCountResponse:
    properties:
      description:
        allOf:
        - $ref: '#/definitions/node.NodeUpDownCountDescription'
        description: 任务数量详情
      total:
        description: 表示任务总计数量
        example: 10
        type: integer
    type: object
  node.NodeUpDownCreateRequest:
    properties:
      clusterBaselineVersion:
        description: 集群基线版本 前端无需填写，后台会自动进行赋值
        example: 1.1.0-baseline
        type: string
      clusterName:
        description: 集群名称 前端无需填写，后台会自动进行赋值
        example: ***********
        type: string
      controlNode:
        allOf:
        - $ref: '#/definitions/node.NodeRequest'
        description: 表示主控节点的信息 无论节点上线｜下线都需要填写
      cri:
        type: string
      nodeConfig:
        allOf:
        - $ref: '#/definitions/node.NodeUpDownNodeConfigRequest'
        description: 节点上下线节点配置
      registry:
        allOf:
        - $ref: '#/definitions/cluster.CreateRegistryConfigRequest'
        description: 节点上线时填写有效，表示自定义的制品服务地址
      reset:
        description: 重置
        example: false
        type: boolean
      startFromFailed:
        description: 失败从失败处开始
        example: false
        type: boolean
      timeServer:
        description: 节点时间同步服务器地址
        type: string
      type:
        description: 节点上显现任务类型
        example: nodeUp
        type: string
    type: object
  node.NodeUpDownCreateResponse:
    properties:
      controlNode:
        allOf:
        - $ref: '#/definitions/node.NodeResponse'
        description: 表示主控节点的信息
      cri:
        type: string
      nodeConfig:
        allOf:
        - $ref: '#/definitions/node.NodeUpDownNodeConfigResponse'
        description: 节点上下线节点配置
      registry:
        allOf:
        - $ref: '#/definitions/cluster.CreateRegistryConfigResponse'
        description: 节点上线时填写有效，表示自定义的制品服务地址
      registryType:
        description: 表示镜像仓库的类型 可选值 default、custom
        example:
        - default
        - custom
        items:
          type: string
        type: array
      timeServer:
        description: 节点时间同步服务器地址
        type: string
      type:
        description: 节点上显现任务类型
        example: nodeUp
        type: string
    type: object
  node.NodeUpDownNodeConfigRequest:
    properties:
      auth:
        allOf:
        - $ref: '#/definitions/cluster.NodeAuthRequest'
        description: 认证信息
      diskPath:
        allOf:
        - $ref: '#/definitions/cluster.NodeDiskPathRequest'
        description: 若节点存储类型为 manual 时填写，为etcd、docker、kubelet、system 的表单
      ip:
        description: 节点IP
        example: *********
        type: string
      port:
        description: 节点端口
        example: 22
        type: integer
      supportGpu:
        description: GPU 仅当节点上线时有
        example: false
        type: boolean
      type:
        description: 节点存储类型 支持 auto、manual
        example: auto
        type: string
    required:
    - auth
    - ip
    - port
    type: object
  node.NodeUpDownNodeConfigResponse:
    properties:
      auth:
        allOf:
        - $ref: '#/definitions/cluster.NodeAuthResponse'
        description: 认证信息
      diskPath:
        allOf:
        - $ref: '#/definitions/cluster.NodeDiskPathResponse'
        description: 若节点存储类型为 manual 时填写，为etcd、docker、kubelet、system 的表单
      ip:
        description: 节点IP
        example: *********
        type: string
      port:
        description: 节点端口
        example: 22
        type: integer
      supportGpu:
        description: GPU 仅当节点上线时有
        example: false
        type: boolean
      type:
        description: 节点存储类型 支持 auto、manual
        example: auto
        type: string
    required:
    - auth
    - ip
    - port
    type: object
  node.NodeUpDownResponse:
    properties:
      clusterName:
        description: 集群名称
        example: test-cluster
        type: string
      createTime:
        description: 表示创建时间
        example: "2024-06-12 15:15:15"
        type: string
      cri:
        type: string
      hasAutoStorageNode:
        description: 是否存在自动挂载磁盘的节点
        example: true
        type: boolean
      ip:
        description: 节点IP
        example: *********
        type: string
      percent:
        description: 最后计算 表示Step/Total
        example: 0.33
        type: number
      process:
        example: 1/3
        type: string
      status:
        description: 节点上下线任务状态
        example: createInitial
        type: string
      statusAlias:
        description: 节点上下线任务状态aliasName
        example: 上线成功
        type: string
      step:
        description: 表示当前执行到第几部了
        example: 1
        type: integer
      total:
        description: 表示总计步骤数
        example: 3
        type: integer
      type:
        description: 节点上显现任务类型
        example: nodeUp
        type: string
    type: object
  node.NodeUpDownStatusResponse:
    properties:
      clusterName:
        description: 集群名称
        example: test-cluster
        type: string
      cri:
        type: string
      hasAutoStorageNode:
        description: 是否存在自动挂载磁盘的节点
        example: true
        type: boolean
      ip:
        description: 节点IP
        example: *********
        type: string
      name:
        description: 节点上线后的节点名称
        example: skyview-cluster-node001
        type: string
      processing:
        description: 表示步骤
        items:
          $ref: '#/definitions/cluster.CreateProcessResponse'
        type: array
      status:
        description: 节点上下线任务状态
        example: createInitial
        type: string
      type:
        description: 节点上显现任务类型
        example: nodeUp
        type: string
    type: object
  node.NodeVerifyResponse:
    properties:
      message:
        description: 消息
        example: '[***********]:''Connect Refuse'''
        type: string
      success:
        description: 是否成功，若全部成功返回true，否则只要有一台失败则返回false
        example: false
        type: boolean
    type: object
  node.NodeVeriryRequest:
    properties:
      host:
        description: 节点IP
        example: ***********
        type: string
      password:
        description: 密码
        example: Ab123456
        type: string
      post:
        description: 节点端口
        example: 22
        type: integer
      sudoPassword:
        description: 提权密码
        example: Ab123456
        type: string
      username:
        description: 账号
        example: root
        type: string
    required:
    - host
    - password
    - post
    - username
    type: object
  node.SimpleNodeResponse:
    properties:
      ip:
        description: 节点IP
        example: ***********
        type: string
      name:
        description: 节点名称
        example: cluster-57-node1
        type: string
      status:
        allOf:
        - $ref: '#/definitions/v1.NodePhase'
        description: 节点状态
        example: Pending
    type: object
  utils.Response:
    properties:
      code:
        description: 返回编码
        type: integer
      count:
        description: 分页总页数
        type: integer
      data:
        description: 成功数据
      errorDetail:
        description: 错误详情
        type: string
      errorMsg:
        description: 错误信息
        type: string
      success:
        description: 是否成功
        type: boolean
    type: object
  v1.JSON:
    type: object
  v1.NodePhase:
    enum:
    - Pending
    - Running
    - Terminated
    type: string
    x-enum-varnames:
    - NodePending
    - NodeRunning
    - NodeTerminated
  v1alpha1.AddonConfigurationData:
    properties:
      cueRenderRule:
        type: string
      key:
        type: string
      value:
        $ref: '#/definitions/v1.JSON'
      valuePath:
        type: string
    type: object
  v1alpha1.AddonConfigurationSchema:
    properties:
      data:
        items:
          $ref: '#/definitions/v1alpha1.AddonConfigurationData'
        type: array
      file:
        $ref: '#/definitions/v1alpha1.FileSchema'
      name:
        type: string
      type:
        $ref: '#/definitions/v1alpha1.AddonSchemaType'
      unstructured:
        $ref: '#/definitions/v1alpha1.UnstructuredSchema'
      unstructuredList:
        $ref: '#/definitions/v1alpha1.UnstructuredListSchema'
    type: object
  v1alpha1.AddonSchemaType:
    enum:
    - file
    - unstructured
    - unstructuredList
    type: string
    x-enum-varnames:
    - FileAddonSchema
    - UnstructuredAddonSchema
    - UnstructuredListAddonSchema
  v1alpha1.FileConfigMap:
    properties:
      key:
        type: string
      name:
        type: string
      namespace:
        type: string
    type: object
  v1alpha1.FileSchema:
    properties:
      configmap:
        $ref: '#/definitions/v1alpha1.FileConfigMap'
      type:
        type: string
    type: object
  v1alpha1.UnstructuredListSchema:
    properties:
      group:
        type: string
      kind:
        type: string
      labels:
        additionalProperties:
          type: string
        type: object
      namespace:
        type: string
      version:
        type: string
    type: object
  v1alpha1.UnstructuredSchema:
    properties:
      group:
        type: string
      kind:
        type: string
      name:
        type: string
      namespace:
        type: string
      version:
        type: string
    type: object
  velero.Backups:
    properties:
      backupsName:
        description: 备份文件名称
        type: string
      createTime:
        description: 开始时间
        type: string
      executeType:
        description: 执行类型
        type: string
      finishTime:
        description: 完成时间
        type: string
      namespaces:
        description: 备份命名空间
        items:
          type: string
        type: array
      notes:
        description: 备注
        type: string
      ownerSchedulesName:
        description: 备份策略名称
        type: string
      persistentVolumeSize:
        description: 存储卷数
        type: integer
      resourcesSize:
        description: 资源数
        type: integer
      status:
        description: 状态
        type: string
    type: object
  velero.Content:
    properties:
      path:
        type: string
      type:
        type: string
      value:
        type: string
    type: object
  velero.Cron:
    properties:
      Days:
        description: 触发时间(日)
        items:
          type: string
        type: array
      Hours:
        description: 触发时间(时)
        items:
          type: string
        type: array
      Minutes:
        description: 触发时间(分)
        items:
          type: string
        type: array
      Weeks:
        description: 触发时间(周)
        items:
          type: string
        type: array
      cronString:
        description: cron表达式字符串
        type: string
      cycleType:
        description: 备份周期类型[month,week,date,hour,custom]
        type: string
    type: object
  velero.Modify:
    properties:
      content:
        items:
          $ref: '#/definitions/velero.Content'
        type: array
      namespaces:
        items:
          $ref: '#/definitions/velero.Namespace'
        type: array
      resourceType:
        type: string
    type: object
  velero.Namespace:
    properties:
      name:
        type: string
      resourceName:
        items:
          type: string
        type: array
    type: object
  velero.Restore:
    properties:
      backupsName:
        description: 备份文件名称
        type: string
      createTime:
        description: 开始时间
        type: string
      description:
        description: 描述
        type: string
      excludeResources:
        additionalProperties:
          type: string
        description: 排除资源标签
        type: object
      finishTime:
        description: 完成时间
        type: string
      includeResources:
        additionalProperties:
          type: string
        description: 指定资源标签
        type: object
      namespaces:
        description: 恢复命名空间
        items:
          type: string
        type: array
      notes:
        description: 备注
        type: string
      persistentVolumeSize:
        description: 存储卷数
        type: integer
      resourcesSize:
        description: 资源数
        type: integer
      restoreName:
        description: 恢复记录名称
        type: string
      restoreTemplateId:
        description: 恢复策略id
        type: string
      restoreTemplateName:
        description: 恢复策略名称
        type: string
      status:
        description: 状态
        type: string
      targetCluster:
        description: 集群名称
        type: string
    type: object
  velero.RestoreTemplate:
    properties:
      backupsName:
        description: 备份文件名称
        type: string
      createTime:
        description: 创建时间
        type: string
      description:
        description: 描述
        type: string
      excludeResources:
        additionalProperties:
          type: string
        description: 排除资源标签
        type: object
      fromCluster:
        type: string
      includeResources:
        additionalProperties:
          type: string
        description: 指定资源标签
        type: object
      isModify:
        description: 修改资源
        type: boolean
      modify:
        items:
          $ref: '#/definitions/velero.Modify'
        type: array
      namespaces:
        description: 恢复命名空间
        items:
          type: string
        type: array
      restoreTemplateId:
        description: 恢复策略id
        type: string
      restoreTemplateName:
        description: 恢复策略名称
        type: string
      restoreType:
        type: integer
      scheduleName:
        type: string
      status:
        description: 状态
        type: string
      storageServers:
        allOf:
        - $ref: '#/definitions/velero.StorageServers'
        description: 备份服务器信息
      storageServersInfo:
        description: 备份服务器异常信息
        type: string
      storageServersStatus:
        allOf:
        - $ref: '#/definitions/velero.StorageServersStatus'
        description: 备份服务器状态
      targetCluster:
        type: string
    type: object
  velero.S3StorageServersConfig:
    properties:
      backupStorageLocationName:
        description: bsl名称
        type: string
      bucket:
        description: 存储桶
        type: string
      password:
        description: 密码
        type: string
      url:
        allOf:
        - $ref: '#/definitions/velero.ServersUrl'
        description: 访问地址
      username:
        description: 账户名
        type: string
    type: object
  velero.Schedules:
    properties:
      allNamespaces:
        description: 是否备份集群所有命名空间
        type: boolean
      backupStorageLocationName:
        description: bsl名称
        type: string
      clusterName:
        description: 集群名称
        type: string
      createTime:
        description: 创建时间
        type: string
      cron:
        allOf:
        - $ref: '#/definitions/velero.Cron'
        description: 备份周期
      defaultVolumesToFsBackup:
        description: 是否备份pv到FSB
        type: boolean
      description:
        description: 描述
        type: string
      excludeResources:
        additionalProperties:
          type: string
        description: 排除资源标签
        type: object
      execute:
        description: 立即执行
        type: boolean
      includeResources:
        additionalProperties:
          type: string
        description: 指定资源标签
        type: object
      namespaces:
        description: 备份命名空间
        items:
          type: string
        type: array
      schedulesName:
        description: 备份策略名称
        type: string
      status:
        description: 备份状态
        type: string
      storageServers:
        allOf:
        - $ref: '#/definitions/velero.StorageServers'
        description: 备份服务器信息
      storageServersInfo:
        description: 备份服务器异常信息
        type: string
      storageServersStatus:
        allOf:
        - $ref: '#/definitions/velero.StorageServersStatus'
        description: 备份服务器状态
      ttl:
        description: etcd数据留存时长(天)
        type: integer
      type:
        allOf:
        - $ref: '#/definitions/velero.SchedulesType'
        description: 备份类型 time和handler
    type: object
  velero.SchedulesItem:
    properties:
      clusterName:
        description: 集群名称
        type: string
      createTime:
        description: 创建时间
        type: string
      description:
        description: 描述
        type: string
      schedulesName:
        description: 备份策略名称
        type: string
      status:
        description: 备份状态
        type: string
      type:
        description: 备份类型
        type: string
    type: object
  velero.SchedulesType:
    enum:
    - time
    - handle
    type: string
    x-enum-varnames:
    - Time
    - Handle
  velero.ServersUrl:
    properties:
      ip:
        description: ip地址
        type: string
      port:
        description: 端口
        type: integer
      protocol:
        description: 访问协议
        type: string
    type: object
  velero.StorageServers:
    properties:
      backupServerId:
        type: string
      backupServerName:
        type: string
      bucket:
        type: string
      bucketQuota:
        $ref: '#/definitions/backupserver.BucketQuota'
      bucketUsage:
        type: integer
      clusters:
        items:
          type: string
        type: array
      createdAt:
        type: string
      description:
        type: string
      nickname:
        description: 显示名称
        type: string
      s3StorageServersConfig:
        allOf:
        - $ref: '#/definitions/velero.S3StorageServersConfig'
        description: S3类型存储服务器配置
      storageServersId:
        description: 备份仓库id
        type: string
      updatedAt:
        type: string
    type: object
  velero.StorageServersStatus:
    enum:
    - Normal
    - SyncError
    - ConnectError
    type: string
    x-enum-varnames:
    - Normal
    - SyncError
    - ConnectError
  velero.VeleroResources:
    properties:
      namespace:
        description: 命名空间
        type: string
      resourceName:
        description: 资源名称
        type: string
      resourceType:
        description: 资源类型
        type: string
    type: object
  velero.VeleroVolumes:
    properties:
      namespace:
        description: 命名空间
        type: string
      persistentVolumeClaimName:
        description: 存储卷声明名称
        type: string
      podName:
        description: 所属pod名称
        type: string
      volumeName:
        description: 挂载卷名称
        type: string
    type: object
  workspace.Icon:
    properties:
      name:
        description: Name icon name
        type: string
      url:
        description: URL icon url
        type: string
    type: object
  workspace.Resource:
    properties:
      icon:
        $ref: '#/definitions/workspace.Icon'
      id:
        type: integer
      resourceAlias:
        type: string
      resourceName:
        type: string
      resourceStateList:
        items:
          $ref: '#/definitions/workspace.ResourceState'
        type: array
      resourceTotalCount:
        type: integer
    type: object
  workspace.ResourceState:
    properties:
      count:
        type: integer
      resourceStateAlias:
        type: string
      resourceStateColor:
        type: string
      resourceStateName:
        type: string
      tips:
        type: string
    type: object
info:
  contact: {}
  title: Olympus Portal API
  version: 1.0.0
paths:
  /apis/v1/baselines/checkers:
    delete:
      consumes:
      - application/json
      description: 根据检查项 ID 列表删除多个自定义检查项
      parameters:
      - description: 检查项 ID 列表
        in: body
        name: checkers
        required: true
        schema:
          $ref: '#/definitions/baseline.DeleteCustomCheckerRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，删除成功
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                success:
                  type: boolean
              type: object
        "401":
          description: 权限不足，返回401错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 删除多个自定义检查项
      tags:
      - 自定义检查项
    get:
      consumes:
      - application/x-www-form-urlencoded
      description: 支持分页，过滤查询
      parameters:
      - description: 资源类型，例如 Kubernetes, Docker, Host
        in: query
        name: resourceType
        type: string
      - description: 风险级别，例如 High, Medium, Low
        in: query
        name: riskLevel
        type: string
      - description: 检查方式，例如 Command, FileYAML, FileJSON
        in: query
        name: checkMode
        type: string
      - default: false
        description: 是否为内置检查项，true 或 false
        in: query
        name: builtin
        required: true
        type: boolean
      - description: 页码, 默认为1
        in: query
        name: page_num
        type: integer
      - description: 每页大小, 默认为10
        in: query
        name: page_size
        type: integer
      - description: 排序方式 asc 或 desc，默认 desc
        in: query
        name: sort_order
        type: string
      - description: 排序字段比较方式，例如 time, string, number
        in: query
        name: sort_func
        type: string
      - description: 排序字段，通过类似 JSONPath 的方式获取, 默认为 createTime
        in: query
        name: sort_name
        type: string
      - description: 符合查询参数的选择器。例如：精确查询 selector=name=a,namespace=default；模糊查询 selector=name~a,namespace~default；组合查询
          selector=name~a,namespace=default
        in: query
        name: selector
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功返回数据
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/models.PageableResponse-baseline_CheckItem'
                success:
                  type: boolean
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "401":
          description: 权限错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 获取自定义检查项
      tags:
      - 自定义检查项
    post:
      consumes:
      - application/json
      description: 创建一个新的自定义检查项，包含名称、风险级别和检查模式等配置
      parameters:
      - description: 新建检查项
        in: body
        name: checker
        required: true
        schema:
          $ref: '#/definitions/baseline.CreateCustomCheckerRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回新创建的自定义检查项的响应
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.CreateCustomCheckerResponse'
                success:
                  type: boolean
              type: object
        "401":
          description: 权限错误，返回401错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 创建自定义检查项
      tags:
      - 自定义检查项
  /apis/v1/baselines/checkers/{id}:
    delete:
      description: 根据检查项 ID 删除指定的自定义检查项
      parameters:
      - description: 检查项 ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，删除成功
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                success:
                  type: boolean
              type: object
        "401":
          description: 权限不足，返回401错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 删除单个自定义检查项
      tags:
      - 自定义检查项
    get:
      consumes:
      - application/x-www-form-urlencoded
      description: Retrieves details of a custom checker by ID.
      parameters:
      - description: Checker ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.GetCustomCheckerDetailsResponse'
                success:
                  type: boolean
              type: object
        "404":
          description: Not Found
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                message:
                  type: string
              type: object
      security:
      - jwt: []
      summary: 获取自定义检查项详情
      tags:
      - 自定义检查项
    put:
      consumes:
      - application/json
      description: 编辑已有的自定义检查项，使用 ID 指定要编辑的检查项
      parameters:
      - description: 检查项 ID
        in: path
        name: id
        required: true
        type: string
      - description: 更新后的检查项
        in: body
        name: checker
        required: true
        schema:
          $ref: '#/definitions/baseline.UpdateCustomCheckerRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回编辑后的自定义检查项响应
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.UpdateCustomCheckerResponse'
                success:
                  type: boolean
              type: object
        "401":
          description: 权限错误，返回401错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 编辑自定义检查项
      tags:
      - 自定义检查项
  /apis/v1/baselines/checkers/{id}/binding_standards:
    post:
      consumes:
      - application/json
      description: 更新自定义检查项关联的基线标准并删除
      parameters:
      - description: 检查项 ID
        in: path
        name: id
        required: true
        type: string
      - description: 检查项 ID 列表
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/baseline.UpdateCustomCheckerBindingStandardsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.UpdateCustomCheckerBindingStandardsResponse'
                success:
                  type: boolean
              type: object
        "401":
          description: 权限不足，返回401错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "404":
          description: 未找到指定的检查项，返回404错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 更新自定义检查项关联的基线标准
      tags:
      - 自定义检查项
  /apis/v1/baselines/checkers/{id}/standards:
    get:
      consumes:
      - application/x-www-form-urlencoded
      description: 查询与指定自定义检查项关联的基线标准
      parameters:
      - description: 检查项 ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回关联的基线标准
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.QueryAssociatedBaselinesResponse'
                success:
                  type: boolean
              type: object
        "401":
          description: 权限不足，返回401错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "404":
          description: 未找到指定的检查项，返回404错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 查询关联基线标准
      tags:
      - 自定义检查项
  /apis/v1/baselines/checkers/assign_standard_checkers:
    get:
      consumes:
      - application/x-www-form-urlencoded
      description: 获取分配给标准自定义检查项列表
      parameters:
      - description: 标准ID
        in: query
        name: standardId
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.GetAssignStandardCheckersResponse'
                success:
                  type: boolean
              type: object
        "404":
          description: Not Found
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                message:
                  type: string
              type: object
      security:
      - jwt: []
      summary: 获取分配给标准自定义检查项
      tags:
      - 自定义检查项
  /apis/v1/baselines/checkers/name_existed:
    get:
      consumes:
      - application/x-www-form-urlencoded
      description: 获取自定义检查项是否存在的结果
      parameters:
      - description: 检查项名称
        in: query
        name: name
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功返回数据
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.GetCustomCheckerNameExistedResponse'
                success:
                  type: boolean
              type: object
        "400":
          description: 请求参数错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "401":
          description: 权限错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 获取自定义检查项是否存在的结果
      tags:
      - 自定义检查项
  /apis/v1/baselines/checkers/upload_files:
    post:
      consumes:
      - multipart/form-data
      description: 上传检查项文件，返回对应id，名称，访问路径等
      parameters:
      - description: 上传的文件对象
        in: formData
        name: file
        required: true
        type: file
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回新创建的自定义检查项的响应
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.UploadCheckerFileResponse'
                success:
                  type: boolean
              type: object
        "401":
          description: 权限错误，返回401错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 上传检查项相关的文件
      tags:
      - 自定义检查项
  /apis/v1/baselines/standard_categories:
    get:
      consumes:
      - application/x-www-form-urlencoded
      description: 查询基线分类时，支持根据分类名称和是否内置进行筛选。
      parameters:
      - description: 基线标准分类名称
        in: query
        name: name
        type: string
      - description: 是否内置分类
        in: query
        name: builtin
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.PageableResponse-baseline_CategoryItem'
              type: object
        "400":
          description: Bad Request
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                errorMsg:
                  type: string
              type: object
        "404":
          description: Not Found
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                errorMsg:
                  type: string
              type: object
      security:
      - jwt: []
      summary: 查询基线分类
      tags:
      - 基线标准分类
    post:
      consumes:
      - application/json
      description: 创建一个新的基线分类
      parameters:
      - description: 创建基线分类请求体
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/baseline.CreateBaselineCategoryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回新创建的基线分类
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.CreateBaselineCategoryResponse'
                success:
                  type: boolean
              type: object
        "400":
          description: 请求参数错误，返回400错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 创建基线分类
      tags:
      - 基线标准分类
  /apis/v1/baselines/standard_categories/{id}:
    delete:
      consumes:
      - application/x-www-form-urlencoded
      description: 根据分类 ID 删除基线分类
      parameters:
      - description: 分类 ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回删除的基线分类信息
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.DeleteBaselineCategoryResponse'
                success:
                  type: boolean
              type: object
        "400":
          description: 请求参数错误，返回400错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "404":
          description: 未找到基线分类，返回404错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 删除基线分类
      tags:
      - 基线标准分类
    put:
      consumes:
      - application/json
      description: 根据分类 ID 编辑基线分类
      parameters:
      - description: 分类 ID
        in: path
        name: id
        required: true
        type: string
      - description: 编辑基线分类请求体
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/baseline.EditBaselineCategoryRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回编辑后的基线分类信息
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.EditBaselineCategoryResponse'
                success:
                  type: boolean
              type: object
        "400":
          description: 请求参数错误，返回400错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "404":
          description: 未找到基线分类，返回404错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 编辑基线分类
      tags:
      - 基线标准分类
  /apis/v1/baselines/standard_categories/name_existed:
    get:
      consumes:
      - application/x-www-form-urlencoded
      description: 查询基线分类名称是否存在
      parameters:
      - description: 基线分类名称
        in: query
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.GetCategoryNameExistedResponse'
                success:
                  type: boolean
              type: object
        "400":
          description: Bad Request
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: Internal Server Error
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 查询基线分类名称是否存在
      tags:
      - 基线标准分类
  /apis/v1/baselines/standard_categories/standards:
    get:
      consumes:
      - application/x-www-form-urlencoded
      description: 查询分类聚和的基线标准列表
      parameters:
      - default: true
        description: 是否查询内置基线标准
        in: query
        name: builtin
        type: boolean
      - description: 标准id， 如果传入返回各个基线标准所选择的id
        in: query
        name: standardId
        type: string
      - description: 是否是复制页面
        in: query
        name: isCopy
        type: boolean
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回基线标准列表
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.QueryCategoryStandardsResponse'
                success:
                  type: boolean
              type: object
        "400":
          description: 请求参数错误，返回400错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 查询分类聚和的基线标准
      tags:
      - 基线标准分类
  /apis/v1/baselines/standards:
    get:
      consumes:
      - application/x-www-form-urlencoded
      description: 根据名称、分类名称和分类是否内置，分页查询基线标准
      parameters:
      - description: 基线标准名称
        in: query
        name: name
        type: string
      - description: 分类ID,精确查询
        in: query
        name: categoryId
        type: integer
      - description: 分类名称
        in: query
        name: categoryName
        type: string
      - description: 分类是否内置
        in: query
        name: categoryBuiltin
        type: boolean
      - description: 是否内置
        in: query
        name: builtin
        type: boolean
      - description: 页码, 默认为1
        in: query
        name: page_num
        type: integer
      - description: 每页大小, 默认为10
        in: query
        name: page_size
        type: integer
      - description: 排序方式 asc 或 desc，默认 desc
        in: query
        name: sort_order
        type: string
      - description: 排序字段比较方式，例如 time, string, number
        in: query
        name: sort_func
        type: string
      - description: 排序字段，通过类似 JSONPath 的方式获取, 默认为 metadata.name
        in: query
        name: sort_name
        type: string
      - description: 符合查询参数的选择器。例如：精确查询 selector=name=a,namespace=default；模糊查询 selector=name~a,namespace~default；组合查询
          selector=name~a,namespace=default
        in: query
        name: selector
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回基线标准列表
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/models.PageableResponse-baseline_StandardItem'
                success:
                  type: boolean
              type: object
        "400":
          description: 请求参数错误，返回400错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 查询基线标准
      tags:
      - 基线标准
    post:
      consumes:
      - application/json
      description: 创建新的基线标准
      parameters:
      - description: 创建基线标准的请求体
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/baseline.CreateBaselineRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回新创建的基线标准信息
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.CreateBaselineResponse'
                success:
                  type: boolean
              type: object
        "400":
          description: 请求参数错误，返回400错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 创建基线标准
      tags:
      - 基线标准
  /apis/v1/baselines/standards/{id}:
    delete:
      consumes:
      - application/x-www-form-urlencoded
      description: 根据基线标准ID删除基线标准
      parameters:
      - description: 基线标准ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回删除结果
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.DeleteBaselineResponse'
                success:
                  type: boolean
              type: object
        "400":
          description: 请求参数错误，返回400错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "404":
          description: 未找到基线标准，返回404错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 删除基线标准
      tags:
      - 基线标准
    get:
      consumes:
      - application/x-www-form-urlencoded
      description: 根据基线标准ID获取详情
      parameters:
      - description: 基线标准ID
        in: path
        name: id
        required: true
        type: integer
      - description: 父ID
        in: query
        name: parentId
        type: integer
      - description: 风险级别
        in: query
        name: riskLevel
        type: string
      - description: 检查项名称
        in: query
        name: name
        type: string
      - description: 页码, 默认为1
        in: query
        name: page_num
        type: integer
      - description: 每页大小, 默认为10
        in: query
        name: page_size
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回基线标准详情
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.GetBaselineDetailsResponse'
                success:
                  type: boolean
              type: object
        "400":
          description: 请求参数错误，返回400错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "404":
          description: 未找到基线标准，返回404错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 查询基线标准详情
      tags:
      - 基线标准
    put:
      consumes:
      - application/json
      description: 根据基线标准ID更新基线标准信息
      parameters:
      - description: 基线标准ID
        in: path
        name: id
        required: true
        type: integer
      - description: 更新基线标准的请求体
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/baseline.UpdateBaselineRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回更新后的基线标准信息
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.UpdateBaselineResponse'
                success:
                  type: boolean
              type: object
        "400":
          description: 请求参数错误，返回400错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "404":
          description: 未找到基线标准，返回404错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 更新基线标准
      tags:
      - 基线标准
  /apis/v1/baselines/standards/{id}/binding_strategies:
    post:
      consumes:
      - application/json
      description: 更新绑定关系
      parameters:
      - description: 标准ID
        in: path
        name: id
        required: true
        type: integer
      - description: 更新基线标准的请求体
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/baseline.UpdateBindingStrategiesResponse'
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回策略列表
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.UpdateBindingStrategiesResponse'
                success:
                  type: boolean
              type: object
        "400":
          description: 请求参数错误，返回400错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 更新绑定关系
      tags:
      - 基线标准
  /apis/v1/baselines/standards/{id}/checkers:
    get:
      consumes:
      - application/x-www-form-urlencoded
      description: 根据基线标准ID获取详情
      parameters:
      - description: 基线标准ID
        in: path
        name: id
        required: true
        type: integer
      - description: 如果传空表示全部，传 1,2,3 以逗号分隔格式获取对应的1,2,3 三个检查项
        in: query
        name: checkIds
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回基线标准详情
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.GetBaselineCheckersResponse'
                success:
                  type: boolean
              type: object
        "400":
          description: 请求参数错误，返回400错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "404":
          description: 未找到基线标准，返回404错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 查询基线标准详情
      tags:
      - 基线标准
  /apis/v1/baselines/standards/{id}/strategies:
    get:
      consumes:
      - application/json
      description: 获取与指定标准ID相关联的策略列表
      parameters:
      - description: 标准ID
        in: path
        name: id
        required: true
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回策略列表
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.QueryAssociatedStrategiesResponse'
                success:
                  type: boolean
              type: object
        "400":
          description: 请求参数错误，返回400错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 查询关联的基线策略
      tags:
      - 基线标准
  /apis/v1/baselines/standards/name_existed:
    get:
      consumes:
      - application/x-www-form-urlencoded
      description: 获取基线标准名称是否已经存在
      parameters:
      - description: 基线标准名称
        in: query
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.GetBaselineNameExistedResponse'
                success:
                  type: boolean
              type: object
        "400":
          description: Bad Request
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: Internal Server Error
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 获取基线标准名称是否已经存在
      tags:
      - 基线标准
  /apis/v1/baselines/strategies:
    get:
      consumes:
      - application/x-www-form-urlencoded
      description: 根据名称、是否开启，分页查询基线策略
      parameters:
      - description: 基线策略
        in: query
        name: name
        type: string
      - description: 策略是否开启
        in: query
        name: enable
        type: boolean
      - description: 页码, 默认为1
        in: query
        name: page_num
        type: integer
      - description: 每页大小, 默认为10
        in: query
        name: page_size
        type: integer
      - description: 排序方式 asc 或 desc，默认 desc
        in: query
        name: sort_order
        type: string
      - description: 排序字段比较方式，例如 time, string, number
        in: query
        name: sort_func
        type: string
      - description: 排序字段，通过类似 JSONPath 的方式获取, 默认为 metadata.name
        in: query
        name: sort_name
        type: string
      - description: 符合查询参数的选择器。例如：精确查询 selector=name=a,namespace=default；模糊查询 selector=name~a,namespace~default；组合查询
          selector=name~a,namespace=default
        in: query
        name: selector
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回基线标准列表
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/models.PageableResponse-baseline_StrategyItem'
                success:
                  type: boolean
              type: object
        "400":
          description: 请求参数错误，返回400错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 查询基线策略
      tags:
      - 基线策略
    post:
      consumes:
      - application/json
      description: 提交数据以创建新的基线策略
      parameters:
      - description: 基线策略创建请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/baseline.CreateBaselineStrategyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回创建的策略信息
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.CreateBaselineStrategyResponse'
                success:
                  type: boolean
              type: object
        "400":
          description: 请求参数错误，返回400错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 创建新的基线策略
      tags:
      - 基线策略
  /apis/v1/baselines/strategies/{id}:
    delete:
      consumes:
      - application/x-www-form-urlencoded
      description: 删除特定ID的基线策略
      parameters:
      - description: 策略ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回删除结果
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.DeleteBaselineStrategyResponse'
                success:
                  type: boolean
              type: object
        "400":
          description: 请求参数错误，返回400错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 删除指定ID的基线策略
      tags:
      - 基线策略
    get:
      consumes:
      - application/x-www-form-urlencoded
      description: 获取指定ID的基线策略详细信息
      parameters:
      - description: 策略ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回基线策略详情
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.GetBaselineStrategyDetailsResponse'
                success:
                  type: boolean
              type: object
        "400":
          description: 请求参数错误，返回400错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 查询基线策略详情
      tags:
      - 基线策略
    put:
      consumes:
      - application/json
      description: 提交数据以更新指定ID的基线策略
      parameters:
      - description: 策略ID
        in: path
        name: id
        required: true
        type: string
      - description: 基线策略更新请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/baseline.UpdateBaselineStrategyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回更新后的策略信息
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.UpdateBaselineStrategyResponse'
                success:
                  type: boolean
              type: object
        "400":
          description: 请求参数错误，返回400错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 更新已有基线策略
      tags:
      - 基线策略
  /apis/v1/baselines/strategies/{id}/binding_standards:
    post:
      consumes:
      - application/json
      description: 更新基线策略绑定的基线标准
      parameters:
      - description: 基线策略更新请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/baseline.UpdateBindingBaselineStandardsRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回绑定结果
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.UpdateBindingBaselineStandardsResponse'
                success:
                  type: boolean
              type: object
        "400":
          description: 请求参数错误，返回400错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 更新基线策略绑定的基线标准
      tags:
      - 基线策略
  /apis/v1/baselines/strategies/{id}/check:
    post:
      consumes:
      - application/json
      description: 执行指定ID的基线策略检查任务
      parameters:
      - description: 策略ID
        in: path
        name: id
        required: true
        type: string
      - description: 执行检查任务请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/baseline.ExecuteCheckJobRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回检查任务执行结果
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.ExecuteCheckJobResponse'
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 执行基线策略检查任务
      tags:
      - 基线策略
  /apis/v1/baselines/strategies/{id}/clusters/{cluster}/standards/{std_id}/check_results:
    get:
      consumes:
      - application/x-www-form-urlencoded
      description: 获取特定ID的基线策略下单独检查项的检查结果
      parameters:
      - description: 策略ID
        in: path
        name: id
        required: true
        type: string
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: 标准ID
        in: path
        name: std_id
        required: true
        type: string
      - description: 集群名称
        in: query
        name: clusterName
        type: string
      - description: 检查项名称
        in: query
        name: checkName
        type: string
      - description: 页码, 默认为1
        in: query
        name: page_num
        type: integer
      - description: 每页大小, 默认为10
        in: query
        name: page_size
        type: integer
      - description: 排序方式 asc 或 desc，默认 desc
        in: query
        name: sort_order
        type: string
      - description: 排序字段比较方式，例如 time, string, number
        in: query
        name: sort_func
        type: string
      - description: 排序字段，通过类似 JSONPath 的方式获取, 默认为 metadata.name
        in: query
        name: sort_name
        type: string
      - description: 符合查询参数的选择器。例如：精确查询 selector=name=a,namespace=default；模糊查询 selector=name~a,namespace~default；组合查询
          selector=name~a,namespace=default
        in: query
        name: selector
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回单独检查项的检查结果
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/models.PageableResponse-baseline_CheckResultItem'
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 获取单独检查项的检查结果
      tags:
      - 基线策略
  /apis/v1/baselines/strategies/{id}/clusters/{cluster}/standards/{std_id}/check_results_report:
    get:
      consumes:
      - application/x-www-form-urlencoded
      description: 获取特定ID的基线策略下单独检查项的检查结果报告文件
      parameters:
      - description: 策略ID
        in: path
        name: id
        required: true
        type: string
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: 标准ID
        in: path
        name: std_id
        required: true
        type: string
      - description: 集群名称
        in: query
        name: clusterName
        type: string
      - description: 检查项名称
        in: query
        name: checkName
        type: string
      - description: 页码, 默认为1
        in: query
        name: page_num
        type: integer
      - description: 每页大小, 默认为10
        in: query
        name: page_size
        type: integer
      - description: 排序方式 asc 或 desc，默认 desc
        in: query
        name: sort_order
        type: string
      - description: 排序字段比较方式，例如 time, string, number
        in: query
        name: sort_func
        type: string
      - description: 排序字段，通过类似 JSONPath 的方式获取, 默认为 metadata.name
        in: query
        name: sort_name
        type: string
      - description: 符合查询参数的选择器。例如：精确查询 selector=name=a,namespace=default；模糊查询 selector=name~a,namespace~default；组合查询
          selector=name~a,namespace=default
        in: query
        name: selector
        type: string
      produces:
      - application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
      responses:
        "200":
          description: OK
          schema:
            type: file
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 获取单独检查项检查结果文件
      tags:
      - 基线策略
  /apis/v1/baselines/strategies/{id}/clusters/{cluster}/standards/check_results:
    get:
      consumes:
      - application/x-www-form-urlencoded
      description: 获取指定ID的基线策略下特定基线标准的检查结果
      parameters:
      - description: 策略ID
        in: path
        name: id
        required: true
        type: string
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: 基线标准名称
        in: query
        name: standardName
        type: string
      - description: 集群名称
        in: query
        name: clusterName
        type: string
      - description: 页码, 默认为1
        in: query
        name: page_num
        type: integer
      - description: 每页大小, 默认为10
        in: query
        name: page_size
        type: integer
      - description: 排序方式 asc 或 desc，默认 desc
        in: query
        name: sort_order
        type: string
      - description: 排序字段比较方式，例如 time, string, number
        in: query
        name: sort_func
        type: string
      - description: 排序字段，通过类似 JSONPath 的方式获取, 默认为 metadata.name
        in: query
        name: sort_name
        type: string
      - description: 符合查询参数的选择器。例如：精确查询 selector=name=a,namespace=default；模糊查询 selector=name~a,namespace~default；组合查询
          selector=name~a,namespace=default
        in: query
        name: selector
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回基线标准检查结果
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/models.PageableResponse-baseline_StandardCheckResultItem'
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 获取指定基线标准的检查结果
      tags:
      - 基线策略
  /apis/v1/baselines/strategies/{id}/clusters/{cluster}/standards/check_results_report:
    get:
      description: 获取指定ID的基线策略下特定基线标准的检查结果报告
      parameters:
      - description: 策略ID
        in: path
        name: id
        required: true
        type: string
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: 基线标准名称
        in: query
        name: standardName
        type: string
      - description: 集群名称
        in: query
        name: clusterName
        type: string
      - description: 页码, 默认为1
        in: query
        name: page_num
        type: integer
      - description: 每页大小, 默认为10
        in: query
        name: page_size
        type: integer
      - description: 排序方式 asc 或 desc，默认 desc
        in: query
        name: sort_order
        type: string
      - description: 排序字段比较方式，例如 time, string, number
        in: query
        name: sort_func
        type: string
      - description: 排序字段，通过类似 JSONPath 的方式获取, 默认为 metadata.name
        in: query
        name: sort_name
        type: string
      - description: 符合查询参数的选择器。例如：精确查询 selector=name=a,namespace=default；模糊查询 selector=name~a,namespace~default；组合查询
          selector=name~a,namespace=default
        in: query
        name: selector
        type: string
      produces:
      - application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: file
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 获取基线标准检查结果报告
      tags:
      - 基线策略
  /apis/v1/baselines/strategies/{id}/clusters/check_results:
    get:
      consumes:
      - application/x-www-form-urlencoded
      description: 获取指定ID的基线策略在指定集群上的检查结果
      parameters:
      - description: 策略ID
        in: path
        name: id
        required: true
        type: string
      - description: 集群名称
        in: query
        name: clusterName
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回集群检查结果
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/models.PageableResponse-baseline_ClusterCheckResultItem'
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 获取指定集群的检查结果
      tags:
      - 基线策略
  /apis/v1/baselines/strategies/{id}/last_job_status:
    get:
      consumes:
      - application/json
      description: 执行指定ID的基线策略最新的任务结果
      parameters:
      - description: 策略ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回检查任务执行结果
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.GetLastCheckJobStatusResponse'
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 获取最新的任务结果
      tags:
      - 基线策略
  /apis/v1/baselines/strategies/{id}/summary:
    get:
      consumes:
      - application/x-www-form-urlencoded
      description: 获取基线策略的总结或概览信息
      parameters:
      - description: 策略ID
        in: path
        name: id
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回基线策略概要
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.GetBaselineStrategySummaryResponse'
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 获取基线策略的概要信息
      tags:
      - 基线策略
  /apis/v1/baselines/strategies/{id}/switch:
    post:
      consumes:
      - application/json
      description: 启用或禁用基线策略
      parameters:
      - description: 策略ID
        in: path
        name: id
        required: true
        type: string
      - description: 启用或禁用请求
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/baseline.SwitchBaselineStrategyRequest'
      produces:
      - application/json
      responses:
        "200":
          description: 请求成功，返回策略切换结果
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.SwitchBaselineStrategyResponse'
                success:
                  type: boolean
              type: object
        "400":
          description: 请求参数错误，返回400错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: 服务器内部错误，返回500错误
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 启用或禁用指定ID的基线策略
      tags:
      - 基线策略
  /apis/v1/baselines/strategies/import_builtin_baseline:
    post:
      consumes:
      - application/x-www-form-urlencoded
      description: 从底座获取内置基线，并导入到数据库中
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                success:
                  type: boolean
              type: object
        "400":
          description: Bad Request
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: Internal Server Error
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 导入内置基线
      tags:
      - 定时任务
  /apis/v1/baselines/strategies/name_existed:
    get:
      consumes:
      - application/x-www-form-urlencoded
      description: 根据名称查询基线策略名称是否存在
      parameters:
      - description: 基线策略名称
        in: query
        name: name
        required: true
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.GetBaselineStrategyNameExistedResponse'
                success:
                  type: boolean
              type: object
        "400":
          description: Bad Request
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: Internal Server Error
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 查询基线策略名称是否存在
      tags:
      - 基线策略
  /apis/v1/baselines/strategies/recurring_jobs:
    get:
      consumes:
      - application/x-www-form-urlencoded
      description: 查询定时任务
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                data:
                  $ref: '#/definitions/baseline.ListRecurringJobResponse'
                success:
                  type: boolean
              type: object
        "400":
          description: Bad Request
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
        "500":
          description: Internal Server Error
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                code:
                  type: integer
                errorMsg:
                  type: string
                success:
                  type: boolean
              type: object
      security:
      - jwt: []
      summary: 查询定时任务
      tags:
      - 定时任务
  /apis/v1/cloudservices:
    get:
      description: 获取平台上安装的云服务列表信息
      parameters:
      - description: selector
        in: query
        name: selector
        type: string
      - description: filter page_size 页长
        in: query
        name: page_size
        type: string
      - description: filter page_num 页码
        in: query
        name: page_num
        type: string
      - description: sort_name
        in: query
        name: sort_name
        type: string
      - description: sort_order
        in: query
        name: sort_order
        type: string
      - description: sort_func
        in: query
        name: sort_func
        type: string
      - description: 标签选择器
        in: query
        name: label_selector
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 获取云服务列表
      tags:
      - 云服务
  /apis/v1/cloudservices/{cloudServiceName}:
    get:
      description: 查看平台云服务详情
      parameters:
      - description: 云服务名称
        in: path
        name: cloudServiceName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.CloudService'
              type: object
      summary: 获取云服务详情
      tags:
      - 云服务
  /apis/v1/cloudservices/{cloudServiceName}/components:
    get:
      description: 根据条件获取云服务的组件列表 该接口属于通用接口 获取管控面数据 query='componentType=managed';获取业务面数据
        query='componentType=work';日志查询时候 不传 query;
      parameters:
      - description: 云服务名称
        in: path
        name: cloudServiceName
        required: true
        type: string
      - description: 云组件类型
        in: query
        name: componentType
        type: string
      - description: 云组件所在集群
        in: query
        name: cluster
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.CloudServiceComponentSortByCluster'
              type: object
      summary: 获取云服务的组件列表
      tags:
      - 云组件
  /apis/v1/cloudservices/{cloudServiceName}/components/{componentName}:
    get:
      description: 根据云服务、云服务下的组件名称与集群，查看管控面或数据面的云组件详情
      parameters:
      - description: 云服务名称
        in: path
        name: cloudServiceName
        required: true
        type: string
      - description: 云组件名称
        in: path
        name: componentName
        required: true
        type: string
      - description: 集群
        in: query
        name: cluster
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/models.CloudServiceComponent'
              type: object
      summary: 查看云组件详情
      tags:
      - 云组件
  /apis/v1/cloudservices/{cloudServiceName}/components/{componentName}/podinstances:
    get:
      description: 获取云组件Workload 的 Pod 列表
      parameters:
      - description: 云服务名称
        in: path
        name: cloudServiceName
        required: true
        type: string
      - description: 云组件名称
        in: path
        name: componentName
        required: true
        type: string
      - description: 集群
        in: query
        name: cluster
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.CloudComponentWorkloadPodGrouper'
                  type: array
              type: object
      summary: 获取云组件Workload 的 Pod 列表
      tags:
      - 云组件工作负载Pod实例
  /apis/v1/cloudservices/{cloudServiceName}/components/{componentName}/workloads:
    get:
      description: 获取某一集群下的某一云组件的workload列表
      parameters:
      - description: 云服务名称
        in: path
        name: cloudServiceName
        required: true
        type: string
      - description: 云组件名称
        in: path
        name: componentName
        required: true
        type: string
      - description: 集群
        in: query
        name: cluster
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.CloudComponentWorkload'
                  type: array
              type: object
      summary: 获取云组件的workload
      tags:
      - 云组件工作负载
  ? /apis/v1/cloudservices/{cloudServiceName}/components/{componentName}/workloadtypes/{workloadType}/namespaces/{namespace}/podinstances
  : get:
      description: 获取云组件Workload 的 Pod 列表
      parameters:
      - description: 云服务名称
        in: path
        name: cloudServiceName
        required: true
        type: string
      - description: 云组件名称
        in: path
        name: componentName
        required: true
        type: string
      - description: 工作负载类型
        in: path
        name: workloadType
        type: string
      - description: 命名空间
        in: path
        name: namespace
        type: string
      - description: 集群
        in: query
        name: cluster
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.CloudComponentWorkloadPodGrouper'
                  type: array
              type: object
      summary: 获取云组件Workload 的 Pod 列表
      tags:
      - 云组件工作负载Pod实例
  ? /apis/v1/cloudservices/{cloudServiceName}/components/{componentName}/workloadtypes/{workloadType}/namespaces/{namespace}/workloads
  : get:
      description: 获取某一集群下的某一云组件的workload列表
      parameters:
      - description: 云服务名称
        in: path
        name: cloudServiceName
        required: true
        type: string
      - description: 云组件名称
        in: path
        name: componentName
        required: true
        type: string
      - description: 工作负载类型 (Option)
        in: path
        name: workloadType
        type: string
      - description: 命名空间(Option)
        in: path
        name: namespace
        type: string
      - description: 集群
        in: query
        name: cluster
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.CloudComponentWorkload'
                  type: array
              type: object
      summary: 获取云组件的workload
      tags:
      - 云组件工作负载
  ? /apis/v1/cloudservices/{cloudServiceName}/components/{componentName}/workloadtypes/{workloadType}/namespaces/{namespace}/workloads/{workloadName}/podinstances
  : get:
      description: 获取云组件Workload 的 Pod 列表
      parameters:
      - description: 云服务名称
        in: path
        name: cloudServiceName
        required: true
        type: string
      - description: 云组件名称
        in: path
        name: componentName
        required: true
        type: string
      - description: 工作负载类型
        in: path
        name: workloadType
        type: string
      - description: 命名空间
        in: path
        name: namespace
        type: string
      - description: 工作负载名称
        in: path
        name: workloadName
        type: string
      - description: 集群
        in: query
        name: cluster
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.CloudComponentWorkloadPodGrouper'
                  type: array
              type: object
      summary: 获取云组件Workload 的 Pod 列表
      tags:
      - 云组件工作负载Pod实例
  /apis/v1/cloudservices/{cloudServiceName}/components/{componentName}/workloadtypes/{workloadType}/podinstances:
    get:
      description: 获取云组件Workload 的 Pod 列表
      parameters:
      - description: 云服务名称
        in: path
        name: cloudServiceName
        required: true
        type: string
      - description: 云组件名称
        in: path
        name: componentName
        required: true
        type: string
      - description: 工作负载类型
        in: path
        name: workloadType
        type: string
      - description: 集群
        in: query
        name: cluster
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.CloudComponentWorkloadPodGrouper'
                  type: array
              type: object
      summary: 获取云组件Workload 的 Pod 列表
      tags:
      - 云组件工作负载Pod实例
  /apis/v1/cloudservices/{cloudServiceName}/components/{componentName}/workloadtypes/{workloadType}/workloads:
    get:
      description: 获取某一集群下的某一云组件的workload列表
      parameters:
      - description: 云服务名称
        in: path
        name: cloudServiceName
        required: true
        type: string
      - description: 云组件名称
        in: path
        name: componentName
        required: true
        type: string
      - description: 工作负载类型 (Option)
        in: path
        name: workloadType
        type: string
      - description: 集群
        in: query
        name: cluster
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/models.CloudComponentWorkload'
                  type: array
              type: object
      summary: 获取云组件的workload
      tags:
      - 云组件工作负载
  /apis/v1/clusters:
    get:
      description: 获取集群列表
      parameters:
      - default: ""
        description: 集群状态筛选器
        enum:
        - controlled
        - un-controlled
        in: query
        name: state
        type: string
      - default: ""
        description: 集群生命周期状态筛选器
        enum:
        - preflighting
        - installing
        - joining
        - preflightFailed
        - installFailed
        - joinFailed
        - online
        - offline
        - initializing
        - unknow
        in: query
        name: status
        type: string
      - default: ""
        description: 集群API-Server状态
        enum:
        - success
        - fail
        - initialize
        in: query
        name: api_server_status
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/cluster.Response'
                  type: array
              type: object
      summary: 获取集群列表
      tags:
      - 集群相关
      - 集群相关API
  /apis/v1/clusters-create:
    post:
      description: 创建集群 ipv4Param\ipv6Param\auth.Param 请点开model查看详情
      parameters:
      - description: 创建集群请求JSON
        in: body
        name: requestBody
        required: true
        schema:
          $ref: '#/definitions/cluster.CreateRequest'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  type: boolean
              type: object
      summary: 创建集群
      tags:
      - 创建集群相关API
  /apis/v1/clusters-create/{clusterName}:
    delete:
      description: 删除创建失败的集群
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  type: boolean
              type: object
      summary: 删除创建失败的集群
      tags:
      - 创建集群相关API
    get:
      description: 获取创建集群的回显 ipv4Param\ipv6Param\auth.Param 请点开model查看详情
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/cluster.CreateResponse'
              type: object
      summary: 获取创建集群的回显
      tags:
      - 创建集群相关API
    put:
      description: 更新创建失败的集群 以重新开始创建集群的流程 ipv4Param\ipv6Param\auth.Param 请点开model查看详情
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      - description: 创建集群请求JSON
        in: body
        name: requestBody
        required: true
        schema:
          $ref: '#/definitions/cluster.CreateRequest'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  type: boolean
              type: object
      summary: 更新创建失败的集群 以重新开始创建集群的流程
      tags:
      - 创建集群相关API
  /apis/v1/clusters-create/{clusterName}/logs:
    get:
      description: 获取集群创建过程中的日志
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      - description: 是否持续输出
        in: query
        name: follow
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  type: boolean
              type: object
      summary: 获取集群创建过程中的日志
      tags:
      - 创建集群相关API
  /apis/v1/clusters-create/{clusterName}/retry:
    post:
      description: 创建集群失败重试
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  type: boolean
              type: object
      summary: 创建集群失败重试
      tags:
      - 创建集群相关API
  /apis/v1/clusters-create/{clusterName}/status:
    get:
      description: 创建集群状态查询接口
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/cluster.CreateStatusResponse'
              type: object
      summary: 创建集群状态查询接口
      tags:
      - 创建集群相关API
  /apis/v1/clusters-create/{clusterName}/steps/{stepName}/logs:
    get:
      description: 获取集群创建过程中的日志
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      - description: 步骤名称
        in: path
        name: stepName
        type: string
      - description: 是否持续输出
        in: query
        name: follow
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  type: boolean
              type: object
      summary: 获取集群创建过程中的日志
      tags:
      - 创建集群相关API
  /apis/v1/clusters/{clusterName}:
    get:
      description: 查看集群详情
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/cluster.Response'
              type: object
      summary: 查看集群详情
      tags:
      - ""
      - 集
      - 集群相关API
  /apis/v1/clusters/{clusterName}/components:
    get:
      description: 查询集群所有插件信息
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/addon.ComponentListDTO'
                  type: array
              type: object
      summary: 查询集群所有插件信息
      tags:
      - 集群插件相关API
    post:
      description: 插件的接入
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      - description: 接入插件配置信息
        in: body
        name: requestBody
        required: true
        schema:
          $ref: '#/definitions/addon.ComponentRequest'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  type: boolean
              type: object
      summary: 插件接入
      tags:
      - 集群插件相关API
  /apis/v1/clusters/{clusterName}/components/{componentName}:
    delete:
      description: 取消接入的插件
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      - description: 插件名称
        in: path
        name: componentName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  type: boolean
              type: object
      summary: 取消接入的插件
      tags:
      - 集群插件相关API
    get:
      description: 查询插件信息
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      - description: 插件名称
        in: path
        name: componentName
        required: true
        type: string
      - description: 是否获取基础信息
        in: query
        name: baseFlag
        type: boolean
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/addon.ComponentDetailResponse'
              type: object
      summary: 查询插件信息
      tags:
      - 集群插件相关API
    put:
      description: 编辑插件
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      - description: 插件名称
        in: path
        name: componentName
        required: true
        type: string
      - description: 更新的插件配置信息
        in: body
        name: requestBody
        required: true
        schema:
          $ref: '#/definitions/addon.ComponentRequest'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  type: boolean
              type: object
      summary: 编辑插件
      tags:
      - 集群插件相关API
  /apis/v1/clusters/{clusterName}/components/scan:
    get:
      description: 扫描集群插件信息
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/addon.ComponentScanResponse'
                  type: array
              type: object
      summary: 扫描集群插件信息
      tags:
      - 集群插件相关API
  /apis/v1/clusters/{clusterName}/components/switches:
    post:
      description: 插件批量安装
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      - description: 插件批量安装配置信息
        in: body
        name: requestBody
        required: true
        schema:
          $ref: '#/definitions/addon.ComponentRequestList'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  type: boolean
              type: object
      summary: 插件批量安装
      tags:
      - 集群插件相关API
  /apis/v1/clusters/{clusterName}/controlNodes:
    get:
      description: 获取集群主控节点信息
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      - default: ""
        description: 过滤节点IP列表
        in: query
        name: excludes
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/node.SimpleNodeResponse'
                  type: array
              type: object
      summary: 获取集群主控节点信息
      tags:
      - 集群主控节点相关
  /apis/v1/clusters/{clusterName}/exist:
    get:
      description: 根据集群名称查看集群是否存在
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/cluster.ExistResponse'
              type: object
      summary: 查看集群是否存在
      tags:
      - 集群相关API
  /apis/v1/clusters/{clusterName}/nodeUpDown:
    get:
      description: 获取节点上下线任务列表
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/node.NodeUpDownResponse'
                  type: array
              type: object
      summary: 获取节点上下线任务列表
      tags:
      - 节点上下线相关
    post:
      description: 创建节点上下线任务
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      - description: requestBody
        in: body
        name: requestBody
        required: true
        schema:
          $ref: '#/definitions/node.NodeUpDownCreateRequest'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 创建节点上下线任务
      tags:
      - 节点上下线相关
  /apis/v1/clusters/{clusterName}/nodeUpDown/{nodeIp}:
    delete:
      description: 删除
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      - description: 节点IP
        in: path
        name: nodeIp
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 删除
      tags:
      - 节点上下线相关
    get:
      description: 节点上下线表单回显
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      - description: 节点IP
        in: path
        name: nodeIp
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/node.NodeUpDownCreateResponse'
              type: object
      summary: 节点上下线表单回显
      tags:
      - 节点上下线相关
    put:
      description: 编辑节点上下线信息
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      - description: 节点IP
        in: path
        name: nodeIp
        required: true
        type: string
      - description: requestBody
        in: body
        name: requestBody
        required: true
        schema:
          $ref: '#/definitions/node.NodeUpDownCreateRequest'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 编辑节点上下线信息
      tags:
      - 节点上下线相关
  /apis/v1/clusters/{clusterName}/nodeUpDown/{nodeIp}/logs:
    get:
      description: 获取节点上下线日志
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      - description: 节点IP
        in: path
        name: nodeIp
        required: true
        type: string
      - description: 步骤名称
        in: path
        name: stepName
        type: string
      - description: 是否持续输出
        in: query
        name: follow
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  type: boolean
              type: object
      summary: 获取节点上下线日志
      tags:
      - 节点上下线相关
  /apis/v1/clusters/{clusterName}/nodeUpDown/{nodeIp}/retry:
    post:
      description: 失败重试
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      - description: 节点IP
        in: path
        name: nodeIp
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 失败重试
      tags:
      - 节点上下线相关
  /apis/v1/clusters/{clusterName}/nodeUpDown/{nodeIp}/status:
    get:
      description: 获取节点上下线任务执行详情
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      - description: 节点IP
        in: path
        name: nodeIp
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/node.NodeUpDownStatusResponse'
              type: object
      summary: 获取节点上下线任务执行详情
      tags:
      - 节点上下线相关
  /apis/v1/clusters/{clusterName}/nodeUpDown/batch:
    post:
      description: 批量创建节点上下线任务
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      - description: requestBody
        in: body
        name: requestBody
        required: true
        schema:
          $ref: '#/definitions/node.NodeUpDownBatchCreateRequest'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 批量创建节点上下线任务
      tags:
      - 节点上下线相关
  /apis/v1/clusters/{clusterName}/nodeUpDown/count:
    get:
      description: 获取节点上下线任务数量，包括总计数量与数量详情
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      - default: ""
        description: 任务状态列表，使用','隔开
        enum:
        - createInitial
        - preflighting
        - installing
        - createInitialFailed
        - preflightFailed
        - installFailed
        in: query
        name: statuses
        type: string
      - default: ""
        description: 任务类型列表，使用','隔开
        enum:
        - nodeUp
        - nodeDown
        in: query
        name: types
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/node.NodeUpDownCountResponse'
              type: object
      summary: 获取节点上下线任务数量
      tags:
      - 节点上下线相关
  /apis/v1/clusters/{clusterName}/nodeUpDown/verify:
    post:
      description: 创建节点上下线任务 - 节点校验
      parameters:
      - description: 集群名称
        in: path
        name: clusterName
        required: true
        type: string
      - description: requestBody
        in: body
        name: requestBody
        required: true
        schema:
          $ref: '#/definitions/node.NodeUpDownCreateRequest'
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/node.NodeVerifyResponse'
              type: object
      summary: 创建节点上下线任务 - 节点校验
      tags:
      - 节点上下线相关
  /apis/v1/draft/clusters-create:
    delete:
      description: 删除草稿
      parameters:
      - description: 用户令牌
        in: header
        name: Authorization
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  type: boolean
              type: object
      summary: 删除草稿
      tags:
      - 创建集群 - 草稿相关API
    get:
      description: 获取草稿内容
      parameters:
      - description: 用户令牌
        in: header
        name: Authorization
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data: {}
              type: object
      summary: 获取草稿内容
      tags:
      - 创建集群 - 草稿相关API
    post:
      description: 创建或保存草稿
      parameters:
      - description: 用户令牌
        in: header
        name: Authorization
        required: true
        type: string
      - description: 保存｜创建草稿JSON
        in: body
        name: requestBody
        required: true
        schema: {}
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  type: boolean
              type: object
      summary: 应用草稿
      tags:
      - 创建集群 - 草稿相关API
  /apis/v1/monitoring/dashboards:
    get:
      description: 获取Grafana Dashboards
      parameters:
      - in: query
        name: PVCName
        type: string
      - in: query
        name: cloudComponentName
        type: string
      - in: query
        name: cloudServiceName
        type: string
      - in: query
        name: cluster
        type: string
      - in: query
        name: datasource
        type: string
      - enum:
        - cluster
        - node
        - workload
        - pod
        - cloudservice
        - cloudcomponent
        in: query
        name: level
        required: true
        type: string
        x-enum-varnames:
        - LevelCluster
        - LevelNode
        - LevelWorkload
        - LevelPod
        - LevelCloudService
        - LevelComponent
      - in: query
        name: namespace
        type: string
      - in: query
        name: namespaceList
        type: string
      - in: query
        name: nodeName
        type: string
      - in: query
        name: podName
        type: string
      - in: query
        name: workloadKind
        type: string
      - in: query
        name: workloadList
        type: string
      - in: query
        name: workloadName
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/monitoring.Dashboard'
              type: object
      summary: 获取Grafana Dashboards
      tags:
      - Grafana
  /apis/v1/node_verify:
    post:
      description: proxy接口，校验节点联通性
      parameters:
      - description: 使用那个集群的西西弗斯，非必填
        in: query
        name: clusterName
        required: true
        type: string
      - description: 需要校验的节点
        in: body
        name: requestBody
        required: true
        schema:
          items:
            $ref: '#/definitions/node.NodeVeriryRequest'
          type: array
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/node.NodeVerifyResponse'
              type: object
      summary: 校验节点联通性
      tags:
      - 节点校验相关
  /apis/v1/nodeterminals/clusters/{cluster}/console/sessions:
    post:
      description: 在指定集群的节点上创建调试控制台会话
      parameters:
      - description: 集群名称
        in: path
        name: cluster
        required: true
        type: string
      - description: 会话请求参数
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/console.ConsoleSessionRequest'
      responses:
        "200":
          description: 成功
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/console.ConsoleSessionResponse'
              type: object
      summary: 创建节点控制台会话
      tags:
      - 节点控制台
  /apis/v1/nodeterminals/clusters/{cluster}/console/sessions/{podName}:
    delete:
      description: 删除指定集群中的控制台会话
      parameters:
      - description: 集群名称
        in: path
        name: cluster
        required: true
        type: string
      - description: Pod名称
        in: path
        name: podName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  additionalProperties: true
                  type: object
              type: object
      summary: 删除控制台会话
      tags:
      - 节点控制台
    get:
      description: 获取指定集群中控制台会话的状态信息
      parameters:
      - description: 集群名称
        in: path
        name: cluster
        required: true
        type: string
      - description: Pod名称
        in: path
        name: podName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/console.SessionStatusResponse'
              type: object
      summary: 获取控制台会话状态
      tags:
      - 节点控制台
  /apis/v1/nodeterminals/clusters/{cluster}/console/sessions/{podName}/attach:
    get:
      description: 通过WebSocket与Pod主进程直接交互（kubectl attach效果）
      parameters:
      - description: 集群名称
        in: path
        name: cluster
        required: true
        type: string
      - description: Pod名称
        in: path
        name: podName
        required: true
        type: string
      responses:
        "101":
          description: Switching Protocols
          schema:
            type: string
      summary: 附着到Pod主进程的WebSocket连接
      tags:
      - 节点控制台
  /apis/v1/nodeterminals/clusters/{cluster}/console/sessions/{podName}/websocket:
    get:
      description: 建立与节点控制台的WebSocket连接
      parameters:
      - description: 集群名称
        in: path
        name: cluster
        required: true
        type: string
      - description: Pod名称
        in: path
        name: podName
        required: true
        type: string
      responses:
        "101":
          description: Switching Protocols
          schema:
            type: string
      summary: 建立WebSocket连接
      tags:
      - 节点控制台
  /apis/v1/sys-configs/{group_name}:
    get:
      description: 获取某一配置组下的全部配置信息
      parameters:
      - default: create-cluster
        description: 配置组名称
        enum:
        - create-cluster
        - sisyphus-config
        - system-info
        - cluster-info
        in: path
        name: group_name
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/config.Response'
                  type: array
              type: object
      summary: 获取某一配置组下的全部配置信息
      tags:
      - 配置相关
  /apis/v1/sys-configs/{group_name}/info:
    get:
      description: 获取某一配置组下的全部配置信息(糖方式)(推荐前端使用该接口)
      parameters:
      - default: create-cluster
        description: 配置组名称
        enum:
        - create-cluster
        - sisyphus-config
        - system-info
        - cluster-info
        in: path
        name: group_name
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/config.SugarResponse'
                  type: array
              type: object
      summary: 获取某一配置组下的全部配置信息(糖方式)(推荐前端使用该接口)
      tags:
      - 配置相关
  /apis/v1/sys-configs/{group_name}/types/{type_name}:
    get:
      description: 获取某一配置组下的特定配置信息
      parameters:
      - default: create-cluster
        description: 配置组名称
        enum:
        - create-cluster
        - sisyphus-config
        - system-info
        - cluster-info
        in: path
        name: group_name
        required: true
        type: string
      - default: kubernetes_versions
        description: 配置项名称
        enum:
        - kubernetes_versions
        - kubernetes_CRIS
        - hub_cluster_ingress_address
        - hub_cluster_stellaries_component
        - default_calico_cidr
        - support
        - default_registry
        - chrony-info
        in: path
        name: type_name
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/config.Response'
              type: object
      summary: 获取某一配置组下的特定配置信息
      tags:
      - 配置相关
  /apis/v1/sys-configs/{group_name}/types/{type_name}/info:
    get:
      description: 获取某一配置组下的特定配置信息(糖方式)(推荐前端使用该接口)
      parameters:
      - default: create-cluster
        description: 配置组名称
        enum:
        - create-cluster
        - sisyphus-config
        - system-info
        - cluster-info
        in: path
        name: group_name
        required: true
        type: string
      - default: kubernetes_versions
        description: 配置项名称
        enum:
        - kubernetes_versions
        - kubernetes_CRIS
        - hub_cluster_ingress_address
        - hub_cluster_stellaries_component
        - default_calico_cidr
        - support
        - default_registry
        - chrony-info
        in: path
        name: type_name
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data: {}
              type: object
      summary: 获取某一配置组下的特定配置信息(糖方式)(推荐前端使用该接口)
      tags:
      - 配置相关
  /apis/v1/system/backuprestore/clusterName/{clusterName}/schedules/:scheduleName/start:
    post:
      parameters:
      - description: 备份策略名称
        in: path
        name: scheduleName
        required: true
        type: string
      - description: 集群名
        in: query
        name: clusterName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 启动数据备份策略
      tags:
      - 系统运维@数据备份
  /apis/v1/system/backuprestore/restoresTemplate:
    get:
      parameters:
      - description: 集群名
        in: query
        name: clusterName
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/velero.RestoreTemplate'
                  type: array
              type: object
      summary: 查询数据恢复列表
      tags:
      - 系统运维@数据恢复
    post:
      parameters:
      - description: 恢复策略
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/velero.RestoreTemplate'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 新增数据恢复策略
      tags:
      - 系统运维@数据恢复
  /apis/v1/system/backuprestore/restoresTemplate/:restoresTemplateId:
    delete:
      parameters:
      - description: 恢复策略id
        in: query
        name: restoresTemplateId
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 删除数据恢复策略
      tags:
      - 系统运维@数据恢复
    get:
      parameters:
      - description: 恢复策略id
        in: query
        name: restoresTemplateId
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/velero.RestoreTemplate'
              type: object
      summary: 查询数据恢复策略详情
      tags:
      - 系统运维@数据恢复
  /apis/v1/system/backuprestore/restoresTemplate/:restoresTemplateId/restores:
    get:
      parameters:
      - description: 恢复策略id
        in: query
        name: restoresTemplateId
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/velero.Restore'
                  type: array
              type: object
      summary: 查询数据恢复记录
      tags:
      - 系统运维@数据恢复
  /apis/v1/system/backuprestore/restoresTemplate/:restoresTemplateId/restores/:restoresName/logs:
    get:
      parameters:
      - description: 恢复策略id
        in: query
        name: restoresTemplateId
        required: true
        type: string
      - description: 恢复记录名称
        in: query
        name: restoresName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 查询数据恢复记录日志
      tags:
      - 系统运维@数据恢复
  /apis/v1/system/backuprestore/restoresTemplate/:restoresTemplateId/restores/:restoresName/persistentVolumes:
    get:
      parameters:
      - description: 恢复策略id
        in: query
        name: restoresTemplateId
        required: true
        type: string
      - description: 恢复记录名称
        in: query
        name: restoresName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/velero.VeleroResources'
                  type: array
              type: object
      summary: 查询数据恢复存储卷列表
      tags:
      - 系统运维@数据恢复
  /apis/v1/system/backuprestore/restoresTemplate/:restoresTemplateId/restores/:restoresName/resources:
    get:
      parameters:
      - description: 恢复策略id
        in: query
        name: restoresTemplateId
        required: true
        type: string
      - description: 恢复记录名称
        in: query
        name: restoresName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/velero.VeleroResources'
                  type: array
              type: object
      summary: 查询数据恢复资源列表
      tags:
      - 系统运维@数据恢复
  /apis/v1/system/backuprestore/restoresTemplate/:restoresTemplateId/retry:
    post:
      parameters:
      - description: 恢复策略id
        in: query
        name: restoresTemplateId
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 重试数据恢复记录
      tags:
      - 系统运维@数据恢复
  /apis/v1/system/backuprestore/restoresTemplate/nameCheck:
    post:
      parameters:
      - description: 恢复策略名称
        in: query
        name: restoresTemplateName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 数据恢复策略名称校验
      tags:
      - 系统运维@数据恢复
  /apis/v1/system/backuprestore/schedules:
    get:
      parameters:
      - description: 集群名
        in: query
        name: clusterName
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/velero.SchedulesItem'
                  type: array
              type: object
      summary: 获取备份策略列表
      tags:
      - 系统运维@数据备份
    post:
      parameters:
      - description: 备份策略
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/velero.Schedules'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 新增数据备份策略
      tags:
      - 系统运维@数据备份
  /apis/v1/system/backuprestore/schedules/:scheduleName:
    delete:
      parameters:
      - description: 备份策略名称
        in: path
        name: scheduleName
        required: true
        type: string
      - description: 集群名
        in: query
        name: clusterName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 删除数据备份策略
      tags:
      - 系统运维@数据备份
    get:
      parameters:
      - description: 备份策略名称
        in: path
        name: scheduleName
        required: true
        type: string
      - description: 集群名
        in: query
        name: clusterName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/velero.Schedules'
              type: object
      summary: 查询数据备份策略详情
      tags:
      - 系统运维@数据备份
    put:
      parameters:
      - description: 备份策略名称
        in: path
        name: scheduleName
        required: true
        type: string
      - description: 备份策略
        in: body
        name: body
        required: true
        schema:
          $ref: '#/definitions/velero.Schedules'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 编辑数据备份策略
      tags:
      - 系统运维@数据备份
  /apis/v1/system/backuprestore/schedules/:scheduleName/backups:
    get:
      parameters:
      - description: 备份策略名称
        in: path
        name: scheduleName
        required: true
        type: string
      - description: 集群名
        in: query
        name: clusterName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/velero.Backups'
                  type: array
              type: object
      summary: 查询数据备份策略备份记录列表
      tags:
      - 系统运维@数据备份
  /apis/v1/system/backuprestore/schedules/:scheduleName/backups/:backupName:
    delete:
      parameters:
      - description: 备份策略名称
        in: path
        name: scheduleName
        required: true
        type: string
      - description: 备份记录名称
        in: path
        name: backupName
        required: true
        type: string
      - description: 集群名
        in: query
        name: clusterName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 删除数据备份策略备份记录
      tags:
      - 系统运维@数据备份
  /apis/v1/system/backuprestore/schedules/:scheduleName/backups/:backupName/logs:
    get:
      parameters:
      - description: 备份策略名称
        in: path
        name: scheduleName
        required: true
        type: string
      - description: 备份文件名称
        in: path
        name: backupName
        required: true
        type: string
      - description: 集群名
        in: query
        name: clusterName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 查询数据备份策略备份记录日志
      tags:
      - 系统运维@数据备份
  /apis/v1/system/backuprestore/schedules/:scheduleName/backups/:backupName/persistentVolumes:
    get:
      parameters:
      - description: 备份策略名称
        in: path
        name: scheduleName
        required: true
        type: string
      - description: 备份文件名称
        in: path
        name: backupName
        required: true
        type: string
      - description: 集群名
        in: query
        name: clusterName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/velero.VeleroVolumes'
                  type: array
              type: object
      summary: 查询数据备份策略备份记录资源存储卷
      tags:
      - 系统运维@数据备份
  /apis/v1/system/backuprestore/schedules/:scheduleName/backups/:backupName/resources:
    get:
      parameters:
      - description: 备份策略名称
        in: path
        name: scheduleName
        required: true
        type: string
      - description: 备份文件名称
        in: path
        name: backupName
        required: true
        type: string
      - description: 集群名
        in: query
        name: clusterName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/velero.VeleroResources'
                  type: array
              type: object
      summary: 查询数据备份策略备份记录资源列表
      tags:
      - 系统运维@数据备份
  /apis/v1/system/backuprestore/schedules/nameCheck:
    post:
      parameters:
      - description: 备份策略名称
        in: query
        name: scheduleName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 数据备份策略名称校验(系统级)
      tags:
      - 系统运维@数据备份
  /apis/v1/system/clusterName/{clusterName}/backuprestore/schedules/:scheduleName/execute:
    post:
      parameters:
      - description: 备份策略名称
        in: path
        name: scheduleName
        required: true
        type: string
      - description: 集群名
        in: query
        name: clusterName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 立即执行数据备份策略
      tags:
      - 系统运维@数据备份
  /apis/v1/system/clusterName/{clusterName}/backuprestore/schedules/:scheduleName/stop:
    post:
      parameters:
      - description: 备份策略名称
        in: path
        name: scheduleName
        required: true
        type: string
      - description: 集群名
        in: query
        name: clusterName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 停止数据备份策略
      tags:
      - 系统运维@数据备份
  /apis/v1/system/clusterName/{clusterName}/backuprestore/schedules/:scheduleName/syncStorageServer:
    post:
      parameters:
      - description: 备份策略名称
        in: path
        name: scheduleName
        required: true
        type: string
      - description: 集群名
        in: query
        name: clusterName
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 同步备份服务器信息
      tags:
      - 系统运维@数据备份
  /apis/v1/system/storageServers:
    get:
      responses:
        "200":
          description: OK
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  $ref: '#/definitions/velero.StorageServers'
              type: object
      summary: 获取存储服务器列表
      tags:
      - 运维设置@备份服务器
    post:
      parameters:
      - description: 参数
        in: body
        name: queryParam
        required: true
        schema:
          $ref: '#/definitions/velero.StorageServers'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 创建存储服务器
      tags:
      - 运维设置@备份服务器
  /apis/v1/system/storageServers/:storageServersId:
    delete:
      parameters:
      - description: 存储服务id
        in: query
        name: storageServersId
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 删除存储服务器
      tags:
      - 运维设置@备份服务器
    put:
      parameters:
      - description: 存储服务id
        in: query
        name: storageServersId
        required: true
        type: string
      - description: 参数
        in: body
        name: queryParam
        required: true
        schema:
          $ref: '#/definitions/velero.StorageServers'
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 更新存储服务器
      tags:
      - 运维设置@备份服务器
  /apis/v1/template/excel/{template_code}/download:
    get:
      description: 获取excel模版文件的下载流
      parameters:
      - default: ""
        description: 语言
        enum:
        - zh-CN
        - zh-HK
        - en-US
        in: query
        name: language
        type: string
      - default: cluster_create_nodes_template_AllInOne
        description: 模版文件code
        enum:
        - cluster_create_nodes_template_AllInOne
        - cluster_create_nodes_template_MinimizeHA
        - cluster_create_nodes_template_StandardNoneHA
        - cluster_create_nodes_template_StandardHA
        - cluster_node_batch_append
        - cluster_upgrade_nodes
        in: path
        name: template_code
        required: true
        type: string
      responses:
        "200":
          description: OK
          schema:
            type: file
      summary: 获取excel模版文件的下载流
      tags:
      - 模版文件相关
  /apis/v1/template/excel/{template_code}/upload:
    post:
      description: 解析上传excel文件的内容
      parameters:
      - default: cluster_create_nodes_template_AllInOne
        description: 模版文件code
        enum:
        - cluster_create_nodes_template_AllInOne
        - cluster_create_nodes_template_MinimizeHA
        - cluster_create_nodes_template_StandardNoneHA
        - cluster_create_nodes_template_StandardHA
        - cluster_node_batch_append
        - cluster_upgrade_nodes
        in: path
        name: template_code
        required: true
        type: string
      - description: 文件
        in: formData
        name: file
        required: true
        type: file
      responses:
        "200":
          description: OK
          schema:
            $ref: '#/definitions/utils.Response'
      summary: 解析上传excel文件的内容
      tags:
      - 模版文件相关
  /apis/v1/workspace/cloudservices/{cloudServiceName}:
    get:
      consumes:
      - application/json
      description: List the resources related to a cloud service in a workspace.
      parameters:
      - description: Name of the cloud service
        in: path
        name: cloudServiceName
        required: true
        type: string
      - description: ID of the project
        in: query
        name: projectId
        type: string
      - description: ID of the organization
        in: query
        name: organId
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: List of cloud service resources
          schema:
            allOf:
            - $ref: '#/definitions/utils.Response'
            - properties:
                data:
                  items:
                    $ref: '#/definitions/workspace.Resource'
                  type: array
              type: object
      summary: List cloud service resources
      tags:
      - WorkspaceIntf
  /k8s/clusters/{cluster}/api/{version}/{resourcetype}:
    delete:
      consumes:
      - application/json
      description: 可删除K8S多个资源
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        type: string
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: 资源版本
        in: path
        name: version
        required: true
        type: string
      - description: 资源类型
        example: pods
        in: path
        name: resourcetype
        required: true
        type: string
      - description: Label过滤
        in: query
        name: label_selector
        type: string
      - description: delete options
        in: body
        name: request
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
      security:
      - ApiKeyAuth: []
      summary: 通用Delete All接口
      tags:
      - 通用Delete All接口
    get:
      description: 可获取一组K8s资源
      parameters:
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: api版本
        in: path
        name: version
        required: true
        type: string
      - description: 资源类型
        in: path
        name: resourcetype
        required: true
        type: string
      - description: selector
        in: query
        name: selector
        type: string
      - description: filter page_size 页长
        in: query
        name: page_size
        type: string
      - description: filter page_num 页码
        in: query
        name: page_num
        type: string
      - description: sort_name
        in: query
        name: sort_name
        type: string
      - description: sort_order
        in: query
        name: sort_order
        type: string
      - description: sort_func
        in: query
        name: sort_func
        type: string
      - description: 标签选择器
        in: query
        name: label_selector
        type: string
      responses:
        "200":
          description: OK
          schema:
            type: object
      summary: 通用List接口
      tags:
      - 通用List接口
    post:
      consumes:
      - application/json
      description: 创建资源
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        type: string
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: 资源版本
        in: path
        name: version
        required: true
        type: string
      - description: 资源类型
        example: pods
        in: path
        name: resourcetype
        required: true
        type: string
      - description: 资源JSON
        in: body
        name: request
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
      security:
      - ApiKeyAuth: []
      summary: 通用Create接口
      tags:
      - 通用Create接口
  /k8s/clusters/{cluster}/api/{version}/{resourcetype}/{name}:
    delete:
      consumes:
      - application/json
      description: 可删除K8S单个资源
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        type: string
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: 资源版本
        in: path
        name: version
        required: true
        type: string
      - description: 资源类型
        example: pods
        in: path
        name: resourcetype
        required: true
        type: string
      - description: 资源名
        in: path
        name: name
        required: true
        type: string
      - description: delete options
        in: body
        name: request
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
      security:
      - ApiKeyAuth: []
      summary: 通用Delete接口
      tags:
      - 通用Delete接口
    get:
      description: 可以获取某一个k8s资源
      parameters:
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: api版本
        in: path
        name: version
        required: true
        type: string
      - description: 资源类型
        in: path
        name: resourcetype
        required: true
        type: string
      - description: 资源名称
        in: path
        name: name
        required: true
        type: string
      responses: {}
      summary: 通用Get接口
      tags:
      - 通用Get接口
    patch:
      description: 可更新K8S单个资源
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        type: string
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: 资源版本
        in: path
        name: version
        required: true
        type: string
      - description: 资源类型
        example: pods
        in: path
        name: resourcetype
        required: true
        type: string
      - description: 资源名
        in: path
        name: name
        required: true
        type: string
      - description: Patch方式[application/json-patch+json,application/merge-patch+json,application/strategic-merge-patch+json,application/apply-patch+yaml]
        in: header
        name: Content-Type
        required: true
        type: string
      - description: 资源Patch JSON
        in: body
        name: request
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
      security:
      - ApiKeyAuth: []
      summary: 通用Patch接口
      tags:
      - 通用Patch接口
    put:
      consumes:
      - application/json
      description: 可更新K8S单个资源
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        type: string
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: 资源版本
        in: path
        name: version
        required: true
        type: string
      - description: 资源类型
        example: pods
        in: path
        name: resourcetype
        required: true
        type: string
      - description: 资源名
        in: path
        name: name
        required: true
        type: string
      - description: 资源JSON
        in: body
        name: request
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
      security:
      - ApiKeyAuth: []
      summary: 通用Update接口
      tags:
      - 通用Update接口
  /k8s/clusters/{cluster}/api/{version}/namespaces/{namespace}/{resourcetype}:
    delete:
      consumes:
      - application/json
      description: 可删除K8S多个资源
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        type: string
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: 资源版本
        in: path
        name: version
        required: true
        type: string
      - description: 命名空间
        in: path
        name: namespace
        type: string
      - description: 资源类型
        example: pods
        in: path
        name: resourcetype
        required: true
        type: string
      - description: Label过滤
        in: query
        name: label_selector
        type: string
      - description: delete options
        in: body
        name: request
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
      security:
      - ApiKeyAuth: []
      summary: 通用Delete All接口
      tags:
      - 通用Delete All接口
    get:
      description: 可获取一组K8s资源
      parameters:
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: api版本
        in: path
        name: version
        required: true
        type: string
      - description: 资源所在Namespace
        in: path
        name: namespace
        type: string
      - description: 资源类型
        in: path
        name: resourcetype
        required: true
        type: string
      - description: selector
        in: query
        name: selector
        type: string
      - description: filter page_size 页长
        in: query
        name: page_size
        type: string
      - description: filter page_num 页码
        in: query
        name: page_num
        type: string
      - description: sort_name
        in: query
        name: sort_name
        type: string
      - description: sort_order
        in: query
        name: sort_order
        type: string
      - description: sort_func
        in: query
        name: sort_func
        type: string
      - description: 标签选择器
        in: query
        name: label_selector
        type: string
      responses:
        "200":
          description: OK
          schema:
            type: object
      summary: 通用List接口
      tags:
      - 通用List接口
    post:
      consumes:
      - application/json
      description: 创建资源
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        type: string
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: 资源版本
        in: path
        name: version
        required: true
        type: string
      - description: 命名空间
        in: path
        name: namespace
        type: string
      - description: 资源类型
        example: pods
        in: path
        name: resourcetype
        required: true
        type: string
      - description: 资源JSON
        in: body
        name: request
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
      security:
      - ApiKeyAuth: []
      summary: 通用Create接口
      tags:
      - 通用Create接口
  /k8s/clusters/{cluster}/api/{version}/namespaces/{namespace}/{resourcetype}/{name}:
    delete:
      consumes:
      - application/json
      description: 可删除K8S单个资源
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        type: string
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: 资源版本
        in: path
        name: version
        required: true
        type: string
      - description: 命名空间
        in: path
        name: namespace
        type: string
      - description: 资源类型
        example: pods
        in: path
        name: resourcetype
        required: true
        type: string
      - description: 资源名
        in: path
        name: name
        required: true
        type: string
      - description: delete options
        in: body
        name: request
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
      security:
      - ApiKeyAuth: []
      summary: 通用Delete接口
      tags:
      - 通用Delete接口
    get:
      description: 可以获取某一个k8s资源
      parameters:
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: api版本
        in: path
        name: version
        required: true
        type: string
      - description: 资源所在Namespace
        in: path
        name: namespace
        type: string
      - description: 资源类型
        in: path
        name: resourcetype
        required: true
        type: string
      - description: 资源名称
        in: path
        name: name
        required: true
        type: string
      responses: {}
      summary: 通用Get接口
      tags:
      - 通用Get接口
    patch:
      description: 可更新K8S单个资源
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        type: string
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: 资源版本
        in: path
        name: version
        required: true
        type: string
      - description: 命名空间
        in: path
        name: namespace
        type: string
      - description: 资源类型
        example: pods
        in: path
        name: resourcetype
        required: true
        type: string
      - description: 资源名
        in: path
        name: name
        required: true
        type: string
      - description: Patch方式[application/json-patch+json,application/merge-patch+json,application/strategic-merge-patch+json,application/apply-patch+yaml]
        in: header
        name: Content-Type
        required: true
        type: string
      - description: 资源Patch JSON
        in: body
        name: request
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
      security:
      - ApiKeyAuth: []
      summary: 通用Patch接口
      tags:
      - 通用Patch接口
    put:
      consumes:
      - application/json
      description: 可更新K8S单个资源
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        type: string
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: 资源版本
        in: path
        name: version
        required: true
        type: string
      - description: 命名空间
        in: path
        name: namespace
        type: string
      - description: 资源类型
        example: pods
        in: path
        name: resourcetype
        required: true
        type: string
      - description: 资源名
        in: path
        name: name
        required: true
        type: string
      - description: 资源JSON
        in: body
        name: request
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
      security:
      - ApiKeyAuth: []
      summary: 通用Update接口
      tags:
      - 通用Update接口
  /k8s/clusters/{cluster}/api/v1/namespaces/{namespace}/pods/{name}/log:
    get:
      description: 读取k8s资源
      parameters:
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: 资源所在Namespace
        in: path
        name: namespace
        required: true
        type: string
      - description: pod名称
        in: path
        name: name
        required: true
        type: string
      - description: 模糊过滤搜索字段
        in: query
        name: searchText
        type: string
      - description: 容器名
        in: query
        name: container
        type: string
      - description: 是否持续输出，如果是true，则需要使用 websocket
        in: query
        name: follow
        type: boolean
      - description: 是否展示已终止的容器日志
        in: query
        name: previous
        type: boolean
      - description: 显示最新日志行数，最多5000
        in: query
        name: tailLines
        required: true
        type: integer
      - description: 展示当前时间过去几秒的日志
        in: query
        name: sinceSeconds
        type: integer
      responses:
        "200":
          description: OK
          schema:
            type: object
      summary: 通用Logs接口
      tags:
      - 通用Logs接口
  /k8s/clusters/{cluster}/api_resources:
    get:
      consumes:
      - application/json
      - ' application/yaml'
      description: 该接口用于创建 Kubernetes 资源。根据客户端提供的 JSON 或 YAML 数据，创建指定的资源。
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        type: string
      - description: 集群名称
        in: path
        name: cluster
        required: true
        type: string
      - description: 资源的 JSON 或 YAML 数据
        in: body
        name: request
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
      security:
      - ApiKeyAuth: []
      summary: 创建资源
      tags:
      - 通用Create接口
  /k8s/clusters/{cluster}/apis/{group}/{version}/:resourcetype/{name}:
    get:
      description: 可以获取某一个k8s资源
      parameters:
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: API组别
        in: path
        name: group
        type: string
      - description: api版本
        in: path
        name: version
        required: true
        type: string
      - description: 资源类型
        in: path
        name: resourcetype
        required: true
        type: string
      - description: 资源名称
        in: path
        name: name
        required: true
        type: string
      responses: {}
      summary: 通用Get接口
      tags:
      - 通用Get接口
  /k8s/clusters/{cluster}/apis/{group}/{version}/{resourcetype}:
    delete:
      consumes:
      - application/json
      description: 可删除K8S多个资源
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        type: string
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: 资源Group
        in: path
        name: group
        type: string
      - description: 资源版本
        in: path
        name: version
        required: true
        type: string
      - description: 资源类型
        example: pods
        in: path
        name: resourcetype
        required: true
        type: string
      - description: Label过滤
        in: query
        name: label_selector
        type: string
      - description: delete options
        in: body
        name: request
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
      security:
      - ApiKeyAuth: []
      summary: 通用Delete All接口
      tags:
      - 通用Delete All接口
    get:
      description: 可获取一组K8s资源
      parameters:
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: API组别
        in: path
        name: group
        type: string
      - description: api版本
        in: path
        name: version
        required: true
        type: string
      - description: 资源类型
        in: path
        name: resourcetype
        required: true
        type: string
      - description: selector
        in: query
        name: selector
        type: string
      - description: filter page_size 页长
        in: query
        name: page_size
        type: string
      - description: filter page_num 页码
        in: query
        name: page_num
        type: string
      - description: sort_name
        in: query
        name: sort_name
        type: string
      - description: sort_order
        in: query
        name: sort_order
        type: string
      - description: sort_func
        in: query
        name: sort_func
        type: string
      - description: 标签选择器
        in: query
        name: label_selector
        type: string
      responses:
        "200":
          description: OK
          schema:
            type: object
      summary: 通用List接口
      tags:
      - 通用List接口
    post:
      consumes:
      - application/json
      description: 创建资源
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        type: string
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: 资源Group
        in: path
        name: group
        type: string
      - description: 资源版本
        in: path
        name: version
        required: true
        type: string
      - description: 资源类型
        example: pods
        in: path
        name: resourcetype
        required: true
        type: string
      - description: 资源JSON
        in: body
        name: request
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
      security:
      - ApiKeyAuth: []
      summary: 通用Create接口
      tags:
      - 通用Create接口
  /k8s/clusters/{cluster}/apis/{group}/{version}/{resourcetype}/{name}:
    delete:
      consumes:
      - application/json
      description: 可删除K8S单个资源
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        type: string
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: 资源Group
        in: path
        name: group
        type: string
      - description: 资源版本
        in: path
        name: version
        required: true
        type: string
      - description: 资源类型
        example: pods
        in: path
        name: resourcetype
        required: true
        type: string
      - description: 资源名
        in: path
        name: name
        required: true
        type: string
      - description: delete options
        in: body
        name: request
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
      security:
      - ApiKeyAuth: []
      summary: 通用Delete接口
      tags:
      - 通用Delete接口
    patch:
      description: 可更新K8S单个资源
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        type: string
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: 资源Group
        in: path
        name: group
        type: string
      - description: 资源版本
        in: path
        name: version
        required: true
        type: string
      - description: 资源类型
        example: pods
        in: path
        name: resourcetype
        required: true
        type: string
      - description: 资源名
        in: path
        name: name
        required: true
        type: string
      - description: Patch方式[application/json-patch+json,application/merge-patch+json,application/strategic-merge-patch+json,application/apply-patch+yaml]
        in: header
        name: Content-Type
        required: true
        type: string
      - description: 资源Patch JSON
        in: body
        name: request
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
      security:
      - ApiKeyAuth: []
      summary: 通用Patch接口
      tags:
      - 通用Patch接口
    put:
      consumes:
      - application/json
      description: 可更新K8S单个资源
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        type: string
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: 资源Group
        in: path
        name: group
        type: string
      - description: 资源版本
        in: path
        name: version
        required: true
        type: string
      - description: 资源类型
        example: pods
        in: path
        name: resourcetype
        required: true
        type: string
      - description: 资源名
        in: path
        name: name
        required: true
        type: string
      - description: 资源JSON
        in: body
        name: request
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
      security:
      - ApiKeyAuth: []
      summary: 通用Update接口
      tags:
      - 通用Update接口
  /k8s/clusters/{cluster}/apis/{group}/{version}/namespaces/{namespace}/{resourcetype}:
    delete:
      consumes:
      - application/json
      description: 可删除K8S多个资源
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        type: string
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: 资源Group
        in: path
        name: group
        type: string
      - description: 资源版本
        in: path
        name: version
        required: true
        type: string
      - description: 命名空间
        in: path
        name: namespace
        type: string
      - description: 资源类型
        example: pods
        in: path
        name: resourcetype
        required: true
        type: string
      - description: Label过滤
        in: query
        name: label_selector
        type: string
      - description: delete options
        in: body
        name: request
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
      security:
      - ApiKeyAuth: []
      summary: 通用Delete All接口
      tags:
      - 通用Delete All接口
    get:
      description: 可获取一组K8s资源
      parameters:
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: API组别
        in: path
        name: group
        type: string
      - description: api版本
        in: path
        name: version
        required: true
        type: string
      - description: 资源所在Namespace
        in: path
        name: namespace
        type: string
      - description: 资源类型
        in: path
        name: resourcetype
        required: true
        type: string
      - description: selector
        in: query
        name: selector
        type: string
      - description: filter page_size 页长
        in: query
        name: page_size
        type: string
      - description: filter page_num 页码
        in: query
        name: page_num
        type: string
      - description: sort_name
        in: query
        name: sort_name
        type: string
      - description: sort_order
        in: query
        name: sort_order
        type: string
      - description: sort_func
        in: query
        name: sort_func
        type: string
      - description: 标签选择器
        in: query
        name: label_selector
        type: string
      responses:
        "200":
          description: OK
          schema:
            type: object
      summary: 通用List接口
      tags:
      - 通用List接口
    post:
      consumes:
      - application/json
      description: 创建资源
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        type: string
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: 资源Group
        in: path
        name: group
        type: string
      - description: 资源版本
        in: path
        name: version
        required: true
        type: string
      - description: 命名空间
        in: path
        name: namespace
        type: string
      - description: 资源类型
        example: pods
        in: path
        name: resourcetype
        required: true
        type: string
      - description: 资源JSON
        in: body
        name: request
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
      security:
      - ApiKeyAuth: []
      summary: 通用Create接口
      tags:
      - 通用Create接口
  /k8s/clusters/{cluster}/apis/{group}/{version}/namespaces/{namespace}/{resourcetype}/{name}:
    delete:
      consumes:
      - application/json
      description: 可删除K8S单个资源
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        type: string
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: 资源Group
        in: path
        name: group
        type: string
      - description: 资源版本
        in: path
        name: version
        required: true
        type: string
      - description: 命名空间
        in: path
        name: namespace
        type: string
      - description: 资源类型
        example: pods
        in: path
        name: resourcetype
        required: true
        type: string
      - description: 资源名
        in: path
        name: name
        required: true
        type: string
      - description: delete options
        in: body
        name: request
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
      security:
      - ApiKeyAuth: []
      summary: 通用Delete接口
      tags:
      - 通用Delete接口
    get:
      description: 可以获取某一个k8s资源
      parameters:
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: API组别
        in: path
        name: group
        type: string
      - description: api版本
        in: path
        name: version
        required: true
        type: string
      - description: 资源所在Namespace
        in: path
        name: namespace
        type: string
      - description: 资源类型
        in: path
        name: resourcetype
        required: true
        type: string
      - description: 资源名称
        in: path
        name: name
        required: true
        type: string
      responses: {}
      summary: 通用Get接口
      tags:
      - 通用Get接口
    patch:
      description: 可更新K8S单个资源
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        type: string
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: 资源Group
        in: path
        name: group
        type: string
      - description: 资源版本
        in: path
        name: version
        required: true
        type: string
      - description: 命名空间
        in: path
        name: namespace
        type: string
      - description: 资源类型
        example: pods
        in: path
        name: resourcetype
        required: true
        type: string
      - description: 资源名
        in: path
        name: name
        required: true
        type: string
      - description: Patch方式[application/json-patch+json,application/merge-patch+json,application/strategic-merge-patch+json,application/apply-patch+yaml]
        in: header
        name: Content-Type
        required: true
        type: string
      - description: 资源Patch JSON
        in: body
        name: request
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
      security:
      - ApiKeyAuth: []
      summary: 通用Patch接口
      tags:
      - 通用Patch接口
    put:
      consumes:
      - application/json
      description: 可更新K8S单个资源
      parameters:
      - description: Bearer 用户令牌
        in: header
        name: Authorization
        type: string
      - description: 集群
        in: path
        name: cluster
        required: true
        type: string
      - description: 资源Group
        in: path
        name: group
        type: string
      - description: 资源版本
        in: path
        name: version
        required: true
        type: string
      - description: 命名空间
        in: path
        name: namespace
        type: string
      - description: 资源类型
        example: pods
        in: path
        name: resourcetype
        required: true
        type: string
      - description: 资源名
        in: path
        name: name
        required: true
        type: string
      - description: 资源JSON
        in: body
        name: request
        required: true
        schema:
          type: string
      produces:
      - application/json
      responses:
        "200":
          description: OK
          schema:
            type: object
      security:
      - ApiKeyAuth: []
      summary: 通用Update接口
      tags:
      - 通用Update接口
securityDefinitions:
  jwt:
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
