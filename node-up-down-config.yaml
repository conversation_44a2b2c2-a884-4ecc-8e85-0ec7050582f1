solutionInfos:
- baselineVersion: 1.2.0
  solutionName: caas-infra-node-up-down
  solutionVersion: 1.2.0-1.0.2-universal
- baselineVersion: 2.1.0
  solutionName: caas-infra-node-up-down
  solutionVersion: 2.1.0-1.0.0-universal
masterNodeLabelKeys:
- node-role.kubernetes.io/control-plane
masterNodeLabelMaps: {}
group:
  controlNode: 主控节点(常用master)
  nodeUpWorker: 工作节点
  nodeUpGpu: 标记GPU分组
  nodeDown: 待下线节点
action:
  nodeUp: 节点上线
  nodeDown: 节点下线
groupInfo:
  initialing:
  - node-disk-mount-group
  prefligting:
  - node-up-prefix-group
  nodeUpInstalling:
  - node-up-group
  nodeDownInstalling:
  - node-down-group
nodeUpDownStepGroups:
- code: node-down-group
  alias: 节点下线
  steps:
  - code: sisyphus-system-step-prepare-execution
    alias: 执行部署平台初始化步骤
    labels:
    - nodeDown
  - code: remove-from-kubernetes
    alias: 从集群中移除节点
    labels:
    - nodeDown
  - code: uninstall-containerd
    alias: 卸载容器运行时并清除用户数据
    labels:
    - nodeDown/containerd
  - code: uninstall-docker
    alias: 卸载容器运行时并清除用户数据
    labels:
    - nodeDown/docker
  - code: uninstall-sisyphus-flag
    alias: 卸载sisyphus初始化步骤标记
    labels:
    - nodeDown
- code: node-disk-mount-group
  alias: 节点数据盘挂载
  steps:
  - code: sisyphus-disk-execution
    alias: 执行部署平台磁盘管理步骤
    labels:
    - nodeUp/auto
- code: node-up-prefix-group
  alias: 节点上线预检
  steps:
  - code: sisyphus-system-step-prepare-execution
    alias: 执行部署平台初始化步骤
    labels:
    - nodeUp
  - code: precheck-check-os
    alias: 预检-检查CPU架构/操作系统
    labels:
    - nodeUp
  - code: precheck-check-linux-packages
    alias: 预检-检查是否存在已知的冲突软件包
    labels:
    - nodeUp
  - code: precheck-check-master-resources
    alias: 预检-检查主控节点资源是否符合要求
    labels:
    - nodeUp
- code: node-up-group
  alias: 节点上线
  steps:
  - code: install-chrony-client
    alias: 安装时间同步服务(客户端)
    labels:
    - nodeUp
  - code: install-containerd
    alias: 安装containerd容器运行时
    labels:
    - nodeUp/containerd
  - code: install-docker
    alias: 安装docker容器运行时
    labels:
    - nodeUp/docker
  - code: join-kubernetes-node
    alias: 添加kubernetes其它节点
    labels:
    - nodeUp
  - code: label-gpu-nodes
    alias: 添加GPU标签
    labels:
    - nodeUp
initial:
  groupCode: data-initial
  groupAlias: 信息提交
  nodeInitialCode: olympus-node-initial
  nodeInitialAlias: 节点信息提交及联通性检查
  nodeDownSisyphusSolutionApplyCode: sisyphus-solution-apply-node-down
  nodeDownSisyphusSolutionApplyAlias: 节点下线信息提交
  nodeUpSisyphusSolutionApplyCode: sisyphus-solution-apply-node-up
  nodeUpSisyphusSolutionApplyAlias: 节点上线信息提交
